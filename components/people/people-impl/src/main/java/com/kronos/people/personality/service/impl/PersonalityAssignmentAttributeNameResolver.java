package com.kronos.people.personality.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.springframework.context.annotation.Lazy;

import com.kronos.people.personality.service.IPersonalityAssignmentAttributeNameResolver;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileDTO;
import com.kronos.timekeeping.service.attestation.api.service.AttestationProfileSetupService;
import com.kronos.wfc.absencemgmt.attendance.business.config.Profile;
import com.kronos.wfc.absencemgmt.leave.business.config.LeaveProfile;
import com.kronos.wfc.commonapp.reporting.business.ReportDataAccessProfile;
import com.kronos.wfc.platform.persistence.framework.ObjectId;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.timekeeping.cascade.business.config.CascadeProfile;
import com.kronos.wfc.timekeeping.paycodes.business.PayCodeDataAccessProfile;
import com.kronos.wfc.timekeeping.workrules.business.WorkRuleDataAccessProfile;
import com.kronos.wfc.totalizing.business.extensibility.Processor;

@Named
public class PersonalityAssignmentAttributeNameResolver implements IPersonalityAssignmentAttributeNameResolver {

	@Lazy
	@Inject
	AttestationProfileSetupService attestationProfileSetupService;

	@Override
	public Map<Long, String> getAttendanceProfileNames(Set<Long> ids) {
		List<?> list = Profile.getAll();
		return list.stream().map(element -> (Profile) element)
				.filter(element -> ids.contains(element.getId().longValue()))
				.collect(Collectors.toMap(ele -> ele.getId().longValue(), Profile::getName));
	}

	@Override
	public Map<Long, String> getLeaveProfileNames(List<Long> ids) {
		List<?> leaveProfileList = LeaveProfile.retrieveListByIds(ids);
		return leaveProfileList.stream().map(element -> (LeaveProfile) element)
				.collect(Collectors.toMap(ele -> ele.getId().longValue(), LeaveProfile::getName));
	}

	@Override
	public Map<Long, String> getCascadeProfileNames(List<Long> ids) {
		List<?> cascadeProfileList = CascadeProfile.retrieveListByIds(ids);
		return cascadeProfileList.stream().map(element -> (CascadeProfile) element)
				.collect(Collectors.toMap(ele -> ele.getId().longValue(), CascadeProfile::getName));
	}

	@Override
	public Map<Long, String> getAssignmentRuleNames(Set<Long> ids) {
		List<ObjectId> lists = ids.stream().map(ObjectIdLong::new).collect(Collectors.toList());
		List<Processor> processors = Processor.getByIds(lists);
		return processors.stream().collect(Collectors.toMap(element -> element.getId().longValue(), Processor::getName));
	}


	@Override
	public String getWorkRuleProfileName(Long workRuleProfileId) {
		WorkRuleDataAccessProfile workRuleDataAccessProfile = WorkRuleDataAccessProfile
				.getWorkRuleProfile(new ObjectIdLong(workRuleProfileId));

		return Objects.nonNull(workRuleDataAccessProfile) ? workRuleDataAccessProfile.getName() : null;
	}

	@Override
	public String getPayCodeProfileName(Long payCodeProfileId) {
		PayCodeDataAccessProfile payCodeDataAccessProfile = PayCodeDataAccessProfile
				.getPayCodeProfile(new ObjectIdLong(payCodeProfileId), true);

		return Objects.nonNull(payCodeDataAccessProfile) ? payCodeDataAccessProfile.getName() : null;
	}
	@Override
	public Map<Long, String> getAttestationProfileNames(Set<Long> ids) {
		return ids.stream().collect(Collectors.toMap(id -> id, id -> {
			AttestationProfileDTO profileDTO = attestationProfileSetupService.getAttestationProfile(id);
			return Objects.nonNull(profileDTO) ? profileDTO.getName() : null;
		}));
	}
	@Override
	public String getReportProfileName(Long reportProfileId) {
		ObjectIdLong reportProfileIdObject = new ObjectIdLong(reportProfileId);
		ReportDataAccessProfile profile = ReportDataAccessProfile.getReportProfile(reportProfileIdObject);
		if (null != profile) {
			return profile.getLabel();
		}
		return null;
	}
}