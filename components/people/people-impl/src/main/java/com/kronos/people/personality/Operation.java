/***********************************************************************
 * Operation.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality;

/**
 * The {@link Operation} interface provides the method for executing a task.
 * 
 * <AUTHOR>
 *
 */
public interface Operation {
	/**
	 * This method executes the task
	 */
	void execute();
}
