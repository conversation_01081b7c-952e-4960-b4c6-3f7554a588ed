package com.kronos.people.personality.dataaccess.legacy.impl;

import com.kronos.datacollection.udm.service.ttipuserprofile.api.ITTIPUserProfileService;
import com.kronos.datacollection.udm.service.ttipuserprofile.api.dto.TTIPEmployeeAttributes;
import com.kronos.datacollection.udm.service.ttipuserprofile.api.dto.TTIPUserProfile;
import com.ukg.container.selectiveloading.exception.NotImplementedException;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TTIPUserProfileService implements ITTIPUserProfileService {

    @Override
    public TTIPEmployeeAttributes getTTIPEmployeeAttributesByPersonId(long l) {
        throw new NotImplementedException();
    }

    @Override
    public List<TTIPUserProfile> getAllTTIPUserProfiles() {
        throw new NotImplementedException();
    }

    @Override
    public String getTTIPUserProfileNameById(long l) {
        throw new NotImplementedException();
    }

    @Override
    public Long getTTIPUserProfileIdByName(String s) {
        throw new NotImplementedException();
    }

    @Override
    public String getTTIPUserProfileNameByEmployeeId(long l) {
        throw new NotImplementedException();
    }

    @Override
    public Long getTTIPUserProfileIdByEmployeeId(long l) {
        throw new NotImplementedException();
    }

    @Override
    public void updateEmployeeTTIPUserProfile(long l, Long aLong) {
        throw new NotImplementedException();
    }

    @Override
    public Boolean getTTIPPWDUpdateReqdSwByEmployeeId(long l) {
        throw new NotImplementedException();
    }

    @Override
    public boolean isExistTTIPEmployee(long l) {
        throw new NotImplementedException();
    }

    @Override
    public String getTTIPUserId(long l) {
        throw new NotImplementedException();
    }

    @Override
    public boolean checkTTIPUserIdExists(String s) {throw new NotImplementedException();
    }
}
