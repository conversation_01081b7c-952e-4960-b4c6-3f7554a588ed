package com.kronos.people.personality.dataaccess.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import com.kronos.people.personality.dataaccess.converter.LocalDateTimeConverter;
import com.kronos.timekeeping.service.attestation.api.entity.AttestationProfile;

@Entity
@Table(name = "assign_attestation_profile")
public class AttestationProfileAssignment extends AbstractEntity<Long> {
    private static final long serialVersionUID = -3597855411799405542L;
    private static final long INITIAL_VERSION = 0L;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "person_accassign_id", nullable = false)
    private PersonAssignment personAssignment;

    @Column(name = "person_accassign_id", insertable = false, updatable = false)
    private Long personAssignmentId;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "attestation_profile_id", nullable = false)
    private AttestationProfile profile;

    @Column(name = "attestation_profile_id", insertable = false, updatable = false)
    private Long profileId;

    @Column(name = "effectivedtm", nullable = false)
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime effectiveDate;

    @Column(name = "expirationdtm", nullable = false)
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime expirationDate;

    @Column(name = "updatedbyusracctid", nullable = false, precision = 12)
    private Long updatedUserId;

    @Column(name = "updatedtm", nullable = false)
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime updatedOn;

    @Version
    @Column(name = "versioncnt", precision = 12, nullable = false)
    private long version = INITIAL_VERSION;

    @Column(name = "manager_role", nullable = false)
    private Boolean isManagerRole = Boolean.FALSE;

    @Override
    @Column(name = "id", nullable = false)
    @Id
    @Access(AccessType.PROPERTY)
    public Long getId() {
        return id;
    }

    public PersonAssignment getPersonAssignment() {
        return personAssignment;
    }

    public void setPersonAssignment(PersonAssignment personAssignment) {
        this.personAssignment = personAssignment;
    }

    public Long getPersonAssignmentId() {
        return personAssignmentId;
    }

    public void setPersonAssignmentId(Long personAssignmentId) {
        this.personAssignmentId = personAssignmentId;
    }

    public AttestationProfile getProfile() {
        return profile;
    }

    public void setProfile(AttestationProfile profile) {
        this.profile = profile;
    }

    public Long getProfileId() {
        return profileId;
    }

    public void setProfileId(Long profileId) {
        this.profileId = profileId;
    }

    public LocalDateTime getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDateTime effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public LocalDateTime getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDateTime expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Long getUpdatedUserId() {
        return updatedUserId;
    }

    public void setUpdatedUserId(Long updatedUserId) {
        this.updatedUserId = updatedUserId;
    }

    public LocalDateTime getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }


    public Boolean getManagerRole() {
        return isManagerRole;
    }

    public void setManagerRole(Boolean managerRole) {
        isManagerRole = managerRole;
    }
}
