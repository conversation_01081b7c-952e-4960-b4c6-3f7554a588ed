/***********************************************************************
 * NotificationEntry.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.notification.entry;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

import com.kronos.people.personality.model.extension.AllExtension;
import com.kronos.people.personality.model.extension.BaseExtension;

/**
 * This is personality change notification entry having all affected cache
 * entries and a function to convert extensionsMap to AllExtension
 * 
 * <AUTHOR>
 *
 */
public class NotificationEntry {
	/**
	 * The chachEntities
	 */
	private List<CacheEntry> cacheEntries;
	/**
	 * The extension map to allExtension converter function
	 */
	private Function<Map<String, BaseExtension>, AllExtension> mapToAllExtensionConverter;
	/**
	 * The tenantId
	 */
	private String tenantId;
	
	/**
	 * Constructor for creating a instance of {@code NotificationEntry} by
	 * passing {@code tenantId}, {@code cacheEntries},
	 * {@code mapToAllExtensionConverter}.
	 * 
	 * @param tenantId
	 * @param cacheEntries
	 * @param mapToAllExtensionConverter
	 */
	public NotificationEntry(String tenantId, List<CacheEntry> cacheEntries, Function<Map<String, BaseExtension>, AllExtension> mapToAllExtensionConverter) {
		this.tenantId = tenantId;
		this.cacheEntries = cacheEntries;
		this.mapToAllExtensionConverter = mapToAllExtensionConverter;
	}
	
	/**
	 * Return personIds of deleted persons
	 * 
	 * @return
	 */
	public Set<Long> getDeleteEntries() {
		Set<Long> personIds = new HashSet<>();
		cacheEntries.stream()
				.filter(entry -> entry.getEventType().equals(EventType.DELETE))
				.forEach(entry -> personIds.add(entry.getKey().getObjectId()));
		return personIds;
	}
	
	/**
	 * Return extensions map for inserted persons.
	 * 
	 * @return
	 */
	public Map<Long, AllExtension> getInsertedPersonExtensions() {
		return getPersonExtensions(EventType.INSERT);
	}
	
	/**
	 * Return extensions map for updated persons.
	 * 
	 * @return
	 */
	public Map<Long, AllExtension> getUpdatedPersonExtensions() {
		return getPersonExtensions(EventType.UPDATE);
	}
	
	/**
	 * This method create person extensions for eventType {@code EventType}
	 * 
	 * @param eventType
	 * @return
	 */
	private Map<Long, AllExtension> getPersonExtensions(EventType eventType) {
		Map<Long, AllExtension> personExtensions = new ConcurrentHashMap<>();
		cacheEntries
				.parallelStream()
				.filter(entry -> entry.getEventType().equals(eventType))
				.forEach(
						entry -> personExtensions.put(entry.getKey()
								.getObjectId(), mapToAllExtensionConverter
								.apply(entry.getExtensions())));
		return personExtensions;
	}

	/**
	 * This method gets the tenantId
	 * 
	 * @return the tenantId
	 */
	public String getTenantId() {
		return tenantId;
	}
}
