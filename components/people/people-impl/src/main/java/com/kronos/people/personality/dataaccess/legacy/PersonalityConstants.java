/***********************************************************************
 * PersonalityConstants.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/

package com.kronos.people.personality.dataaccess.legacy;

/**
 * This class have Personality related constants
 * <AUTHOR>
 *
 */
public enum PersonalityConstants {

	///////////////////////// PROPERTY CONSTANTS - Start ///////////////////////////////////////
	
	CREATE("create"),
	UPDATE("update"),
	DELETE("delete"),
	SWIMLANE("wfp"),
	LOCALE("en_US"),
	VERSION("1.0"),
	EMPLOYEE_WORKFLOW_PROFILE("employeeWorkFlowProfile"),
	MANAGER_WORKFLOW_PROFILE("managerWorkFlowProfile"),
	EVICT_ALL("EVICT_ALL"), 
	TENANT_ID("TENANT_ID"), 
	PERSON_ID("PERSON_ID"), 
	NOTIFICATION_QUEUE("PersonalityTopic"),
	
   ///////////////////////// PROPERTY CONSTANTS - End ///////////////////////////////////////
	
   ///////////////////////// DB CONSTANTS - Start ///////////////////////////////////////
	
	PRIMING_ENABLED("commonbusiness.people.extension.priming.enabled"),
	PRIMING_SIZE("commonbusiness.people.extension.priming.size"),
	PRIMING_PARALLEL_THREADS_PER_TENANT("commonbusiness.people.extension.priming.parallel.threads.per.tenant"),
	PRIMING_PARALLEL_TENANTS_PROCESSING("commonbusiness.people.extension.priming.parallel.tenant.processing"),
	CACHE_PUT_SIZE("commonbusiness.people.extension.cacheput.batch.size"),
	EXTENSION_BUILD_CONSUMERS_COUNT("commonbusiness.people.extension.build.thread.count"),
	CACHEPUT_CONSUMERS_COUNT("commonbusiness.people.extension.cacheput.thread.count"),
	PRIMING_ENABLED_FOR_TENANT("commonbusiness.people.extension.tenant.priming.enabled"), 
	MULTIGET_THREAD_COUNT("commonbusiness.people.extension.multiget.thread.count"),
	MULTIEVICT_THREAD_COUNT("commonbusiness.people.extension.multievict.thread.count"),
	SERVER_IS_NON_INTERATCTIVE("site.non-interactive.server"),
	PRIMING_PREFERENCE_FLAG("commonbusiness.people.extension.priming.systemuser.preference.flag"),
	
   ///////////////////////// DB CONSTANTS - Start ///////////////////////////////////////

	///////////////////////MESSAGING CONSTANTS - Start///////////////////////////////////
	/**
	This is the JVM argument, from wfc_vars file. In development enviornment, if RabbitMQ server is not available, then setting this variable as true, 
	we can bypass the exception and continue with system start up but in production, this variable will always be set to true.
	As it is defined in wfc_vars so its name will not change. If in certain condition, its name got changed then we need to update its name here.
	*/
	BYPASS_MESSAGING_FAILURE("messaging.failure.bypass"), 
	EXTENSION_BUILD_THREAD_COUNT("commonbusiness.people.extension.building.thread.count"),
	//////////Added below property for checking Manager and Employee Roles///////////////
	
	WORKFORCE_MANAGER("Workforce_Manager"),
	WORKFORCE_EMPLOYEE("Workforce_Professional_Employee"), EMPLOYEE_WORKFLOW_PROFILE_ID("employeeWorkFlowProfileId"), MANAGER_WORKFLOW_PROFILE_ID("managerWorkFlowProfileId"),
	PEOPLE_IS_DISTRIBUTED_PRIMING_ENABLED("people.is.distributed.priming.enabled"),
	PEOPLE_APPLY_PERFORMANCE_FIXES("people.apply.performance.fixes"),
	PEOPLE_PARALLEL_PROCESSING_NODES_PRIMING("people.number.nodes.priming.parallelization"),
	MULTIPLE_EMPLOYMENTS("MultipleEmployments");

	///////////////////////MESSAGING CONSTANTS - End////////////////////////////////////
	/**
	 * The error name.
	 */
	private final String value;
	
	/**
	 * Initialize the newly create instance of constant enum by passing constant value.
	 * 
	 * @param value the constant value
	 */
	private PersonalityConstants(String value) {
		this.value = value;
	}
	
	/**
	 * @return the constant value.
	 */
	public String getValue() {
		return value;
	}
}
