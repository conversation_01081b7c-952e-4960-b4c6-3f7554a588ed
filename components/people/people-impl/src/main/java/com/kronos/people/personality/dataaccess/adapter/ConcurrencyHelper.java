/***********************************************************************
 * ConcurrencyHelper.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.adapter;

import com.kronos.logging.slf4jadapter.util.LogService;
import com.kronos.people.personality.dataaccess.legacy.PersonalityConstants;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.exception.impl.ExceptionHelper;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.BaseExtension;
import com.kronos.people.personality.properties.KronosPropertiesFacade;
import com.kronos.people.personality.tenant.TenantHandlingFacade;
import com.kronos.people.personality.util.LogTimeHelper;
import com.kronos.wfc.platform.logging.framework.Log;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Named;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * This class is for performing concurrency related operations.
 * This class also helps different components to transform data from and to non concurrent to concurrent data structures.
 * <AUTHOR>
 *
 */
@Named
public class ConcurrencyHelper {
    public TenantHandlingFacade tenantHandlingFacade;
    public KronosPropertiesFacade kronosPropertiesFacade;
    public ExceptionHelper exceptionHelper;
    private ExecutorService executor;
    private static final Logger LOGGER = LoggerFactory.getLogger(ConcurrencyHelper.class);

    private static final String MINIMUM_POOL_SIZE = "people.thread.count.min";
    private static final String QUEUE_SIZE = "people.thread.queue.size";
    private static final String MAXIMUM_POOL_SIZE = "people.thread.count.max";
    private static final String KEEP_ALIVE_TIME = "people.thread.alive.time.in.sec";
    private static final String THREAD_NAME_PREFIX = "personality-ConcurrencyHelper-";

    public ConcurrencyHelper(
            TenantHandlingFacade tenantHandlingFacade, KronosPropertiesFacade kronosPropertiesFacade,
            ExceptionHelper exceptionHelper) {
        this.tenantHandlingFacade = tenantHandlingFacade;
        this.kronosPropertiesFacade = kronosPropertiesFacade;
        this.exceptionHelper = exceptionHelper;
        init();
    }

    public void init() {
        LOGGER.info("Initializing threadFactory");
        ThreadFactory threadFactory = new ThreadFactory() {
            private final ThreadGroup group = Thread.currentThread().getThreadGroup();
            private final AtomicLong count = new AtomicLong();
            @Override
            public Thread newThread(final Runnable target) {
                return new Thread(this.group, target, THREAD_NAME_PREFIX + "-" + this.count.incrementAndGet());
            }
        };

        LinkedBlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>( kronosPropertiesFacade.getIntegerKronosProperty(QUEUE_SIZE, 100));
        executor = new ThreadPoolExecutor(kronosPropertiesFacade.getIntegerKronosProperty(MINIMUM_POOL_SIZE, 6),
                kronosPropertiesFacade.getIntegerKronosProperty(MAXIMUM_POOL_SIZE, 6),
                kronosPropertiesFacade.getIntegerKronosProperty(KEEP_ALIVE_TIME, 60),
                TimeUnit.SECONDS,
                workQueue, threadFactory);


    }


    /**
     * Parallel processing on queue items.
     * @param <T> The type of input and output object
     * @param queue The queue
     * @param supplier supplier The functional interface apply T argument and return Boolean
     * @param executor The executor service
     * @param numberOfThreads Total Threads
     * @param size the queueSize
     * @return int total item processed by queue
     */
    public <T> int parallelProcessQueueItems(Queue<T> queue, Function<T, Boolean> supplier, ExecutorService executor, int numberOfThreads, int size) {

        //For single item
        if (size == 1 && queue.size() == 1) { //added extra check to make sure that we have not passed size as 1 by mistake.
            //only one item is present in queue.
            return supplier.apply(queue.remove()) ? 1 : 0;
        }
        String mainThreadTenantId = tenantHandlingFacade.isTenantIdNullForCurrentThread() ? null : tenantHandlingFacade.getTenantId();
        Callable<Integer> mainCallable = getMainThreadCallable(queue, supplier);
        Callable<Integer> parallelTasksCallable = getParallelTasksCallable(mainThreadTenantId, mainCallable);
        int tasksToBeCreated = compareTaskCountWithSize(numberOfThreads, size);
        AtomicInteger primedCount = startProcessingAndGetProcessedCount(
                executor, tasksToBeCreated, parallelTasksCallable, mainCallable, queue.size());
        return primedCount.get();
    }

    /**
     * Returns main thread callable. The same needs to be wrapped to set and unset thread local for executor's threads.
     * This callable is responsible of retrieving the items to work upon and run the supplier on the item from the queue.
     * @param queue Queue to work upon
     * @param supplier Supplier to which each item will be passed
     * @return Callable which will be run directly by main thread.
     */
    protected <T> Callable<Integer> getMainThreadCallable(Queue<T> queue,
                                                          Function<T, Boolean> supplier) {
        return () -> {
            Integer processed = 0;
            while (!queue.isEmpty()) {
                T item = queue.poll();
                if (item == null) {
                    continue;
                }
                try {
                    supplier.apply(item);
                } catch (Exception e) {
                    LogTimeHelper.logError(e, "Exception occurred while executing the supplier.");
                } finally {
                    processed++;
                }
            }
            return processed;
        };
    }

    /**
     * Returns Parallel Tasks Callable.
     * This is wrapper over main thread callable. We set tenant id and remove the same.
     * @param mainThreadTenantId Main thread tenant id
     * @param mainCallable Callable which needs to be called by main thread
     * @return Callable to be called by executor's tasks
     */
    protected Callable<Integer> getParallelTasksCallable(String mainThreadTenantId,
                                                         Callable<Integer> mainCallable) {
        return () ->
                tenantHandlingFacade.performOperationsUsingTenantId(() -> {
                    Integer processed = 0;
                    try {
                        processed = mainCallable.call();
                    } catch (Exception e) {
                        LogTimeHelper.logError(e, "Exception occurred while gettingParalleltaskscallable.");
                    }
                    return processed;

                }, mainThreadTenantId);
    }

    /**
     * Checks if the size is less that the configured number of threads.
     * If true, returns size
     * Else, returns configured number of threads
     * @param numberOfThreads the configured number of threads
     * @param size size parameter
     * @return threads to be spawned
     */
    protected int compareTaskCountWithSize(int numberOfThreads, int size) {
        return (size < numberOfThreads) ? size : numberOfThreads;
    }


    /**
     * @param executor The executor service
     * @param numberOfThreads total threads
     * @param cacheEntryCreationRunnable The integer type Callable
     * @param mainCallable Callable interface of main thread which returns Integer
     * @return AtomicInteger The atomicInteger
     */
    public AtomicInteger startProcessingAndGetProcessedCount(ExecutorService executor, int numberOfThreads,
            Callable<Integer> cacheEntryCreationRunnable, Callable<Integer> mainCallable, int queueSize) {
        LogService logService = new LogService();
        String transactionId = logService.getTransactionId();
        String tenantId = tenantHandlingFacade.getTenantId();
        AtomicInteger primedCount = new AtomicInteger(0);
        List<Future<Integer>> futures = new ArrayList<>();
        try {
            Collections.nCopies(numberOfThreads, cacheEntryCreationRunnable)
                    .forEach(integerCallable -> futures.add(executor.submit(integerCallable)));
        } catch (Exception e) {
            LOGGER.error("Failed to add Worker Threads for some tasks, the tenant id is {}, the trxId is {}",
                    tenantId, transactionId);
            LOGGER.error("Error in Concurrency Helper ", e);
        }
        try {
            primedCount.addAndGet(mainCallable.call());
            //If MainThread process all the tasks then no need to wait for Child Threads execution
            if (primedCount.get() < queueSize) {
                futures.forEach(future -> {
                    try {
                    	 if (primedCount.get() < queueSize) {
                    		 primedCount.addAndGet(future.get(10, TimeUnit.MINUTES));
                    	 }
                    } catch (Exception e) {
                        LogTimeHelper.logError(e, "Could not wait for future");
                    }
                });
            }
        } catch (Exception e1) {
            LogTimeHelper.logError(e1, "Exception occured in processing current thread.");
        }


        return primedCount;
    }

    /**
     * Parallel processing on queue items.
     * @param <T> The type of elements in queue
     * @param queue Queue of items to be worked on
     * @param processingFunction function which has to be called
     * @param size the queueSize
     * @return number of items processed successfully,
     * success criteria is based on the return of processing function
     */
    public <T> int parallelProcessQueueItems(Queue<T> queue, Function<T, Boolean> processingFunction, int size) {
        return parallelProcessQueueItems(queue, processingFunction, getExecutor(), getNumberOfProcessingThreads(), size);
    }

    /**
     * @return {@link Integer} indicating the number of processing threads
     */
    public Integer getNumberOfProcessingThreads() {
        return getNumberOfProcessingThreads(PersonalityConstants.MULTIGET_THREAD_COUNT.getValue());
    }

    /**
     * Return number of processing threads depending on key else returns a default value.
     * @param key String instance
     * @return {@link Integer} indicating the number of processing threads
     */
    public Integer getNumberOfProcessingThreads(String key) {
        return kronosPropertiesFacade.getIntegerKronosProperty(key, 2);
    }


    /**
     * Parallel processing on queue items for multiple get.
     * @param <T> Type of entry in queue passed
     * @param criteriaQueue Queue of items to be worked on
     * @param processingFunction function which has to be called
     * @param size the queueSize
     */
    public <T> void parallelProcessQueueItemsForMultiGet(Queue<T> criteriaQueue, Function<T, Boolean> processingFunction, int size) {
        parallelProcessQueueItems(criteriaQueue, processingFunction, size);
    }


    /**
     * Parallel processing on queue items.
     * @param <T> The type of elements in queue
     * @param queue Queue of items to be worked on
     * @param processingFunction function which has to be called
     * @param numberOfThreads Total Threads
     * @param size the size of queue elements
     * @return number of items processed successfully,
     * success criteria is based on the return of processing function
     */
    public <T> int parallelProcessQueueItems(Queue<T> queue, Function<T, Boolean> processingFunction, int numberOfThreads, int size) {
        return parallelProcessQueueItems(queue, processingFunction, getExecutor(), numberOfThreads, size);
    }

    /**
     * All items of passed queue is removed and added to an ArrayList.
     * @param <T> The type of input and output object
     * @param queue queue which needs to be extracted as list.
     * @return List of items present in queue
     */
    public <T> List<T> getList(Queue<T> queue) {
        List<T> list = new ArrayList<>();
        while (!queue.isEmpty()) {
            list.add(queue.remove());
        }
        return list;
    }

    /**
     * Get queue for items in array
     * @param <T> type of items in array
     * @param items array of items for which queue is needed
     * @return Queue of items present in array
     */
    public <T> Queue<T> getQueueFromArray(T[] items) {
        Queue<T> queue = new ConcurrentLinkedQueue<T>();
        for (T item : items) {
            queue.add(item);
        }
        return queue;
    }

    /**
     * This method is used to return Queue from list passed.
     * @param <T> Type of entries in List
     * @param list List of type T
     * @return Queue of type T
     */
    public <T> Queue<T> getQueueFromList(List<T> list) {
        return new ConcurrentLinkedQueue<>(list);
    }

    /**
     * Get missing id's not present in a set.
     * @param <T> The type of input and output object
     * @param allIds All id's available. Super set
     * @param idsInSet Id's already found. Sub set
     * @param size AtomicInteger to set the size of elements to be stored in queue.
     * @return ConcurrentLinkedQueue for the differential. super set - sub set
     */
    public <T> Queue<T> getMissingIdsNotPresentInSet(T[] allIds, Set<T> idsInSet, AtomicInteger size) {
        Queue<T> queue = new ConcurrentLinkedQueue<T>();
        if (idsInSet.isEmpty()) {
            queue = getQueueFromArray(allIds);
            size.set(allIds.length);
        } else {
            for (T actualPersonId : allIds) {
                if (!idsInSet.contains(actualPersonId)) {
                    queue.add(actualPersonId);
                    size.getAndIncrement();
                }
            }
        }
        return queue;
    }

    /**
     * Get personality response from extension for e
     * @param extension the extension object
     * @param snapShotDate the snapshot date
     * @param isActiveOnly active only check
     * @param <U>
     *         Extension of type {@link BaseExtension}
     * @return the extension object
     */
    public <U extends BaseExtension> PersonalityResponse<U> getPersonalityResponseFromExtension(U extension, LocalDate snapShotDate, Boolean isActiveOnly) {
        if (isActiveOnly && !extension.isActive()) {
            return new PersonalityResponse<U>(null, exceptionHelper.createException(PersonalityErrorCode.NOT_ACTIVE, "Personality is not active."));
        }
        return new PersonalityResponse<U>(extension, null);
    }

    /**
     * This method is used in criteria requests response retrieved from cache where if configured, have to send positive response only for active personalities.
     * @param <T>
     *         type of key of input and output maps
     * @param <U>
     *         type of extension of input and output maps
     * @param allExtensionsMap Map of the extensions which needs to be looked upon.
     * @param snapShotDate Date to be set
     * @param activeOnly  parameter to check if we need only active
     * @return Map of key, personality responses for given key, extensionsMap
     */
    public <T, U extends BaseExtension> Map<T, PersonalityResponse<U>> getPersonalityResponseFromExtensions(Map<T, U> allExtensionsMap, LocalDate snapShotDate, Boolean activeOnly) {
        Map<T, PersonalityResponse<U>> map = new ConcurrentHashMap<T, PersonalityResponse<U>>();
        AdapterHelper.setIfNotNull(allExtensionsMap, map, (t, u) -> u.forEach((k, v) -> map.put(k, getPersonalityResponseFromExtension(v, snapShotDate, activeOnly))));
        return map;
    }

    /**
     * @return the tenantHandlingFacade
     */
    public TenantHandlingFacade getTenantHandlingFacade() {
        return tenantHandlingFacade;
    }

    /**
     * @param tenantHandlingFacade the tenantHandlingFacade to set
     */
    public void setTenantHandlingFacade(TenantHandlingFacade tenantHandlingFacade) {
        this.tenantHandlingFacade = tenantHandlingFacade;
    }

    /**
     * @return the kronosPropertiesFacade
     */
    public KronosPropertiesFacade getKronosPropertiesFacade() {
        return kronosPropertiesFacade;
    }

    /**
     * @param kronosPropertiesFacade the kronosPropertiesFacade to set
     */
    public void setKronosPropertiesFacade(KronosPropertiesFacade kronosPropertiesFacade) {
        this.kronosPropertiesFacade = kronosPropertiesFacade;
    }


    @PreDestroy
    public void cleanUp() {
        shutDownExecutor(getExecutor());
    }

    /**
     * Shut down executor.
     * @param executor Executor to be shut down
     */
    public void shutDownExecutor(ExecutorService executor) {
        try {
            executor.shutdown();
            // Wait a while for existing tasks to terminate
            if (!executor.isTerminated() && !executor.awaitTermination(60, TimeUnit.SECONDS)) {
                LogTimeHelper.logWithLogLevel(Log.DEBUG, "Killing non-finished tasks.");
                executor.shutdownNow(); // Cancel currently executing tasks
                // Wait a while for tasks to respond to being cancelled
                if (!executor.awaitTermination(30, TimeUnit.SECONDS))
                    LogTimeHelper.logWithLogLevel(Log.DEBUG, "ExecutorService did not terminate. Probably thread leak.");
            }
        } catch (InterruptedException ie) {
            // (Re-)Cancel if current thread also interrupted
            LogTimeHelper.logError(Log.DEBUG, ie, "interrupted occured !!");
            executor.shutdownNow();

        }
    }

    public ExecutorService getExecutor() {
        return executor;
    }
}