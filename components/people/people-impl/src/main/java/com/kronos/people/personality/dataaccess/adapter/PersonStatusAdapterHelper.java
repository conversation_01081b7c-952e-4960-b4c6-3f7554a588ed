/**
 *
 */
package com.kronos.people.personality.dataaccess.adapter;

import java.util.Collection;

import jakarta.inject.Named;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.kronos.people.personality.model.extension.entry.AccountStatusEntry;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.EmploymentAnalyticsLaborTypeEntry;
import com.kronos.people.personality.model.extension.entry.EmploymentStatusEntry;
import com.kronos.wfc.commonapp.people.business.person.PersonAnalyticsLaborTypeSet;
import com.kronos.wfc.commonapp.people.business.person.PersonStatusSet;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.shared.EmploymentStatusMapList;
import com.kronos.wfc.commonapp.people.shared.PersonAnalyticsLaborTypeMapList;
import com.kronos.wfc.commonapp.people.shared.UserAccountStatusMapList;
import com.kronos.wfc.commonapp.types.business.AnalyticsLaborType;
import com.kronos.wfc.commonapp.types.business.EmploymentStatusType;

/**
 * This is the adapter class for Employee Extension. It provides the methods for
 * populating the person status from personality.
 *
 * <AUTHOR>
 *
 */

@Named
public class PersonStatusAdapterHelper extends BaseHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(PersonStatusAdapterHelper.class);

    /**
     * This method convert the {@code personality} to effective dated collection
     * {@code EffectiveDatedCollection} of {@code AccountStatusEntry} by
     * {@code personality}.
     *
     * @param statusData
     *            {@link Personality}, the personality is a combination of the
     *            person demographics, user, and Account status information.
     * @return {@link EffectiveDatedCollection}, effective dated collection of
     *         {@link AccountStatusEntry}.
     */

    protected EffectiveDatedCollection<AccountStatusEntry> convertEffDatedAccountStatus(
            PersonStatusSet statusData) {
        Collection<UserAccountStatusMapList> legacyUserAccountStatus = personalityFacade
                .getLegacyUserAccountStatus(statusData);
        return new EffectiveDatedCollection<AccountStatusEntry>(
                adapterHelper.convertLegacyCollection(
                        legacyUserAccountStatus,
                        userAccountStatus -> getDatedAccountStatusEntry(userAccountStatus, legacyUserAccountStatus)));
    }


	/**
     * This method gets the dated account status entry by
     * {@code userAccountStatus}.
     *
     * @param userAccountStatus
     *         {@link UserAccountStatusMapList}, the effective information
     *         for the user account status for a person.
     * @return {@link AccountStatusEntry}.
     */
    protected AccountStatusEntry getDatedAccountStatusEntry(UserAccountStatusMapList userAccountStatus) {
        EmploymentStatusType employmentStatusType = personalityFacade.getEmploymentStatusTypeByUserAccountStatus(userAccountStatus);
        AccountStatusEntry accountStatusEntry = new AccountStatusEntry();
        if (employmentStatusType != null) {
            adapterHelper.setDates(accountStatusEntry, userAccountStatus.getEffectiveDate(), userAccountStatus.getExpirationDate());
            accountStatusEntry.setAccountStatusTypeId(adapterHelper.getLongFromObjectIdLong(employmentStatusType.getEmploymentStatusTypeId()));
        } else {
            LOGGER.error("employmentStatusType from EmploymentStatusTypeCache is NULL for userAccountStatus: " + userAccountStatus.getUserAccountStatus(), new RuntimeException());
        }
        return accountStatusEntry;
    }

    /**
     * This method convert the {@code personality} to effective dated employment
     * status {@code EffectiveDatedCollection} of {@code EmploymentStatusEntry}
     * by {@code personality}.
     *
     * @param statusData
     *            {@link Personality}, the personality is a combination of the
     *            person demographics, user, and Employment status information.
     * @return {@link EffectiveDatedCollection}, effective dated collection of
     *         {@link EmploymentStatusEntry}.
     */
    protected EffectiveDatedCollection<EmploymentStatusEntry> convertEffDatedEmploymentStatus(
            PersonStatusSet statusData) {
        Collection<EmploymentStatusMapList> legacyEmploymentStatus = personalityFacade.getLegacyEmploymentStatus(statusData);
        return new EffectiveDatedCollection<EmploymentStatusEntry>(
                adapterHelper.convertLegacyCollection(
                        legacyEmploymentStatus,
                        employmentStatus -> getDatedEmploymentStatusEntry(employmentStatus, legacyEmploymentStatus)));
    }

    /**
     * This method gets the dated employment status entry by
     * {@code employmentStatus}.
     *
     * @param employmentStatus
     *         {@link EmploymentStatusMapList} , the effective information
     *         for the employment status for a person.
     * @return {@link EmploymentStatusEntry}.
     */
    protected EmploymentStatusEntry getDatedEmploymentStatusEntry(EmploymentStatusMapList employmentStatus) {
        EmploymentStatusType employmentStatusType = personalityFacade.getEmploymentStatusType(employmentStatus);
        EmploymentStatusEntry employmentStatusEntry = new EmploymentStatusEntry();
        if (employmentStatusType != null) {
            adapterHelper.setDates(employmentStatusEntry, employmentStatus.getEffectiveDate(), employmentStatus.getExpirationDate());
            employmentStatusEntry.setEmploymentStatusTypeId(adapterHelper.getLongFromObjectIdLong(employmentStatusType.getEmploymentStatusTypeId()));
        } else {
            LOGGER.error("employmentStatusType from EmploymentStatusTypeCache is NULL  for  employmentStatus: " + employmentStatus, new RuntimeException());
        }
        return employmentStatusEntry;
    }

    private AccountStatusEntry getDatedAccountStatusEntry(UserAccountStatusMapList userAccountStatus, Collection<UserAccountStatusMapList> legacyUserAccountStatus) {
        EmploymentStatusType employmentStatusType = personalityFacade.getEmploymentStatusTypeByUserAccountStatus(userAccountStatus);
        AccountStatusEntry accountStatusEntry = new AccountStatusEntry();
        if (employmentStatusType != null) {
            adapterHelper.setDates(accountStatusEntry, userAccountStatus.getEffectiveDate(), userAccountStatus.getExpirationDate());
            accountStatusEntry.setAccountStatusTypeId(adapterHelper.getLongFromObjectIdLong(employmentStatusType.getEmploymentStatusTypeId()));
        } else {
            LOGGER.error("FLC-64249 ALERT!!!  EmploymentStatusType from EmploymentStatusTypeCache is NULL  for  userAccountStatus: {} : userAccountStatusMapLists: {} "
                    , userAccountStatus, getUserAccountStatusMapListAsString(legacyUserAccountStatus));
        }
        return accountStatusEntry;
    }


    private EmploymentStatusEntry getDatedEmploymentStatusEntry(EmploymentStatusMapList employmentStatus, Collection<EmploymentStatusMapList> legacyEmploymentStatus) {
        EmploymentStatusType employmentStatusType = personalityFacade.getEmploymentStatusType(employmentStatus);
        EmploymentStatusEntry employmentStatusEntry = new EmploymentStatusEntry();
        if (employmentStatusType != null) {
            adapterHelper.setDates(employmentStatusEntry, employmentStatus.getEffectiveDate(), employmentStatus.getExpirationDate());
            employmentStatusEntry.setEmploymentStatusTypeId(adapterHelper.getLongFromObjectIdLong(employmentStatusType.getEmploymentStatusTypeId()));
        } else {
            LOGGER.error("FLC-64249 ALERT!!!  EmploymentStatusType from EmploymentStatusTypeCache is NULL  for  employmentStatus: {} : legacyEmploymentStatus: {} "
                    , employmentStatus, getEmploymentStatusMapListAsString(legacyEmploymentStatus));
        }
        return employmentStatusEntry;
    }

    private String getEmploymentStatusMapListAsString(Collection<EmploymentStatusMapList> employmentStatusMapLists) {
        StringBuffer stringBuffer = new StringBuffer();
        //Print EmploymentStatusMapList Response  from StatusDataSet
        stringBuffer.append("\nEmploymentStatus Data WIth getStatuses");
        employmentStatusMapLists.forEach(employmentStatusMapList -> {
            stringBuffer.append("\n\t  EmploymentStatus.getEmploymentStatus : ").append(employmentStatusMapList.getEmploymentStatus());
            stringBuffer.append("\n\t  EmploymentStatus.getEffectiveDate : ").append(employmentStatusMapList.getEffectiveDate());
            stringBuffer.append("\n\t  EmploymentStatus.getExpirationDate : ").append(employmentStatusMapList.getExpirationDate());
        });
        return stringBuffer.toString();
    }


    private String getUserAccountStatusMapListAsString(Collection<UserAccountStatusMapList> userAccountStatusMapLists) {
        StringBuffer stringBuffer = new StringBuffer();
        //Print UserAccountStatusMapList Response  from StatusDataSet
        stringBuffer.append("\nEmploymentStatus Data WIth getStatuses");
        userAccountStatusMapLists.forEach(employmentStatusMapList -> {
            stringBuffer.append("\n\t  UserAccountStatus.getUserAccountStatus : ").append(employmentStatusMapList.getUserAccountStatus());
            stringBuffer.append("\n\t  UserAccountStatus.getEffectiveDate : ").append(employmentStatusMapList.getEffectiveDate());
            stringBuffer.append("\n\t  UserAccountStatus.getExpirationDate : ").append(employmentStatusMapList.getExpirationDate());
        });
        return stringBuffer.toString();
    }


	/**
	 * This method convert the {@code personality} to effective dated employment
	 * analyticsLaborTypeData {@code EffectiveDatedCollection} of {@code EmploymentAnalyticsLaborTypeEntry}
	 * by {@code personality}.
	 *
	 * @param analyticsLaborTypeData
	 *            {@link Personality}, the personality is a combination of the
	 *            person demographics, user, and Employment analytics labor type information.
	 * @return {@link EffectiveDatedCollection}, effective dated collection of
	 *         {@link EmploymentAnalyticsLaborTypeEntry}.
	 */
	protected EffectiveDatedCollection<EmploymentAnalyticsLaborTypeEntry> convertEffDatedAnalyticsLaborType(
			PersonAnalyticsLaborTypeSet analyticsLaborTypeData) {
		return new EffectiveDatedCollection<>(adapterHelper.convertLegacyCollection(
					personalityFacade.getLegacyAnalyticsLaborTypes(analyticsLaborTypeData),
					this::getDatedEmploymentAnalyticslaborTypeEntry));
	}

	/**
	 * This method gets the dated employment analytics labor type entry by
	 * {@code personAnalyticsLaborTypeMapList}.
	 *
	 * @param personAnalyticsLaborTypeMapList
	 *            {@link PersonAnalyticsLaborTypeMapList} , the effective information
	 *            for the employment analytics labor type for a person.
	 * @return {@link PersonAnalyticsLaborTypeMapList}.
	 */
	protected EmploymentAnalyticsLaborTypeEntry getDatedEmploymentAnalyticslaborTypeEntry(
			PersonAnalyticsLaborTypeMapList personAnalyticsLaborTypeMapList) {
		AnalyticsLaborType sType = personalityFacade.getAnalyticslaborType(personAnalyticsLaborTypeMapList);
		EmploymentAnalyticsLaborTypeEntry employmentAnalyticsLaborTypeEntry = new EmploymentAnalyticsLaborTypeEntry();
		adapterHelper.setDates(employmentAnalyticsLaborTypeEntry,
				personAnalyticsLaborTypeMapList.getEffectiveDate(),
				personAnalyticsLaborTypeMapList.getExpirationDate());
		AdapterHelper.setIfNotNull(sType, employmentAnalyticsLaborTypeEntry, (entry, type)->
					 entry.setEmploymentAnalyticsLaborTypeId(adapterHelper.getLongFromObjectIdLong(type.getAnalyticsLaborTypeId())));
		return employmentAnalyticsLaborTypeEntry;

	}

}
