/***********************************************************************
 * ExtensionAdapterEnum.java
 * 
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.adapter;

import com.kronos.people.personality.model.extension.AccrualExtension;
import com.kronos.people.personality.model.extension.DevicesExtension;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.SchedulingExtension;
import com.kronos.people.personality.model.extension.TimekeepingExtension;

/**
 * The {@code ExtensionAdapterEnum} is an enum for extensions.
 * 
 * <AUTHOR>
 *
 */
public enum ExtensionAdapterEnum {
	EMPLOYEE(EmployeeExtensionAdapter.class, EmployeeExtension.IDENTIFIER.getIdentifier()), 
	SCHEDULING(SchedulingExtensionAdapter.class, SchedulingExtension.IDENTIFIER.getIdentifier()),
	TIMEKEEPING(TimeKeepingAdapter.class, TimekeepingExtension.IDENTIFIER.getIdentifier()), 
	ACCRUAL(AccrualExtensionAdapter.class, AccrualExtension.IDENTIFIER.getIdentifier()),
	DEVICES(DevicesExtensionAdapter.class, DevicesExtension.IDENTIFIER.getIdentifier());

	private final Class<?> converterType;
	private final String identifier;

	/**
	 * Constructor of {@code ExtensionAdapterEnum}.
	 * 
	 * @param converterType
	 *            - {@link Class}.
	 */
	private ExtensionAdapterEnum(Class<?> converterType, String identifier) {
		this.converterType = converterType;
		this.identifier = identifier;
	}
	
	
	/**
	 * @return the identifier
	 */
	public String getIdentifier() {
		return identifier;
	}

}
