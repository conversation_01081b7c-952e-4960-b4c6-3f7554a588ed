package com.kronos.people.personality.dataaccess.repository;

import com.kronos.people.personality.dataaccess.entity.WtkEmployee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
@Transactional
public interface WTKPeopleRepository extends JpaRepository<WtkEmployee, Long> {

    /**
     * Returns all WTKEmployee  by the given person assignment id.
     *
     * @param personId the person assignment id
     * @return List of Profile Assignments
     */
    @Query("SELECT distinct wtkempl FROM WtkEmployee wtkempl WHERE wtkempl.personId = :personId")
    Optional<List<WtkEmployee>> findByPersonId(@Param("personId") Long personId);

}