/***********************************************************************
 * ExtensionCacheUpdaterImpl.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.notification;

import com.kronos.logging.slf4jadapter.util.LogService;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.people.personality.cache.ExtensionCacheUpdater;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.exception.impl.ExceptionHelper;
import com.kronos.people.personality.facade.PersonalityCacheFacade;
import com.kronos.people.personality.notification.batch.PersonalityBatchProcessor;
import com.kronos.people.personality.notification.entry.EventType;
import com.kronos.people.personality.notification.entry.PersonalityEvent;
import com.kronos.people.personality.tenant.TenantHandlingFacade;
import com.kronos.people.personality.util.LogTimeHelper;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.personality.PersonalityTriplet;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonNotification;
import com.kronos.wfc.platform.logging.framework.Log;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;

/**
 * Personality object lands on this class. It triggers Insert, Update, and
 * Delete operations. It Converts Personality to PersonalityEvent, and add it to
 * channel.
 *
 * <AUTHOR>
 */
@Named
public class ExtensionCacheUpdaterImpl implements ExtensionCacheUpdater<Personality> {

    /**
     * The instance of {@link AdapterHelper}
     */
    @Inject
    AdapterHelper adapterHelper;

    /**
     * The instance of {@link ExceptionHelper}
     */
    @Inject
    ExceptionHelper exceptionHandler;

    /**
     * The instance of {@link ChangeNotificationChannelController}
     */
    @Inject
    ChangeNotificationChannelController changeNotificationChannelController;

    /**
     * Tenant facade
     */
    @Inject
    TenantHandlingFacade tenantFacade;

    @Inject
    PersonalityBatchProcessor personalityBatchProcessor;

    @Inject
    PersonalityCacheFacade personalityCacheFacade;

    @Inject
    PersonalityFacade personalityFacade;

    /**
     * This method inserts the personality to the channel.
     *
     * @param personality
     *         the personality object
     */
    @Override
    public void insert(Personality personality) {
        LogTimeHelper.logWithLogLevel(Log.DEBUG, "Notification recieved for personality for insert...." + getPersonId(personality));
        addToProcessorChannel(personality, EventType.INSERT);
    }

    /**
     * Get person id for person.
     *
     * @param personality
     *         personality
     * @return person id
     */
    protected long getPersonId(Personality personality) {
        return personality.getPersonId().longValue();
    }

    /**
     * This method evict the personality from all the servers on update event.
     *
     * @param personality
     *         The personality object
     */
    @Override
    public void update(Personality personality) {
        LogTimeHelper.logWithLogLevel(Log.DEBUG, "Notification recieved for personality for update...." + getPersonId(personality));
        PersonNotification.sendUpdate(personality.getPersonId());
    }

    /* (non-Javadoc)
     * @see com.kronos.people.personality.cache.ExtensionCacheUpdater#registerBatch(java.util.List)
     */
    @Override
    public void registerBatch(List<Long> personIds) {
        personalityBatchProcessor.addToBatchMap(personIds);
    }

    /**
     * This method finds whether given person id is present in the person batch.
     */
    @Override
    public boolean isPersonInBatch(Long personId) {
        return personalityBatchProcessor.isPersonInBatch(personId);
    }

    /**
     * This method evict the personality from all the servers on delete event.
     *
     * @param personality
     *         the personality object
     */
    @Override
    public void delete(Personality personality) {
        LogTimeHelper.logWithLogLevel(Log.DEBUG, "Notification recieved for personality for delete...." + getPersonId(personality));
        PersonNotification.sendDelete(personality.getPersonId());
    }

    /**
     * This method adds the list of {@link PersonalityEvent} to channel.
     *
     * @param pes
     *         list of PersonalityEvent
     */
    public void addToProcessorChannel(List<PersonalityEvent> pes) {
        changeNotificationChannelController.notify(pes);
    }
   
    /**
     * This method adds the {@link Personality} to channel.
     *
     * @param personality
     *         the personality object
     * @param eventType
     *         the event type
     */
    public void addToProcessorChannel(Personality personality, EventType eventType) {
        LogTimeHelper.logWithLogLevel(Log.INFO, "personalityService --> ExtensionCacheUpdaterImpl addToProcessorChannel");
        LogService logService = new LogService();
        PersonalityEvent pe = new PersonalityEvent(eventType, personality, tenantFacade.getTenantId());
        pe.setTransactionId(logService.getTransactionId());
        changeNotificationChannelController.notify(pe);
    }

    public void addToProcessorChannel(PersonalityEvent pe) {
        LogTimeHelper.logWithLogLevel(Log.DEBUG, "personalityService --> ExtensionCacheUpdaterImpl addToProcessorChannel");
        changeNotificationChannelController.notify(pe);
    }

    /**
     * This method sets the {@link ChangeNotificationChannelController}
     * instance.
     *
     * @param extensionsCacheUpdator
     *         the extensionsCacheUpdator to set This is used for JUnit test
     *         case only
     */
    public void setExtensionsCacheUpdator(ChangeNotificationChannelController extensionsCacheUpdator) {
        this.changeNotificationChannelController = extensionsCacheUpdator;
    }

    /* (non-Javadoc)
     * @see com.kronos.people.personality.cache.ExtensionCacheUpdater#update(java.lang.Long)
     */
    @Override
    public void update(Long personId) {
    	PersonNotification.sendUpdate(new ObjectIdLong(personId));
    }

    /* (non-Javadoc)
     * @see com.kronos.people.personality.cache.ExtensionCacheUpdater#insert(java.lang.Long)
     */
    @Override
    public void insert(Long personId) {
        insert(personalityFacade.findPersonality(personId));
    }

    /* (non-Javadoc)
     * @see com.kronos.people.personality.cache.ExtensionCacheUpdater#delete(java.lang.Long)
     */
    @Override
    public void delete(Long personId) {
    	PersonNotification.sendDelete(new ObjectIdLong(personId));
    }

    /* (non-Javadoc)
     * @see com.kronos.people.personality.cache.ExtensionCacheUpdater#unregisterBatch(java.util.List)
     */
    @Override
    public void unregisterBatch(List<Long> personIds) {
        personalityBatchProcessor.removeFromBatchMap(personIds);
        personalityCacheFacade.evictFromOnHeap(personIds);
    }

    protected Personality getPersonality(Long personId) {
        if (personId == null)
            throw exceptionHandler.createException(PersonalityErrorCode.NULL_PARAM, "PersonId is null, invalid parameter");
        PersonalityTriplet personalityTriplet = new PersonalityTriplet();
        personalityTriplet.setPersonId(new ObjectIdLong(personId));
        Personality personality = new Personality(personalityTriplet);
        return personality;
    }


    /**
     * clears the legacy caches for a list of valid personIds
     * 
     * This api will be used by consumers after updating person-data in DB , to ensure that cache 
     * is evicted. In these usecases, PERSON-UPDATE event should be submitted in RabbitMQ so that 
     * listeners run there update-related flow and not deletion-related flows associated with PERSON-DELETE event.
     */
    @Override
    public void deleteFromLegacyPersonCache(List<Long> personIds) {
    	List<String> personList = personIds.stream().map(s -> String.valueOf(s)).collect(Collectors.toList());
    	PersonNotification.sendBulkUpdate(personList, false);
    }

    /**
     * clears the legacy and new caches for a list of valid personIds
     * 
     * This api will be used by consumers after updating person-data in DB , to ensure that cache 
     * is evicted. In these usecases, PERSON-UPDATE event should be submitted in RabbitMQ so that 
     * listeners run there update-related flow and not deletion-related flows associated with PERSON-DELETE event.
     */
    @Override
    public void deleteFromLegacyAndNewPersonCache(List<Long> personIds) {
    	List<String> personList = personIds.stream().map(s -> String.valueOf(s)).collect(Collectors.toList());
    	PersonNotification.sendBulkUpdate(personList, true); 
    }

    /* (non-Javadoc)
     * @see com.kronos.people.personality.cache.ExtensionCacheUpdater#addToProcessorChannel(java.lang.Long, java.lang.String)
     */
    @Override
    public void refreshNewPersonalityCache(Long personId, String eventType) {
        if (eventType.equals(PersonNotification.UPDATE_EVENT)) {
            LogService logService = new LogService();
            PersonalityEvent pe = new PersonalityEvent(EventType.UPDATE, personalityFacade.findPersonality(personId),
                    tenantFacade.getTenantId(), true);
            pe.setTransactionId(logService.getTransactionId());
            addToProcessorChannel(pe);
        } else {
            addToProcessorChannel(getPersonality(personId), EventType.DELETE);
        }

    }
}