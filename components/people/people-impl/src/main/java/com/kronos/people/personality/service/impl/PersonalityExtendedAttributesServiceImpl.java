package com.kronos.people.personality.service.impl;

import com.kronos.commonapp.employeegroup.setup.api.IEmployeeGroupQueryService;
import com.kronos.commonapp.hfsecuritymask.api.ICombinedHomeAccountService;
import com.kronos.commonapp.hfsecuritymask.api.IPositionHomeAccountService;
import com.kronos.commonapp.hfsecuritymask.model.CombinedHomeAccount;
import com.kronos.commonapp.hfsecuritymask.model.position.PositionHomeAccount;
import com.kronos.commonapp.laborcategory.laboraccount.model.LaborAccount;
import com.kronos.datacollection.udm.service.devicegroup.api.dto.DeviceGroup;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.dataaccess.legacy.IWorkEmployeeServiceFacade;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacadeForCachedAttributes;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.entry.CostCenterEntry;
import com.kronos.people.personality.service.PersonalityExtendedAttributesService;
import com.kronos.wfc.commonapp.employment.business.terms.EmploymentTerm;
import com.kronos.wfc.commonapp.requestreviewers.business.RequestReviewerList;
import com.kronos.wfc.commonapp.types.business.EmploymentStatusType;
import com.kronos.wfc.commonapp.types.business.LogonProfile;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;

import com.kronos.wfc.scheduling.core.business.profiles.groups.ScheduleGroupProfile;
import com.kronos.wfc.scheduling.core.business.profiles.shifttemplates.ShiftTemplateProfile;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.kronos.wfc.scheduling.core.business.profiles.patterns.SchedulePatternProfile;

import jakarta.inject.Inject;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PersonalityExtendedAttributesServiceImpl implements PersonalityExtendedAttributesService {
	
	@Inject
	PersonalityFacadeForCachedAttributes personalityFacadeForCachedAttributes;
	
	@Inject
	PersonalityFacade personalityfacade;
	
	@Inject
	AdapterHelper adapterHelper;
	
	@Inject
	PersonalityServiceImpl personalityServiceImpl;

	@Inject
	IEmployeeGroupQueryService employeeGroupQueryService;

	@Inject
	IWorkEmployeeServiceFacade workEmployeeServiceFacade;

	@Inject
	ICombinedHomeAccountService combinedHomeAccountService;

	@Inject
	IPositionHomeAccountService positionHomeAccountService;

	/**
	 * creates a mapping of Long to String from input List of Long via the function.
	 * @param ids List of Long
	 * @param function passed in function
	 * @return Mapping of long to string
	 */
	protected Map<Long, String> getMapFromListOfLong(List<Long> ids, Function<Long, String> function) {
		Map<Long, String> map = new HashMap<>();
		if (!CollectionUtils.isEmpty(ids)) {
			ids.forEach(id ->map.put(id, function.apply(id)));
		}
		return map;
	}
	
	/**
         * creates a mapping of Long to Boolean from input List of Long via the function.
         * @param ids List of Long
         * @param function passed in function
         * @return Mapping of Long to Boolean
         */
        protected Map<Long, Boolean> getBooleanMapFromListOfLong(List<Long> ids, Function<Long, Boolean> function) {
                Map<Long, Boolean> map = new HashMap<>();
                if (!CollectionUtils.isEmpty(ids)) {
                        ids.forEach(id ->map.put(id, function.apply(id)));
                }
                return map;
        }
	
	/**
	 * This method is a generic method which fetches Map of Long ids and String names from cache.
	 * @param <T> Type of entries required in bi-consumer
	 * @param ids List of Long
	 * @param dataExtractorFunction Function which produces List of T
	 * @param consumer a bi-Consumer of map and T
	 * @return Mapping of long to String
	 */
	protected <T> Map<Long, String> getListFromListCache(List<Long> ids, Function<List<ObjectIdLong>, List<T>> dataExtractorFunction, BiConsumer<Map<Long, String>, T> consumer) {
		List<ObjectIdLong> objectIdLong = adapterHelper.getListOfObjectIdsFromListOfLong(ids);
		List<T> outputList = dataExtractorFunction.apply(objectIdLong);
		Map<Long, String> idNameMap = new HashMap<>();
		outputList.forEach(item -> consumer.accept(idNameMap, item));
		return idNameMap;
	}
	

	@Override
	public List<CostCenterEntry> getCostCentersForAPerson(Long personId, LocalDate effectiveDate, LocalDate expirationDate,Long primaryOrgID){
	List<CombinedHomeAccount> combinedHomeAccountList=combinedHomeAccountService.getHomeAccounts(personId, effectiveDate, expirationDate);
	List<CostCenterEntry> costCenterEntryList = new ArrayList<>();
	if(combinedHomeAccountList!=null){
		combinedHomeAccountList.forEach(combinedHomeAccount->{
			LaborAccount account=combinedHomeAccount.getLaborAccount();
				if (account != null && account.getLlid7() != null && !account.getLlid7().equals("")
						&& combinedHomeAccount.getOrgNodeId() != null
						&& combinedHomeAccount.getOrgNodeId().equals(primaryOrgID)) {
					CostCenterEntry costCenterEntry=new CostCenterEntry();
				costCenterEntry.setCostCenterId(account.getLlid7());
				costCenterEntry.setEffectiveDate(combinedHomeAccount.getEffectiveDate());
				costCenterEntry.setExpirationDate(combinedHomeAccount.getExpirationDate());
				costCenterEntryList.add(costCenterEntry);
			}

		});
		costCenterEntryList.sort(Comparator.comparing(CostCenterEntry::getExpirationDate));
	}

	return costCenterEntryList;

}

	@Override
	public List<CostCenterEntry> getCostCentersForAPosition(Long positionId, LocalDate effectiveDate, LocalDate expirationDate,Long primaryOrgID){
	List<PositionHomeAccount> positionHomeAccountList=positionHomeAccountService.getHomeAccounts(positionId, effectiveDate, expirationDate);
	List<CostCenterEntry> costCenterEntryList = new ArrayList<>();
	if(positionHomeAccountList!=null){
		positionHomeAccountList.forEach(positionHomeAccount->{
			LaborAccount account=positionHomeAccount.getLaborAccount();
				if (account != null && account.getLlid7() != null && !account.getLlid7().equals("")
						&& positionHomeAccount.getOrgNodeId() != null
						&& positionHomeAccount.getOrgNodeId().equals(primaryOrgID)) {
				CostCenterEntry costCenterEntry=new CostCenterEntry();
				costCenterEntry.setCostCenterId(account.getLlid7());
				costCenterEntry.setEffectiveDate(positionHomeAccount.getEffectiveDate());
				costCenterEntry.setExpirationDate(positionHomeAccount.getExpirationDate());
				costCenterEntryList.add(costCenterEntry);
			}
		});
		costCenterEntryList.sort(Comparator.comparing(CostCenterEntry::getExpirationDate));
	}

	return costCenterEntryList;

}




	/**
	 * Converts Long value to ObjectIdLong instance
	 * @param value Long value
	 * @return ObjectIdLong instance
	 */
	public static ObjectIdLong longToObjectIdLong(Long value) {
		return new ObjectIdLong(value);
	}
			
	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getJobTransferSet(java.lang.Long)
	 */
	@Override
	public String getJobTransferSet(Long id) {
		Function<Long, String> function = transferId->adapterHelper.getIfNotNull(personalityfacade.getOrgMapAccessorService().findOrgMapGroupById(longToObjectIdLong(transferId)), t->t.getName());
		return adapterHelper.getIfNotNull(id, function);
	}
	
	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getJobTransferSet(java.util.List)
	 */
	@Override
	public Map<Long, String> getJobTransferSet(List<Long> ids) {
		return getMapFromListOfLong(ids, this::getJobTransferSet);
	}
	
	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getWorkerType(java.lang.Long)
	 */
	@Override
	public String getWorkerType(Long id) {
		return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getWorkerTypeFromId(id), t->t.getName());
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getWorkerType(java.util.List)
	 */
	@Override
	public Map<Long, String> getWorkerType(List<Long> ids) {
		return getMapFromListOfLong(ids, this::getWorkerType);
	}
	
	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getPayRule(java.lang.Long)
	 */
	@Override
	public String getPayRule(Long id) {
		return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getPayRuleFromId(id), t->t.getName());
	}
	
	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getPayRule(java.util.List)
	 */
	@Override
	public Map<Long, String> getPayRule(List<Long> ids) {
		return getMapFromListOfLong(ids, this::getPayRule);
	}
	
	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getEmploymentTerms(java.lang.Long)
	 */
	@Override
	public String getEmploymentTerms(Long id) {
		return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getEmploymentTermFromId(id), t->t.getName());
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getEmploymentTerms(java.util.List)
	 */
	@Override
	public Map<Long, String> getEmploymentTerms(List<Long> ids) {
		Function<List<ObjectIdLong>, List<EmploymentTerm>> employmentTermExtractorFunction =  personalityFacadeForCachedAttributes::getEmploymentTermFromIdList;
		BiConsumer<Map<Long, String>, EmploymentTerm> consumer = (map, employmentTerm) -> map.put(adapterHelper.getLongFromObjectId(employmentTerm.getId()), employmentTerm.getName());
		return getListFromListCache(ids, employmentTermExtractorFunction, consumer);
	}
	
	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getAccrualProfileName(java.lang.Long)
	 */
	@Override
	public String getAccrualProfileName(Long id) {
		return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getAccrualProfileFromId(id), t->t.getShortName());
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getAccrualProfileName(java.util.List)
	 */
	@Override
	public Map<Long, String> getAccrualProfileName(List<Long> ids) {
		return getMapFromListOfLong(ids, this::getAccrualProfileName);
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getExpectedHrsName(java.lang.Long)
	 */
	@Override
	public String getExpectedHrsName(Long id) {
		return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getTimePeriodTypeFromId(id), t->t.getShortName());
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getExpectedHrsName(java.util.List)
	 */
	@Override
	public Map<Long, String> getExpectedHrsName(List<Long> ids) {
		return getMapFromListOfLong(ids, this::getExpectedHrsName);
	}
	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getPurposeName(java.lang.Long)
	 */
	@Override
	public String getDateName(Long id) {
		return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getCustomDateName(personalityFacadeForCachedAttributes.getObjectIdLong(id)), t->t.getName());
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getDateName(java.util.List)
	 */
	@Override
	public Map<Long, String> getDateName(List<Long> ids) {
		return getMapFromListOfLong(ids, this::getDateName);
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getPurposeName(java.lang.Long)
	 */
	@Override
	public String getPurposeName(Long id) {
		return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getRequestPurposeFromId(id), t->t.getName());
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getPurposeName(java.util.List)
	 */
	@Override
	public Map<Long, String> getPurposeName(List<Long> ids) {
		return getMapFromListOfLong(ids, this::getPurposeName);
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getReviewerListName(java.lang.Long)
	 */
	@Override
	public String getReviewerListName(Long id) {
		return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getRequestReviewerListFromId(id), RequestReviewerList::fetchAssociatedName);
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getReviewerListName(java.util.List)
	 */
	@Override
	public Map<Long, String> getReviewerListName(List<Long> ids) {
		return getMapFromListOfLong(ids, this::getReviewerListName);
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getWageProfileName(java.lang.Long)
	 */
	@Override
	public String getWageProfileName(Long id) {
		return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getWageProfileFromId(id), t->t.getName());
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getWageProfileName(java.util.List)
	 */
	@Override
	public Map<Long, String> getWageProfileName(List<Long> ids) {
		return getMapFromListOfLong(ids, this::getWageProfileName);
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getLogonProfileName(java.lang.Long)
	 */
	@Override
	public String getLogonProfileName(Long id) {
		return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getLogonProfileFromId(id), t->t.getName());
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getLogonProfileName(java.util.List)
	 */
	@Override
	public Map<Long, String> getLogonProfileName(List<Long> ids) {
		return getMapFromListOfLong(ids, this::getLogonProfileName);
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getAuthenticationType(java.lang.Long)
	 */
	@Override
	public String getAuthenticationType(Long id){
		return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getAuthenticationType(id), t->t.getName());
	}
	
	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getAuthenticationType(java.util.List)
	 */
	@Override
	public Map<Long, String> getAuthenticationType(List<Long> ids){
		return getMapFromListOfLong(ids, this::getAuthenticationType);
	}
	
	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getSuperviserFullName(java.lang.Long)
	 */
	@Override
	public String getFullName(Long id){
		PersonalityResponse<EmployeeExtension> empExtension=personalityServiceImpl.findEmployeeExtension(id);
		return AdapterHelper.getIfPredicatePass(empExtension, extension->!extension.isExceptionPresent(), extension-> extension.getExtension().getFullName());
		
	}
	
	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getSuperviserFullName(java.util.List)
	 */
	@Override
	public Map<Long, String> getFullName(List<Long> ids){
		return getMapFromListOfLong(ids, this::getFullName);
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getprocessProfile(java.lang.Long)
	 */
	@Override
	public String getProcessProfile(Long id) {
		return  adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getProcessProfile(id), t->t.getName());
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getprocessProfile(java.util.List)
	 */
	@Override
	public Map<Long, String> getProcessProfile(List<Long> ids) {
		return getMapFromListOfLong(ids, this::getProcessProfile);
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getPrimaryJob(java.lang.Long, java.time.LocalDate)
	 */
	@Override
	public String getPrimaryJob(Long id, LocalDate date) {
		return adapterHelper.getIfNotNull(id, jobId->personalityFacadeForCachedAttributes.getPrimaryJob(jobId, date));
	}

	/* (non-Javadoc)
	 * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getPrimaryJob(java.util.List, java.time.LocalDate)
	 */
	@Override
	public Map<Long, String> getPrimaryJob(List<Long> ids,LocalDate date) {
		return getMapFromListOfLong(ids, id->getPrimaryJob(id, date));
	}

	/* (non-Javadoc)
         * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getDeviceGroupName(java.lang.Long)
         */
        @Override
        public String getDeviceGroupName(final Long id) {
                return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getDeviceGroupNameFromId(id), t-> t);
        }
        
        /* (non-Javadoc)
         * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getDeviceGroupName(java.util.List)
         */
		@Override
		public Map<Long, String> getDeviceGroupName(List<Long> ids) {
			List<DeviceGroup> deviceGroups = personalityFacadeForCachedAttributes.getDeviceGroupsFromIds(ids);
			return deviceGroups.stream()
					.collect(Collectors.toMap(DeviceGroup::getId, DeviceGroup::getName));
		}
        
        /* (non-Javadoc)
         * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getDeviceProfileName(java.lang.Long)
         */
        @Override
        public String getDeviceProfileName(final Long id) {
                return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getDeviceProfileNameFromId(id), t-> t);
        }
        
        /* (non-Javadoc)
         * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getDeviceProfileName(java.util.List)
         */
        @Override
        public Map<Long,String> getDeviceProfileName(List<Long> ids) {
            return getMapFromListOfLong(ids, this::getDeviceProfileName);
        }

	@Override
	public String getTTIPUserProfileName(Long id) {
		return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getTTIPUserProfileNameFromId(id), t -> t);
	}

	@Override
	public Map<Long, String> getTTIPUserProfileName(List<Long> ids) {
		return getMapFromListOfLong(ids, this::getTTIPUserProfileName);
	}

	@Override
	public String getEmployeeGroup(Long id) {
		return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getEmployeeGroupName(id), t-> t);

	}
	
	/* (non-Javadoc)
         * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#isFingerEnrolled(long)
         */
	@Override
	public Boolean isFingerEnrolled(long employeeId) {
	    return personalityFacadeForCachedAttributes.isFingerEnrolled(employeeId);
	}
        
	/* (non-Javadoc)
        * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#isFingerEnrolled(java.util.List)
        */
	@Override
        public Map<Long,Boolean> isFingerEnrolled(List<Long> employeeIds) {
	    return getBooleanMapFromListOfLong(employeeIds, this::isFingerEnrolled);
	}
	
	/* (non-Javadoc)
         * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#isFingerEnrolledForIdentification(long)
         */
	@Override
	public Boolean isFingerEnrolledForIdentification(long employeeId) {
	    return personalityFacadeForCachedAttributes.isFingerEnrolledForIdentification(employeeId);
	}
        
	/* (non-Javadoc)
         * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#isFingerEnrolledForIdentification(java.util.List)
         */
	@Override
        public Map<Long,Boolean> isFingerEnrolledForIdentification(List<Long> employeeIds) {
	    return getBooleanMapFromListOfLong(employeeIds, this::isFingerEnrolledForIdentification);
	}
	
	/* (non-Javadoc)
         * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getPrimaryFingerThreshold(long)
         */
	@Override
	public String getPrimaryFingerThreshold(long employeeId) {
	    return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getPrimaryFingerThreshold(employeeId), t-> t);
	}
        
	/* (non-Javadoc)
         * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getPrimaryFingerThreshold(java.util.List)
         */
	@Override
        public Map<Long,String> getPrimaryFingerThreshold(List<Long> employeeIds) {
	    return getMapFromListOfLong(employeeIds, this::getPrimaryFingerThreshold);
	}
	
	/* (non-Javadoc)
         * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getPrimaryFingerEnrollmentLocation(long)
         */
	@Override
	public String getPrimaryFingerEnrollmentLocation(final long employeeId) {
	    return adapterHelper.getIfNotNull(personalityFacadeForCachedAttributes.getPrimaryFingerEnrollmentLocation(employeeId), t-> t);
	}
        
	/* (non-Javadoc)
         * @see com.kronos.people.personality.service.PersonalityExtendedAttributesService#getPrimaryFingerEnrollmentLocation(java.util.List)
         */
	@Override
        public Map<Long,String> getPrimaryFingerEnrollmentLocation(List<Long> employeeIds) {
	    return getMapFromListOfLong(employeeIds, this::getPrimaryFingerEnrollmentLocation);
	}

	@Override
	public String getEmploymentStatus(Long employmentStatusTypeId) {
		return EmploymentStatusType.getEmploymentStatusType(longToObjectIdLong(employmentStatusTypeId)).getName();
	}
	
	@Override
	public Map<Long,String> getAllLogonProfilesMap(){
		List<LogonProfile> logonProfiles=LogonProfile.getLogonProfiles();
		if(logonProfiles!=null && !logonProfiles.isEmpty()){
			return logonProfiles.stream().collect(Collectors.toMap(x->x.getLogonProfileId().longValue(), LogonProfile::getName));	
		}
		return Collections.emptyMap();
	}

    @Override
    public Map<Long, String> getAllPatternTemplateProfiles() {
        List<SchedulePatternProfile> schedulePatternProfiles = SchedulePatternProfile.getSchedulePatternProfiles();
        if (schedulePatternProfiles != null && !schedulePatternProfiles.isEmpty()) {
            return schedulePatternProfiles.stream().collect(Collectors.toMap(p -> p.getProfileId().toLong(), p -> p.getName()));
        }
        return Collections.emptyMap();
    }

	@Override
	public String getScheduleGroupProfileName(Long scheduleGroupProfileId) {
		return scheduleGroupProfileId == null ? null : ScheduleGroupProfile.getScheduleGroupProfile(longToObjectIdLong(scheduleGroupProfileId)).getName();
	}

	@Override
	public String getShiftTemplateProfileName(Long shiftCodeId) {
		return shiftCodeId == null ? null : ShiftTemplateProfile.getShiftTemplateProfile(longToObjectIdLong(shiftCodeId)).getName();
	}

	@Override
	public String getWorkActivityName(Long id) {
		return workEmployeeServiceFacade.getActivityName(id);
	}

	@Override
	public String getWorkActivityProfileName(Long id) {
		return workEmployeeServiceFacade.getProfileName(id);
	}

	@Override
	public String getWorkQueryName(Long id) {
		return workEmployeeServiceFacade.getQueryName(id);
	}
}