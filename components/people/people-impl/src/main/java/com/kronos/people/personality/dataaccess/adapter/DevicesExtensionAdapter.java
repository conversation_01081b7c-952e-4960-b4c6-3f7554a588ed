/***********************************************************************
 * DeviceExtensionAdapter.java
 * 
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.adapter;

import static com.kronos.people.personality.dataaccess.adapter.AdapterHelper.setIfNotNull;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Collection;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.datacollection.udm.service.facescan.api.dto.FaceScan;
import com.kronos.datacollection.udm.service.fingerscan.api.dto.FingerScan;
import com.kronos.datacollection.udm.service.ttipuserprofile.api.dto.TTIPEmployeeAttributes;
import com.kronos.datacollection.udm.service.ttipuserprofile.api.dto.TTIPUserProfile;
import com.kronos.datacollection.udm.service.bioconsentdetails.api.dto.BiometricConsentDetails;
import com.kronos.datacollection.udm.service.bioconsentdetails.api.enums.BiometricConsentType;
import com.kronos.datacollection.udm.service.bioconsentdetails.api.enums.BiometricType;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.model.extension.BaseExtension;
import com.kronos.people.personality.model.extension.DevicesExtension;
import com.kronos.people.personality.model.extension.entry.BadgeDetailsEntry;
import com.kronos.people.personality.model.extension.entry.BiometricConsentDetailsEntry;
import com.kronos.people.personality.model.extension.entry.EffectiveDateTimedCollection;
import com.kronos.people.personality.model.extension.entry.FaceScanEntry;
import com.kronos.wfc.commonapp.people.business.person.BadgeAssignment;
import com.kronos.wfc.commonapp.people.business.person.BadgeAssignmentSet;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDateTime;
import org.apache.commons.lang3.StringUtils;

/**
 * The {@code DeviceExtensionAdapter} class populates the device extension from
 * legacy personality.
 * 
 * <AUTHOR>
 *
 */
@Named
public class DevicesExtensionAdapter implements ExtensionAdapter<DevicesExtension> {

	/**
	 * Instance of {@link PersonalityFacade}. It provides methods for finding
	 * personality and personality triplet.
	 */
	@Inject
	private PersonalityFacade personalityFacade;

	/**
	 * Instance of {@link AdapterHelper}. It provides the utility methods.
	 */
	@Inject
	private AdapterHelper adapterHelper;
	
	/**
	 * Sets the local time from KDateTime.
	 */
	BiConsumer<Consumer<LocalTime>, KDateTime> setLocalTime = (consumer, dateTime) ->
		AdapterHelper.setIfNotNull(dateTime, consumer, (localDateConsumer, legacyDateTime)->localDateConsumer.accept(adapterHelper.kTimeToLocalTime(legacyDateTime.getTime())));

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.kronos.people.personality.service.impl.dataaccess.adapter.
	 * ExtensionAdapter
	 * #convert(com.kronos.wfc.commonapp.people.business.personality
	 * .Personality)
	 */
	/**
	 * {@inheritDoc}
	 */
	@Override
	public DevicesExtension convert(Personality personality, LocalDate snapShotDate) {

		DevicesExtension devicesExtension = new DevicesExtension();
		ObjectIdLong personId = personality.getPersonId();
		devicesExtension.setDeviceGroupId(personalityFacade.getDeviceGroupIdByPersonId(personId));
		devicesExtension.setProfileId(personalityFacade.getDeviceProfileIdByPersonId(personId));
		setFingerScan(devicesExtension, personId);
		devicesExtension.setFaceScan(createFaceScan(personId, personality.getPersonNumber()));
		setTTIPEmployeeAttributes(devicesExtension, personId);
		devicesExtension.setFingerConsentDetails(getConsentDetailsByBioType(personId, BiometricType.FINGER_TYPE));
		devicesExtension.setFaceConsentDetails(getConsentDetailsByBioType(personId, BiometricType.FACE_TYPE));
		//setBadgeDetails(personality, devicesExtension);
		setIfNotNull(personality.getNameData(), devicesExtension, (t, u) -> setPersonRelatedAttributes(t, u));
		return devicesExtension;
	}

	private void setTTIPEmployeeAttributes(DevicesExtension devicesExtension, ObjectIdLong personId) {
		TTIPEmployeeAttributes ttipEmployeeAttributes = personalityFacade.getTTIPEmployeeAttributes(personId);
		if(ttipEmployeeAttributes != null) {
			devicesExtension.isTeleTimeIPEmployee(true);
			devicesExtension.setChangePasswordIsRequired(ttipEmployeeAttributes.getIsChangePasswordRequired());
			devicesExtension.setTeleTimeIPId(ttipEmployeeAttributes.getTTIPUserId());
			TTIPUserProfile ttipUserProfile = ttipEmployeeAttributes.getTTIPUserProfile();
			if (ttipUserProfile != null) {
				devicesExtension.setTeleTimeIPUserProfileId(ttipUserProfile.getId());
				devicesExtension.setTeleTimeIPUserProfileName(ttipUserProfile.getName());
			}
		}
	}

	private void setFingerScan(DevicesExtension devicesExtension, ObjectIdLong personId) {
		FingerScan fingerScan = personalityFacade.getFingerScanByPersonId(personId);
		if(fingerScan != null) {
			devicesExtension.setFingerEnrolled(fingerScan.getIsEnrolled());
			devicesExtension.setFingerEnrolledForIdentification(fingerScan.getIsEnrolledForIdentification());
			devicesExtension.setPrimaryFingerThreshold(fingerScan.getPrimaryFingerThreshold());
			devicesExtension.setPrimaryFingerEnrollmentLocation(fingerScan.getPrimaryFingerEnrollmentLocation());
		} else {
			devicesExtension.setFingerEnrolled(false);
			devicesExtension.setFingerEnrolledForIdentification(false);
		}
	}

	private BiometricConsentDetailsEntry getConsentDetailsByBioType(ObjectIdLong personId, BiometricType biometricType) {
        BiometricConsentDetails biometricConsentDetails = personalityFacade.getBiometricConsentDetailsByPersonIdAndConsentType(personId, biometricType);
		BiometricConsentDetailsEntry.Builder consentDetailBuilder = new BiometricConsentDetailsEntry.Builder();
        if(biometricConsentDetails != null) {
			final BiometricConsentType biometricConsentType = biometricConsentDetails.getConsentStatus();
			consentDetailBuilder.withConsentStatus(biometricConsentType != null ? biometricConsentType.name() : null)
								.withConsentDateTime(biometricConsentDetails.getConsentDateTime())
								.withConsentLocation(getConsentLocation(personId, biometricType, biometricConsentDetails.getConsentLocation()))
								.withConsentForm(biometricConsentDetails.getConsentForm())
								.withConsentText(biometricConsentDetails.getConsentFormText());
        }
		return consentDetailBuilder.build();
	}

	private String getConsentLocation(ObjectIdLong personId, BiometricType biometricType, String consentLocation) {
		if (StringUtils.isEmpty(consentLocation)) {
			switch (biometricType) {
				case FINGER_TYPE:
					return personalityFacade.getPrimaryFingerEnrollmentLocation(personId);
				case FACE_TYPE:
					return personalityFacade.getFaceEnrollmentLocation(personId);
			}
		}
		return consentLocation;
	}

	@Override
	public DevicesExtension createSnapshot(DevicesExtension extension) {
		return Optional.ofNullable(extension).map(DevicesExtension::new).orElse(null);
	}


	public void setBadgeDetails(Personality personality, BaseExtension baseExtension) {
		baseExtension.setBadges(convertEffDatedBadgeDetails(personality.getBadgeAssignmentSet()));
	}

	protected void setPersonRelatedAttributes(DevicesExtension devicesExtension, Person person) {
	    devicesExtension.setFingerRequired(person.getFingerRequiredSwitch() != null && person.getFingerRequiredSwitch() == 1L);
	    if (personalityFacade.isFaceScanFeatureEnabled(person.getPersonId(), person.getPersonNumber())) {
			devicesExtension.setFaceRequired(person.getFaceRequiredSwitch() != null && person.getFaceRequiredSwitch() == 1L);
		}
	}
	
	/**
	 * THis method convert the badge details to effective dated collection of
	 * badge details entry.
	 * 
	 * @param badgeAssignmentSet
	 *            This is a set of BadgeAssignment objects.
	 * @return the effective dated collection of badge details entry.
	 */
	protected EffectiveDateTimedCollection<BadgeDetailsEntry> convertEffDatedBadgeDetails(BadgeAssignmentSet badgeAssignmentSet) {
		return badgeAssignmentSet != null ? new EffectiveDateTimedCollection<>(getDatedBadgeDetailsEntries(badgeAssignmentSet)) : null;
	}

	/**
	 * This method gets the badge detail entries.
	 * 
	 * @param badgeAssignmentSet
	 *            {@link BadgeAssignmentSet}, a set of BadgeAssignment objects.
	 * @return collection of {@link BadgeDetailsEntry}.
	 */
	protected Collection<BadgeDetailsEntry> getDatedBadgeDetailsEntries(BadgeAssignmentSet badgeAssignmentSet) {
		return adapterHelper.convertLegacyCollection(getDatedLegacyBadgeAssignment(badgeAssignmentSet),
				legacyBadgeAssignment -> getDatedBadgeAssignment(legacyBadgeAssignment));
	}

	/**
	 * This methods gets the dated legacy badge assignment.
	 * 
	 * @param badgeAssignmentSet
	 *            {@link BadgeAssignmentSet}, a set of BadgeAssignment objects.
	 * @return collection of {@link BadgeAssignment}.
	 */
	@SuppressWarnings("unchecked")
	protected Collection<BadgeAssignment> getDatedLegacyBadgeAssignment(BadgeAssignmentSet badgeAssignmentSet) {
		return badgeAssignmentSet.getAllBadgeAssignments();
	}

	/**
	 * This method gets the dated badge assignment.
	 * 
	 * @param badgeAssignment
	 *            {@link BadgeAssignment}, the BadgeAssignment identifies a
	 *            badge number used during a specific time period.
	 * @return {@link BadgeDetailsEntry}, the effective dated badge details.
	 */
	protected BadgeDetailsEntry getDatedBadgeAssignment(BadgeAssignment badgeAssignment) {

		KDateTime effectiveDateTime = badgeAssignment.getEffectiveDateTime();
		KDateTime expirationDateTime = badgeAssignment.getExpirationDateTime();
		BadgeDetailsEntry bde = new BadgeDetailsEntry(adapterHelper.kDateTimeToLocalDateTime(effectiveDateTime), adapterHelper.kDateTimeToLocalDateTime(expirationDateTime));
		bde.setBadgeNumber(badgeAssignment.getBadgeNumber());
		adapterHelper.setLongFromObjectIdLong(bde::setBadgeAssignmentId, badgeAssignment.getBadgeAssignmentId());
		bde.setVersionCount(badgeAssignment.getVersionCount());
		return bde;

	}

	public void setAdapterHelper(AdapterHelper adapterHelper) {
		this.adapterHelper = adapterHelper;
	}
	
	/**
	 * Setter method for {@code  personalityFacade}.
	 * 
	 * @param personalityFacade
	 *            Personality facade used for fetching legacy personality
	 *            objects.
	 */
	public void setPersonalityFacade(PersonalityFacade personalityFacade) {
		this.personalityFacade = personalityFacade;
	}

	private FaceScanEntry createFaceScan(ObjectIdLong personId, String personNumber) {
		FaceScan faceScan = personalityFacade.getFaceScanByPersonId(personId);
		FaceScanEntry faceScanEntry = new FaceScanEntry();

		if(faceScan == null) {
			faceScanEntry.setEnrolled(false);
			faceScanEntry.setTemplateDeleted(false);
		} else {
			faceScanEntry.setEnrolled(faceScan.getIsEnrolled());
			faceScanEntry.setEnrollmentDateTime(faceScan.getEnrollmentDateTime());
			faceScanEntry.setThreshold(faceScan.getThreshold());
			faceScanEntry.setThresholdLabel(faceScan.getThresholdLabel());
			faceScanEntry.setQualityScore(faceScan.getQualityScore());
			faceScanEntry.setTemplateDeleted(faceScan.getIsDeleted());
			faceScanEntry.setEnrollmentLocation(faceScan.getEnrollmentLocation());
		}
		return faceScanEntry;
	}
}
