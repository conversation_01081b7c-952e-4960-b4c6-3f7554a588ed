package com.kronos.people.personality.dataaccess.legacy;

import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;

public interface ForecastMapDAPFacade {

	/**
	 * Gets forecast map profile name by id.
	 * 
	 * @param id id
	 * @return forecast map profile name
	 */
	String getForecastMapName(ObjectIdLong id);

	/**
	 * Gets forecast map profile id by name.
	 * 
	 * @param name forecast map profile name
	 * @return id
	 */
	ObjectIdLong getForecastMapId(String name);
}
