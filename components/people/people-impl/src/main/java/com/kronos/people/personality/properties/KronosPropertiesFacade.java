/***********************************************************************
 * KronosPropertiesFacade.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/

package com.kronos.people.personality.properties;

import java.util.Optional;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;

import com.kronos.commonapp.kronosproperties.api.IKProperties;

@Named
@Lazy
public class KronosPropertiesFacade {

	private static final String DEFAULT_VALUE = "true";
	@Inject
	IKProperties kronosProperties;
	/**
	 * Default constructor
	 */
	public KronosPropertiesFacade() {

	}

	/**
	 * To get Integer for properties.
	 * 
	 * @param key
	 *            The {@link String} key
	 * @param defaultValue
	 *            The default value
	 * @return The property value for a passed key
	 */
	public Integer getIntegerKronosProperty(String key, Integer defaultValue) {
		String propertyValue = getPropertyFromKronosProperty(key);
		if (Optional.ofNullable(propertyValue).isPresent()) {
			propertyValue = propertyValue.trim();
			if (StringUtils.isNumeric(propertyValue)) {
				return Integer.valueOf(propertyValue);
			}
		}
		return defaultValue;
	}

	/**
	 * Get String value from Kronos properties.
	 * 
	 * @param key
	 *            The {@link String} key
	 * @param defaultValue
	 *            The default value
	 * @return The {@link String} value of the property for a passed key
	 */
	public String getStringKronosProperty(String key, String defaultValue) {
		return kronosProperties.getProperty(key, defaultValue);
	}

	/**
	 * 
	 * @param key
	 *            The {@link String} key
	 * @param defaultValue
	 *            The default value
	 * @return The {@link Boolean} value of the property for a passed key
	 */
	public Boolean getBooleanKronosProperty(String key, Boolean defaultValue) {
		return kronosProperties.getPropertyAsBoolean(key, defaultValue);
	}
	
	/**
	 * @param key
	 *        The {@link String} key
	 * @return Boolean
	 *        The {@link Boolean} value of the property for a passed key
	 */
	public Boolean getBooleanKronosProperty(String key) {
		return kronosProperties.getPropertyAsBoolean(key);
	} 
	
	/**
	 * @param key
	 *       The {@link String} key
	 * @return String
	 *       The {@link String} value of the property for a passed key
	 */
	public String getStringKronosProperty(String key) {
		return kronosProperties.getProperty(key);
	}
	
	/**
	 *  Need to enable below code once createAndUpdateTenantProperties method is merged in IKProperties component
	 * 
	 * @param key
	 *        The {@link String} key
	 * @param value
	 *        The String value
	 */
	public void setKronosTenantProperty(String key, String value){
		kronosProperties.setProperty(key, value);
	}
	
	/**
	 * @param key
	 *        The {@link String} key
	 * @return String
	 *        The {@link String} value of the property for a passed key
	 */
	public String getKronosTenantProperty(String key){
		return kronosProperties.getProperty(key, DEFAULT_VALUE);
	}
	
	/**
	 * @param key
	 *     The {@link String} key
	 */
	public void removeKronosTenantProperty(String key){
		kronosProperties.removeProperty(key);
	}
	
	/**
	 * @param key
	 *     The {@link String} key
	 */
	public void removeKronosProperty(String key){
		kronosProperties.removeProperty(key);
	}

	/**
	 * @param key
	 *            The {@link String} key
	 * @return The {@link String} value of the property for a passed key
	 */
	protected String getPropertyFromKronosProperty(String key) {
		return kronosProperties.get(key);
	}

	/**
	 * @param kronosProperties the kronosProperties to set
	 */
	public void setKronosProperties(IKProperties kronosProperties) {
		this.kronosProperties = kronosProperties;
	}
	

}
