package com.kronos.people.personality.dataaccess.adapter;

import com.kronos.commonapp.labortransfer.api.ILaborTransferService;
import com.kronos.commonapp.labortransfer.model.ResolvedLaborCategoryItems;
import com.kronos.commonapp.orgmap.setup.model.OrgObjectRef;
import com.kronos.commonapp.orgmap.traversal.api.IOrgMapService;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.entry.CostCenterEntry;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.PrimaryJobAccountEntry;
import com.kronos.people.personality.service.PersonalityExtendedAttributesService;
import com.kronos.people.personality.util.LogTimeHelper;
import com.kronos.wfc.commonapp.laborlevel.business.entries.LaborAccount;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignment;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignmentDetails;
import com.kronos.wfc.commonapp.people.business.jobassignment.PrimaryLaborAccount;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import static com.kronos.people.personality.dataaccess.adapter.AdapterHelper.extractIfNotNull;
import static com.kronos.people.personality.dataaccess.adapter.AdapterHelper.setIfNotNull;

/**
 * This class have utility methods for EmployeeExtension
 *
 * <AUTHOR>
 *
 */
@Named
public class EmployeeExtensionAdapterHelper {

	static final String LLE_SEPRATOR_CHAR = "/";

	/**
	 * OrgMap service of name ORG to let us communicate with new OrgMap api.
	 */
	@Inject
	@Named("ORG")
	IOrgMapService orgmapservice;

	/**
	 * Instance of {@link AdapterHelper}. It provides the utility methods.
	 */
	@Inject
	AdapterHelper converterHelper;

	@Inject
	@Lazy
	ILaborTransferService labourTransferService;
	/**
	 * Instance of {@link PersonalityFacade}. It provides methods for finding
	 * personality and personality triplet.
	 */
	@Inject
	PersonalityFacade personalityFacade;

	@Inject
	@Lazy
	PersonalityExtendedAttributesService personalityExtendedAttributesService;

	BiFunction<PrimaryLaborAccount,Long, PrimaryJobAccountEntry> primaryLaborAccountFunction = (
			PrimaryLaborAccount edon, Long personId) -> {
		LocalDate effectiveDate = converterHelper.kDateToLocalDate(edon
				.getEffectiveDate());
		LocalDate expirationDate = converterHelper.kDateToLocalDate(edon
				.getExpirationDate());
		ObjectIdLong primaryOrganizationId = edon.getPrimaryOrganizationId();
		String primaryJobName = getPrimaryJobName(primaryOrganizationId, edon.getEffectiveDate());
		List<ResolvedLaborCategoryItems> resolvedLaborcategory=labourTransferService.resolveLaborCategoriesFromId(Arrays.asList(converterHelper.getLongFromObjectIdLong(edon.getLaborAccountId())));
		PrimaryJobAccountEntry primaryJobAccountEntry = new PrimaryJobAccountEntry();
		primaryJobAccountEntry.setEffectiveDate(effectiveDate);
		primaryJobAccountEntry.setExpirationDate(expirationDate);
		primaryJobAccountEntry.setLaborAccountId(converterHelper.getLongFromObjectIdLong(edon.getLaborAccountId()));
		primaryJobAccountEntry.setPrimaryJob(primaryJobName);
		String labourCategoryString = resolvedLaborcategory.get(0).getLaborCategoryString();
		primaryJobAccountEntry.setPrimaryLabourAccount(labourCategoryString);
		primaryJobAccountEntry.setPrimaryLabourCategory(labourCategoryString);
		primaryJobAccountEntry.setPrimaryOrganizationId(converterHelper.getLongFromObjectIdLong(edon.getPrimaryOrganizationId()));
		List<CostCenterEntry> costCenterHistory=personalityExtendedAttributesService.getCostCentersForAPerson(personId, effectiveDate, expirationDate,primaryJobAccountEntry.getPrimaryOrganizationId());
		primaryJobAccountEntry.setEffDatedCostCenterEntries(costCenterHistory);
		primaryJobAccountEntry.setVersionCount(edon.getVersionCount());
		return primaryJobAccountEntry;
	};

	public EmployeeExtensionAdapterHelper() {
	}

	/**
	 * @param empExtension
	 *            the employee extension.
	 * @param jobAssignment
	 *            the job assignment details.
	 */
	@SuppressWarnings("unchecked")
	public void setJobAssignmentRelatedAttributes(
			EmployeeExtension empExtension, JobAssignment jobAssignment) {

		setIfNotNull(
				jobAssignment.getPrimaryLaborAccounts(),
				empExtension,
				(t, u) -> t
						.setEffDatedPrimaryJobAccount(convertEffDatedPrimaryJobAccount(u
								.getAllMembers(),empExtension.getPersonId())));


		setIfNotNull(jobAssignment.getJobAssignmentDetails(), empExtension, (t,
				u) -> setJobAssignmentDetailsRelatedAttributes(t, u));

	}

	protected void setJobAssignmentDetailsRelatedAttributes(
			EmployeeExtension empExtension,
			JobAssignmentDetails jobAssignmentDetails) {
		setIfNotNull(jobAssignmentDetails.getTimeZone(), empExtension,
				(t, u) -> t.setTimeZoneId(converterHelper.getLongFromObjectId(u
						.getObjectId())));

		empExtension.setSeniorityDate(converterHelper
				.kDateToLocalDate(jobAssignmentDetails.getSeniorityRankDate()));
		empExtension
				.setSupervisorPersonId(converterHelper
						.getLongFromObjectIdLong(jobAssignmentDetails
								.getSupervisorId()));
		converterHelper.setIfObjectIdLongNotNull(
				jobAssignmentDetails.getSupervisorId(), empExtension,
				this::getSupervisorFullName);
	}

	/**
	 * This method covert the {@code JobAssignment} to
	 * {@code EffectiveDatedCollection} of {@code PrimaryJobAccountEntry} by
	 * {@code JobAssignment}.
	 *
	 * @param collection
	 *            {@link Collection} ,collection of PrimaryLaborAccount
	 * @return the effective dated collection {@link EffectiveDatedCollection}
	 *         of {@link PrimaryJobAccountEntry}.
	 */
	protected EffectiveDatedCollection<PrimaryJobAccountEntry> convertEffDatedPrimaryJobAccount(
			Collection<PrimaryLaborAccount> collection, Long personId) {

		if (converterHelper.isNotNull(collection)) {
			try {
				return new EffectiveDatedCollection<>(collection.stream()
						.filter(primaryLaborAccount -> converterHelper.isNotNull(primaryLaborAccount))
						.map(primaryLaborAccount -> primaryLaborAccountFunction.apply(primaryLaborAccount, personId))
						.filter(primaryLaborAccount -> converterHelper.isNotNull(primaryLaborAccount))
						.collect(Collectors.toList()));
			} catch (Exception e) {
				LogTimeHelper.logErrorMessage(e, "Exception in method convertEffDatedPrimaryJobAccount. PersonId: {0}", personId);
				throw e;
			}
		}

		return new EffectiveDatedCollection<>();
	}

	/**
	 * This method gets the supervisor full name by supervisorId.
	 *
	 * @param supervisorId
	 *            {@link ObjectIdLong}, contains the supervisor id.
	 * @param empExtension
	 *            Employee Extension
	 */
	protected void getSupervisorFullName(EmployeeExtension empExtension,
			ObjectIdLong supervisorId) {
		setIfNotNull(personalityFacade.getPerson(supervisorId), empExtension, (
				t, u) -> {
			t.setSupervisorFullName(u.getFullName());
			t.setSupervisorPersonNumber(u.getPersonNumber());
		});
	}

	/**
	 * @param converterHelper
	 *            The converterHelper to set
	 */
	public void setConverterHelper(AdapterHelper converterHelper) {
		this.converterHelper = converterHelper;
	}

	/**
	 * @param personalityFacade
	 *            The personalityFacade to set
	 */
	public void setPersonalityFacade(PersonalityFacade personalityFacade) {
		this.personalityFacade = personalityFacade;
	}

	protected String getPrimaryJobName(ObjectIdLong primaryOrganizationId, KDate effectiveDate) {
		BiFunction<KDate,Long,String> consumer = (kdate,l)->{
				OrgObjectRef orgObjectRef = getObjectRef(l);
				OrgObjectRef resolvedObjectRef = getOrgMapService().resolve(orgObjectRef,
			               converterHelper.kDateToLocalDate(kdate));
			         if(resolvedObjectRef != null) {
			            return resolvedObjectRef.getQualifier();
			         }
			         return null;
		};
		return converterHelper.getIfObjectIdLongNotNull(primaryOrganizationId, effectiveDate, consumer );
	}

	/**
	 * Get org object ref for long id value.
	 * @param longId Long value
	 * @return OrgObjectRef
	 */
	protected OrgObjectRef getObjectRef(Long longId) {
		return new OrgObjectRef(longId);
	}


	/**
	 * This method returns instance of OrgMapService.
	 * @return OrgMapService
	 */
	protected IOrgMapService getOrgMapService() {
		return orgmapservice;
	}



	/**
	 * Get labor account name.
	 * @param pla
	 *            The instance of {@link PrimaryLaborAccount}
	 *
	 * @return - Get the combined labor account name
	 */
	protected String getLaborAccountName(PrimaryLaborAccount pla) {
		List<String> lles = getLaborLevels(pla);
		return lles == null ? "" : formatLaborLevels(lles); //fix for creating name from the actual list
															//and not to assume fixed number of elements.
	}

	protected String formatLaborLevels(List<String> lles) {
		StringBuilder sb = new StringBuilder();
		lles.forEach(lle-> {
			if (!StringUtils.isBlank(lle) && !"null".equalsIgnoreCase(lle)){
				sb.append(lle).append(LLE_SEPRATOR_CHAR);
			}
		});
		if (sb.length() > 1) {
			sb.deleteCharAt(sb.length()-1);
		}
	    return sb.toString();
	}


	protected List<String> getLaborLevels(PrimaryLaborAccount pla) {
		ObjectIdLong laborAccountId =pla.getLaborAccountId();
		//FLC-19822 - when getting Labor Account, allow account with inactive entries
		String[] derivedLLEs = converterHelper.extractIfLegacyNullableNotNull(laborAccountId, lAccId->
			extractIfNotNull(LaborAccount.getById(lAccId, true), laborAccount->LaborAccount
						.decomposeLaborAccountName(laborAccount.getName())));

		String[] userEnteredLLEs = extractIfNotNull(pla.getLaborAccount(),
				laborAccount -> LaborAccount.decomposeLaborAccountName(laborAccount.getName()));

		return combineUserAndLALLEs(userEnteredLLEs, derivedLLEs);
	}



	protected List<String> combineUserAndLALLEs(String[] userLLEs,
			String[] derivedLLEs) {

		List<String> combinedEntries = new ArrayList<String>();
		if (userLLEs != null) {
			for (int index = 0; index < userLLEs.length; index++) {
				addCombinedEntry(userLLEs, derivedLLEs, combinedEntries, index);
			}
		}
		else if (derivedLLEs != null) {
			addDerivedLLEs(derivedLLEs, combinedEntries);
		}
		return combinedEntries;
	}

	/**
	 * Add combined entry on basis of user and derived lle
	 * @param userLLEs user lle
	 * @param derivedLLEs derived lle
	 * @param combinedEntries combined entries
	 * @param index index
	 */
	protected void addCombinedEntry(String[] userLLEs, String[] derivedLLEs,
			List<String> combinedEntries, int index) {
		if (!StringUtils.isBlank(userLLEs[index])) {
			combinedEntries.add(userLLEs[index]);
		}
		else if (derivedLLEs != null && !StringUtils.isBlank(derivedLLEs[index])) {
				combinedEntries.add(derivedLLEs[index]);
		}
	}

	/**
	 * Add derived lles if valid in combined entries
	 * @param derivedLLEs Derived lle
	 * @param combinedEntries Combined entries in which we have to add entries
	 */
	protected void addDerivedLLEs(String[] derivedLLEs, List<String> combinedEntries) {
		for (int index = 0; index < derivedLLEs.length; index++) {
			if (!StringUtils.isBlank(derivedLLEs[index])) {
				combinedEntries.add(derivedLLEs[index]);
			}
		}
	}
}