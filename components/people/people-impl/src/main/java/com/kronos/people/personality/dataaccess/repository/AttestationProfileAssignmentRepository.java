package com.kronos.people.personality.dataaccess.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.kronos.people.personality.dataaccess.entity.AttestationProfileAssignment;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional
public interface AttestationProfileAssignmentRepository extends JpaRepository<AttestationProfileAssignment, Long> {
    /**
     * Returns the number of Attestation Profiles with the given profileId
     * assigned to any person record.
     *
     * @param profileId attestation profile ID
     * @return the number of attestationProfiles with specified profileId
     * assigned to any person record
     */
    @Query("SELECT count(*) FROM AttestationProfileAssignment apa JOIN apa.profile p WHERE p.id=:profileId")
    long countByProfileId(@Param("profileId") Long profileId);

    /**
     * Returns the number of Attestation Assignments with the given assignmentId
     * assigned to any person record.
     *
     * @param assignmentId attestation assignment ID
     * @return the number of attestationAssignments with specified assignmentId
     * assigned to any person record
     */
    @Query("SELECT count(*) FROM AttestationProfileAssignment apa JOIN apa.profile p JOIN p.attestationAssignment a WHERE a.id = :assignmentId")
    long countByAssignmentId(@Param("assignmentId") Long assignmentId);

    /**
     * Returns all Profile Assignments by the given person assignment id.
     *
     * @param personId the person assignment id
     * @return List of Profile Assignments
     */
    @Query("SELECT distinct apa FROM AttestationProfileAssignment apa WHERE apa.personAssignment.id = :personId")
    List<AttestationProfileAssignment> findByPersonId(@Param("personId") Long personId);

    /**
     * Returns all Profile Assignments by the given person assignment ids.
     *
     * @param personIds the person assignment ids
     * @return List of Profile Assignments
     */
    @Query("SELECT distinct apa FROM AttestationProfileAssignment apa WHERE apa.personAssignment.id IN :personIds")
    List<AttestationProfileAssignment> findByPersonIds(@Param("personIds") List<Long> personIds);

    /**
     *
     * Returns all Profile Assignments by the given person assignment ids and roles
     *
     * @param personIds the person assignment ids
     * @param isManagerRoles role indiactor
     * @return List of Profile Assignments
     */
    @Query("SELECT distinct apa FROM AttestationProfileAssignment apa WHERE apa.personAssignment.id IN :personIds and apa.isManagerRole in :isManagerRoles")
    List<AttestationProfileAssignment> findByPersonIdsAndManagerRoles(@Param("personIds") List<Long> personIds, @Param("isManagerRoles") List<Boolean> isManagerRoles);

    /**
     * Returns Profile Assignments by the given person assignment id .
     *
     * @param personId the person assignment id
     * @param isManagerRole if true will return manager role's assignments else return employee role's
     * @return List of Profile Assignments
     */
    @Query("SELECT distinct apa FROM AttestationProfileAssignment apa WHERE apa.personAssignment.id = :personId and apa.isManagerRole = :isManagerRole")
    List<AttestationProfileAssignment> findByPersonIdAndManagerRole(@Param("personId") Long personId, @Param("isManagerRole") Boolean isManagerRole);
}