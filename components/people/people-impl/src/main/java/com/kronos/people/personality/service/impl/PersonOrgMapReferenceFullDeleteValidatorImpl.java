/*
 * *****************************************************************************
 * Copyright (c) 2022 Kronos, Inc. All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * Kronos, Inc. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with Kronos.
 *
 * KRONOS MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE
 * SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
 * PURPOSE, OR NON-INFRINGEMENT. KRONOS SHALL NOT BE LIABLE FOR ANY DAMAGES
 * SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING
 * THIS SOFTWARE OR ITS DERIVATIVES.
 ****************************************************************************
 *
 **/
package com.kronos.people.personality.service.impl;

import com.kronos.commonapp.orgmap.setup.api.IOrgMapReferenceFullDeleteValidator;
import com.kronos.commonapp.orgmap.setup.model.OrgNodeReferenceUsage;
import com.kronos.commonapp.orgmap.setup.model.OrgObjectRef;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.people.personality.dataaccess.repository.PersonRepository;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.inject.Inject;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * This is the implementation class of IOrgMapReferenceFullDeleteValidator
 *
 * <AUTHOR>
 */

@Service
public class PersonOrgMapReferenceFullDeleteValidatorImpl implements IOrgMapReferenceFullDeleteValidator {
	private static final KLogger LOGGER = KLoggerFactory.getKLogger(PersonOrgMapReferenceFullDeleteValidatorImpl.class);

	public static final String PERSON = "person_job";

	@Lazy
	@Inject
	private PersonRepository personRepository;

	@Override
	@Transactional(readOnly = true)
	public List<OrgNodeReferenceUsage> existingBlockingOrgReferences(List<Long> orgNodeIds, int limit) {

		LOGGER.info("METHOD START- [existingBlockingOrgReferences] Parameter - [ (orgNodeIds : {}, limit : {}) ]", orgNodeIds, limit);
		List<Object[]> personItems = personRepository.findByOrgNodeIds(orgNodeIds, limit);
		Map<Object,List<Object[]>> grpByOrg = personItems.stream().collect(Collectors.groupingBy(x -> x[1]));
		List<OrgNodeReferenceUsage> persons = grpByOrg
				.entrySet()
				.stream()
				.map(e -> {
					Long id = ((BigInteger) e.getKey()).longValue();
					List<OrgObjectRef> entityRefs = e.getValue()
							.stream()
							.map(ob -> new OrgObjectRef(((BigInteger) ob[0]).longValue(), (String) ob[2], (String) ob[3]))
							.collect(Collectors.toList());
					return new OrgNodeReferenceUsage(id, entityRefs, PERSON);
				})
				.collect(Collectors.toList());

		LOGGER.info("METHOD END- [existingBlockingOrgReferences] Parameter - [ (orgNodeIds : {}, limit : {}) ]", orgNodeIds, limit);
		return persons;
	}

	@Override
	public String getEntityType() {
		return PERSON;
	}
}
