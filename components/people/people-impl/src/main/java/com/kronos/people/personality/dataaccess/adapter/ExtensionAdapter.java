/***********************************************************************
 * ExtensionAdapter.java
 * 
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.adapter;

import com.kronos.people.personality.model.extension.BaseExtension;
import com.kronos.people.personality.model.extension.IExtension;
import com.kronos.wfc.commonapp.people.business.personality.Personality;

import java.time.LocalDate;

/**
 * The {@code ExtensionAdapter} is interface for the extension converter.
 * 
 * @param <T>
 *            the referent type
 * 
 * <AUTHOR>
 */
public interface ExtensionAdapter<T extends BaseExtension> {

	/**
	 * Populate legacy object to extension by personality {@link Personality}.
	 *
	 * @param personality
	 *            {@link Personality}.
	 * @return T the extension, type of {@link IExtension}.
	 */
	public T convert(Personality personality, LocalDate snapShotDate);

	T createSnapshot(T extension);

}