/***********************************************************************
 * EventType.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.notification.entry;

/**
 * An enum for the Insert, Update and delete events. Events signifies the type
 * of operation to be perform on the cache.
 * 
 * <AUTHOR>
 *
 */
public enum EventType {
	INSERT, UPDATE, DELETE
}
