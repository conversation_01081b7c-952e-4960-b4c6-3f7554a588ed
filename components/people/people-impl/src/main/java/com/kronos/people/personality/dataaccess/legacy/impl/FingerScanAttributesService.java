package com.kronos.people.personality.dataaccess.legacy.impl;

import com.kronos.datacollection.udm.service.fingerscan.api.IFingerScanAttributesService;
import com.kronos.datacollection.udm.service.fingerscan.api.dto.FingerScan;
import com.ukg.container.selectiveloading.exception.NotImplementedException;
import org.springframework.stereotype.Component;

@Component
public class FingerScanAttributesService implements IFingerScanAttributesService {

    @Override
    public boolean isFingerEnrolled(long l) {
        throw new NotImplementedException();
    }

    @Override
    public boolean isFingerEnrolledForIdentification(long l) {
        throw new NotImplementedException();
    }

    @Override
    public String getPrimaryFingerThreshold(long l) {
        throw new NotImplementedException();
    }

    @Override
    public String getPrimaryFingerEnrollmentLocation(long l) {
        throw new NotImplementedException();
    }

    @Override
    public FingerScan getFingerScanByPersonId(long l) {
        throw new NotImplementedException();
    }
}
