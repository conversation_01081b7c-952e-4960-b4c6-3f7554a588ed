package com.kronos.people.personality.dataaccess.legacy;

import com.kronos.commonapp.employeegroup.setup.api.IEmployeeGroupQueryService;
import com.kronos.commonapp.employeegroup.setup.model.EmployeeGroup;
import com.kronos.commonapp.labortransfer.api.ILaborTransferService;
import com.kronos.commonapp.orgmap.setup.model.OrgObjectRef;
import com.kronos.commonapp.orgmap.traversal.api.IOrgMapService;
import com.kronos.datacollection.udm.service.devicegroup.api.IDeviceGroupService;
import com.kronos.datacollection.udm.service.fingerscan.api.IFingerScanAttributesService;
import com.kronos.datacollection.udm.service.devicegroup.api.dto.DeviceGroup;
import com.kronos.datacollection.udm.service.individualprofile.api.IIndividualProfileService;
import com.kronos.datacollection.udm.service.ttipuserprofile.api.ITTIPUserProfileService;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.wfc.commonapp.employment.business.terms.EmploymentTerm;
import com.kronos.wfc.commonapp.processmanager.business.profiles.workflow.WorkflowProfile;
import com.kronos.wfc.commonapp.processmanager.business.profiles.workflow.WorkflowProfileCache;
import com.kronos.wfc.commonapp.requestreviewers.business.RequestPurpose;
import com.kronos.wfc.commonapp.requestreviewers.business.RequestPurposeCache;
import com.kronos.wfc.commonapp.requestreviewers.business.RequestReviewerList;
import com.kronos.wfc.commonapp.requestreviewers.business.RequestReviewerListCache;
import com.kronos.wfc.commonapp.rules.business.PayRule;
import com.kronos.wfc.commonapp.rules.business.PayRuleCache;
import com.kronos.wfc.commonapp.types.business.CustomDateType;
import com.kronos.wfc.commonapp.types.business.CustomDateTypeCache;
import com.kronos.wfc.commonapp.types.business.LogonProfile;
import com.kronos.wfc.commonapp.types.business.LogonProfileCache;
import com.kronos.wfc.commonapp.types.business.TimePeriodType;
import com.kronos.wfc.commonapp.types.business.TimePeriodTypeCache;
import com.kronos.wfc.commonapp.types.business.WorkerType;
import com.kronos.wfc.commonapp.types.business.WorkerTypeCache;
import com.kronos.wfc.platform.persistence.framework.ObjectId;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.business.authentication.types.AuthenticationType;
import com.kronos.wfc.platform.security.business.authentication.types.AuthenticationTypeCache;
import com.kronos.wfc.timekeeping.accruals.business.AccrualProfile;
import com.kronos.wfc.timekeeping.accruals.business.AccrualProfileCache;
import com.kronos.wfc.timekeeping.wages.business.WageProfile;
import com.kronos.wfc.timekeeping.wages.business.WageProfileCache;
import org.springframework.util.CollectionUtils;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * This class returns instances from cache based on the passed id.
 * <AUTHOR>
 *
 */
@Named
public class PersonalityFacadeForCachedAttributes {
	
	@Inject
	AdapterHelper adapterHelper;
	
	@Inject
	ILaborTransferService labourTransferService;
	
	@Inject
	@Named("ORG")
	IOrgMapService orgmapservice;
	
	@Inject
	IDeviceGroupService deviceGroupService;

	@Inject
	IFingerScanAttributesService fingerScanAttributesService;

	@Inject
        IIndividualProfileService deviceProfileService;
	
	@Inject
	IEmployeeGroupQueryService employeeGroupQueryService;

	@Inject
	ITTIPUserProfileService ittipUserProfileService;

	/**
	 * This method returns WorkerType from cache depending on the id passed.
	 * @param id Long instance 
	 * @return WorkerType instance
	 */
	public WorkerType getWorkerTypeFromId(Long id) {
		return WorkerTypeCache.getWorkerType(getObjectIdLong(id));
	}
	
	/**
	 * This method returns PayRule from cache depending on the id passed.
	 * @param id Long instance
	 * @return PayRule instance 
	 */
	public PayRule getPayRuleFromId(Long id) {
		if (id == null) {
			return null;
		}
		ArrayList<PayRule> payRules = PayRuleCache.getPayRuleByPayRuleId(getObjectIdLong(id));
		if(!CollectionUtils.isEmpty(payRules)){
			return payRules.get(0);
		} else {
			return null;
		}
	}
	
	/**
	 * Returns AccrualProfileInstance depending on the id passed.
	 * @param id Long instance
	 * @return AccrualProfile instance
	 */
	public AccrualProfile getAccrualProfileFromId(Long id){
		return AccrualProfileCache.getAccrualProfile(getObjectIdLong(id));
	}
	
	/**
	 * Returns TimePeriodType instance corresponding to the id passed.
	 * @param id Long instance
	 * @return TimePeriodType instance
	 */
	public TimePeriodType getTimePeriodTypeFromId(Long id){
		return TimePeriodTypeCache.getTimePeriodType(getObjectIdLong(id));
	}

	/**
	 * Returns RequestPurpose corresponding to the id passed.
	 * @param id Long instance
	 * @return RequestPurpose instance
	 */
	public RequestPurpose getRequestPurposeFromId(Long id){
		return RequestPurposeCache.getPurpose(getObjectIdLong(id));
	}
	
	/**
	 * Returns RequestReviewerList corresponding to the id passed.
	 * @param id Long instance
	 * @return RequestReviewerList instance
	 */
	public RequestReviewerList getRequestReviewerListFromId(Long id) {
		return RequestReviewerListCache.getRequestReviewerList(getObjectIdLong(id));
	}
	
	/**
	 * Returns WageProfile corresponding to the id passed.
	 * @param id Long instance
	 * @return WageProfile instance
	 */
	public WageProfile getWageProfileFromId(Long id) {
		return WageProfileCache.getWageProfile(getObjectIdLong(id));
	}
	
	/**
	 * Returns LogonProfile corresponding to the id passed.
	 * @param id Long instance
	 * @return LogonProfile instance
	 */
	public LogonProfile getLogonProfileFromId(Long id) {
		return LogonProfileCache.getLogonProfile(getObjectIdLong(id));
	}
		
	/**
	 * Returns EmploymentTerm corresponding to the id passed.
	 * @param id Long instance
	 * @return EmploymentTerm instance
	 */
	public EmploymentTerm getEmploymentTermFromId(Long id) {
		return EmploymentTerm.retrieveById(getObjectIdLong(id));
	}
	
	/**
	 * Returns List of EmploymentTerm corresponding to the id passed.
	 * @param ids List of Long instances
	 * @return EmploymentTerm instance
	 */
	public List<EmploymentTerm> getEmploymentTermFromIdList(List<ObjectIdLong> ids) {
		return EmploymentTerm.retrieveListByIds(ids);
	}
	
	/**
	 * Returns AuthenticationType corresponding to the id passed.
	 * @param id Long instance
	 * @return AuthenticationType instance
	 */
	public AuthenticationType getAuthenticationType(Long id){
		return AuthenticationTypeCache.getAuthenticationType((ObjectId)getObjectIdLong(id));
	}
	
	/**
	 * Returns CustomDateType corresponding to the id passed.
	 * @param id Long instance
	 * @return CustomDateType instance
	 */
	public CustomDateType getCustomDateName(ObjectIdLong id){
		return CustomDateTypeCache.getCustomDateType(id);
	}

	/**
	 * Returns WorkflowProfile for given profile id passed.
	 * @param id Long instance
	 * @return WorkflowProfile instance
	 */
	public WorkflowProfile getProcessProfile(Long id){
		return WorkflowProfileCache.getWorkflowProfile(getObjectIdLong(id));
	}
	
	/**
	 * Retrieve Legacy ObjectIdLong for given Long so as to call legacy calls.
	 * @param id Long Instance for which we need ObjectIdLong
	 * @return ObjectIdLong
	 */
	public ObjectIdLong getObjectIdLong(Long id) {
		return adapterHelper.getObjectIdLong(id);
	}
	
	/**
	 * Retrieve Primary Job for primary organization id and Date.
	 * @param id Primary Organization id
	 * @param date LocalDate Instance
	 * @return Primary Job for given id and date
	 */
	public String getPrimaryJob(Long id, LocalDate date) {
	    OrgObjectRef orgObjectRef = new OrgObjectRef(id);
		return adapterHelper.getIfNotNull(orgmapservice.resolve(orgObjectRef, date), objRef->objRef.getQualifier());
	}
	
	/**
         * Returns DeviceGroup name corresponding to the id passed.
         * @param id Long instance
         * @return String name of device group
         */
        public String getDeviceGroupNameFromId(final Long id) {
            return id == null
                   ? null
                   : deviceGroupService.getDeviceGroupNameById(id);
        }

	public List<DeviceGroup> getDeviceGroupsFromIds(final List<Long> ids) {
		return deviceGroupService.getDeviceGroupsByIds(ids);
	}

        /**
         * Returns Device Profile name corresponding to the id passed.
         * @param id Long instance
         * @return String name of device profile
         */
        public String getDeviceProfileNameFromId(final Long id) {
            return id == null
                   ? null
                   : deviceProfileService.getIndividualProfileNameById(id);
        }
	/**
	 * Returns TeleTimeIP User Profile name corresponding to the id passed.
	 * @param id Long instance
	 * @return String name of TeleTimeIP user profile
	 */
	public String getTTIPUserProfileNameFromId(final Long id) {
		return id == null
				? null
				: ittipUserProfileService.getTTIPUserProfileNameById(id);
	}

	/**
	 * Return employeeGroupName from id. Employee Group is cached
	 */
	public String getEmployeeGroupName(final Long id){
		Optional<EmployeeGroup> employeeGroupOption = employeeGroupQueryService.findById(id);
		if(employeeGroupOption.isPresent())
			return employeeGroupOption.get().getName();
		return null;
	}
	
	/**
         * Returns whether an employee has a primary finger scan enrolled that can be used in verify mode.
         * @param employeeId Long id of the employee
         * @return boolean whether an employee has a primary finger scan enrolled that can be used in verify mode
         * @deprecated Finger scan attributes are supported via PersonalityFacade
         */
        public boolean isFingerEnrolled(final Long employeeId) {
            return employeeId == null
                   ? false
                   : fingerScanAttributesService.isFingerEnrolled(employeeId);
        }
        
        /**
         * Returns whether an employee has a primary finger scan enrolled that can be used in identify mode.
         * @param employeeId Long id of the employee
         * @return boolean whether an employee has a primary finger scan enrolled that can be used in identify mode
         * @deprecated Finger scan attributes are supported via PersonalityFacade
         */
        public boolean isFingerEnrolledForIdentification(final Long employeeId) {
            return employeeId == null
                   ? false
                   : fingerScanAttributesService.isFingerEnrolledForIdentification(employeeId);
        }
        
        /**
         * Returns the highest threshold value of a primary finger scan enrolled by this employee.
         * @param employeeId Long id of the employee
         * @return String the highest threshold value enrolled
         * @deprecated Finger scan attributes are supported via PersonalityFacade
         */
        public String getPrimaryFingerThreshold(final Long employeeId) {
            return employeeId == null
                   ? null
                   : fingerScanAttributesService.getPrimaryFingerThreshold(employeeId);
        }
        
        /**
         * Returns the enrollment location of the primary finger scan enrolled by this employee.
         * @param employeeId Long id of the employee
         * @return String the enrollment location of the primary finger scan
         * @deprecated Finger scan attributes are supported via PersonalityFacade
         */
        public String getPrimaryFingerEnrollmentLocation(final Long employeeId) {
            return employeeId == null
                   ? null
                   : fingerScanAttributesService.getPrimaryFingerEnrollmentLocation(employeeId);
        }
}
