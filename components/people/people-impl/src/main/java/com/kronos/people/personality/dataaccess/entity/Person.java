/***********************************************************************
 * Person.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * The {@code Person} entity class for person table.
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "person")
public class Person {

	/**
	 * Contains the value of the column personid.
	 */
	@Id
	@Column(name = "personid")
	private Long personid;
	
	@Column(name = "updatedtm")
    private LocalDateTime updatedtm;
	
	private String personnum ;
	
	private String firstnm;
	
	private String  lastnm;
	
	private String middleinitialnm;
	
	private LocalDateTime birthdtm;
	
	
	/**
	 * @return the person id
	 */
	public Long getPersonid() {
		return personid;
	}

	/**
	 * @param personid
	 *            the {@link Long} person id to set
	 */
	public void setPersonid(Long personid) {
		this.personid = personid;
	}

	public String getPersonnum() {
		return personnum;
	}

	public void setPersonnum(String personnum) {
		this.personnum = personnum;
	}

	public String getFirstnm() {
		return firstnm;
	}

	public void setFirstnm(String firstnm) {
		this.firstnm = firstnm;
	}

	public String getLastnm() {
		return lastnm;
	}

	public void setLastnm(String lastnm) {
		this.lastnm = lastnm;
	}

	public String getMiddleinitialnm() {
		return middleinitialnm;
	}

	public void setMiddleinitialnm(String middleinitialnm) {
		this.middleinitialnm = middleinitialnm;
	}

	public LocalDateTime getBirthdtm() {
		return birthdtm;
	}

	public void setBirthdtm(LocalDateTime birthdtm) {
		this.birthdtm = birthdtm;
	}

	public LocalDateTime getUpdatedtm() {
		return updatedtm;
	}

	public void setUpdatedtm(LocalDateTime updatedtm) {
		this.updatedtm = updatedtm;
	}
}