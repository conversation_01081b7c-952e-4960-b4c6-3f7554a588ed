package com.kronos.people.personality.dataaccess.repository;

import java.time.LocalDateTime;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.kronos.people.personality.dataaccess.entity.Person;

/**
 * <AUTHOR>
 *
 */
@Repository
@Transactional
public interface IPersonReadRepository extends JpaRepository<Person, Long> {

	@Query(value = "select p.personid from Person p, PersonStatus ps where p.personid=ps.personid and (ps.employeeStatusId =:employmentStatusId and ps.userAccountStatusId=:userAccountStatusId) "
			+ "and (ps.effectivedtm <= :effectivedtm and ps.expirationdtm >= :expirationdtm) and p.personid > 0  order by p.personid")
	public Page<Long> findLightPersonIdsRecords(@Param("effectivedtm") LocalDateTime effectivedtm, @Param("expirationdtm") LocalDateTime expirationdtm, @Param("employmentStatusId") Long employmentStatusId,
			@Param("userAccountStatusId") Long userAccountStatusId, Pageable paging);

	@Query(value = "select p.personid from Person p, PersonStatus ps where p.personid=ps.personid and ps.userAccountStatusId=:userAccountStatusId "
			+ "and (ps.effectivedtm <= :effectivedtm and ps.expirationdtm >= :expirationdtm) and p.personid > 0 order by p.personid")
	public Page<Long> findLightPersonIdsRecordsByUserAccountStatus(@Param("effectivedtm") LocalDateTime effectivedtm, @Param("expirationdtm") LocalDateTime expirationdtm,
			@Param("userAccountStatusId") Long userAccountStatusId, Pageable paging);

	@Query(value = "select p.personid from Person p, PersonStatus ps where p.personid=ps.personid and ps.employeeStatusId =:employmentStatusId "
			+ "and (ps.effectivedtm <= :effectivedtm and ps.expirationdtm >= :expirationdtm) and p.personid > 0 order by p.personid")
	public Page<Long> findLightPersonIdsRecordsByEmploymentStatusId(@Param("effectivedtm") LocalDateTime effectivedtm, @Param("expirationdtm") LocalDateTime expirationdtm,
			@Param("employmentStatusId") Long employmentStatusId, Pageable paging);

	@Query(value = "select p.personid from Person p, PersonStatus ps where p.personid=ps.personid and (ps.effectivedtm <= :effectivedtm and ps.expirationdtm >= :expirationdtm) and p.personid > 0 order by p.personid")
	public Page<Long> findLightPersonIdsRecords(@Param("effectivedtm") LocalDateTime effectivedtm, @Param("expirationdtm") LocalDateTime expirationdtm, Pageable paging);

	@Query(value = "select p.personid,p.personNum,p.firstnm, p.lastnm, ps.employmentstatid,ps.useracctstatid from person p, personstatusmm ps where p.personid=ps.personid and (ps.employmentstatid =:employmentStatusId and ps.useracctstatid=:userAccountStatusId) " + 
			"and (ps.effectivedtm <= :effectivedtm and ps.expirationdtm >= :expirationdtm) and p.personid > 0 order by p.personid",
			countQuery="select count(*) from person p, personstatusmm ps where p.personid=ps.personid and (ps.employmentstatid =:employmentStatusId and ps.useracctstatid=:userAccountStatusId) " + 
		"and (ps.effectivedtm <= :effectivedtm and ps.expirationdtm >= :expirationdtm) and p.personid > 0",
			nativeQuery = true)
	public Page<Object[]> findLightPersonRecords(@Param("effectivedtm") LocalDateTime effectivedtm, @Param("expirationdtm") LocalDateTime expirationdtm, @Param("employmentStatusId")  Long employmentStatusId,
			@Param("userAccountStatusId") Long userAccountStatusId, Pageable paging);
	
	@Query(value = "select p.personid,p.personNum,p.firstnm, p.lastnm, ps.employmentstatid,ps.useracctstatid from person p, personstatusmm ps where p.personid=ps.personid and (ps.useracctstatid=:userAccountStatusId) " + 
			"and (ps.effectivedtm <= :effectivedtm and ps.expirationdtm >= :expirationdtm) and p.personid > 0 order by p.personid", 
			countQuery="select count(*) from person p, personstatusmm ps where p.personid=ps.personid and (ps.useracctstatid=:userAccountStatusId) " + 
					"and (ps.effectivedtm <= :effectivedtm and ps.expirationdtm >= :expirationdtm) and p.personid > 0",
			nativeQuery = true)
	public Page<Object[]> findLightPersonRecordsRecordsByUserAccountStatus(@Param("effectivedtm") LocalDateTime effectivedtm, @Param("expirationdtm") LocalDateTime expirationdtm,
			@Param("userAccountStatusId") Long userAccountStatusId, Pageable paging);
	
	@Query(value = "select p.personid,p.personNum,p.firstnm, p.lastnm, ps.employmentstatid,ps.useracctstatid from person p, personstatusmm ps where p.personid=ps.personid and (ps.employmentstatid =:employmentStatusId) "
			+ " and (ps.effectivedtm <= :effectivedtm and ps.expirationdtm >= :expirationdtm) and p.personid > 0 order by p.personid",
			countQuery="select count(*) from person p, personstatusmm ps where p.personid=ps.personid and (ps.employmentstatid =:employmentStatusId) "
					+ " and (ps.effectivedtm <= :effectivedtm and ps.expirationdtm >= :expirationdtm) and p.personid > 0",
			nativeQuery = true)
	public Page<Object[]> findLightPersonRecordsByEmploymentStatusId(@Param("effectivedtm") LocalDateTime effectivedtm, @Param("expirationdtm") LocalDateTime expirationdtm, @Param("employmentStatusId")  Long employmentStatusId,
			Pageable paging);
	
	@Query(value = "select p.personid,p.personNum,p.firstnm, p.lastnm, ps.employmentstatid,ps.useracctstatid  from person p, personstatusmm ps where p.personid=ps.personid  and (ps.effectivedtm <= :effectivedtm and ps.expirationdtm >= :expirationdtm) and p.personid > 0 order by p.personid",
			countQuery="select count(*) from person p, personstatusmm ps where p.personid=ps.personid  and (ps.effectivedtm <= :effectivedtm and ps.expirationdtm >= :expirationdtm) and p.personid > 0",
			nativeQuery = true)
	public Page<Object[]> findLightPersonRecords(@Param("effectivedtm") LocalDateTime effectivedtm, @Param("expirationdtm") LocalDateTime expirationdtm, Pageable paging);
	
}
