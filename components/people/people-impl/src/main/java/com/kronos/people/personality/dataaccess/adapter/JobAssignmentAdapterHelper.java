package com.kronos.people.personality.dataaccess.adapter;

import static com.kronos.people.personality.dataaccess.adapter.AdapterHelper.setIfNotNull;

import java.util.Collection;
import java.util.function.Function;

import jakarta.inject.Named;

import com.kronos.people.personality.model.extension.TimekeepingExtension;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.EmploymentTermEntry;
import com.kronos.people.personality.model.extension.entry.PayRuleEntry;
import com.kronos.people.personality.model.extension.entry.WageProfileEntry;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignment;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignmentDetails;
import com.kronos.wfc.commonapp.people.business.person.group.employment.EmploymentTermAssignment;
import com.kronos.wfc.commonapp.people.business.person.payruleassignment.PayRuleAssignment;
import com.kronos.wfc.commonapp.rules.business.PayRule;

@Named
public class JobAssignmentAdapterHelper extends BaseHelper {

	/**
	 * Utility function for employment term assignment converter.
	 */
	Function<EmploymentTermAssignment, EmploymentTermEntry> employmentTermAssignmentConvertorFunction = (EmploymentTermAssignment employeeTermAssignment) ->
			new EmploymentTermEntry(employeeTermAssignment.getEmploymentTermName(), adapterHelper.getLongFromObjectIdLong(employeeTermAssignment.getEmploymentTermId()),
					adapterHelper.kDateToLocalDate(employeeTermAssignment.getEffectiveDate()), adapterHelper.kDateToLocalDate(employeeTermAssignment
					.getExpirationDate()));


	/**
	 * Utility method for pay rule converter.
	 */
	@SuppressWarnings("static-access")
	Function<PayRuleAssignment, PayRuleEntry> payRuleConvertorFunction = (PayRuleAssignment payRuleAssignment) -> {
		PayRule payRule = personalityFacade.getPayRuleByAssign(payRuleAssignment);
		PayRuleEntry  payRuleEntry = adapterHelper.extractIfNotNull(payRule, pr->{
			PayRuleEntry entry = new PayRuleEntry();
			adapterHelper.setLongFromObjectIdLong(entry::setPayRuleId, pr.getPayRuleId());
			adapterHelper.setLongFromObjectIdLong(entry::setPayRuleAssignmentId, payRuleAssignment.getPayRuleAssignmentId());
			entry.setVersionCount(payRuleAssignment.getVersionCnt());
			return entry;
		});

		adapterHelper.setDates(payRuleEntry, payRuleAssignment.getEffectiveDate(), payRuleAssignment.getExpirationDate());
		return payRuleEntry;
	};


	/**
	 * This method sets the job assignment properties.
	 *
	 * @param extension
	 *            - {@link TimekeepingExtension}.
	 * @param jobAssignment
	 *            - {@link JobAssignment}, A job assignment is a collection of
	 *            information associated with person and job.
	 */
	@SuppressWarnings("unchecked")
	protected void setJobAssignmentProperties(TimekeepingExtension extension, JobAssignment jobAssignment) {
		setIfNotNull(jobAssignment.getJobAssignmentDetails(), extension, this::setJobAssignmentDetailsProperties);
		setIfNotNull(jobAssignment.getEmploymentTermAssignments(), extension, (t,u)-> {
			Collection<EmploymentTermEntry> effectiveDatedEntries = adapterHelper.convertLegacyCollection(u.getAllMembers(), employmentTermAssignmentConvertorFunction);

			extension.setEmployeeTerms(new EffectiveDatedCollection<EmploymentTermEntry>(effectiveDatedEntries));
		});
	}

	/**
	 * This method sets the job assignment details properties.
	 *
	 * @param extension
	 *            -{@link TimekeepingExtension}.
	 * @param jobAssignmentDetails
	 *            - {@link JobAssignmentDetails}, A Job Assignment Detail
	 *            contains the basic information needed for a person's job in
	 *            the system.
	 */
	@SuppressWarnings("unchecked")
	protected void setJobAssignmentDetailsProperties(TimekeepingExtension extension, JobAssignmentDetails jobAssignmentDetails) {
		setIfNotNull(jobAssignmentDetails.getWageProfile(), extension, (t, wp) -> t.setWageProfile(new WageProfileEntry(wp.getId().toLong())));

		setIfNotNull(jobAssignmentDetails.getWorkerTypeId(), extension, (t, u) -> t.setWorkerTypeId(u.toLong()));
		setIfNotNull(jobAssignmentDetails.getPayRuleAssignmentSet(), extension, (t,u)->
				t.setPayRules(new EffectiveDatedCollection<PayRuleEntry>(adapterHelper.convertLegacyCollection(u.getAllPayRuleAssignments(), payRuleConvertorFunction))));
		setIfNotNull(jobAssignmentDetails.getDirectPayRuleAssignmentSet(), extension,
				(t, u) -> t.setDirectPayRules(new EffectiveDatedCollection<PayRuleEntry>(adapterHelper
						.convertLegacyCollection(u.getAllPayRuleAssignments(), payRuleConvertorFunction))));
	}

}