/***********************************************************************
 * ExtensionBuilder.java
 * 
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.legacy;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;

import jakarta.annotation.PreDestroy;
import jakarta.inject.Named;

import com.kronos.people.personality.properties.TracePropertyHelper;
import com.kronos.wfc.platform.tenant.api.TenantDiscriminatorProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;

import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.logging.slf4jadapter.util.LogService;
import com.kronos.people.personality.dataaccess.adapter.AccrualExtensionAdapter;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.dataaccess.adapter.ConcurrencyHelper;
import com.kronos.people.personality.dataaccess.adapter.DevicesExtensionAdapter;
import com.kronos.people.personality.dataaccess.adapter.EmployeeExtensionAdapter;
import com.kronos.people.personality.dataaccess.adapter.ExtensionAdapter;
import com.kronos.people.personality.dataaccess.adapter.ExtensionAdapterEnum;
import com.kronos.people.personality.dataaccess.adapter.SchedulingExtensionAdapter;
import com.kronos.people.personality.dataaccess.adapter.TimeKeepingAdapter;
import com.kronos.people.personality.facade.PersonalityCacheFacade;
import com.kronos.people.personality.model.extension.AccrualExtension;
import com.kronos.people.personality.model.extension.AllExtension;
import com.kronos.people.personality.model.extension.BaseExtension;
import com.kronos.people.personality.model.extension.DevicesExtension;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.IExtension;
import com.kronos.people.personality.model.extension.SchedulingExtension;
import com.kronos.people.personality.model.extension.TimekeepingExtension;
import com.kronos.people.personality.notification.ExtensionCacheOperations;
import com.kronos.people.personality.notification.ExtensionCacheUpdaterImpl;
import com.kronos.people.personality.properties.KronosPropertiesFacade;
import com.kronos.people.personality.util.LogTimeHelper;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.logging.framework.Log;

import io.opentelemetry.instrumentation.annotations.SpanAttribute;
import io.opentelemetry.instrumentation.annotations.WithSpan;

import static com.kronos.people.personality.properties.TracePropertyHelper.EXTENSION_TYPE_ATTRIBUTE_KEY;

/**
 * The {@code ExtensionBuilder} class creates extensions.
 * 
 * <AUTHOR>
 *
 */
@Named
public class ExtensionBuilder {
	private static final Logger LOGGER = LoggerFactory.getLogger(ExtensionBuilder.class);
	private static final String THREAD_POOL_NAME = "personality.ExtensionBuilder";
	private ExecutorService executor ;
	private PersonalityCacheFacade cache;
	public ExtensionCacheUpdaterImpl cacheUpdater;
	public ExtensionCacheOperations cacheOperations;
	public KronosPropertiesFacade kronosPropertiesFacade;
	private PersonalityFacade personalityFacade;
	private EmployeeExtensionAdapter employeeExtensionAdapter;
	private SchedulingExtensionAdapter schedulingExtensionAdapter;
	private TimeKeepingAdapter timeKeepingExtensionAdapter;
	private AccrualExtensionAdapter accrualExtensionAdapter;
	private DevicesExtensionAdapter devicesExtensionAdapter;
	private AdapterHelper adapterHelper;
	@SuppressWarnings("rawtypes")
	private Map<ExtensionAdapterEnum, ExtensionAdapter> adapterMap = new HashMap<>();
	public ConcurrencyHelper concurrencyHelper;
	private final KronosThreadPoolService kronosThreadPoolService;
	private TracePropertyHelper tracePropertyHelper;

	@Lazy
	public ExtensionBuilder(
			EmployeeExtensionAdapter employeeExtensionAdapter, PersonalityCacheFacade cache,
			ExtensionCacheUpdaterImpl cacheUpdater, ExtensionCacheOperations cacheOperations,
			KronosPropertiesFacade kronosPropertiesFacade, PersonalityFacade personalityFacade,
			SchedulingExtensionAdapter schedulingExtensionAdapter, TimeKeepingAdapter timeKeepingExtensionAdapter,
			AccrualExtensionAdapter accrualExtensionAdapter, DevicesExtensionAdapter devicesExtensionAdapter, AdapterHelper adapterHelper,
			KronosThreadPoolService kronosThreadPoolService, ConcurrencyHelper concurrencyHelper, TracePropertyHelper tracePropertyHelper) {
		this.employeeExtensionAdapter = employeeExtensionAdapter;
		this.cache = cache;
		this.cacheUpdater = cacheUpdater;
		this.cacheOperations = cacheOperations;
		this.kronosPropertiesFacade = kronosPropertiesFacade;
		this.personalityFacade = personalityFacade;
		this.schedulingExtensionAdapter = schedulingExtensionAdapter;
		this.timeKeepingExtensionAdapter = timeKeepingExtensionAdapter;
		this.accrualExtensionAdapter = accrualExtensionAdapter;
		this.devicesExtensionAdapter = devicesExtensionAdapter;
		this.adapterHelper = adapterHelper;
		this.kronosThreadPoolService = kronosThreadPoolService;
		this.concurrencyHelper = concurrencyHelper;
		executor = kronosThreadPoolService.newThreadPool(THREAD_POOL_NAME);
		adapterMap.put(ExtensionAdapterEnum.ACCRUAL, accrualExtensionAdapter);
		adapterMap.put(ExtensionAdapterEnum.DEVICES, devicesExtensionAdapter);
		adapterMap.put(ExtensionAdapterEnum.EMPLOYEE, employeeExtensionAdapter);
		adapterMap.put(ExtensionAdapterEnum.SCHEDULING, schedulingExtensionAdapter);
		adapterMap.put(ExtensionAdapterEnum.TIMEKEEPING, timeKeepingExtensionAdapter);
		this.tracePropertyHelper = tracePropertyHelper;
	}

	/**
	 * This method will clear the map and shutdown the executor of
	 * {@link ExecutorService} . It will get invoke before destruction of this
	 * {@link ExtensionBuilder} instance.
	 */
	@PreDestroy
	public void cleanUp() {
		adapterMap.clear();
		concurrencyHelper.shutDownExecutor(executor);		
	}

	/**
	 * This method will provide extensions by personId and extnToReturn.
	 * 
	 * @param <T>
	 *            T is type of {@link BaseExtension}
	 * @param personId
	 *            {@code long}
	 * @param extnToReturn
	 *            {@link ExtensionAdapterEnum}
	 * @return T, where T is type of {@link BaseExtension}, The populated
	 *         extension of type {@link IExtension} from personality.
	 *
	 */

	public <T extends BaseExtension> T createExtensions(Long personId, ExtensionAdapterEnum extnToReturn, LocalDate snapShotDate) {

		Personality personality = personalityFacade.findPersonality(personId);
		BaseExtension extension = createExtensions(extnToReturn, personality, snapShotDate);
		cacheUpdater.insert(personality);
		return (T) extension;
	}


	/**
	 * @param extnToReturn
	 *            the ExtensionAdapterEnum
	 * @param personality
	 *            the personality object
	 * @return BaseExtension the BaseExtension
	 */
	@WithSpan
	public BaseExtension createExtensions(
			@SpanAttribute (EXTENSION_TYPE_ATTRIBUTE_KEY)
			ExtensionAdapterEnum extnToReturn, Personality personality, LocalDate snapShotDate) {
		tracePropertyHelper.setServiceNameTenantAndCluster(personality);
		BaseExtension extension;
		try {
			extension = adapterMap.get(extnToReturn).convert(personality, snapShotDate);
			updateCommonAttributes(personality, extension);
		} catch (Exception e) {
			LogTimeHelper.logError(Log.WARNING, e, "Error occurred while creating Extension");
			throw e;
		}
		return extension;
	}

	/**
	 * @param personality
	 *            personality object
	 * @param extension
	 *            This is BaseExtension
	 */
	public void updateCommonAttributes(Personality personality, BaseExtension extension) {
		if (AdapterHelper.isNullObjectNotPresent(extension, personality)) {
			extension.setPersonId(adapterHelper.getLongFromObjectIdLong(personality.getPersonId()));
			extension.setPersonNumber(personality.getPersonNumber());
			devicesExtensionAdapter.setBadgeDetails(personality, extension);
			extension.setActive(personality.isPersonActive());
		}
	}

	/**
	 * This is used for JUnit test cases
	 * 
	 * @param personalityFacade
	 *            the personalityFacade to set
	 */
	public void setPersonalityFacade(PersonalityFacade personalityFacade) {
		this.personalityFacade = personalityFacade;
	}

	/**
	 * @param accrualExtensionAdapter
	 *            the accrualExtensionAdapter to set
	 */
	public void setAccrualExtensionAdapter(AccrualExtensionAdapter accrualExtensionAdapter) {
		this.accrualExtensionAdapter = accrualExtensionAdapter;
	}

	/**
	 * @param adapterHelper
	 *            the adapterHelper to set
	 */
	public void setAdapterHelper(AdapterHelper adapterHelper) {
		this.adapterHelper = adapterHelper;
	}

	/**
	 * @param employeeExtensionAdapter
	 *            the employeeExtensionAdapter to set
	 */
	public void setEmployeeExtensionAdapter(EmployeeExtensionAdapter employeeExtensionAdapter) {
		this.employeeExtensionAdapter = employeeExtensionAdapter;
	}

	/**
	 * @param schedulingExtensionAdapter
	 *            the schedulingExtensionAdapter to set
	 */
	public void setSchedulingExtensionAdapter(SchedulingExtensionAdapter schedulingExtensionAdapter) {
		this.schedulingExtensionAdapter = schedulingExtensionAdapter;
	}

	/**
	 * @param timeKeepingExtensionAdapter
	 *            the timeKeepingExtensionAdapter to set
	 */
	public void setTimeKeepingExtensionAdapter(TimeKeepingAdapter timeKeepingExtensionAdapter) {
		this.timeKeepingExtensionAdapter = timeKeepingExtensionAdapter;
	}

	/**
	 * @param devicesExtensionAdapter
	 *            the devicesExtensionAdapter to set
	 */
	public void setDevicesExtensionAdapter(DevicesExtensionAdapter devicesExtensionAdapter) {
		this.devicesExtensionAdapter = devicesExtensionAdapter;
	}

	/**
	 * @param adapterMap
	 *            the adapterMap to set This is used for Junit test case
	 */
	public void setAdapterMap(Map<ExtensionAdapterEnum, ExtensionAdapter> adapterMap) {
		this.adapterMap = adapterMap;
	}

	/**
	 * @param personality
	 *            the personality object
	 * @return Map of BaseExtension
	 */
	public Map<String, BaseExtension> buildExtensions(Personality personality) {
		String trxId = new LogService().getTransactionId();
		LOGGER.debug("personalityService --> ExtensionBuilder buildExtensions");
		Map<String, BaseExtension> extensionsMap = new ConcurrentHashMap<>();
		Map<String, RuntimeException> exceptions = new ConcurrentHashMap<>();
		Queue<ExtensionAdapterEnum> queue = new ConcurrentLinkedQueue<>(adapterMap.keySet());
		concurrencyHelper.parallelProcessQueueItems(queue, adapter -> {
			try {
				return extensionsMap.put(adapter.getIdentifier(),
						createExtensions(adapter, personality, null)) != null;
			} catch (RuntimeException re) {
				exceptions.put(adapter.getIdentifier(), re);
				return false;
			}
		}, getExtensionsParallizationCount(), adapterMap.size());

		if (!exceptions.isEmpty()) {
			throw exceptions.entrySet().iterator().next().getValue();
		}
		return extensionsMap;
	}

	private int getExtensionsParallizationCount() {
		return kronosPropertiesFacade.getIntegerKronosProperty(PersonalityConstants.EXTENSION_BUILD_THREAD_COUNT.getValue(), 5);
	}

	/**
	 * @param personId
	 *            the person id
	 * @return Map of BaseExtension
	 */
	public Map<String, BaseExtension> buildExtensions(Long personId) {
		return buildExtensions(personalityFacade.findPersonality(personId));
	}

	public AllExtension buildAllExtensionAndPushToRedis(Long personId) {
		LOGGER.info("PersonId is missing from cache : {}", personId);
		Map<String, BaseExtension> map = buildExtensions(personId);
		AllExtension allExtension = new AllExtension();
		allExtension.setAccrualExtension((AccrualExtension) map.get(ExtensionAdapterEnum.ACCRUAL.getIdentifier()));
		allExtension.setEmployeeExtension((EmployeeExtension) map.get(ExtensionAdapterEnum.EMPLOYEE.getIdentifier()));
		allExtension.setDeviceExtension((DevicesExtension) map.get(ExtensionAdapterEnum.DEVICES.getIdentifier()));
		allExtension.setSchedulingExtension((SchedulingExtension) map.get(ExtensionAdapterEnum.SCHEDULING.getIdentifier()));
		allExtension.setTimekeepingExtension((TimekeepingExtension) map.get(ExtensionAdapterEnum.TIMEKEEPING.getIdentifier()));

		cacheOperations.putInRedisChannel(map);

		allExtension.setPersonId(allExtension.getAccrualExtension().getPersonId());
		allExtension.setActive(allExtension.getAccrualExtension().isActive());
		allExtension.setPersonNumber(allExtension.getAccrualExtension().getPersonNumber());
		return allExtension;
	}
	
	public void setConcurrencyHelper(ConcurrencyHelper concurrencyHelper) {
		this.concurrencyHelper = concurrencyHelper;
	}
	
	/**
	 * Used for getting the current logged in user person Id
	 * 
	 * @return Long person Id
	 */
	public Long getCurrentUserId(){
		return personalityFacade.getCurrentUserId();
	}

	@SuppressWarnings("unchecked")
	public <T extends BaseExtension> T createExtensionSnapshot(T extension, ExtensionAdapterEnum extnToReturn) {
		try{
			return (T) adapterMap.get(extnToReturn).createSnapshot(extension);
		} catch(Exception e){
			LogTimeHelper.logError(Log.WARNING, e, "Error occurred while creating Extension");
			throw e;
		}
	}

	public void setTracePropertyHelper(TracePropertyHelper tracePropertyHelper) {
		this.tracePropertyHelper = tracePropertyHelper;
	}
}
