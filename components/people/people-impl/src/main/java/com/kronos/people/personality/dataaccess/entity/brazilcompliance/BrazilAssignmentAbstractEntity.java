package com.kronos.people.personality.dataaccess.entity.brazilcompliance;

import com.kronos.people.personality.dataaccess.converter.LocalDateTimeConverter;
import com.kronos.people.personality.dataaccess.entity.AbstractEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Access;
import jakarta.persistence.AccessType;

import java.time.LocalDateTime;

@MappedSuperclass
@SuppressWarnings("squid:S2160") //Suppress "Override equals/hashcode" sonar warning for Entity classes
public abstract class BrazilAssignmentAbstractEntity extends AbstractEntity<Long> {

    @Column(name = "person_id")
    protected Long personId;

    @Column(name = "effective_date")
    @Convert(converter = LocalDateTimeConverter.class)
    protected LocalDateTime effectiveDate;

    @Override
    @Column(name = "id")
    @Id
    @Access(AccessType.PROPERTY)
    public Long getId() {
        return id;
    }

    public Long getPersonId() {
        return personId;
    }

    public void setPersonId(Long personId) {
        this.personId = personId;
    }

    public LocalDateTime getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDateTime effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

}