/***********************************************************************
 * Person.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * The {@code Person} entity class for person table.
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "Prsncmmnidentassign")
public class Prsncmmnidentassign {

	/**
	 * Contains the value of the column personid.
	 */
	@Id
	@Column(name = "personid")
	private Long personid;

	
	

	public Long getPersonid() {
		return personid;
	}


	public void setPersonid(Long personid) {
		this.personid = personid;
	}


	
	

}