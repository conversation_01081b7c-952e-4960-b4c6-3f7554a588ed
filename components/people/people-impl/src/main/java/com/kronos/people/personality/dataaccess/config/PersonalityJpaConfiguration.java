/***********************************************************************
 * PersonalityJpaConfiguration.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * This class is the JPA configuration class.
 * 
 * <AUTHOR>
 *
 */
@Configuration
@EnableJpaRepositories("com.kronos.people.personality.dataaccess.repository")
public class PersonalityJpaConfiguration {

}
