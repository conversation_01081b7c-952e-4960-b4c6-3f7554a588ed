/***********************************************************************
 * PersonalityBatchProcessor.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.notification.batch;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.apache.commons.collections.CollectionUtils;

import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.notification.entry.CacheEntry;
import com.kronos.people.personality.tenant.TenantHandlingFacade;

/**
 * This Class is used for the notification flow
 * when we are registering the person ids then we are adding that 
 * into the map and calling clear cache thereafter.
 * <AUTHOR>
 *
 */
@Named
public class PersonalityBatchProcessor {


	/**
	 * Map containing tenantId as key and Set of PersonIds as value for the tenantId.
	 */
	Map<String, Collection<Long>> personalityBatchMap = new ConcurrentHashMap<>();

	/**
	 * TenantHandlingFacade to get the tenant id as key while adding personIds to map.
	 */
	@Inject
	TenantHandlingFacade tenantFacade;
	
	/**
	 * Adapter helper instance for performing operations on map.
	 */
	@Inject
	AdapterHelper adapterHelper;

	/**
	 * This method adds the list of personIds corresponding to the current tenantId.
	 * @param personIds List of Long containing personIds
	 */
	public void addToBatchMap(List<Long> personIds) {
		if (!CollectionUtils.isEmpty(personIds)) {
			adapterHelper.putListInSetInsideMap(personalityBatchMap, tenantFacade.getTenantId(), personIds);
		}
	}
	
	/**
	 * 
	 * @param personId
	 * @return
	 */
	public boolean isPersonInBatch(Long personId) {
		String tenantId = tenantFacade.getTenantId();
		if(personalityBatchMap.get(tenantId) == null || personalityBatchMap.get(tenantId).isEmpty()) {
			return false;
		}
		return personalityBatchMap.get(tenantId).contains(personId);
	}
	
	/**
	 * This method removes the list of personIds corresponding to the current tenantId.
	 * @param personIds List of Long containing personIds
	 */
	public void removeFromBatchMap(List<Long> personIds) {
		Set<Long> personIdsInMap = (Set<Long>) personalityBatchMap.get(tenantFacade.getTenantId());
		if (!CollectionUtils.isEmpty(personIdsInMap) && !CollectionUtils.isEmpty(personIds)) {
			personIdsInMap.removeAll(personIds);
		}
	}
	
	/**
	 * This method checks if the given cacheEntry's personId exists in the map,
	 * if present, then it removes it from the map and returns true, else
	 * returns false.
	 * 
	 * @param ce
	 *            CacheEntry containing the incoming cache
	 * @return boolean instance true if the entry is present and is removed from
	 *         map successfully, else returns false
	 */
	public boolean isPersonPresentForGivenTenantIdAndRemoveFromMap(CacheEntry ce) {
		if(ce == null){
			return false;
		}
		String tenantId = ce.getTenantId();
		Long objectId = ce.getKey().getObjectId();
		Set<Long> personIds = (Set<Long>) personalityBatchMap.get(tenantId);
		if(!CollectionUtils.isEmpty(personIds) && personIds.contains(objectId)){
			personIds.remove(objectId);
			return true;
		}
		return false;
	}
	
}
