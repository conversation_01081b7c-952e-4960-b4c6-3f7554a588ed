package com.kronos.people.personality.dataaccess.entity.brazilcompliance;

import com.kronos.people.personality.dataaccess.entity.AbstractEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Access;
import jakarta.persistence.AccessType;


@Entity
@Table(name = "brc_employee")
@SuppressWarnings("squid:S2160") //Suppress "Override equals/hashcode" sonar warning for Entity classes
public class BrazilEmployeeEntity extends AbstractEntity<Long> {

    @Column(name = "person_id")
    private Long personId;

    @Column(name = "pis")
    private String pis;

    @Column(name = "e_social")
    private String eSocial;

    @Column(name = "cpf")
    private String cpf;


    @Override
    @Column(name = "id")
    @Id
    @Access(AccessType.PROPERTY)
    public Long getId() {
        return id;
    }

    public void setPersonId(Long personId) {
        this.personId = personId;
    }

    public void setPis(String pis) {
        this.pis = pis;
    }

    public String getPis() {
        return pis;
    }
    public void seteSocial(String eSocial) {
        this.eSocial = eSocial;
    }
    public Long getPersonId() {
        return personId;
    }
    public String geteSocial() {
        return eSocial;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getCpf() {
        return cpf;
    }


}