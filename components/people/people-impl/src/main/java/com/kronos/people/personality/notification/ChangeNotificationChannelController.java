/***********************************************************************
 * ChangeNotificationChannelController.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.notification;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.people.personality.properties.KronosPropertiesFacade;
import org.springframework.context.annotation.Lazy;

import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.people.personality.notification.entry.PersonalityEvent;

/**
 * This class register consumer and add PersonalityEvent to channel.
 * 
 * <AUTHOR>
 */
@Named
@Lazy
public class ChangeNotificationChannelController {
	
	/**
	 * Instance of {@link ExecutorService}. It provides the threads which will
	 * convert the {@link PersonalityEvent} to extension.
	 */
	private ExecutorService executor;

	/**
	 * The instance of {@link Channel}
	 */
	private Channel<PersonalityEvent> channel;

	@Inject
	private KronosThreadPoolService kronosThreadPoolService;

	@Inject
	private KronosPropertiesFacade kronosPropertiesFacade;

	private static final String THREAD_POOL_NAME = "personality.ChangeNotificationChannelController";

	@PostConstruct
	public void init(){
		executor = kronosThreadPoolService.newThreadPool(THREAD_POOL_NAME );
		channel = new Channel<>(executor, "PersonalityEventChannel");
	}

	/**
	 * This method adds the {@link PersonalityEvent} to the channel.
	 * 
	 * @param personalityEvent
	 *            The {@link PersonalityEvent} instance to set
	 */
	public void notify(PersonalityEvent personalityEvent) {
		if(kronosPropertiesFacade.getBooleanKronosProperty("people.apply.performance.fixes",true)){
			channel.addPersonalityEventInQueue(personalityEvent);
		}else{
			channel.add(personalityEvent);
		}
	}

	/**
	 * This method register the consumer to the channel.
	 * 
	 * @param consumer
	 *            The {@link Consumer} instance to set
	 * @param name
	 *            The name of the consumer to set
	 */
	public void registerListener(Consumer<PersonalityEvent> consumer, String name) {
		channel.register(consumer, name);
	}

	/**
	 * @param pes
	 *            The list of PersonalityEvent
	 */
	public void notify(List<PersonalityEvent> pes) {
		if(kronosPropertiesFacade.getBooleanKronosProperty("people.apply.performance.fixes",true)){
			channel.addPersonalityItems(pes);
		}else{
			channel.add(pes);
		}

	}

	/**
	 * This method sets the channel.
	 * 
	 * @param channelMap
	 *            The channelMap to set
	 * 
	 */
	public void setChannel(Channel<PersonalityEvent> channelMap) {
		this.channel = channelMap;
	}

	/**
	 * This method gets the Channel.
	 * 
	 * @return channel of PersonalityEvent
	 */
	public Channel<PersonalityEvent> getChannel() {
		return channel;
	}
	
	/**
	 * This method will shutdown the executor of
	 * {@link ExecutorService} . It will get invoke before destruction of this instance.
	 */
	@PreDestroy
	public void cleanUp() {
		kronosThreadPoolService.shutDownExecutor(executor);
	}

	/**
	 * Unregister consumer.
	 * @param consumerName Consumer name
	 */
	public void unregister(String consumerName) {
		channel.unregister(consumerName);
	}


}
