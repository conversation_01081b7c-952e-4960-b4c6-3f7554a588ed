/***********************************************************************
 * PersonalityChangeNotificationManager.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.notification;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.people.personality.dataaccess.adapter.ConcurrencyHelper;
import com.kronos.people.personality.model.extension.AllExtension;
import com.kronos.people.personality.notification.entry.NotificationEntry;
import com.kronos.people.personality.tenant.TenantHandlingFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

/**
 * This class handle personality change notifications by invoking appropriate
 * method of @link {@link PersonalityChangeListener}
 * 
 * <AUTHOR>
 *
 */
@Named
public class PersonalityChangeNotificationManager {

	private static final String PERSONALITY_CHANGE_NOTIFICATION_CHANNEL = "PersonalityChangeNotificationChannel";
	private static final String PERSONALITY_CHANGE_NOTIFICATION_CONSUMER = "PersonalityChangeNotificationConsumer";
	private static final String THREAD_POOL_NAME = "personality.PersonalityChangeNotificationManager";

	/**
	 * The instance of {@link TenantHandlingFacade}.
	 */
	@Inject
	TenantHandlingFacade tenantHandler;
	
	/**
	 * The instance of {@link ConcurrencyHelper}.
	 */
	@Inject
	private KronosThreadPoolService kronosThreadPoolService;

	
	/**
	 * Instance of {@link ExecutorService}. It provides the threads which will
	 * notify changes in People to it consumers.
	 */
	private ExecutorService executor;
	
	@Autowired(required = false)
	@Lazy
	List<PersonalityChangeListener> personalityChangeListeners;
	
	/**
	 * The channel to for notifying people change consumers
	 */
	private Channel<NotificationEntry> notificationToPeopleConsumersChannel;
	
	/**
	 * The instance of {@link Consumer} of {@link NotificationEntry}. This
	 * method is notifying insert, update and delete person events to consumers
	 */
	Consumer<NotificationEntry> consumer = entry -> tenantHandler
			.performOperationsUsingTenantId(() -> performNotify(entry), entry.getTenantId());
			

	/**
	 * This will register the consumer with channel
	 */
	@PostConstruct
	public void init() {
		executor = kronosThreadPoolService.newThreadPool(THREAD_POOL_NAME );
		notificationToPeopleConsumersChannel = new Channel<>(executor, PERSONALITY_CHANGE_NOTIFICATION_CHANNEL);
		notificationToPeopleConsumersChannel.register(consumer, PERSONALITY_CHANGE_NOTIFICATION_CONSUMER);
	}

	protected void performNotify(NotificationEntry entry) {
		notifyUpdate(entry.getUpdatedPersonExtensions());
		notifyInsert(entry.getInsertedPersonExtensions());
		notifyDelete(entry.getDeleteEntries());
	}

	/**
	 * This method will unregister the consumer and shutdown the executor of
	 * {@link ExecutorService} . It will get invoke before destruction of this.
	 */
	@PreDestroy
	public void cleanUp() {
		notificationToPeopleConsumersChannel.unregister(PERSONALITY_CHANGE_NOTIFICATION_CONSUMER);
		kronosThreadPoolService.shutDownExecutor(executor);
	}

	/**
	 *  Notify insert events
	 * 
	 * @param person2ExtensionsMap
	 */
	public void notifyInsert(Map<Long, AllExtension> person2ExtensionsMap){
		if (!person2ExtensionsMap.isEmpty()){
			if (personalityChangeListeners != null){
				personalityChangeListeners.forEach(personalityChangeListener-> personalityChangeListener.onInsert(person2ExtensionsMap));
			}
		}
	}
	
	/**
	 *  Notify update events
	 * 
	 * @param person2ExtensionsMap
	 */
	public void notifyUpdate(Map<Long, AllExtension> person2ExtensionsMap){
		if (!person2ExtensionsMap.isEmpty()){
			if (personalityChangeListeners != null){
				personalityChangeListeners.forEach(personalityChangeListener-> personalityChangeListener.onUpdate(person2ExtensionsMap));
			}
		}
	}
	
	/**
	 * Notify delete events
	 * 
	 * @param personIds
	 */
	public void notifyDelete(Set<Long> personIds){
		if (!personIds.isEmpty()){
			if (personalityChangeListeners != null){
				personalityChangeListeners.forEach(personalityChangeListener-> personalityChangeListener.onDelete(personIds));
			}
		}
	}
	
	/**
	 * Add personality change to notificationToPeopleConsumersChannel
	 * 
	 * @param notificationEntry
	 */
	public void addNotificationEntry(NotificationEntry notificationEntry) {
		notificationToPeopleConsumersChannel.add(notificationEntry);
	}
	
}	
