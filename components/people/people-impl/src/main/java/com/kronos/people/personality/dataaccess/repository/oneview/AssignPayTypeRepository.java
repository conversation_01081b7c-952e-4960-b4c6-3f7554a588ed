package com.kronos.people.personality.dataaccess.repository.oneview;

import com.kronos.people.personality.dataaccess.entity.oneview.AssignPayTypeEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface AssignPayTypeRepository extends JpaRepository<AssignPayTypeEntity, Long> {
    void deleteByPersonId(Long personId);
    List getByPersonId(Long personId);
    List<AssignPayTypeEntity> findByPersonIdIn(List<Long> personIds);
    List<AssignPayTypeEntity> findByPersonId(Long personId);
}