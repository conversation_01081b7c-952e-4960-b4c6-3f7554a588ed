package com.kronos.people.personality.dataaccess.legacy;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import jakarta.inject.Inject;

import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.model.extension.entry.PersonCustomDataEntry;
import com.kronos.people.personality.model.extension.entry.PersonDatesEntry;
import com.kronos.wfc.commonapp.currency.business.CurrencyPolicy;
import com.kronos.wfc.commonapp.currency.business.assignment.EmployeeCurrencyAssignment;
import com.kronos.wfc.commonapp.currency.business.assignment.EmployeeCurrencyAssignmentService;
import com.kronos.wfc.commonapp.currency.business.assignment.UserCurrencyAssignment;
import com.kronos.wfc.commonapp.currency.business.assignment.UserCurrencyAssignmentService;
import com.kronos.wfc.commonapp.namedentity.business.dap.DapAssignment;
import com.kronos.wfc.commonapp.namedentity.business.dap.DapAssignmentGuest;
import com.kronos.wfc.commonapp.people.business.person.AccessAssignment;
import com.kronos.wfc.commonapp.people.business.person.CustomDate;
import com.kronos.wfc.commonapp.people.business.person.EMailAddress;
import com.kronos.wfc.commonapp.people.business.person.PersonLicenseType;
import com.kronos.wfc.commonapp.people.business.person.PostalAddress;
import com.kronos.wfc.commonapp.people.business.person.TelephoneNumber;
import com.kronos.wfc.commonapp.people.business.person.group.PersonGroup;
import com.kronos.wfc.commonapp.people.business.person.group.PersonGroupService;
import com.kronos.wfc.commonapp.people.business.person.payruleassignment.PayRuleAssignment;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.user.CurrentUserAccountManager;
import com.kronos.wfc.commonapp.people.business.user.PersonAuthenticationType;
import com.kronos.wfc.commonapp.processmanager.business.workflow.WorkflowAccessAssignment;
import com.kronos.wfc.commonapp.rules.business.PayRule;
import com.kronos.wfc.commonapp.rules.business.PayRuleCache;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.business.authentication.types.AuthenticationType;
import com.kronos.wfc.platform.utility.framework.datetime.KDateTime;

/**
 * This class will handle the access to the Legacy Facade object.
 * <AUTHOR>
 *
 */
public abstract class BaseLegacyFacade {

	@Inject
	PersonDatesHelper personDatesHelper;

	public BaseLegacyFacade() {
	}

	/**
	 * Get schedule group.
	 * @param objectIdLong
	 * 			The long objectIdLong
	 * @return PersonGroup object
	 */
	public PersonGroup getScheduleGroup(ObjectIdLong objectIdLong) {
		return PersonGroupService.getScheduleGroupById(objectIdLong);
	}

	/**
	 * Get Dap Assignment
	 * @param personality
	 * 			The Personality personality
	 * @return DapAssignment object
	 */
	public DapAssignment getDapAssignment(Personality personality) {
		DapAssignmentGuest dapGuest = (DapAssignmentGuest) personality.getGuestManager().getGuestOfType(DapAssignmentGuest.class);
		if (dapGuest == null) {
			dapGuest = new DapAssignmentGuest(personality);
		}
		return dapGuest.getDapAssignment();
	}


	/**
	 * Get Employee Currency assignment for person id.
	 *
	 * @param personId
	 *            PersonId
	 * @return EmployeeCurrencyAssignment object
	 */
	public EmployeeCurrencyAssignment getEmployeeCurrencyAssignment(ObjectIdLong personId) {
		EmployeeCurrencyAssignment empCurrencyAssign = EmployeeCurrencyAssignmentService.selectByPersonId(personId);
		return createEmployeeCurrencyAssignment(personId, empCurrencyAssign);
	}

	/**
	 *  Create Employee Currency assignment for person id.
	 * @param personId - Person Id
	 * @param empCurrencyAssign - Emp Current Assignment
	 * @return EmployeeCurrencyAssignment object
	 */
	protected EmployeeCurrencyAssignment createEmployeeCurrencyAssignment(ObjectIdLong personId, EmployeeCurrencyAssignment empCurrencyAssign) {
		return AdapterHelper.extractIfNulOrNotNull(empCurrencyAssign, t->t, ()->new EmployeeCurrencyAssignment(personId,CurrencyPolicy.UNSPECIFIED_ID , null));
	}

	/**
	 * Get user currency assignment
	 *
	 * @param personId
	 *            personId object
	 * @return UserCurrencyAssignment object
	 */
	public UserCurrencyAssignment getUserCurrencyAssignment(ObjectIdLong personId) {
		UserCurrencyAssignment userCurrencyAssign = UserCurrencyAssignmentService.selectByPersonId(personId);
		return createUserCurrencyAssignment(personId, userCurrencyAssign);
	}

	/**
	 *  Create user currency assignment
	 * @param personId - person id
	 * @param userCurrencyAssign - user current assignment
	 * @return UserCurrencyAssignment object
	 */
	protected UserCurrencyAssignment createUserCurrencyAssignment(
			ObjectIdLong personId, UserCurrencyAssignment userCurrencyAssign) {
		return AdapterHelper.extractIfNulOrNotNull(userCurrencyAssign, t->t, ()->new UserCurrencyAssignment(personId, CurrencyPolicy.UNSPECIFIED_ID, null));
	}

	/**
	 * This method finds the pay rule by pay rule assignment.
	 *
	 * @param payRuleAssignment
	 *            {@link PayRuleAssignment}, A pay rule assignment is assigned
	 *            to an employee with an effective date.
	 * @return the pay rule assignment.
	 */
	public PayRule getPayRuleByAssign(PayRuleAssignment payRuleAssignment) {
		return PayRuleCache.getPayRule(payRuleAssignment.getPayRuleName(), new KDateTime(payRuleAssignment.getEffectiveDate()));
	}


	/**
	 * @param accessAssignment
	 *            the access assignment
	 * @return the manager profile name
	 */
	public Map<String, Object> getProcessManagerProfileName(AccessAssignment accessAssignment) {
		Map<String, Object> map = new HashMap<>();
		WorkflowAccessAssignment waa = getWorkflowAssignment(accessAssignment);
		map.put(PersonalityConstants.EMPLOYEE_WORKFLOW_PROFILE_ID.getValue(), waa.getEmployeeWorkflowProfileId());
		map.put(PersonalityConstants.MANAGER_WORKFLOW_PROFILE_ID.getValue(), waa.getManagerWorkflowProfileId());
		return map;
	}

	public WorkflowAccessAssignment getWorkflowAssignment(AccessAssignment accessAssignment) {
		WorkflowAccessAssignment waa = new WorkflowAccessAssignment(accessAssignment);
		waa.refresh();
		return waa;
	}

	/**
	 * This method gets the legacy person dates by {@code personality}.
	 *
	 * @param personality
	 *            {@link Personality}, the personality is a combination of the
	 *            person demographics, user, and job assignment information.
	 * @param converterHelper {@link AdapterHelper}, the helper class
	 * @return collection of {@link CustomDate}.
	 */
	public List<PersonDatesEntry> getLegacyPersonDates(Personality personality, AdapterHelper converterHelper) {
		return personDatesHelper.getPersonDates(personality,converterHelper);
	}

	public List<PersonCustomDataEntry> getLegacyPersonCustomData(Personality personality) {
		return personDatesHelper.getPersonCustomData(personality);
	}

	/**
	 * This method get the legacy telephone number by {@code personality}.
	 *
	 * @param personality
	 *            - {@link Personality}, the personality is a combination of the
	 *            person demographics, user, and job assignment information.
	 * @return - collection of telephone number {@link TelephoneNumber}.
	 */
	@SuppressWarnings("unchecked")
	public Collection<TelephoneNumber> getLegacyTelNumbers(Personality personality) {
		return personality.getTelephoneNumbers().collection();
	}

	/**
	 * This method returns the collection of person license type.
	 *
	 * @param personality {@link Personality} instance
	 * @return Collection of {@link PersonLicenseType} class
	 */
	@SuppressWarnings("unchecked")
	public Collection<PersonLicenseType> getLegacyLicenceTypes(Personality personality) {
		return AdapterHelper.extractIfNotNull(personality.getLicenseTypes(), licenseTypeSet -> licenseTypeSet.collection());
	}

	/**
	 * This method returns the collection of active person license type.
	 *
	 * @param personality {@link Personality} instance
	 * @return Collection of {@link PersonLicenseType} class
	 */
	@SuppressWarnings("unchecked")
	public Collection<PersonLicenseType> getActiveLegacyLicenceTypes(Personality personality) {
		return AdapterHelper.extractIfNotNull(personality.getActiveLicenseTypes(), licenseTypeSet -> licenseTypeSet.collection());
	}

	/**
	 * This method gets the legacy email address by {@code personality}.
	 *
	 * @param personality
	 *            - {@link Personality}, the personality is a combination of the
	 *            person demographics, user, and job assignment information.
	 * @return - collection of {@link EMailAddress}.
	 */
	@SuppressWarnings("unchecked")
	public Collection<EMailAddress> getLegacyEmailAdress(Personality personality) {
		return personality.getEmailAddresses().collection();
	}

	/**
	 * This method gets the legacy postal address by {@code personality}.
	 *
	 * @param personality
	 *            - {@link Personality}, the personality is a combination of the
	 *            person demographics, user, and job assignment information.
	 * @return - collection of {@link PostalAddress}.
	 */
	@SuppressWarnings("unchecked")
	public Collection<PostalAddress> getLegacyPostalAddresses(Personality personality) {
		return personality.getPostalAddresses().collection();
	}

	/**
	 * This method convert authentication type by {@code personAuthTypeSet}.
	 *
	 * @param personAuthTypeSet
	 *            collection of {@link PersonAuthenticationType}, the person
	 *            authentication type.A user is assigned an authentication type
	 *            to define how his/her credentials will be authenticated at
	 *            logon.
	 * @return {@link AuthenticationType}, the authentication type.
	 */
	public AuthenticationType convertAuthenticationType(Collection<PersonAuthenticationType> personAuthTypeSet) {
		Optional<PersonAuthenticationType> personAuthTypeOpt = personAuthTypeSet.stream().filter(
				perAuhType ->perAuhType.getActive()).findFirst();
		PersonAuthenticationType personAuthType = null;
		if (personAuthTypeOpt.isPresent()) {
			personAuthType = personAuthTypeOpt.get();
			return personAuthType.getAuthenticationType();
		}

		return null;
	}

	/**
	 * Used for getting the current logged in user person Id
	 *
	 * @return Long person Id
	 */
	public Long getCurrentUserId(){
		return CurrentUserAccountManager.getPersonality().getId();
	}

}