/***********************************************************************
 * AccrualExtensionAdapter.java
 * 
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.adapter;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Optional;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.model.extension.AccrualExtension;
import com.kronos.people.personality.model.extension.entry.AccrualProfilesEntry;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.FullTimeEquivalencyEntry;
import com.kronos.wfc.commonapp.people.business.person.accrualprofileassignment.AccrualProfileAssignment;
import com.kronos.wfc.commonapp.people.business.person.accrualprofileassignment.DirectAccrualProfileAssignmentSet;
import com.kronos.wfc.commonapp.people.business.person.fulltimeequivalency.FullTimeEquivalency;
import com.kronos.wfc.commonapp.people.business.person.fulltimeequivalency.FullTimeEquivalencySet;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;

/**
 * This is the adapter class for Accrual Extension. It provides the methods for
 * populating the accrual extension from personality.
 * 
 * <AUTHOR>
 *
 */

@Named
public class AccrualExtensionAdapter implements ExtensionAdapter<AccrualExtension> {

	/**
	 * Instance of {@link AdapterHelper}. It provides the utility methods.
	 */
	@Inject
	private AdapterHelper adapterHelper;

	@Inject
	private PersonalityFacade personalityFacade;

	/**
	 * {@inheritDoc}
	 */
	@SuppressWarnings("static-access")
	@Override
	public AccrualExtension convert(Personality personality, LocalDate snapShotDate) {
		AccrualExtension accuralExtension = new AccrualExtension();
		adapterHelper.setIfObjectIdLongNotNull(personality.getJobAssignmentId(), accuralExtension, this::convertEffDatedAccrualProfiles);
		adapterHelper.setIfNotNull(personality.getFullTimeEquivalencySet(), accuralExtension, this::convertFullTimeEquivalency);

		return accuralExtension;
	}

	@Override
	public AccrualExtension createSnapshot(AccrualExtension extension) {
		return Optional.ofNullable(extension).map(AccrualExtension::new).orElse(null);
	}


	/**
	 * This method convert {@code FullTimeEquivalencySet} to
	 * {@code EffectiveDatedCollection} by fullTimeEquivalencySet.
	 * 
	 * @param accrualExtension
	 *            The instance of {@link AccrualExtension}
	 * 
	 * @param fullTimeEquivalencySet
	 *            {@link FullTimeEquivalencySet}, a set of effective-dated Full
	 *            Time Equivalencies.
	 * 
	 */

	protected void convertFullTimeEquivalency(AccrualExtension accrualExtension, FullTimeEquivalencySet fullTimeEquivalencySet) {
		accrualExtension.setFullTimeEquivalency(new EffectiveDatedCollection<FullTimeEquivalencyEntry>(getDatedFullTimeEquivalency(fullTimeEquivalencySet)));
	}

	/**
	 * This method gets the {@code FullTimeEquivalencyEntry} by
	 * fullTimeEquivalencySet {@code FullTimeEquivalencySet} .
	 * 
	 * @param fullTimeEquivalencySet
	 *            {@link FullTimeEquivalencySet}, a set of effective-dated Full
	 *            Time Equivalencies.
	 * @return collection of {@link FullTimeEquivalencyEntry}.
	 */
	@SuppressWarnings("unchecked")
	protected Collection<FullTimeEquivalencyEntry> getDatedFullTimeEquivalency(FullTimeEquivalencySet fullTimeEquivalencySet) {
		return adapterHelper.convertLegacyCollection((Collection<FullTimeEquivalency>) fullTimeEquivalencySet.getAllMembers(),
				fullTimeEquivalency -> getDatedFullTimeEquivalencyEntry(fullTimeEquivalency));
	}

	/**
	 * This method gets the dated {@code FullTimeEquivalencyEntry} by
	 * fullTimeEquivalency {@code FullTimeEquivalency}.
	 * 
	 * @param fullTimeEquivalency
	 *            fulltime Equivalency
	 * @return {@link FullTimeEquivalencyEntry}
	 */
	protected FullTimeEquivalencyEntry getDatedFullTimeEquivalencyEntry(FullTimeEquivalency fullTimeEquivalency) {
		FullTimeEquivalencyEntry fullTimeEquivalencyEntry = new FullTimeEquivalencyEntry();
		adapterHelper.setDates(fullTimeEquivalencyEntry, fullTimeEquivalency.getEffectiveDate(), fullTimeEquivalency.getExpirationDate());
		fullTimeEquivalencyEntry.setFullTimeEquivalencyId(adapterHelper.getLongFromObjectIdLong(fullTimeEquivalency.getFullTimeEquivalencyId()));
		fullTimeEquivalencyEntry.setFullTimeEquivalencyPercent(fullTimeEquivalency.getFullTimeEquivalencyPercent());
		fullTimeEquivalencyEntry.setFullTimeStandardHoursQuantity(fullTimeEquivalency.getFullTimeStandardHoursQuantity());
		fullTimeEquivalencyEntry.setEmployeeStandardHoursQuantity(fullTimeEquivalency.getEmployeeStandardHoursQuantity());
		fullTimeEquivalencyEntry.setVersionCount(fullTimeEquivalency.getVersionCnt());
		return fullTimeEquivalencyEntry;
	}

	/**
	 * This method convert the effective dated accrual profiles by
	 * jobAssignmentId {@code ObjectIdLong}
	 * 
	 * @param accrualExtension
	 *            The instance of {@link AccrualExtension}
	 * @param jobAssignmentId
	 *            {@code ObjectIdLong}
	 * 
	 */
	protected void convertEffDatedAccrualProfiles(AccrualExtension accrualExtension, ObjectIdLong jobAssignmentId) {
		DirectAccrualProfileAssignmentSet aSet = personalityFacade.getDirectAccrualProfileAssignment(jobAssignmentId);
		accrualExtension.setAccuralProfiles(new EffectiveDatedCollection<AccrualProfilesEntry>(getDatedAccrualProfileEntries(aSet)));
	}

	/**
	 * This method gets the dated accrual profile entries by aSet
	 * {@code DirectAccrualProfileAssignmentSet}.
	 * 
	 * @param aSet
	 *            {@link DirectAccrualProfileAssignmentSet} - the accrual
	 *            profile assignment.
	 * @return collection of {@link AccrualProfilesEntry}.
	 */
	protected Collection<AccrualProfilesEntry> getDatedAccrualProfileEntries(DirectAccrualProfileAssignmentSet aSet) {
		return adapterHelper.convertLegacyCollection(getDatedLegacyAccuralProfiles(aSet),
				legacyAccuralProfile -> getDatedAccuralProfileEntry(legacyAccuralProfile));
	}

	/**
	 * This method gets the legacy accrual profiles by aSet
	 * {@code DirectAccrualProfileAssignmentSet}.
	 * 
	 * @param aSet
	 *            {@link DirectAccrualProfileAssignmentSet} - the accrual
	 *            profile assignment.
	 * @return collection of {@link AccrualProfileAssignment}.
	 */
	@SuppressWarnings("unchecked")
	protected Collection<AccrualProfileAssignment> getDatedLegacyAccuralProfiles(DirectAccrualProfileAssignmentSet aSet) {
		return aSet.getAllAccrualProfileAssignments();
	}

	/**
	 * This method gets the dated accrual profile entry by
	 * accuralProfileAssignment {@code AccrualProfileAssignment}.
	 * 
	 * @param accuralProfileAssignment
	 *            {@link AccrualProfileAssignment} - the accrual profile
	 *            assignment.
	 * @return instance of {@link AccrualProfilesEntry}
	 */

	protected AccrualProfilesEntry getDatedAccuralProfileEntry(AccrualProfileAssignment accuralProfileAssignment) {
		AccrualProfilesEntry accrualProfilesEntry = new AccrualProfilesEntry();
		accrualProfilesEntry.setAccrualProfileId(adapterHelper.getLongFromObjectId(accuralProfileAssignment.getAccrualProfileId()));
		adapterHelper.setDates(accrualProfilesEntry, accuralProfileAssignment.getEffectiveDate(), accuralProfileAssignment.getExpirationDate());
		accrualProfilesEntry.setAccuralProfileAssignmentId(adapterHelper.getLongFromObjectIdLong(accuralProfileAssignment.getAccrualProfileAssignmentId()));

		return accrualProfilesEntry;
	}

	/**
	 * @param adapterHelper
	 *            the adapterHelper to set
	 */
	public void setAdapterHelper(AdapterHelper adapterHelper) {
		this.adapterHelper = adapterHelper;
	}

	/**
	 * @param personalityFacade
	 *            the personalityFacade to set
	 */
	public void setPersonalityFacade(PersonalityFacade personalityFacade) {
		this.personalityFacade = personalityFacade;
	}

}
