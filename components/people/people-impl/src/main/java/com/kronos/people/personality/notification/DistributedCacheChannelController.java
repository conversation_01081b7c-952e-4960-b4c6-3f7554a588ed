/***********************************************************************
 * DistributedCacheChannelController.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.notification;

import static com.kronos.people.personality.notification.entry.EventType.DELETE;
import static com.kronos.people.personality.notification.entry.EventType.INSERT;
import static com.kronos.people.personality.notification.entry.EventType.UPDATE;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.springframework.context.annotation.Lazy;

import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.people.personality.dataaccess.legacy.PersonalityConstants;
import com.kronos.people.personality.notification.entry.CacheEntry;
import com.kronos.people.personality.notification.entry.EventType;
import com.kronos.people.personality.properties.KronosPropertiesFacade;

/**
 * This is the channel controller class for the distributed cache.
 * 
 * <AUTHOR>
 *
 */
@Named
@Lazy
public class DistributedCacheChannelController {

	private static final String THREAD_POOL_NAME = "personality.DistributedCacheChannelController";
	/**
	 * The default cache put size
	 */
	private static final int DEFAULT_CACHE_PUT_BATCH_SIZE = 100;

	/**
	 * The instance of {@link KronosPropertiesFacade}
	 */
	@Inject
	KronosPropertiesFacade kronosPropertiesFacade;
	
	@Inject
	private KronosThreadPoolService kronosThreadPoolService;

	/**
	 * Instance of {@link ExecutorService}. It provides the threads which will
	 * push changes to Redis.
	 */
	private ExecutorService executor;

	/**
	 * Value contains the batch size.
	 */
	private int batchSize = DEFAULT_CACHE_PUT_BATCH_SIZE;

	/**
	 * Value contains the {@link Map} containing the {@link Channel} of
	 * {@link CacheEntry} and {@link EventType}
	 */
	private Map<EventType, Channel<CacheEntry>> channelMap = new HashMap<EventType, Channel<CacheEntry>>();

	/**
	 * Default Constructor This default constructor is used for JUnt test case
	 * to spy this class.
	 */
	public DistributedCacheChannelController() {

	}

	/**
	 * This method constructs the channel map.
	 */
	@PostConstruct
	public void init() {
		executor = kronosThreadPoolService.newThreadPool(THREAD_POOL_NAME);
		batchSize = kronosPropertiesFacade.getIntegerKronosProperty(PersonalityConstants.CACHE_PUT_SIZE.getValue(), DEFAULT_CACHE_PUT_BATCH_SIZE);
		channelMap.put(UPDATE, new Channel<CacheEntry>(executor, batchSize, "updateCacheEntryChannel"));
		channelMap.put(INSERT, new Channel<CacheEntry>(executor, batchSize, "insertCacheEntryChannel"));
		channelMap.put(DELETE, new Channel<CacheEntry>(executor, batchSize, "deleteCacheEntryChannel"));
	}

	/**
	 * This method will shutdown the executor of
	 * {@link ExecutorService} . It will get invoke before destruction of this.
	 */
	@PreDestroy
	public void cleanUp() {
		kronosThreadPoolService.shutDownExecutor(executor);
	}
	
	/**
	 * This method add the {@link CacheEntry} to the channel.
	 * 
	 * @param eventType
	 *            The instance of {@link EventType} to set
	 * @param entry
	 *            The instance of {@link CacheEntry} to set
	 */
	public void notify(EventType eventType, CacheEntry entry) {
		channelMap.get(eventType).add(entry);
	}

	/**
	 * This method register the batch of consumer.
	 * 
	 * @param eventType
	 *            The instance of {@link EventType} to set
	 * @param consumer
	 *            The instance of {@link Consumer} containing list of
	 *            {@link CacheEntry} to set
	 * @param consumerName
	 *            The {@link String} consumer name to set
	 */
	public void registerListener(EventType eventType, Consumer<List<CacheEntry>> consumer, String consumerName) {
		channelMap.get(eventType).registerBatchConsumer(consumer, consumerName);
	}

	/**
	 * This method unregisters consumer.
	 * @param eventType Event type
	 * @param consumerName Consumer name
	 */
	public void unregister(EventType eventType, String consumerName) {
		channelMap.get(eventType).unregister(consumerName);
	}
	/**
	 * This method sets the channel map.
	 * 
	 * @param channelMap
	 *            The {@link Map} of {@link EventType} and {@link Channel} to
	 *            set
	 */
	public void setChannelMap(Map<EventType, Channel<CacheEntry>> channelMap) {
		this.channelMap = channelMap;
	}

	/**
	 * This method sets the instance of {@link KronosPropertiesFacade}.
	 * 
	 * @param kronosPropertiesFacade
	 *            the kronosPropertiesFacade to set
	 */
	public void setKronosPropertiesFacade(KronosPropertiesFacade kronosPropertiesFacade) {
		this.kronosPropertiesFacade = kronosPropertiesFacade;
	}

}
