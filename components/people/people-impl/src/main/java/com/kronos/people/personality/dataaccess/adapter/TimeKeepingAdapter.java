/***********************************************************************
 * TiemKeepingAdapter.java
 * 
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.adapter;

import com.kronos.people.personality.dataaccess.legacy.IWorkEmployeeServiceFacade;
import com.kronos.people.personality.dataaccess.legacy.PersonalityConstants;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.model.extension.TimekeepingExtension;
import com.kronos.people.personality.model.extension.entry.*;
import com.kronos.wfc.commonapp.currency.business.assignment.CurrencyAssignment;
import com.kronos.wfc.commonapp.people.business.person.*;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.currency.KCurrency;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import static com.kronos.people.personality.dataaccess.adapter.AdapterHelper.setIfNotNull;

/**
 * The {@code TiemKeepingAdapter} populates the time keeping extension from
 * legacy personality.
 * 
 * <AUTHOR>
 *
 */
@Named
public class TimeKeepingAdapter implements ExtensionAdapter<TimekeepingExtension> {

	/**
	 * Instance of {@link PersonalityFacade}. It provides methods for finding
	 * personality and personality triplet.
	 */
	@Inject
	private PersonalityFacade personalityFacade;

	/**
	 * Instance of {@link AdapterHelper}. It provides the utility methods.
	 */
	@Inject
	private AdapterHelper adapterHelper;

	@Inject
	AccessAssignmentAdapterHelper accessAssignmentAdapterHelper;

	@Inject
	JobAssignmentAdapterHelper jobAssignmentAdapterHelper;

	@Inject
	IWorkEmployeeServiceFacade workEmployeeServiceFacade;

	// Functions
	/**
	 * Utility function for base wage converter.
	 */
	Function<BaseWageRate, BaseWageEntry> baseWageRateConvertorFunction = (BaseWageRate rate) -> new BaseWageEntry(adapterHelper.getLongFromObjectIdLong(rate.getBaseWageId()), getDoubleFromKCurrency(rate.getHourlyRate()),
			adapterHelper.kDateToLocalDate(rate.getEffectiveDate()), adapterHelper.kDateToLocalDate(rate.getExpirationDate()), rate.getVersionCnt());


	/**
	 * Utility function for expected hours converter.
	 */
	Function<ExpectedHours, ExpectedHoursEntry> expectedHoursConvertorFunction = (ExpectedHours expectedHour) -> new ExpectedHoursEntry(adapterHelper.getLongFromObjectIdLong(expectedHour.getTimePeriodTypeId()),
			expectedHour.getExpectedHoursQuantity());

	/**
	 * Utility function for recent entry converter.
	 */
	Function<com.kronos.wfc.commonapp.people.business.user.RecentEntry,RecentEntry>  recentEntryConvertorFunction = (com.kronos.wfc.commonapp.people.business.user.RecentEntry recentEntry) -> new RecentEntry(recentEntry.getOrder(),recentEntry.getEnteredText());

	/**
	 * Utility function for review entry converter.
	 */
	Function<PurposeAssignment, ReviewerEntry> reviewEntryConvertorFunction = (PurposeAssignment purposeAssignment) -> new ReviewerEntry(
			adapterHelper.getLongFromObjectIdLong(purposeAssignment.getRequestPurposeId()), adapterHelper.getLongFromObjectIdLong(purposeAssignment.getRequestReviewerListId()));

	/**
	 * Utility method for currency details.
	 */
	Function<CurrencyAssignment, CurrencyDetailsEntry> currencyFunction = currency -> {
		CurrencyDetailsEntry cde = new CurrencyDetailsEntry();
		cde.setCurrencyCode(currency.getCurrencyCode());
		cde.setCurrencyId(adapterHelper.getLongFromObjectId(currency.getCurrencyId()));
		return cde;
	};

	/**
	 * Utility method for attestation profile converter.
	 */
	@SuppressWarnings("static-access")
	Function<AssignAttestationProfile, AttestationProfileEntry> attestationProfileConvertorFunction = (AssignAttestationProfile attestationProfileAssignment) -> {
		AttestationProfileEntry  entry = new AttestationProfileEntry();
		adapterHelper.setLongFromObjectIdLong(entry::setAttestationProfileId, attestationProfileAssignment.getAttestationProfileId());
		adapterHelper.setLongFromObjectIdLong(entry::setAttestationProfileAssignmentId, attestationProfileAssignment.getAssignAttestationProfileId());
		entry.setVersionCount(attestationProfileAssignment.getVersionCount().longValue());
		adapterHelper.setDates(entry, attestationProfileAssignment.getEffectiveDate(), attestationProfileAssignment.getExpirationDate());
		entry.setAssignedToManagerRole(adapterHelper.isNotNull(attestationProfileAssignment.getIsManagerRole()) && attestationProfileAssignment.getIsManagerRole()
				.get().booleanValue());
		return entry;
	};

	public void setAdapterHelper(AdapterHelper adapterHelper) {
		this.adapterHelper = adapterHelper;
	}

	/**
	 * Setter method for {@code  personalityFacade}.
	 *
	 * @param personalityFacade Personality facade used for fetching legacy personality objects.
	 */
	public void setPersonalityFacade(PersonalityFacade personalityFacade) {
		this.personalityFacade = personalityFacade;
	}

	// Cachable

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TimekeepingExtension convert(Personality personality, LocalDate snapShotDate) {
		return adapterHelper.extractIfLegacyNullableNotNull(personality.getPersonId(), personId->populate(personality));
	}

	@Override
	public TimekeepingExtension createSnapshot(TimekeepingExtension extension) {
        return Optional.ofNullable(extension).map(TimekeepingExtension::new).orElse(null);
	}

	/**
	 * Return Timekeeping Extension for personality
	 * @param personality
	 * @return TimekeepingExtension
	 */
	protected TimekeepingExtension populate(Personality personality) {
		ObjectIdLong personId = personality.getPersonId();
		TimekeepingExtension extension = new TimekeepingExtension();
		if (personId != null) {
			extension.setPersonId(personId.longValue());
		}
		setIfNotNull(personalityFacade.getJobAssignment(personId), extension, jobAssignmentAdapterHelper::setJobAssignmentProperties);
		setIfNotNull(personalityFacade.getAccessAssignment(personId), extension,accessAssignmentAdapterHelper::setTimeKeepingAccessProperties);
		setIfNotNull(personalityFacade.getPerson(personId),extension, this::setPersonPropeties);
		setManagerLicense(personality, extension);
		setCurrencyProperties(extension, personId);
		setPersonalityProperties(extension, personality);
		setRecentEntries(extension, personId);
		setWorkEmployee(extension, personId);
		setAttestationProfileAssignmentProperties(extension, personality);
		return extension;
	}

	private void setManagerLicense(Personality personality, TimekeepingExtension extension) {
		Collection<PersonLicenseType> licenseTypes = personalityFacade.getActiveLegacyLicenceTypes(personality);
		boolean isManager = licenseTypes.stream().anyMatch(lt -> PersonalityConstants.WORKFORCE_MANAGER.getValue().equalsIgnoreCase(lt.getLicenseType().getName()) && lt.getActive());
		extension.setIsManager(isManager);
	}

	/**
	 * Setter method for recent entries. It sets the recent enries.
	 *
	 * @param extension
	 *            - {@link TimekeepingExtension}.
	 * @param personId
	 *            - the requested person id.
	 */
	protected void setRecentEntries(TimekeepingExtension extension, ObjectIdLong personId) {
		Collection<com.kronos.wfc.commonapp.people.business.user.RecentEntry> recentEntries = personalityFacade.getRecentEntries(personId);
		extension.setRecentEntries(adapterHelper.convertLegacyCollection(recentEntries, recentEntryConvertorFunction));
	}

	protected void setWorkEmployee(TimekeepingExtension extension, ObjectIdLong personId) {
		WorkEmployeeEntry workEmployeeEntry = workEmployeeServiceFacade.getEmployeeByPersonId(personId.toLong());
		extension.setWorkEmployee(workEmployeeEntry);
	}

	/**
	 * Setter method for personality properties. It sets the expected hours,
	 * base wage entries and reviewers.
	 *
	 * @param extension
	 *            - {@link TimekeepingExtension}.
	 * @param personality
	 *            - the requested personality.
	 */
	@SuppressWarnings({ "unchecked", "static-access" })
	protected void setPersonalityProperties(TimekeepingExtension extension, Personality personality) {
		// ExpectedHours
		Collection<ExpectedHours> expectedHours = adapterHelper.extractIfNotNull(personality.getExpectedHours(), t->t.collection());
		extension.setExpectedHours(adapterHelper.convertLegacyCollection(expectedHours, expectedHoursConvertorFunction));

		// Base wage entries
		Collection<BaseWageRate> baseWageRates = adapterHelper.extractIfNotNull(personality.getBaseWageRateSet(), t->t.getAllBaseWageRates());
		Collection<BaseWageEntry> effectiveDatedEntries = adapterHelper.convertLegacyCollection(baseWageRates,
				baseWageRateConvertorFunction);
		extension.setBaseWage(new EffectiveDatedCollection<BaseWageEntry>(effectiveDatedEntries));

		// reviewers
		Collection<PurposeAssignment> purposeAssignments = adapterHelper.extractIfNotNull(personality.getPurposeAssignments(), t->t.collection());
		extension.setReviewers(adapterHelper.convertLegacyCollection(purposeAssignments, reviewEntryConvertorFunction));

	}

	/**
	 * This method sets the attestation profile assignment properties.
	 *
	 * @param extension
	 *            - {@link TimekeepingExtension}.
	 * @param Personality
	 *            - {@link Personality}, .
	 */
	@SuppressWarnings("unchecked")
	protected void setAttestationProfileAssignmentProperties(TimekeepingExtension extension, Personality personality) {
		List<AssignAttestationProfile> assignAttestationProfiles = new ArrayList<>();
		if (personality.getAssignAttestationProfileSet() != null) assignAttestationProfiles.addAll(
				personality.getAssignAttestationProfileSet().getAllAssignAttestationProfiles());
		if (personality.getAssignAttestationProfileForManagerRoleSet() != null) assignAttestationProfiles.addAll(
				personality.getAssignAttestationProfileForManagerRoleSet().getAllAssignAttestationProfiles());
		extension.setAttestationProfiles(new EffectiveDatedCollection<>(adapterHelper.convertLegacyCollection(assignAttestationProfiles, attestationProfileConvertorFunction)));
	}

	/**
	 * This method sets the currency properties.
	 *
	 * @param extension
	 *            - {@link TimekeepingExtension}.
	 * @param personId
	 *            - the requested person id
	 */
	@SuppressWarnings("static-access")
	protected void setCurrencyProperties(TimekeepingExtension extension, ObjectIdLong personId) {
		adapterHelper.setIfNotNull(personalityFacade.getEmployeeCurrencyAssignment(personId), extension, (ext,currency)->ext.setEmployeeCurrency(currencyFunction.apply(currency)));
		adapterHelper.setIfNotNull(personalityFacade.getUserCurrencyAssignment(personId), extension, (ext,currency)-> ext.setUserCurrency(currencyFunction.apply(currency)));
	}

	/**
	 * This method sets the person properties.
	 *
	 * @param extension
	 *            -{@link TimekeepingExtension}.
	 * @param nameData
	 *            -{@link Person}, the person access assignment data.
	 */
	protected void setPersonPropeties(TimekeepingExtension extension, Person nameData) {
		adapterHelper.setDate(extension::setPayrollLockoutThruDate, nameData.getPayrollLockoutThruDate());
		adapterHelper.setDate(extension::setManagerSignoffThruDate,nameData.getManagerSignoffThruDate());
		adapterHelper.setDate(extension::setSignoffPreparationDate,nameData.getSignoffPreparationDate());
	}


	/**
	 * Convert the KCurrency to the Double value.
	 * @param kCurrency - Kronos Currency
	 * @return Double  - double value
	 */
	public Double getDoubleFromKCurrency(KCurrency kCurrency){
		return adapterHelper.extractIfNotNull(kCurrency, currency-> Double.valueOf(Double.valueOf(kCurrency.doubleValue())));
	}

}
