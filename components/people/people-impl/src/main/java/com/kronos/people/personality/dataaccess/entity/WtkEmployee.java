package com.kronos.people.personality.dataaccess.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "wtkemployee")
public class WtkEmployee {

	/**
	 * Contains the value of the column personid.
	 */
	@Id
	@Column(name = "wtkemployeeid")
	private Long wtkEmployeeId;

	@Column(name = "supervisorid")
	private Long supervisorId;

	@Column(name = "personid")
	private Long personId;

	@Column(name = "employeeid")
	private Long employeeId;

	public Long getWtkEmployeeId() {
		return wtkEmployeeId;
	}

	public void setWtkEmployeeId(Long wtkEmployeeId) {
		this.wtkEmployeeId = wtkEmployeeId;
	}

	public Long getSupervisorId() {
		return supervisorId;
	}

	public void setSupervisorId(Long supervisorId) {
		this.supervisorId = supervisorId;
	}

	public Long getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(Long employeeId) {
		this.employeeId = employeeId;
	}

	public Long getPersonId() {
		return personId;
	}

	public void setPersonId(Long personId) {
		this.personId = personId;
	}
}