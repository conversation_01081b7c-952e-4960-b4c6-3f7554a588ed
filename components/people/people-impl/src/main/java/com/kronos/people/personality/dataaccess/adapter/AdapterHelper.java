/***********************************************************************
 * AdapterHelper.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.adapter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import jakarta.inject.Named;

import org.apache.commons.collections.CollectionUtils;

import com.kronos.people.personality.model.extension.entry.EffectiveDatedEntry;
import com.kronos.wfc.platform.persistence.framework.ObjectId;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.resources.shared.constants.KNullIfc;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.platform.utility.framework.datetime.KDateTime;
import com.kronos.wfc.platform.utility.framework.datetime.KTime;

/**
 * The {@code AdapterHelper} class handles the general utility conversion.
 *
 * <AUTHOR>
 */
@Named
public class AdapterHelper {


	/**
	 * Get String from Long.
	 * @param longValue Long value for which String is required
	 * @return String value
	 */
	public String getStringFromLong(Long longValue) {
		return getIfNotNull(longValue, l->l.toString());
	}


	/**
	 * Test if an object is null.
	 * @param object Object
	 * @return true if not null
	 */
	public boolean isNotNull(Object object) {
		return Optional.ofNullable(object).isPresent();
	}

	/**
	 * Get if not null. Returns null if the data is null.
	 * @param <T> type of data to check
	 * @param <R> return type
	 * @param dataForNullCheck Data for null check
	 * @param functionIfSuccess Converter function
	 * @return Return of function or null in case of null data
	 */
	public <T,R> R getIfNotNull(T dataForNullCheck, Function<T, R> functionIfSuccess) {
		return isNotNull(dataForNullCheck) ? functionIfSuccess.apply(dataForNullCheck) : null;
	}

	/**
	 * Convert the ObjectIdLong BO to the Long.
	 *
	 * @param objectIdLong
	 *            ObjectIdLong
	 * @return Long the object reference
	 */
	public Long getLongFromObjectIdLong(ObjectIdLong objectIdLong) {
		return extractIfLegacyNullableNotNull(objectIdLong, objectidlong->objectidlong.longValue());
	}

	/**
	 * Predicate to check null of legacy nullable value.
	 * @param legacyNullable value to test
	 * @return true if not null
	 */
	private boolean legacyNullPredicate(KNullIfc legacyNullable) {
		return Optional.ofNullable(legacyNullable).isPresent() && !legacyNullable.isNull();
	}

	/**
	 * Convert the ObjectId BO to the object reference.
	 *
	 * @param objectId
	 *            ObjectId
	 * @return Long the object reference
	 */
	public Long getLongFromObjectId(ObjectId objectId) {
		return legacyNullPredicate(objectId) ? Long.valueOf(Long.valueOf(objectId.toString()).longValue()) : null;
	}

	/**
	 * Process an conversion function on an legacy collection to produce an
	 * collection of converted model.
	 *
	 * @param <T>
	 *            the converted model
	 * @param <V>
	 *            the legacy data
	 * @param legacyData
	 *            Collection
	 * @param function
	 *            Function
	 * @return the collection of converted model
	 */
	public <T, V> Collection<T> convertLegacyCollection(Collection<V> legacyData, Function<V, T> function) {
		return isNotNull(legacyData) ? legacyData.stream().filter(this::isNotNull).map(function).filter(this::isNotNull).collect(Collectors.toList()) : null;
	}

	/**
	 * Filter legacy collection.
	 * @param <V>
	 *            the legacy data
	 * @param legacyData Legacy collection
	 * @param predicate Predicate to filter
	 * @return Filtered collection
	 */
	public <V> Collection<V> filterLegacyCollection(Collection<V> legacyData, Predicate<V> predicate) {
		return extractIfNotNull(legacyData, data -> {
			List<V> output = new ArrayList<>();
			for (V v: data) {
				if (predicate.test(v)) {
					output.add(v);
				}
			}
			return output;
		});
	}

	/**
	 * Convert the KDate BO to the LocalDate.
	 *
	 * @param date
	 *            KDate
	 * @return LocalDate the local date
	 */
	public LocalDate kDateToLocalDate(KDate date) {
		return legacyNullPredicate(date) ? LocalDate.of(date.getYear(), date.getMonth(), date.getDay()) : null;
	}

	/**
	 * Convert the LocalDate to the KDate
	 *
	 * @param date
	 *            LocalDate
	 * @return KDate the kronos date
	 */
	public KDate localDateToKdate(LocalDate date) {
		return (Optional.ofNullable(date).isPresent()) ? KDate.createDate(date.getYear(), date.getMonthValue(), date.getDayOfMonth()) : null;
	}

	/**
	 * Convert the KDateTime BO to the LocalDateTime.
	 *
	 * @param kDateTime
	 *            The kronos date and time
	 * @return LocalDateTime the local date time
	 */
	public LocalDateTime kDateTimeToLocalDateTime(KDateTime kDateTime) {
		return legacyNullPredicate(kDateTime) ? LocalDateTime.of(kDateToLocalDate(kDateTime.getDate()), kTimeToLocalTime(kDateTime.getTime()))
				: null;
	}

	/**
	 * Convert the LocalDateTime BO to the KDateTime.
	 *
	 * @param localDateTime
	 *            LocalDateTime
	 * @return KDateTime the Kronos date time
	 */
	public KDateTime localDateTimeToKdateTime(LocalDateTime localDateTime) {
		return (Optional.ofNullable(localDateTime).isPresent()) ? KDateTime.createDateTime(localDateToKdate(localDateTime.toLocalDate()),
				localTimeToKtime(localDateTime.toLocalTime())) : null;
	}

	/**
	 * Convert the KTime BO to the LocalTime.
	 *
	 * @param kTime
	 *            KTime
	 * @return LocalTime the local time
	 */
	@SuppressWarnings("deprecation")
	public LocalTime kTimeToLocalTime(KTime kTime) {
		return legacyNullPredicate(kTime) ? LocalTime.of(kTime.getHour(), kTime.getMinute(), kTime.getSecond(),
				(int) TimeUnit.MILLISECONDS.toNanos(kTime.getMillis())) : null;
	}

	/**
	 * Convert the LocalTime BO to the KTime.
	 *
	 * @param localTime
	 *            the local time
	 * @return KTime the Kronos time
	 */
	public KTime localTimeToKtime(LocalTime localTime) {
		return (Optional.ofNullable(localTime).isPresent()) ? KTime.create(localTime.getHour(), localTime.getMinute(), localTime.getSecond(),
				(int) TimeUnit.NANOSECONDS.toMillis(localTime.getNano())) : null;
	}



	/**
	 * This method convert date to local date.
	 *
	 * @param date
	 *            {@link Date}.
	 * @return local date {@link LocalDate}.
	 */
	public LocalDate dateToLocalDate(Date date) {
		return (Optional.ofNullable(date).isPresent() && date != null) ? date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate() : null;
	}

	/**
	 * This method check for the null values.
	 *
	 * @param items
	 *            {@link Object}, value to check for null.
	 * @return {@code true} if items not null else {@code false}.
	 */
	@SuppressWarnings("rawtypes")
	public static boolean isNullNotPresent(Supplier... items) {
		for (Supplier supplier : items) {
			if (!Optional.ofNullable(supplier.get()).isPresent())
				return false;
		}
		return true;
	}

	/**
	 * This method check for the null values.
	 * Return true if no parameter is null.
	 * Return false if any parameter is null.
	 *
	 * @param items
	 *            - {@link Object}, value to check for null.
	 * @return -{@link Boolean} - {@code true} if items not null else
	 *         {@code false}.
	 */
	public static boolean isNullObjectNotPresent(Object... items) {
		for (Object obj : items) {
			if (!Optional.ofNullable(obj).isPresent())
				return false;
		}
		return true;
	}

	/**
	 * This method check for the null values. It will accept values only if not
	 * null.
	 *
	 * @param <T>
	 *            the extension object
	 * @param <U>
	 *            which to check for null
	 * @param typeIsNull
	 *            value to check for null
	 * @param extensionObject
	 *            Object on which operation to be performed.
	 * @param consumer
	 *            {@link BiConsumer}
	 */
	public static <T, U> void setIfNotNull(U typeIsNull, T extensionObject, BiConsumer<T, U> consumer) {
		runConsumerIfPredicatePass(typeIsNull, t -> Optional.ofNullable(t).isPresent(), extensionObject, consumer);
	}

	/**
	 * This method executes consumer if the predicate is pass on given object.
	 * @param <T> Other parameter's type class
	 * @param <U> typeToTest class
	 * @param typeToTest object to test
	 * @param predicate testing if the value of typeToTest is valid 
	 * @param otherParamOfConsumer Other parameter
	 * @param consumer which will be run in case of predicate success
	 */
	public static <T, U> void runConsumerIfPredicatePass(U typeToTest, Predicate<U> predicate, T otherParamOfConsumer, BiConsumer<T, U> consumer) {
		if (predicate.test(typeToTest)) {
			consumer.accept(otherParamOfConsumer, typeToTest);
		}
	}


	/**
	 * Pass to consumer OtherObject if typeToTest pass predicate and output of converter converting the typeTo test. 
	 * @param <T> Other object to be passed to consumer
	 * @param <U> Item to be tested for null and to be consumed further
	 * @param <V> converter returns in this type and the value will be consumed by consumer
	 * @param typeToTest Item to be tested for null and to be consumed further
	 * @param predicate Predicate to test the value
	 * @param otherObject Item to be consumed by consumer
	 * @param convertor Converter to convert typeToTest to relevant consumer type
	 * @param consumer Consumer accepting the typeToTest and otherObject
	 */
	public static <T, U, V> void setIfPredicatePass(U typeToTest, Predicate<U> predicate, T otherObject, Function<U,V> convertor, BiConsumer<T, V> consumer) {
		if (predicate.test(typeToTest)) {
			consumer.accept(otherObject, convertor.apply(typeToTest));
		}
	}

	/**
	 * This method validates typeToTest parameter on basis of predicate. 
	 * On successful validation convert the input(type to test) 
	 * which is then passed to function along with other object
	 * and the value of this function is returned.
	 * @param <T> extension object passed to the consumer
	 * @param <U> converter returns in this type and the value will be consumed by consumer
	 * @param <V> Item to be tested for null and to be consumed further 
	 * @param <R> Returned by this method if the V instance is tested passed
	 * @param typeToTest V instance
	 * @param predicate Predicate to test the value
	 * @param otherObject Y instance
	 * @param consumer bifunction of T, U and R
	 * @param convertor Function of V and U
	 * @return R if V instance passes
	 */
	public static <T, U,V,R> R getIfPredicatePass(V typeToTest, Predicate<V> predicate, T otherObject, BiFunction<T, U, R> consumer, Function<V,U> convertor) {
		if (predicate.test(typeToTest)) {
			return consumer.apply(otherObject, convertor.apply(typeToTest));
		}
		return null;
	}

	/**
	 * Test predicate, if it pass then return the function result.
	 * @param <V> Type to test
	 * @param <R> Type to return
	 * @param typeToTest Type on which predicate has to be executed
	 * @param predicate Predicate to be tested on the type
	 * @param function Actual function which return value if predicate pass
	 * @return Return value of function or null if predicate fails
	 */
	public static <V,R> R getIfPredicatePass(V typeToTest, Predicate<V> predicate, Function<V,R> function) {
		if (predicate.test(typeToTest)) {
			return function.apply(typeToTest);
		}
		return null;
	}

	/**
	 * This method sets extensionObject if {@link ObjectIdLong} instance is not null.
	 * @param <T> Extension class
	 * @param valueToTest {@link ObjectIdLong} instance
	 * @param extensionObject T instance
	 * @param consumer Consumes T and {@link ObjectIdLong} instances
	 */
	public <T> void setIfObjectIdLongNotNull(ObjectIdLong valueToTest, T extensionObject, BiConsumer<T, ObjectIdLong> consumer) {
		runConsumerIfPredicatePass(valueToTest, this::legacyNullPredicate, extensionObject, consumer);
	}

	/**
	 * Returns R if ObjectIdLong instance is not null.
	 * @param <R> Returned if {@link ObjectIdLong} instance is not null
	 * @param <T> Extension class
	 * @param valueToTest {@link ObjectIdLong} instance
	 * @param extensionObject T instance
	 * @param consumer a bifunction 
	 * @return R instance
	 */
	public <T,R> R getIfObjectIdLongNotNull(ObjectIdLong valueToTest, T extensionObject, BiFunction<T, Long, R> consumer) {
		return getIfPredicatePass(valueToTest, this::legacyNullPredicate, extensionObject, consumer, this::getLongFromObjectIdLong);
	}

	/**
	 * Extract from an object if the object is not null. For extracting,
	 * function is used.
	 *
	 * @param <T>
	 *            T Returned if not null
	 * @param <U>
	 *            Type to check for null
	 * @param typeIsNull
	 *            Object to be tested for null
	 * @param functionToExtract
	 *            function to be used for extracting
	 * @return extracted value from function
	 */
	public static <T, U> T extractIfNotNull(U typeIsNull, Function<U, T> functionToExtract) {
		return Optional.ofNullable(typeIsNull).isPresent() ? functionToExtract.apply(typeIsNull) : null;
	}

	/**
	 * Extract from an object if the object is not null. For extracting,
	 * function is used.
	 *
	 * @param <T>
	 *            T Returned if not null
	 * @param <U>
	 *            Type to check for null
	 * @param typeIsNull
	 *            Object to be tested for null
	 * @param functionToExtract
	 *            function to be used for extracting
	 * @param supplierForNullCase
	 *            function to be used for supplying if null
	 * @return extracted value from function
	 */
	public static <T, U> T extractIfNulOrNotNull(U typeIsNull, Function<U, T> functionToExtract, Supplier<T> supplierForNullCase) {
		return Optional.ofNullable(typeIsNull).isPresent() ? functionToExtract.apply(typeIsNull) : supplierForNullCase.get();
	}

	/**
	 * Extract from an object if the objectIdLong is not null. For extracting,
	 * function is used.
	 *
	 * @param <V>
	 *            Type to check for null
	 * @param <T>
	 *            Returned if not null
	 * @param typeIsNull
	 *            {@link ObjectIdLong} to be tested for null
	 * @param functionToExtract
	 *            function to be used for extracting
	 * @return extracted value from function
	 */
	public <V extends KNullIfc,T> T extractIfLegacyNullableNotNull(V typeIsNull, Function<V, T> functionToExtract) {
		return getIfPredicatePass(typeIsNull, this::legacyNullPredicate, functionToExtract);
	}

	/**
	 * This method sets Dates.
	 * @param <T> any type extending {@link EffectiveDatedEntry}
	 * @param attribute
	 *        attribute for T instance
	 * @param effectiveDate {@link KDate} instance for effective date
	 * @param expirationDate {@link KDate} instance for expiration date
	 */
	public <T extends EffectiveDatedEntry> void setDates(T attribute, KDate effectiveDate, KDate expirationDate) {
		setDate(attribute::setEffectiveDate, effectiveDate);
		setDate(attribute::setExpirationDate, expirationDate);
	}

	/**
	 * This method sets date from kdate in local date format.
	 *
	 * @param consumer Consumer instance for accepting kdate entry in local 
	 * date format.
	 *
	 * @param kdate {@link KDate} instance
	 */
	public void setDate(Consumer<LocalDate> consumer, KDate kdate) {
		consumer.accept(kDateToLocalDate(kdate));
	}

	/**
	 * Set Long value using consumer from ObjectIdLong.
	 *
	 * @param consumer
	 *            the consumer of {@link Long} type.
	 * @param objectIdLong
	 *            the person id.
	 */
	public void setLongFromObjectIdLong(Consumer<Long> consumer, ObjectIdLong objectIdLong) {
		consumer.accept(getLongFromObjectIdLong(objectIdLong));
	}

	/**
	 * This get the KDate from KDateTime.
	 *
	 * @param effectiveDateTime
	 *            the effective date and time as KDateTime
	 * @return the kDate
	 */
	public KDate getDateFromTime(KDateTime effectiveDateTime) {
		return Optional.ofNullable(effectiveDateTime).isPresent() ? effectiveDateTime.getDate() : null;
	}

	/**
	 * This method returns Array of T instances which are not present in cache Set.
	 *
	 * @param <T> Extension class
	 * @param actualPersonIds Array of T instances
	 * @param personIdsFromCache with person IDs from cache
	 * @return List of T instance
	 */
	public <T> List<T> getIdsNotInSet(T[] actualPersonIds, Set<T> personIdsFromCache) {
		if (personIdsFromCache.isEmpty()) {
			// nothing retrieved from cache
			return Arrays.asList(actualPersonIds);
		}
		List<T> outputList = new ArrayList<T>();
		for (T actualPersonId : actualPersonIds) {
			if (!personIdsFromCache.contains(actualPersonId)) {
				outputList.add(actualPersonId);
			}
		}
		return outputList;
	}

	/**
	 * Put in list present in map.
	 * @param <T>
	 *            the map key
	 * @param <U>
	 *            the item
	 * @param map
	 *            this is tenentMap. It has long key and List of CacheEntry as
	 *            value
	 * @param key
	 *            tenentId is key
	 * @param item
	 *            CacheEnty is item
	 */
	public <T, U> void putInListInsideMap(Map<T, List<U>> map, T key, U item) {
		List<U> list = map.get(key);
		if (list == null) {
			list = new ArrayList<>();
			map.put(key, list);
		}
		list.add(item);
	}

	/**
	 * This method adds list of items in map corresponding to the key passed.
	 *
	 * @param <T>
	 *            map key type
	 * @param <U>
	 *            Collection of this type is the value
	 * @param map
	 *            map of T as key and collection of U as value
	 * @param key
	 *            instance of T for which we need to add items in map
	 * @param items
	 *            List of U to be added in map corresponding to the key passed
	 */
	public <T, U> void putListInSetInsideMap(Map<T, Collection<U>> map, T key, List<U> items) {
		putInCollectionInsideMap(map, key, items, ConcurrentHashMap::newKeySet);
	}

	/**
	 * This method puts/adds itemsToBePut collection in map corresponding to the
	 * Key.
	 *
	 * @param <T>
	 *            map key type
	 * @param <U>
	 *            Collection of this type is the value
	 * @param map
	 *            map of T instance as key and collection of U instances as
	 *            value.
	 * @param key
	 *            instance of T
	 * @param itemsToBePut
	 *            Collection of U to be added as value in map
	 * @param collectionGenerator
	 *            Supplier which is passed to instantiate collection of a
	 *            specific type to be added as value in map
	 */
	public <T, U> void putInCollectionInsideMap(Map<T, Collection<U>> map, T key, Collection<U> itemsToBePut, Supplier<Collection<U>> collectionGenerator) {
		Collection<U> collection = map.get(key);
		if (collection == null) {
			synchronized (map) {
				collection = map.get(key);
				if (collection == null) {
					collection = collectionGenerator.get();
					map.put(key, collection);
				}
			}
		}
		collection.addAll(itemsToBePut);
	}

	/**
	 * Getter method for {@code ObjectIdLong}.
	 *
	 * @param value
	 *            {@link Long}, will contain the person id
	 *
	 * @return {@link ObjectIdLong} instance having value casted to {@code Long}
	 *         .
	 */
	public ObjectIdLong getObjectIdLong(Long value) {
		return new ObjectIdLong((Long) value);
	}

	/**
	 * Takes list of Long instances and returns list of corresponding ObjectIdLong instances.
	 * @param listOfLong List of Long instances
	 * @return List of ObjectIdLong
	 */
	public List<ObjectIdLong> getListOfObjectIdsFromListOfLong(List<Long> listOfLong){
		return (List<ObjectIdLong>) convertLegacyCollection(listOfLong, this::getObjectIdLong);
	}

	/**
	 * This method takes in a list of type T, and filters entries from this list
	 * based on the predicate. List filtered out is passed in consumer of
	 * filteredList and the remaining list is passed in consumer of remaining
	 * list.
	 *
	 * Please note, the actual list should not be used as it has been filtered as per above logic.
	 *
	 * @param <T>
	 * 			  generic class of which we have to process the list of 
	 * @param actualList
	 *            List of type T on which operations are to be performed
	 * @param predicate
	 *            Predicate taking in T
	 * @param filteredConsumer
	 *            Consumer of List of T for filtered list
	 * @param remainingConsumer
	 *            Consumer of List of T for remaining list
	 */
	public <T> void filterAndProcessSubLists(List<T> actualList, Predicate<T> predicate, Consumer<List<T>> filteredConsumer,
											 Consumer<List<T>> remainingConsumer) {
		//The actual list being consumed here and filterd, hence should not be used post this method call.
		List<T> filteredList = actualList.stream().filter(predicate).collect(Collectors.toList());
		actualList.removeAll(filteredList);
		if(!CollectionUtils.isEmpty(filteredList)){
			filteredConsumer.accept(filteredList);
		}
		if(!CollectionUtils.isEmpty(actualList)){
			remainingConsumer.accept(actualList);//actual list now only have remaining items.
		}
	}

}