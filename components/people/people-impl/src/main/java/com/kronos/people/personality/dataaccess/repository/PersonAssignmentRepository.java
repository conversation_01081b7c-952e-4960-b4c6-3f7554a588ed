package com.kronos.people.personality.dataaccess.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.kronos.people.personality.dataaccess.entity.PersonAssignment;
import org.springframework.transaction.annotation.Transactional;


/**
 * Attestation Person Assignment Repository for DB operations
 */
@Repository
@Transactional
public interface PersonAssignmentRepository extends JpaRepository<PersonAssignment, Long> {
    /**
     * Retrieves a Person Assignment by its id.
     *
     * @param id the person assignment id
     * @return the person assignment with the given id or Optional#empty() if none found
     */
    @Query("SELECT distinct pa FROM PersonAssignment pa LEFT JOIN FETCH pa.attestationProfileAssignments a WHERE pa.id = :id")
    Optional<PersonAssignment> findById(@Param("id") Long id);

    /**
     * Returns all Person Assignments with the given IDs.
     *
     * @param ids person assignment IDs
     * @return List of Person Assignments
     */
    @Query("SELECT distinct pa FROM PersonAssignment pa LEFT JOIN FETCH pa.attestationProfileAssignments a WHERE pa.id in :ids")
    List<PersonAssignment> findAll(@Param("ids") Iterable<Long> ids);

    @Query("SELECT pa.id FROM PersonAssignment pa WHERE pa.preferenceprofId in :ids")
    List<Long> findPersonIdsByDisplayProfileIds(@Param("ids") List<Long> ids);
}
