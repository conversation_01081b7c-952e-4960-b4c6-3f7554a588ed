/***********************************************************************
 * PersonalityDataAccessServiceImpl.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.kronos.people.personality.dataaccess.repository.PersonRepository;

/**
 * The {@code PeopleService} class provides implementation of
 * {@link PersonalityDataAccessService}.
 * 
 * <AUTHOR>
 */

@Repository
@Transactional(readOnly = true)
public class PersonalityDataAccessServiceImpl implements PersonalityDataAccessService {

	/**
	 * Instance of {@link PersonRepository}
	 */
	@Autowired
	private PersonRepository personRepository;

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<Long> getPersonIds() {
		return getPersonRepository().getPersonIds();
	}
	public List<Long> getPersonIdspreferred() {
		return getPersonRepository().getPersonIdspreferred();
	}
	/**
	 * @return the instance of {@link PersonRepository}
	 */
	public PersonRepository getPersonRepository() {
		return personRepository;
	}

	/**
	 * @param personRepository
	 *            the instance of {@link PersonRepository} to set
	 */
	public void setPersonRepository(PersonRepository personRepository) {
		this.personRepository = personRepository;
	}

	@Override
	public List<Long> getPersonIds(Integer moduloRemainder,Integer moduloFactor) {
		return getPersonRepository().getPersonIds(moduloRemainder,moduloFactor);
	}
}
