package com.kronos.people.personality.dataaccess.entity.brazilcompliance;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Entity
@Table(name = "brc_employee_type_assignment")
@SuppressWarnings("squid:S2160") //Suppress "Override equals/hashcode" sonar warning for Entity classes
public class BrazilRepTypeAssignmentEntity extends BrazilAssignmentAbstractEntity {

    @Column(name = "rep_type_id")
    private Long repTypeId;

    @Column(name = "union_agreement_number")
    private String unionAgreementNumber;

    public Long getRepTypeId() {
        return repTypeId;
    }

    public void setRepTypeId(Long repTypeId) {
        this.repTypeId = repTypeId;
    }

    public String getUnionAgreementNumber() {
        return unionAgreementNumber;
    }

    public void setUnionAgreementNumber(String unionAgreementNumber) {
        this.unionAgreementNumber = unionAgreementNumber;
    }
}