package com.kronos.people.personality.exception.impl.ia;

import com.kronos.commonbusiness.datatypes.ia.IAErrorDetail;
import com.kronos.commonbusiness.datatypes.ia.IARequest;
import com.kronos.container.api.util.IAPIExceptionHandler;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import java.util.List;
import java.util.stream.Collectors;

import static com.kronos.people.personality.exception.ExceptionConstants.PARTIAL_SUCCESS_ERROR_CODE;
import static com.kronos.people.personality.exception.ExceptionConstants.PERSONALITY_EXCEPTION_ERROR_CODE;
import static com.kronos.people.personality.exception.ExceptionConstants.PRIMARY_JOB_HISTORY_COMMON_ERROR_CODE;

@Component
public class PersonalityExceptionHelper {

	@Lazy
	@Inject
	private IAPIExceptionHandler apiExceptionHandler;

	public IAErrorDetail buildEmployeeExtensionErrorDetail(List<PersonalityExtensionException> exceptionList, IARequest request) {
		String message = getLocalizedErrorMessage(PARTIAL_SUCCESS_ERROR_CODE);
		IAErrorDetail errorDetail = buildErrorDetail(PARTIAL_SUCCESS_ERROR_CODE, null, message);

		String errorMessage = getLocalizedErrorMessage(PERSONALITY_EXCEPTION_ERROR_CODE);
		String columnName = getFirstColumnName(request);
		IAErrorDetail employeeExtensionErrorDetail = buildErrorDetail(PERSONALITY_EXCEPTION_ERROR_CODE, columnName, errorMessage);
		errorDetail.setDetails(exceptionList.stream().map(e -> employeeExtensionErrorDetail)
									   .collect(Collectors.toList()));
		return errorDetail;
	}

	public IAErrorDetail buildCommonErrorDetail(IARequest request) {
		String columnName = getFirstColumnName(request);
		String message = getLocalizedErrorMessage(PRIMARY_JOB_HISTORY_COMMON_ERROR_CODE);
		return buildErrorDetail(PRIMARY_JOB_HISTORY_COMMON_ERROR_CODE, columnName, message);
	}

	private String getFirstColumnName(IARequest request) {
		return request.getColumns().size() > 0 ? request.getColumns().get(0).getName() : StringUtils.EMPTY;
	}

	private String getLocalizedErrorMessage(String messageKey) {
		// As IKProperties already knows how to find the correct locale for the current user.
		return apiExceptionHandler.getErrorMessage(messageKey, null, null);
	}

	private IAErrorDetail buildErrorDetail(String code, String key, String message) {
		IAErrorDetail errorDetail = new IAErrorDetail();
		errorDetail.setCode(code);
		errorDetail.setKey(key);
		errorDetail.setMessage(message);
		return errorDetail;
	}
}
