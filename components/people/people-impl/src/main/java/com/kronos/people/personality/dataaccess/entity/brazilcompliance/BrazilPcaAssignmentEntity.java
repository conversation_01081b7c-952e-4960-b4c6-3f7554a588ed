package com.kronos.people.personality.dataaccess.entity.brazilcompliance;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Entity
@Table(name = "brc_employee_paycode_attribute_assignment")
@SuppressWarnings("squid:S2160") //Suppress "Override equals/hashcode" sonar warning for Entity classes
public class BrazilPcaAssignmentEntity extends BrazilAssignmentAbstractEntity {

    @Column(name = "paycode_attr_def_id")
    private Long payCodeAttrDefId;

    public Long getPayCodeAttrDefId() {
        return payCodeAttrDefId;
    }

    public void setPayCodeAttrDefId(Long payCodeAttrDefId) {
        this.payCodeAttrDefId = payCodeAttrDefId;
    }
}