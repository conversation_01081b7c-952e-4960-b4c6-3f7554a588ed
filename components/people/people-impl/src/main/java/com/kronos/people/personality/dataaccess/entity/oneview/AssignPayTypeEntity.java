package com.kronos.people.personality.dataaccess.entity.oneview;

import com.kronos.people.personality.dataaccess.converter.LocalDateTimeConverter;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "assignpaytype")
public class AssignPayTypeEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "assignpaytypeid", nullable = false)
    private Long assignPayTypeId;

    @Column(name = "personid", nullable = false)
    private Long personId;

    @Column(name = "paytype", nullable = false)
    private String payType;

    @Column(name = "effectivedtm", nullable = false)
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime effectiveDtm;

    @Column(name = "expirationdtm", nullable = false)
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime expirationDtm;

    @Column(name = "updatedtm", nullable = false)
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime updateDtm;

    // Getters and Setters

    public Long getAssignPayTypeId() {
        return assignPayTypeId;
    }

    public void setAssignPayTypeId(Long assignPayTypeId) {
        this.assignPayTypeId = assignPayTypeId;
    }

    public Long getPersonId() {
        return personId;
    }

    public void setPersonId(Long personId) {
        this.personId = personId;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public LocalDateTime getEffectiveDtm() {
        return effectiveDtm;
    }

    public void setEffectiveDtm(LocalDateTime effectiveDtm) {
        this.effectiveDtm = effectiveDtm;
    }

    public LocalDateTime getExpirationDtm() {
        return expirationDtm;
    }

    public void setExpirationDtm(LocalDateTime expirationDtm) {
        this.expirationDtm = expirationDtm;
    }

    public LocalDateTime getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(LocalDateTime updateDtm) {
        this.updateDtm = updateDtm;
    }
}