package com.kronos.people.personality.dataaccess.entity.brazilcompliance;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Entity
@Table(name = "brc_employee_company_assignment")
@SuppressWarnings("squid:S2160") //Suppress "Override equals/hashcode" sonar warning for Entity classes
public class BrazilCompanyAssignmentEntity extends BrazilAssignmentAbstractEntity {

    @Column(name = "company_id")
    private Long companyId;

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}