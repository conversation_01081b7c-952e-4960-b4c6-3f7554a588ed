package com.kronos.people.personality.dataaccess.repository.brazilcompliance;

import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilCompanyAssignmentEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface BrazilCompanyAssignmentRepository extends JpaRepository<BrazilCompanyAssignmentEntity, Long> {

    /**
     * Returns all Brazil employee records by the given person  ids.
     *
     * @param personIds the person ids
     * @return List of BRC employees
     */
    @Query("SELECT brccompanyassignment FROM BrazilCompanyAssignmentEntity brccompanyassignment WHERE brccompanyassignment.personId IN :personIds ORDER BY brccompanyassignment.effectiveDate ASC")
    List<BrazilCompanyAssignmentEntity> findByPersonIds(@Param("personIds") List<Long> personIds);

}