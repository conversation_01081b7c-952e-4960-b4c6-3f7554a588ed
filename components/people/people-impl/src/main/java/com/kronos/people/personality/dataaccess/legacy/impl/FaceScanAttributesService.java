package com.kronos.people.personality.dataaccess.legacy.impl;

import com.kronos.datacollection.udm.service.facescan.api.IFaceScanAttributesService;
import com.kronos.datacollection.udm.service.facescan.api.dto.FaceScan;
import com.ukg.container.selectiveloading.exception.NotImplementedException;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class FaceScanAttributesService implements IFaceScanAttributesService {

    @Override
    public boolean isFeatureEnabled() {
        return true;
    }

    @Override
    public boolean isFeatureEnabled(long l, String s) {
        return true;
    }

    @Override
    public FaceScan getFaceScanByPersonId(long l) {
        throw new NotImplementedException();
    }

    @Override
    public Boolean isConsentGiven(long l) {
        throw new NotImplementedException();
    }

    @Override
    public LocalDateTime getEnrollmentConsentDateTime(long l) {
        throw new NotImplementedException();
    }

    @Override
    public boolean isEnrolled(long l) {
        return false;
    }

    @Override
    public LocalDateTime getEnrollmentDateTime(long l) {
        throw new NotImplementedException();
    }

    @Override
    public String getEnrollmentLocation(long l) {
        throw new NotImplementedException();
    }

    @Override
    public Integer getVerificationThreshold(long l) {
        throw new NotImplementedException();
    }

    @Override
    public String getVerificationThresholdLabel(long l) {
        throw new NotImplementedException();
    }

    @Override
    public Integer getQualityScore(long l) {
        throw new NotImplementedException();
    }

    @Override
    public boolean isTemplateDeleted(long l) {
        throw new NotImplementedException();
    }
}
