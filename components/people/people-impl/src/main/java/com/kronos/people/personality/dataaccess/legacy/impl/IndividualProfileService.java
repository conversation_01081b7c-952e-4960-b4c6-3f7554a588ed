package com.kronos.people.personality.dataaccess.legacy.impl;

import com.kronos.datacollection.udm.jaxb.AssignedIndividualProfile;
import com.kronos.datacollection.udm.service.individualprofile.api.IIndividualProfileService;
import com.kronos.datacollection.udm.service.individualprofile.api.dto.IndividualProfile;
import com.ukg.container.selectiveloading.exception.NotImplementedException;
import org.springframework.stereotype.Component;
import java.util.List;

@Component
public class IndividualProfileService implements IIndividualProfileService {

    @Override
    public List<IndividualProfile> getAllIndividualProfiles() {
        throw new NotImplementedException();
    }

    @Override
    public List<AssignedIndividualProfile> getAllAssignedIndividualProfiles() {
        throw new NotImplementedException();
    }

    @Override
    public String getIndividualProfileNameById(long l) {
        throw new NotImplementedException();
    }

    @Override
    public Long getIndividualProfileIdByName(String s) {
        throw new NotImplementedException();
    }

    @Override
    public String getIndividualProfileNameByEmployeeId(long l) {
        throw new NotImplementedException();
    }

    @Override
    public Long getIndividualProfileIdByEmployeeId(long l) {
        throw new NotImplementedException();
    }

    @Override
    public void updateEmployeeIndividualProfile(long l, Long aLong) {
        throw new NotImplementedException();
    }
}
