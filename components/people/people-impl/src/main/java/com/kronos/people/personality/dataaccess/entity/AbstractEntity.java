package com.kronos.people.personality.dataaccess.entity;

import java.io.Serializable;
import java.util.Objects;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.MappedSuperclass;

@MappedSuperclass
@Access(AccessType.FIELD)
public abstract class AbstractEntity<I extends Serializable> implements Entity<I> {

    private static final long serialVersionUID = 501991620752343537L;

    protected transient I id;

    public void setId(I id) {
        this.id = id;
    }

    @SuppressWarnings("rawtypes")
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        if (this.getId() == null || ((AbstractEntity) obj).getId() == null) {
            return false;
        }

        return Objects.equals(this.getId(), ((AbstractEntity) obj).getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(this.getId());
    }
}
