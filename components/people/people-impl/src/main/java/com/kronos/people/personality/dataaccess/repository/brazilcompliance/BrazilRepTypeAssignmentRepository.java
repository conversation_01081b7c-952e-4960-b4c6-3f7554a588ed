package com.kronos.people.personality.dataaccess.repository.brazilcompliance;

import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilRepTypeAssignmentEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface BrazilRepTypeAssignmentRepository extends JpaRepository<BrazilRepTypeAssignmentEntity, Long> {

    /**
     * Returns all Brazil employee records by the given person  ids.
     *
     * @param personIds the person ids
     * @return List of BRC employees
     */
    @Query("SELECT brcreptypeassignment FROM BrazilRepTypeAssignmentEntity brcreptypeassignment WHERE brcreptypeassignment.personId IN :personIds ORDER BY brcreptypeassignment.effectiveDate ASC")
    List<BrazilRepTypeAssignmentEntity> findByPersonIds(@Param("personIds") List<Long> personIds);

}