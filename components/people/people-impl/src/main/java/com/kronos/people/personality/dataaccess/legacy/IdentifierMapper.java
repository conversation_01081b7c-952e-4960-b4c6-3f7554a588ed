/***********************************************************************
 * IdentifierMapper.java
 * 
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.legacy;

import com.kronos.logging.slf4jadapter.util.LogService;
import com.kronos.people.personality.dataaccess.adapter.ConcurrencyHelper;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import com.kronos.people.personality.exception.impl.ExceptionHelper;
import com.kronos.people.personality.model.Criteria;
import com.kronos.people.personality.model.IdentifierType;
import com.kronos.people.personality.util.OptionalPersonId;
import com.kronos.wfc.commonapp.people.business.personality.PersonalityTriplet;
import com.kronos.wfc.platform.businessobject.framework.BusinessProcessingException;
import com.kronos.wfc.platform.exceptions.framework.PersistenceException;
import com.kronos.wfc.platform.logging.framework.Log;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.wfc.platform.tenant.api.TenantDiscriminatorProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * This {@code IdentifierMapper} class is to get person id/id's by criteria
 * {@link Criteria}.
 * 
 * 
 * <AUTHOR>
 *
 */
@Named
public class IdentifierMapper {

	private static final Logger LOGGER = LoggerFactory.getLogger(IdentifierMapper.class);

	@Inject
	ExceptionHelper exceptionHandler;

	@Inject
	PersonalityFacade personalityFacade;
	
	@Inject
	ConcurrencyHelper concurrencyHelper;

	/**
	 * This method gets the person ids from {@link Criteria}. It will validate
	 * the type of each person id, if validated then assign it to
	 * {@code personId} of {@link OptionalPersonId} else assign {@code null} to
	 * {@code personId} of {@code OptionalPersonId}.
	 * 
	 * @param criteria
	 *            {@link Criteria}
	 * @return {@link Map} of personId {@link Long} and {@link OptionalPersonId}
	 *         .
	 */
	public Map<Object, OptionalPersonId> getPersonIds(Criteria criteria) {
		String trxId=new LogService().getTransactionId();
		LOGGER.info("personalityService --> IdentifierMapper getPersonIds");
		IdentifierType idType = criteria.getIdsType();
		Map<Object, OptionalPersonId> output = new ConcurrentHashMap<>();
		Object[] criteriaIdsArray = criteria.getIds();
		Queue<Object> queue = concurrencyHelper.getQueueFromArray(criteriaIdsArray);
		Function<Object, Long> personIdExtractor;
		if (IdentifierType.BADGENUMBER.equals(idType)) {
			personIdExtractor = id->getPersonId(personalityFacade.getViaBadgeNumber(id, criteria.getSnapshotDateTime()));
		} else {
			personIdExtractor = id->getPersonId(id, idType);
		}
		concurrencyHelper.parallelProcessQueueItemsForMultiGet(queue, id -> {
				OptionalPersonId pid = null;
				try {
					pid = new OptionalPersonId(personIdExtractor.apply(id), null);
				} catch (Exception e) {
					Log.log(Log.ERROR, "IdentifierMapper : " + e.getLocalizedMessage());
					Log.log(Log.ERROR,e);
					pid = new OptionalPersonId(null, e);
				}
				output.put(id, pid);
				return true;
			},criteriaIdsArray.length);

		// Going to through the values after the lambda to make sure that all exception are logged on the HTTP thread and not the concurrency thread
		output.values().forEach(optionalPersonId -> {
			Exception currentException = optionalPersonId.getException();
			if (currentException != null) {
				if (currentException instanceof PersonalityExtensionException) {
					Log.log(Log.ERROR, "PersonalityFacade : " + currentException.getLocalizedMessage(), currentException);
					optionalPersonId.setException(currentException);
				}
				// We are not going to log the BusinessProcessingException for example when person doesn't exist? This may be redundant as the response already has the cause.
				else if (currentException instanceof BusinessProcessingException) {
					Log.log(Log.ERROR, "PersonalityFacade : " + currentException.getLocalizedMessage(), currentException);
					optionalPersonId.setException(exceptionHandler.transformException(currentException, idType));
				}
				else if (currentException instanceof PersistenceException) {
					Log.log(Log.ERROR, "PersonalityFacade : " + currentException.getLocalizedMessage(), currentException);
					currentException.getLocalizedMessage();
					optionalPersonId.setException(exceptionHandler.createException(idType, currentException.getLocalizedMessage()));
				}
				else {
					Log.log(Log.ERROR, "PersonalityFacade : " + currentException.getMessage());
					Log.log(Log.ERROR,currentException);
					optionalPersonId.setException(exceptionHandler.transformException(currentException, idType));
				}
			}
		});
		return output;
	}

	/**
	 * This method get the person id from cache by secondaryIdentifier and
	 * identifierType. If the type of person id is not validated then exception
	 * {@link PersonalityExtensionException} is thrown. For more details refer
	 * method {@link # getPersonIdFromCache(HashKey, Object, IdentifierType)}.
	 * 
	 * @param secondaryIdentifier
	 *            {@link Object}, will contain the person id
	 * @param identifierType
	 *            {@link IdentifierType}
	 * @return person id {@link Long} from cache
	 */
	private Long getPersonId(Object secondaryIdentifier, IdentifierType identifierType) {
		return getPersonIdFromCache(secondaryIdentifier, identifierType);
	}

	// @Cachable(key=hashkey,cachename="personality")

	/**
	 * This method gets the person id from cache by hashkey, value and
	 * identifierType.
	 * 
	 * @param value
	 *            {@link Object} - will conatin the person id
	 * @param identifierType
	 *            {@link IdentifierType}
	 * @return person id {@link Long} - If identifierType is {@code PERSONID}
	 *         then return it as {@link Long}. If {@link PersonalityTriplet} is
	 *         null, return null else return person id.
	 */
	private Long getPersonIdFromCache(Object value, IdentifierType identifierType) {
		PersonalityTriplet pt = personalityFacade.getPersonalityTriplet(value, identifierType);
		return getPersonId(pt);
	}

	/**
	 * Getter method for person id.
	 * 
	 * @param pt
	 *            {@link PersonalityTriplet}
	 * @return person id in {@code Long}.
	 */
	protected Long getPersonId(PersonalityTriplet pt) {
		return pt == null ? null : pt.getPersonId().toLong();
	}

	/**
	 * @param exceptionHandler
	 *            the exceptionHandler to set
	 */
	public void setExceptionHandler(ExceptionHelper exceptionHandler) {
		this.exceptionHandler = exceptionHandler;
	}

	/**
	 * @param personalityFacade
	 *            the personalityFacade to set
	 */
	public void setPersonalityFacade(PersonalityFacade personalityFacade) {
		this.personalityFacade = personalityFacade;
	}

}
