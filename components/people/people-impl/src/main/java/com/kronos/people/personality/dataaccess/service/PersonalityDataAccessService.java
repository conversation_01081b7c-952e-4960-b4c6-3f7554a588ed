/***********************************************************************
 * PersonalityDataAccessService.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/

package com.kronos.people.personality.dataaccess.service;

import java.util.List;

/**
 * The {@code PeopleService} interface provides the methods for retrieving
 * person id's.
 * 
 * <AUTHOR>
 */
public interface PersonalityDataAccessService {

	/**
	 * This will return the list of person id's.
	 * 
	 * @return {@link Long} list of person id's
	 */
	List<Long> getPersonIds();
	List<Long> getPersonIdspreferred();
	List<Long> getPersonIds(Integer moduloFactor,Integer moduloRemainder);
}
