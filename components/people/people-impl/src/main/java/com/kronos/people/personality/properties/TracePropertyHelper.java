package com.kronos.people.personality.properties;

import com.kronos.tenantprovider.api.TenantProvider;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import io.opentelemetry.api.trace.Span;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import java.net.InetAddress;

@Component
public class TracePropertyHelper {

   public static final String EXTENSION_TYPE_ATTRIBUTE_KEY = "ukg.extension.type";
   private static final String SERVICE_NAME = "service.name";
   private static final String CLUSTER_ID = "service.cluster.id";
   private static final String ENDUSER_TENANT_ID = "enduser.tenant.id";
   private static final String TARGET_PERSON_ID_EXTENSION_KEY = "ukg.target_employee.person.id";
   private static final String TARGET_PERSON_NUMBER_EXTENSION_KEY = "ukg.target_employee.person.number";
   private static final String SERVICE = "wfm.wfmaas.wfm_platform.personality_service";
   private static final String PILLAR = "wfm";

   @Inject
   @Lazy
   TenantProvider tenantProvider;

   public void setServiceNameTenantAndCluster(Personality personality) {
      setServiceNameTenantAndCluster();
      if (personality != null) {
         Span.current().setAttribute(TARGET_PERSON_ID_EXTENSION_KEY, personality.getId());
         Span.current().setAttribute(TARGET_PERSON_NUMBER_EXTENSION_KEY, personality.getPersonNumber());
      }
   }

   public void setServiceNameTenantAndCluster() {
      Span.current().setAttribute(SERVICE_NAME, SERVICE);
      if (!tenantProvider.isTenantIdNullforCurrentThread()) {
         Span.current().setAttribute(ENDUSER_TENANT_ID, tenantProvider.getTenantId());
      }
      Span.current().setAttribute(CLUSTER_ID, getClusterId());
   }

   private String getClusterId() {
      String clusterId;
      try {
         String serverName = InetAddress.getLocalHost().getHostName();
         int index = serverName.indexOf(PILLAR);
         if (index > 0 && serverName.length() > index + 9) {
            clusterId = serverName.substring(index, index + 9).trim();
         } else {
            clusterId = PILLAR;
         }
      } catch (Exception e) {
         return PILLAR;
      }
      return clusterId;
   }
}
