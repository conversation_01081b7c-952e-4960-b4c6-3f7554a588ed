package com.kronos.people.personality.dataaccess.entity.oneview;

import com.kronos.people.personality.dataaccess.converter.LocalDateTimeConverter;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "assignpaygroup")
public class AssignPayGroupEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "assignpaygroupid", nullable = false)
    private Long assignPayGroupId;

    @Column(name = "personid", nullable = false)
    private Long personId;

    @Column(name = "paygroup", nullable = false)
    private String payGroup;

    @Column(name = "effectivedtm", nullable = false)
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime effectiveDtm;

    @Column(name = "expirationdtm", nullable = false)
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime expirationDtm;

    @Column(name = "updatedtm", nullable = false)
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime updateDtm;

    // Getters and Setters

    public Long getAssignPayGroupId() {
        return assignPayGroupId;
    }

    public void setAssignPayGroupId(Long assignPayGroupId) {
        this.assignPayGroupId = assignPayGroupId;
    }

    public Long getPersonId() {
        return personId;
    }

    public void setPersonId(Long personId) {
        this.personId = personId;
    }

    public String getPayGroup() {
        return payGroup;
    }

    public void setPayGroup(String payGroup) {
        this.payGroup = payGroup;
    }

    public LocalDateTime getEffectiveDtm() {
        return effectiveDtm;
    }

    public void setEffectiveDtm(LocalDateTime effectiveDtm) {
        this.effectiveDtm = effectiveDtm;
    }

    public LocalDateTime getExpirationDtm() {
        return expirationDtm;
    }

    public void setExpirationDtm(LocalDateTime expirationDtm) {
        this.expirationDtm = expirationDtm;
    }

    public LocalDateTime getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(LocalDateTime updateDtm) {
        this.updateDtm = updateDtm;
    }
}