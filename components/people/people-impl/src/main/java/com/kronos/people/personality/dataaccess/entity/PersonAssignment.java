package com.kronos.people.personality.dataaccess.entity;

import java.util.List;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

@Entity
@Table(name = "prsnaccsassign")
public class PersonAssignment extends AbstractEntity<Long> {

    @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true, mappedBy = "personAssignment")
    private List<AttestationProfileAssignment> attestationProfileAssignments;

    @Override
    @Column(name = "personid", nullable = false)
    @Id
    @Access(AccessType.PROPERTY)
    public Long getId() {
        return id;
    }

    @Column(name = "preferenceprofid")
    public Long preferenceprofId;

    public Long getPreferenceprofId() {
        return preferenceprofId;
    }

    public void setPreferenceprofId(Long preferenceprofId) {
        this.preferenceprofId = preferenceprofId;
    }

    public List<AttestationProfileAssignment> getAttestationProfileAssignments() {
        return attestationProfileAssignments;
    }

    public void setAttestationProfileAssignments(List<AttestationProfileAssignment> attestationProfileAssignments) {
        this.attestationProfileAssignments = attestationProfileAssignments;
    }
}
