/***********************************************************************
 * PersonRepository.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.repository;

import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.kronos.people.personality.dataaccess.entity.Person;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * The {@code PersonRepository} interface provides the methods for retrieving
 * active person id's from the DB.
 * 
 * <AUTHOR>
 */
@Repository
@Transactional
public interface PersonRepository extends JpaRepository<Person, Long> {

	/**
	 * This will return the list of active person id's.
	 * 
	 * @return {@link Long} list of person id's
	 */
	@Query(value = "select distinct p.personid from Person p, PersonStatus ps where p.personid=ps.personid and (ps.employeeStatusId =1 or ps.userAccountStatusId=1)")
	List<Long> getPersonIds();


	@Query(nativeQuery = true,value = "select  personid  from (select distinct p.personid, u.is_api_only_named_usr_sw  from Person p inner join PersonStatusmm ps on p.personid=ps.personid left outer join useraccount u on p.personid  = u.personid where  (ps.employmentstatid  =1 or ps.useracctstatid =1) ) t order by t.is_api_only_named_usr_sw desc , t.personid")
	List<Long> getPersonIdspreferred();

	@Query(value = "select distinct p.personid from Person p, PersonStatus ps where p.personid=ps.personid and (ps.employeeStatusId =1 or ps.userAccountStatusId=1) and (ps.employeeStatusId != 3) " +
			"and ps.effectivedtm <= :effectivedtm and ps.expirationdtm > :expirationdtm and p.personid > 0")
	List<Long> getPersonIdsAsOfToday(@Param("effectivedtm")LocalDateTime effectivedtm, @Param("expirationdtm")LocalDateTime expirationdtm);

	/**
	 * Returns list of person IDs by pay ryle ID.
	 * @param payRuleId pay rule ID.
	 * @return list of person IDs related to specified pay rule. 
	 */
	@Query(value = "select p.personid from person p join wtkemployee w on w.personid = p.personid where w.payruleid = :payRuleId",
	        nativeQuery = true)
	List<BigInteger> getPersonIdsByPayruleId(@Param("payRuleId") Long payRuleId);
	
	
	/**
	 * Returns value of is_multi_person property of person.
	 * @return true is person were assigned multiple positions at least once.
	 */
	@Query(value = "select p.is_multi_position from person p where p.personid = :personId",
	        nativeQuery = true)
	Integer isMultiPositionEmployee(@Param("personId") Long personId);

	/**
	 * Returns list of person IDs by org node ids.
	 * @param list of orgNodeIds.
	 * @param limit integer value.
	 * @return list of person IDs related to specified org node id
	 */
	@Query(nativeQuery = true, value = "select distinct home.employeeid, home.primaryorgidsid, person.personnum, person.fullnm from" +
			" (select employeeid, primaryorgidsid, row_number() over(partition by primaryorgidsid) as rn from homeaccthist) as home" +
			" JOIN person ON home.employeeid = person.personid" +
			" where home.primaryorgidsid in (:orgNodeIds) and rn <= :limit")
	List<Object[]> findByOrgNodeIds(@Param("orgNodeIds") List<Long> orgNodeIds, @Param("limit") int limit);

	@Query(nativeQuery = true, value = "select distinct p.personid from Person p, personstatusmm ps where p.personid=ps.personid and (ps.employmentstatid =1 or ps.useracctstatid=1) and (case when :moduloRemainder = 0 then p.personid%:moduloFactor <= 0 else p.personid%:moduloFactor = :moduloRemainder end)")
	List<Long> getPersonIds(@Param("moduloRemainder") Integer moduloRemainder,@Param("moduloFactor") Integer moduloFactor);

}
