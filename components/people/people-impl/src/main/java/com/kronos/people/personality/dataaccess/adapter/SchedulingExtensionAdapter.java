/***********************************************************************
 * SchedulingExtensionAdapter.java
 * 
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.adapter;

import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.model.extension.SchedulingExtension;
import com.kronos.people.personality.model.extension.entry.*;
import com.kronos.wfc.commonapp.namedentity.business.dap.DapAssignment;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignment;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignmentDetails;
import com.kronos.wfc.commonapp.people.business.person.AccessAssignment;
import com.kronos.wfc.commonapp.people.business.person.Approver;
import com.kronos.wfc.commonapp.people.business.person.ExpectedHours;
import com.kronos.wfc.commonapp.people.business.person.ExpectedHoursSet;
import com.kronos.wfc.commonapp.people.business.person.PersonAccessAssignment;
import com.kronos.wfc.commonapp.people.business.person.group.PersonGroupAssignment;
import com.kronos.wfc.commonapp.people.business.person.group.PersonGroupAssignmentSet;
import com.kronos.wfc.commonapp.people.business.person.predsched.PredictiveSchedulingEligibilityRecord;
import com.kronos.wfc.commonapp.people.business.person.predsched.PredictiveSchedulingEligibilityRecordsManager;
import com.kronos.wfc.commonapp.people.business.person.predschedoverride.PredictiveSchedulingOverrideRecord;
import com.kronos.wfc.commonapp.people.business.person.predschedoverride.PredictiveSchedulingOverrideRecordsManager;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.types.business.TimePeriodType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KConstants;
import com.kronos.wfc.platform.utility.framework.datetime.KTimeDuration;
import com.kronos.wfc.scheduling.core.business.EffectiveDatedList;
import com.kronos.wfc.scheduling.core.business.EffectiveDatedObject;
import com.kronos.wfc.scheduling.core.business.SchedEmployeeService;
import com.kronos.wfc.scheduling.core.shared.Pair;
import com.kronos.wfc.scheduling.request.business.setup.paycodeprofile.PayCodeProfile;
import com.kronos.wfc.scheduling.request.business.setup.paycodeprofile.PayCodeProfileAssignmentService;
import com.kronos.wfc.scheduling.selfscheduling.business.people.SseAccessAssignment;
import com.kronos.wfc.scheduling.selfscheduling.business.people.SseAccessAssignmentFactory;
import com.kronos.wfc.scheduling.selfscheduling.business.people.SseAccessAssignmentGuest;
import com.kronos.wfc.scheduling.workload.business.zonecategory.PreferredZoneCategoryService;
import com.kronos.wfc.scheduling.workload.business.zonecategory.ZoneCategory;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.kronos.people.personality.dataaccess.adapter.AdapterHelper.isNullNotPresent;
import static com.kronos.people.personality.dataaccess.adapter.AdapterHelper.setIfNotNull;

/**
 * The {@code SchedulingExtensionAdapter} class populates the scheduling
 * extension from legacy personality.
 * 
 * <AUTHOR>
 *
 */
@Named
public class SchedulingExtensionAdapter implements ExtensionAdapter<SchedulingExtension> {
	
	/**
	 * Instance of {@link AdapterHelper}. It provides the utility methods.
	 */
	@Inject
	private AdapterHelper adapterHelper;
	
	@Inject
	private PersonalityFacade personalityFacade;

	/*
	 * (non-Javadoc) This method returns SchedulingExtension based on provided
	 * personId
	 * 
	 * @see
	 * com.kronos.people.personality.service.impl.dataaccess.legacy.IConverter
	 * #convert(java.lang.Long)
	 */
	/**
	 * {@inheritDoc}
	 */
   @Override
   public SchedulingExtension convert(Personality personality, LocalDate snapShotDate) {
      AccessAssignment accessAssignment = personality.getAccessAssignment();
      SchedulingExtension schedulingExtension = new SchedulingExtension();

      setPersonAttributes(personality, schedulingExtension);
      setExpectedHoursAttributes(personality, schedulingExtension);

      setIfNotNull(personality.getJobAssignment(), schedulingExtension, this::setJobAssignmentAttributes);
      setAccessAssignmentAttributes(accessAssignment, schedulingExtension);
		setSelfScheduleEmployeeShiftTemplateProfile(schedulingExtension, personality);//also access assignment
      if (isNullNotPresent(personality::getPersonAccessAssignmentSet)) {
         setIfNotNull(personality.getPersonAccessAssignmentSet().getAllPersonAccessAssignments(), schedulingExtension, this::setJobTransferSet);
      }
      setIfNotNull(personality.getApprovers(), schedulingExtension, (schedExt, approversSet) -> setIfNotNull(approversSet.collection(), schedExt, this::setApprover));
      setPredictiveSchedulingEligibilityEntries(schedulingExtension, personality);
	   setPredictiveSchedulingOverrideEntries(schedulingExtension, personality);
		setSkillList(schedulingExtension, personality);
		setCertificationList(schedulingExtension, personality);
		setPreferredZoneCategory(schedulingExtension, personality);
		setPayCodeProfile(schedulingExtension, personality);
      return schedulingExtension;
   }

	@Override
	public SchedulingExtension createSnapshot(SchedulingExtension extension) {
		return Optional.ofNullable(extension).map(SchedulingExtension::new).orElse(null);
	}

	/**
	 * It sets approver parameter of {@link SchedulingExtension} instance from list of {@link Approver}.
	 * @param schedulingExtention {@link SchedulingExtension} instance
	 * @param approvers List of {@link Approver}
	 */
	void setApprover(SchedulingExtension schedulingExtention, List<Approver> approvers) {
		schedulingExtention.setApprovers(getAdapterHelper().convertLegacyCollection(approvers, this::getApproverDataEntry));
	}
	
	/**
	 * It sets approver data entries in {@link ApproverEntry} instance from {@link Approver} instance.
	 * @param approver {@link Approver} instance containing MgrId, due date amount, order num, version count
	 * @return {@link ApproverEntry} instance 
	 */
	protected ApproverEntry getApproverDataEntry(Approver approver) {
		ApproverEntry approversEntry = new ApproverEntry();
		adapterHelper.setLongFromObjectIdLong(approversEntry::setEmployeeId, approver.getMgrId());
		approversEntry.setDueDateAmt(approver.getDueDateAmount());
		approversEntry.setOrderNum(approver.getOrderNum());
		approversEntry.setVersionCount(approver.getVersionCount());
		return approversEntry;
	}
    
	/**
	 * This method sets person sttributes in {@link SchedulingExtension} instance 
	 * from {@link Personality} instance.
	 * 
	 * @param personality {@link Personality} instance
	 * @param schedulingExtension {@link SchedulingExtension} instance
	 */
	protected void setPersonAttributes(Personality personality, SchedulingExtension schedulingExtension) {
		schedulingExtension.setPersonId(getAdapterHelper().getLongFromObjectId(personality.getPersonId()));
		schedulingExtension.setPersonNumber(personality.getPersonNumber());
		schedulingExtension.setActive(personality.isActive());
		schedulingExtension.setSchoolCalendarDapId(getSchoolCalendarDapId(personality));
		schedulingExtension.setForecastingCategoryProfileId(getForecastMapDapId(personality));
	}

	/**
	 * This method sets expectedHrs attributes in {@link SchedulingExtension} instance
	 * from {@link Personality} instance. 
	 * 
	 * @param personality {@link Personality} instance for populating the value of expectedHrs attributes  
	 * @param schedulingExtension {@link SchedulingExtension} instance
	 */
	protected void setExpectedHoursAttributes(Personality personality, SchedulingExtension schedulingExtension) {

		ExpectedHoursSet expectedHoursSet = personality.getExpectedHours();
		ExpectedHours expHoursDaily = null;
		ExpectedHours expHoursWeekly = null;
		ExpectedHours expHoursPayPeriod = null;

		if (expectedHoursSet != null) {
			expHoursDaily = expectedHoursSet.getExpectedHours(TimePeriodType.DAILY);
			expHoursWeekly = expectedHoursSet.getExpectedHours(TimePeriodType.WEEKLY);
			expHoursPayPeriod = expectedHoursSet.getExpectedHours(TimePeriodType.PAY_PERIOD);
		}
		if (expHoursDaily != null)
			schedulingExtension.setExpectedDailyHours(convertExpectedHoursToDouble(expHoursDaily));
		if (expHoursWeekly != null)
			schedulingExtension.setExpectedWeeklyHours(convertExpectedHoursToDouble(expHoursWeekly));
		if (expHoursPayPeriod != null)
			schedulingExtension.setExpectedByPayPeriodHours(convertExpectedHoursToDouble(expHoursPayPeriod));
	}

	/**
	 * Sets values to {@link SchedulingExtension} instance parameters from {@link JobAssignment} instance.  
	 * @param schedulingExtension {@link SchedulingExtension} instance
	 * @param jobAssignment {@link JobAssignment} instance
	 */
	protected void setJobAssignmentAttributes(SchedulingExtension schedulingExtension, JobAssignment jobAssignment) {
		setJobAssignmentSwitchEntry(schedulingExtension, jobAssignment);
		JobAssignmentDetails jobAssignmentDetails = jobAssignment.getJobAssignmentDetails();
		if (isNullNotPresent(() -> jobAssignmentDetails)) {
			setIfNotNull(jobAssignmentDetails.getWorkerType(), schedulingExtension, (t, u) -> {
				t.setWorkerType(u.getWorkerTypeName());
				t.setWorkerTypeId(getAdapterHelper().getLongFromObjectIdLong(u.getWorkerTypeId()));
			});
		}
		setIfNotNull(jobAssignment.getScheduleGroupAssignmentSet(), schedulingExtension, this::convertEffDatedGroup);
	}

	/** 
	 * This method sets {@link JobAssignmentSwitchEntry} parameter of {@link SchedulingExtension}
	 *  from {@link JobAssignment} instance.
	 *  
	 * @param schedulingExtension {@link SchedulingExtension} instance
	 * @param jobAssignment {@link JobAssignment} instance
	 */
	protected void setJobAssignmentSwitchEntry(SchedulingExtension schedulingExtension, JobAssignment jobAssignment) {
		JobAssignmentSwitchEntry jobAssignmentEntry=new JobAssignmentSwitchEntry();
		jobAssignmentEntry.setAssignPersonOvertimeSwitch(jobAssignment.getHasPersonalOvertimeAssignmentSwitch());
		jobAssignmentEntry.setDeletedSwitch(jobAssignment.getDeletedSwitch());
		jobAssignmentEntry.setUseMASwitch(jobAssignment.getUseMASwitch());
		jobAssignmentEntry.setVersionCount(jobAssignment.getVersionCount());
		schedulingExtension.setJobAssignmentSwitchEntry(jobAssignmentEntry);
	}
	
	/**
	 * This method sets AccessAssingnment parameters into {@link SchedulingExtension} instance from 
	 * {@link AccessAssignment} intsance.
	 * 
	 * @param accessAssignment {@link AccessAssignment} instance
	 * @param schedulingExtension {@link SchedulingExtension} instance
	 */
	protected void setAccessAssignmentAttributes(AccessAssignment accessAssignment, SchedulingExtension schedulingExtension) {
		if (accessAssignment == null)
			return;
		schedulingExtension.setGroupScheduleId(getAdapterHelper().getLongFromObjectIdLong(accessAssignment.getGroupScheduleId()));
		schedulingExtension.setShiftCodeId(getAdapterHelper().getLongFromObjectIdLong(accessAssignment.getShiftCodeId()));
		schedulingExtension.setSchedulePatternId(getAdapterHelper().getLongFromObjectIdLong(accessAssignment.getSchedulePatternId()));
		schedulingExtension.setAvailabilityPatternId(getAdapterHelper().getLongFromObjectIdLong(accessAssignment.getAvailabilityPatternId()));
		schedulingExtension.setHyperfindProfileId(getAdapterHelper().getLongFromObjectIdLong(accessAssignment.getHyperFindProfileId()));
		schedulingExtension.setManagerAccessSetId(getAdapterHelper().getLongFromObjectIdLong(accessAssignment.getManagerAccessSetId()));

	}

	/**
	 * This method gets the school calendar dap id by {@code personality}.
	 * 
	 * @param personality
	 *            {@link Personality}, the personality is a combination of the
	 *            person demographics, user, and job assignment information.
	 * @return school calender dap id
	 */
	protected Long getSchoolCalendarDapId(Personality personality) {
		DapAssignment dapAssignment = getPersonalityFacade().getDapAssignment(personality);
		return dapAssignment != null ? getAdapterHelper().getLongFromObjectIdLong(dapAssignment.getSchoolCalendarDapId()) : null;
	}

	/**
	 * This method converts the dated group entries to effective dated
	 * collection of {@code GroupAssignmentEntry} by {@code jobAssign}.
	 * 
	 * @param se
	 *            SchedulingExtension
	 * @param personGroupAssignmentSet
	 *            PersonGroupAssugbmentSet
	 */
	protected void convertEffDatedGroup(SchedulingExtension se, PersonGroupAssignmentSet personGroupAssignmentSet) {
		se.setEffDatedGroupAssignment(new EffectiveDatedCollection<GroupAssignmentEntry>(getDatedGroupEntries(personGroupAssignmentSet)));
	}

	/**
	 * This method gets the dated group entries by {@code personGroupAssignSet}.
	 * 
	 * @param personGroupAssignSet
	 *            {@link PersonGroupAssignmentSet}, This is a set of
	 *            PersonGroupAssignment objects.
	 * @return collection of {@link GroupAssignmentEntry}.
	 */
	@SuppressWarnings({ "unchecked" })
	protected Collection<GroupAssignmentEntry> getDatedGroupEntries(PersonGroupAssignmentSet personGroupAssignSet) {
		return getAdapterHelper().convertLegacyCollection((Collection<PersonGroupAssignment>) personGroupAssignSet.getAllPersonGroupAssignments(),
				legacyGroupAssign -> getDatedGroupEntry(legacyGroupAssign));
	}

	/**
	 * This method gets the dated group entry by {@code personGroupAssign}.
	 * 
	 * @param personGroupAssign
	 *            {@link PersonGroupAssignment}, A group is assigned to a person
	 *            with effective dates.
	 * @return instance of {@link GroupAssignmentEntry}.
	 */
	@SuppressWarnings({ "deprecation" })
	protected GroupAssignmentEntry getDatedGroupEntry(PersonGroupAssignment personGroupAssign) {
		GroupAssignmentEntry groupAssignmentEntry = new GroupAssignmentEntry();
		groupAssignmentEntry.setEffectiveDate(getAdapterHelper().kDateToLocalDate(personGroupAssign.getEffectiveDate()));
		groupAssignmentEntry.setExpirationDate(getAdapterHelper().kDateToLocalDate(personGroupAssign.getExpirationDate()));
		groupAssignmentEntry.setGroupId(getAdapterHelper().getLongFromObjectIdLong(personGroupAssign.getGroupId()));
		return groupAssignmentEntry;
	}

	/**
	 * This method convert the expected hours to {@code Double}.
	 * 
	 * @param expectedHours
	 *            {@link ExpectedHours}, The expected hours define the amount to
	 *            associate with the person.
	 * @return expected hours in {@link Double}.
	 */
	protected Double convertExpectedHoursToDouble(ExpectedHours expectedHours) {
		Double expHours = null;
		KTimeDuration kTimeDuration = new KTimeDuration();

		if (expectedHours != null && expectedHours.getExpectedHoursQuantity() != null) {
			expHours = Double.valueOf(kTimeDuration.getIn(KConstants.HOURS));
			kTimeDuration = new KTimeDuration(expectedHours.getExpectedHoursQuantity().doubleValue(), KConstants.HOURS);
			expHours = Double.valueOf(kTimeDuration.getIn(KConstants.HOURS));
		}
		return expHours;
	}

	/**
	 * This method populates the list of {@code JobTransferEntry} by
	 * {@code extension} and {@code personality}.
	 * 
	 * @param extension
	 *            {@link SchedulingExtension}.
	 * @param pAccessAssignments
	 *            list of {@link PersonAccessAssignment} instances.
	 */
	protected void setJobTransferSet(SchedulingExtension extension, List<PersonAccessAssignment> pAccessAssignments) {
		Collection<JobTransferEntry> jobTransferEntries = getAdapterHelper().convertLegacyCollection(pAccessAssignments, this::getJobTransferEntry);
		extension.setJobTransfer(new EffectiveDatedCollection<JobTransferEntry>(jobTransferEntries));
	}

	/**
	 * This method returns {@link JobTransferEntry} instance after setting the values from
	 * {@link PersonAccessAssignment} instance. 
	 *  
	 * @param personAccessAssignment {@link PersonAccessAssignment} instance
	 * @return {@link JobTransferEntry} instance
	 */
	@SuppressWarnings({ "deprecation" })
	public JobTransferEntry getJobTransferEntry(PersonAccessAssignment personAccessAssignment) {
		JobTransferEntry jobTransferEntry = new JobTransferEntry();
		jobTransferEntry.setEffectiveDate(getAdapterHelper().kDateToLocalDate(personAccessAssignment.getEffectiveDate()));
		jobTransferEntry.setExpirationDate(getAdapterHelper().kDateToLocalDate(personAccessAssignment.getExpirationDate()));
		
		adapterHelper.setIfObjectIdLongNotNull(personAccessAssignment.getManagerTransferOrganizationSetId(), jobTransferEntry, (jobTransferEntryAlias,managerTransferOrganizationSetId)->
			setIfNotNull(personalityFacade.findOrgMapGroupById(managerTransferOrganizationSetId), jobTransferEntryAlias, (jobTrfrEntry,orgMap)->
			jobTrfrEntry.setJobTransferSet(orgMap.getName()))
		);
        jobTransferEntry.setManagerAccessOrganizationSetId(getAdapterHelper().getLongFromObjectIdLong(personAccessAssignment.getManagerAccessOrganizationSetId()));
        jobTransferEntry.setManagerEmployeeGroupId(getAdapterHelper().getLongFromObjectIdLong(personAccessAssignment.getEmployeeGroupId()));
        jobTransferEntry.setHomeHyperFindQueryId(getAdapterHelper().getLongFromObjectIdLong(personAccessAssignment.getHomeHyperFindQueryId()));
		jobTransferEntry.setManagerTransferOrganizationSetId(getAdapterHelper().getLongFromObjectIdLong(personAccessAssignment.getManagerTransferOrganizationSetId()));
		jobTransferEntry.setProfessionalTransferOrganizationSetId(getAdapterHelper().getLongFromObjectIdLong(personAccessAssignment.getProfessionalTransferOrganizationSetId()));
		jobTransferEntry.setEmpMgrTransferOrganizationSetId(getAdapterHelper().getLongFromObjectIdLong(personAccessAssignment.getEmpMgrTransferOrganizationSetId()));

		return jobTransferEntry;
	}
	
    /**
     * This method gets the forecast map dap id by {@code personality}.
     * 
     * @param personality
     *            {@link Personality}, the personality is a combination of the person demographics, user, and job
     *            assignment information.
     * @return forecast map dap id
     */
    protected Long getForecastMapDapId(Personality personality) {
        final DapAssignment assignment = getPersonalityFacade().getDapAssignment(personality);
		if (assignment != null && assignment.getForecastMapDapId() != null) {
			return getAdapterHelper().getLongFromObjectIdLong(assignment.getForecastMapDapId());
		} else if (personality.getDapAssignment() != null && !personality.getDapAssignment().getForecastMapDapId().isNull()) {
			return personality.getDapAssignment().getForecastMapDapId().longValue();
		}
		return null;
    }

   private void setPredictiveSchedulingEligibilityEntries(SchedulingExtension se, Personality personality) {
      PredictiveSchedulingEligibilityRecordsManager eligibilityRecordsManager = PredictiveSchedulingEligibilityRecordsManager.getInstance(personality);
      List<PredictiveSchedulingEligibilityRecord> eligibilityRecords = eligibilityRecordsManager.getAllEligibilityRecords();
      Collection<PredictiveSchedulingEligibilityEntry> eligibilityEntries = getAdapterHelper().convertLegacyCollection(eligibilityRecords, this::convertPredictiveEligibilityRecord);
      se.setPredictiveSchedulingEligibilityEntries(new EffectiveDatedCollection<>(eligibilityEntries));
   }

   private PredictiveSchedulingEligibilityEntry convertPredictiveEligibilityRecord(PredictiveSchedulingEligibilityRecord record) {
      PredictiveSchedulingEligibilityEntry entry = new PredictiveSchedulingEligibilityEntry();
      entry.setIsEligible(record.getIsEligibile());
      entry.setEffectiveDate(getAdapterHelper().kDateToLocalDate(record.getEffectiveDate()));
      entry.setExpirationDate(getAdapterHelper().kDateToLocalDate(record.getExpirationDate()));
      return entry;
   }


	private void setPredictiveSchedulingOverrideEntries(SchedulingExtension se, Personality personality) {
		PredictiveSchedulingOverrideRecordsManager schedulingOverrideRecordsManager = PredictiveSchedulingOverrideRecordsManager.getInstance(personality);
		List<PredictiveSchedulingOverrideRecord> predictiveScheduleOverrideRecords = schedulingOverrideRecordsManager.getAllOverrideRecords();
		Collection<PredictiveSchedulingOverrideEntry> predictiveScheduleOverrideEntries = getAdapterHelper().convertLegacyCollection(predictiveScheduleOverrideRecords, this::convertPredictiveSchedulingOverrideRecord);
		se.setPredictiveSchedulingOverrideEntries(new EffectiveDatedCollection<>(predictiveScheduleOverrideEntries));
	}

	private PredictiveSchedulingOverrideEntry convertPredictiveSchedulingOverrideRecord(PredictiveSchedulingOverrideRecord record) {
		PredictiveSchedulingOverrideEntry entry = new PredictiveSchedulingOverrideEntry();
		entry.setSchedulueOverrideId(record.getPredictiveScheduleOverrideId().longValue());
		entry.setEffectiveDate(getAdapterHelper().kDateToLocalDate(record.getEffectiveDate()));
		entry.setExpirationDate(getAdapterHelper().kDateToLocalDate(record.getExpirationDate()));
		return entry;
	}

	/**
	 * This method sets the self schedule employee shift template profile attribute in {@link SchedulingExtension} instance
	 * from {@link Personality} instance.
	 *
	 * @param personality {@link Personality} instance
	 * @param schedulingExtension {@link SchedulingExtension} instance
	 */
	protected void setSelfScheduleEmployeeShiftTemplateProfile(SchedulingExtension schedulingExtension, Personality personality) {
		 SseAccessAssignment sseAccessAssignment = SseAccessAssignmentFactory.getInstance().createSseAccessAssignment(personality.getPersonId());
		 schedulingExtension.setSseShiftCodeId(getAdapterHelper().getLongFromObjectIdLong(sseAccessAssignment.getSseShiftCodeId()));
	}

	protected void setSkillList(SchedulingExtension schedulingExtension, Personality personality) {
		EffectiveDatedList skillList = SchedEmployeeService.getInstance().getEmployeeSkillFromDBById(personality.getPersonId());
		schedulingExtension.setSkillList(convertCertificationSkillFromEffectiveDatedList(skillList));
	}

	protected void setCertificationList(SchedulingExtension schedulingExtension, Personality personality) {
		EffectiveDatedList certificationList = SchedEmployeeService.getInstance().getEmployeeCertificationFromDBById(personality.getPersonId());
		schedulingExtension.setCertificationList(convertCertificationSkillFromEffectiveDatedList(certificationList));
	}

	protected void setPreferredZoneCategory(SchedulingExtension schedulingExtension, Personality personality) {
		ZoneCategory zoneCategory = PreferredZoneCategoryService.getInstance().retrievePreferredZoneCategoryByEmployeeId(personality.getPersonId());
		schedulingExtension.setPreferredZoneCategory(zoneCategory == null || zoneCategory.getId() == null ? null : zoneCategory.getId().toLong());
	}

	protected void setPayCodeProfile(SchedulingExtension schedulingExtension, Personality personality) {
		PayCodeProfile payCodeProfile = PayCodeProfileAssignmentService.getInstance().retrievePayCodeProfileByEmployeeId(personality.getPersonId());
		schedulingExtension.setPayCodeProfile(payCodeProfile == null || payCodeProfile.getId() == null ? null : payCodeProfile.getId().toLong());
	}

	private EffectiveDatedCollection<CertSkillEntry> convertCertificationSkillFromEffectiveDatedList(EffectiveDatedList certOrSkillList) {
		Collection<CertSkillEntry> certSkillEntries = new ArrayList<>();
		if (certOrSkillList != null) {
			Collection<EffectiveDatedObject> effectiveDatedObjects = certOrSkillList.getEffectiveDatedObjectList();
			certSkillEntries = getAdapterHelper().convertLegacyCollection(effectiveDatedObjects, this::convertCertificationSkillFromEffectiveDatedObject);
		}
		return new EffectiveDatedCollection<>(certSkillEntries);
	}

	private CertSkillEntry convertCertificationSkillFromEffectiveDatedObject(EffectiveDatedObject skillOrCertLevelEDO) {
		Pair<ObjectIdLong, ObjectIdLong> skillOrCertLevelPair = (Pair<ObjectIdLong, ObjectIdLong>) (skillOrCertLevelEDO.getValue());
		Long certSkillId = skillOrCertLevelPair.getFirstEntity().toLong();
		Long certSkillProficiencyId = skillOrCertLevelPair.getSecondEntity().toLong();
		LocalDate effectiveDate = getAdapterHelper().kDateToLocalDate(skillOrCertLevelEDO.getEffectiveDate());
		LocalDate expirationDate = getAdapterHelper().kDateToLocalDate(skillOrCertLevelEDO.getExpirationDate());
		return new CertSkillEntry(effectiveDate, expirationDate, certSkillId, certSkillProficiencyId);
	}

	/** 
	 * Returns {@link AdapterHelper} instance.
	 * 
	 * @return {@link AdapterHelper} instance
	 */
	public AdapterHelper getAdapterHelper() {
		return adapterHelper;
	}

	/**
	 * Sets {@link AdapterHelper} instance.
	 * 
	 * @param adapterHelper {@link AdapterHelper} instance
	 */
	public void setAdapterHelper(AdapterHelper adapterHelper) {
		this.adapterHelper = adapterHelper;
	}

	/**
	 * @return {@link PersonalityFacade} instance
	 */
	public PersonalityFacade getPersonalityFacade() {
		return personalityFacade;
	}

	/**
	 * Sets {@link PersonalityFacade} instance.
	 * 
	 * @param personalityFacade {@link PersonalityFacade} instance
	 */
	public void setPersonalityFacade(PersonalityFacade personalityFacade) {
		this.personalityFacade = personalityFacade;
	}

}