package com.kronos.people.personality.converter;

import java.time.LocalDate;
import java.time.LocalDateTime;

import java.util.Map;
import java.util.Optional;

import jakarta.inject.Inject;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.timekeeping.service.attestation.api.entity.AttestationProfile;
import com.kronos.wfc.commonapp.people.business.person.AssignAttestationProfile;
import com.kronos.wfc.platform.utility.framework.datetime.KDateTime;
import org.springframework.stereotype.Component;

import com.kronos.commonapp.orgmap.common.api.ISystemProperties;
import com.kronos.people.personality.dataaccess.entity.AttestationProfileAssignment;
import com.kronos.timekeeping.service.attestation.api.converter.AttestationProfileConverter;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileAssignmentDTO;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileDTO;

@Component
public class AttestationProfileAssignmentConverter {

    private final ISystemProperties systemProperties;
    private AttestationProfileConverter attestationProfileConverter;
    private AdapterHelper adapterHelper;

    @Inject
    public AttestationProfileAssignmentConverter(
        ISystemProperties systemProperties, AdapterHelper adapterHelper) {
        this.systemProperties = systemProperties;
        this.adapterHelper = adapterHelper;
    }

    public com.kronos.people.personality.model.AttestationProfileAssignmentDTO convertEntityToDto(AssignAttestationProfile assignAttestationProfile, ObjectRef profile) {
        com.kronos.people.personality.model.AttestationProfileAssignmentDTO attestationProfileAssignmentDTO = new com.kronos.people.personality.model.AttestationProfileAssignmentDTO();
        attestationProfileAssignmentDTO.setEffectiveDate(
                adapterHelper.kDateToLocalDate(new KDateTime(assignAttestationProfile.getEffectiveDate())));
        attestationProfileAssignmentDTO.setExpirationDate(
                adapterHelper.kDateToLocalDate(new KDateTime(assignAttestationProfile.getExpirationDate())));
        attestationProfileAssignmentDTO.setProfile(profile);
        return attestationProfileAssignmentDTO;
    }

    public AttestationProfileAssignmentDTO convertEntityToDto(AssignAttestationProfile assignAttestationProfile, AttestationProfile profile) {
        AttestationProfileAssignmentDTO attestationProfileAssignmentDTO = new AttestationProfileAssignmentDTO();
        attestationProfileAssignmentDTO.setEffectiveDate(
                adapterHelper.kDateTimeToLocalDateTime(new KDateTime(assignAttestationProfile.getEffectiveDate())));
        attestationProfileAssignmentDTO.setExpirationDate(
                adapterHelper.kDateTimeToLocalDateTime(new KDateTime(assignAttestationProfile.getExpirationDate())));

        AttestationProfileDTO profileDTO = new AttestationProfileDTO();
        profileDTO.setId(profile.getId());
        profileDTO.setName(profile.getName());

        attestationProfileAssignmentDTO.setAttestationProfile(profileDTO);
        attestationProfileAssignmentDTO.setAssignToManagerRole(assignAttestationProfile.getIsManagerRole().get());
        return attestationProfileAssignmentDTO;
    }

    public AttestationProfileAssignmentDTO convertEntityToDto(
        AttestationProfileAssignment attestationProfileAssignment, Map<Long, AttestationProfileDTO> profilesMap) {
        return convertEntityToDto(attestationProfileAssignment, profilesMap == null ?
            null : profilesMap.get(attestationProfileAssignment.getProfileId()));
    }

    public AttestationProfileAssignmentDTO convertEntityToDto(
        AttestationProfileAssignment attestationProfileAssignment, AttestationProfileDTO profile) {
        AttestationProfileAssignmentDTO attestationProfileAssignmentDTO = null;

        if (attestationProfileAssignment != null) {
            attestationProfileAssignmentDTO = convertEntityToDtoWithoutProfile(attestationProfileAssignment);
            AttestationProfileDTO profileDTO = profile == null ?
                attestationProfileConverter.convertEntityToDto(attestationProfileAssignment.getProfile()) :
                profile;

            attestationProfileAssignmentDTO.setAttestationProfile(profileDTO);
        }

        return attestationProfileAssignmentDTO;
    }

    private AttestationProfileAssignmentDTO convertEntityToDtoWithoutProfile(
        AttestationProfileAssignment attestationProfileAssignment) {
        AttestationProfileAssignmentDTO attestationProfileAssignmentDTO = new AttestationProfileAssignmentDTO();
        attestationProfileAssignmentDTO.setId(attestationProfileAssignment.getId());
        attestationProfileAssignmentDTO.setEffectiveDate(attestationProfileAssignment.getEffectiveDate());
        attestationProfileAssignmentDTO.setExpirationDate(attestationProfileAssignment.getExpirationDate());
        attestationProfileAssignmentDTO.setUpdatedOn(attestationProfileAssignment.getUpdatedOn());
        attestationProfileAssignmentDTO.setUpdatedUserId(attestationProfileAssignment.getUpdatedUserId());
        attestationProfileAssignmentDTO.setVersionCount(attestationProfileAssignment.getVersion());
        attestationProfileAssignmentDTO.setAssignToManagerRole(attestationProfileAssignment.getManagerRole());

        return attestationProfileAssignmentDTO;
    }

    /**
     * Sets attestationProfileConverter. Initializes through setter in order to avoid possible circular references
     *
     * @param attestationProfileConverter attestation profile converter
     */
    @Inject
    public void setAttestationProfileConverter(
        AttestationProfileConverter attestationProfileConverter) {
        this.attestationProfileConverter = attestationProfileConverter;
    }



    public AttestationProfileAssignment convertToAttestationProfileAssignment(com.kronos.people.personality.model.AttestationProfileAssignmentDTO attestationProfileAssignmentDTO, Long currentUserAccountId, AttestationProfile profile) {
        AttestationProfileAssignment attestationProfileAssignment = new AttestationProfileAssignment();
        attestationProfileAssignment.setProfile(profile);
        attestationProfileAssignment.setEffectiveDate(Optional.ofNullable(attestationProfileAssignmentDTO.getEffectiveDate())
                .map(LocalDate::atStartOfDay)
                .orElse(null));
        final LocalDateTime experationDate = Optional.ofNullable(attestationProfileAssignmentDTO.getExpirationDate())
                .map(LocalDate::atStartOfDay)
                .orElse(systemProperties.getEndOfTime().atStartOfDay());
        attestationProfileAssignment.setExpirationDate(experationDate);
        attestationProfileAssignment.setUpdatedUserId(currentUserAccountId);
        attestationProfileAssignment.setUpdatedOn(LocalDateTime.now());
        return attestationProfileAssignment;
    }
}
