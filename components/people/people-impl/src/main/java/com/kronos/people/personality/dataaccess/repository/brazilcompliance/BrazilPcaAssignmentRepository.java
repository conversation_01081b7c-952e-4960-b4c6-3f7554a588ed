package com.kronos.people.personality.dataaccess.repository.brazilcompliance;

import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilPcaAssignmentEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface BrazilPcaAssignmentRepository extends JpaRepository<BrazilPcaAssignmentEntity, Long> {

    /**
     * Returns all Brazil employee Pay code attribute assignments by the given person  ids.
     *
     * @param personIds the person ids
     * @return List of BrcPcaAssignmentEntity
     */
    @Query("SELECT brcpcaassignment FROM BrazilPcaAssignmentEntity brcpcaassignment WHERE brcpcaassignment.personId IN :personIds ORDER BY brcpcaassignment.effectiveDate ASC")
    List<BrazilPcaAssignmentEntity> findByPersonIds(@Param("personIds") List<Long> personIds);

}