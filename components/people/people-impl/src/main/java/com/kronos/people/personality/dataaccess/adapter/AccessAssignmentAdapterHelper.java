/***********************************************************************
 * AccessAssignmentAdapterHelper.java
 * 
 * Copyright 2016, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.adapter;

import static com.kronos.people.personality.dataaccess.adapter.AdapterHelper.setIfNotNull;

import java.util.Collection;
import java.util.Map;
import java.util.function.Function;

import jakarta.inject.Named;

import com.kronos.people.personality.dataaccess.legacy.PersonalityConstants;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.TimekeepingExtension;
import com.kronos.people.personality.model.extension.entry.AccessAssignmentDetailsEntry;
import com.kronos.people.personality.model.extension.entry.AssignEffDatedTimeEntry;
import com.kronos.people.personality.model.extension.entry.DataAccessEntry;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.PreferenceProfileEntry;
import com.kronos.wfc.commonapp.people.business.person.AccessAssignment;
import com.kronos.wfc.commonapp.people.business.person.AssignTimeEntry;
import com.kronos.wfc.commonapp.people.business.person.DataAccessGroupAssignment;
import com.kronos.wfc.commonapp.people.business.person.DataAccessGroupAssignmentSet;
import com.kronos.wfc.commonapp.preferences.business.PreferenceProfile;
import com.kronos.wfc.platform.persistence.framework.ObjectId;


/**
 * This is the adapter class for Timekeeping Extension. It provides the methods for
 * populating the timekeeping access assignments properties from personality.
 * 
 * <AUTHOR>
 */


@Named
public class AccessAssignmentAdapterHelper extends BaseHelper{
	
	/**
	 * Utility method for effective dated access assignment converter.
	 */
	Function<AssignTimeEntry, AssignEffDatedTimeEntry> accessAssignmentEffDatedConvertorFunction = (AssignTimeEntry assignTimeEntry) -> {
			AssignEffDatedTimeEntry accessAssignmentEffDated = new AssignEffDatedTimeEntry();
			adapterHelper.setLongFromObjectIdLong(accessAssignmentEffDated::setAssignTimeEntryId, assignTimeEntry.getAssignTimeEntryId());
			adapterHelper.setLongFromObjectIdLong(accessAssignmentEffDated::setTimeEntryTypeId, assignTimeEntry.getTimeEntryType());
		    adapterHelper.setLongFromObjectIdLong(accessAssignmentEffDated::setActivityTrackingStatusId, assignTimeEntry.getActivityTrackingStatusId());
			accessAssignmentEffDated.setVersionCount(assignTimeEntry.getVersionCnt());
			adapterHelper.setDates(accessAssignmentEffDated, assignTimeEntry.getEffectiveDate(), assignTimeEntry.getExpirationDate());
			return accessAssignmentEffDated;	
		
	};
	
	/**
	 * This method sets the access assignments properties.
	 * 
	 * @param extension
	 *            - {@link TimekeepingExtension}.
	 * @param accessAssignment
	 *            - {@link AccessAssignment}, the person access assignment.The
	 *            access assignment defines to actions, data, other people, etc.
	 */	
	protected void setTimeKeepingAccessProperties(TimekeepingExtension extension, AccessAssignment accessAssignment) {
		setTimeKeepingAccessAssignmentDetails(extension, accessAssignment);

		AccessAssignmentDetailsEntry accessAssignmentDetail = new AccessAssignmentDetailsEntry();
		accessAssignmentDetail.setCanTransfer(accessAssignment.getTransferEmployee());
		accessAssignmentDetail.setCanApproveOvertime(accessAssignment.getApproveOvertime());
		accessAssignmentDetail.setPersonId(extension.getPersonId());
		
		// workRule prolifiles
		setIfNotNull(accessAssignment.getManagerWorkRuleId(), accessAssignmentDetail, (t, u) -> t.setWorkRuleProfileId(u.toLong()));
		setIfNotNull(accessAssignment.getSseWorkRuleId(), accessAssignmentDetail, (t, u) -> t.setSseWorkRuleProfileId(u.toLong()));

		// labor levels
		setIfNotNull(accessAssignment.getManagerLaborCategoryProfileId(), accessAssignmentDetail, (t, u) -> t.setLaborLevelTransfersetId(u.toLong()));
		setIfNotNull(accessAssignment.getEmployeeLaborCategoryProfileId(), accessAssignmentDetail, (t, u) -> t.setSseLaborLevelTransfersetId(u.toLong()));
		setIfNotNull(accessAssignment.getManagerLaborCategoryProfileId(), accessAssignmentDetail, (t, u) -> t.setManagerCategoryLaborProfileId(u.toLong()));
		setIfNotNull(accessAssignment.getEmployeeLaborCategoryProfileId(), accessAssignmentDetail, (t, u) -> t.setEmployeeCategoryLaborProfileId(u.toLong()));
		setIfNotNull(accessAssignment.getMgrEmployeeLaborCategoryProfileId(), accessAssignmentDetail, (t, u) -> t.setMgEmplCategoryLaborProfileId(u.toLong()));

		setIfNotNull(accessAssignment.getReportId(), accessAssignmentDetail, (t, u) -> t.setReportId(u.toLong()));
		// pay codes
		setIfNotNull(accessAssignment.getManagerPayCodeId(), accessAssignmentDetail, (t, u) -> t.setPayCodeProfileId(u.toLong()));
		setIfNotNull(accessAssignment.getManagerViewPayCodeId(), accessAssignmentDetail, (t, u) -> t.setPayCodeViewProfileId(u.toLong()));
		setIfNotNull(accessAssignment.getSsePayCodeId(), accessAssignmentDetail, (t, u) -> t.setSsePayCodeId(u.toLong()));

		extension.setAccessAssignmentDetails(accessAssignmentDetail);
		
	}
	
	/**
	 * This method sets the access assignments effective dated properties.
	 * 
	 * @param extension
	 *            - {@link TimekeepingExtension}.
	 * @param accessAssignment
	 *            - {@link AccessAssignment}, the person access assignment.The
	 *            access assignment defines to actions, data, other people, etc.
	 */
	protected void setTimeKeepingAccessAssignmentDetails(TimekeepingExtension extension, AccessAssignment accessAssignment) {
		setIfNotNull(accessAssignment.getEffectiveDatedTimeEntryMethods(), extension, (t,u)-> {
			Collection<AssignEffDatedTimeEntry> effectiveDatedEntries = 
					adapterHelper.convertLegacyCollection(u.getAllAssignTimeEntries(), accessAssignmentEffDatedConvertorFunction);

			extension.setAccessAssignmentEffDated(new EffectiveDatedCollection<AssignEffDatedTimeEntry>(effectiveDatedEntries));
		});
		
	}
	
		
	/**
	 * This method returns an EffectiveDatedCollection of {@link DataAccessEntry} from {@link DataAccessGroupAssignmentSet} instance.
	 * 
	 * @param dataAccessGroup {@link DataAccessGroupAssignmentSet} instance
	 * @return EffectiveDatedCollection of {@link DataAccessEntry} 
	 */
	@SuppressWarnings("unchecked")
	protected EffectiveDatedCollection<DataAccessEntry> convertEffDatedDataAccess(
			DataAccessGroupAssignmentSet dataAccessGroup) {
		Collection convertLegacyCollection = adapterHelper.convertLegacyCollection(
						(Collection<DataAccessGroupAssignment>)dataAccessGroup.getAllMembers(),
						userAccountStatus -> getDatedDataAccessEntry(userAccountStatus));
		return new EffectiveDatedCollection<DataAccessEntry>(
				convertLegacyCollection);
	}
	
	/**This method sets values to {@link DataAccessEntry} instance from {@link DataAccessGroupAssignmentSet}
	 * instanceand returns it.
	 * 
	 * @param dataAccessAssignment {@link DataAccessGroupAssignmentSet} instance
	 * @return {@link DataAccessEntry} instance
	 */
	protected DataAccessEntry getDatedDataAccessEntry(DataAccessGroupAssignment dataAccessAssignment) {
		DataAccessEntry dataAccessEntry = new DataAccessEntry();
		adapterHelper.setDates(dataAccessEntry,
				dataAccessAssignment.getEffectiveDate(),
				dataAccessAssignment.getExpirationDate());
		setIfNotNull(dataAccessAssignment, dataAccessEntry, (dae, dAccessAssignment) -> {
			dae.setAssignDAGID(adapterHelper.getLongFromObjectId(dAccessAssignment.getAssignDAGId()));
			dae.setdataAccessGroupRoleId(adapterHelper.getLongFromObjectId(dAccessAssignment.getDAGRoleId()));
			dae.setDataAccessGroupId(adapterHelper.getLongFromObjectId(dAccessAssignment.getDataAccessGrpId()));
			dae.setEffectiveDate(adapterHelper.kDateToLocalDate(dAccessAssignment.getEffectiveDate()));
			dae.setExpirationDate(adapterHelper.kDateToLocalDate(dAccessAssignment.getExpirationDate()));
			dae.setPersonID(adapterHelper.getLongFromObjectId(dAccessAssignment.getPersonId()));
			dae.setDefaultSwitch(dAccessAssignment.getDefaultSwitch());
			dae.setVersionCount(dAccessAssignment.getVersionCount());
		});
		return dataAccessEntry;
	}
	
	/**
	 * This method sets the access assignments attributes.
	 * 
	 * @param empExtension
	 *            the employee extension.
	 * @param accessAssignment
	 *            the access assignment.
	 */
	public void setAccessAssignmentAttributes(EmployeeExtension empExtension, AccessAssignment accessAssignment) {
		empExtension.setAccessProfileId(adapterHelper.getLongFromObjectIdLong(accessAssignment.getAccessProfileId()));
		empExtension.setPreferenceProfileId(adapterHelper.getLongFromObjectIdLong(accessAssignment.getPreferenceProfileId()));
		empExtension.setLocaleProfileId(adapterHelper.getLongFromObjectIdLong(accessAssignment.getLocaleProfileId()));
		empExtension.setNotificationProfileId(adapterHelper.getLongFromObjectIdLong(accessAssignment.getNotificationProfileId()));
		empExtension.setAccessMethodProfileId(adapterHelper.getLongFromObjectId(accessAssignment.getAccessMethodProfileId()));
		empExtension.setDelegateProfileId(adapterHelper.getLongFromObjectIdLong(accessAssignment.getDelegateProfileId()));
        empExtension.setApprovalMethodId(adapterHelper.getLongFromObjectIdLong(accessAssignment.getApprovalMethodId()));

		setProcessProfileDetails(empExtension, accessAssignment);
		
		PreferenceProfile preferenceProfile= accessAssignment.getPreferenceProfile();
		
		setIfNotNull(preferenceProfile, empExtension, (employeeExtension, prefProfile) -> 
			preparePreferenceProfileData(accessAssignment, employeeExtension, prefProfile));
	}

	/**
	 * Set process profile details.
	 * @param empExtension Employee extension object in which we have to set attributes
	 * @param accessAssignment AccessAssignment object from which we need to extract the process attributes
	 */
	protected void setProcessProfileDetails(EmployeeExtension empExtension,
			AccessAssignment accessAssignment) {
		Map<String, Object> processProfile =personalityFacade.getProcessManagerProfileName(accessAssignment);
		empExtension.setProcessManagerProfileId(adapterHelper.getLongFromObjectId((ObjectId) processProfile.get(PersonalityConstants.MANAGER_WORKFLOW_PROFILE_ID.getValue())));
		empExtension.setProcessEmployeeProfileId(adapterHelper.getLongFromObjectId((ObjectId) processProfile.get(PersonalityConstants.EMPLOYEE_WORKFLOW_PROFILE_ID.getValue())));
	}
	
	/**
	 * This method prepares Preference Profile Data.
	 * 
	 * @param accessAssignment
	 *        The accessAssignment
	 * @param employeeExtension
	 *        The EmployeeExtension
	 * @param preferenceProfile
	 *        The PreferenceProfile
	 */
	protected void preparePreferenceProfileData(AccessAssignment accessAssignment, EmployeeExtension employeeExtension, PreferenceProfile preferenceProfile) {
		PreferenceProfileEntry preferenceProfileEntry = new PreferenceProfileEntry();
		preferenceProfileEntry.setUsesTwelveHourFormat(preferenceProfile.usesTwelveHourFormat());
		preferenceProfileEntry.setUsingShiftLabel(preferenceProfile.usingShiftLabel());
		preferenceProfileEntry.setSummaryViewProfileId(adapterHelper.getLongFromObjectIdLong(preferenceProfile.getSummaryViewProfileId()));
		preferenceProfileEntry.setCalendarProfileId(adapterHelper.getLongFromObjectIdLong(preferenceProfile.getCalendarProfileId()));
		preferenceProfileEntry.setExternalURLProfileId(adapterHelper.getLongFromObjectIdLong(preferenceProfile.getExternalURLProfileId()));
		preferenceProfileEntry.setSchedulePeriodId(adapterHelper.getLongFromObjectIdLong(preferenceProfile.getSchedulePeriodId()));
		preferenceProfileEntry.setStaffSchedulerProfileId(adapterHelper.getLongFromObjectIdLong(preferenceProfile.getStaffSchedulerProfileId()));
		preferenceProfileEntry.setPreferenceProfileId(adapterHelper.getLongFromObjectIdLong(accessAssignment.getPreferenceProfileId()));
		employeeExtension.setPreferenceProfileEntry(preferenceProfileEntry );
	}

}
