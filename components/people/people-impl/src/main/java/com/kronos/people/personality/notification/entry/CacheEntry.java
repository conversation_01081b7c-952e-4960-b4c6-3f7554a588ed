
/***********************************************************************
 * CacheEntry.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.notification.entry;

import java.util.Map;

import com.kronos.cache.api.key.MultiExtensionKey;
import com.kronos.people.personality.model.extension.BaseExtension;

/**
 * This class provides the {@link MultiExtensionKey}, {@link Map} containing the
 * extensions and the tenant id.
 * 
 * <AUTHOR>
 *
 */
public class CacheEntry {

	/**
	 * The instance of {@link MultiExtensionKey}.
	 */
	private MultiExtensionKey key;

	/**
	 * The {@link Map} having extensions.
	 */
	private Map<String, BaseExtension> extensions;

	/**
	 * The {@link String} tenant id.
	 */
	private String tenantId;
	
	/**
	 *  The {@link EventType} eventType
	 */
	private EventType eventType;

	/**
	 * Constructor for initializing the newly created instance of
	 * {@code CacheEntry} by passed {@code key}, {@code eventType},
	 * {@code extensions} and {@code tenantId}.
	 * 
	 * @param key
	 *            Key is MultiExtensionKey object
	 * @param eventType
	 *            the event type
	 * @param extensions
	 *            the map
	 * @param tenantId
	 *            the tenant Id
	 */
	public CacheEntry(MultiExtensionKey key, EventType eventType, Map<String, BaseExtension> extensions, String tenantId) {
		this.key = key;
		this.eventType = eventType;
		this.extensions = extensions;
		this.tenantId = tenantId;
	}
	
	/**
	 * Constructor to be used for creating delete(evict) entries.
	 * @param personId Person Id for which we need to evict.
	 * @param tenantId Tenant id of tenant of which this person.
	 */
	public CacheEntry(Long personId, String tenantId) {
		this (new MultiExtensionKey(personId),EventType.DELETE, null, tenantId);
	}

	/**
	 * This method gets the key.
	 * 
	 * @return The instance of {@link MultiExtensionKey}
	 */
	public MultiExtensionKey getKey() {
		return key;
	}

	/**
	 * This method gets the extensions.
	 * 
	 * @return The {@link Map} of extensions
	 */
	public Map<String, BaseExtension> getExtensions() {
		return extensions;
	}

	/**
	 * This method gets the tenant id.
	 * 
	 * @return The {@link String} tenant id
	 */
	public String getTenantId() {
		return tenantId;
	}

	/**
	 * This method sets the tenant id.
	 * 
	 * @param tenantId
	 *            The {@link Long} tenant id to set
	 */
	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	/**
	 * The methods gets the event type
	 * 
	 * @return the eventType
	 */
	public EventType getEventType() {
		return eventType;
	}

}
