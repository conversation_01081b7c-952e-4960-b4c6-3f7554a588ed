package com.kronos.people.personality.dataaccess.repository;

import java.time.LocalDateTime;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.kronos.people.personality.dataaccess.entity.Person;
import org.springframework.transaction.annotation.Transactional;

/**
 * 
 * <AUTHOR>
 *
 */
/**
 * return Page<Person>
 * @param  LocalDateTime startDate,
		LocalDateTime endDate,Pageable  pageable
 * return Page<Person>
 */
@Repository
@Transactional
public interface IPersonAOIDReadRepository extends JpaRepository<Person, Long> {
	@Query(value = "select per FROM Person per WHERE per.personid NOT IN ( select  prscm.personid FROM Prsncmmnidentassign prscm) and per.updatedtm between :startDate AND :endDate and per.personid >0 ", countQuery = "select count(per) FROM Person per WHERE per.personid NOT IN ( select  prscm.personid FROM Prsncmmnidentassign prscm) and per.updatedtm between :startDate AND :endDate and per.personid >0 ")
	public Page<Person> findByEmployeeWithoutAoid(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate,Pageable pageable);
}