package com.kronos.people.personality.dataaccess.legacy;

import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.model.extension.entry.positions.PositionCustomDataEntry;
import com.kronos.people.personality.model.extension.entry.positions.PositionDatesEntry;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.positions.IEmployeePosition;
import com.kronos.wfc.commonapp.people.business.positions.PositionCustomData;
import com.kronos.wfc.commonapp.people.business.positions.PositionCustomDataSet;
import com.kronos.wfc.commonapp.people.business.positions.PositionCustomDate;
import com.kronos.wfc.commonapp.people.business.positions.PositionCustomDateSet;
import com.kronos.wfc.commonapp.people.business.positions.PositionDetails;
import com.kronos.wfc.commonapp.types.business.CustomDateType;
import com.kronos.wfc.commonapp.types.business.ShortNameComparator;
import com.kronos.wfc.platform.persistence.framework.PersistentIterator;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import org.apache.commons.lang3.StringUtils;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Named
public class PositionDatesHelper {

    @Inject
    private AdapterHelper converter;

    public void setConverter(AdapterHelper converter) {
        this.converter = converter;
    }

    /**
     * Returns List of {@link PositionCustomDataEntry} instance.
     *
     * @param position {@link IEmployeePosition} instance
     *
     * @return {@link PositionCustomDataEntry}
     */
    public List<PositionCustomDataEntry> getPositionCustomData(IEmployeePosition position) {
        List<PositionCustomDataEntry> positionCustomDataList = new ArrayList<>();
        PositionCustomDataSet customDataSet = position.getPositionCustomData();
        if (customDataSet == null) {
            return positionCustomDataList;
        }
        PersistentIterator dataIterator = customDataSet.iterator();
        while (dataIterator.hasNext()) {
            PositionCustomDataEntry positionCustomData = new PositionCustomDataEntry();
            PositionCustomData cd = (PositionCustomData) dataIterator.next();
            if (cd != null) {
                positionCustomData.setCustomDataTypeId(converter.getLongFromObjectIdLong(cd.getCustomDataTypeId()));
                positionCustomData.setCustomText(cd.getCustomText());
                positionCustomData.setVersionCount(cd.getVersionCount());
            }
            positionCustomDataList.add(positionCustomData);
        }
        return positionCustomDataList;
    }

    /**
     * @param position The {@link Personality} instance.
     *
     * @return The list of person dates.
     */
    public List<PositionDatesEntry> getPositionDates(IEmployeePosition position) {
        List<PositionDatesEntry> positionDates = new ArrayList<>();
        PositionCustomDateSet dates = position.getPositionCustomDates();
        if (dates == null) {
            return positionDates;
        }
        PersistentIterator datesIterator = dates.iterator(new ShortNameComparator());
        while (datesIterator.hasNext()) {
            PositionCustomDate currentDate = (PositionCustomDate) datesIterator.next();
            positionDates.add(getPositionDateEntry(position, currentDate, dates));
        }
        return positionDates;
    }

    /**
     * This method populates {@link PositionDatesEntry} entry and returns it.
     *
     * @param position    {@link IEmployeePosition} instance
     * @param currentDate {@link PositionCustomDate} entity
     * @param dates       {@link PositionCustomDateSet} instance
     *
     * @return {@link PositionDatesEntry} instance
     */
    private PositionDatesEntry getPositionDateEntry(IEmployeePosition position, PositionCustomDate currentDate,
                                                    PositionCustomDateSet dates) {
        PositionDatesEntry personDate = new PositionDatesEntry();
        personDate.setOverrideDate(getActualDate(currentDate));
        CustomDateType defaultDate = getDefaultCustomDateType(currentDate);
        if (defaultDate != null) {
            personDate.setDescription(defaultDate.getShortName());
            personDate.setDefaultDate(findDefaultDateValue(position, defaultDate, dates));
        } else {
            personDate.setDefaultDate(getDateOfHire(position));
        }
        personDate.setCustomDateTypeId(converter.getLongFromObjectIdLong(currentDate.getCustomDateTypeId()));
        return personDate;
    }

    private CustomDateType getDefaultCustomDateType(PositionCustomDate date) {
        return Optional.ofNullable(date)
                .map(PositionCustomDate::getCustomDateType)
                .map(CustomDateType::getDefCustomDateTypId)
                .filter(id -> !id.isNull())
                .map(CustomDateType::getCustomDateType)
                .orElse(null);
    }

    /**
     * This method uses the customDates object, position object,
     * defaultDate to find default date value.
     *
     * @param position    The instance of {@link IEmployeePosition}
     * @param defaultDate The instance of {@link CustomDateType}
     * @param customDates The instance of {@link PositionCustomDateSet}
     *
     * @return The default values
     */
    private String findDefaultDateValue(IEmployeePosition position, CustomDateType defaultDate,
                                        PositionCustomDateSet customDates) {
        if (defaultDate.getSiteWideScopeSwitch() != 0) {
            return formatDate(defaultDate.getSiteWidedDateTime());
        }
        PositionCustomDate customDate = customDates.getPositionCustomDate(defaultDate.getObjectId());
        if (customDate == null) {
            return "";
        }
        String customValue = getActualDate(customDate);
        if (StringUtils.isNotEmpty(customValue)) {
            return customValue;
        }
        return getDateOfHire(position);
    }

    /**
     * This method uses the IEmployeePosition business object to retrieve the Hire Date.
     *
     * @param customDate the IEmployeePosition object associated with the person being edited
     *
     * @return String value of the current position's hire date
     */
    private String getActualDate(PositionCustomDate customDate) {
        return Optional.ofNullable(customDate)
                .map(PositionCustomDate::getActualCustomDate)
                .map(this::formatDate)
                .orElse(null);
    }

    /**
     * This method uses the IEmployeePosition business object to retrieve the Hire Date.
     *
     * @param position the IEmployeePosition object associated with the person being edited
     *
     * @return String value of the current position's hire date
     */
    private String getDateOfHire(IEmployeePosition position) {
        return Optional.ofNullable(position)
                .map(IEmployeePosition::getDetails)
                .map(PositionDetails::getHiredDate)
                .map(this::formatDate)
                .orElse("");
    }

    private String formatDate(KDate date) {
        return Optional.ofNullable(date)
                .map(converter::kDateToLocalDate)
                .map(DateTimeFormatter.ISO_LOCAL_DATE::format)
                .orElse(null);
    }
}
