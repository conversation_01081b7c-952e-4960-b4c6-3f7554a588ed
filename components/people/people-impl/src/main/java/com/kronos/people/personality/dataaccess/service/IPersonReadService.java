package com.kronos.people.personality.dataaccess.service;

import java.time.LocalDateTime;
import java.util.List;

import com.kronos.people.personality.dataaccess.entity.EmployeeDTO;
import com.kronos.persons.rest.model.LightPersonInformationSearchCriteria;
/**
 * 
 * <AUTHOR>
 *
 */


public interface IPersonReadService {
	/**
	 * This method is used to get Employees without AOID
	 * 
	 * @param Long pageNum, Long pageSize, 
	 * LocalDateTime startDate, LocalDateTime endDate
	 * @return List<EmployeeDTO>
	 */
	public List<EmployeeDTO> findWithoutaoid(Long pageNum, Long pageSize, LocalDateTime startDate, LocalDateTime endDate);

	/**
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param effectiveDateTime
	 * @param expirationDateTime
	 * @param searchCriteria
	 * @return List of EmployeeDTO
	 */
	public List<EmployeeDTO> findLightPersonRecords(Long pageNum, Long pageSize, LocalDateTime effectiveDateTime,
			LocalDateTime expirationDateTime, LightPersonInformationSearchCriteria searchCriteria);
	
}