package com.kronos.people.personality.dataaccess.service;

import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.logging.slf4jadapter.util.Loggable;
import com.kronos.people.personality.dataaccess.entity.EmployeeDTO;
import com.kronos.people.personality.dataaccess.entity.Person;
import com.kronos.people.personality.dataaccess.repository.IPersonAOIDReadRepository;
import com.kronos.people.personality.dataaccess.repository.IPersonReadRepository;
import com.kronos.people.personality.util.StatusCode;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.model.LightPersonInformationSearchCriteria;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
/**
 * 
 * <AUTHOR>
 *
 */

@Repository
@Lazy
@Service("PersonReadServiceImpl")
public class PersonReadServiceImpl implements IPersonReadService {

	private KLogger logger = KLoggerFactory.getKLogger(PersonReadServiceImpl.class);
	private  final  String INDEXVALUE ="indexValue";
	private  final  String ERROR_DATA_NOT_FOUND = "WCO-101336";
	private  final  String NO_DATA_NOT_FOUND = "WCO-101337";
 
	@Inject
	private IPersonAOIDReadRepository iPersonAOIDReadRepository;

	@Inject
	private IPersonReadRepository iPersonReadRepository;

	@Autowired
	private EntityManager em;

	
	/**
	 * return Employees without AOID
	 * @param Long pageNum, Long pageSize, LocalDateTime startDate,
			LocalDateTime endDate
	 * return List<EmployeeDTO>
	 */
	@Override
	@Loggable
	@Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
	public List<EmployeeDTO> findWithoutaoid(Long pageNum, Long pageSize, LocalDateTime startDate,
			LocalDateTime endDate) {
		
		 //With spring-Data-Commons 2.2.1 , constructor PageRequest(int page, int size, Sort.Direction direction, String... properties) 
		 //in PageRequest class has been removed and a static method of(int page, int size, Sort.Direction direction, String... properties)
		 // is added which creates a new PageRequest with sort direction and properties applied.
		 
		Pageable paging = PageRequest.of(pageNum.intValue(), pageSize.intValue());
		Page<Person> pagedResult = null;
		try {
			pagedResult = iPersonAOIDReadRepository.findByEmployeeWithoutAoid(startDate, endDate, paging);
		} catch (Exception exp) {
			logger.error("Error while geting Pageable data fron person :-", exp);
			throw new APIException(ERROR_DATA_NOT_FOUND);
		}
		String commaSepratedIds = null;
        Long totalElements = pagedResult.getTotalElements();
        if(totalElements==0) {
        	logger.info("No Recrd found for given data");
			throw new APIException("NO_DATA_NOT_FOUND_FOR_PAYLOAD");	
        }
        commaSepratedIds = getPersonRecords(pageNum, pagedResult);
        return getPersionid(commaSepratedIds, totalElements);
	}

	@Override
	@Loggable
	@Transactional(readOnly = true)
	public List<EmployeeDTO> findLightPersonRecords(Long pageNum, Long pageSize, LocalDateTime effectiveDateTime,
			LocalDateTime expirationDateTime, LightPersonInformationSearchCriteria searchCriteria) {
		Pageable paging = PageRequest.of(pageNum.intValue(), pageSize.intValue());
		try {
			boolean returnPersonIdOnly = searchCriteria.getWhere().getReturnPersonIdOnly();
			Long employmentStatusId = validateEmploymentStatus(searchCriteria);
			Long userAccountStatusId = validateUserAccount(searchCriteria);
			if (returnPersonIdOnly) {
				return findLightPersonRecordsIds(effectiveDateTime, expirationDateTime, paging, employmentStatusId,
						userAccountStatusId);
			} else {
				return findLightPersonRecordsIdsAllFields(effectiveDateTime, expirationDateTime, paging,
						employmentStatusId, userAccountStatusId);
			}
		} catch (APIException exp) {
			logger.error("Error while geting Pageable data fron person :-", exp);
			throw exp;
		}
	}

	private List<EmployeeDTO> findLightPersonRecordsIdsAllFields(LocalDateTime effectiveDateTime,
			LocalDateTime expirationDateTime, Pageable paging, Long employmentStatusId, Long userAccountStatusId) {
		Page<Object[]> pagedResultForPerson;
		if (employmentStatusId != null && userAccountStatusId != null) {
			pagedResultForPerson = iPersonReadRepository.findLightPersonRecords(effectiveDateTime,expirationDateTime, employmentStatusId, userAccountStatusId, paging);
		} else if (employmentStatusId == null && userAccountStatusId != null) {
			pagedResultForPerson = iPersonReadRepository.findLightPersonRecordsRecordsByUserAccountStatus(effectiveDateTime,expirationDateTime, userAccountStatusId, paging);
		} else if (checkUserActNullAndEmpStatusNotNull(userAccountStatusId,employmentStatusId)) {
			pagedResultForPerson = iPersonReadRepository.findLightPersonRecordsByEmploymentStatusId(effectiveDateTime,expirationDateTime, employmentStatusId, paging);
		} else {
			pagedResultForPerson = iPersonReadRepository.findLightPersonRecords(effectiveDateTime,expirationDateTime, paging);
		}
		return getPersons(pagedResultForPerson);
	}

	private List<EmployeeDTO> findLightPersonRecordsIds(LocalDateTime effectiveDateTime,
			LocalDateTime expirationDateTime, Pageable paging, Long employmentStatusId, Long userAccountStatusId) {
		Page<Long> pagedResultForIds;
		if (employmentStatusId != null && userAccountStatusId != null) {
			pagedResultForIds = iPersonReadRepository.findLightPersonIdsRecords(effectiveDateTime,expirationDateTime, employmentStatusId, userAccountStatusId, paging);
		} else if (employmentStatusId == null && userAccountStatusId != null) {
			pagedResultForIds = iPersonReadRepository.findLightPersonIdsRecordsByUserAccountStatus(effectiveDateTime,expirationDateTime, userAccountStatusId, paging);
		} else if (checkUserActNullAndEmpStatusNotNull(userAccountStatusId,employmentStatusId)) {
			pagedResultForIds = iPersonReadRepository.findLightPersonIdsRecordsByEmploymentStatusId(effectiveDateTime,expirationDateTime, employmentStatusId, paging);
		} else {
			pagedResultForIds = iPersonReadRepository.findLightPersonIdsRecords(effectiveDateTime,expirationDateTime, paging);
		}
		return getPersonIds(pagedResultForIds);
	}

	private Long validateEmploymentStatus(LightPersonInformationSearchCriteria searchCriteria) {
		Long userEmploymentStatusId = StatusCode.getAccountStatusByValue(searchCriteria.getWhere().getEmploymentStatus());
		if (userEmploymentStatusId == null && searchCriteria.getWhere().getEmploymentStatus() != null) {
			logger.error("Request contains invalid employment status..", searchCriteria.getWhere().getEmploymentStatus());
			APIException exception = new APIException(ExceptionConstants.INVALID_EMPLOYMENT_STATUS);
			exception.addUserParameter(ExceptionConstants.EMPLOYMENT_STATUS, searchCriteria.getWhere().getEmploymentStatus());
			throw exception;
		}
		return userEmploymentStatusId;
	}
	
	private Long validateUserAccount(LightPersonInformationSearchCriteria searchCriteria) {
		Long userAccountStatusId = StatusCode.getAccountStatusByValue(searchCriteria.getWhere().getUserAccountStatus());
		if (userAccountStatusId == null && searchCriteria.getWhere().getUserAccountStatus() != null) {
			logger.error("Request contains invalid user account status..", searchCriteria.getWhere().getUserAccountStatus());
			APIException exception = new APIException(ExceptionConstants.INVALID_USERACCOUNT_STATUS);
			exception.addUserParameter(ExceptionConstants.USERACCOUNT_STATUS, searchCriteria.getWhere().getUserAccountStatus());
			throw exception;
		}
		return userAccountStatusId;
	}
	
	private List<EmployeeDTO> getPersons(Page<Object[]> pagedResultForPerson) {
		List<Object[]> listOfPersonObject = pagedResultForPerson.getContent();
		List<EmployeeDTO> employeeDTOs = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(listOfPersonObject)) {
			employeeDTOs = listOfPersonObject.stream().map(EmployeeDTO::new).collect(Collectors.toList());
		}
		return employeeDTOs;
	}

	private List<EmployeeDTO> getPersonIds(Page<Long> pagedResultForIds) {
		return pagedResultForIds.stream().map(e -> {
			EmployeeDTO employeeDTO = new EmployeeDTO();
			employeeDTO.setPersonid(e);
			return employeeDTO;
		}).collect(Collectors.toList());
	}

	private String getPersonRecords(Long pageNum, Page<Person> pagedResult) {
        List<Person> personRecord = null;
        List<Long> personIds = null;
        String separatedIds=null;
        if (pagedResult.hasContent()) {
            personRecord = pagedResult.getContent();
        } else {
            personRecord = new ArrayList<Person>();
        }
        personIds = personRecord.stream().map(Person::getPersonid).collect(Collectors.toList());
        if (! personIds.isEmpty()) {
            separatedIds = "'" + StringUtils.join(personIds, "','") + "'";
        } else {
            logger.error("No person record exist in system for supplied index");
            APIException exception = new APIException(NO_DATA_NOT_FOUND);
            Long pageNo=pageNum;
			if (pageNum >= 0) {
				pageNo = pageNum + 1;
			}
			
            exception.addUserParameter(INDEXVALUE, String.valueOf(pageNo));
            throw exception;
        }
        return separatedIds;
    }

	private List<EmployeeDTO> getPersionid(String persoinIds, Long totalElements) {
		String query = "select per.personid, per.personnum, per.firstnm, per.lastnm, per.birthdtm, per.updatedtm, pnm.phonenum,ctype.shortnm as phonenumtype,\r\n"
				+ "email.emailaddresstxt,\r\n" + "emailctype.shortnm as emailtype\r\n"
				+ "from person as per left join emailaddress as email ON per.personid = email.personid\r\n"
				+ "left  join phonenumber as pnm on per.personid = pnm.personid \r\n"
				+ "left join contacttype as ctype on pnm.contacttypeid= ctype.contacttypeid \r\n"
				+ "left join contacttype as emailctype on email.contacttypeid=emailctype.contacttypeid where per.personid in("
				+ persoinIds + ")";

		@SuppressWarnings("unchecked")
		List<Object[]> results = em.createNativeQuery(query).getResultList();

		return createEmployeeDTOList(totalElements, results);
	}

	private List<EmployeeDTO> createEmployeeDTOList(Long totalElements, List<Object[]> results) {
		List<EmployeeDTO> employeeDTOList = new ArrayList<EmployeeDTO>();
		for (Object[] result : results) {
			EmployeeDTO empDTO = new EmployeeDTO();
			empDTO.setTotalElements(totalElements);
			empDTO.setPersonid(Long.valueOf(getValueAt(result, 0)));
			empDTO.setPersonNum(getValueAt(result, 1));
			empDTO.setFirstName(getValueAt(result, 2));
			empDTO.setLastName(getValueAt(result, 3));
			empDTO.setBirthDate(getValueAt(result, 4));
			empDTO.setUpdatedTime(getValueAt(result, 5));
			empDTO.setTelephoneNumbers(getValueAt(result, 6));
			empDTO.setTelephoneType(getValueAt(result, 7));
			empDTO.setEmailAddresses(getValueAt(result, 8));
			empDTO.setEmailType(getValueAt(result, 9));
			employeeDTOList.add(empDTO);
		}
		return employeeDTOList;
	}

	private String getValueAt(Object[] result, int index) {
		String value = "";
		if (result[index] != null) {
			value = result[index].toString();
		}
		return value;
	}
	private boolean checkUserActNullAndEmpStatusNotNull(Long userAccountStatusId, Long employmentStatusId) {
		return Objects.isNull(userAccountStatusId) && Objects.nonNull(employmentStatusId);
	}
}