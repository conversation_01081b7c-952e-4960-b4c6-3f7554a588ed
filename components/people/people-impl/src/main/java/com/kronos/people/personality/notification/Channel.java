/***********************************************************************
 * Channel.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.notification;

import java.time.Duration;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

import com.kronos.people.personality.Operation;
import com.kronos.people.personality.notification.entry.PersonalityEvent;
import com.kronos.wfc.platform.logging.framework.Log;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * This class provides the functionality of the channel. A personality instance
 * is put and evict from the channel.
 * 
 * <AUTHOR>
 *
 * @param <T>
 *            T is type of {@link PersonalityEvent}
 */
public class Channel<T> {

	private static final Logger LOGGER = LoggerFactory.getLogger(Channel.class);

	/**
	 * The default batch size
	 */
	private static final int DEFAULT_BATCH_SIZE = 10;

	/**
	 * The value contains the channel name.
	 */
	private String name;

	/**
	 * The instance of {@link ExecutorService}.
	 */
	private ExecutorService executorService;

	/**
	 * The value contains the batch size.
	 */
	private int batchSize = DEFAULT_BATCH_SIZE;

	/**
	 * The instance of {@link ConcurrentLinkedQueue}.
	 */
	private Queue<T> queue = new ConcurrentLinkedQueue<T>();

	/**
	 * The instance of {@link ConcurrentLinkedQueue} containing the consumer
	 * list.
	 */
	private Queue<String> consumersList = new ConcurrentLinkedQueue<>();

	AtomicLong timeSpentByConsumers = new AtomicLong(0L);

	private ConcurrentMap<String, Boolean> eventMap = new ConcurrentHashMap<>();

	/**
	 * Default constructor for initializing a newly created instance of
	 * {@link Channel}.
	 */
	public Channel() {

	}

	/**
	 * Constructor for initializing the newly created instance of
	 * {@link Channel} by passed {@code service}, {@code batchSize} and
	 * {@code name}.
	 * 
	 * @param service
	 *            The instance of {@link ExecutorService} to set
	 * @param batchSize
	 *            The batch size to set
	 * @param name
	 *            The channel name to set
	 */
	public Channel(ExecutorService service, int batchSize, String name) {
		this.executorService = service;
		this.batchSize = batchSize;
		this.name = name;
	}

	/**
	 * Constructor for initializing a newly created instance of {@link Channel}
	 * by passed {@code service} and {@code name}.
	 * 
	 * @param service
	 *            The instance {@link ExecutorService} to set
	 * @param name
	 *            The {@link String} name to set
	 */
	public Channel(ExecutorService service, String name) {
		this.executorService = service;
		this.name = name;
	}

	/**
	 * This method register the consumer.
	 * 
	 * @param consumer
	 *            The instance of {@link Consumer} to set
	 * @param consumerName
	 *            The consumer name.
	 */
	public void register(Consumer<T> consumer, String consumerName) {
		putInExecutor(() -> getFromQueueAndProcess(consumer, consumerName), consumerName);
	}

	/**
	 * This method process and log time.
	 * 
	 * @param operation
	 *            The {@link Operation} instance to set
	 */
	public void processAndLogTime(Operation operation) {
		LocalTime startTime = LocalTime.now();
		operation.execute();
		LocalTime endTime = LocalTime.now();
		Duration d = Duration.between(startTime, endTime);
		long curTakenTime = d.toMillis();
		Long curTimeSpent = timeSpentByConsumers.addAndGet(curTakenTime);
		Log.log(Log.DEBUG, "CurTimeSpent = " + curTakenTime + " & Time spent for " + name + " channel :" + curTimeSpent);
	}

	/**
	 * This method polls from the queue and process.
	 * 
	 * @param consumer
	 *            The {@link Consumer} instance to set
	 * @param consumerName
	 *            The {@link String} consumer name to set
	 */
	protected void getFromQueueAndProcess(Consumer<T> consumer, String consumerName) {
		while (!queue.isEmpty()) {
			T item = queue.poll();
			if (item == null) {
				continue;
			}
			processAndLogTime(() -> {
				try {
					consumer.accept(item);
				} catch (Exception e) {
					Log.log(Log.ERROR, getExceptionMessage(consumerName, item), e);
				}
			});
		}
	}

	/**
	 * This method gets the exception message.
	 * 
	 * @param consumerName
	 *            The {@link String} consumer name
	 * @param item
	 *            The item of T type to set
	 * @return The {@link String} exception message
	 */
	public String getExceptionMessage(String consumerName, T item) {
		StringBuilder sb = new StringBuilder();
		sb.append("Exception occured in channel ").append(this.name).append(" for consumer : ").append(consumerName).append(" while consuming for item :")
				.append(item);
		return sb.toString();
	}

	/**
	 * This method gets the exception m
	 * 
	 * @param consumerName
	 *            the consumerName
	 * @param items
	 *            the list of items
	 * @return String append message
	 */
	public String getExceptionMessage(String consumerName, List<T> items) {
		StringBuilder sb = new StringBuilder();
		sb.append("Exception occured in channel ").append(this.name).append(" for consumer : ").append(consumerName).append(" while consuming for items :");
		items.forEach(item->sb.append(item).append(","));
		sb.deleteCharAt(sb.length()-1);
		return sb.toString();
	}

	/**
	 * @param consumer
	 *            the consumer
	 * @param consumerName
	 *            the consumer name
	 */
	public void registerBatchConsumer(Consumer<List<T>> consumer, String consumerName) {
		putInExecutor(() -> getFromQueueAndBatchProcess(consumer, consumerName), consumerName);
	}

	/**
	 * This method puts in executor.
	 * 
	 * @param operation
	 *            The instance of {@link Operation} to set
	 * @param consumerName
	 *            The {@link String} consumer name to set
	 */
	public void putInExecutor(Operation operation, String consumerName) {
		LOGGER.info("personalityService --> Channel putInExecutor");
		consumersList.add(consumerName);
		this.executorService.execute(() -> {
			String actualName = Thread.currentThread().getName();
			Thread.currentThread().setName(actualName + consumerName);
			while (consumersList.contains(consumerName)) {
				operation.execute();
				if (consumersList.contains(consumerName)) {
					try {
						synchronized (queue) {
							queue.wait();
						}
					} catch (InterruptedException e) {
						Log.log(e, "Exception occured in Channel registerBatchConsumer method");
					}
				}
			}
			synchronized (queue) {
				queue.notifyAll();
			}
			Thread.currentThread().setName(actualName);
		});
	}

	/**
	 * This method gets from the queue and process.
	 * 
	 * @param consumer
	 *            The {@link Consumer} instance to set
	 * @param consumerName
	 *            The {@link String} consumer name to set
	 */
	protected void getFromQueueAndBatchProcess(Consumer<List<T>> consumer, String consumerName) {
		while (!queue.isEmpty()) {
			int itemsPresentInOutputList = 0;
			List<T> output = new ArrayList<>();
			while (itemsPresentInOutputList++ <= batchSize && !queue.isEmpty()) {
				T item = queue.poll();
				if (item != null) {
					output.add(item);
				}
			}
			if (output.isEmpty()) {
				continue;
			}
			processAndLogTime(() -> {
				try {
					consumer.accept(output);
				} catch (Exception e) {
					Log.log(e, this.getExceptionMessage(consumerName, output));
				}

			});
		}
	}

	/**
	 * This method unregister the consumer.
	 * 
	 * @param consumerName
	 *            The {@link String} consumer name to remove
	 */
	public void unregister(String consumerName) {
		consumersList.remove(consumerName);
		synchronized (queue) {
			queue.notifyAll();
		}
	}

	/**
	 * This method add an item to the queue.
	 * 
	 * @param item
	 *            The item to add
	 */
	public void add(T item) {
		queue.add(item);
		notifyOnAdd();
	}

	public void addPersonalityEventInQueue(T item) {
		PersonalityEvent event = (PersonalityEvent) item;
		if (eventMap.putIfAbsent(event.getTenantId().concat(String.valueOf(event.getPersonality().getPersonId().longValue())) , Boolean.TRUE) == null || Boolean.TRUE.equals(event.getForcedRefresh())) {
			queue.add(item);
			notifyOnAdd();
		} else {
			LOGGER.info("discarded duplicate PersonalityItems as it already exists in queue or because of forced refresh: {} {}",
					event.getPersonality().getPersonId().longValue(),event.getForcedRefresh());
		}
	}

	public void addPersonalityItems(List<T> items) {
		List<T> itemsToAdd = new ArrayList<>();
		for (T item : items) {
			PersonalityEvent event = (PersonalityEvent) item;
			if (eventMap.putIfAbsent(event.getTenantId().concat(String.valueOf(event.getPersonality().getPersonId().longValue())) , Boolean.TRUE) == null || Boolean.TRUE.equals(event.getForcedRefresh())) {
				itemsToAdd.add(item);
			} else {
				LOGGER.info("discarded duplicate PersonalityItems as it already exists in queue: {} {}",
						event.getPersonality().getPersonId().longValue(),event.getForcedRefresh());
			}
		}
		if (!itemsToAdd.isEmpty()) {
			queue.addAll(itemsToAdd);
			notifyOnAdd();
		}
	}
	/**
	 * This method notify on addition of an item in the queue.
	 */
	public void notifyOnAdd() {
		if (!consumersList.isEmpty())
			this.executorService.execute(() -> {
				synchronized (queue) {
					queue.notifyAll();
				}
			});
	}

	/**
	 * This method adds list of items in queue.
	 * 
	 * @param items
	 *            The {@link List} of items to add
	 */
	public void add(List<T> items) {
		queue.addAll(items);
		notifyOnAdd();
	}

	/**
	 * This method sets the queue.
	 * 
	 * @param queue
	 *            The {@link Queue} to set
	 */
	public void setQueue(Queue<T> queue) {
		this.queue = queue;
	}

	/**
	 * This method gets the queue.
	 * 
	 * @return The {@link Queue} instance
	 */
	public Queue<T> getQueue() {
		return queue;
	}

	/**
	 * This method sets the consumer list.
	 * 
	 * @param consumersList
	 *            The {@link Queue} of consumer list to set
	 */
	public void setConsumersList(Queue<String> consumersList) {
		this.consumersList = consumersList;
	}

	/**
	 * This method gets the consumer list.
	 * 
	 * @return The {@link Queue} of consumer list
	 */
	public Queue<String> getConsumersList() {
		return consumersList;
	}

	/**
	 * This method sets the executor service.
	 * 
	 * @param executorService
	 *            The {@link ExecutorService} instance to set
	 */
	public void setExecutorService(ExecutorService executorService) {
		this.executorService = executorService;
	}

	public ConcurrentMap<String, Boolean> getEventMap() {
		return eventMap;
	}

	public void setEventMap(ConcurrentMap<String, Boolean> eventMap) {
		this.eventMap = eventMap;
	}
	
}
