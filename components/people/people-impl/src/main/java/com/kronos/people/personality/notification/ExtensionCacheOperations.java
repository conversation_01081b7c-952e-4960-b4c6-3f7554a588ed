/***********************************************************************
 * ExtensionCacheOperations.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.notification;

import com.kronos.cache.api.key.MultiExtensionKey;
import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.healthcheck.thread.monitoring.util.ThreadMonitoringUtil;
import com.kronos.logging.slf4jadapter.util.LogService;
import com.kronos.people.personality.cache.CachingKeyHelper;
import com.kronos.people.personality.cache.PersonalityCacheAccessor;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.dataaccess.adapter.ConcurrencyHelper;
import com.kronos.people.personality.dataaccess.adapter.ExtensionAdapterEnum;
import com.kronos.people.personality.dataaccess.legacy.ExtensionBuilder;
import com.kronos.people.personality.dataaccess.legacy.PersonalityConstants;
import com.kronos.people.personality.facade.PersonalityCacheFacade;
import com.kronos.people.personality.model.extension.AllExtension;
import com.kronos.people.personality.model.extension.BaseExtension;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.notification.entry.CacheEntry;
import com.kronos.people.personality.notification.entry.EventType;
import com.kronos.people.personality.notification.entry.PersonalityEvent;
import com.kronos.people.personality.properties.KronosPropertiesFacade;
import com.kronos.people.personality.tenant.TenantHandlingFacade;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.logging.framework.Log;
import com.kronos.wfc.platform.tenant.api.TenantDiscriminatorProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.time.Duration;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;

/**
 * This class provide the functionality for the various cache operations i.e
 * put, evict etc.
 *
 * <AUTHOR>
 *
 */
@Named
@Lazy
public class ExtensionCacheOperations {

	private static final Logger LOGGER = LoggerFactory.getLogger(ExtensionCacheOperations.class);

	/**
	 * The extension builder consumer count
	 */
	private static final Integer EXTENSION_BUILDER_CONSUMERS_COUNT = 5;

	private static final String MISSING_FIELD = "missing";

	@Inject
	private KronosThreadPoolService kronosThreadPoolService;

	private static final String THREAD_POOL_NAME = "personality.ExtensionCacheOperations";

	/**
	 * The cache put consumer count
	 */
	private static final Integer CACHE_PUT_CONSUMERS_COUNT = 5;

	private ExecutorService executor;


	/**
	 * The instance of {@link KronosPropertiesFacade}
	 */
	@Inject
	private KronosPropertiesFacade kronosPropertiesFacade;

	/**
	 * The instance of {@link AdapterHelper}.
	 */
	@Inject
	private AdapterHelper adapterHelper;

	/**
	 * Tenant provider
	 */
	@Inject
	TenantHandlingFacade tenantFacade;

	/**
	 * The instance of {@link PersonalityCacheFacade}.
	 */
	@Inject
	private PersonalityCacheFacade personalityCacheFacade;

	/**
	 * The instance of {@link ExtensionBuilder}.
	 */
	@Inject
	public ExtensionBuilder extensionBuilder;

	/**
	 * The instance of {@link ChangeNotificationChannelController}.
	 */
	@Inject
	private ChangeNotificationChannelController changeNotificationChannelController;

	/**
	 * The instance of {@link CachingKeyHelper}.
	 */
	@Inject
	private CachingKeyHelper cachingKeyHelper;

	/**
	 * The instance of {@link DistributedCacheChannelController}.
	 */
	@Inject
	private DistributedCacheChannelController distributedChannelController;

	/**
	 * The instance of {@link ConcurrencyHelper}.
	 */
	@Inject
	ConcurrencyHelper concurrencyHelper;

	@Inject
	public PersonalityCacheAccessor personalityCacheAccessor;

	/**
	 * The instance of {@link Consumer}. Value contains the list of
	 * {@link CacheEntry} which needs to put.
	 */
	Consumer<List<CacheEntry>> consumerListPut = cacheEntry -> personalityCacheFacade.put(cacheEntry);

	/**
	 * The instance {@link Consumer}. Value contains the list of
	 * {@link CacheEntry} which needs to evict.
	 */
	Consumer<List<CacheEntry>> consumerListEvict = cacheEntry -> personalityCacheFacade.evict(cacheEntry);

	/**
	 * The instance of {@link Consumer} of {@link PersonalityEvent}. This method
	 * is notifying on delete event and for other events build and put
	 * extensions to channel.
	 */
	Consumer<PersonalityEvent> consumer = personalityEvent -> {
		LocalTime startTime = LocalTime.now();
		LogService logService = new LogService();
		String transactionId = logService.getTransactionId();
		LOGGER.debug("personalityService --> personalityEvent.getTransactionId(): {}",personalityEvent.getTransactionId());
		LOGGER.debug("personalityService --> ExtensionCacheOperations consumer");

		try {
			if (personalityEvent.getEventType() == EventType.DELETE) {
				CacheEntry entry = buildCacheEntry(personalityEvent);
				distributedChannelController.notify(EventType.DELETE, entry);
			} else {
				tenantFacade.performOperationsUsingTenantId(() -> {
					LOGGER.info("personalityService --> ExtensionCacheOperations performOperationsUsingTenantId");
					//check if extensions cache already exists/ if yes skip building extensions
					Map<String, BaseExtension> extensionsMap = null;
					if (!kronosPropertiesFacade.getBooleanKronosProperty("people.apply.performance.fixes",true) || Boolean.TRUE.equals(personalityEvent.getForcedRefresh()) || getEmployeeExtensionByPersonId(personalityEvent) == null ) {
						LOGGER.info(
								"personalityService --> Building redis cache from database. personalityEvent.getForcedRefresh {}",
								personalityEvent.getForcedRefresh());
						extensionsMap = buildExtensions(personalityEvent);
						sendToChannel(personalityEvent, extensionsMap);
					} else {
						LOGGER.info(
								"personalityService --> Skipping building extensions as cache is already populated by another notification for personId {}",
								personalityEvent.getPersonality().getPersonId().toLong());
					}

				}, personalityEvent.getTenantId());
			}
		} catch (Exception e) {
			Log.log(e, "This is a warning for personality: " + personalityEvent.getPersonality().getPersonId() + ", tenantId is : " +personalityEvent.getTenantId() + ", trxId is : "+ transactionId +". The data is not in sync and an exception has been raised and evicting cache to resolve the issue");
			extensionBuilder.cacheUpdater.delete(personalityEvent.getPersonality());
		}finally{
			changeNotificationChannelController.getChannel().getEventMap()
					.remove(personalityEvent.getTenantId().concat(String.valueOf(personalityEvent.getPersonality().getPersonId().longValue())));
			LOGGER.info("ChangeNotificationChannelController queue size :{}",
					changeNotificationChannelController.getChannel().getEventMap().size());
		}
		LocalTime endTime = LocalTime.now();
		Duration d = Duration.between(startTime, endTime);
		Log.log(Log.DEBUG, "Time taken for creating cacheEntry for " + personalityEvent.getEventType() + " with personID:"
				+ personalityEvent.getPersonality().getPersonId().toLong() + " is " + d.getNano());
	};

	private EmployeeExtension getEmployeeExtensionByPersonId(PersonalityEvent personalityEvent) {
		try{
			//trying to fetch one small object from redis instead of whole object to save latency
			MultiExtensionKey mk = new MultiExtensionKey(personalityEvent.getPersonality().getPersonId()
					.longValue(), EmployeeExtension.IDENTIFIER.getIdentifier());
			EmployeeExtension extensions=personalityCacheAccessor.getExtensionByPersonId(mk);
			LOGGER.debug("Employee Extensions objects returned from redis  {} for personality: {} for multi extension key {}",extensions,personalityEvent.getPersonality().getPersonId()
					.longValue(),mk);
			return extensions;

		}catch(Exception e){
			LOGGER.error("Error while fetching Multi ExtensionKey from Cache",e);
		}
		return null;
	}

	private Integer extConsumerCount;

	private Integer cacheConsumerCount;

	/**
	 * Build cache entry for personality event.
	 * @param personalityEvent PersonalityEvent for which cache entry to be build
	 * @return CacheEntry
	 */
	protected CacheEntry buildCacheEntry(PersonalityEvent personalityEvent) {
		return new CacheEntry(new MultiExtensionKey(adapterHelper.getLongFromObjectId(personalityEvent.getPersonality().getPersonId())),
				personalityEvent.getEventType(), null, personalityEvent.getTenantId());
	}

	/**
	 * This method register listener for the consumer and for the UPDATE, INSERT
	 * and DELETE events.
	 */
	@PostConstruct
	public void init() {
		executor = kronosThreadPoolService.newThreadPool(THREAD_POOL_NAME );

		//extension building consumers initialization.
		//The consumers will be waiting for notification from 1st channel to start building the extensions
		Integer extensionBuildingConsumersCount = getExtensionBuildingConsumersCount();
		for (int index = 0; index < extensionBuildingConsumersCount; index++) {
			changeNotificationChannelController.registerListener(consumer, "PersonalityEventConsumer" + index);
		}


		//initialization of consumers which will work on the extensions created by 1st channel.
		distributedChannelController.registerListener(EventType.UPDATE, t -> personalityCacheFacade.put(t), "updateRedisConsumer");

		Integer cachePutConsumerCount = getCachePutConsumerCount();
		for (int index = 0; index < cachePutConsumerCount; index++) {
			distributedChannelController.registerListener(EventType.INSERT, t -> personalityCacheFacade.put(t), "insertRedisConsumer"+index);
		}

		distributedChannelController.registerListener(EventType.DELETE, t -> personalityCacheFacade.evict(t), "deleteRedisConsumer");
	}

	/**
	 * This method gets cachePut consumer count.
	 * @return {@link Integer} instance
	 */
	protected Integer getCachePutConsumerCount() {
		if (cacheConsumerCount == null) {
			cacheConsumerCount = kronosPropertiesFacade.getIntegerKronosProperty(PersonalityConstants.CACHEPUT_CONSUMERS_COUNT.getValue(),
					CACHE_PUT_CONSUMERS_COUNT);
		}
		return cacheConsumerCount;
	}

	/**
	 * This method returns extension building consumers count.
	 * @return {@link Integer} instance
	 */
	protected Integer getExtensionBuildingConsumersCount() {
		if(extConsumerCount == null) {
		return kronosPropertiesFacade.getIntegerKronosProperty(PersonalityConstants.EXTENSION_BUILD_CONSUMERS_COUNT.getValue(),
				EXTENSION_BUILDER_CONSUMERS_COUNT);
		}
		return extConsumerCount;
	}

	/**
	 * The method is responsible for cleaning up system before GC.
	 * The executor will be stopped along with various consumers running via this executor.
	 */
	@PreDestroy
	public void cleanUp() {
		//cleaning consumers of 1st channel
		distributedChannelController.unregister(EventType.UPDATE, "updateRedisConsumer");
		Integer cachePutConsumerCount = getCachePutConsumerCount();
		for (int index = 0; index < cachePutConsumerCount; index++) {
			distributedChannelController.unregister(EventType.INSERT, "insertRedisConsumer"+index);
		}

		distributedChannelController.unregister(EventType.DELETE, "deleteRedisConsumer");

		//cleaning consumers of 2nd channel
		Integer extensionBuildingConsumersCount = getExtensionBuildingConsumersCount();
		for (int index = 0; index < extensionBuildingConsumersCount; index++) {
			changeNotificationChannelController.unregister("PersonalityEventConsumer" + index);
		}

		kronosThreadPoolService.shutDownExecutor(executor);
	}

	/**
	 * This method put extensions to channel.
	 *
	 * @param t
	 *            the PersonalityEvent
	 * @param extensionsMap
	 *            map
	 */
	public void sendToChannel(PersonalityEvent t, Map<String, BaseExtension> extensionsMap) {
		CacheEntry entry = getCacheEntry(extensionsMap, t.getEventType(), t.getTenantId());
		distributedChannelController.notify(t.getEventType(), entry);
	}

	/**
	 * This method creates the cache entry.
	 *
	 * @param t
	 *            The instance {@link Personality} to set
	 * @param tenantId
	 *            The tenant id to set
	 * @return The {@link CacheEntry} instance
	 */
	public CacheEntry createEntryForPersonality(Personality t, String tenantId) {
		Map<String, BaseExtension> extensionsMap = extensionBuilder.buildExtensions(t);
		return getCacheEntry(extensionsMap, EventType.INSERT, tenantId);
	}

	/**
	 * This method puts the extension in Redis channel.
	 *
	 * @param extensions
	 *            The instance {@link AllExtension} to set
	 * @param tenantId
	 *            The tenant id to set
	 */
	public void putInRedisChannel(AllExtension extensions, String tenantId) {
		Map<String, BaseExtension> extensionsMap = getExtensionsMap(extensions);
		LogService logService = new LogService();
		String transactionId = logService.getTransactionId();
		putInRedisChannel(tenantId, extensionsMap, transactionId);
	}

	/**
	 * This method gets the CacheEntry from the extensions and notify on insert
	 * event.
	 *
	 * @param tenantId
	 *            the tenant id
	 * @param extensionsMap
	 *            Map of BaseExtension
	 */
	protected void putInRedisChannel(String tenantId, Map<String, BaseExtension> extensionsMap, String transactionId) {
		try {
			String personId = MISSING_FIELD;
			String personNum = MISSING_FIELD;
			String userName = MISSING_FIELD;
			TenantDiscriminatorProvider.setTenantId(tenantFacade.getTenantProvider().getInternalTenantId(tenantId));
			EmployeeExtension employeeExtension = (EmployeeExtension) extensionsMap.get(EmployeeExtension.IDENTIFIER.getIdentifier());
			if(employeeExtension != null) {
				personId = employeeExtension.getPersonId() != null ? employeeExtension.getPersonId().toString() : MISSING_FIELD;
				personNum = employeeExtension.getPersonNumber() != null ? employeeExtension.getPersonNumber() : MISSING_FIELD;
				userName = employeeExtension.getUserName() != null ? employeeExtension.getUserName() : MISSING_FIELD;
				}
			ThreadMonitoringUtil.addThreadContext(createThreadContextMap(tenantId, personId, personNum, userName, transactionId));
			CacheEntry entry = getCacheEntry(extensionsMap, EventType.INSERT, tenantId);
			distributedChannelController.notify(EventType.INSERT, entry);
		} finally {
			ThreadMonitoringUtil.removeThreadContext();
			TenantDiscriminatorProvider.removeCurrentRequest();
		}
	}

	/**
	 * This method puts tenantId into radis channel.
	 * @param extensionsMap Map of {@link String} and {@link BaseExtension}
	 */
	public void putInRedisChannel(Map<String, BaseExtension> extensionsMap) {
		String tenantId = getCurrentTenantId();
		LogService logService = new LogService();
		String transactionId = logService.getTransactionId();
		executor.execute(() -> putInRedisChannel(tenantId, extensionsMap, transactionId));
	}

	/**
	 * This method gets the {@link CacheEntry}.
	 *
	 * @param extensionsMap
	 *            The map of extensions
	 * @param tenantId
	 *            The tenant Id
	 * @return The {@link CacheEntry} object
	 */
	public CacheEntry getCacheEntry(Map<String, BaseExtension> extensionsMap, EventType eventType, String tenantId) {
		return new CacheEntry(cachingKeyHelper.generatePutKey(extensionsMap), eventType, extensionsMap, tenantId);
	}

	/**
	 * This method gets the tenant id.
	 *
	 * @return The {@link String} tenant id
	 */
	protected String getCurrentTenantId() {
			return tenantFacade.getTenantId();
	}

	/**
	 * This method constructs the map of extensions.
	 *
	 * @param extensions
	 *            The {@link AllExtension} instance
	 * @return The map of {@link BaseExtension}
	 */
	protected Map<String, BaseExtension> getExtensionsMap(AllExtension extensions) {
		Map<String, BaseExtension> extensionsMap = new ConcurrentHashMap<>();
		extensionsMap.put(ExtensionAdapterEnum.ACCRUAL.getIdentifier(), extensions.getAccrualExtension());
		extensionsMap.put(ExtensionAdapterEnum.DEVICES.getIdentifier(), extensions.getDeviceExtension());
		extensionsMap.put(ExtensionAdapterEnum.EMPLOYEE.getIdentifier(), extensions.getEmployeeExtension());
		extensionsMap.put(ExtensionAdapterEnum.SCHEDULING.getIdentifier(), extensions.getSchedulingExtension());
		extensionsMap.put(ExtensionAdapterEnum.TIMEKEEPING.getIdentifier(), extensions.getTimekeepingExtension());

		return extensionsMap;
	}

	/**
	 * This method builds the extension
	 *
	 * @param t
	 *            The {@link PersonalityEvent} instance to set
	 * @return The map of {@link BaseExtension}
	 */
	public Map<String, BaseExtension> buildExtensions(PersonalityEvent t) {
		// Create Extensions.
		Personality p = t.getPersonality();
		return extensionBuilder.buildExtensions(p);
	}

	/**
	 * This method sets the {@link AdapterHelper} instance.
	 *
	 * @param adapterHelper
	 *            the adapterhelper to set
	 */
	public void setAdapterHelper(AdapterHelper adapterHelper) {
		this.adapterHelper = adapterHelper;
	}

	/**
	 * This method sets the {@link PersonalityCacheFacade} instance.
	 *
	 * @param personalityCacheFacade
	 *            the personalityCacheFacade to set
	 */
	public void setPersonalityCacheFacade(PersonalityCacheFacade personalityCacheFacade) {
		this.personalityCacheFacade = personalityCacheFacade;
	}

	/**
	 * This method sets the {@link ExtensionBuilder} instance.
	 *
	 * @param extensionBuilder
	 *            the extensionBuilder to set
	 */
	public void setExtensionBuilder(ExtensionBuilder extensionBuilder) {
		this.extensionBuilder = extensionBuilder;
	}

	/**
	 * This method sets the {@link ChangeNotificationChannelController}
	 * instance.
	 *
	 * @param changeNotificationChannelController
	 *            the changeNotificationChannelController to set
	 */
	public void setChangeNotificationChannelController(ChangeNotificationChannelController changeNotificationChannelController) {
		this.changeNotificationChannelController = changeNotificationChannelController;
	}

	/**
	 * This method sets the {@link CachingKeyHelper} instance.
	 *
	 * @param cachingKeyHelper
	 *            the cachingKeyHelper to set
	 */
	public void setCachingKeyHelper(CachingKeyHelper cachingKeyHelper) {
		this.cachingKeyHelper = cachingKeyHelper;
	}

	/**
	 * This method sets the {@link DistributedCacheChannelController} instance.
	 *
	 * @param distributedChannelController
	 *            the distributedChannelController to set
	 */
	public void setDistributedChannelController(DistributedCacheChannelController distributedChannelController) {
		this.distributedChannelController = distributedChannelController;
	}

	/**
	 * This method sets the {@link Consumer} instance.
	 *
	 * @param consumer
	 *            the consumer to set
	 */
	public void setConsumer(Consumer<PersonalityEvent> consumer) {
		this.consumer = consumer;
	}

	/**
	 * This method sets the list of {@link CacheEntry}.
	 *
	 * @param consumerListPut
	 *            the consumerListPut to set
	 */
	public void setConsumerListPut(Consumer<List<CacheEntry>> consumerListPut) {
		this.consumerListPut = consumerListPut;
	}

	/**
	 * This method sets the list of {@link CacheEntry}.
	 *
	 * @param consumerListEvict
	 *            the consumerListEvict to set
	 */
	public void setConsumerListEvict(Consumer<List<CacheEntry>> consumerListEvict) {
		this.consumerListEvict = consumerListEvict;
	}

	/**
	 * This method sets the {@link KronosPropertiesFacade} instance to set.
	 *
	 * @param kronosPropertiesFacade
	 *            the kronosPropertiesFacade to set
	 */
	public void setKronosPropertiesFacade(KronosPropertiesFacade kronosPropertiesFacade) {
		this.kronosPropertiesFacade = kronosPropertiesFacade;
	}

	private Map<String, String> createThreadContextMap(String... values) {
		Map<String, String> stringMap = new HashMap<String, String>(5);
		stringMap.put(" TenantId passed(", values[0] + ")");
		stringMap.put(" PersonId(", values[1] + ")");
		stringMap.put(" PersonNum(", values[2] + ")");
		stringMap.put(" UserName(", values[3] + ")");
		stringMap.put(" TrxId(", values[4] + ")");
		return stringMap;
	}
}
