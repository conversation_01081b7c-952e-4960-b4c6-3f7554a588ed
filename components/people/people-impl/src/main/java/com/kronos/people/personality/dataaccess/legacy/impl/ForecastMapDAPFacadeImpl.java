package com.kronos.people.personality.dataaccess.legacy.impl;

import com.kronos.people.personality.dataaccess.legacy.ForecastMapDAPFacade;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.ukg.container.selectiveloading.exception.NotImplementedException;
import org.springframework.stereotype.Component;

@Component
public class ForecastMapDAPFacadeImpl implements ForecastMapDAPFacade {

    @Override
    public String getForecastMapName(ObjectIdLong id) {
        throw new NotImplementedException();
    }

    @Override
    public ObjectIdLong getForecastMapId(String name) {
        throw new NotImplementedException();
    }
}
