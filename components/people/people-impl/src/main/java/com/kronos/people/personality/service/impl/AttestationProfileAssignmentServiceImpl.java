package com.kronos.people.personality.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kronos.people.personality.converter.AttestationProfileAssignmentConverter;
import com.kronos.people.personality.dataaccess.entity.AttestationProfileAssignment;
import com.kronos.people.personality.dataaccess.repository.AttestationProfileAssignmentRepository;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileAssignmentDTO;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileDTO;
import com.kronos.timekeeping.service.attestation.api.service.AttestationProfileAssignmentService;
import com.kronos.timekeeping.service.attestation.api.service.AttestationProfileSetupService;

@Service("AttestationProfileAssignmentServiceImpl")
@Transactional
public class AttestationProfileAssignmentServiceImpl implements AttestationProfileAssignmentService {
    private final AttestationProfileAssignmentRepository attestationProfileAssignmentRepository;

    private final AttestationProfileAssignmentConverter attestationProfileAssignmentConverter;

    private AttestationProfileSetupService attestationProfileSetupService;

    @Inject
    public AttestationProfileAssignmentServiceImpl(
        AttestationProfileAssignmentRepository attestationProfileAssignmentRepository,
        AttestationProfileAssignmentConverter attestationProfileAssignmentConverter) {
        this.attestationProfileAssignmentRepository = attestationProfileAssignmentRepository;
        this.attestationProfileAssignmentConverter = attestationProfileAssignmentConverter;
    }

    @Override
    public List<AttestationProfileAssignmentDTO> getAttestationProfileAssignments(Long personId) {
        return getAttestationProfileAssignments(personId, false);
    }

    @Override
    public List<AttestationProfileAssignmentDTO> getAttestationProfileAssignments(Long personId, boolean isAssignToManagerRole) {
        List<AttestationProfileAssignment> profileAssignments = attestationProfileAssignmentRepository.findByPersonIdAndManagerRole(
                personId, isAssignToManagerRole);
        Map<Long, AttestationProfileDTO> profilesMap = retrieveProfiles(profileAssignments);

        return profileAssignments.stream()
                .map(profileAssignment -> attestationProfileAssignmentConverter.convertEntityToDto(profileAssignment,
                        profilesMap))
                .collect(Collectors.toList());
    }

    @Override
    public AttestationProfileAssignmentDTO getAttestationProfileAssignment(Long personId, LocalDateTime asOfDate) {
        return getAttestationProfileAssignment(personId, asOfDate, false);
    }

    @Override
    public AttestationProfileAssignmentDTO getAttestationProfileAssignment(Long personId, LocalDateTime asOfDate, boolean isAssignToManagerRole) {
        // get all assignments, then filter out by effective date and isManagerRole flag
        List<AttestationProfileAssignmentDTO> assignments = getAttestationProfileAssignments(personId, isAssignToManagerRole);
        LocalDateTime effectiveDate = (asOfDate != null) ? asOfDate : LocalDateTime.now();
        if (assignments != null && !assignments.isEmpty()) {
            Optional<AttestationProfileAssignmentDTO> assignmentDTO =
                assignments.stream()
                    .filter(dto -> dto.getEffectiveDate().isBefore(effectiveDate) || dto.getEffectiveDate().isEqual(effectiveDate))
                    .filter(dto -> dto.getExpirationDate().isEqual(effectiveDate) || dto.getExpirationDate().isAfter(effectiveDate))
                    .findAny();
            // one assignment at a time
            if (assignmentDTO != null && assignmentDTO.isPresent()) {
                return assignmentDTO.get();
            }
        }
        return null;
    }

    private Map<Long, AttestationProfileDTO> retrieveProfiles(List<AttestationProfileAssignment> profileAssignments) {
        return profileAssignments
            .stream()
            .map(AttestationProfileAssignment::getProfileId)
            .distinct()
            .collect(Collectors.toMap(Function.identity(), attestationProfileSetupService::getAttestationProfile));
    }

    @Override
    public boolean isProfileAssigned(Long profileId) {
        long assignedCount = attestationProfileAssignmentRepository.countByProfileId(profileId);
        return assignedCount > 0;
    }

    @Override
    public boolean isAssignmentAssigned(Long assignmentId) {
        long assignedCount = attestationProfileAssignmentRepository.countByAssignmentId(assignmentId);
        return assignedCount > 0;
    }

    /**
     * Sets attestationProfileSetupService. Initializes through setter in order to avoid possible circular references
     *
     * @param attestationProfileSetupService attestation profile setup service
     */
    @Inject
    public void setAttestationProfileSetupService(
        AttestationProfileSetupService attestationProfileSetupService) {
        this.attestationProfileSetupService = attestationProfileSetupService;
    }
}