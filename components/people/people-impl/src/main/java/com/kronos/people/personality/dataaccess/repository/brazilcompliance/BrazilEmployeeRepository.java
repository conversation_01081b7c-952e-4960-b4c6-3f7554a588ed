package com.kronos.people.personality.dataaccess.repository.brazilcompliance;

import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilEmployeeEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface BrazilEmployeeRepository extends JpaRepository<BrazilEmployeeEntity, Long> {

    /**
     * Returns all Brazil employee records by the given person  ids.
     *
     * @param personIds the person ids
     * @return List of BRC employees
     */
    @Query("SELECT distinct brcemp FROM BrazilEmployeeEntity brcemp WHERE brcemp.personId IN :personIds")
    List<BrazilEmployeeEntity> findByPersonIds(@Param("personIds") List<Long> personIds);

}