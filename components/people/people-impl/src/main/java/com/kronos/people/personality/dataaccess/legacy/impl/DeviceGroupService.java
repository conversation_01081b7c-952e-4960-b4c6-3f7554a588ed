package com.kronos.people.personality.dataaccess.legacy.impl;

import com.kronos.datacollection.udm.service.devicegroup.api.IDeviceGroupService;
import com.kronos.datacollection.udm.service.devicegroup.api.dto.DeviceGroup;
import com.ukg.container.selectiveloading.exception.NotImplementedException;
import org.springframework.stereotype.Component;
import java.util.List;

@Component
public class DeviceGroupService implements IDeviceGroupService {

    @Override
    public List<DeviceGroup> getAllDeviceGroups() {        
        throw new NotImplementedException();
    }

    @Override
    public List<DeviceGroup> getDeviceGroupsByIds(List<Long> list) {
        throw new NotImplementedException();
    }

    @Override
    public String getDeviceGroupNameById(long l) {
        throw new NotImplementedException();
    }

    @Override
    public Long getDeviceGroupIdByName(String s) {
        throw new NotImplementedException();
    }

    @Override
    public String getDeviceGroupNameByEmployeeId(long l) {
        throw new NotImplementedException();
    }

    @Override
    public Long getDeviceGroupIdByEmployeeId(long l) {
        throw new NotImplementedException();
    }

    @Override
    public void updateEmployeeDeviceGroup(long l, Long aLong) {
        throw new NotImplementedException();
    }
}
