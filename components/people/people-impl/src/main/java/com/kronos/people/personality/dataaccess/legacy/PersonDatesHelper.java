package com.kronos.people.personality.dataaccess.legacy;

import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.model.extension.entry.PersonCustomDataEntry;
import com.kronos.people.personality.model.extension.entry.PersonDatesEntry;
import com.kronos.wfc.commonapp.localepolicy.bridge.ILocalePolicyAccessorService;
import com.kronos.wfc.commonapp.people.business.person.CustomData;
import com.kronos.wfc.commonapp.people.business.person.CustomDataSet;
import com.kronos.wfc.commonapp.people.business.person.CustomDate;
import com.kronos.wfc.commonapp.people.business.person.CustomDateSet;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.types.business.CustomDateType;
import com.kronos.wfc.commonapp.types.business.CustomDateTypeCache;
import com.kronos.wfc.commonapp.types.business.ShortNameComparator;
import com.kronos.wfc.platform.datetime.framework.KServer;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.PersistentIterator;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

@Named
public class PersonDatesHelper {


	@Inject
	AdapterHelper converter;

	private static final String DATE_FORMAT = "yyyy-MM-dd";
	
	Function<CustomDate, CustomDateType> defaultDateFunction = (CustomDate currentDate) -> {
		ObjectIdLong defaultDateId = currentDate.getCustomDateType().getDefCustomDateTypId();
		return converter.extractIfLegacyNullableNotNull(
				defaultDateId, id->CustomDateTypeCache.getCustomDateType(id)
				);
	};
	
	
	/**
	 * Default private constructor.
	 */
	protected PersonDatesHelper() {
	}

	/**
	 * Returns List of {@link PersonCustomDataEntry} instance. 
	 * @param currentPersonality {@link Personality} instance
	 * @return {@link PersonCustomDataEntry} 
	 */
	public List<PersonCustomDataEntry> getPersonCustomData(Personality currentPersonality) {

		List<PersonCustomDataEntry> personCustomDataList = new ArrayList<PersonCustomDataEntry>();
		if (currentPersonality.getCustomDates() == null)
			return personCustomDataList;

		CustomDataSet customDataSet = currentPersonality.getCustomData();
		PersistentIterator dataIterator = customDataSet.iterator();
		while (dataIterator.hasNext()) {
			PersonCustomDataEntry personCustomdata = new PersonCustomDataEntry();
			CustomData cd = (CustomData) dataIterator.next();
			if (cd != null) {
				personCustomdata.setCustomDataTypeId(converter.getLongFromObjectIdLong(cd.getCustomDataTypeId()));
				personCustomdata.setCustomText(cd.getCustomText());
				personCustomdata.setVersionCount(cd.getVersionCount());
			}
			personCustomDataList.add(personCustomdata);
		}
		return personCustomDataList;
	}

	/**
	 * @param currentPersonality
	 *            The {@link Personality} instance.
	 * @param converterHelper {@link AdapterHelper}, the helper class
	 * @return The list of person dates.
	 */
	public List<PersonDatesEntry> getPersonDates(Personality currentPersonality, AdapterHelper converterHelper) {
		List<PersonDatesEntry> personDates = new ArrayList<PersonDatesEntry>();
		CustomDateSet dates = currentPersonality.getCustomDates();
		if (dates == null) {
			return personDates;
		}
		
		PersistentIterator datesIterator = dates.iterator(new ShortNameComparator());
		while (datesIterator.hasNext()) {
			CustomDate currentDate = (CustomDate) datesIterator.next();
			personDates.add(getPersonDateEntry(currentPersonality, converterHelper, dates, currentDate));
		}
		return personDates;
	}

	/**
	 * This method populates {@link PersonDatesEntry} entry and returns it.
	 * 
	 * @param currentPersonality {@link Personality} instance
	 * @param converterHelper {@link AdapterHelper} instance
	 * @param dates {@link CustomDateSet} instance
	 * @param currentDate {@link CustomDate} entity
	 * @return {@link PersonDatesEntry} instance
	 */
	protected PersonDatesEntry getPersonDateEntry(Personality currentPersonality, AdapterHelper converterHelper,
			CustomDateSet dates, CustomDate currentDate) {
		PersonDatesEntry personDate = new PersonDatesEntry();
		personDate.setOverrideDate(KServer.dateToString(currentDate.getActualCustomDate(), new SimpleDateFormat(DATE_FORMAT)));
		CustomDateType defaultDate = defaultDateFunction.apply(currentDate);
		if (defaultDate != null) {
			personDate.setDescription(defaultDate.getShortName());
			personDate.setDefaultDate(findDefaultDateValue(defaultDate, dates, currentPersonality, true));
		}
		// If no Default defined, Hire Date is the default (PAR SURO-0343)
		else {
			personDate.setDefaultDate(getDateOfHire(currentPersonality));
		}
		personDate.setCustomDateTypeId(converterHelper.getLongFromObjectIdLong(currentDate.getCustomDateTypeId()));
		return personDate;
	}

	

	/**
	 * This method uses the customDates object, currentPersonality object,
	 * defaultToHireDt object, defaultDate to find default date value.
	 * 
	 * @param defaultDate
	 *            The instance of {@link CustomDateType}
	 * @param customDates
	 *            The instance of {@link CustomDateSet}
	 * @param currentPersonality
	 *            The instance of {@link Personality}
	 * @param defaultToHireDt
	 *            The boolean value of default to hire date
	 * @return The default values
	 */
	protected String findDefaultDateValue(CustomDateType defaultDate, CustomDateSet customDates, Personality currentPersonality, boolean defaultToHireDt) {
		// if default date is sitewide, retun its value
		if (defaultDate.getSiteWideScopeSwitch().longValue() != 0) {
			return converter.kDateToLocalDate(defaultDate.getSiteWidedDateTime()).format(DateTimeFormatter.ofPattern(DATE_FORMAT));
		}
		// else default date is a custom date, check for override value
		CustomDate customDate = customDates.getCustomDate(defaultDate.getObjectId());
		// If date not found in CustomDateSet, just return blank value because
		// we are
		// unable to determine the value
		if (customDate == null) {
			return "";
		}
		String customValue = KServer.dateToString(customDate.getActualCustomDate(), new SimpleDateFormat(DATE_FORMAT));
		if (customValue != null && customValue.length() > 0) {
			return customValue;
		}
		// No override value for custom date, attempt to get it's default date
		CustomDateType newDefaultDate = null;
		ObjectIdLong newDefaultDateId = customDate.getCustomDateType().getDefCustomDateTypId();
		if (newDefaultDateId != null) {
			newDefaultDate = CustomDateTypeCache.getCustomDateType(newDefaultDateId);
		}
		return getDefaultDateValue(customDates, currentPersonality, defaultToHireDt, newDefaultDate);
	}

	/**
	 * This method uses the customDates object, currentPersonality object,
	 * defaultToHireDt object, defaultDate to get default date value.
	 * 
	 * @param customDates
	 *            The instance of {@link CustomDateSet}
	 * @param currentPersonality
	 *            The instance of {@link Personality}
	 * @param defaultToHireDt
	 *            The boolean value of default to hire date.
	 * @param newDefaultDate
	 *            The instance of{@link CustomDateType}
	 * @return The default values
	 */
	protected String getDefaultDateValue(CustomDateSet customDates, Personality currentPersonality, boolean defaultToHireDt,
			CustomDateType newDefaultDate) {
		if (defaultToHireDt) {
			return getDateOfHire(customDates, currentPersonality, newDefaultDate);
		}// else do NOT default to Hire Date
		else {
			if (newDefaultDate != null) {
				return findDefaultDateValue(newDefaultDate, customDates, currentPersonality, false);
			}
			// Return blank, could not find a value
			return "";
		}
	}

	/**
	 * This method uses the customDates object, currentPersonality object,
	 * newDefaultDate to get date of hire.
	 * 
	 * @param customDates
	 *            The instance of {@link CustomDateSet}
	 * @param currentPersonality
	 *            The instance of {@link Personality}
	 * @param newDefaultDate
	 *            The instance of{@link CustomDateType}
	 * @return The date of hire
	 */
	protected String getDateOfHire(CustomDateSet customDates, Personality currentPersonality, CustomDateType newDefaultDate) {
		if (newDefaultDate != null) {
			String strTemp = findDefaultDateValue(newDefaultDate, customDates, currentPersonality, false);
			if (strTemp == null || strTemp.length() == 0)
				return getDateOfHire(currentPersonality);
			return strTemp;
		} else {
			return getDateOfHire(currentPersonality);
		}
	}

	/**
	 * This method uses the Person business object to retrieve the Hire Date
	 *
	 * @param currentPersonality
	 *            the Personality object associated with the person being edited
	 * @return String value of the current person's hire date
	 */
	protected String getDateOfHire(Personality currentPersonality) {
		String hireDate = "";
		Person person = currentPersonality.getNameData();
		// Make sure we have a person
		if (person != null && person.getHireDate() != null) {
			hireDate = KServer.dateToString(person.getHireDate(), new SimpleDateFormat(DATE_FORMAT));
		}
		return hireDate;
	}
}
