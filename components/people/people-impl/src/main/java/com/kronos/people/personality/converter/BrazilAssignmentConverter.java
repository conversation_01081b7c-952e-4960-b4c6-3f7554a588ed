package com.kronos.people.personality.converter;

import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilAssignmentAbstractEntity;
import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilCompanyAssignmentEntity;
import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilEmployeeEntity;
import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilPcaAssignmentEntity;
import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilRepTypeAssignmentEntity;
import com.kronos.people.personality.model.brazilcompliance.BrazilAssignmentDTO;
import com.kronos.people.personality.model.brazilcompliance.BrazilAssignmentRepTypeDTO;
import com.kronos.people.personality.model.brazilcompliance.BrazilEmployeeDTO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class BrazilAssignmentConverter {


    /**
     * Convert Entity to DTO (Data access Layer --> Service layer)
     *
     * @param personId
     * @param empEntity
     * @param companyEntities
     * @param pcaEntities
     * @param repTypeEntities
     * @return BrazilEmployeeDTO
     */
    public BrazilEmployeeDTO convertSingleEmployeeAssignmentEntitiesToDTO(Long personId, BrazilEmployeeEntity empEntity, List<BrazilCompanyAssignmentEntity> companyEntities,
                                                                          List<BrazilPcaAssignmentEntity> pcaEntities, List<BrazilRepTypeAssignmentEntity> repTypeEntities) {
        BrazilEmployeeDTO empDTO = convertBrazilEmployeeEntityToDTO(personId, empEntity);
        empDTO.setCompanyAssignments(convertBrazilCompanyAssignmentEntityToDTO(companyEntities));
        empDTO.setPcaAssignments(convertBrazilPcaAssignmentEntityToDTO(pcaEntities));
        empDTO.setRepTypeAssignments(convertBrazilRepTypeAssignmentEntityToDTO(repTypeEntities));
        return empDTO;
    }


    protected BrazilEmployeeDTO convertBrazilEmployeeEntityToDTO(Long personId, BrazilEmployeeEntity empEntity) {
        BrazilEmployeeDTO brcEmpDTO = new BrazilEmployeeDTO();
        brcEmpDTO.setEmployeeId(personId);
        if (empEntity != null) {
            brcEmpDTO.setPis(empEntity.getPis());
            brcEmpDTO.seteSocial(empEntity.geteSocial());
            brcEmpDTO.setCpf(empEntity.getCpf());
        }
        return brcEmpDTO;
    }

    protected List<BrazilAssignmentDTO> convertBrazilCompanyAssignmentEntityToDTO(List<BrazilCompanyAssignmentEntity> companyEntityList) {
        List<BrazilAssignmentDTO> companyDTOList = new ArrayList<>();
        if (companyEntityList != null && !companyEntityList.isEmpty()) {
            for (BrazilCompanyAssignmentEntity companyEntity : companyEntityList) {
                companyDTOList.add(getBrazilAssignmentDTO(companyEntity, companyEntity.getCompanyId()));
            }
        }
        return companyDTOList;
    }

    protected List<BrazilAssignmentDTO> convertBrazilPcaAssignmentEntityToDTO(List<BrazilPcaAssignmentEntity> pcaEntityList) {
        List<BrazilAssignmentDTO> pcaDTOList = new ArrayList<>();
        if (pcaEntityList != null && !pcaEntityList.isEmpty()) {
            for (BrazilPcaAssignmentEntity pcaEntity : pcaEntityList) {
                pcaDTOList.add(getBrazilAssignmentDTO(pcaEntity, pcaEntity.getPayCodeAttrDefId()));
            }
        }
        return pcaDTOList;
    }

    protected List<BrazilAssignmentDTO> convertBrazilRepTypeAssignmentEntityToDTO(List<BrazilRepTypeAssignmentEntity> repTypeEntityList) {
        List<BrazilAssignmentDTO> repTypeDTOList = new ArrayList<>();
        if (repTypeEntityList != null && !repTypeEntityList.isEmpty()) {
            for (BrazilRepTypeAssignmentEntity repTypeEntity : repTypeEntityList) {
                repTypeDTOList.add(getBrazilRepTypeAssignmentDTO(repTypeEntity));
            }
        }
        return repTypeDTOList;
    }

    protected BrazilAssignmentDTO getBrazilRepTypeAssignmentDTO(BrazilRepTypeAssignmentEntity repTypeEntity) {
        BrazilAssignmentDTO baseDTO = new BrazilAssignmentRepTypeDTO();
        baseDTO.setPersonId(repTypeEntity.getPersonId());
        baseDTO.setId(repTypeEntity.getId());
        baseDTO.setAttributeId(repTypeEntity.getRepTypeId());
        ((BrazilAssignmentRepTypeDTO) baseDTO).setUnionAgreementNumber(repTypeEntity.getUnionAgreementNumber());
        baseDTO.setEffectiveDate(repTypeEntity.getEffectiveDate().toLocalDate());
        return baseDTO;
    }

    protected BrazilAssignmentDTO getBrazilAssignmentDTO(BrazilAssignmentAbstractEntity baseEntity, Long attributeId) {
        BrazilAssignmentDTO baseDTO = new BrazilAssignmentDTO();
        baseDTO.setPersonId(baseEntity.getPersonId());
        baseDTO.setId(baseEntity.getId());
        baseDTO.setAttributeId(attributeId);
        baseDTO.setEffectiveDate(baseEntity.getEffectiveDate().toLocalDate());
        return baseDTO;
    }
}