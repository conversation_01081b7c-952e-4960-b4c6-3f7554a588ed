package com.kronos.people.personality.service.impl;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.kronos.wfc.commonapp.types.business.TimeEntryType;
import com.kronos.wfc.commonapp.types.business.TimeEntryTypeCache;
import org.springframework.stereotype.Service;

import com.kronos.people.personality.service.IPersonalityAttributeNameResolver;
import com.kronos.wfc.commonapp.accessgrp.business.DataAccessGroup;
import com.kronos.wfc.commonapp.accessgrp.business.DataAccessGroupCache;
import com.kronos.wfc.commonapp.hyperfind.business.profile.LightWeightQuery;
import com.kronos.wfc.commonapp.hyperfind.business.profile.LightWeightQueryCache;
import com.kronos.wfc.commonapp.localepolicy.bridge.ILocalePolicyAccessorService;
import com.kronos.wfc.commonapp.localepolicy.bridge.LocalePolicy;
import com.kronos.wfc.commonapp.people.business.person.delegation.DelegateProfile;
import com.kronos.wfc.commonapp.types.business.AnalyticsLaborType;
import com.kronos.wfc.platform.notification.framework.NotificationProfile;
import com.kronos.wfc.platform.notification.framework.NotificationProfileCache;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;


@Service
public class PersonalityAttributeNameResolver implements IPersonalityAttributeNameResolver {

	@Inject
	ILocalePolicyAccessorService localePolicyAccessorService;
	
	@Override
	public String getGDAPProfileName(Long dataAccessGroupId) {
		DataAccessGroup dag = DataAccessGroupCache.getDataAccessGroup(new ObjectIdLong((dataAccessGroupId)));
		if (dag != null) {
			return dag.getLocalizedName();
		}
		return null;
	}

	/**
	 * This method makes a call to AnalyticsLaborType cache and fetches the name on
	 * the basis of ID supplied.
	 */
	@Override
	public String getAnalyticsLaborTypeName(Long analyticsLaborTypeId) {
		return Optional.ofNullable(AnalyticsLaborType.getAnalyticsLaborType(new ObjectIdLong(analyticsLaborTypeId)))
				.map(AnalyticsLaborType::getName).orElse(null);
	}
	
	/**
	 * This method makes a call to LightWeightQueryCache and fetches the names of
	 * all HYPERFINDS with its ID.
	 */
	@Override
	public Map<Long, String> getHyperFindProfiles() {
		return Optional.ofNullable(LightWeightQueryCache.getLightWeightQueries()).orElse(Collections.emptyList())
				.stream().filter(Objects::nonNull)
				.collect(Collectors.toMap(x -> x.getQueryId().longValue(), LightWeightQuery::getName));

	}

	@Override
	public String getDelegateProfileName(Long delegateProfileId) {
		DelegateProfile delegateProfile = DelegateProfile.getDelegateProfile(new ObjectIdLong(delegateProfileId));
		return Objects.nonNull(delegateProfile) ? delegateProfile.getName() : null;
	}
	
	@Override
	public Map<Long,String> getAllNotificationProfiles() {
		return Optional.ofNullable(NotificationProfileCache.getAllNotificationProfiles()).orElse(Collections.emptyList()).stream()
				.collect(Collectors.toMap(x->x.getId().longValue(),NotificationProfile::getDisplayName));
		
	}

	@Override
	public Map<Long, String> getLocalePolicies() {
		return Optional.ofNullable(localePolicyAccessorService.findAll()).orElse(Collections.emptyList()).stream()
				.collect(Collectors.toMap(LocalePolicy::getId, LocalePolicy::getName));
	}

	@Override
	public String getTimeEntryName(Long timeEntryTypeId) {
		TimeEntryType timeEntryType = TimeEntryTypeCache.getTimeEntryType(new ObjectIdLong(timeEntryTypeId));
		return Objects.nonNull(timeEntryType) ? timeEntryType.getName() : null;
	}
}