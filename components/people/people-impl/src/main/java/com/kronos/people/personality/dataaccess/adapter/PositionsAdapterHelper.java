package com.kronos.people.personality.dataaccess.adapter;

import static com.kronos.people.personality.dataaccess.adapter.AdapterHelper.setIfNotNull;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.people.personality.model.extension.entry.positions.*;
import com.kronos.wfc.commonapp.people.business.person.wageoverride.WageWorkRuleOverride;
import com.kronos.wfc.commonapp.people.business.person.wageoverride.WageWorkRuleOverrideSet;
import com.kronos.wfc.commonapp.people.business.positions.*;
import com.kronos.wfc.commonapp.people.business.positions.PositionAccrualProfileAssignment;
import com.kronos.wfc.commonapp.people.business.positions.PositionDirectAccrualProfileAssignmentSet;
import com.kronos.wfc.commonapp.people.business.positions.hrpositioncode.HrPositionCode;
import com.kronos.wfc.commonapp.people.business.positions.hrpositioncode.HrPositionCodeSet;
import com.kronos.wfc.scheduling.core.business.profiles.shifttemplates.ShiftTemplateProfile;
import com.kronos.wfc.timekeeping.cascade.business.config.CascadeProfile;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.kronos.commonapp.labortransfer.api.ILaborTransferService;
import com.kronos.commonapp.labortransfer.model.LaborCategoryItems;
import com.kronos.commonapp.orgmap.setup.model.OrgObjectRef;
import com.kronos.commonapp.orgmap.traversal.api.IOrgMapService;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.model.extension.entry.CostCenterEntry;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.service.PersonalityExtendedAttributesService;
import com.kronos.wfc.commonapp.people.bridge.OrgSet;
import com.kronos.wfc.commonapp.types.business.EmploymentStatusType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;

/**
 * This class have utility methods for EmployeeExtension
 *
 * <AUTHOR> Raman
 */
@Named
public class PositionsAdapterHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(PositionsAdapterHelper.class);

    @Inject
    private ILaborTransferService laborTransferService;

    /**
     * Instance of {@link PersonalityFacade}. It provides methods for finding
     * personality and personality triplet.
     */
    @Inject
    private PersonalityFacade personalityFacade;

    /**
     * Instance of {@link AdapterHelper}. It provides the utility methods.
     */
    @Inject
    private AdapterHelper adapterHelper;

    @Inject
    private PersonalityExtendedAttributesService personalityExtendedAttributesService;

    /**
     * OrgMap service of name ORG to let us communicate with new OrgMap api.
     */
    @Inject
    @Named("ORG")
    private IOrgMapService orgMapService;

    /**
     * @param adapterHelper The adapterHelper to set
     */
    public void setAdapterHelper(AdapterHelper adapterHelper) {
        this.adapterHelper = adapterHelper;
    }

    /**
     * @param personalityFacade The personalityFacade to set
     */
    public void setPersonalityFacade(PersonalityFacade personalityFacade) {
        this.personalityFacade = personalityFacade;
    }

    public void setPositionDetailsRelatedAttributes(PositionEntry positionEntry, PositionDetails positionDetails) {
        PositionDetailsEntry detailsEntry = new PositionDetailsEntry();
        detailsEntry.setHireDate(adapterHelper.kDateToLocalDate(positionDetails.getHiredDate()));
        detailsEntry.setSeniorityDate(adapterHelper.kDateToLocalDate(positionDetails.getSeniorityRankDate()));
        detailsEntry.setSupervisorPersonId(adapterHelper.getLongFromObjectIdLong(positionDetails.getSupervisorId()));
        adapterHelper.setIfObjectIdLongNotNull(positionDetails.getSupervisorId(), detailsEntry,
                this::getSupervisorFullName);
        detailsEntry.setShiftTemplateProfileId(adapterHelper.getLongFromObjectIdLong(positionDetails.getShiftTemplateProfileId()));
        adapterHelper.setIfObjectIdLongNotNull(positionDetails.getShiftTemplateProfileId(),detailsEntry,
                this::getShiftTemplateProfileName);
        detailsEntry.setCascadingProfileId(adapterHelper.getLongFromObjectIdLong(positionDetails.getCascadingProfileId()));
        adapterHelper.setIfObjectIdLongNotNull(positionDetails.getCascadingProfileId(),detailsEntry,
                this::getCascadingProfileName);

        positionEntry.setPositionDetails(detailsEntry);
    }

    /**
     * This method convert the {@code position} to effective dated position status {@code EffectiveDatedCollection}
     * of {@code PositionStatusEntry} by {@code position}.
     *
     * @param positionEntry {@link PositionEntry} position entry to set the collection of orders
     * @param statuses      {@link PositionStatusSet}, the position status data.
     */
    public void setPositionStatusRelatedAttributes(PositionEntry positionEntry, PositionStatusSet statuses) {
        Collection<PositionStatusMapList> legacyEmploymentStatus = personalityFacade.getLegacyPositionStatus(statuses);
        Collection<PositionStatusEntry> entries = adapterHelper
                .convertLegacyCollection(legacyEmploymentStatus, this::getDatedPositionStatusEntry);
        positionEntry.setEffDatedPositionStatus(new EffectiveDatedCollection<>(entries));
    }

    /**
     * This method convert the {@code Position} to
     * {@code EffectiveDatedCollection} of {@code PositionJobAccountEntry} by
     * {@code Position}.
     *
     * @param positionEntry         {@link PositionEntry} position entry to set the collection of orders
     * @param positionLaborAccounts {@link Collection} ,collection of PositionLaborAccount
     */
    public void setPositionJobRelatedAttributes(PositionEntry positionEntry,
                                                PositionLaborAccountSet positionLaborAccounts) {
        Collection<PositionLaborAccount> members = positionLaborAccounts.getAllPositionLaborAccounts();
        List<Long> laborAccountIds = members.stream().map(PositionLaborAccount::getLaborCategoryId)
                .map(adapterHelper::getLongFromObjectIdLong).distinct().collect(Collectors.toList());
        Map<Long, LaborCategoryItems> laborCategories = laborTransferService
                .getLaborCategoryItemsFromAccountIdList(laborAccountIds)
                .stream()
                .collect(Collectors.toMap(LaborCategoryItems::getLaborAccountId, Function.identity()));
        Collection<PositionJobAccountEntry> jobAccountEntries = adapterHelper
                .convertLegacyCollection(members, account -> convertPositionLaborAccount(account, laborCategories,positionEntry.getPositionId()));
        positionEntry.setEffDatedPositionJobAccount(new EffectiveDatedCollection<>(jobAccountEntries));
    }

    /**
     * This method convert the {@code Position} to
     * {@code EffectiveDatedCollection} of {@code PositionJobAccountEntry} by
     * {@code Position}.
     *
     * @param positionEntry             {@link PositionEntry} position entry to set the collection of orders
     * @param positionAccessAssignments {@link Collection}, collection of PositionAccessAssignment
     */
    public void setPositionJobTransferRelatedAttributes(PositionEntry positionEntry,
                                                        PositionAccessAssignmentSet positionAccessAssignments) {
        Collection<PositionAccessAssignment> members = positionAccessAssignments.getAllPositionAccessAssignments();
        Collection<PositionJobTransferEntry> jobTransferEntries = adapterHelper
                .convertLegacyCollection(members, this::getJobTransferEntry);
        positionEntry.setEffDatedPositionJobTransfer(new EffectiveDatedCollection<>(jobTransferEntries));
    }

    public void setPositionOrderRelatedAttributes(PositionEntry positionEntry, IEmployeePosition position) {
        List<PositionOrderEntry> orderEntries = position.getOrderSnapshots().getAllPositionOrders()
                .stream()
                .map(s -> s.getPositionOrder(position))
                .filter(Objects::nonNull)
                .map(this::convertPositionOrder)
                .collect(Collectors.toList());
        EffectiveDatedCollection<PositionOrderEntry> positionOrders = new EffectiveDatedCollection<>(orderEntries);
        positionEntry.setEffDatedPositionOrder(positionOrders);
    }

    /**
     * This method convert the {@code positionEntry} to effective dated ScheduleGroups
     * @param positionEntry {@link PositionEntry}
     * @param assignmentSet {@link PositionScheduleGroupAssignmentSet}
     */
    public void setPositionScheduleGroups(PositionEntry positionEntry,
                                          PositionScheduleGroupAssignmentSet assignmentSet) {
        EffectiveDatedCollection<PositionScheduleGroupAssignmentEntry> effDatedScheduleGroupAssignments;
        effDatedScheduleGroupAssignments =
                new EffectiveDatedCollection<>(assignmentSet.getAllPositionScheduleGroupAssignments()
                        .stream().map(this::convertToPositionScheduleGroupAssignmentEntry)
                        .collect(Collectors.toList()));

        positionEntry.setEffDatedScheduleGroupAssignments(effDatedScheduleGroupAssignments);
    }

    /**
     * This method convert the {@code position} to effective dated position employee terms
     * {@code EffectiveDatedCollection} of {@code PositionEmploymentTermAssignments} by {@code position}.
     * @param positionEntry {@link PositionEntry}, the position entry to set the collection of orders
     * @param assignmentSet {@link PositionEmploymentTermAssignmentSet} the position group assignment data.
     */
    public void setPositionEmploymentTerms(PositionEntry positionEntry,
                                           PositionEmploymentTermAssignmentSet assignmentSet) {
        assignmentSet.setPositionId(new ObjectIdLong(positionEntry.getPositionId()));
        positionEntry.setEffDatedEmploymentTermAssignments(
                new EffectiveDatedCollection<>(assignmentSet.getAllPositionEmploymentTermAssignments()
                        .stream()
                        .map(this::convertToEmploymentTermAssignment)
                        .collect(Collectors.toList())
                )
        );
    }

    public void setHrPositionCodes(PositionEntry positionEntry, HrPositionCodeSet hrPositionCodeSet) {
        positionEntry.setHrPositionCodeBean(
                hrPositionCodeSet.getAllHrPositionCodes()
                        .stream()
                        .map(this::convertToHrPositionCodeEntry)
                        .collect(Collectors.toList())
        );
    }

    /**
     * Converts a set of effective dated {@link WageWorkRuleOverride} instances to {@link PositionWageWorkRuleOverrideEntry}
     * instances and sets the list within {@link PositionEntry} object.
     * @param positionEntry {@link PositionEntry}, the position entry to set the collection of wage work rule overrides
     * @param wageWorkRuleOverrideSet a set of effective dated {@link WageWorkRuleOverride} instances
     */
    public void setPositionWageWorkRuleOverrides(PositionEntry positionEntry,
                                                 WageWorkRuleOverrideSet wageWorkRuleOverrideSet) {
        positionEntry.setEffDatedWageWorkRuleOverrides(
                new EffectiveDatedCollection<>(wageWorkRuleOverrideSet.getAllMembers()
                        .stream()
                        .map(this::convertToWageWorkRuleOverride)
                        .collect(Collectors.toList())
                )
        );
    }

    /**
     * This method convert the {@code position} to effective dated position pay from schedule
     * {@code EffectiveDatedCollection} of {@code PositionPayFromScheduleAssignments} by {@code position}.
     *
     * @param positionEntry {@link PositionEntry}, the position entry to set the collection of orders
     * @param assignmentSet {@link PositionPayFromScheduleAssignmentSet} the position pay from schedule assignment data.
     */
    public void setPositionPayFromSchedule(PositionEntry positionEntry,
                                           PositionPayFromScheduleAssignmentSet assignmentSet) {
        assignmentSet.setPositionId(new ObjectIdLong(positionEntry.getPositionId()));
        List<PositionPayFromScheduleAssignmentEntry> entries = assignmentSet.getAllPositionPayFromScheduleAssignments()
                                                                       .stream()
                                                                       .map(this::convertToPayFromScheduleAssignment)
                                                                       .collect(Collectors.toList());
        positionEntry.setEffDatedPayFromScheduleAssignments(
                new EffectiveDatedCollection<>(getPFSAssignmentsWithFilledGaps(entries)));
    }

    public void setPositionAccrualProfiles(PositionEntry positionEntry,
                                          PositionDirectAccrualProfileAssignmentSet assignmentSet) {
        assignmentSet.setPositionId(new ObjectIdLong(positionEntry.getPositionId()));
        positionEntry.setEffDatedAccrualProfileAssignments(
                new EffectiveDatedCollection<>(assignmentSet.getAllPositionAccrualProfileAssignments()
                        .stream()
                        .map(this::convertToAccrualProfileAssignmentEntry)
                        .collect(Collectors.toList())
                )
        );


    }

    /**
     * This method gets the supervisor full name by supervisorId.
     *
     * @param supervisorId {@link ObjectIdLong}, contains the supervisor id.
     * @param detailsEntry Employee Extension
     */
    private void getSupervisorFullName(PositionDetailsEntry detailsEntry, ObjectIdLong supervisorId) {
        setIfNotNull(personalityFacade.getPerson(supervisorId), detailsEntry,
                (t, u) -> t.setSupervisorFullName(u.getFullName()));
    }

    /**
     * This method gets the shift template profile name by id
     *
     * @param shiftTemplateProfileId {@link ObjectIdLong}, contains the shift template id.
     * @param detailsEntry Employee Extension
     */
    private void getShiftTemplateProfileName(PositionDetailsEntry detailsEntry, ObjectIdLong shiftTemplateProfileId) {
        setIfNotNull(ShiftTemplateProfile.getShiftTemplateProfile(new ObjectIdLong(shiftTemplateProfileId)), detailsEntry,
                (t, u) -> t.setShiftTemplateProfile(u.getName()));
    }

    /**
     * This method gets the cascading profile name by id
     *
     * @param cascadingProfileId {@link ObjectIdLong}, contains the shift template id.
     * @param detailsEntry Employee Extension
     */
    private void getCascadingProfileName(PositionDetailsEntry detailsEntry, ObjectIdLong cascadingProfileId) {
        setIfNotNull(CascadeProfile.retrieveById(new ObjectIdLong(cascadingProfileId)), detailsEntry,
                (t, u) -> t.setCascadingProfile(u.getName()));
    }

    private PositionStatusEntry getDatedPositionStatusEntry(PositionStatusMapList positionStatus) {
        EmploymentStatusType employmentStatusType = personalityFacade.getEmploymentStatusType(positionStatus);
        PositionStatusEntry positionStatusEntry = new PositionStatusEntry();
        if (employmentStatusType != null) {
            adapterHelper.setDates(positionStatusEntry, positionStatus.getEffectiveDate(),
                    positionStatus.getExpirationDate());
            Long statusTypeId = adapterHelper.getLongFromObjectIdLong(employmentStatusType.getEmploymentStatusTypeId());
            positionStatusEntry.setPositionStatusTypeId(statusTypeId);
        } else {
                LOGGER.error(
                    "ALERT!!! EmploymentStatusType from EmploymentStatusTypeCache is NULL for positionStatus: {}",
                    positionStatus);
        }
        return positionStatusEntry;
    }

    /**
     * Converts a {@link WageWorkRuleOverride} instance to a {@link  PositionWageWorkRuleOverrideEntry} instance.
     * @param wwrOverride the {@link WageWorkRuleOverride} instance to convert
     * @return a {@link PositionWageWorkRuleOverrideEntry} instance
     */
    private PositionWageWorkRuleOverrideEntry convertToWageWorkRuleOverride(WageWorkRuleOverride wwrOverride) {
        BigDecimal baseWageRate = wwrOverride.getPositionBaseWage() != null ?
                wwrOverride.getPositionBaseWage().decimalValue() : null;
        Long baseWorkRuleId = wwrOverride.getPositionBaseWorkRuleId() != null ?
                adapterHelper.getLongFromObjectIdLong(wwrOverride.getPositionBaseWorkRuleId()) : null;
        LocalDate effectiveDate = adapterHelper.kDateToLocalDate(wwrOverride.getEffectiveDate());
        LocalDate expirationDate = adapterHelper.kDateToLocalDate(wwrOverride.getExpirationDate());

        return new PositionWageWorkRuleOverrideEntry(baseWageRate, baseWorkRuleId, effectiveDate, expirationDate);
    }

    private PositionJobAccountEntry convertPositionLaborAccount(PositionLaborAccount laborAccount,
                                                                Map<Long, LaborCategoryItems> laborCategories,Long positionId) {
        PositionJobAccountEntry positionJobAccountEntry = new PositionJobAccountEntry();
        positionJobAccountEntry.setEffectiveDate(adapterHelper.kDateToLocalDate(laborAccount.getEffectiveDate()));
        positionJobAccountEntry.setExpirationDate(adapterHelper.kDateToLocalDate(laborAccount.getExpirationDate()));
        adapterHelper
                .setDates(positionJobAccountEntry, laborAccount.getEffectiveDate(), laborAccount.getExpirationDate());
        Long laborCategoryId = adapterHelper.getLongFromObjectIdLong(laborAccount.getLaborCategoryId());
        positionJobAccountEntry.setLaborAccountId(laborCategoryId);
        Optional.ofNullable(laborCategoryId)
                .map(laborCategories::get)
                .ifPresent(laborCategory -> {
                    String laborCategoryString = laborCategory.getLaborCategoryString();
                    positionJobAccountEntry.setLaborAccount(laborCategoryString);
                    positionJobAccountEntry.setLaborCategory(laborCategoryString);
                });
        ObjectIdLong organizationId = laborAccount.getPrimaryOrganizationId();
        LOGGER.debug("PS-316091 organizationId = {}", organizationId);
        LOGGER.debug("PS-316091 laborAccount.getEffectiveDate() = {}", laborAccount.getEffectiveDate());
        positionJobAccountEntry.setJob(getJobName(organizationId, laborAccount.getEffectiveDate()));
        positionJobAccountEntry.setOrganizationId(adapterHelper.getLongFromObjectIdLong(organizationId));
        List<CostCenterEntry> costCenterHistory=personalityExtendedAttributesService.getCostCentersForAPosition(positionId, positionJobAccountEntry.getEffectiveDate(), positionJobAccountEntry.getExpirationDate(),positionJobAccountEntry.getOrganizationId());
        positionJobAccountEntry.setEffDatedCostCenterEntries(costCenterHistory);
        positionJobAccountEntry.setVersionCount(laborAccount.getVersionCount());
        return positionJobAccountEntry;
    }

    private String getJobName(ObjectIdLong primaryOrganizationId, KDate effectiveDate) {
        BiFunction<KDate, Long, String> consumer = (kdate, id) -> {
            OrgObjectRef orgObjectRef = new OrgObjectRef(id);
            OrgObjectRef resolvedObjectRef = orgMapService.resolve(orgObjectRef, adapterHelper.kDateToLocalDate(kdate));
            if (resolvedObjectRef != null) {
                LOGGER.debug("PS-316091  Primary Job Name === {}", resolvedObjectRef.getQualifier());
                return resolvedObjectRef.getQualifier();
            }
            return null;
        };
        return adapterHelper.getIfObjectIdLongNotNull(primaryOrganizationId, effectiveDate, consumer);
    }

    /**
     * This method returns {@link PositionJobTransferEntry} instance after setting the values from
     * {@link PositionAccessAssignment} instance.
     *
     * @param accessAssignment {@link PositionAccessAssignment} instance
     *
     * @return {@link PositionJobTransferEntry} instance
     */
    private PositionJobTransferEntry getJobTransferEntry(PositionAccessAssignment accessAssignment) {
        PositionJobTransferEntry jobTransferEntry = new PositionJobTransferEntry();
        jobTransferEntry.setEffectiveDate(adapterHelper.kDateToLocalDate(accessAssignment.getEffectiveDate()));
        jobTransferEntry.setExpirationDate(adapterHelper.kDateToLocalDate(accessAssignment.getExpirationDate()));
        adapterHelper
                .setDates(jobTransferEntry, accessAssignment.getEffectiveDate(), accessAssignment.getExpirationDate());
        adapterHelper.setIfObjectIdLongNotNull(accessAssignment.getManagerTransferOrganizationSetId(),
                jobTransferEntry, (entry, id) -> setJobTransferSet(id, entry::setManagerJobTransferSet));
        adapterHelper.setIfObjectIdLongNotNull(accessAssignment.getProfessionalTransferOrganizationSetId(),
                jobTransferEntry, (entry, id) -> setJobTransferSet(id, entry::setEmployeeJobTransferSet));
        adapterHelper.setIfObjectIdLongNotNull(accessAssignment.getEmpMgrTransferOrganizationSetId(),
                jobTransferEntry, (entry, id) -> setJobTransferSet(id, entry::setEmpManagerJobTransferSet));
        jobTransferEntry.setManagerAccessOrganizationSetId(adapterHelper
                .getLongFromObjectIdLong(accessAssignment.getManagerAccessOrganizationSetId()));
        jobTransferEntry.setManagerTransferOrganizationSetId(
                adapterHelper.getLongFromObjectIdLong(accessAssignment.getManagerTransferOrganizationSetId()));
        jobTransferEntry.setProfessionalTransferOrganizationSetId(adapterHelper
                .getLongFromObjectIdLong(accessAssignment.getProfessionalTransferOrganizationSetId()));
        jobTransferEntry.setEmpMgrTransferOrganizationSetId(adapterHelper
                .getLongFromObjectIdLong(accessAssignment.getEmpMgrTransferOrganizationSetId()));
        return jobTransferEntry;
    }

    private void setJobTransferSet(ObjectIdLong transferOrganizationSetId, Consumer<String> jobNameConsumer) {
        OrgSet orgSet = personalityFacade.findOrgMapGroupById(transferOrganizationSetId);
        Optional.ofNullable(orgSet).map(OrgSet::getName).ifPresent(jobNameConsumer);
    }

    private PositionOrderEntry convertPositionOrder(PositionOrder order) {
        PositionOrderEntry positionOrderEntry = new PositionOrderEntry();
        positionOrderEntry.setOrderNumber(order.getOrderNumber());
        positionOrderEntry.setPrimary(order.isPrimary());
        adapterHelper.setDates(positionOrderEntry, order.getEffectiveDate(), order.getExpirationDate());
        return positionOrderEntry;
    }

    /**
     * Convert {@link PositionScheduleGroupAssignment} to {@link PositionScheduleGroupAssignmentEntry}
     * @param groupAssignment - {@link PositionScheduleGroupAssignment}
     * @return entry - {@link PositionScheduleGroupAssignmentEntry}
     */
    private PositionScheduleGroupAssignmentEntry convertToPositionScheduleGroupAssignmentEntry(
            PositionScheduleGroupAssignment groupAssignment){
        PositionScheduleGroupAssignmentEntry entry = new PositionScheduleGroupAssignmentEntry();
        entry.setGroupId(adapterHelper.getLongFromObjectIdLong(groupAssignment.getScheduleGroupId()));
        entry.setName(groupAssignment.getGroupName());
        entry.setEffectiveDate(adapterHelper.kDateToLocalDate(groupAssignment.getEffectiveDate()));
        entry.setExpirationDate(adapterHelper.kDateToLocalDate(groupAssignment.getExpirationDate()));
        return entry;
    }

    /**
     * Convert {@link PositionEmploymentTermAssignment} to {@link PositionEmploymentTermAssignmentEntry}
     * @param employmentTerm - {@link PositionEmploymentTermAssignment}
     * @return entry - {@link PositionEmploymentTermAssignmentEntry}
     */
    private PositionEmploymentTermAssignmentEntry convertToEmploymentTermAssignment(
            PositionEmploymentTermAssignment employmentTerm){
        PositionEmploymentTermAssignmentEntry entry = new PositionEmploymentTermAssignmentEntry();
        entry.setEmploymentTermId(adapterHelper.getLongFromObjectIdLong(employmentTerm.getScheduleGroupId()));
        entry.setName(employmentTerm.getName());
        entry.setEffectiveDate(adapterHelper.kDateToLocalDate(employmentTerm.getEffectiveDate()));
        entry.setExpirationDate(adapterHelper.kDateToLocalDate(employmentTerm.getExpirationDate()));
        return entry;
    }

    private PositionCodeEntry convertToHrPositionCodeEntry(HrPositionCode hrPositionCode){
        PositionCodeEntry entry = new PositionCodeEntry();
        entry.setId(hrPositionCode.getPositionCodeId().get().toLong());
        entry.setName(hrPositionCode.getHrPositionCodeName().get());
        entry.setStartDate(adapterHelper.kDateToLocalDate(hrPositionCode.getEffectiveDate()).toString());
        entry.setEndDate(adapterHelper.kDateToLocalDate(hrPositionCode.getExpirationDate()).toString());
        return entry;
    }

    /**
     * Convert {@link PositionPayFromScheduleAssignment} to {@link PositionPayFromScheduleAssignmentEntry}.
     *
     * @param pfsAssignment - {@link PositionPayFromScheduleAssignment}
     * @return entry - {@link PositionPayFromScheduleAssignmentEntry}
     */
    private PositionPayFromScheduleAssignmentEntry convertToPayFromScheduleAssignment(
            PositionPayFromScheduleAssignment pfsAssignment) {
        PositionPayFromScheduleAssignmentEntry entry = new PositionPayFromScheduleAssignmentEntry();

        entry.setOverridePayRule(pfsAssignment.getOverridePFS());
        entry.setPayShiftFromSchedule(pfsAssignment.getPayShiftFromSchedule());
        entry.setPayEditsFromSchedule(pfsAssignment.getPayEditsFromSchedule());
        entry.setCancelPFSOnHolidays(pfsAssignment.getCancelPFSOnHolidays());
        ObjectIdLong payCodeTagId = pfsAssignment.getNoncancellingTagId();
        if (Objects.nonNull(payCodeTagId) && !payCodeTagId.isNull()) {
            ObjectRef payCodeTag = new ObjectRef(adapterHelper.getLongFromObjectIdLong(payCodeTagId),
                    pfsAssignment.getNoncancellingTagName());
            entry.setPaycodeTag(payCodeTag);
        }
        entry.setEffectiveDate(adapterHelper.kDateToLocalDate(pfsAssignment.getEffectiveDate()));
        entry.setExpirationDate(adapterHelper.kDateToLocalDate(pfsAssignment.getExpirationDate()));
        return entry;
    }

    private List<PositionPayFromScheduleAssignmentEntry> getPFSAssignmentsWithFilledGaps(
            List<PositionPayFromScheduleAssignmentEntry> assignments) {
        LocalDate startOfTime = adapterHelper.kDateToLocalDate(KDate.getNewSotDate());
        LocalDate endOfTime = adapterHelper.kDateToLocalDate(KDate.getEotDate());
        if (CollectionUtils.isEmpty(assignments)) {
            return Collections.singletonList(createDefaultPFS(startOfTime, endOfTime));
        }
        List<PositionPayFromScheduleAssignmentEntry> result = new ArrayList<>();
        for (int i = 0; i < assignments.size(); i++) {
            PositionPayFromScheduleAssignmentEntry assignment1 = assignments.get(i);
            if (startOfTime.isBefore(assignment1.getEffectiveDate()) && result.isEmpty()) {
                result.add(createDefaultPFS(startOfTime, assignment1.getEffectiveDate()));
            }
            result.add(assignment1);
            if (i + 1 < assignments.size()) {
                PositionPayFromScheduleAssignmentEntry assignment2 = assignments.get(i + 1);
                if (assignment1.getExpirationDate().isBefore(assignment2.getEffectiveDate())) {
                    result.add(createDefaultPFS(assignment1.getExpirationDate(), assignment2.getEffectiveDate()));
                }
            } else if (endOfTime.isAfter(assignment1.getExpirationDate())) {
                result.add(createDefaultPFS(assignment1.getExpirationDate(), endOfTime));
            }
        }
        return result;
    }

    private PositionPayFromScheduleAssignmentEntry createDefaultPFS(LocalDate effectiveDate,
                                                                    LocalDate expirationDate) {
        PositionPayFromScheduleAssignmentEntry assignment = new PositionPayFromScheduleAssignmentEntry();
        assignment.setOverridePayRule(false);
        assignment.setPayShiftFromSchedule(false);
        assignment.setPayEditsFromSchedule(false);
        assignment.setCancelPFSOnHolidays(false);
        assignment.setEffectiveDate(effectiveDate);
        assignment.setExpirationDate(expirationDate);
        return assignment;
    }

    public void setPositionFullTimeEquivalencies(PositionEntry positionEntry, PositionFullTimeEquivalencySet positionFullTimeEquivalencySet) {
        positionFullTimeEquivalencySet.setPositionId(new ObjectIdLong(positionEntry.getPositionId()));
        positionEntry.setEffDatedFullTimeEquivalencies(new EffectiveDatedCollection<>(positionFullTimeEquivalencySet.getAllPositionFullTimeEquivalencies()
                .stream()
                .map(this::convertToPositionFullTimeEquivalencyEntry)
                .collect(Collectors.toList())));
    }

    private PositionFullTimeEquivalencyEntry convertToPositionFullTimeEquivalencyEntry(PositionFullTimeEquivalency positionFullTimeEquivalency) {
        PositionFullTimeEquivalencyEntry entry = new PositionFullTimeEquivalencyEntry();
        entry.setFullTimeEquivalencyPercent(positionFullTimeEquivalency.getFullTimeEquivalencyPercent());
        entry.setFullTimeStandardHoursQuantity(positionFullTimeEquivalency.getFullTimeStandardHoursQuantity());
        entry.setEmployeeStandardHoursQuantity(positionFullTimeEquivalency.getEmployeeStandardHoursQuantity());
        adapterHelper.setDates(entry, positionFullTimeEquivalency.getEffectiveDate(), positionFullTimeEquivalency.getExpirationDate());
        return entry;
    }

    private PositionAccrualProfileAssignmentEntry convertToAccrualProfileAssignmentEntry(
            PositionAccrualProfileAssignment accrualAssignment) {
        PositionAccrualProfileAssignmentEntry entry = new PositionAccrualProfileAssignmentEntry();

        entry.setAccrualProfileId(adapterHelper.getLongFromObjectIdLong(accrualAssignment.getAccrualProfileId()));
        entry.setAccrualProfileName(accrualAssignment.getName());
        entry.setEffectiveDate(adapterHelper.kDateToLocalDate(accrualAssignment.getEffectiveDate()));
        entry.setExpirationDate(adapterHelper.kDateToLocalDate(accrualAssignment.getExpirationDate()));
        return entry;
    }
}