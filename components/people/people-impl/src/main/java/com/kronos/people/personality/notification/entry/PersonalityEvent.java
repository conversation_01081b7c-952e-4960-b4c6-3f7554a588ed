/***********************************************************************
 * PersonalityEvent.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/

package com.kronos.people.personality.notification.entry;

import com.kronos.wfc.commonapp.people.business.personality.Personality;

/**
 * This is wrapper class, which consists personality object, and it's related
 * eventType, and tenantId.
 * 
 * <AUTHOR>
 * <AUTHOR>
 *
 */
public class PersonalityEvent {

	/**
	 * The instance of {@link EventType}
	 */
	private EventType eventType;

	/**
	 * The instance of {@link Personality}
	 */
	private Personality personality;

	/**
	 * The {@link String} tenant id.
	 */
	private String tenantId;

	private String transactionId;


	private Boolean isForcedRefresh;

	/**
	 * Constructor of PersonalityEvent class.
	 * 
	 * @param eventType
	 *            the event type
	 * @param personality
	 *            the personality object
	 * @param tenantId
	 *            the tenant id
	 */

	public PersonalityEvent(EventType eventType, Personality personality, String tenantId,Boolean isForcedRefresh) {
		this.eventType = eventType;
		this.personality = personality;
		this.tenantId = tenantId;
		this.isForcedRefresh=isForcedRefresh;
	}
	public PersonalityEvent(EventType eventType, Personality personality, String tenantId) {
		this.eventType = eventType;
		this.personality = personality;
		this.tenantId = tenantId;
	}
	
	/**
	 * Constructor of PersonalityEvent class.
	 * 
	 * @param personality
	 *            the personality object
	 * @param tenantId
	 *            the tenant id
	 */
	public PersonalityEvent(Personality personality, String tenantId) {
		this.personality = personality;
		this.tenantId = tenantId;
	}

	/**
	 * This method gets the event type.
	 * 
	 * @return the eventType
	 */
	public EventType getEventType() {
		return eventType;
	}

	/**
	 * This method sets the event type.
	 * 
	 * @param eventType
	 *            the eventType to set
	 */
	public void setEventType(EventType eventType) {
		this.eventType = eventType;
	}

	/**
	 * This method gets the personality.
	 * 
	 * @return the personality
	 */
	public Personality getPersonality() {
		return personality;
	}

	/**
	 * This method sets the personality.
	 * 
	 * @param personality
	 *            the personality to set
	 */
	public void setPersonality(Personality personality) {
		this.personality = personality;
	}

	/**
	 * This method gets the tenant id.
	 * 
	 * @return the tenantId
	 */
	public String getTenantId() {
		return tenantId;
	}

	/**
	 * This method sets the tenant id.
	 * 
	 * @param tenantId
	 *            the tenantId to set
	 */
	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getTransactionId() { return transactionId; }

	public void setTransactionId(String trxId) { this.transactionId = trxId; }
	public Boolean getForcedRefresh() {
		return isForcedRefresh;
	}

	public void setForcedRefresh(Boolean forcedRefresh) {
		isForcedRefresh = forcedRefresh;
	}


}
