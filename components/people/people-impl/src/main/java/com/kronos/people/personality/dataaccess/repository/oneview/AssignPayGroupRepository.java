package com.kronos.people.personality.dataaccess.repository.oneview;

import com.kronos.people.personality.dataaccess.entity.oneview.AssignPayGroupEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface AssignPayGroupRepository extends JpaRepository<AssignPayGroupEntity, Long> {
    void deleteByPersonId(Long personId);
    List getByPersonId(Long personId);
    List<AssignPayGroupEntity> findByPersonIdIn(List<Long> personIds);
    List<AssignPayGroupEntity> findByPersonId(Long personId);
}
