/***********************************************************************
 * PersonStatus.java
 *
 * Copyright 2015, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.dataaccess.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * The {@code PersonStatus} entity class for personstatusmm table.
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "personstatusmm")
public class PersonStatus {

	/**
	 * Contains the value of personstatusmmid column.
	 */
	@Id
	@Column(name = "personstatusmmid")
	private Long id;

	/**
	 * Contains the value of personid column.
	 */
	@Column(name = "personid")
	private Long personid;
	
	/**
	 * Contains the value of employmentstatid column.
	 */
	@Column(name = "employmentstatid")
	private Long employeeStatusId;

	/**
	 * Contains the value of useracctstatid column.
	 */
	@Column(name = "useracctstatid")
	private Long userAccountStatusId;

	/**
	 * Contains the value of effectivedtm column.
	 */
	@Column(name = "effectivedtm")
	private LocalDateTime effectivedtm;

	/**
	 * Contains the value of expirationdtm column.
	 */
	@Column(name = "expirationdtm")
	private LocalDateTime expirationdtm;

	/**
	 * @return the id
	 */
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the {@link Long} id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	
	
	/**
	 * @param employeeStatusId
	 *            the {@link Long} employeeStatusId to set
	 */
	public void setEmployeeStatusId(Long employeeStatusId) {
		this.employeeStatusId = employeeStatusId;
	}

	/**
	 * @param userAccountStatusId
	 *            the {@link Long} userAccountStatusId to set
	 */
	public void setUserAccountStatusId(Long userAccountStatusId) {
		this.userAccountStatusId = userAccountStatusId;
	}

	/**
	 * @return the employeeStatusId
	 */
	public Long getEmployeeStatusId() {
		return employeeStatusId;
	}

	/**
	 * @return the userAccountStatusId
	 */
	public Long getUserAccountStatusId() {
		return userAccountStatusId;
	}

	/**
	 * @return the effectivedtm
	 */
	public LocalDateTime getEffectivedtm() {
		return effectivedtm;
	}

	/**
	 * @param effectivedtm
	 *            the {@link LocalDateTime} effectivedtm to set
	 */
	public void setEffectivedtm(LocalDateTime effectivedtm) {
		this.effectivedtm = effectivedtm;
	}

	/**
	 * @return the expirationdtm
	 */
	public LocalDateTime getExpirationdtm() {
		return expirationdtm;
	}

	/**
	 * @param expirationdtm
	 *            the {@link LocalDateTime} expirationdtm to set
	 */
	public void setExpirationdtm(LocalDateTime expirationdtm) {
		this.expirationdtm = expirationdtm;
	}

	public Long getPersonid() {
		return personid;
	}

	public void setPersonid(Long personid) {
		this.personid = personid;
	}

}
