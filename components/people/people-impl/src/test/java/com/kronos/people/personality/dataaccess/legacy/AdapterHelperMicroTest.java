package com.kronos.people.personality.dataaccess.legacy;

import com.kronos.people.CustomArrayList;
import com.kronos.people.personality.Operation;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.util.LogTimeHelper;
import com.kronos.wfc.platform.logging.framework.Log;
import com.kronos.wfc.platform.persistence.framework.ObjectId;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.platform.utility.framework.datetime.KDateTime;
import com.kronos.wfc.platform.utility.framework.datetime.KDateTimeSpan;
import com.kronos.wfc.platform.utility.framework.datetime.KTime;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class AdapterHelperMicroTest {

	private AdapterHelper adapterHelper;

	@BeforeEach
	public void setup() {
		adapterHelper = new AdapterHelper();
	}

	@AfterEach
	public void tearDown() {
		adapterHelper = null;
	}

	@Test
	public void testToObjectRef() {
		ObjectIdLong objectLIdLong = new ObjectIdLong(101);
		assertEquals(Long.valueOf(101),
				adapterHelper.getLongFromObjectIdLong(objectLIdLong));
		assertNull(adapterHelper.getLongFromObjectIdLong(null));
	}

	@Test
	public void testToObjectRefToString() {
		ObjectId objectId = new ObjectIdLong(201);
		ObjectId objectIdAsNull = null;
		assertEquals(Long.valueOf(201),
				adapterHelper.getLongFromObjectId(objectId));
		assertNull(adapterHelper.getLongFromObjectId(objectIdAsNull));
	}



	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Test
	public void testConvertLegacyCollection() {
		Function<String, String> function = x -> x.toUpperCase();
		CustomArrayList<String> fruitListParam = new CustomArrayList();
		fruitListParam.addItem("Orange").addItem("Mango").addItem("Guava")
				.addItem("Grapes").addItem("Pineapple");
		CustomArrayList<String> fruitList = new CustomArrayList();
		fruitList.addItem("ORANGE").addItem("MANGO").addItem("GUAVA")
				.addItem("GRAPES").addItem("PINEAPPLE");
		Collection<String> resultList = adapterHelper.convertLegacyCollection(
				fruitListParam, function);
		assertEquals(fruitList, resultList);
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Test
	public void testConvertLegacyCollectionOther() {
		Function<String, String> function = x -> null;
		CustomArrayList<String> fruitListParam = new CustomArrayList();
		fruitListParam.addItem("Orange").addItem(null).addItem("Guava")
				.addItem("Grapes").addItem("Pineapple");
		CustomArrayList<String> fruitList = new CustomArrayList();
		//fruitList.addItem("ORANGE").addItem("GUAVA")
		//		.addItem("GRAPES").addItem("PINEAPPLE");
		Collection<String> resultList = adapterHelper.convertLegacyCollection(
				fruitListParam, function);
		assertEquals(fruitList, resultList);
	}


	@Test
	public void testKDateToLocalDate() {
		Date date = new Date();
		date.setDate(16);
		date.setMonth(0);
		date.setYear(1998 - 1900);
		System.out.println(date.toString());
		KDate kDate = new KDate(date);
		LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault())
				.toLocalDate();
		LocalDate resultDate = adapterHelper.kDateToLocalDate(kDate);
		System.out.println(resultDate + ">>>>" + localDate);
		assertEquals(localDate, resultDate);
		assertNull(adapterHelper.kDateToLocalDate(null));
		Date dt = new Date(2015-1900, 8, 30);
		System.out.println(dt);
		kDate = new KDate(dt);
		LocalDate ld =(adapterHelper.kDateToLocalDate(kDate));
		assertEquals(ld, LocalDate.of(2015, 9, 30));
		System.out.println(ld);
	}

	@Test
	public void testLocalDateToKdate() {
		LocalDate localDate = LocalDate.of(2015, 10, 30);
		KDate kDate = new KDate(2015, 10, 30);
		KDate resultDate = adapterHelper.localDateToKdate(localDate);
		assertEquals(kDate, resultDate);
		assertNull(adapterHelper.localDateToKdate(null));
	}

	@Test
	public void testKDateTimeToLocalDateTime() {
		KTime kTime = new KTime(10, 10, 30, 744);
		KDate kDate = new KDate(2015, 10, 30);
		KDateTime kDateTime = new KDateTime(kDate, kTime);
		LocalDateTime localDateTime = LocalDateTime.of(2015, 10, 30, 10, 10,
				30, (int) TimeUnit.MILLISECONDS.toNanos(744));
		LocalDateTime resultDateTime = adapterHelper
				.kDateTimeToLocalDateTime(kDateTime);
		assertNull(adapterHelper.kDateTimeToLocalDateTime(null));
		assertEquals(localDateTime, resultDateTime);
	}

	@Test
	public void testLocalDateTimeToKdateTime() throws ParseException {
		LocalDateTime localDateTime = LocalDateTime.of(2015, 10, 30, 10, 10,
				30, (int) TimeUnit.MILLISECONDS.toNanos(744));
		KTime kTime = new KTime(10, 10, 30, 744);
		KDate kDate = new KDate(2015, 10, 30);
		KDateTime kDateTime = new KDateTime(kDate, kTime);
		KDateTime kDateTimeResult = adapterHelper
				.localDateTimeToKdateTime(localDateTime);
		assertEquals(kDateTime, kDateTimeResult);
		assertNull(adapterHelper.localDateTimeToKdateTime(null));
	}

	@Test
	public void testKTimeToLocalTime() {
		KTime kTime = new KTime(10, 10);
		LocalTime localTime = LocalTime.of(10, 10);
		LocalTime localTimeResult = adapterHelper.kTimeToLocalTime(kTime);
		assertEquals(localTime, localTimeResult);
		assertNull(adapterHelper.kTimeToLocalTime(null));
	}

	@Test
	public void testLocalTimeToKtime() {
		LocalTime localTime = LocalTime.of(10, 10, 30,
				(int) TimeUnit.MILLISECONDS.toNanos(744));
		KTime kTime = new KTime(10, 10, 30, 744);
		KTime resultKTime = adapterHelper.localTimeToKtime(localTime);
		assertEquals(kTime, resultKTime);
		assertNull(adapterHelper.localTimeToKtime(null));
	}


	@Test
	public void testLocalDateTimeSpanToKDateTimeSpan() {
		LocalDateTime startLocalDateTime = LocalDateTime.of(1998, 01, 01, 10,
				35, 30, (int) TimeUnit.MILLISECONDS.toNanos(744));
		LocalDateTime endLocalDateTime = LocalDateTime.of(1998, 12, 31, 12, 20,
				40, (int) TimeUnit.MILLISECONDS.toNanos(543));
		KTime startKTime = new KTime(10, 35, 30, 744);
		Date begin = new Date();
		begin.setDate(01);
		begin.setMonth(0);
		begin.setYear(1998 - 1900);
		KDate startKDate = new KDate(begin);
		KDateTime startKDateTime = new KDateTime(startKDate, startKTime);
		KTime endKTime = new KTime(12, 20, 40, 543);
		Date endDate = new Date();
		endDate.setDate(31);
		endDate.setMonth(11);
		endDate.setYear(1998 - 1900);
		KDate endKDate = new KDate(endDate);
		KDateTime endKDateTime = new KDateTime(endKDate, endKTime);
		KDateTimeSpan kDateTimeSpan = new KDateTimeSpan(startKDateTime,
				endKDateTime);
		KDateTimeSpan resultKDateTimeSpan =
				localDateTimeSpanToKDateTimeSpan(startLocalDateTime,
						endLocalDateTime);
		assertEquals(kDateTimeSpan, resultKDateTimeSpan);
		assertNull(localDateTimeSpanToKDateTimeSpan(null,
				endLocalDateTime));
		assertNull(localDateTimeSpanToKDateTimeSpan(
				startLocalDateTime, null));
		assertNull(localDateTimeSpanToKDateTimeSpan(null, null));
	}

	/**
	 * Convert the LocalDateTime BO to the KDateTimeSpan.
	 *
	 * @param startDateTime
	 *            LocalDateTime
	 * @param endDateTime
	 *            LocalDateTime
	 * @return KDateTimeSpan the Kronos Date Time Span
	 */
	public KDateTimeSpan localDateTimeSpanToKDateTimeSpan(LocalDateTime startDateTime, LocalDateTime endDateTime) {
		return (Optional.ofNullable(startDateTime).isPresent() && Optional.ofNullable(endDateTime).isPresent()) ? new KDateTimeSpan(
				adapterHelper.localDateTimeToKdateTime(startDateTime), adapterHelper.localDateTimeToKdateTime(endDateTime)) : null;
	}

	@Test
	public void testDateToLocalDate() {
		Date d = new Date();
		d.setDate(16);
		d.setMonth(0);
		d.setYear(1998 - 1900);
		System.out.println(d);
		LocalDate localDate = LocalDate.of(1998, 1, 16);//d.toInstant().atZone(ZoneId.systemDefault())
		//.toLocalDate();
		LocalDate resultDate = adapterHelper.dateToLocalDate(d);
		assertEquals(localDate, resultDate);

		d = new Date();
		d.setDate(21);
		d.setMonth(11);
		d.setYear(2005 - 1900);
		System.out.println(d);
		localDate = LocalDate.of(2005, 12, 21);//d.toInstant().atZone(ZoneId.systemDefault())
		//.toLocalDate();
		resultDate = adapterHelper.dateToLocalDate(d);
		assertEquals(localDate, resultDate);

		assertNull(adapterHelper.dateToLocalDate(null));
	}








	@SuppressWarnings("static-access")
	@Test
	public void testIfNullNotPresent() {
		String strNull = null;
		assertFalse(adapterHelper.isNullNotPresent(()-> "ORANGE",
				()-> "MANGO", ()->strNull));
		assertTrue(adapterHelper.isNullNotPresent(()-> "ORANGE",
				()-> "MANGO"));
	}

	@SuppressWarnings({ "static-access", "unchecked" })
	@Test
	public void testSetIfNotNull() {
		CustomArrayList<String> list = new CustomArrayList();
		list.addItem("One").addItem("Two").addItem("Three");
		CustomArrayList<String> paramList = new CustomArrayList();
		paramList.addItem("One").addItem("Two");
		String toBeAdded = "Three";
		BiConsumer<List<String>, String> biConsumer = new BiConsumer<List<String>, String>() {
			@Override
			public void accept(List<String> list, String item) {
				list.add(item);
			}
		};
		adapterHelper.setIfNotNull(toBeAdded, paramList, biConsumer);
		adapterHelper.setIfNotNull(null, list, biConsumer);
		assertEquals(list, paramList);

	}

	private void handleDate(LocalDate ld) {
		System.out.println(ld);
	}

	@Test
	public void testSetDateNull() {
		class TestDate {
			LocalDate ld;
			public void setDate(LocalDate l) {
				ld = l;
			}
		}
		TestDate td = new TestDate();
		adapterHelper.setDate(d->td.setDate(d),null);//;td::handleDate, null);
		assertNull(td.ld);
		adapterHelper.setDate(d->td.setDate(d), new KDate());
		assertEquals(LocalDate.now(),td.ld);
		adapterHelper.setDate(d->td.setDate(d), new KDate(2015,12,02));
		assertEquals(LocalDate.of(2015, 12, 2), td.ld);
	}


	@Test
	public void testGetDateFromTime(){
		KTime kTime = new KTime(10, 35, 30, 744);
		Date date = new Date();
		date.setDate(01);
		date.setMonth(0);
		date.setYear(1998 - 1900);
		KDate kDate = new KDate(date);
		KDateTime kDateTime = new KDateTime(kDate, kTime);
		assertEquals(kDate,adapterHelper.getDateFromTime(kDateTime));
	}

	@Test
	public void testGetDateFromTimeNull(){
		assertNull(adapterHelper.getDateFromTime(null));
	}

	@Test
	public void testPutInMap(){

		Map<Long, List<Integer>> tenantMap = new HashMap<>();
		adapterHelper.putInListInsideMap(tenantMap, 1L, Integer.valueOf(1));
		adapterHelper.putInListInsideMap(tenantMap, 2L, Integer.valueOf(2));
		adapterHelper.putInListInsideMap(tenantMap, 1L, Integer.valueOf(1));
		adapterHelper.putInListInsideMap(tenantMap, 2L, Integer.valueOf(2));
		adapterHelper.putInListInsideMap(tenantMap, 3L, Integer.valueOf(3));
		System.out.println(tenantMap.size());
		tenantMap.get(1L).size();
		assertEquals(2, tenantMap.get(1L).size());
		assertEquals(2, tenantMap.get(2L).size());
		assertEquals(1, tenantMap.get(3L).size());
	}

	@Test
	public void testGetIdsNotInSetForEmptyPersonIdCache(){


		Long[] actualPersonIds = {1L, 2L};
		Set<Long> personIdsFromCache = new HashSet<>();
		List<Long> ids = adapterHelper.getIdsNotInSet(actualPersonIds, personIdsFromCache);
		assertEquals(2, ids.size());
	}

	@Test
	public void testGetIdsNotInSet(){


		Long[] actualPersonIds = {3L, 2L};
		Set<Long> personIdsFromCache = new HashSet<>();
		personIdsFromCache.add(3L);
		personIdsFromCache.add(4L);

		List<Long> ids = adapterHelper.getIdsNotInSet(actualPersonIds, personIdsFromCache);
		assertEquals(Long.valueOf(2), ids.get(0));
		System.out.println(ids);
	}



	@SuppressWarnings("static-access")
	@Test
	public void testgetAndLogTime(){
		Long output = LogTimeHelper.getAndLogTime(Log.ERROR, ()->getPersonIds(1L), "Retrieving personIds from db");
		assertEquals(Long.valueOf(1), output);
	}

	private Long getPersonIds(long l) {
		return Long.valueOf(l);
	}

	@SuppressWarnings("static-access")
	@Test
	public void testprocessAndLogTime(){
		Operation oprMock = mock(Operation.class);
		doNothing().when(oprMock);
		LogTimeHelper.processAndLogTime(Log.DEBUG, oprMock, "priming actual call");
		verify(oprMock, times(0)).execute();
		System.out.println("AA");
	}

	@Test
	public void testgetIfPredicatePassWhenPredicateFail() {
		Long output = AdapterHelper.getIfPredicatePass(2L, l->l<2, l->l*100);
		assertNull(output, "Output should be null");
	}

	@Test
	public void testgetIfPredicatePass() {
		Long output = AdapterHelper.getIfPredicatePass(2L, l->l<=2L, l->l*100);
		assertEquals(200L,output.longValue(), "Output should be 200L");
	}

	@Test
	public void testextractIfObjectIdLongNotNull() {
		AdapterHelper adapterHelper = new AdapterHelper();
		ObjectIdLong objId = new ObjectIdLong();
		Long output= adapterHelper.extractIfLegacyNullableNotNull(objId, l->l.longValue());
		assertNull(output, "Output should be null");
		objId= null;
		output = adapterHelper.extractIfLegacyNullableNotNull(objId, l->l.longValue());
		assertNull(output, "Output should be null");
	}

	@Test
	public void testextractIfObjectIdLongNotNullWhenValid() {
		AdapterHelper adapterHelper = new AdapterHelper();
		Long output= adapterHelper.extractIfLegacyNullableNotNull(new ObjectIdLong(2L), l->l.longValue()*100);
		assertEquals(200L,output.longValue(), "Output should be 200L");

	}

	@Test
	public void testgetStringFromLong() {
		AdapterHelper adapterHelper = new AdapterHelper();
		assertEquals("2", adapterHelper.getStringFromLong(2L));
		assertNull(adapterHelper.getStringFromLong(null));
	}

	@Test
	public void testGetObjectIdLong() {
		assertEquals(1L, adapterHelper.getObjectIdLong(1L).longValue());
	}

	@Test
	public void testPutInCollectionInsideMap(){
		Map<Long, Collection<Long>> map = new ConcurrentHashMap<>();
		Long key = 1L;
		List<Long> itemsToBePut = new ArrayList<>();
		itemsToBePut.add(1L);
		itemsToBePut.add(1L);
		itemsToBePut.add(2L);
		adapterHelper.putInCollectionInsideMap(map, key, itemsToBePut, ConcurrentHashMap::newKeySet);
		assertEquals(2, map.get(key).size());

		Map<Long, Collection<Long>> map2 = new ConcurrentHashMap<>();
		Long key2 = 2L;
		List<Long> itemsToBePut2 = new ArrayList<>();
		adapterHelper.putInCollectionInsideMap(map2, key2, itemsToBePut2, ConcurrentHashMap::newKeySet);
		assertEquals(0, map2.get(key2).size());

	}

	@Test
	public void testFilterAndProcessBothPartsOfList() {
		Predicate<String> predicate = s->s.toUpperCase().equals(s);
		List<String> list = new ArrayList<>();
		list.add("abc");
		list.add("SDF");
		list.add("xyz");
		list.add("TEM");
		list.add("other");

		AtomicReference<List<String>> filteredStrings = new AtomicReference<List<String>>();
		AtomicReference<List<String>> remainingStrings = new AtomicReference<List<String>>();
		Consumer<List<String>> filteredConsumer = flist->filteredStrings.set(flist);
		Consumer<List<String>> remainingConsumer = flist->remainingStrings.set(flist);

		adapterHelper.filterAndProcessSubLists(list, predicate, filteredConsumer, remainingConsumer);
		assertTrue(filteredStrings.get().size() == 2);
		assertTrue(remainingStrings.get().size() == 3);
		assertTrue(filteredStrings.get().contains("TEM"));
		assertTrue(filteredStrings.get().contains("SDF"));
		assertTrue(remainingStrings.get().contains("abc"));
		assertTrue(remainingStrings.get().contains("xyz"));
		assertTrue(remainingStrings.get().contains("other"));
	}
}