package com.kronos.people.personality.dataaccess.legacy;

import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.datacollection.udm.service.bioconsentdetails.api.IBioConsentDetailsService;
import com.kronos.datacollection.udm.service.bioconsentdetails.api.dto.BiometricConsentDetails;
import com.kronos.datacollection.udm.service.bioconsentdetails.api.dto.BiometricConsentDetailsDTO;
import com.kronos.datacollection.udm.service.bioconsentdetails.api.enums.BiometricConsentType;
import com.kronos.datacollection.udm.service.bioconsentdetails.api.enums.BiometricType;
import com.kronos.datacollection.udm.service.devicegroup.api.IDeviceGroupService;
import com.kronos.datacollection.udm.service.facescan.api.IFaceScanAttributesService;
import com.kronos.datacollection.udm.service.facescan.api.dto.FaceScan;
import com.kronos.datacollection.udm.service.facescan.api.dto.FaceScanDTO;
import com.kronos.datacollection.udm.service.fingerscan.api.IFingerScanAttributesService;
import com.kronos.datacollection.udm.service.fingerscan.api.dto.FingerScan;
import com.kronos.datacollection.udm.service.fingerscan.api.dto.FingerScanDTO;
import com.kronos.datacollection.udm.service.ttipuserprofile.api.ITTIPUserProfileService;
import com.kronos.datacollection.udm.service.ttipuserprofile.api.dto.TTIPEmployeeAttributes;
import com.kronos.datacollection.udm.service.ttipuserprofile.api.dto.TTIPEmployeeAttributesDTO;
import com.kronos.datacollection.udm.service.ttipuserprofile.api.dto.TTIPUserProfileDTO;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import com.kronos.people.personality.exception.impl.ExceptionHelper;
import com.kronos.people.personality.model.IdentifierType;
import com.kronos.people.personality.properties.KronosPropertiesFacade;
import com.kronos.people.personality.tenant.TenantHandlingFacade;
import com.kronos.wfc.commonapp.currency.business.assignment.EmployeeCurrencyAssignment;
import com.kronos.wfc.commonapp.currency.business.assignment.UserCurrencyAssignment;
import com.kronos.wfc.commonapp.people.bridge.IOrgMapAccessorService;
import com.kronos.wfc.commonapp.people.bridge.OrgSet;
import com.kronos.wfc.commonapp.people.business.person.*;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.personality.PersonalityException;
import com.kronos.wfc.commonapp.people.business.personality.PersonalityTriplet;
import com.kronos.wfc.commonapp.people.business.positions.PositionStatusMapList;
import com.kronos.wfc.commonapp.people.business.positions.PositionStatusSet;
import com.kronos.wfc.commonapp.people.business.user.PersonAuthenticationType;
import com.kronos.wfc.commonapp.people.business.user.RecentEntrySet;
import com.kronos.wfc.commonapp.people.shared.EmploymentStatusMapList;
import com.kronos.wfc.commonapp.people.shared.PersonAnalyticsLaborTypeMapList;
import com.kronos.wfc.commonapp.people.shared.UserAccountStatusMapList;
import com.kronos.wfc.commonapp.processmanager.business.workflow.WorkflowAccessAssignment;
import com.kronos.wfc.platform.extensibility.framework.GuestManager;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.business.authentication.types.AuthenticationType;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonalityFacadeMicroTest {

    @InjectMocks
    private PersonalityFacade personalityFacade;

    @Mock
    TenantHandlingFacade tenantHandlingFacade;
    @Mock
    KronosPropertiesFacade propertiesFacade;
    @Mock
    KronosThreadPoolService threadPoolService;
    @Mock
    ExecutorService executor;
    @Mock
    Personality p;

    @BeforeEach
    public void setUp() throws Exception {
        when(propertiesFacade.getIntegerKronosProperty(any(),anyInt())).thenReturn(100);
        when(threadPoolService.newThreadPool(any())).thenReturn(executor);
        personalityFacade.setExceptionHelper(new ExceptionHelper(null));
        personalityFacade.setAdapterHelper(new AdapterHelper());
    }

    @Test
    public void testGetProcessManagerProfileName() {
        AccessAssignment accessAssignment = mock(AccessAssignment.class);
        when(accessAssignment.getPersonId()).thenReturn(new ObjectIdLong(233));
        class WorkflowAccessAssignmentMock extends WorkflowAccessAssignment {
            ObjectIdLong employeeWorkflowProfileId = new ObjectIdLong(1234L);
            ObjectIdLong managerWorkflowProfileId = new ObjectIdLong(4321L);


            public WorkflowAccessAssignmentMock(AccessAssignment accessAssignment) {
                super(accessAssignment);
            }

            @Override
            public ObjectIdLong getEmployeeWorkflowProfileId() {
                return employeeWorkflowProfileId;
            }

            @Override
            public ObjectIdLong getManagerWorkflowProfileId() {
                return managerWorkflowProfileId;
            }
        }
        personalityFacade = new PersonalityFacade(null,null,null,null,null,null,null, null) {
            @Override
            public WorkflowAccessAssignment getWorkflowAssignment(AccessAssignment accessAssignment) {
                WorkflowAccessAssignment wfa = new WorkflowAccessAssignmentMock(accessAssignment);
                return wfa;
            }
        };
        Map<String, Object> profileMap = personalityFacade.getProcessManagerProfileName(accessAssignment);
        assertEquals(new ObjectIdLong(1234L), profileMap.get(PersonalityConstants.EMPLOYEE_WORKFLOW_PROFILE_ID.getValue()));
        assertEquals(new ObjectIdLong(4321L), profileMap.get(PersonalityConstants.MANAGER_WORKFLOW_PROFILE_ID.getValue()));
    }

    @Test
    public void testGetLegacyPersonDates() {
        List<CustomDate> customDateList = new ArrayList<>();
        Personality personality = mock(Personality.class);
        CustomDate customDate1 = mock(CustomDate.class);
        when(customDate1.getCustomDateTypeId()).thenReturn(new ObjectIdLong(1));
        when(customDate1.getActualCustomDate()).thenReturn(new KDate(2015, 8, 17));
        when(customDate1.getShortName()).thenReturn("401K Enrollment Date");
        customDateList.add(customDate1);

        CustomDate customDate2 = mock(CustomDate.class);
        when(customDate2.getCustomDateTypeId()).thenReturn(new ObjectIdLong(4));
        when(customDate2.getActualCustomDate()).thenReturn(new KDate(2015, 11, 9));
        when(customDate2.getShortName()).thenReturn("Union Hire Date");

        customDateList.add(customDate2);

        CustomDate customDate3 = mock(CustomDate.class);
        when(customDate3.getCustomDateTypeId()).thenReturn(new ObjectIdLong(5));
        when(customDate3.getActualCustomDate()).thenReturn(new KDate(2015, 6, 10));
        when(customDate3.getShortName()).thenReturn("WTK Union Date");

        customDateList.add(customDate3);

        CustomDateSet customDateSet = spy(CustomDateSet.class);
        when(customDateSet.getTypeCollection()).thenReturn(customDateList);
        when(personality.getCustomDates()).thenReturn(customDateSet);

        /*
         * PersonDateAttribute reportBeanAttribute1 =
         * mock(PersonDateAttribute.class);
         * when(reportBeanAttribute1.getName()).
         * thenReturn("401K Enrollment Date"); ReportDateBean reportDateBean =
         * mock(ReportDateBean.class); reportDateBean.
         * when(reportBeanAttribute1.getDefaultDate()).thenReturn();
         * when(reportBeanAttribute1.getOverrideDate()).thenReturn();
         */

		/*for (ReportBeanAttribute reportBeanAttribute : personalityFacade.getLegacyPersonDates(personality)) {
			System.out.println(reportBeanAttribute.getName());
		}*/

        // System.out.println(personality.getCustomDates().getCustomDate(new
        // ObjectIdLong(4)).getActualCustomDate());
    }

    @Test
    public void testGetLegacyTelNumbers() {
        Personality personality = mock(Personality.class);

        ArrayList<TelephoneNumber> telephoneList = new ArrayList<>();
        TelephoneNumber telephoneNumber1 = mock(TelephoneNumber.class);
        when(telephoneNumber1.getContactTypeId()).thenReturn(new ObjectIdLong(1));
        when(telephoneNumber1.getPhoneNumber()).thenReturn("************");
        when(telephoneNumber1.getPersonId()).thenReturn(new ObjectIdLong(223));
        telephoneList.add(telephoneNumber1);

        TelephoneNumber telephoneNumber2 = mock(TelephoneNumber.class);
        when(telephoneNumber2.getContactTypeId()).thenReturn(new ObjectIdLong(2));
        when(telephoneNumber2.getPhoneNumber()).thenReturn("************");
        when(telephoneNumber2.getPersonId()).thenReturn(new ObjectIdLong(223));
        telephoneList.add(telephoneNumber2);

        TelephoneNumberSet telephoneNumberSet = mock(TelephoneNumberSet.class);
        when(telephoneNumberSet.collection()).thenReturn(telephoneList);

        when(personality.getTelephoneNumbers()).thenReturn(telephoneNumberSet);

        assertEquals(telephoneList, personalityFacade.getLegacyTelNumbers(personality));

    }

    @Test
    public void testGetLegacyEmailAdress() {
        Personality personality = mock(Personality.class);

        ArrayList<EMailAddress> emailAddressList = new ArrayList<>();
        EMailAddress emailAddress1 = mock(EMailAddress.class);
        when(emailAddress1.getContactTypeId()).thenReturn(new ObjectIdLong(4));
        when(emailAddress1.getEmailAddress()).thenReturn("<EMAIL>");
        when(emailAddress1.getPersonId()).thenReturn(new ObjectIdLong(223));
        emailAddressList.add(emailAddress1);

        EMailAddress emailAddress2 = mock(EMailAddress.class);
        when(emailAddress2.getContactTypeId()).thenReturn(new ObjectIdLong(4));
        when(emailAddress2.getEmailAddress()).thenReturn("<EMAIL>");
        when(emailAddress2.getPersonId()).thenReturn(new ObjectIdLong(223));
        emailAddressList.add(emailAddress2);

        EMailAddressSet emailAddressSet = mock(EMailAddressSet.class);
        when(emailAddressSet.collection()).thenReturn(emailAddressList);

        when(personality.getEmailAddresses()).thenReturn(emailAddressSet);

        assertEquals(emailAddressList, personalityFacade.getLegacyEmailAdress(personality));
    }

    @Test
    public void testGetLegacyPostalAddress() {
        Personality personality = mock(Personality.class);

        ArrayList<PostalAddress> postalAddressList = new ArrayList<>();

        PostalAddress postalAddress1 = mock(PostalAddress.class);
        when(postalAddress1.getContactTypeId()).thenReturn(new ObjectIdLong(5));
        when(postalAddress1.getCountry()).thenReturn("Test Country1");
        when(postalAddress1.getCity()).thenReturn("New City1");
        when(postalAddress1.getState()).thenReturn("New State1");
        when(postalAddress1.getPersonId()).thenReturn(new ObjectIdLong(223));
        when(postalAddress1.getPostalCode()).thenReturn("110077");
        when(postalAddress1.getStreet()).thenReturn("New Street1");
        postalAddressList.add(postalAddress1);

        PostalAddress postalAddress2 = mock(PostalAddress.class);
        when(postalAddress2.getContactTypeId()).thenReturn(new ObjectIdLong(5));
        when(postalAddress2.getCountry()).thenReturn("Test Country2");
        when(postalAddress2.getCity()).thenReturn("New City2");
        when(postalAddress2.getState()).thenReturn("New State2");
        when(postalAddress2.getPersonId()).thenReturn(new ObjectIdLong(223));
        when(postalAddress2.getPostalCode()).thenReturn("110066");
        when(postalAddress2.getStreet()).thenReturn("New Street2");
        postalAddressList.add(postalAddress2);

        PostalAddressSet postalAddressSet = mock(PostalAddressSet.class);
        when(postalAddressSet.collection()).thenReturn(postalAddressList);

        when(personality.getPostalAddresses()).thenReturn(postalAddressSet);

        assertEquals(postalAddressList, personalityFacade.getLegacyPostalAddresses(personality));
    }

    @Test
    public void findPersonIdNullTest() {
        personalityFacade = new PersonalityFacade(null,null,null,null,null,null,null, null);
        personalityFacade.setExceptionHelper(new ExceptionHelper(null));
        try {
            personalityFacade.findPersonality(null);
        } catch (PersonalityExtensionException ex) {
            assertEquals("PersonId is null, invalid parameter", ex.getDebugMessage());
            assertEquals(PersonalityErrorCode.NULL_PARAM.getDefaultMessage(), ex.getLocalizedMessage());
        }

    }

    @SuppressWarnings("deprecation")
    @Test
    public void findPersonalityTest() {
        personalityFacade = new PersonalityFacade(null,null,null,null,null,null,null, null) {
            @Override
            protected Personality getPersonalityBy(Long personId) {
                // TODO Auto-generated method stub
                return new Personality();
            }
        };


        assertNotNull(personalityFacade.findPersonality(123l));

    }

    @Test
    public void findPersonalityNullTest() {
        personalityFacade = new PersonalityFacade(null,null,null,null,null,null,null, null) {
            @Override
            protected Personality getPersonalityBy(Long personId) {
                // TODO Auto-generated method stub
                return null;
            }
        };

        try {
            personalityFacade.setExceptionHelper(new ExceptionHelper(null));
            personalityFacade.findPersonality(123l);
        } catch (PersonalityExtensionException ex) {
            assertEquals("Person not found, invalid parameter", ex.getDebugMessage());
            assertEquals(PersonalityErrorCode.NOT_FOUND.getDefaultMessage(), ex.getLocalizedMessage());
        }

    }

    @Test
    public void testInit() {
        //personalityFacade.init();
        //assertNotNull(personalityFacade.identifierMap.get(IdentifierType.BADGENUMBER));
        assertNotNull(personalityFacade.identifierMap.get(IdentifierType.FULLNAME));
        assertNotNull(personalityFacade.identifierMap.get(IdentifierType.JOBASSIGNMENTID));
        assertNotNull(personalityFacade.identifierMap.get(IdentifierType.PERSONNUMBER));
        assertNotNull(personalityFacade.identifierMap.get(IdentifierType.USERACCOUNTID));
        assertNotNull(personalityFacade.identifierMap.get(IdentifierType.USEREMAILADDRESS));
        assertNotNull(personalityFacade.identifierMap.get(IdentifierType.USERNAME));
        assertNotNull(personalityFacade.identifierMap.get(IdentifierType.PERSONID));
    }

    @Disabled
    @Test
    //stop testing your mocks! this is not a real test, it is faking code coverage numbers, write a real one!
    public void testGetPersonalityByThrowingPersonalityException() {
        doThrow(new PersonalityException(2)).when(personalityFacade).getPersonality(any());
        try {
            personalityFacade.getPersonalityBy(null);
            fail("Should have thrown exception.");
        } catch (PersonalityExtensionException pe) {
            assertEquals("Employee not found", pe.getDebugMessage());
            assertEquals("NOT_FOUND", pe.getErrorCode());
        }
    }

    @Disabled @Test
    //this is not a real test, it's faking code coverage metrics, write a real one!
    public void testGetPersonalityByThrowingException() {
        doThrow(new RuntimeException("My custom exception")).when(personalityFacade).getPersonality(any());
        try {
            personalityFacade.getPersonalityBy(null);
            fail("Should have thrown exception.");
        } catch (PersonalityExtensionException pe) {
            assertEquals("UNKNOWN_ERROR", pe.getErrorCode());
            assertEquals("My custom exception", pe.getDebugMessage());
        }
    }

    @Disabled @Test
    //this is not a real test, it's faking code coverage metrics, write a real one!
    public void testGetPersonalityBy() {
        doReturn(p).when(personalityFacade).getPersonality(any());
        try {
            Personality p1 = personalityFacade.getPersonalityBy(null);
            assertEquals(p, p1);
        } catch (PersonalityExtensionException pe) {
            fail("Should not have thrown exception.");
        }
    }

    static class MockAuthenticationType extends AuthenticationType {
        public MockAuthenticationType(String displayName) {
            this.displayName = displayName;
        }

        String displayName;

        @Override
        public String getDisplayName() {
            // TODO Auto-generated method stub
            return this.displayName;
        }

    }

    static class MockPersonAuthenticationType extends PersonAuthenticationType {
        boolean active;
        String displayName;

        public MockPersonAuthenticationType(boolean active, String displayName) {
            this.active = active;
            this.displayName = displayName;
        }

        public boolean getActive() {
            return this.active;
        }

        public AuthenticationType getAuthenticationType() {
            return displayName != null ? new MockAuthenticationType(displayName) : null;
        }

        /* (non-Javadoc)
         * @see java.lang.Object#toString()
         */
        @Override
        public String toString() {
            return "MockPersonAuthenticationType [active=" + active
                    + ", displayName=" + displayName + "]";
        }

        ;


    }

    @Test
    public void testConvertAuthenticationTypeEmpty() {
        Collection<PersonAuthenticationType> personAuthTypeSet = new ArrayList<PersonAuthenticationType>();
        assertNull(personalityFacade.convertAuthenticationType(personAuthTypeSet));
    }

    @Test
    public void testConvertAuthenticationTypeNonTrue() {
        Collection<PersonAuthenticationType> personAuthTypeSet = new ArrayList<PersonAuthenticationType>();
        PersonAuthenticationType paType = new MockPersonAuthenticationType(false, "False name");
        personAuthTypeSet.add(paType);
        assertNull(personalityFacade.convertAuthenticationType(personAuthTypeSet));
    }

    @Test
    public void testConvertAuthenticationTypeNameNull() {
        Collection<PersonAuthenticationType> personAuthTypeSet = new ArrayList<PersonAuthenticationType>();
        personAuthTypeSet.add(new MockPersonAuthenticationType(false, "Some name"));
        personAuthTypeSet.add(new MockPersonAuthenticationType(true, null));
        assertNull(personalityFacade.convertAuthenticationType(personAuthTypeSet));
    }

    @Test
    public void testConvertAuthenticationTypeNameNotNull() {
        Collection<PersonAuthenticationType> personAuthTypeSet = new ArrayList<PersonAuthenticationType>();
        personAuthTypeSet.add(new MockPersonAuthenticationType(false, "Some name"));
        personAuthTypeSet.add(new MockPersonAuthenticationType(true, null));
        personAuthTypeSet.add(new MockPersonAuthenticationType(true, "MockName"));
        assertNull(personalityFacade.convertAuthenticationType(personAuthTypeSet));

        personAuthTypeSet = new ArrayList<PersonAuthenticationType>();
        personAuthTypeSet.add(new MockPersonAuthenticationType(false, "Some name"));
        personAuthTypeSet.add(new MockPersonAuthenticationType(true, "MockName"));
        assertEquals("MockName", personalityFacade.convertAuthenticationType(personAuthTypeSet).getDisplayName());
    }

    @Test
    public void testGetDapAssignment() {
        final Personality personality = mock(Personality.class);
        final GuestManager guestManager = mock(GuestManager.class);
        when(personality.getGuestManager()).thenReturn(guestManager);
        assertNull(personalityFacade.getDapAssignment(personality));
    }

    @Test @Disabled
    //this is not a real test, it's just faking code coverage, write one that does something!
    public void testCreateEmployeeCurrencyAssignment() {
        EmployeeCurrencyAssignment eca = mock(EmployeeCurrencyAssignment.class);
        assertEquals(eca, personalityFacade.createEmployeeCurrencyAssignment(new ObjectIdLong(23L), eca));

        doReturn(null).when(personalityFacade).createEmployeeCurrencyAssignment(any(), any());
        assertNull(personalityFacade.getEmployeeCurrencyAssignment(null));
    }

    @Test @Disabled
    //this is not a real test, it's just faking code coverage, write one that does something!
    public void testCreateUserCurrencyAssignment() {
        UserCurrencyAssignment uca = mock(UserCurrencyAssignment.class);
        assertEquals(uca, personalityFacade.createUserCurrencyAssignment(new ObjectIdLong(23L), uca));

        doReturn(null).when(personalityFacade).createUserCurrencyAssignment(any(), any());
        assertNull(personalityFacade.getUserCurrencyAssignment(null));
    }

    @Test
    public void testGetPersonalityTriplet() {
        PersonalityTriplet mp = mock(PersonalityTriplet.class);
        Function<Object, PersonalityTriplet> function = t -> mp;
        personalityFacade.identifierMap.put(IdentifierType.FULLNAME, function);
        assertEquals(mp, personalityFacade.getPersonalityTriplet(23L, IdentifierType.FULLNAME));
    }

    @Test
    public void testgetObjectIdLong() {
        assertEquals(new ObjectIdLong(1), personalityFacade.getObjectIdLong(1L));
    }


    @Test
    public void testGetLegacyEmploymentStatus() {
        PersonStatusSet statusDataMock = mock(PersonStatusSet.class);

        when(statusDataMock.getStatuses(any(), any())).thenReturn(new ArrayList());
        Collection<EmploymentStatusMapList> lst = personalityFacade.getLegacyEmploymentStatus(statusDataMock);
        assertNotNull(lst);
    }

    @Test
    public void testGetLegacyPositionStatus() {
        PositionStatusSet statusDataMock = mock(PositionStatusSet.class);

        when(statusDataMock.getAllStatuses(any(), any())).thenReturn(Collections.emptyList());
        Collection<PositionStatusMapList> lst = personalityFacade.getLegacyPositionStatus(statusDataMock);
        assertNotNull(lst);
    }
    
    @Test
    public void testGetLegacyAnalyticsLaborTypes() {
        PersonAnalyticsLaborTypeSet analyticsLaborTypeDataMock = mock(PersonAnalyticsLaborTypeSet.class);
        when(analyticsLaborTypeDataMock.getAllAnalyticsLaborTypes(any(),any())).thenReturn(new ArrayList());
        Collection<PersonAnalyticsLaborTypeMapList> laborTypelist = personalityFacade.getLegacyAnalyticsLaborTypes(analyticsLaborTypeDataMock);
        assertNotNull(laborTypelist);
    }
    
    @SuppressWarnings("deprecation")
    @Test
    public void testGetLegacyUserAccountStatus() {
        PersonStatusSet statusDataMock = mock(PersonStatusSet.class);
        when(statusDataMock.getStatuses(any(),any())).thenReturn(new ArrayList());
        Collection<UserAccountStatusMapList> lst = personalityFacade.getLegacyUserAccountStatus(statusDataMock);
        assertNotNull(lst);
    }

    @Test
    public void testUserAccountIdBeforeRecentEntry() {
        StringBuilder strb = new StringBuilder();
        personalityFacade = getMyMock(null, null, strb);
        assertNull(personalityFacade.getRecentEntries(null));
        assertEquals(0, strb.length());

        personalityFacade = getMyMock(new ObjectIdLong(), null, strb);
        assertNull(personalityFacade.getRecentEntries(null));
        assertEquals(0, strb.length());

        personalityFacade = getMyMock(new ObjectIdLong(2L), null, strb);
        assertNull(personalityFacade.getRecentEntries(null));
        assertEquals("getRecentEntrySetForUserAccountId called".length(), strb.length());

        RecentEntrySet res = mock(RecentEntrySet.class);
        strb.setLength(0);
        when(res.collection()).thenReturn(null);
        personalityFacade = getMyMock(new ObjectIdLong(2L), res, strb);
        assertNull(personalityFacade.getRecentEntries(null));
        assertEquals("getRecentEntrySetForUserAccountId called".length(), strb.length());

        strb.setLength(0);

        ArrayList mockList = new ArrayList();
        when(res.collection()).thenReturn(mockList);
        personalityFacade = getMyMock(new ObjectIdLong(2L), res, strb);
        assertEquals(mockList, personalityFacade.getRecentEntries(null));
        assertEquals("getRecentEntrySetForUserAccountId called".length(), strb.length());

    }

    @Test
    public void testSetOrgMapAccessorService() {
        IOrgMapAccessorService oMapAccessorService = Mockito.mock(IOrgMapAccessorService.class);
        personalityFacade.setOrgMapAccessorService(oMapAccessorService);
        assertEquals(oMapAccessorService, personalityFacade.orgMapAccessorService);
    }

    @Test
    public void testFindOrgMapGroupByID() {
        IOrgMapAccessorService oMapAccessorService = Mockito.mock(IOrgMapAccessorService.class);
        PersonalityFacade personalityFacade = new PersonalityFacade(null,null,null,null,null,null,null, null) {
            public IOrgMapAccessorService getOrgMapAccessorService() {
                return oMapAccessorService;
            }
        };
        OrgSet orgSet = new OrgSet();
        when(oMapAccessorService.findOrgMapGroupByIdWithoutNodes(any())).thenReturn(orgSet);
        assertEquals(orgSet, personalityFacade.findOrgMapGroupById(new ObjectIdLong(1L)));
    }

    @Test
    public void testGetOrgMapAccessorService() {
        PersonalityFacade personalityFacade = new PersonalityFacade(null,null,null,null,null,null,null, null);
        personalityFacade.orgMapAccessorService = Mockito.mock(IOrgMapAccessorService.class);
        assertNotNull(personalityFacade.getOrgMapAccessorService());
    }

    @Test
    public void testGetDeviceGroupIdByPersonId() {
        IDeviceGroupService deviceGroupService = mock(IDeviceGroupService.class);
        when(deviceGroupService.getDeviceGroupIdByEmployeeId(3L)).thenReturn(5L);

        ObjectIdLong personId = mock(ObjectIdLong.class);
        when(personId.toString()).thenReturn("3");

        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null);
        pf.setAdapterHelper(new AdapterHelper());
        pf.setDeviceGroupService(deviceGroupService);

        Long actual = pf.getDeviceGroupIdByPersonId(personId);
        assertEquals(5L, actual.longValue());

        actual = pf.getDeviceGroupIdByPersonId(null);
        assertNull(actual);
    }

    @Test
    public void testGetTTIPEmployeeAttributes() {
    	TTIPUserProfileDTO ttipUserProfileDTO = new TTIPUserProfileDTO(5L, "name");
        TTIPEmployeeAttributesDTO ttipEmployeeAttributesDTO = 
        		new TTIPEmployeeAttributesDTO(ttipUserProfileDTO, "10", false);
        
        ITTIPUserProfileService ttipUserProfileService = mock(ITTIPUserProfileService.class);

        when(ttipUserProfileService.getTTIPEmployeeAttributesByPersonId(3L)).thenReturn(ttipEmployeeAttributesDTO);

        ObjectIdLong personId = mock(ObjectIdLong.class);
        when(personId.toString()).thenReturn("3");

        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null);
        pf.setAdapterHelper(new AdapterHelper());
        pf.setTtipUserProfileService(ttipUserProfileService);

        TTIPEmployeeAttributes actual = pf.getTTIPEmployeeAttributes(personId);
        assertNotNull(actual);

        assertEquals(ttipEmployeeAttributesDTO, actual);
    }

    @Test
    public void testGetTTIPEmployeeAttributesNull() {
        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null);
        pf.setAdapterHelper(new AdapterHelper());
        TTIPEmployeeAttributes actual = pf.getTTIPEmployeeAttributes(null);
        assertNull(actual);
    }

    @Test
    public void testGetFingerScanByPersonId() {
        IFingerScanAttributesService fingerScanAttributesService = Mockito.mock(IFingerScanAttributesService.class);
        FingerScanDTO fingerScanDTO = new FingerScanDTO();
        fingerScanDTO.setEnrolled(true);
        fingerScanDTO.setEnrolledForIdentification(true);
        fingerScanDTO.setPrimaryFingerThreshold("High");
        fingerScanDTO.setPrimaryFingerEnrollmentLocation("My Device (025096)");

        when(fingerScanAttributesService.getFingerScanByPersonId(1L)).thenReturn(fingerScanDTO);

        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null);
        pf.setAdapterHelper(new AdapterHelper());
        pf.setFingerScanAttributesService(fingerScanAttributesService);

        ObjectIdLong personId = mock(ObjectIdLong.class);
        when(personId.toString()).thenReturn("1");
        FingerScan actual = pf.getFingerScanByPersonId(personId);
        assertEquals(fingerScanDTO, actual);
    }

    @Test
    public void testGetFingerScanByPersonIdNull() {
        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null);
        pf.setAdapterHelper(new AdapterHelper());
        FingerScan actual = pf.getFingerScanByPersonId(null);
        assertNull(actual);
    }

    @Test
    public void testGetPrimaryFingerEnrollmentLocation() {
        IFingerScanAttributesService fingerScanAttributesService = Mockito.mock(IFingerScanAttributesService.class);
        when(fingerScanAttributesService.getPrimaryFingerEnrollmentLocation(1L)).thenReturn("My Device (025096)");

        PersonalityFacade pf = new PersonalityFacade(null, null, null, null, null, null, null, null);
        pf.setAdapterHelper(new AdapterHelper());
        pf.setFingerScanAttributesService(fingerScanAttributesService);

        ObjectIdLong personId = mock(ObjectIdLong.class);
        when(personId.toString()).thenReturn("1");
        String actual = pf.getPrimaryFingerEnrollmentLocation(personId);
        assertEquals("My Device (025096)", actual);
        assertNull(pf.getPrimaryFingerEnrollmentLocation(null));
    }

    @Test
    public void testGetFaceScanByPersonId() {
        final long personId = 1L;
        final boolean isEnrolled = true;
        final String thresholdLabel = "Normal";
        final Integer threshold = 5;
        final Integer qualityScore = 90;
        final boolean isDeleted = false;
        final String enrollmentLocation = "device";
        final LocalDateTime dateTime = LocalDateTime.now();


        FaceScanDTO faceScan = new FaceScanDTO();
        faceScan.setIsEnrolled(isEnrolled);
        faceScan.setThresholdLabel(thresholdLabel);
        faceScan.setThreshold(threshold);
        faceScan.setDeleted(isDeleted);
        faceScan.setEnrollmentLocation(enrollmentLocation);
        faceScan.setEnrollmentDateTime(dateTime);
        faceScan.setQualityScore(qualityScore);


        IFaceScanAttributesService faceScanAttributesService = Mockito.mock(IFaceScanAttributesService.class);
        when(faceScanAttributesService.getFaceScanByPersonId(personId)).thenReturn(faceScan);

        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null);
        pf.setAdapterHelper(new AdapterHelper());
        pf.setFaceScanAttributesService(faceScanAttributesService);

        ObjectIdLong personObjectId = new ObjectIdLong(personId);
        FaceScan actualFaceScan = pf.getFaceScanByPersonId(personObjectId);
        assertEquals(isEnrolled, actualFaceScan.getIsEnrolled());
        assertEquals(thresholdLabel, actualFaceScan.getThresholdLabel());
        assertEquals(threshold, actualFaceScan.getThreshold());
        assertEquals(qualityScore, actualFaceScan.getQualityScore());
        assertEquals(isDeleted, actualFaceScan.getIsDeleted());
        assertEquals(enrollmentLocation, actualFaceScan.getEnrollmentLocation());
        assertEquals(dateTime, actualFaceScan.getEnrollmentDateTime());
    }

    @Test
    public void testIsFingerEnrolled() {
        IFingerScanAttributesService fingerScanAttributesService = Mockito.mock(IFingerScanAttributesService.class);
        when(fingerScanAttributesService.isFingerEnrolled(1L)).thenReturn(Boolean.TRUE);
        when(fingerScanAttributesService.isFingerEnrolled(2L)).thenReturn(Boolean.FALSE);

        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null);
        pf.setAdapterHelper(new AdapterHelper());
        pf.setFingerScanAttributesService(fingerScanAttributesService);

        ObjectIdLong personId = mock(ObjectIdLong.class);
        when(personId.toString()).thenReturn("1");
        assertTrue(pf.isFingerEnrolled(personId));

        when(personId.toString()).thenReturn("2");
        assertFalse(pf.isFingerEnrolled(personId));
        assertFalse(pf.isFingerEnrolled(null));
    }

    @Test
    public void testIsFingerEnrolledForIdentification() {
        IFingerScanAttributesService fingerScanAttributesService = Mockito.mock(IFingerScanAttributesService.class);
        when(fingerScanAttributesService.isFingerEnrolledForIdentification(1L)).thenReturn(Boolean.TRUE);
        when(fingerScanAttributesService.isFingerEnrolledForIdentification(2L)).thenReturn(Boolean.FALSE);

        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null);
        pf.setAdapterHelper(new AdapterHelper());
        pf.setFingerScanAttributesService(fingerScanAttributesService);

        ObjectIdLong personId = mock(ObjectIdLong.class);
        when(personId.toString()).thenReturn("1");
        assertTrue(pf.isFingerEnrolledForIdentification(personId));

        when(personId.toString()).thenReturn("2");
        assertFalse(pf.isFingerEnrolledForIdentification(personId));
        assertFalse(pf.isFingerEnrolledForIdentification(null));
    }

    @Test
    public void testGetPrimaryFingerThreshold() {
        IFingerScanAttributesService fingerScanAttributesService = Mockito.mock(IFingerScanAttributesService.class);
        when(fingerScanAttributesService.getPrimaryFingerThreshold(1L)).thenReturn("High");

        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null);
        pf.setAdapterHelper(new AdapterHelper());
        pf.setFingerScanAttributesService(fingerScanAttributesService);

        ObjectIdLong personId = mock(ObjectIdLong.class);
        when(personId.toString()).thenReturn("1");
        String actual = pf.getPrimaryFingerThreshold(personId);
        assertEquals("High", actual);
        assertNull(pf.getPrimaryFingerThreshold(null));
    }

    @Test
    public void testGetBiometricConsentDetailsByPersonIdAndConsentType() {
        final long personId = 1L;
        final BiometricConsentType consentType = BiometricConsentType.AFFIRMATIVE;
        final LocalDateTime dateTime = LocalDateTime.now();
        final String consentLocation = "device";
        final String consentForm = "consentForm";
        final String consentText = "consentText";

        BiometricConsentDetailsDTO consentDetails = new BiometricConsentDetailsDTO();
        consentDetails.setConsentStatus(consentType);
        consentDetails.setConsentDateTime(dateTime);
        consentDetails.setConsentLocation(consentLocation);
        consentDetails.setConsentForm(consentForm);
        consentDetails.setConsentFormText(consentText);


        IBioConsentDetailsService bioConsentDetailsService = Mockito.mock(IBioConsentDetailsService.class);
        when(bioConsentDetailsService.getBiometricConsentDetailsByPersonIdAndBiometricType(personId, BiometricType.FACE_TYPE)).thenReturn(consentDetails);

        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null);
        pf.setAdapterHelper(new AdapterHelper());
        pf.setBioConsentDetailsService(bioConsentDetailsService);

        ObjectIdLong personObjectId = new ObjectIdLong(personId);
        BiometricConsentDetails actualConsentDetails = pf.getBiometricConsentDetailsByPersonIdAndConsentType(personObjectId, BiometricType.FACE_TYPE);
        assertEquals(consentType, actualConsentDetails.getConsentStatus());
        assertEquals(dateTime, actualConsentDetails.getConsentDateTime());
        assertEquals(consentLocation, actualConsentDetails.getConsentLocation());
        assertEquals(consentForm, actualConsentDetails.getConsentForm());
        assertEquals(consentText, actualConsentDetails.getConsentFormText());
    }

    @Test
    public void testGetBiometricConsentDetailsByPersonIdAndNullConsentType() {
        final long personId = 1L;
        final BiometricConsentType consentType = BiometricConsentType.AFFIRMATIVE;
        final LocalDateTime dateTime = LocalDateTime.now();
        final String consentLocation = "device";
        final String consentForm = "consentForm";
        final String consentText = "consentText";

        BiometricConsentDetailsDTO consentDetails = new BiometricConsentDetailsDTO();
        consentDetails.setConsentStatus(consentType);
        consentDetails.setConsentDateTime(dateTime);
        consentDetails.setConsentLocation(consentLocation);
        consentDetails.setConsentForm(consentForm);
        consentDetails.setConsentFormText(consentText);


        IBioConsentDetailsService bioConsentDetailsService = Mockito.mock(IBioConsentDetailsService.class);
        when(bioConsentDetailsService.getBiometricConsentDetailsByPersonIdAndBiometricType(personId, BiometricType.FACE_TYPE)).thenReturn(consentDetails);

        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null);
        pf.setAdapterHelper(new AdapterHelper());
        pf.setBioConsentDetailsService(bioConsentDetailsService);

        ObjectIdLong personObjectId = new ObjectIdLong(personId);
        BiometricConsentDetails actualConsentDetails = pf.getBiometricConsentDetailsByPersonIdAndConsentType(personObjectId, null);
        assertNull(actualConsentDetails);
    }

    @Test
    public void testGetBiometricConsentDetailsByNullPersonIdAndConsentType() {
        final long personId = 1L;
        final BiometricConsentType consentType = BiometricConsentType.AFFIRMATIVE;
        final LocalDateTime dateTime = LocalDateTime.now();
        final String consentLocation = "device";
        final String consentForm = "consentForm";
        final String consentText = "consentText";

        BiometricConsentDetailsDTO consentDetails = new BiometricConsentDetailsDTO();
        consentDetails.setConsentStatus(consentType);
        consentDetails.setConsentDateTime(dateTime);
        consentDetails.setConsentLocation(consentLocation);
        consentDetails.setConsentForm(consentForm);
        consentDetails.setConsentFormText(consentText);


        IBioConsentDetailsService bioConsentDetailsService = Mockito.mock(IBioConsentDetailsService.class);
        when(bioConsentDetailsService.getBiometricConsentDetailsByPersonIdAndBiometricType(personId, BiometricType.FACE_TYPE)).thenReturn(consentDetails);

        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null);
        pf.setAdapterHelper(new AdapterHelper());
        pf.setBioConsentDetailsService(bioConsentDetailsService);

        ObjectIdLong personObjectId = new ObjectIdLong(personId);
        BiometricConsentDetails actualConsentDetails = pf.getBiometricConsentDetailsByPersonIdAndConsentType(null, BiometricType.FACE_TYPE);
        assertNull(actualConsentDetails);
    }

    @Test
    public void testGetFaceScanByNullPersonId() {
        final long personId = 1L;
        final boolean isEnrolled = true;
        final String thresholdLabel = "Normal";
        final Integer threshold = 5;
        final Integer qualityScore = 90;
        final boolean isDeleted = false;
        final String enrollmentLocation = "device";
        final LocalDateTime dateTime = LocalDateTime.now();


        FaceScanDTO faceScan = new FaceScanDTO();
        faceScan.setIsEnrolled(isEnrolled);
        faceScan.setThresholdLabel(thresholdLabel);
        faceScan.setThreshold(threshold);
        faceScan.setDeleted(isDeleted);
        faceScan.setEnrollmentLocation(enrollmentLocation);
        faceScan.setEnrollmentDateTime(dateTime);
        faceScan.setQualityScore(qualityScore);


        IFaceScanAttributesService faceScanAttributesService = Mockito.mock(IFaceScanAttributesService.class);
        when(faceScanAttributesService.getFaceScanByPersonId(personId)).thenReturn(faceScan);

        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null);
        pf.setAdapterHelper(new AdapterHelper());
        pf.setFaceScanAttributesService(faceScanAttributesService);

        ObjectIdLong personObjectId = new ObjectIdLong(personId);
        FaceScan actualFaceScan = pf.getFaceScanByPersonId(null);
        assertNull(actualFaceScan);
    }

    private PersonalityFacade getMyMock(ObjectIdLong userAccountId, RecentEntrySet recentEntrySet, StringBuilder strb) {
        PersonalityFacade pf = new PersonalityFacade(null,null,null,null,null,null,null, null) {
            @Override
            protected ObjectIdLong getUserAccountId(ObjectIdLong personId) {
                return userAccountId;
            }

            @Override
            protected RecentEntrySet getRecentEntrySetForUserAccountId(ObjectIdLong userAccountId) {
                strb.append("getRecentEntrySetForUserAccountId called");
                return recentEntrySet;
            }
        };
        pf.adapterHelper = new AdapterHelper();
        return pf;
    }
}
