package com.kronos.people.personality.model;

import static com.kronos.people.personality.model.IdentifierType.PERSONID;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.kronos.people.personality.model.extension.BasicPojoMicroTest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CriteriaMicroTest extends BasicPojoMicroTest {

	public CriteriaMicroTest() {
		super(Criteria.class);
	}

	@Test
	public void testIdType() {

		Object[] ids = {123L, 1234L};
		Criteria criteria = new Criteria(ids, PERSONID);

		assertEquals(PERSONID, criteria.getIdsType());

		criteria.setIdsType(PERSONID);
		assertEquals(PERSONID, criteria.getIdsType());
	}

	@Test
	public void testForActivePerson() {

		Criteria criteria = new Criteria(null, null, true);
//		assertEquals(true, criteria.isOnlyActivePerson());
        assertTrue(criteria.isOnlyActivePerson());

		criteria = new Criteria(null, null, null, false);
//		assertEquals(false, criteria.isOnlyActivePerson());
        assertFalse(criteria.isOnlyActivePerson());

		criteria.setOnlyActivePerson(true);
//		assertEquals(true, criteria.isOnlyActivePerson());
        assertTrue(criteria.isOnlyActivePerson());

//		assertEquals(false, new Criteria().isOnlyActivePerson());
        assertFalse(new Criteria().isOnlyActivePerson());
	}

	@Test
	public void testForvalidData() {

//		assertEquals(true, PERSONID.isInputDataValid(1234l));
        assertTrue(PERSONID.isInputDataValid(1234L));
//		assertEquals(false, IdentifierType.USEREMAILADDRESS.isInputDataValid(1234l));
        assertFalse(IdentifierType.USEREMAILADDRESS.isInputDataValid(1234L));
        assertFalse(PERSONID.isInputDataValid(null));
	}
	
	@Test
	public void testForSnapshotDateSetter() {

		Criteria criteria = new Criteria(null, PERSONID, LocalDate.of(2015, 8, 12));
		assertEquals(LocalDate.of(2015, 8, 12), criteria.getSnapShotDate());

		criteria = new Criteria();
		criteria.setSnapShotDate(LocalDate.of(2015, 8, 12));
		assertEquals(LocalDate.of(2015, 8, 12), criteria.getSnapShotDate());
	}

//	@SuppressWarnings("deprecation")
	@Test
	public void testForIds() {
		Object[] ids = {123L, 1234L};
		Criteria criteria = new Criteria(ids, PERSONID);
		assertEquals(ids, criteria.getIds());

		criteria.setIds(ids);
		assertEquals(ids, criteria.getIds());
	}

	@Test
	public void testGetSnapshotDate_SetSnapshotDateTime(){
		//Given
		Criteria criteria = new Criteria();

		//When
		criteria.setSnapshotDateTime(LocalDateTime.MAX);

		//Then
		assertNotNull(criteria.getSnapShotDate());
		assertEquals(LocalDate.MAX, criteria.getSnapShotDate());
		assertEquals(LocalDateTime.MAX, criteria.getSnapshotDateTime());
	}

}
