/*******************************************************************************
 * FingerScanAttributesServiceTest.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.dataaccess.legacy.impl;

import com.ukg.container.selectiveloading.exception.NotImplementedException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * The class to unit test methods of FingerScanAttributesService.
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class FingerScanAttributesServiceTest {

    @InjectMocks
    private FingerScanAttributesService fingerScanAttributesService;

    @Test
    public void testIsFingerEnrolled() {
        assertThrows(NotImplementedException.class, () -> {
            fingerScanAttributesService.isFingerEnrolled(123L);
        });
    }

    @Test
    public void testGetPrimaryFingerThreshold() {
        assertThrows(NotImplementedException.class, () -> {
            fingerScanAttributesService.getPrimaryFingerThreshold(123L);
        });
    }

    @Test
    public void isGetPrimaryFingerEnrollmentLocation() {
        assertThrows(NotImplementedException.class, () -> {
            fingerScanAttributesService.getPrimaryFingerEnrollmentLocation(123L);
        });
    }

    @Test
    public void testGetFingerScanByPersonId() {
        assertThrows(NotImplementedException.class, () -> {
            fingerScanAttributesService.getFingerScanByPersonId(123L);
        });
    }

    @Test
    public void testisFingerEnrolledForIdentification() {
        assertThrows(NotImplementedException.class, () -> {
            fingerScanAttributesService.isFingerEnrolledForIdentification(123L);
        });
    }
}