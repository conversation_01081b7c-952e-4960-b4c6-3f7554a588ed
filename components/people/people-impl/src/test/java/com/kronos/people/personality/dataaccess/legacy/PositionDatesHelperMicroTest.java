package com.kronos.people.personality.dataaccess.legacy;

import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.model.extension.entry.positions.PositionCustomDataEntry;
import com.kronos.people.personality.model.extension.entry.positions.PositionDatesEntry;
import com.kronos.wfc.commonapp.people.business.positions.*;
import com.kronos.wfc.commonapp.types.business.CustomDataType;
import com.kronos.wfc.commonapp.types.business.CustomDateType;
import com.kronos.wfc.platform.persistence.framework.ObjectId;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.PersistentIterator;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PositionDatesHelperMicroTest {

    private static final ObjectIdLong DEFAULT_DATE_ID = new ObjectIdLong(123L);

    private PositionDatesHelper positionDatesHelper;

    @Mock
    private IEmployeePosition position;
    @Mock
    private PositionDetails positionDetails;
    @Mock
    private CustomDateType customDateTypeMock;
    @Mock
    private PositionCustomDateSet customDateSetMock;
    @Mock
    private PositionCustomDate customDateMock;
    @Mock
    private CustomDateType defaultDateType;
    @Mock
    private PositionCustomDate defaultDate;

    private MockedStatic<CustomDataType> mockedCustomDataType;
    private MockedStatic<CustomDateType> mockedCustomDateType;

    @BeforeEach
    public void setUp() {
        mockedCustomDataType = mockStatic(CustomDataType.class);
        mockedCustomDateType = mockStatic(CustomDateType.class);
        mockedCustomDateType.when(CustomDateType::getNonSiteWideTypes).thenReturn(Collections.singletonList(customDateTypeMock));
        when(defaultDateType.getSiteWidedDateTime()).thenReturn(KDate.create(2019, 10, 10));
        when(defaultDateType.getObjectId()).thenReturn(DEFAULT_DATE_ID);
        mockedCustomDateType.when(()->CustomDateType.getCustomDateType((ObjectId) Mockito.any())).thenReturn(customDateTypeMock);
        mockedCustomDateType.when(()->CustomDateType.getCustomDateType(DEFAULT_DATE_ID)).thenReturn(defaultDateType);
        positionDatesHelper = new PositionDatesHelper();
        positionDatesHelper.setConverter(new AdapterHelper());
    }

    @AfterEach
    public void tearDown() {
        mockedCustomDataType.close();
        mockedCustomDateType.close();
    }

    @Test
    public void testGetPositionDates() {
        when(position.getPositionCustomDates()).thenReturn(null);
        List<PositionDatesEntry> actualList = positionDatesHelper.getPositionDates(position);
        assertEquals(0, actualList.size());

        when(defaultDateType.getSiteWideScopeSwitch()).thenReturn(0L);
        mockCustomDate();
        List<PositionDatesEntry> actualList2 = positionDatesHelper.getPositionDates(position);
        assertEquals(1, actualList2.size());
    }

    @Test
    public void testGetPositionDatesWithSiteWideDefault() {
        when(defaultDateType.getSiteWideScopeSwitch()).thenReturn(1L);
        mockCustomDate();
        List<PositionDatesEntry> actualList2 = positionDatesHelper.getPositionDates(position);
        assertEquals(1, actualList2.size());
    }

    @Test
    public void testGetPositionDatesWithDefaultDate() {
        when(defaultDateType.getSiteWideScopeSwitch()).thenReturn(0L);
        when(customDateSetMock.getPositionCustomDate(DEFAULT_DATE_ID)).thenReturn(defaultDate);
        when(defaultDate.getActualCustomDate()).thenReturn(KDate.create(2020, 1, 1));
        mockCustomDate();
        List<PositionDatesEntry> actualList2 = positionDatesHelper.getPositionDates(position);
        assertEquals(1, actualList2.size());
    }

    @Test
    public void testGetPositionDatesFromHireDate() {
        when(defaultDateType.getSiteWideScopeSwitch()).thenReturn(0L);
        when(customDateSetMock.getPositionCustomDate(DEFAULT_DATE_ID)).thenReturn(defaultDate);
        mockCustomDate();
        List<PositionDatesEntry> actualList2 = positionDatesHelper.getPositionDates(position);
        assertEquals(1, actualList2.size());
    }

    private void mockCustomDate() {
        when(customDateMock.getShortName()).thenReturn("abc");
        when(customDateMock.getActualCustomDate()).thenReturn(KDate.create(2019, 12, 18));
        when(customDateMock.getCustomDateType()).thenReturn(customDateTypeMock);
        PersistentIterator persistentIteratorMock = mock(PersistentIterator.class);
        when(persistentIteratorMock.hasNext()).thenReturn(true, false);
        when(persistentIteratorMock.next()).thenReturn(customDateMock);
        when(customDateSetMock.iterator(Mockito.any())).thenReturn(persistentIteratorMock);
        when(customDateSetMock.size()).thenReturn(1);
        when(position.getPositionCustomDates()).thenReturn(customDateSetMock);
        when(customDateTypeMock.getDefCustomDateTypId()).thenReturn(DEFAULT_DATE_ID);
    }

    @Test
    public void testGetPersonCustomDataWhenCustomDatesNull() {
        List<PositionCustomDataEntry> pce = positionDatesHelper.getPositionCustomData(position);
        assertEquals(0, pce.size());
    }

    @Test
    public void testGetPersonCustomData() {
        PositionCustomDataSet customDataSetMock = mock(PositionCustomDataSet.class);
        when(position.getPositionCustomData()).thenReturn(customDataSetMock);
        PersistentIterator persistentIteratorMock = mock(PersistentIterator.class);
        when(customDataSetMock.iterator()).thenReturn(persistentIteratorMock);
        Mockito.when(persistentIteratorMock.hasNext()).thenReturn(true, false);
        PositionCustomData customDataMockm = mock(PositionCustomData.class);
        Mockito.when(persistentIteratorMock.next()).thenReturn(customDataMockm);
        when(customDataMockm.getCustomDataTypeId()).thenReturn(new ObjectIdLong(3));
        when(customDataMockm.getCustomText()).thenReturn("customText");
        when(customDataMockm.getVersionCount()).thenReturn(1L);
        List<PositionCustomDataEntry> pce = positionDatesHelper.getPositionCustomData(position);
        assertEquals(1, pce.size());
    }
}
