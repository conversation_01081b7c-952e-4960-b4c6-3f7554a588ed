package com.kronos.people.personality.dataaccess.adapter;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.access.SpringContext;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.model.extension.SchedulingExtension;
import com.kronos.people.personality.model.extension.entry.*;
import com.kronos.people.proxy.api.service.AccessAssignmentProxyService;
import com.kronos.people.proxy.api.service.MultiManagerRoleProxyService;
import com.kronos.releasetoggle.api.ReleaseToggleService;
import com.kronos.wfc.commonapp.namedentity.business.dap.DapAssignment;
import com.kronos.wfc.commonapp.namedentity.business.dap.EmployeeDapAssignment;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignment;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignmentDetails;
import com.kronos.wfc.commonapp.people.business.person.*;
import com.kronos.wfc.commonapp.people.business.person.group.PersonGroup;
import com.kronos.wfc.commonapp.people.business.person.group.PersonGroupAssignment;
import com.kronos.wfc.commonapp.people.business.person.group.PersonGroupAssignmentSet;
import com.kronos.wfc.commonapp.people.business.person.predsched.PredictiveSchedulingEligibilityRecord;
import com.kronos.wfc.commonapp.people.business.person.predsched.PredictiveSchedulingEligibilityRecordsManager;
import com.kronos.wfc.commonapp.people.business.person.predschedoverride.PredictiveSchedulingOverrideRecord;
import com.kronos.wfc.commonapp.people.business.person.predschedoverride.PredictiveSchedulingOverrideRecordsManager;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.types.business.TimePeriodType;
import com.kronos.wfc.commonapp.types.business.WorkerType;
import com.kronos.wfc.platform.extensibility.framework.GuestManager;
import com.kronos.wfc.platform.member.framework.DateMember;
import com.kronos.wfc.platform.member.framework.DateProperties;
import com.kronos.wfc.platform.member.framework.ObjectIdLongMember;
import com.kronos.wfc.platform.member.framework.ObjectIdLongProperties;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KConstants;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.scheduling.core.business.SchedEmployeeService;
import com.kronos.wfc.scheduling.request.business.setup.paycodeprofile.PayCodeProfileAssignmentService;
import com.kronos.wfc.scheduling.selfscheduling.business.people.SseAccessAssignment;
import com.kronos.wfc.scheduling.selfscheduling.business.people.SseAccessAssignmentFactory;
import com.kronos.wfc.scheduling.workload.business.zonecategory.PreferredZoneCategoryService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class SchedulingExtensionAdapterMicroTest {

	private SchedulingExtensionAdapter schedulingExtensionAdapter;
	private AdapterHelper adapterHelper;
	private Personality personality;
	private SchedulingExtension schedulingExtension;
	private PersonalityFacade personalityFacade;

	private MockedStatic<PredictiveSchedulingEligibilityRecordsManager> mockedPredictiveSchedulingEligibilityRecordsManager;
	private MockedStatic<PredictiveSchedulingOverrideRecordsManager> mockedPredictiveSchedulingOverrideRecordsManager;
	private MockedStatic<SseAccessAssignmentFactory> mockedSseAccessAssignmentFactory;
	private MockedStatic<SpringContext> mockedSpringContext;
	private MockedStatic<SchedEmployeeService> mockedSchedEmployeeService;
	private MockedStatic<PreferredZoneCategoryService> mockedPreferredZoneCategoryService;
	private MockedStatic<PayCodeProfileAssignmentService> mockedPayCodeProfileAssignmentService;

	@Mock
	private PredictiveSchedulingEligibilityRecordsManager eligibilityRecordsManager;
	@Mock
	private PredictiveSchedulingOverrideRecordsManager predictiveSchedulingOverrideRecordsManager;
	@Mock
	private SseAccessAssignmentFactory sseAccessAssignmentFactory;

	@Mock
	private SchedEmployeeService schedEmployeeService;

	@Mock
	private PreferredZoneCategoryService preferredZoneCategoryService;

	@Mock
	private PayCodeProfileAssignmentService payCodeProfileAssignmentService;

	@BeforeEach
	public void setup() {
		schedulingExtensionAdapter = spy(SchedulingExtensionAdapter.class);
		adapterHelper = new AdapterHelper();
    	schedulingExtensionAdapter.setAdapterHelper(adapterHelper);
		schedulingExtensionAdapter.setPersonalityFacade(mock(PersonalityFacade.class));
		personality = mock(Personality.class);
		personalityFacade = mock(PersonalityFacade.class);
		schedulingExtension = new SchedulingExtension();
		schedulingExtensionAdapter.setPersonalityFacade(personalityFacade);
		mockedPredictiveSchedulingEligibilityRecordsManager = Mockito.mockStatic(PredictiveSchedulingEligibilityRecordsManager.class);
		mockedPredictiveSchedulingOverrideRecordsManager = Mockito.mockStatic(PredictiveSchedulingOverrideRecordsManager.class);
		mockedSseAccessAssignmentFactory = Mockito.mockStatic(SseAccessAssignmentFactory.class);
		mockedSpringContext = Mockito.mockStatic(SpringContext.class);
		mockedSchedEmployeeService = Mockito.mockStatic(SchedEmployeeService.class);
		mockedPreferredZoneCategoryService = Mockito.mockStatic(PreferredZoneCategoryService.class);
		mockedPayCodeProfileAssignmentService = Mockito.mockStatic(PayCodeProfileAssignmentService.class);

		mockedPredictiveSchedulingEligibilityRecordsManager.when(() -> PredictiveSchedulingEligibilityRecordsManager.getInstance(personality)).thenReturn(eligibilityRecordsManager);

		mockedPredictiveSchedulingEligibilityRecordsManager.when(() -> PredictiveSchedulingOverrideRecordsManager.getInstance(personality)).thenReturn(predictiveSchedulingOverrideRecordsManager);

		mockedSseAccessAssignmentFactory.when(() -> SseAccessAssignmentFactory.getInstance()).thenReturn(sseAccessAssignmentFactory);

		mockedSchedEmployeeService.when(() -> SchedEmployeeService.getInstance()).thenReturn(schedEmployeeService);

		mockedPreferredZoneCategoryService.when(() -> PreferredZoneCategoryService.getInstance()).thenReturn(preferredZoneCategoryService);

		mockedPayCodeProfileAssignmentService.when(() -> PayCodeProfileAssignmentService.getInstance()).thenReturn(payCodeProfileAssignmentService);
	}

	@AfterEach
	public void teardown(){
		mockedPredictiveSchedulingEligibilityRecordsManager.close();
		mockedPredictiveSchedulingOverrideRecordsManager.close();
		mockedSseAccessAssignmentFactory.close();
		mockedSpringContext.close();
		mockedSchedEmployeeService.close();
		mockedPreferredZoneCategoryService.close();
		mockedPayCodeProfileAssignmentService.close();
	}

	private void mockStatic() {
		MultiManagerRoleProxyService multiManagerRoleProxyService = mock(MultiManagerRoleProxyService.class);
		when(multiManagerRoleProxyService.isUserOnMultiManagerRole(any())).thenReturn(false);
		mockedSpringContext.when(() -> SpringContext.getBean(MultiManagerRoleProxyService.class)).thenReturn(multiManagerRoleProxyService);
		IKProperties ikProperties = mock(IKProperties.class);
		mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(ikProperties);
		when(ikProperties.getPropertyAsBoolean(anyString(), anyBoolean())).thenReturn(false);
		ReleaseToggleService releaseToggleService = mock(ReleaseToggleService.class);
		mockedSpringContext.when(() -> SpringContext.getBean(ReleaseToggleService.class)).thenReturn(releaseToggleService);
		when(releaseToggleService.getValue(anyString())).thenReturn(true);
	}

	@Test
	public void testConvertWithFilledData(){
		SchedulingExtension schedulingExtension = mock(SchedulingExtension.class);
		ObjectIdLong objectIdLong = mock(ObjectIdLong.class);
		when(objectIdLong.toString()).thenReturn("223");
		when(personality.getExpectedHours()).thenReturn(mock(ExpectedHoursSet.class));
		JobAssignment jobAssignment = mock(JobAssignment.class);
		when(personality.getJobAssignment()).thenReturn(jobAssignment);
		//when(personality.getJobAssignment()).thenReturn(mock(JobAssignment.class));
		AccessAssignment accessAssignment = mock(AccessAssignment.class);
		when(personality.getAccessAssignment()).thenReturn(accessAssignment);
		//when(personality.getAccessAssignment()).thenReturn(mock(AccessAssignment.class));
		//when(personality.getPersonAccessAssignmentSet()).thenReturn(mock(PersonAccessAssignmentSet.class));
		GuestManager guestManager = mock(GuestManager.class);
		when(personality.getGuestManager()).thenReturn(guestManager);
		when(personality.getPersonId()).thenReturn(objectIdLong);
		when(personality.getPersonNumber()).thenReturn("Person Number");
		when(personality.isActive()).thenReturn(true);
		when(personality.getApprovers()).thenReturn(mock(ApproverSet.class));
		SseAccessAssignment sseAccessAssignment = mock(SseAccessAssignment.class);
		when(sseAccessAssignmentFactory.createSseAccessAssignment(personality.getPersonId())).thenReturn(sseAccessAssignment);
		//when(sseAccessAssignmentFactory.createSseAccessAssignment(personality.getPersonId())).thenReturn(mock(SseAccessAssignment.class));
		DapAssignment dapAssignment = mock(DapAssignment.class);
		when(personalityFacade.getDapAssignment(personality)).thenReturn(dapAssignment);
		schedulingExtensionAdapter.convert(personality,null);

		verify(schedulingExtensionAdapter,times(1)).setPersonAttributes(any(),any());
		verify(schedulingExtensionAdapter,times(1)).setExpectedHoursAttributes(any(),any());
		verify(schedulingExtensionAdapter,times(1)).setJobAssignmentAttributes(any(),any());
		verify(schedulingExtensionAdapter,times(1)).setAccessAssignmentAttributes(any(),any());
		verify(schedulingExtension,times(0)).setSchoolCalendarDapId(any());
		verify(schedulingExtension,times(0)).setForecastingCategoryProfileId(any());
		verify(schedulingExtensionAdapter,times(0)).setJobTransferSet(schedulingExtension,null);
		verify(schedulingExtensionAdapter, times(1)).setSelfScheduleEmployeeShiftTemplateProfile(any(), any());
		verify(schedulingExtensionAdapter, times(1)).setSkillList(any(), any());
		verify(schedulingExtensionAdapter, times(1)).setCertificationList(any(), any());
		verify(schedulingExtensionAdapter, times(1)).setPreferredZoneCategory(any(), any());
		verify(schedulingExtensionAdapter, times(1)).setPayCodeProfile(any(), any());
		//verify(schedulingExtensionAdapter,times(1)).setJobTransferSet(any(), any());
	}

	@Test
	public void testSetSelfScheduleEmployeeShiftTemplateProfile() {
		SseAccessAssignment sseAccessAssignment = mock(SseAccessAssignment.class);
		when(sseAccessAssignmentFactory.createSseAccessAssignment(any())).thenReturn(sseAccessAssignment);
		when(sseAccessAssignment.getSseShiftCodeId()).thenReturn(new ObjectIdLong(7));

		schedulingExtensionAdapter.setSelfScheduleEmployeeShiftTemplateProfile(schedulingExtension, personality);
		assertEquals(Long.valueOf(7),schedulingExtension.getSseShiftCodeId());
	}

	@Test
   public void convert_whenPredictiveEligibilityRecordsAreExist_thenConvertThem() {
      PredictiveSchedulingEligibilityRecord eligibilityRecord = new PredictiveSchedulingEligibilityRecord(1L, 1L, true, KDate.createDate(), KDate.getEotDate());
      when(eligibilityRecordsManager.getAllEligibilityRecords()).thenReturn(singletonList(eligibilityRecord));
		SseAccessAssignment sseAccessAssignment = mock(SseAccessAssignment.class);
		when(sseAccessAssignmentFactory.createSseAccessAssignment(any())).thenReturn(sseAccessAssignment);
		DapAssignment dapAssignment = mock(DapAssignment.class);
		when(personalityFacade.getDapAssignment(personality)).thenReturn(dapAssignment);
      SchedulingExtension actual = schedulingExtensionAdapter.convert(personality,null);

      assertEquals(1, actual.getPredictiveSchedulingEligibilityEntries().size());
      assertEquals(convertPredictiveEligibilityRecordToEntry(eligibilityRecord), actual.getPredictiveSchedulingEligibilityEntries().iterator().next());
   }

	@Test
	public void convert_whenPredictiveSchedulingOverrideRecordsAreExist_thenConvertThem() {
		PredictiveSchedulingOverrideRecord schedulingOvertimeRecord = new PredictiveSchedulingOverrideRecord(1L, 1L, 1L, KDate.createDate(), KDate.getEotDate());
		when(predictiveSchedulingOverrideRecordsManager.getAllOverrideRecords()).thenReturn(singletonList(schedulingOvertimeRecord));
		SseAccessAssignment sseAccessAssignment = mock(SseAccessAssignment.class);
		when(sseAccessAssignmentFactory.createSseAccessAssignment(any())).thenReturn(sseAccessAssignment);
		DapAssignment dapAssignment = mock(DapAssignment.class);
		when(personalityFacade.getDapAssignment(personality)).thenReturn(dapAssignment);
		SchedulingExtension actual = schedulingExtensionAdapter.convert(personality,null);

		assertEquals(1, actual.getPredictiveSchedulingOverrideEntries().size());
		assertEquals(convertPredictiveScheduleOverrideRecordToEntry(schedulingOvertimeRecord), actual.getPredictiveSchedulingOverrideEntries().iterator().next());
	}

	@Test
	public void testGetApproverDataEntry() {
		Approver approver=mock(Approver.class);
		ObjectIdLong objectIdLong = mock(ObjectIdLong.class);
		ApproverEntry approverEntry=new ApproverEntry();
		when(objectIdLong.longValue()).thenReturn(1L);
		when(approver.getMgrId()).thenReturn(objectIdLong);
		when(approver.getDueDateAmount()).thenReturn(12);
		when(approver.getOrderNum()).thenReturn(11);
		when(approver.getVersionCount()).thenReturn(10L);
		approverEntry = schedulingExtensionAdapter.getApproverDataEntry(approver);
		assertEquals(Long.valueOf(1),approverEntry.getEmployeeId());
		assertEquals(Integer.valueOf(12),approverEntry.getDueDateAmt());
		assertEquals(Integer.valueOf(11),approverEntry.getOrderNumber());
		assertEquals(Long.valueOf(10),approverEntry.getVersionCount());
	}

	@Test
	public void testSetPersonAttributes(){
    	when(personality.getPersonId()).thenReturn(new ObjectIdLong(223l));
    	when(personality.getPersonNumber()).thenReturn("223");
    	when(personality.isActive()).thenReturn(true);
		DapAssignment dapAssignment = mock(DapAssignment.class);
		when(dapAssignment.getSchoolCalendarDapId()).thenReturn(new ObjectIdLong(131));
		when(dapAssignment.getForecastMapDapId()).thenReturn(new ObjectIdLong(151));
		when(personalityFacade.getDapAssignment(personality)).thenReturn(dapAssignment);
    	schedulingExtensionAdapter.setPersonAttributes(personality, schedulingExtension);
    	assertEquals(Long.valueOf(223),schedulingExtension.getPersonId());
    	assertEquals("223",schedulingExtension.getPersonNumber());
    	assertEquals(true,schedulingExtension.isActive());
    	assertEquals(Long.valueOf(131),schedulingExtension.getSchoolCalendarDapId());

	}


	@Test
	public void testSetPersonAttributes_whenDapAssignmentNotIncludeForecastMapDapId(){
		when(personality.getPersonId()).thenReturn(new ObjectIdLong(223l));
		when(personality.getPersonNumber()).thenReturn("223");
		when(personality.isActive()).thenReturn(true);
		DapAssignment dapAssignment = mock(DapAssignment.class);
		when(dapAssignment.getSchoolCalendarDapId()).thenReturn(new ObjectIdLong(131));
		when(personalityFacade.getDapAssignment(personality)).thenReturn(dapAssignment);
		schedulingExtensionAdapter.setPersonAttributes(personality, schedulingExtension);
		assertEquals(Long.valueOf(223),schedulingExtension.getPersonId());
		assertEquals("223",schedulingExtension.getPersonNumber());
		assertEquals(true,schedulingExtension.isActive());
		assertNull(schedulingExtension.getForecastingCategoryProfileId());

	}

	@Test
	public void testSetPersonAttributes_whenDapAssignmentIncludeForecastMapDapId(){
		when(personality.getPersonId()).thenReturn(new ObjectIdLong(223l));
		when(personality.getPersonNumber()).thenReturn("223");
		when(personality.isActive()).thenReturn(true);
		DapAssignment dapAssignment = mock(DapAssignment.class);
		when(dapAssignment.getForecastMapDapId()).thenReturn(new ObjectIdLong(131));
		when(personalityFacade.getDapAssignment(personality)).thenReturn(dapAssignment);
		schedulingExtensionAdapter.setPersonAttributes(personality, schedulingExtension);
		assertEquals(Long.valueOf(223),schedulingExtension.getPersonId());
		assertEquals("223",schedulingExtension.getPersonNumber());
		assertEquals(true,schedulingExtension.isActive());
		assertNotNull(schedulingExtension.getForecastingCategoryProfileId());
		assertEquals(Long.valueOf(131),schedulingExtension.getForecastingCategoryProfileId());

	}
	@Test
	public void testSetPersonAttributes_whenPersonalityIncludeForecastMapDapId(){
		when(personality.getPersonId()).thenReturn(new ObjectIdLong(223l));
		when(personality.getPersonNumber()).thenReturn("223");
		when(personality.isActive()).thenReturn(true);
		EmployeeDapAssignment employeeDapAssignment = new EmployeeDapAssignment();
		employeeDapAssignment.setForecastMapDapId(new ObjectIdLong(1L));
		DapAssignment dapAssignment = mock(DapAssignment.class);
		when(personalityFacade.getDapAssignment(personality)).thenReturn(dapAssignment);
		personality.setDapAssignment(employeeDapAssignment);
		when(personality.getDapAssignment()).thenReturn(employeeDapAssignment);
		schedulingExtensionAdapter.setPersonAttributes(personality, schedulingExtension);
		assertEquals(Long.valueOf(223),schedulingExtension.getPersonId());
		assertEquals("223",schedulingExtension.getPersonNumber());
		assertEquals(true,schedulingExtension.isActive());
		assertNotNull(schedulingExtension.getForecastingCategoryProfileId());
		assertEquals(Long.valueOf(1),schedulingExtension.getForecastingCategoryProfileId());

	}

	@Test
	public void testSetPersonAttributes_whenPersonalityNotIncludeForecastMapDapId(){
		when(personality.getPersonId()).thenReturn(new ObjectIdLong(223l));
		when(personality.getPersonNumber()).thenReturn("223");
		when(personality.isActive()).thenReturn(true);
		EmployeeDapAssignment employeeDapAssignment = new EmployeeDapAssignment();
		DapAssignment dapAssignment = mock(DapAssignment.class);
		when(personalityFacade.getDapAssignment(personality)).thenReturn(dapAssignment);
		when(personality.getDapAssignment()).thenReturn(employeeDapAssignment);
		schedulingExtensionAdapter.setPersonAttributes(personality, schedulingExtension);
		assertEquals(Long.valueOf(223),schedulingExtension.getPersonId());
		assertEquals("223",schedulingExtension.getPersonNumber());
		assertEquals(true,schedulingExtension.isActive());
		assertNull(schedulingExtension.getForecastingCategoryProfileId());

	}

	@Test
	public void testSetExpectedHoursAttributes(){
		ExpectedHours expHoursDaily = mock(ExpectedHours.class);
		ObjectIdLong objectIdLong = mock(ObjectIdLong.class);
		when(objectIdLong.toString()).thenReturn("223");
		when(expHoursDaily.getPersonId()).thenReturn(objectIdLong);
		when(expHoursDaily.getExpectedHoursQuantity()).thenReturn(Double.valueOf(7.500000));
//		when(objectIdLong.toString()).thenReturn("1");
//		when(expHoursDaily.getTimePeriodTypeId()).thenReturn(objectIdLong);
//		when(expHoursDaily.getTimePeriodType()).thenReturn(mock(TimePeriodType.class));

		ExpectedHours expHoursWeekly = mock(ExpectedHours.class);
		when(objectIdLong.toString()).thenReturn("223");
		when(expHoursWeekly.getPersonId()).thenReturn(objectIdLong);
		when(expHoursWeekly.getExpectedHoursQuantity()).thenReturn(Double.valueOf(45.000000));
//		when(objectIdLong.toString()).thenReturn("2");
//		when(expHoursWeekly.getTimePeriodTypeId()).thenReturn(objectIdLong);
//		when(expHoursWeekly.getTimePeriodType()).thenReturn(mock(TimePeriodType.class));

		ExpectedHours expHoursPayPeriod = mock(ExpectedHours.class);
		when(objectIdLong.toString()).thenReturn("223");
		when(expHoursPayPeriod.getPersonId()).thenReturn(objectIdLong);
		when(expHoursPayPeriod.getExpectedHoursQuantity()).thenReturn(Double.valueOf(75.000000));
//		when(objectIdLong.toString()).thenReturn("5");
//		when(expHoursPayPeriod.getTimePeriodTypeId()).thenReturn(objectIdLong);
//		when(expHoursPayPeriod.getTimePeriodType()).thenReturn(mock(TimePeriodType.class));


		ExpectedHoursSet expectedHoursSet = mock(ExpectedHoursSet.class);
		when(expectedHoursSet.getExpectedHours(TimePeriodType.DAILY)).thenReturn(expHoursDaily);
		when(expectedHoursSet.getExpectedHours(TimePeriodType.WEEKLY)).thenReturn(expHoursWeekly);
		when(expectedHoursSet.getExpectedHours(TimePeriodType.PAY_PERIOD)).thenReturn(expHoursPayPeriod);

		when(personality.getExpectedHours()).thenReturn(expectedHoursSet);

		schedulingExtensionAdapter.setExpectedHoursAttributes(personality, schedulingExtension);

		ExpectedHours expHoursWithNullQty = mock(ExpectedHours.class);
		when(objectIdLong.toString()).thenReturn("223");
		when(expHoursWithNullQty.getPersonId()).thenReturn(objectIdLong);
		when(expHoursWithNullQty.getExpectedHoursQuantity()).thenReturn(null);

		assertEquals(schedulingExtension.getExpectedDailyHours(),Double.valueOf(7.500000));
		assertEquals(schedulingExtension.getExpectedWeeklyHours(),Double.valueOf(45.000000));
		assertEquals(schedulingExtension.getExpectedByPayPeriodHours(),Double.valueOf(75.000000));

		assertEquals(Double.valueOf(7.500000),schedulingExtensionAdapter.convertExpectedHoursToDouble(expHoursDaily));
		assertEquals(Double.valueOf(45.000000),schedulingExtensionAdapter.convertExpectedHoursToDouble(expHoursWeekly));
		assertEquals(Double.valueOf(75.000000),schedulingExtensionAdapter.convertExpectedHoursToDouble(expHoursPayPeriod));
		assertNull(schedulingExtensionAdapter.convertExpectedHoursToDouble(null));
		assertNull(schedulingExtensionAdapter.convertExpectedHoursToDouble(expHoursWithNullQty));

	}

	@Test
	public void testSetExpectedHoursAttributesWithNull(){

		when(personality.getExpectedHours()).thenReturn(null);

		schedulingExtensionAdapter.setExpectedHoursAttributes(personality, schedulingExtension);

		assertNull(schedulingExtension.getExpectedDailyHours());
		assertNull(schedulingExtension.getExpectedWeeklyHours());
		assertNull(schedulingExtension.getExpectedByPayPeriodHours());

	}

	@Test
	public void testSetJobAssignmentAttributes(){
		JobAssignment jobAssignment = mock(JobAssignment.class);
		JobAssignmentDetails jobAssignmentDetails = mock(JobAssignmentDetails.class);
		WorkerType workerType = mock(WorkerType.class);
		PersonGroupAssignmentSet personGroupAssignmentSet = mock(PersonGroupAssignmentSet.class);
		List<PersonGroupAssignment> list = new ArrayList<PersonGroupAssignment>();

		when(workerType.getWorkerTypeId()).thenReturn(new ObjectIdLong(151));
		when(workerType.getWorkerTypeName()).thenReturn("PE Full Time");
		when(jobAssignmentDetails.getWorkerType()).thenReturn(workerType);
		when(jobAssignment.getJobAssignmentDetails()).thenReturn(jobAssignmentDetails);

		when(jobAssignment.getHasPersonalOvertimeAssignmentSwitch()).thenReturn(1L);
		when(jobAssignment.getDeletedSwitch()).thenReturn(2L);
		when(jobAssignment.getUseMASwitch()).thenReturn(3L);
		when(jobAssignment.getVersionCount()).thenReturn(4L);

		PersonGroupAssignment personGroupAssignment1 = mock(PersonGroupAssignment.class);
		when(personGroupAssignment1.getPersonId()).thenReturn(new ObjectIdLong(233));
		KDate kDate = new KDate(2015, 10, 30);
		when(personGroupAssignment1.getEffectiveDate()).thenReturn(kDate);
		kDate = new KDate(2017, 10, 30);
		when(personGroupAssignment1.getExpirationDate()).thenReturn(kDate);
		when(personGroupAssignment1.getGroupId()).thenReturn(new ObjectIdLong(1));
		PersonGroup personGroup1 = mock(PersonGroup.class);
		when(personGroup1.getGroupName()).thenReturn("Group 1");
		when(personalityFacade.getScheduleGroup(new ObjectIdLong(1))).thenReturn(personGroup1);
		list.add(personGroupAssignment1);

		GroupAssignmentEntry groupAssignmentEntry1 = new GroupAssignmentEntry(LocalDate.of(2015, 10, 30),LocalDate.of(2017, 10, 30), 1l);


		PersonGroupAssignment personGroupAssignment2 = mock(PersonGroupAssignment.class);
		when(personGroupAssignment2.getPersonId()).thenReturn(new ObjectIdLong(233));
		kDate = new KDate(2011, 8, 30);
		when(personGroupAssignment2.getEffectiveDate()).thenReturn(kDate);
		kDate = new KDate(2015, 11, 30);
		when(personGroupAssignment2.getExpirationDate()).thenReturn(kDate);
		when(personGroupAssignment2.getGroupId()).thenReturn(new ObjectIdLong(2));
		PersonGroup personGroup2 = mock(PersonGroup.class);
		when(personGroup2.getGroupName()).thenReturn("Group 2");
		when(personalityFacade.getScheduleGroup(new ObjectIdLong(2))).thenReturn(personGroup2);
		list.add(personGroupAssignment2);

		GroupAssignmentEntry groupAssignmentEntry2 = new GroupAssignmentEntry(LocalDate.of(2011, 8, 30),LocalDate.of(2015, 11, 30), 2l);


		PersonGroupAssignment personGroupAssignment3 = mock(PersonGroupAssignment.class);
		when(personGroupAssignment3.getPersonId()).thenReturn(new ObjectIdLong(233));
		kDate = new KDate(2015, 5, 21);
		when(personGroupAssignment3.getEffectiveDate()).thenReturn(kDate);
		kDate = new KDate(2021, 10, 29);
		when(personGroupAssignment3.getExpirationDate()).thenReturn(kDate);
		when(personGroupAssignment3.getGroupId()).thenReturn(new ObjectIdLong(3));
		PersonGroup personGroup3 = mock(PersonGroup.class);
		when(personGroup3.getGroupName()).thenReturn("Group 3");
		when(personalityFacade.getScheduleGroup(new ObjectIdLong(3))).thenReturn(personGroup3);
		list.add(personGroupAssignment3);

		GroupAssignmentEntry groupAssignmentEntry3 = new GroupAssignmentEntry(LocalDate.of(2015, 5, 21),LocalDate.of(2021, 10, 29), 3l);


		when(personGroupAssignmentSet.getAllPersonGroupAssignments()).thenReturn(list);


		when(jobAssignment.getScheduleGroupAssignmentSet()).thenReturn(personGroupAssignmentSet);

		schedulingExtensionAdapter.setJobAssignmentAttributes(schedulingExtension, jobAssignment);

		List<GroupAssignmentEntry> groupAssignnmentEntryList = new ArrayList<>();
		groupAssignnmentEntryList.add(groupAssignmentEntry1);
		groupAssignnmentEntryList.add(groupAssignmentEntry2);
		groupAssignnmentEntryList.add(groupAssignmentEntry3);

		assertEquals(Long.valueOf(1L), schedulingExtension.getSwitches().getAssignPersonOvertimeSwitch());
		assertEquals(Long.valueOf(2L), schedulingExtension.getSwitches().getDeletedSwitch());
		assertEquals(Long.valueOf(3L), schedulingExtension.getSwitches().getUseMASwitch());
		assertEquals(Long.valueOf(4L), schedulingExtension.getSwitches().getVersionCount());

		assertEquals("PE Full Time",schedulingExtension.getWorkerType());

		assertEquals(groupAssignnmentEntryList,schedulingExtension.getGroupAssignments());

		schedulingExtension.setEffDatedGroupAssignment(null);

		schedulingExtensionAdapter.convertEffDatedGroup(schedulingExtension, personGroupAssignmentSet);

		assertEquals(groupAssignnmentEntryList,schedulingExtension.getGroupAssignments());

		assertEquals(groupAssignnmentEntryList,schedulingExtensionAdapter.getDatedGroupEntries(personGroupAssignmentSet));

		assertEquals(groupAssignmentEntry1,schedulingExtensionAdapter.getDatedGroupEntry(personGroupAssignment1));

	}

	@Test
	public void testSetJobAssignmentSwitchEntry() {
		JobAssignment jobAssignment=mock(JobAssignment.class);
		when(jobAssignment.getHasPersonalOvertimeAssignmentSwitch()).thenReturn(1L);
		when(jobAssignment.getDeletedSwitch()).thenReturn(2L);
		when(jobAssignment.getUseMASwitch()).thenReturn(3L);
		when(jobAssignment.getVersionCount()).thenReturn(4L);
		SchedulingExtension schedulingExtension=new SchedulingExtension();
		schedulingExtensionAdapter.setJobAssignmentSwitchEntry(schedulingExtension, jobAssignment);

		JobAssignmentSwitchEntry jobAssignmentEntry=schedulingExtension.getSwitches();
		assertEquals(Long.valueOf(1L), jobAssignmentEntry.getAssignPersonOvertimeSwitch());
		assertEquals(Long.valueOf(2L), jobAssignmentEntry.getDeletedSwitch());
		assertEquals(Long.valueOf(3L), jobAssignmentEntry.getUseMASwitch());
		assertEquals(Long.valueOf(4L), jobAssignmentEntry.getVersionCount());
	}

	@Test
	public void testGetDatedGroupEntryWithNull(){
		GroupAssignmentEntry groupAssignmentEntry1 = new GroupAssignmentEntry(LocalDate.of(2015, 10, 30),LocalDate.of(2017, 10, 30),null,1l);
		PersonGroupAssignment personGroupAssignment1 = mock(PersonGroupAssignment.class);
		when(personGroupAssignment1.getPersonId()).thenReturn(new ObjectIdLong(233));
		KDate kDate = new KDate(2015, 10, 30);
		when(personGroupAssignment1.getEffectiveDate()).thenReturn(kDate);
		kDate = new KDate(2017, 10, 30);
		when(personGroupAssignment1.getExpirationDate()).thenReturn(kDate);
		when(personGroupAssignment1.getGroupId()).thenReturn(new ObjectIdLong(1));
		when(personalityFacade.getScheduleGroup(new ObjectIdLong(1))).thenReturn(null);
		assertEquals(groupAssignmentEntry1,schedulingExtensionAdapter.getDatedGroupEntry(personGroupAssignment1));
	}



	@Test
	public void testSetAccessAssignmentAttributes(){
		AccessAssignment accessAssignment = mock(AccessAssignment.class);
		when(accessAssignment.getGroupScheduleId()).thenReturn(new ObjectIdLong(154));
		when(accessAssignment.getShiftCodeId()).thenReturn(new ObjectIdLong(11));
		when(accessAssignment.getSchedulePatternId()).thenReturn(new ObjectIdLong(1));
		when(accessAssignment.getAvailabilityPatternId()).thenReturn(new ObjectIdLong(-2));
		when(accessAssignment.getHyperFindProfileId()).thenReturn(new ObjectIdLong(-1));

		when(personality.getAccessAssignment()).thenReturn(accessAssignment);

		MultiManagerRoleProxyService multiManagerRoleProxyService = mock(MultiManagerRoleProxyService.class);
		mockedSpringContext.when(() -> SpringContext.getBean(MultiManagerRoleProxyService.class)).thenReturn(multiManagerRoleProxyService);
		when(multiManagerRoleProxyService.isUserOnMultiManagerRole(any())).thenReturn(true);
		AccessAssignmentProxyService accessAssignmentProxyService = mock(AccessAssignmentProxyService.class);
		mockedSpringContext.when(() -> SpringContext.getBean(AccessAssignmentProxyService.class)).thenReturn(accessAssignmentProxyService);
		when(accessAssignmentProxyService.getGroupScheduleId()).thenReturn(154L);
		IKProperties ikProperties = mock(IKProperties.class);
		mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(ikProperties);
		when(ikProperties.getPropertyAsBoolean(anyString(), anyBoolean())).thenReturn(true);
		ReleaseToggleService releaseToggleService = mock(ReleaseToggleService.class);
		mockedSpringContext.when(() -> SpringContext.getBean(ReleaseToggleService.class)).thenReturn(releaseToggleService);
		when(releaseToggleService.getValue(anyString())).thenReturn(true);

		schedulingExtensionAdapter.setAccessAssignmentAttributes(accessAssignment, schedulingExtension);

		assertEquals(Long.valueOf(154),schedulingExtension.getGroupScheduleId());
		assertEquals(Long.valueOf(11),schedulingExtension.getShiftCodeId());
		assertEquals(Long.valueOf(1),schedulingExtension.getSchedulePatternId());
		assertEquals(Long.valueOf(-2),schedulingExtension.getAvailabilityPatternId());
		assertEquals(Long.valueOf(-1),schedulingExtension.getHyperfindProfileId());

		schedulingExtensionAdapter.setAccessAssignmentAttributes(null, schedulingExtension);

	}

	@Test
	public void testSetAccessAssignmentAttributesWithNull(){
		MultiManagerRoleProxyService multiManagerRoleProxyService = mock(MultiManagerRoleProxyService.class);
		mockedSpringContext.when(() -> SpringContext.getBean(MultiManagerRoleProxyService.class)).thenReturn(multiManagerRoleProxyService);
		when(multiManagerRoleProxyService.isUserOnMultiManagerRole(any())).thenReturn(true);
		AccessAssignmentProxyService accessAssignmentProxyService = mock(AccessAssignmentProxyService.class);
		mockedSpringContext.when(() -> SpringContext.getBean(AccessAssignmentProxyService.class)).thenReturn(accessAssignmentProxyService);
		when(accessAssignmentProxyService.getGroupScheduleId()).thenReturn(null);
		IKProperties ikProperties = mock(IKProperties.class);
		mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(ikProperties);
		when(ikProperties.getPropertyAsBoolean(anyString(), anyBoolean())).thenReturn(true);
		ReleaseToggleService releaseToggleService = mock(ReleaseToggleService.class);
		mockedSpringContext.when(() -> SpringContext.getBean(ReleaseToggleService.class)).thenReturn(releaseToggleService);
		when(releaseToggleService.getValue(anyString())).thenReturn(true);

		schedulingExtensionAdapter.setAccessAssignmentAttributes(null, schedulingExtension);
		assertEquals(null,schedulingExtension.getGroupScheduleId());
		assertEquals(null,schedulingExtension.getShiftCodeId());
		assertEquals(null,schedulingExtension.getSchedulePatternId());
		assertEquals(null,schedulingExtension.getAvailabilityPatternId());
		assertEquals(null,schedulingExtension.getHyperfindProfileId());

	}

	@Test
	public void testGetSchoolCalendarDapId(){
		DapAssignment dapAssignment = mock(DapAssignment.class);
		when(dapAssignment.getSchoolCalendarDapId()).thenReturn(new ObjectIdLong(131));
		when(personalityFacade.getDapAssignment(personality)).thenReturn(dapAssignment);
		assertEquals(Long.valueOf(131),schedulingExtensionAdapter.getSchoolCalendarDapId(personality));

		when(personalityFacade.getDapAssignment(personality)).thenReturn(null);
		assertEquals(null,schedulingExtensionAdapter.getSchoolCalendarDapId(personality));

	}

    @Test
    public void testGetForecastMapDapId() {
        DapAssignment dapAssignment = mock(DapAssignment.class);
        when(dapAssignment.getForecastMapDapId()).thenReturn(new ObjectIdLong(151));
        when(personalityFacade.getDapAssignment(personality)).thenReturn(dapAssignment);
        assertEquals(Long.valueOf(151), schedulingExtensionAdapter.getForecastMapDapId(personality));

        when(personalityFacade.getDapAssignment(personality)).thenReturn(null);
        assertEquals(null, schedulingExtensionAdapter.getForecastMapDapId(personality));

    }


	//@Test
	public void testSetJobTransferSet(){
		List<PersonAccessAssignment> list = new ArrayList<>();
		List<JobTransferEntry> jobTranferEntries = new ArrayList<>();
		PersonAccessAssignment personAccessAssignment1 = mock(PersonAccessAssignment.class);
//		when(personAccessAssignment1.getPersonId()).thenReturn(new ObjectIdLong(233));
		when(personAccessAssignment1.getManagerTransferOrganizationSetId()).thenReturn(new ObjectIdLong(108));
//		when(personAccessAssignment1.getManagerAccessOrganizationSetId()).thenReturn(new ObjectIdLong(108));
		when(personAccessAssignment1.getEffectiveDate()).thenReturn(new KDate(2011,11,23));
		when(personAccessAssignment1.getExpirationDate()).thenReturn(new KDate(2016,10,21));
		when(personAccessAssignment1.getManagerAccessOrganizationSetId()).thenReturn(new ObjectIdLong(123l));
		when(personAccessAssignment1.getProfessionalTransferOrganizationSetId()).thenReturn(new ObjectIdLong(123l));
		when(personAccessAssignment1.getManagerTransferOrganizationSetId()).thenReturn(new ObjectIdLong(123l));
		list.add(personAccessAssignment1);
		JobTransferEntry jobTransferEntry1 = new JobTransferEntry(LocalDate.of(2011,11,23),LocalDate.of(2016,10,21),"All Organizational Set 1");
		jobTransferEntry1.setManagerAccessOrganizationSetId(123l);
		jobTransferEntry1.setManagerEmployeeGroupId(123l);
		jobTransferEntry1.setManagerTransferOrganizationSetId(123l);
		jobTransferEntry1.setProfessionalTransferOrganizationSetId(123l);

		jobTranferEntries.add(jobTransferEntry1);

		PersonAccessAssignment personAccessAssignment2 = mock(PersonAccessAssignment.class);
//		when(personAccessAssignment2.getPersonId()).thenReturn(new ObjectIdLong(233));
		when(personAccessAssignment2.getManagerTransferOrganizationSetId()).thenReturn(new ObjectIdLong(107));
//		when(personAccessAssignment2.getManagerAccessOrganizationSetId()).thenReturn(new ObjectIdLong(107));
		when(personAccessAssignment2.getEffectiveDate()).thenReturn(new KDate(2012,9,11));
		when(personAccessAssignment2.getExpirationDate()).thenReturn(new KDate(2015,12,22));
		when(personAccessAssignment2.getManagerAccessOrganizationSetId()).thenReturn(new ObjectIdLong(123l));
		when(personAccessAssignment2.getProfessionalTransferOrganizationSetId()).thenReturn(new ObjectIdLong(123l));
		when(personAccessAssignment2.getManagerTransferOrganizationSetId()).thenReturn(new ObjectIdLong(123l));
		list.add(personAccessAssignment2);
		JobTransferEntry jobTransferEntry2 = new JobTransferEntry(LocalDate.of(2012,9,11),LocalDate.of(2015,12,22),"All Organizational Set 2");
		jobTransferEntry2.setManagerAccessOrganizationSetId(123l);
		jobTransferEntry2.setManagerEmployeeGroupId(123l);
		jobTransferEntry2.setManagerTransferOrganizationSetId(123l);
		jobTransferEntry2.setProfessionalTransferOrganizationSetId(123l);
		jobTranferEntries.add(jobTransferEntry2);

		PersonAccessAssignment personAccessAssignment3 = mock(PersonAccessAssignment.class);
//		when(personAccessAssignment3.getPersonId()).thenReturn(new ObjectIdLong(233));
		when(personAccessAssignment3.getManagerTransferOrganizationSetId()).thenReturn(new ObjectIdLong(105));
//		when(personAccessAssignment3.getManagerAccessOrganizationSetId()).thenReturn(new ObjectIdLong(105));
		when(personAccessAssignment3.getEffectiveDate()).thenReturn(new KDate(2013,10,26));
		when(personAccessAssignment3.getExpirationDate()).thenReturn(new KDate(2018,9,25));
		when(personAccessAssignment3.getManagerAccessOrganizationSetId()).thenReturn(new ObjectIdLong(123l));
		when(personAccessAssignment3.getProfessionalTransferOrganizationSetId()).thenReturn(new ObjectIdLong(123l));
		when(personAccessAssignment3.getManagerTransferOrganizationSetId()).thenReturn(new ObjectIdLong(123l));
		list.add(personAccessAssignment3);
		JobTransferEntry jobTransferEntry3 = new JobTransferEntry(LocalDate.of(2013,10,26),LocalDate.of(2018,9,25),"All Organizational Set 3");
		jobTransferEntry3.setManagerAccessOrganizationSetId(123l);
		jobTransferEntry3.setManagerEmployeeGroupId(123l);
		jobTransferEntry3.setManagerTransferOrganizationSetId(123l);
		jobTransferEntry3.setProfessionalTransferOrganizationSetId(123l);
		jobTranferEntries.add(jobTransferEntry3);

		schedulingExtensionAdapter.setJobTransferSet(schedulingExtension, list);

		assertEquals(jobTranferEntries,schedulingExtension.getJobTransfer());

	}

	@Test
	public void testGetJobTransferEntry() {
		TestPersonAccessAssignment personAccessAssignment = new TestPersonAccessAssignment();
		personAccessAssignment.setEffectiveDate("01/01/2000");
		personAccessAssignment.setExpirationDate("01/01/3000");
		personAccessAssignment.setManagerTransferOrganizationSetId(1L);
		personAccessAssignment.setManagerAccessOrganizationSetId(2L);
		personAccessAssignment.setEmployeeGroupId(3L);
		personAccessAssignment.setHomeHyperFindQueryId(4L);
		personAccessAssignment.setProfessionalTransferOrganizationSetId(5L);
		personAccessAssignment.setEmpMgrTransferOrganizationSetId(6L);

		JobTransferEntry resultJobTransferEntry = schedulingExtensionAdapter.getJobTransferEntry(personAccessAssignment);

		assertEquals("2000-01-01", resultJobTransferEntry.getEffectiveDate().toString());
		assertEquals("3000-01-01", resultJobTransferEntry.getExpirationDate().toString());
		assertEquals(1L, resultJobTransferEntry.getManagerTransferOrganizationSetId().longValue());
		assertEquals(2L, resultJobTransferEntry.getManagerAccessOrganizationSetId().longValue());
		assertEquals(3L, resultJobTransferEntry.getManagerEmployeeGroupId().longValue());
		assertEquals(4L, resultJobTransferEntry.getHomeHyperFindQueryId().longValue());
		assertEquals(5L, resultJobTransferEntry.getProfessionalTransferOrganizationSetId().longValue());
		assertEquals(6L, resultJobTransferEntry.getEmpMgrTransferOrganizationSetId().longValue());
	}

	@Test
	public void testCreateSnapshot() {
		mockStatic();
		SchedulingExtension expectedExtension = new SchedulingExtension();
		expectedExtension.setEffDatedGroupAssignment(new EffectiveDatedCollection<>());
		expectedExtension.setWorkerType("workerType");
		expectedExtension.setWorkerTypeId(1L);
		SchedulingExtension actualExtension = schedulingExtensionAdapter.createSnapshot(expectedExtension);
		assertEquals(expectedExtension, actualExtension);
		assertNotSame(expectedExtension, actualExtension);
	}

	@Test
	public void testCreateSnapshotWithNull() {
		SchedulingExtension actualExtension = schedulingExtensionAdapter.createSnapshot(null);
		assertNull(actualExtension);
	}

   private PredictiveSchedulingEligibilityEntry convertPredictiveEligibilityRecordToEntry(PredictiveSchedulingEligibilityRecord record) {
      PredictiveSchedulingEligibilityEntry entry = new PredictiveSchedulingEligibilityEntry();
      entry.setIsEligible(record.getIsEligibile());
      entry.setEffectiveDate(adapterHelper.kDateToLocalDate(record.getEffectiveDate()));
      entry.setExpirationDate(adapterHelper.kDateToLocalDate(record.getExpirationDate()));
      return entry;
   }

	private PredictiveSchedulingOverrideEntry convertPredictiveScheduleOverrideRecordToEntry(PredictiveSchedulingOverrideRecord record) {
		PredictiveSchedulingOverrideEntry entry = new PredictiveSchedulingOverrideEntry();
		entry.setSchedulueOverrideId(record.getPredictiveScheduleOverrideId().longValue());
		entry.setEffectiveDate(adapterHelper.kDateToLocalDate(record.getEffectiveDate()));
		entry.setExpirationDate(adapterHelper.kDateToLocalDate(record.getExpirationDate()));
		return entry;
	}

	private class TestPersonAccessAssignment extends PersonAccessAssignment {

		public void setEffectiveDate(String effectiveDateString) {
			DateProperties effectiveDateProperties =
					new DateProperties(PERSON_ACCESS_ASSIGNMENT_EFFECTIVE, KConstants.startOfTime_Date, false);
			super.effectiveDate = new DateMember(effectiveDateString, effectiveDateProperties);
		}

		public void setExpirationDate(String expirationDateString) {
			DateProperties expirationDateProperties =
					new DateProperties(PERSON_ACCESS_ASSIGNMENT_EXPIRATION, KConstants.endOfTime_Date, false);
			super.expirationDate = new DateMember(expirationDateString, expirationDateProperties);
		}

		public void setManagerTransferOrganizationSetId(Long id) {
			ObjectIdLongProperties managerTransferOrganizationProperties =
					new ObjectIdLongProperties(PERSON_ACCESS_ASSIGNMENT_MANAGER_TRANSFER_ORGANIZATION_ID, EMPTY_DAP, false);
			super.managerTransferOrganizationSetId = new ObjectIdLongMember(id, managerTransferOrganizationProperties);
		}

		public void setManagerAccessOrganizationSetId(Long id) {
			ObjectIdLongProperties managerAccessOrganizationProperties =
					new ObjectIdLongProperties(PERSON_ACCESS_ASSIGNMENT_MANAGER_ACCESS_ORGANIZATION_ID, EMPTY_DAP, false);
			super.managerAccessOrganizationSetId = new ObjectIdLongMember(id, managerAccessOrganizationProperties);
		}

		public void setEmployeeGroupId(Long id) {
			ObjectIdLongProperties employeeGroupProperties =
					new ObjectIdLongProperties(PERSON_ACCESS_ASSIGNMENT_EMPLOYEE_GROUP_ID, EMPTY_DAP, false);
			super.employeeGroupId = new ObjectIdLongMember(id, employeeGroupProperties);
		}

		public void setHomeHyperFindQueryId(Long id) {
			ObjectIdLongProperties homeHyperFindQueryProperties =
					new ObjectIdLongProperties(PERSON_ACCESS_ASSIGNMENT_HOME_HYPER_FIND_QUERY_ID, null, true);
			super.homeHyperFindQueryId = new ObjectIdLongMember(id, homeHyperFindQueryProperties);
		}

		public void setProfessionalTransferOrganizationSetId(Long id) {
			ObjectIdLongProperties professionalTransferOrganizationProperties =
					new ObjectIdLongProperties(PERSON_ACCESS_ASSIGNMENT_PROFESSIONAL_TRANSFER_ORGANIZATION_ID, EMPTY_DAP, false);
			super.professionalTransferOrganizationSetId = new ObjectIdLongMember(id, professionalTransferOrganizationProperties);
		}

		public void setEmpMgrTransferOrganizationSetId(Long id) {
			ObjectIdLongProperties empManagerAccessOrganizationProperties =
					new ObjectIdLongProperties(PERSON_ACCESS_ASSIGNMENT_EMP_MGR_TRANSFER_ORGANIZATION_ID, EMPTY_DAP, false);
			super.empMgrTransferOrganizationSetId = new ObjectIdLongMember(id, empManagerAccessOrganizationProperties);
		}
	}
}
