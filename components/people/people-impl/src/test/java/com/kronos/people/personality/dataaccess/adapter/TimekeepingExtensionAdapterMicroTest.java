/**
 *
 */
package com.kronos.people.personality.dataaccess.adapter;

import com.kronos.container.api.access.SpringContext;
import com.kronos.people.personality.dataaccess.legacy.IWorkEmployeeServiceFacade;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.model.extension.TimekeepingExtension;
import com.kronos.people.personality.model.extension.entry.*;
import com.kronos.people.proxy.api.service.MultiManagerRoleProxyService;
import com.kronos.wfc.commonapp.currency.business.assignment.EmployeeCurrencyAssignment;
import com.kronos.wfc.commonapp.currency.business.assignment.UserCurrencyAssignment;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignment;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignmentDetails;
import com.kronos.wfc.commonapp.people.business.person.*;
import com.kronos.wfc.commonapp.people.business.person.group.employment.EmploymentTermAssignment;
import com.kronos.wfc.commonapp.people.business.person.payruleassignment.DirectPayRuleAssignmentSet;
import com.kronos.wfc.commonapp.people.business.person.payruleassignment.PayRuleAssignment;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.rules.business.PayRule;
import com.kronos.wfc.commonapp.types.business.WageProfile;
import com.kronos.wfc.platform.member.framework.BooleanMember;
import com.kronos.wfc.platform.member.framework.BooleanProperties;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.currency.KCurrency;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

import static com.kronos.wfc.platform.resources.shared.constants.BeanConstants.ASSIGN_ATTESTATION_PROFILE_IS_MANAGER_ROLE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class TimekeepingExtensionAdapterMicroTest {
	private TimeKeepingAdapter timeKeepingAdapter;
	private PersonalityFacade personalityFacadeMock;
	private AccessAssignmentAdapterHelper accessAssignmentAdapterHelper;
	private JobAssignmentAdapterHelper jobAssignmentAdapterHelper;
	private MockedStatic<SpringContext> mockedSpringContext;

	@BeforeEach
	public void setup() {
		timeKeepingAdapter = new TimeKeepingAdapter();
		jobAssignmentAdapterHelper = new JobAssignmentAdapterHelper();
		jobAssignmentAdapterHelper.adapterHelper = new AdapterHelper();
		accessAssignmentAdapterHelper = new AccessAssignmentAdapterHelper();
		accessAssignmentAdapterHelper.adapterHelper = new AdapterHelper();
		personalityFacadeMock = mock(PersonalityFacade.class);
		timeKeepingAdapter.setPersonalityFacade(personalityFacadeMock);
		timeKeepingAdapter.setAdapterHelper(new AdapterHelper());
		timeKeepingAdapter.jobAssignmentAdapterHelper = jobAssignmentAdapterHelper;
		timeKeepingAdapter.accessAssignmentAdapterHelper = accessAssignmentAdapterHelper;
		mockedSpringContext = Mockito.mockStatic(SpringContext.class);
	}

	@AfterEach
	public void teardown(){
		timeKeepingAdapter = null;
		personalityFacadeMock = null;
		mockedSpringContext.close();
	}

	private AdapterHelper mockAdapterHelper() {
		// TODO Auto-generated method stub
		return mock(AdapterHelper.class);
	}

	@Test
	public void testConvertEmptyPersonality() {
		timeKeepingAdapter = spy(TimeKeepingAdapter.class);
		timeKeepingAdapter.setAdapterHelper(mock(AdapterHelper.class));
		Personality p = mock(Personality.class);
		timeKeepingAdapter.setPersonalityFacade(personalityFacadeMock);

		mockHelpers();
		TimekeepingExtension ext = timeKeepingAdapter.convert(p,null);
		verify(jobAssignmentAdapterHelper, times(0)).setJobAssignmentProperties(any(), any());
		verify(accessAssignmentAdapterHelper, times(0)).setTimeKeepingAccessProperties(any(), any());
		verify(timeKeepingAdapter, times(0)).setPersonPropeties(any(), any());
		verify(timeKeepingAdapter, times(0)).setPersonalityProperties(any(), any());
		verify(timeKeepingAdapter, times(0)).setCurrencyProperties(any(), any());
		assertNull(ext);
	}

	@Test
	public void testConvertNullPersonId() {
		timeKeepingAdapter = spy(TimeKeepingAdapter.class);
		timeKeepingAdapter.setAdapterHelper(mock(AdapterHelper.class));
		Personality p = mock(Personality.class);
		when(p.getPersonId()).thenReturn(new ObjectIdLong());
		timeKeepingAdapter.setPersonalityFacade(personalityFacadeMock);
		TimekeepingExtension ext = timeKeepingAdapter.convert(p,null);
		mockHelpers();
		verify(jobAssignmentAdapterHelper, times(0)).setJobAssignmentProperties(any(), any());
		verify(accessAssignmentAdapterHelper, times(0)).setTimeKeepingAccessProperties(any(), any());
		verify(timeKeepingAdapter, times(0)).setPersonPropeties(any(), any());
		verify(timeKeepingAdapter, times(0)).setPersonalityProperties(any(), any());
		verify(timeKeepingAdapter, times(0)).setCurrencyProperties(any(), any());
		assertNull(ext);
	}

	/**
	 *
	 */
	protected void mockHelpers() {
		jobAssignmentAdapterHelper = mock(JobAssignmentAdapterHelper.class);
		accessAssignmentAdapterHelper = mock(AccessAssignmentAdapterHelper.class);
		timeKeepingAdapter.accessAssignmentAdapterHelper = accessAssignmentAdapterHelper;
		timeKeepingAdapter.jobAssignmentAdapterHelper = jobAssignmentAdapterHelper;
	}

	static boolean flag = false;

	@Test
	public void testConvertWithPersonId() {
		flag=false;
		timeKeepingAdapter = new TimeKeepingAdapter(){
			@Override
			protected TimekeepingExtension populate(Personality p) {
				flag = true;
				return null;
			}
		};
		Personality p = mock(Personality.class);
		when(p.getPersonId()).thenReturn(new ObjectIdLong(-1));
		timeKeepingAdapter.setPersonalityFacade(personalityFacadeMock);
		timeKeepingAdapter.setAdapterHelper(new AdapterHelper());
		TimekeepingExtension ext = timeKeepingAdapter.convert(p,null);
		assertTrue(flag);
	}

	@Test
	public void testConvertEmptyPersonalityFacade() {
		timeKeepingAdapter = spy(TimeKeepingAdapter.class);
		timeKeepingAdapter.setAdapterHelper(mock(AdapterHelper.class));
		timeKeepingAdapter.setPersonalityFacade(personalityFacadeMock);
		mockHelpers();
		Personality p = mock(Personality.class);
		when(p.getJobAssignment()).thenReturn(null);
		when(p.getNameData()).thenReturn(null);
		when(p.getAccessAssignment()).thenReturn(null);
		when(p.getExpectedHours()).thenReturn(mock(ExpectedHoursSet.class));
		when(p.getBaseWageRateSet()).thenReturn(mock(BaseWageRateSet.class));
		when(p.getPurposeAssignments()).thenReturn(mock(PurposeAssignmentSet.class));
		when(p.getPersonId()).thenReturn(mock(ObjectIdLong.class));
		timeKeepingAdapter.workEmployeeServiceFacade = mock(IWorkEmployeeServiceFacade.class);
		TimekeepingExtension ext = timeKeepingAdapter.populate(p);
		verify(jobAssignmentAdapterHelper, times(0)).setJobAssignmentProperties(any(), any());
		verify(accessAssignmentAdapterHelper, times(0)).setTimeKeepingAccessProperties(any(), any());
		verify(timeKeepingAdapter, times(0)).setPersonPropeties(any(), any());
		verify(timeKeepingAdapter, times(1)).setPersonalityProperties(any(), any());
		verify(timeKeepingAdapter, times(1)).setCurrencyProperties(any(), any());
		assertNotNull(ext);
	}

	@Test
	public void testConvertFilledPersonality() {
		timeKeepingAdapter = spy(TimeKeepingAdapter.class);
		timeKeepingAdapter.setAdapterHelper(mock(AdapterHelper.class));
		timeKeepingAdapter.setPersonalityFacade(personalityFacadeMock);
		mockHelpers();
		when(personalityFacadeMock.getJobAssignment(any())).thenReturn(Mockito.mock(JobAssignment.class));
		when(personalityFacadeMock.getAccessAssignment(any())).thenReturn(mock(AccessAssignment.class));
		when(personalityFacadeMock.getPerson(any())).thenReturn(mock(Person.class));

		Personality p = mock(Personality.class);
		when(p.getJobAssignment()).thenReturn(mock(JobAssignment.class));
		when(p.getNameData()).thenReturn(mock(Person.class));
		when(p.getAccessAssignment()).thenReturn(mock(AccessAssignment.class));
		when(p.getExpectedHours()).thenReturn(mock(ExpectedHoursSet.class));
		when(p.getBaseWageRateSet()).thenReturn(mock(BaseWageRateSet.class));
		when(p.getPurposeAssignments()).thenReturn(mock(PurposeAssignmentSet.class));
		when(p.getAssignAttestationProfileSet()).thenReturn(mock(AssignAttestationProfileSet.class));
		when(p.getPersonId()).thenReturn(mock(ObjectIdLong.class));
		timeKeepingAdapter.workEmployeeServiceFacade = mock(IWorkEmployeeServiceFacade.class);
		TimekeepingExtension ext = timeKeepingAdapter.populate(p);
		verify(jobAssignmentAdapterHelper, times(1)).setJobAssignmentProperties(any(), any());
		verify(accessAssignmentAdapterHelper, times(1)).setTimeKeepingAccessProperties(any(), any());
		verify(timeKeepingAdapter, times(1)).setPersonPropeties(any(), any());
		verify(timeKeepingAdapter, times(1)).setAttestationProfileAssignmentProperties(any(), any());

	}

	@Test
	public void testSetCurrencyProperties() {
		TimekeepingExtension ext = new TimekeepingExtension();
		CurrencyDetailsEntry cur = new CurrencyDetailsEntry();
		cur.setCurrencyCode("USD");
		cur.setCurrencyId(1L);
		ext.setEmployeeCurrency(cur);
		ext.setUserCurrency(cur);
		//	Personality p = mock(Personality.class);
		ext.setIsManager(true);
		PersonalityFacade personalityFacade = mock(PersonalityFacade.class);
		timeKeepingAdapter.setPersonalityFacade(personalityFacade);
		timeKeepingAdapter.setCurrencyProperties(ext, new ObjectIdLong(2L));

		assertNotNull(ext.getEmployeeCurrency());
		assertNotNull(ext.getUserCurrency());

		//when(adapterHelper.getLongFromObjectIdLong(any())).thenReturn(25L);
		EmployeeCurrencyAssignment eca = mock(EmployeeCurrencyAssignment.class);
		when(eca.getCurrencyCode()).thenReturn("INR");
		when(eca.getCurrencyId()).thenReturn(new ObjectIdLong(25L));
		when(personalityFacade.getEmployeeCurrencyAssignment(any())).thenReturn(eca);

		//doCallRealMethod().when(adapterHelper.setLongFromObjectIdLong(any(), any()));
		timeKeepingAdapter.setCurrencyProperties(ext, new ObjectIdLong(2L));
		CurrencyDetailsEntry cd = ext.getEmployeeCurrency();
		assertNotNull(cd);

		assertEquals(25L, cd.getCurrencyId().longValue());
		assertEquals("INR", cd.getCurrencyCode());

		assertNotNull(ext.getUserCurrency());

		UserCurrencyAssignment eca1 = mock(UserCurrencyAssignment.class);
		when(eca1.getCurrencyCode()).thenReturn("USD");
		when(eca1.getCurrencyId()).thenReturn(new ObjectIdLong(20L));
		when(personalityFacade.getUserCurrencyAssignment(any())).thenReturn(eca1);
		ext = new TimekeepingExtension();
		ext.setIsManager(true);
		timeKeepingAdapter.setCurrencyProperties(ext, new ObjectIdLong(2L));
		cd = ext.getEmployeeCurrency();
		assertNotNull(cd);

		assertEquals(25L, cd.getCurrencyId().longValue());
		assertEquals("INR", cd.getCurrencyCode());

		cd = ext.getUserCurrency();
		assertEquals(20L, cd.getCurrencyId().longValue());
		assertEquals("USD", cd.getCurrencyCode());
	}

	@Test
	public void testSetPersonPropeties() {
		TimekeepingExtension ext = new TimekeepingExtension();
		Person person = mock(Person.class);

		timeKeepingAdapter.setPersonPropeties(ext, person);
		assertNull(ext.getPayrollLockoutThruDate());
		assertNull(ext.getManagerSignoffThruDate());

		when(person.getPayrollLockoutThruDate()).thenReturn(new KDate(2015, 9, 1));
		when(person.getManagerSignoffThruDate()).thenReturn(new KDate(2015, 10, 1));
		when(person.getSignoffPreparationDate()).thenReturn(new KDate(2015, 10, 1));

		timeKeepingAdapter.setPersonPropeties(ext, person);
		assertEquals(LocalDate.of(2015, 9, 1), ext.getPayrollLockoutThruDate());
		assertEquals(LocalDate.of(2015, 10, 1), ext.getManagerSignoffThruDate());
		assertEquals(LocalDate.of(2015, 10, 1), ext.getSignoffPreparationDate());
	}

	@Test
	public void testSetRecentEntries() {
		TimekeepingExtension ext = new TimekeepingExtension();
		Collection<com.kronos.wfc.commonapp.people.business.user.RecentEntry> recentEntryList = new ArrayList<com.kronos.wfc.commonapp.people.business.user.RecentEntry>();
		com.kronos.wfc.commonapp.people.business.user.RecentEntry mockOj1 = mock(com.kronos.wfc.commonapp.people.business.user.RecentEntry.class);
		com.kronos.wfc.commonapp.people.business.user.RecentEntry mockOj2 = mock(com.kronos.wfc.commonapp.people.business.user.RecentEntry.class);
		System.out.println(mockOj1.getEnteredText());
		when(mockOj1.getEnteredText()).thenReturn("Value1");
		System.out.println(mockOj1.getEnteredText());
		when(mockOj2.getOrder()).thenReturn(2L);
		recentEntryList.add(mockOj1);
		recentEntryList.add(mockOj2);
		ObjectIdLong personId = new ObjectIdLong(1L);
		timeKeepingAdapter.setPersonalityFacade(personalityFacadeMock);
		when(personalityFacadeMock.getRecentEntries(personId)).thenReturn(recentEntryList);
		timeKeepingAdapter.setRecentEntries(ext, personId);
		Function<com.kronos.wfc.commonapp.people.business.user.RecentEntry,RecentEntry> recentEntryFunction = timeKeepingAdapter.recentEntryConvertorFunction;
		com.kronos.wfc.commonapp.people.business.user.RecentEntry recentEntryMock = mock(com.kronos.wfc.commonapp.people.business.user.RecentEntry.class);
		AdapterHelper adapterHelperMock = mock(AdapterHelper.class);
		timeKeepingAdapter.setAdapterHelper(adapterHelperMock);
		when(recentEntryMock.getOrder()).thenReturn(Long.valueOf(1L));
		when(recentEntryMock.getEnteredText()).thenReturn("entered text");
		RecentEntry recentEntry = recentEntryFunction.apply(recentEntryMock);
		assertNotNull(recentEntry);
		assertEquals(2,ext.getRecentEntries().size());
		assertEquals("entered text", recentEntry.getEnteredText());
		assertEquals(Long.valueOf(1), recentEntry.getOrder());
	}




	@Test
	public void testSetJobAssignmentDetailsProperties(){
		TimekeepingExtension ext = new TimekeepingExtension();
		JobAssignmentDetails jobAssignmentDetails = Mockito.mock(JobAssignmentDetails.class);
		WageProfile wageProfile = Mockito.mock(WageProfile.class);
		when(jobAssignmentDetails.getWageProfile()).thenReturn(wageProfile);
		when(wageProfile.getName()).thenReturn("Wage Profile Name");
		when(wageProfile.getId()).thenReturn(new ObjectIdLong(2L));

		when(jobAssignmentDetails.getWorkerTypeId()).thenReturn(new ObjectIdLong(3L));

		DirectPayRuleAssignmentSet payRuleAssignmentSet = Mockito.mock(DirectPayRuleAssignmentSet.class);
		when(jobAssignmentDetails.getPayRuleAssignmentSet()).thenReturn(payRuleAssignmentSet);

		ArrayList<PayRuleAssignment> list = new ArrayList<PayRuleAssignment>();
		PayRuleAssignment assignment = mock(PayRuleAssignment.class);
		list.add(assignment);

		List<PayRuleAssignment> testList = new ArrayList<>();
		Function<PayRuleAssignment, PayRuleEntry> fn = jobAssignmentAdapterHelper.payRuleConvertorFunction;
		jobAssignmentAdapterHelper.payRuleConvertorFunction = asm->{
			testList.add(asm);
			return null;
		};
		when(payRuleAssignmentSet.getAllPayRuleAssignments()).thenReturn(list);

		AdapterHelper adapterHelperMock = mock(AdapterHelper.class);
		timeKeepingAdapter.setAdapterHelper(adapterHelperMock);
		jobAssignmentAdapterHelper.setJobAssignmentDetailsProperties(ext, jobAssignmentDetails);
		jobAssignmentAdapterHelper.payRuleConvertorFunction = fn;
		assertEquals(Long.valueOf(3), ext.getWorkerTypeId());
		assertEquals(Long.valueOf(2), ext.getWageProfile().getWageProfileId());
		assertEquals(1, testList.size());
		assertEquals(assignment, testList.get(0));
	}
	@Test
	public void testSetJobAssignmentDetailsPropertiesDirectPayRule(){
		TimekeepingExtension ext = new TimekeepingExtension();
		JobAssignmentDetails jobAssignmentDetails = Mockito.mock(JobAssignmentDetails.class);
		DirectPayRuleAssignmentSet directPayRuleAssignmentSet = Mockito.mock(DirectPayRuleAssignmentSet.class);
		
		when(jobAssignmentDetails.getDirectPayRuleAssignmentSet())
				.thenReturn(directPayRuleAssignmentSet);
		
		ArrayList<PayRuleAssignment> list = new ArrayList<PayRuleAssignment>();
		PayRuleAssignment directAssignment = mock(PayRuleAssignment.class);
		list.add(directAssignment);
		List<PayRuleAssignment> testList = new ArrayList<>();
		Function<PayRuleAssignment, PayRuleEntry> directTest = jobAssignmentAdapterHelper.payRuleConvertorFunction;
		jobAssignmentAdapterHelper.payRuleConvertorFunction = dirAsmt->{
			testList.add(dirAsmt);
			return null;
		};
		when(directPayRuleAssignmentSet.getAllPayRuleAssignments()).thenReturn(list);
		AdapterHelper adapterHelperMock = mock(AdapterHelper.class);
		timeKeepingAdapter.setAdapterHelper(adapterHelperMock);
		jobAssignmentAdapterHelper.setJobAssignmentDetailsProperties(ext, jobAssignmentDetails);
		jobAssignmentAdapterHelper.payRuleConvertorFunction = directTest;
		assertEquals(1, testList.size());
		assertEquals(directAssignment, testList.get(0));
	}

	@Test
	public void testPayRuleConvertorFunction(){
		Function<PayRuleAssignment, PayRuleEntry> payRuleConvertorFunction = jobAssignmentAdapterHelper.payRuleConvertorFunction;
		AdapterHelper adapterHelper = new AdapterHelper();
		timeKeepingAdapter.setAdapterHelper(adapterHelper);
		timeKeepingAdapter.setPersonalityFacade(personalityFacadeMock);
		jobAssignmentAdapterHelper.personalityFacade = personalityFacadeMock;
		PayRuleAssignment payRuleAssignmentMock =mock(PayRuleAssignment.class);

		when(payRuleAssignmentMock.getPayRuleAssignmentId()).thenReturn(new ObjectIdLong(2L));
		PayRule payRule = mock(PayRule.class);
		when(payRule.getName()).thenReturn("Payrule Name");
		when(payRule.getWorkRuleId()).thenReturn(new ObjectIdLong(3L));
		when(payRule.getPayRuleId()).thenReturn(new ObjectIdLong(4L));

		when(personalityFacadeMock.getPayRuleByAssign(payRuleAssignmentMock)).thenReturn(payRule);
		when(payRuleAssignmentMock.getVersionCnt()).thenReturn(Long.valueOf(1L));
		PayRuleEntry payRuleEntry = payRuleConvertorFunction.apply(payRuleAssignmentMock);

		assertEquals(Long.valueOf(4), payRuleEntry.getPayRuleId());
		assertEquals(Long.valueOf(2), payRuleEntry.getPayRuleAssignmentId());
		assertEquals(Long.valueOf(1L), payRuleEntry.getVersionCount());
	}

	@Test
	public void testEmploymentTermAssignmentConvertorFunction(){
		Function<EmploymentTermAssignment, EmploymentTermEntry> empTermAssignFunction = jobAssignmentAdapterHelper.employmentTermAssignmentConvertorFunction;

		EmploymentTermAssignment employmentTermAssignment = new EmploymentTermAssignment();
		timeKeepingAdapter.setPersonalityFacade(personalityFacadeMock);

		employmentTermAssignment.setEmploymentTermName("Emp Term");
		employmentTermAssignment.setEmploymentTermId(new ObjectIdLong(1L));
		employmentTermAssignment.setEffectiveDate(new KDate(2013,01,01));
		employmentTermAssignment.setExpirationDate(new KDate(2016,01,01));

		EmploymentTermEntry employmentTermEntry = empTermAssignFunction.apply(employmentTermAssignment);

		assertEquals("Emp Term", employmentTermEntry.getEmploymentTerm());
		assertEquals(Long.valueOf(1), employmentTermEntry.getEmploymentTermId());
		assertTrue(employmentTermEntry.getEffectiveDate().equals(LocalDate.of(2013, 01, 01)));
		assertTrue(employmentTermEntry.getExpirationDate().equals(LocalDate.of(2016, 01, 01)));

	}

	@Test
	public void testbaseWageRateConvertorFunction(){
		Function<BaseWageRate, BaseWageEntry> baseWageConvertorFunction = timeKeepingAdapter.baseWageRateConvertorFunction;

		BaseWageRate baseWageRateMock = mock(BaseWageRate.class);
		AdapterHelper adapterHelperMock = mock(AdapterHelper.class);
		timeKeepingAdapter.setAdapterHelper(adapterHelperMock);
		when(baseWageRateMock.getEffectiveDate()).thenReturn(new KDate(2013,01,01));
		when(baseWageRateMock.getExpirationDate()).thenReturn(new KDate(2016,01,01));
		when(baseWageRateMock.getBaseWageId()).thenReturn(new ObjectIdLong(1L));
		//when(baseWageRateMock.getHourlyRate()).thenReturn(new KCurrency(50.5));

		when(adapterHelperMock.kDateToLocalDate(baseWageRateMock.getEffectiveDate())).thenReturn(LocalDate.of(2013, 01, 01));
		when(adapterHelperMock.kDateToLocalDate(baseWageRateMock.getExpirationDate())).thenReturn(LocalDate.of(2016, 01, 01));
		when(adapterHelperMock.getLongFromObjectIdLong(baseWageRateMock.getBaseWageId())).thenReturn(Long.valueOf(1L));
		//when(adapterHelperMock.getDoubleFromKCurrency(baseWageRateMock.getHourlyRate())).thenReturn(new Double(60.5));
		when(baseWageRateMock.getBaseWageId()).thenReturn(new ObjectIdLong(1L));
		when(baseWageRateMock.getVersionCnt()).thenReturn(Long.valueOf(1L));

		BaseWageEntry baseWageEntry = baseWageConvertorFunction.apply(baseWageRateMock);

		assertTrue(baseWageEntry.getEffectiveDate().equals(LocalDate.of(2013, 01, 01)));
		assertTrue(baseWageEntry.getExpirationDate().equals(LocalDate.of(2016, 01, 01)));
		//assertTrue(baseWageEntry.getVersionCnt()).equals(1L);
		assertEquals(Long.valueOf(1L), baseWageEntry.getBaseWageId());
		assertEquals(Long.valueOf(1L), baseWageEntry.getVersionCount());

	}

	@Test
	public void testAttestationProfileConvertorFunction(){
		Function<AssignAttestationProfile, AttestationProfileEntry> attestationProfileConvertorFunction = timeKeepingAdapter.attestationProfileConvertorFunction;

		AssignAttestationProfile assignAttestationProfileMock = mock(AssignAttestationProfile.class);
		AdapterHelper adapterHelperMock = spy(AdapterHelper.class);
		timeKeepingAdapter.setAdapterHelper(adapterHelperMock);
		when(assignAttestationProfileMock.getEffectiveDate()).thenReturn(new KDate(2013,01,01));
		when(assignAttestationProfileMock.getExpirationDate()).thenReturn(new KDate(2016,01,01));
		when(assignAttestationProfileMock.getAssignAttestationProfileId()).thenReturn(new ObjectIdLong(1L));
		when(assignAttestationProfileMock.getAttestationProfileId()).thenReturn(new ObjectIdLong(2L));

		when(adapterHelperMock.kDateToLocalDate(assignAttestationProfileMock.getEffectiveDate())).thenReturn(LocalDate.of(2013, 01, 01));
		when(adapterHelperMock.kDateToLocalDate(assignAttestationProfileMock.getExpirationDate())).thenReturn(LocalDate.of(2016, 01, 01));
		when(adapterHelperMock.getLongFromObjectIdLong(assignAttestationProfileMock.getAssignAttestationProfileId())).thenReturn(Long.valueOf(1));
		when(adapterHelperMock.getLongFromObjectIdLong(assignAttestationProfileMock.getAttestationProfileId())).thenReturn(Long.valueOf(2));

		when(assignAttestationProfileMock.getAssignAttestationProfileId()).thenReturn(new ObjectIdLong(1L));
		when(assignAttestationProfileMock.getAttestationProfileId()).thenReturn(new ObjectIdLong(2L));

		AttestationProfileEntry attestationProfileEntry = attestationProfileConvertorFunction.apply(assignAttestationProfileMock);

		assertTrue(attestationProfileEntry.getEffectiveDate().equals(LocalDate.of(2013, 01, 01)));
		assertTrue(attestationProfileEntry.getExpirationDate().equals(LocalDate.of(2016, 01, 01)));
		assertEquals(Long.valueOf(1L), attestationProfileEntry.getAttestationProfileAssignmentId());
		assertEquals(Long.valueOf(2L), attestationProfileEntry.getAttestationProfileId());
		assertFalse(attestationProfileEntry.isAssignedToManagerRole());

		BooleanProperties booleanProperties = new BooleanProperties(ASSIGN_ATTESTATION_PROFILE_IS_MANAGER_ROLE, false, false);
		BooleanMember booleanMember = new BooleanMember(true, booleanProperties);

		when(assignAttestationProfileMock.getIsManagerRole()).thenReturn(booleanMember);
		attestationProfileEntry = attestationProfileConvertorFunction.apply(assignAttestationProfileMock);
		assertTrue(attestationProfileEntry.isAssignedToManagerRole());
	}

	@Test
	public void testExpectedHoursConvertorFunction(){
		Function<ExpectedHours, ExpectedHoursEntry> expectedHrsFunction = timeKeepingAdapter.expectedHoursConvertorFunction;

		ExpectedHours expectedHoursMock = mock(ExpectedHours.class);
		AdapterHelper adapterHelperMock = mock(AdapterHelper.class);
		timeKeepingAdapter.setAdapterHelper(adapterHelperMock);

		when(expectedHoursMock.getTimePeriodTypeId()).thenReturn(new ObjectIdLong(1L));
		when(expectedHoursMock.getExpectedHoursQuantity()).thenReturn(20.0D);
		when(expectedHoursMock.getShortName()).thenReturn("short name");

		when(adapterHelperMock.getLongFromObjectIdLong(expectedHoursMock.getTimePeriodTypeId())).thenReturn(Long.valueOf(1));

		ExpectedHoursEntry hoursEntry = expectedHrsFunction.apply(expectedHoursMock);

		assertEquals(Double.valueOf(20.0), hoursEntry.getExpectedHoursQuantity());
		assertEquals(Long.valueOf(1), hoursEntry.getTimePeriodTypeId());

	}

	@Test
	public void testReviewEntryConvertorFunction(){

		Function<PurposeAssignment, ReviewerEntry> reviewWntryFunction = timeKeepingAdapter.reviewEntryConvertorFunction;

		PurposeAssignment assignmentMock = mock(PurposeAssignment.class);
		AdapterHelper adapterHelperMock = mock(AdapterHelper.class);
		timeKeepingAdapter.setAdapterHelper(adapterHelperMock);

		when(assignmentMock.getPurposeName()).thenReturn("Purpose Namer");
		when(assignmentMock.getRequestReviewerListName()).thenReturn("Review List");


		ReviewerEntry reviewerEntry = reviewWntryFunction.apply(assignmentMock);

	}

	@Test
	public void testAccessAssignmentProperties(){

		MultiManagerRoleProxyService multiManagerRoleProxyService = mock(MultiManagerRoleProxyService.class);
		when(multiManagerRoleProxyService.isUserOnMultiManagerRole(any())).thenReturn(false);
		mockedSpringContext.when(() -> SpringContext.getBean(MultiManagerRoleProxyService.class)).thenReturn(multiManagerRoleProxyService);

		TimekeepingExtension ext = new TimekeepingExtension();

		AccessAssignment accessAssignmentMock = mock(AccessAssignment.class);

		when(accessAssignmentMock.getTransferEmployee()).thenReturn(true);
		when(accessAssignmentMock.getApproveOvertime()).thenReturn(false);
		when(accessAssignmentMock.getManagerWorkRuleId()).thenReturn(new ObjectIdLong(1L));
		when(accessAssignmentMock.getSseWorkRuleId()).thenReturn(new ObjectIdLong(2L));
		when(accessAssignmentMock.getManagerLaborCategoryProfileId()).thenReturn(new ObjectIdLong(3L));
		when(accessAssignmentMock.getEmployeeLaborCategoryProfileId()).thenReturn(new ObjectIdLong(4L));
		when(accessAssignmentMock.getManagerPayCodeId()).thenReturn(new ObjectIdLong(5L));
		when(accessAssignmentMock.getManagerViewPayCodeId()).thenReturn(new ObjectIdLong(6L));
		when(accessAssignmentMock.getSsePayCodeId()).thenReturn(new ObjectIdLong(7L));

		accessAssignmentAdapterHelper.setTimeKeepingAccessProperties(ext, accessAssignmentMock );

		assertEquals(Long.valueOf(1), ext.getAccessAssignmentDetails().getWorkRuleProfileId());
		assertEquals(true, ext.getAccessAssignmentDetails().isCanTransfer());
		assertEquals(false, ext.getAccessAssignmentDetails().isCanApproveOvertime());
		assertEquals(Long.valueOf(2), ext.getAccessAssignmentDetails().getSseWorkRuleProfileId());
		assertEquals(Long.valueOf(3), ext.getAccessAssignmentDetails().getLaborLevelTransfersetId());
		assertEquals(Long.valueOf(4), ext.getAccessAssignmentDetails().getSseLaborLevelTransfersetId());
		assertEquals(Long.valueOf(5), ext.getAccessAssignmentDetails().getPayCodeProfileId());
		assertEquals(Long.valueOf(6), ext.getAccessAssignmentDetails().getPayCodeViewProfileId());
		assertEquals(Long.valueOf(7), ext.getAccessAssignmentDetails().getSsePayCodeId());

	}

	@Test
	public void setAccessAssignmentEffDatedDetails(){

		TimekeepingExtension ext = new TimekeepingExtension();
		ArrayList<AssignEffDatedTimeEntry> l = new ArrayList<AssignEffDatedTimeEntry>();
		accessAssignmentAdapterHelper.accessAssignmentEffDatedConvertorFunction = a->{
			AssignEffDatedTimeEntry mock = mock(AssignEffDatedTimeEntry.class);
			l.add(mock);
			return mock;
		};
		assertTrue(l.isEmpty(), "testing accessAssignment");

	}

	@Test
	public void testaccessAssignmentEffDatedConvertorFunction() {
		AssignTimeEntry assignTimeEntryMock = mock(AssignTimeEntry.class);

		when(assignTimeEntryMock.getAssignTimeEntryId()).thenReturn(new ObjectIdLong(1L));
		when(assignTimeEntryMock.getTimeEntryType()).thenReturn(new ObjectIdLong(2L));
		when(assignTimeEntryMock.getVersionCnt()).thenReturn(Long.valueOf(3L));

		AssignEffDatedTimeEntry assignObj=accessAssignmentAdapterHelper.accessAssignmentEffDatedConvertorFunction.apply(assignTimeEntryMock);


		assertEquals(Long.valueOf(1L), assignObj.getAssignTimeEntryId());
		assertEquals(Long.valueOf(2L), assignObj.getTimeEntryTypeId());
		assertEquals(Long.valueOf(3L), assignObj.getVersionCount());

	}

	public void testSetJobAssignmentProperties() {
		timeKeepingAdapter = spy(TimeKeepingAdapter.class);

	}

	@Test
	public void testgetDoubleFromKCurrency(){
		KCurrency kc = new KCurrency();
		assertEquals(Double.valueOf(0.0D), timeKeepingAdapter.getDoubleFromKCurrency(kc));
		KCurrency kc1 = new KCurrency(23.8);
		assertEquals(Double.valueOf(23.8D), timeKeepingAdapter.getDoubleFromKCurrency(kc1));
		KCurrency kc2 = null;
		assertNull(timeKeepingAdapter.getDoubleFromKCurrency(kc2));
	}

	@Test
	public void testCreateSnapshot() {
		TimekeepingExtension expectedExtension = new TimekeepingExtension();
		expectedExtension.setReviewers(Collections.emptyList());
		expectedExtension.setPayRules(new EffectiveDatedCollection<>());
		expectedExtension.setWorkerTypeId(1L);

		TimekeepingExtension actualExtension = timeKeepingAdapter.createSnapshot(expectedExtension);
		assertEquals(expectedExtension, actualExtension);
		assertNotSame(expectedExtension, actualExtension);
	}

	@Test
	public void testCreateSnapshotWithNull() {
		TimekeepingExtension actualExtension = timeKeepingAdapter.createSnapshot(null);
		assertNull(actualExtension);
	}
}