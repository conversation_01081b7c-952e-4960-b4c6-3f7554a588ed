package com.kronos.people.personality.dataaccess.legacy;

import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.model.extension.entry.PersonCustomDataEntry;
import com.kronos.people.personality.model.extension.entry.PersonDatesEntry;
import com.kronos.wfc.commonapp.people.business.person.*;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.types.business.CustomDateType;
import com.kronos.wfc.platform.datetime.framework.KServer;
import com.kronos.wfc.platform.persistence.framework.ObjectId;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.PersistentIterator;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.text.SimpleDateFormat;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonDatesHelperMicroTest {
	
	@InjectMocks
	PersonDatesHelper personDatesHelper;
	@Mock
	Personality personalityMock;
	@Mock
	Person person;
	@Mock
	CustomDateType customDateTypeMock;
	@Mock
	CustomDateSet customDateSetMock;
	@Mock
	CustomDate customDateMock;
	@Spy
	AdapterHelper adapterHelper=null;
	private static final String DATE_FORMAT="yyyy-MM-dd";

	@BeforeEach
	public void setup() {
	}

	@Test
	public void testGetDateOfHire() {
		when(personalityMock.getNameData()).thenReturn(person);
		when(person.getHireDate()).thenReturn(KDate.create(2015, 12, 18));
		String actualHireDate = personDatesHelper.getDateOfHire(personalityMock);
		assertEquals(actualHireDate, KServer.dateToString(person.getHireDate(), new SimpleDateFormat(DATE_FORMAT)));

		when(personalityMock.getNameData()).thenReturn(null);
		String actualHireDate2 = personDatesHelper.getDateOfHire(personalityMock);
		assertEquals(actualHireDate2, "");

		when(personalityMock.getNameData()).thenReturn(person);
		when(person.getHireDate()).thenReturn(null);
		String actualHireDate3 = personDatesHelper.getDateOfHire(personalityMock);
		assertEquals(actualHireDate3, "");

	}

	@Test
	public void testGetDateOfHire_2() {

		when(customDateTypeMock.getSiteWideScopeSwitch()).thenReturn(123L);
		when(customDateTypeMock.getSiteWidedDateTime()).thenReturn(KDate.create(2015, 12, 18));
		String actualDateofHire = personDatesHelper.getDateOfHire(customDateSetMock, personalityMock, customDateTypeMock);
		assertEquals( "2015-12-18", actualDateofHire);

		Person person = mock(Person.class);
		when(personalityMock.getNameData()).thenReturn(person);
		when(person.getHireDate()).thenReturn(KDate.create(2015, 12, 18));
		String actualDateofHire2 = personDatesHelper.getDateOfHire(customDateSetMock, personalityMock, null);
		assertEquals(actualDateofHire2, KServer.dateToString(person.getHireDate(), new SimpleDateFormat(DATE_FORMAT)));

	}

	@Test
	public void testGetDateOfHire_3() {

		CustomDateType customDateTypeMock=mock(CustomDateType.class);
		CustomDateSet  customDateSetMock= mock(CustomDateSet.class);
		when(customDateTypeMock.getSiteWideScopeSwitch()).thenReturn(Long.valueOf(0));
		ObjectId objectId = getObjectId(false, 0);
		when(customDateTypeMock.getObjectId()).thenReturn(objectId);
		when(customDateSetMock.getCustomDate(customDateTypeMock.getObjectId())).thenReturn(null);
		String actualDateofHire3 = personDatesHelper.getDateOfHire(customDateSetMock, personalityMock, customDateTypeMock);
		assertEquals(actualDateofHire3, "");
	}

	@Test
	public void findDefaultDateValue() {
		CustomDateType customDateTypeMock=mock(CustomDateType.class);
		CustomDateSet  customDateSetMock= mock(CustomDateSet.class);
		when(customDateTypeMock.getSiteWideScopeSwitch()).thenReturn(Long.valueOf(123l));
		when(customDateTypeMock.getSiteWidedDateTime()).thenReturn(KDate.create(2015, 12, 18));
		String actualDefaultDateValue = personDatesHelper.findDefaultDateValue(customDateTypeMock, customDateSetMock, personalityMock, true);

		String[] expected = actualDefaultDateValue.split("-");
		String[] result = customDateTypeMock.getSiteWidedDateTime().toString().split("/");
		assertEquals(expected[0], result[2]);
		assertEquals(expected[1], result[0]);
		assertEquals(expected[2], result[1]);

		when(customDateTypeMock.getSiteWideScopeSwitch()).thenReturn(Long.valueOf(0));
		ObjectId objectId =getObjectId(false, 0);
		when(customDateTypeMock.getObjectId()).thenReturn(objectId);
		when(customDateSetMock.getCustomDate(customDateTypeMock.getObjectId())).thenReturn(null);
		String actualDefaultDateValue2 = personDatesHelper.findDefaultDateValue(customDateTypeMock, customDateSetMock, personalityMock, true);
		assertEquals(actualDefaultDateValue2, "");

		CustomDate customDateMock = mock(CustomDate.class);
		when(customDateTypeMock.getObjectId()).thenReturn(objectId);
		when(customDateSetMock.getCustomDate(customDateTypeMock.getObjectId())).thenReturn(customDateMock);
		when(customDateMock.getActualCustomDate()).thenReturn(KDate.create(2015, 12, 18));
		String actualDefaultDateValue3 = personDatesHelper.findDefaultDateValue(customDateTypeMock, customDateSetMock, personalityMock, true);
		assertEquals(actualDefaultDateValue3, KServer.dateToString(customDateMock.getActualCustomDate(), new SimpleDateFormat(DATE_FORMAT)));
	}

	@Test
	public void getDefaultDateValue_2() {

		when(customDateTypeMock.getSiteWideScopeSwitch()).thenReturn(Long.valueOf(123l));
		when(customDateTypeMock.getSiteWidedDateTime()).thenReturn(KDate.create(2015, 12, 18));
		String actualDefaultValue = personDatesHelper.getDefaultDateValue(customDateSetMock, personalityMock, true, customDateTypeMock);
		assertEquals(actualDefaultValue, "2015-12-18");

		String actualDefaultValue2 = personDatesHelper.getDefaultDateValue(customDateSetMock, personalityMock, false, customDateTypeMock);
		assertEquals(actualDefaultValue2, "2015-12-18");

		String actualDefaultValue3 = personDatesHelper.getDefaultDateValue(customDateSetMock, personalityMock, false, null);
		assertEquals(actualDefaultValue3, "");
	}

	@Test
	public void testGetPersonDates() {
		CustomDateType customDateTypeMock=mock(CustomDateType.class);
		CustomDateSet  customDateSetMock= mock(CustomDateSet.class);
		when(personalityMock.getCustomDates()).thenReturn(null);
		List<PersonDatesEntry> actualList = personDatesHelper.getPersonDates(personalityMock,adapterHelper);
		assertEquals(0, actualList.size());

		PersistentIterator persistentIteratorMock = mock(PersistentIterator.class);
		when(customDateSetMock.iterator(Mockito.any())).thenReturn(persistentIteratorMock);
		when(customDateSetMock.size()).thenReturn(1);
		when(personalityMock.getCustomDates()).thenReturn(customDateSetMock);
		when(customDateMock.getShortName()).thenReturn("abc");
		when(customDateMock.getActualCustomDate()).thenReturn(KDate.create(2015, 12, 18));
		when(customDateMock.getCustomDateType()).thenReturn(customDateTypeMock);
		ObjectIdLong objectIdLong = new ObjectIdLong(123l);
		when(customDateTypeMock.getDefCustomDateTypId()).thenReturn(objectIdLong);
		List<PersonDatesEntry> actualList2 = personDatesHelper.getPersonDates(personalityMock,adapterHelper);
		assertEquals(0, actualList2.size());
	}
	
	@Test
	public void testGetPersonDateEntry() {
		Personality currentPersonality = mock(Personality.class);
		AdapterHelper converterHelper = mock(AdapterHelper.class);
		
		personDatesHelper.defaultDateFunction = customDate->customDateTypeMock;
		when(customDateMock.getActualCustomDate()).thenReturn(KDate.create(2015, 12, 18));
		when(customDateTypeMock.getSiteWideScopeSwitch()).thenReturn(Long.valueOf(0));
		ObjectId objectId = getObjectId(false, 0);
		when(customDateTypeMock.getObjectId()).thenReturn(objectId);
		
		when(customDateSetMock.getCustomDate(customDateTypeMock.getObjectId())).thenReturn(null);
		personDatesHelper.getPersonDateEntry(currentPersonality, converterHelper, customDateSetMock, customDateMock);
		
	}
	

	@Test
	public void testGetPersonDateEntryNull() {
		Personality currentPersonality = mock(Personality.class);
		AdapterHelper converterHelper = mock(AdapterHelper.class);
		
		personDatesHelper.defaultDateFunction = customDate-> null;
		when(customDateMock.getActualCustomDate()).thenReturn(KDate.create(2015, 12, 18));
		when(customDateTypeMock.getSiteWideScopeSwitch()).thenReturn(Long.valueOf(0));
		ObjectId objectId = getObjectId(false, 0);
		when(customDateTypeMock.getObjectId()).thenReturn(objectId);
		when(currentPersonality.getNameData()).thenReturn(person);
		when(person.getHireDate()).thenReturn(KDate.create(2015, 12, 18));
		
		when(customDateSetMock.getCustomDate(customDateTypeMock.getObjectId())).thenReturn(null);
		personDatesHelper.getPersonDateEntry(currentPersonality, converterHelper, customDateSetMock, customDateMock);
	}
	
	@Test
	public void testDefaultDateFunction() {
		ObjectIdLong objectIdLong = null;
		when(customDateMock.getCustomDateType()).thenReturn(customDateTypeMock);
		when(customDateTypeMock.getDefCustomDateTypId()).thenReturn(objectIdLong);
		personDatesHelper.converter = new AdapterHelper(); 
		//when(adapterHelper.extractIfObjectIdLongNotNull(objectIdLong, id->CustomDateTypeCache.getCustomDateType(id))).thenReturn(customDateTypeMock);
		CustomDateType defaultDate = personDatesHelper.defaultDateFunction.apply(customDateMock);
		
		assertNull(defaultDate);
	}

	private ObjectId getObjectId(boolean isNullOutput, int paramObjectOutput) {
		return new ObjectId() {

			@Override
			public boolean isNull() {
				return isNullOutput;
			}

			@Override
			public int compareTo(Object paramObject) {
				return paramObjectOutput;
			}
		};
	}

	
	
	@SuppressWarnings("deprecation")
	@Test
	public void testGetPersonCustomDataWhenCustomDatesNull(){
		@SuppressWarnings("static-access")
		List<PersonCustomDataEntry> pce = personDatesHelper.getPersonCustomData(personalityMock);
		assertEquals(0, pce.size());
	}
	
	@SuppressWarnings("deprecation")
	@Test
	public void testGetPersonCustomData(){
		when(personalityMock.getCustomDates()).thenReturn(customDateSetMock);
		CustomDataSet customDataSetMock = mock(CustomDataSet.class);
		when(personalityMock.getCustomData()).thenReturn(customDataSetMock);
		PersistentIterator  persistentIteratorMock = mock(PersistentIterator.class);
		when(customDataSetMock.iterator()).thenReturn(persistentIteratorMock);
		Mockito.when(persistentIteratorMock.hasNext()).thenReturn(true,false);
		CustomData  customDataMockm= mock(CustomData.class);
		Mockito.when(persistentIteratorMock.next()).thenReturn(customDataMockm);
		AdapterHelper  adapterHelperMock = mock(AdapterHelper.class);
		when(customDataMockm.getCustomDataTypeId()).thenReturn(new ObjectIdLong(3));
		when(customDataMockm.getCustomText()).thenReturn("customText");
		when(customDataMockm.getVersionCount()).thenReturn(1L);
		when(adapterHelperMock.getLongFromObjectId(mock(ObjectIdLong.class))).thenReturn(3l);
		personDatesHelper.converter = adapterHelperMock;
		@SuppressWarnings("static-access")
		List<PersonCustomDataEntry> pce = personDatesHelper.getPersonCustomData(personalityMock);
		assertEquals(1, pce.size());
	}

}
