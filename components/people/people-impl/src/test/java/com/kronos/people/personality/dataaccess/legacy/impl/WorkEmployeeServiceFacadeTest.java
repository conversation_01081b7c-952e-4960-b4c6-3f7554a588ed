/*******************************************************************************
 * WorkEmployeeServiceFacadeTest.java
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.dataaccess.legacy.impl;

import com.kronos.people.personality.model.extension.entry.WorkEmployeeEntry;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.ukg.container.selectiveloading.exception.NotImplementedException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * The class to unit test methods of WorkEmployeeServiceFacade.
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class WorkEmployeeServiceFacadeTest {

    private final WorkEmployeeServiceFacade workEmployeeServiceFacade = new WorkEmployeeServiceFacade();

    @Test
    public void testSaveWorkEmployee() {
        assertThrows(NotImplementedException.class, () -> {
            workEmployeeServiceFacade.saveWorkEmployee((new Personality()), new WorkEmployeeEntry(), true);
        });
    }

    @Test
    public void testGetEmployeeByPersonId() {
        assertThrows(NotImplementedException.class, () -> {
            workEmployeeServiceFacade.getEmployeeByPersonId(123L);
        });
    }

    @Test
    public void testValidateActivityProfile() {
        assertThrows(NotImplementedException.class, () -> {
            workEmployeeServiceFacade.validateActivityProfile("profile name");
        });
    }

    @Test
    public void testValidateUserField() {
        assertThrows(NotImplementedException.class, () -> {
            workEmployeeServiceFacade.validateUserField("name", "value");
        });
    }

    @Test
    public void testValidateActivity() {
        assertThrows(NotImplementedException.class, () -> {
            workEmployeeServiceFacade.validateActivity("Activity name");
        });
    }

    @Test
    public void testIsEffectiveDatedActivityDefaultsExist() {
        assertThrows(NotImplementedException.class, () -> {
            workEmployeeServiceFacade.isEffectiveDatedActivityDefaultsExist(123L);
        });
    }

    @Test
    public void testIsEffectiveDatedActivityDefaultsEnabled() {
        assertThrows(NotImplementedException.class, () -> {
            workEmployeeServiceFacade.isEffectiveDatedActivityDefaultsEnabled();
        });
    }

    @Test
    public void testGetActivityName() {
        assertThrows(NotImplementedException.class, () -> {
            workEmployeeServiceFacade.getActivityName(123L);
        });
    }

    @Test
    public void testGetProfileName() {
        assertThrows(NotImplementedException.class, () -> {
            workEmployeeServiceFacade.getProfileName(123L);
        });
    }

    @Test
    public void testGetQueryName() {
        assertThrows(NotImplementedException.class, () -> {
            workEmployeeServiceFacade.getQueryName(123L);
        });
    }

    @Test
    public void testValidateCurrentListQuery() {
        assertThrows(NotImplementedException.class, () -> {
            workEmployeeServiceFacade.validateCurrentListQuery("query name");
        });
    }
}
