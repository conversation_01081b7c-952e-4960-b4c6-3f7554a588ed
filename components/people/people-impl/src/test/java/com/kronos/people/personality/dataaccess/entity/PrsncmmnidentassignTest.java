/*******************************************************************************
 * PrsncmmnidentassignTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.dataaccess.entity;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

/*******************************************************************************
 * Test class for Prsncmmnidentassign
 * <AUTHOR> settypalli
 ******************************************************************************/
@ExtendWith(MockitoExtension.class)
public class PrsncmmnidentassignTest {
    @InjectMocks
   private Prsncmmnidentassign prsncmmnidentassign;

    @Test
    public void test_GetPersonId(){
        prsncmmnidentassign.setPersonid(12L);
        assertEquals(12L, (long) prsncmmnidentassign.getPersonid());
    }

    @Test
    public void test_SetPersonId(){
        prsncmmnidentassign.setPersonid(12L);
        assertEquals(12L, (long) prsncmmnidentassign.getPersonid());
    }
}
