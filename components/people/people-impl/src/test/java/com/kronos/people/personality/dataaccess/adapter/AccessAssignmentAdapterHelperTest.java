package com.kronos.people.personality.dataaccess.adapter;

import com.google.common.collect.ImmutableMap;
import com.kronos.container.api.access.SpringContext;
import com.kronos.people.personality.dataaccess.legacy.PersonalityConstants;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.proxy.api.service.AccessAssignmentProxyService;
import com.kronos.people.proxy.api.service.KioskEmployeePreferenceProxy;
import com.kronos.people.proxy.api.service.MultiManagerRoleProxyService;
import com.kronos.wfc.commonapp.people.business.person.AccessAssignment;
import com.kronos.wfc.platform.persistence.framework.ObjectId;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

/**
 * AccessAssignmentAdapterHelperTest.
 * Copyright (C) 2020 Kronos.com
 * Date: Mar 06, 2020
 *
 * <AUTHOR> Kuchynski
 */
@ExtendWith(MockitoExtension.class)
public class AccessAssignmentAdapterHelperTest {
	private static final ObjectIdLong APPROVAL_METHOD_ID = new ObjectIdLong(0L);
    private static final ObjectIdLong ACCESS_PROFILE_ID = new ObjectIdLong(1L);
    private static final ObjectIdLong PREFERENCE_PROFILE_ID = new ObjectIdLong(2L);
    private static final ObjectIdLong LOCALE_PROFILE_ID = new ObjectIdLong(3L);
    private static final ObjectIdLong NOTIFICATION_PROFILE_ID = new ObjectIdLong(4L);
    private static final ObjectIdLong DELEGATE_PROFILE_ID = new ObjectIdLong(5L);
    private static final ObjectIdLong PROCESS_MANAGER_PROFILE_ID = new ObjectIdLong(6L);
    private static final ObjectIdLong PROCESS_EMPLOYEE_PROFILE_ID = new ObjectIdLong(7L);
    private static final ObjectIdLong ACCESS_METHOD_PROFILE_ID = new ObjectIdLong(8L);
    
    @Mock
    private AccessAssignment accessAssignment;

    @Mock
    private AdapterHelper adapterHelper;

    @Mock
    private PersonalityFacade personalityFacade;

    @Mock
    private MultiManagerRoleProxyService multiManagerRoleProxyService;
    
    @Mock
    private KioskEmployeePreferenceProxy kioskEmployeePreferenceProxy;
    
    @Mock
    private AccessAssignmentProxyService accessAssignmentProxyService;

    @InjectMocks
    private AccessAssignmentAdapterHelper helper;

    private MockedStatic<SpringContext> mockedSpringContext;
    private MockedStatic<AccessAssignment> mockedAccessAssignment;

    @BeforeEach
    public void setUp() {
        mockedSpringContext = Mockito.mockStatic(SpringContext.class);
        mockedAccessAssignment = Mockito.mockStatic(AccessAssignment.class);
    }

    @AfterEach
    public void close(){
        mockedSpringContext.close();
        mockedAccessAssignment.close();
    }

   @Test
    public void testSetAccessAssignmentAttributes() {
        when(multiManagerRoleProxyService.isUserOnMultiManagerRole(any())).thenReturn(false);
        when(kioskEmployeePreferenceProxy.getKioskEmployeePreferenceProfileId()).thenReturn(null);
//        when(accessAssignmentProxyService.isUserAMPAssigned()).thenReturn(true);
//        when(accessAssignmentProxyService.getAMPProfileId(1)).thenReturn(1L);
//        when(accessAssignmentProxyService.getAMPProfileId(2)).thenReturn(2L);
        mockedSpringContext.when(() -> SpringContext.getBean(MultiManagerRoleProxyService.class)).thenReturn(multiManagerRoleProxyService);
        mockedSpringContext.when(() -> SpringContext.getBean(KioskEmployeePreferenceProxy.class)).thenReturn(kioskEmployeePreferenceProxy);
        mockedSpringContext.when(() -> SpringContext.getBean(AccessAssignmentProxyService.class)).thenReturn(accessAssignmentProxyService);

        Map<String, Object> processProfile = ImmutableMap.<String, Object>builder()
                .put(PersonalityConstants.MANAGER_WORKFLOW_PROFILE_ID.getValue(), PROCESS_MANAGER_PROFILE_ID)
                .put(PersonalityConstants.EMPLOYEE_WORKFLOW_PROFILE_ID.getValue(), PROCESS_EMPLOYEE_PROFILE_ID)
                .build();
        when(accessAssignment.getAccessProfileId()).thenReturn(ACCESS_PROFILE_ID);
        when(accessAssignment.getPreferenceProfileId()).thenReturn(PREFERENCE_PROFILE_ID);
        when(accessAssignment.getLocaleProfileId()).thenReturn(LOCALE_PROFILE_ID);
        when(accessAssignment.getNotificationProfileId()).thenReturn(NOTIFICATION_PROFILE_ID);
        when(accessAssignment.getDelegateProfileId()).thenReturn(DELEGATE_PROFILE_ID);
        when(accessAssignment.getApprovalMethodId()).thenReturn(APPROVAL_METHOD_ID);
        when(accessAssignment.getAccessMethodProfileId()).thenReturn(ACCESS_METHOD_PROFILE_ID);
        when(personalityFacade.getProcessManagerProfileName(accessAssignment)).thenReturn(processProfile);
        doAnswer(answer -> {
            ObjectIdLong id = (ObjectIdLong) answer.getArguments()[0];
            return id.toLong();
        }).when(adapterHelper).getLongFromObjectIdLong(any(ObjectIdLong.class));
        when(adapterHelper.getLongFromObjectId(any(ObjectId.class))).thenCallRealMethod();
        EmployeeExtension employeeExtension = new EmployeeExtension();
        helper.setAccessAssignmentAttributes(employeeExtension, accessAssignment);

        assertEquals(APPROVAL_METHOD_ID.toLong(), employeeExtension.getApprovalMethodId());
        assertEquals(ACCESS_PROFILE_ID.toLong(), employeeExtension.getAccessProfileId());
        assertEquals(PREFERENCE_PROFILE_ID.toLong(), employeeExtension.getPreferenceProfileId());
        assertEquals(LOCALE_PROFILE_ID.toLong(), employeeExtension.getLocaleProfileId());
        assertEquals(NOTIFICATION_PROFILE_ID.toLong(), employeeExtension.getNotificationProfileId());
        assertEquals(DELEGATE_PROFILE_ID.toLong(), employeeExtension.getDelegateProfileId());
        assertEquals(PROCESS_MANAGER_PROFILE_ID.toLong(), employeeExtension.getProcessManagerProfileId());
        assertEquals(PROCESS_EMPLOYEE_PROFILE_ID.toLong(), employeeExtension.getProcessEmployeeProfileId());
        assertEquals(ACCESS_METHOD_PROFILE_ID.toLong(), employeeExtension.getAccessMethodProfileId());
    }
}
