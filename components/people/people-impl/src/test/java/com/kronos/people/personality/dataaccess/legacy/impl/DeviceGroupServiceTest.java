/*******************************************************************************
 * DeviceGroupServiceTest.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.dataaccess.legacy.impl;

import com.ukg.container.selectiveloading.exception.NotImplementedException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * The class to unit test methods of DeviceGroupService.
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class DeviceGroupServiceTest {

    @InjectMocks
    private DeviceGroupService deviceGroupService;

    @Test
    public void testGetAllDeviceGroups(){
        assertThrows(NotImplementedException.class, () -> {
            deviceGroupService.getAllDeviceGroups();
        });
    }

    @Test
    public void testGetDeviceGroupsByIds(){
        assertThrows(NotImplementedException.class, () -> {
            deviceGroupService.getDeviceGroupsByIds(new ArrayList<>());
        });
    }

    @Test
    public void testGetDeviceGroupNameById(){
        assertThrows(NotImplementedException.class, () -> {
            deviceGroupService.getDeviceGroupNameById(123L);
        });
    }

    @Test
    public void testGetDeviceGroupIdByName(){
        assertThrows(NotImplementedException.class, () -> {
            deviceGroupService.getDeviceGroupIdByName("GroupId Name");
        });
    }

    @Test
    public void testGetDeviceGroupNameByEmployeeId(){
        assertThrows(NotImplementedException.class, () -> {
            deviceGroupService.getDeviceGroupNameByEmployeeId(123L);
        });
    }

    @Test
    public void testGetDeviceGroupIdByEmployeeId(){
        assertThrows(NotImplementedException.class, () -> {
            deviceGroupService.getDeviceGroupIdByEmployeeId(123L);
        });
    }

    @Test
    public void testUpdateEmployeeDeviceGroup(){
        assertThrows(NotImplementedException.class, () -> {
            deviceGroupService.updateEmployeeDeviceGroup(123L,456L);
        });
    }
}
