package com.kronos.people;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.FutureTask;
import java.util.concurrent.RunnableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

public class MockExecutorService implements ExecutorService {

	public int callables = 0;
		@Override
		public void execute(Runnable command) {
			command.run();
		}
		
		@Override
		public <T> Future<T> submit(Runnable task, T result) {
			task.run();
			return null;
		}
		
		@Override
		public Future<?> submit(Runnable task) {
			task.run();
			return null;
		}
		
		@Override
		public <T> Future<T> submit(Callable<T> task) {
			callables++;
			RunnableFuture<T> ftask = new FutureTask<T>(task);
	        execute(ftask);
	        return ftask;
		}
		
		@Override
		public List<Runnable> shutdownNow() {
			return null;
		}
		
		@Override
		public void shutdown() {
			
		}
		
		@Override
		public boolean isTerminated() {
			return false;
		}
		
		@Override
		public boolean isShutdown() {
			return false;
		}
		
		@Override
		public <T> T invokeAny(Collection<? extends Callable<T>> tasks,
				long timeout, TimeUnit unit) throws InterruptedException,
				ExecutionException, TimeoutException {
			return null;
		}
		
		@Override
		public <T> T invokeAny(Collection<? extends Callable<T>> tasks)
				throws InterruptedException, ExecutionException {
			return null;
		}
		
		@Override
		public <T> List<Future<T>> invokeAll(
				Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit)
				throws InterruptedException {
			return null;
		}
		
		@Override
		public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks)
				throws InterruptedException {
			return tasks.stream().map(this::submit).collect(Collectors.toList());
		}
		
		@Override
		public boolean awaitTermination(long timeout, TimeUnit unit)
				throws InterruptedException {
			return false;
		}
	}