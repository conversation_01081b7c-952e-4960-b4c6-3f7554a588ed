package com.kronos.people.personality.service.impl;

import com.kronos.commonapp.orgmap.setup.model.OrgNodeReferenceUsage;
import com.kronos.people.personality.dataaccess.repository.PersonRepository;

import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;


//@RunWith(PowerMockRunner.class)
//@PowerMockIgnore("javax.management.*")
@ExtendWith(MockitoExtension.class)
public class PersonOrgMapReferenceFullDeleteValidatorImplTest {
	public static final String PERSON = "person_job";

	@Inject
	@InjectMocks
	private PersonOrgMapReferenceFullDeleteValidatorImpl positionOrgMapReferenceService;

	@Mock
	private PersonRepository personRepository;



	@Test
	public void existingBlockingOrgReferences() {
		List<Long> orgNodeIds =  new ArrayList<>();
		List<Object[]> objList = new ArrayList<>();

		orgNodeIds.add(395L);

		objList.add(new Object[]{new BigInteger(String.valueOf(3)),new BigInteger(String.valueOf(395)), "20001", "abc"});

		when(personRepository.findByOrgNodeIds(orgNodeIds, 10)).thenReturn(objList);

		List<OrgNodeReferenceUsage> orgNodeReferenceUsages = positionOrgMapReferenceService.existingBlockingOrgReferences(orgNodeIds, 10);
		assertEquals(orgNodeReferenceUsages.size(), orgNodeIds.size());
	}

	@Test
	public void getEntityType() {
		String entityType = positionOrgMapReferenceService.getEntityType();
		assertEquals(PERSON, entityType);
	}
}
