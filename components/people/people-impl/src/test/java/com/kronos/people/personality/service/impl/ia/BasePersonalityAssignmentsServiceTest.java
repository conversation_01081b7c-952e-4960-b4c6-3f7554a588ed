/*******************************************************************************
 * BasePersonalityAssignmentsServiceTest.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.service.impl.ia;


import com.kronos.commonbusiness.datatypes.ia.IAColumn;
import com.kronos.commonbusiness.datatypes.ia.IARequest;
import com.kronos.commonbusiness.datatypes.ia.IAResponse;
import com.kronos.commonbusiness.datatypes.ia.IARow;
import com.kronos.commonbusiness.datatypes.ia.IAErrorDetail;
import com.kronos.people.personality.exception.BaseException;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import com.kronos.people.personality.exception.impl.ia.PersonalityExceptionHelper;
import com.kronos.people.personality.model.extension.entry.ApproverEntry;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;
import java.util.List;
import java.util.HashMap;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;


/**
 * Verifies {@link BasePersonalityAssignmentsService}.
 * Copyright (C) 2019 Kronos.com
 * Date: Jun 25, 2019
 *
 * <AUTHOR> Sharma
 */
//@RunWith(MockitoJUnitRunner.class)
    @ExtendWith(MockitoExtension.class)
public class BasePersonalityAssignmentsServiceTest {

    @Mock
    private PersonalityExceptionHelper exceptionHelper;

    private BasePersonalityAssignmentsService basePersonalityAssignmentsService;

    @BeforeEach
    public void setUp() throws Exception {
        basePersonalityAssignmentsService = new BasePersonalityAssignmentsService() {
            @Override
            protected String getEntityName() {
                return null;
            }

            @Override
            protected IAResponse getAssignmentData(IARequest request, IAResponse response, List<Long> employeeIds, List<PersonalityExtensionException> exceptions) {
                IARow iaRow = new IARow();
                iaRow.setDimensionKey("dimensionKey");
                List<IARow> list = new ArrayList<>();
                list.add(iaRow);
                response.setData(list);
                return response;
            }

            @Override
            protected Consumer<IAColumn> populateColumns(IARow row, ApproverEntry approverEntry, Map<Long, String> approverFullNameMap) {
                return null;
            }
        };
    }

    @Test
    public void testGetDataWhenBasePersonalityAssignmentsServiceGetAssignmentData() {
        IARequest iaRequest = new IARequest();
        List<String> empList = Arrays.asList("1","2");
        iaRequest.setEmployees(empList);
        IAResponse result = basePersonalityAssignmentsService.getData(iaRequest);
        assertNotNull(result);
        assertEquals("dimensionKey",result.getData().get(0).getDimensionKey());
    }

    @Test
    public void testGetDataWhenBasePersonalityAssignmentsServiceGetAssignmentDataThenThrowsBaseException() {
        basePersonalityAssignmentsService = Mockito.spy(BasePersonalityAssignmentsService.class);
        basePersonalityAssignmentsService.exceptionHelper=exceptionHelper;
        IARequest iaRequest = new IARequest();
        List<String> empList = Arrays.asList("1","2");
        iaRequest.setEmployees(empList);
        Map<String, String> messageArgsMap = new HashMap<>();
       BaseException baseException = new BaseException(PersonalityErrorCode.PERSON_ID_NOT_VALID,"invalid person","invalid person",messageArgsMap);
       Mockito.when(basePersonalityAssignmentsService.getAssignmentData( Mockito.any(),Mockito.any(), Mockito.any(), Mockito.any())).thenThrow(baseException);
        IAErrorDetail iaErrorDetail = new IAErrorDetail();
        iaErrorDetail.setCode("400");
        Mockito.when(exceptionHelper.buildCommonErrorDetail(Mockito.any())).thenReturn(iaErrorDetail);
        IAResponse result = basePersonalityAssignmentsService.getData(iaRequest);
        assertNotNull(result);
        assertNotNull(result.getErrorDetails());
        assertEquals(iaErrorDetail.getCode(),result.getErrorDetails().getCode());
    }
    @Test
    public void testColumnWhenBasePersonalityAssignmentsServiceAddColumn() {
        IARow iaRow = new IARow();
        String columnName = "testColumn";
        String columnValue = "testValue";
        IAColumn iaColumn = new IAColumn();
        iaColumn.setAlias(columnName);

        basePersonalityAssignmentsService.addColumn(iaRow, iaColumn, columnValue);
        assertEquals(columnValue,iaRow.getAttributes().get(0).getValue());
     }
    }


