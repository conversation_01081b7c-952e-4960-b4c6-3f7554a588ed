package com.kronos.people.personality.dataaccess.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiFunction;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;

import com.kronos.commonapp.orgmap.setup.model.OrgObjectRef;
import com.kronos.commonapp.orgmap.traversal.api.IOrgMapService;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.PrimaryJobAccountEntry;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignment;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignmentDetails;
import com.kronos.wfc.commonapp.people.business.jobassignment.PrimaryLaborAccount;
import com.kronos.wfc.commonapp.people.business.jobassignment.PrimaryLaborAccountSet;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.platform.datetime.framework.KTimeZone;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class EmployeeExtensionAdapterHelperMicroTest {
	
	private final class MockEmployeeExtensionAdapterHelper extends
			EmployeeExtensionAdapterHelper {
		String ret;
		public MockEmployeeExtensionAdapterHelper(String ret) {
			this.ret = ret;
		}
		@Override
		protected OrgObjectRef getObjectRef(Long l) {
			OrgObjectRef o = mock(OrgObjectRef.class);
			when(o.getQualifier()).thenReturn(ret);
			return o;
		}
	}

	private EmployeeExtensionAdapterHelper employeeExtensionAdapterHelper;
	private AdapterHelper adapterHelperMock;
	private PersonalityFacade personalityFacadeMock;

	@BeforeEach
	public void setup() {
		employeeExtensionAdapterHelper = new EmployeeExtensionAdapterHelper();
    	adapterHelperMock = mock(AdapterHelper.class);
    	personalityFacadeMock = mock(PersonalityFacade.class);
	}
	
	@AfterEach
    public void teardown(){
		employeeExtensionAdapterHelper = null;
		adapterHelperMock = null;
		personalityFacadeMock = null;
	}
	
	//@Test
	public void testPrimaryLaborAccountFunction(){
		
		AdapterHelper adapterHelper=new AdapterHelper();
		
		employeeExtensionAdapterHelper = new EmployeeExtensionAdapterHelper(){
			@Override
			protected String getLaborAccountName(PrimaryLaborAccount pla) {
				// TODO Auto-generated method stub
				return "abc";
			}
			
			@Override
			protected String getPrimaryJobName(ObjectIdLong o, KDate kd) {
				// TODO Auto-generated method stub
				return "Primary Job";
			}
		};
		
    	BiFunction<PrimaryLaborAccount, Long, PrimaryJobAccountEntry> primaryLaborAccountFunction = employeeExtensionAdapterHelper.primaryLaborAccountFunction;
    	employeeExtensionAdapterHelper.setConverterHelper(adapterHelper);
    	employeeExtensionAdapterHelper.setPersonalityFacade(personalityFacadeMock);
		
    	/*PrimaryLaborAccount laborAccount =new PrimaryLaborAccount();
    	PrimaryLaborAccountEffectiveDatedAttribute primaryLaborAccountEffectiveDatedAttribute = mock(PrimaryLaborAccountEffectiveDatedAttribute.class);
    	when(personalityFacadeMock.getPrimaryAccountEffectiveDatedAttributes(laborAccount)).thenReturn(primaryLaborAccountEffectiveDatedAttribute);
    	EffectiveDatedAttribute effectiveDatedAttribute = mock(EffectiveDatedAttribute.class);
    	when(primaryLaborAccountEffectiveDatedAttribute.getPrimaryJob()).thenReturn(effectiveDatedAttribute);
    	ReportDateBean dateBean1 = mock(ReportDateBean.class);
    	ReportDateBean dateBean2 = mock(ReportDateBean.class);*/
    	
    	
    	PrimaryLaborAccount primaryLaborAccount = mock(PrimaryLaborAccount.class);
    	when(primaryLaborAccount.getEffectiveDate()).thenReturn(KDate.create(2005, 9, 27));
    	when(primaryLaborAccount.getExpirationDate()).thenReturn(KDate.create(2014, 9, 27));
    	
//    	LaborAccount laborAccount=mock(LaborAccount.class);
//    	
//    	when(primaryLaborAccount.getLaborAccount()).thenReturn(laborAccount);
//    	when(primaryLaborAccount.getLaborAccount().getName()).thenReturn("abc");
//    	
    	
    	//AdapterHelper helper = new AdapterHelper();

    	PrimaryJobAccountEntry accountEntry = primaryLaborAccountFunction.apply(primaryLaborAccount, 1L);
    	
    	assertEquals("Primary Job", accountEntry.getPrimaryJob());
    	assertEquals("abc", accountEntry.getPrimaryLabourAccount());
    	assertEquals("abc", accountEntry.getPrimaryLaborCategory());
    	assertEquals(LocalDate.of(2005, 9, 27), accountEntry.getEffectiveDate());
    	assertEquals(LocalDate.of(2014, 9, 27), accountEntry.getExpirationDate());
    	
	}
	
	@Test
	public void testSupervisorFullName(){
		EmployeeExtension employeeExtension= new EmployeeExtension();
		employeeExtensionAdapterHelper.setPersonalityFacade(personalityFacadeMock);
		ObjectIdLong supervisorId  = new ObjectIdLong(1L);
		
		Person personMock =  mock(Person.class);
		when(personMock.getFullName()).thenReturn("Michaela Manning");
		when(personalityFacadeMock.getPerson(supervisorId)).thenReturn(personMock);
		
		
		employeeExtensionAdapterHelper.getSupervisorFullName(employeeExtension, supervisorId);
		
		assertEquals("Michaela Manning", employeeExtension.getSupervisorFullName());
	}
	
	@Test
	public void testSupervisorIfPersonIsNull(){
		EmployeeExtension employeeExtension= new EmployeeExtension();
		employeeExtensionAdapterHelper.setPersonalityFacade(personalityFacadeMock);
		ObjectIdLong supervisorId  = new ObjectIdLong(1L);
		
		Person personMock =  mock(Person.class);
		//when(personMock.getFullName()).thenReturn(null);
		
		when(personalityFacadeMock.getPerson(supervisorId)).thenReturn(personMock);
		employeeExtensionAdapterHelper.getSupervisorFullName(employeeExtension, supervisorId);
		assertNull(employeeExtension.getSupervisorFullName(), "Supervisor is null");
	}
	
	@Test
	public void testSetJobAssignmentDetailsProperties(){
		EmployeeExtension employeeExtension = new EmployeeExtension();
		JobAssignmentDetails jobAssignmentDetails = Mockito.mock(JobAssignmentDetails.class);
		
		employeeExtensionAdapterHelper.setConverterHelper(adapterHelperMock);
    	when(adapterHelperMock.getLongFromObjectIdLong(new ObjectIdLong(5))).thenReturn(5L);
    	
    	KTimeZone timeZoneMock = mock(KTimeZone.class);
//    	when(timeZoneMock.getObjectId()).thenReturn(new ObjectIdLong(13411));
//    	when(timeZoneMock.getName()).thenReturn("Eastern");
    	
    	when(jobAssignmentDetails.getSeniorityRankDate()).thenReturn(new KDate(2014,10,14));
//    	when(adapterHelperMock.kDateToLocalDate(KDate.createDate(2005,9,27))).thenReturn(LocalDate.of(2014,10,14));
    	
    	when(jobAssignmentDetails.getSupervisorId()).thenReturn(new ObjectIdLong(5));
    	when(adapterHelperMock.getLongFromObjectIdLong(new ObjectIdLong(5))).thenReturn(5L);
		
		employeeExtensionAdapterHelper.setJobAssignmentDetailsRelatedAttributes(employeeExtension, jobAssignmentDetails);
		
		assertEquals(Long.valueOf(5), employeeExtension.getSupervisorPersonId());
	}
	
	@Test
	public void testconvertEffDatedPrimaryJobAccount(){
		  	
    	PrimaryLaborAccount laborAccount = new PrimaryLaborAccount();
    	
		Collection<PrimaryLaborAccount> collection = new ArrayList<>();
		collection.add(laborAccount);
		
		employeeExtensionAdapterHelper = new EmployeeExtensionAdapterHelper();
			
			BiFunction<PrimaryLaborAccount, Long, PrimaryJobAccountEntry> primaryLaborAccountFunction = new BiFunction<PrimaryLaborAccount, Long, PrimaryJobAccountEntry>() {
				
				@Override
				public PrimaryJobAccountEntry apply(PrimaryLaborAccount t, Long personId) {
					PrimaryJobAccountEntry pje = new PrimaryJobAccountEntry();
					pje.setEffectiveDate(LocalDate.now());
					pje.setExpirationDate(LocalDate.now().plusYears(1));
					pje.setVersionCount(101L);
					return pje;
				}
			};
			employeeExtensionAdapterHelper.primaryLaborAccountFunction = primaryLaborAccountFunction;
		AdapterHelper adapterHelper = new AdapterHelper();
		employeeExtensionAdapterHelper.setConverterHelper(adapterHelper);
		
		EffectiveDatedCollection<PrimaryJobAccountEntry> jobEntry = employeeExtensionAdapterHelper.convertEffDatedPrimaryJobAccount(collection, 1L);
		List<PrimaryJobAccountEntry> entries = (List<PrimaryJobAccountEntry>) jobEntry.getAll();
		
		assertEquals(LocalDate.now(), entries.get(0).getEffectiveDate());
		assertEquals(Long.valueOf(101L), entries.get(0).getVersionCount());
		
	}
	
	@Test
	public void testSetJobAssignmentRelatedAttributes(){
		
		employeeExtensionAdapterHelper = spy(EmployeeExtensionAdapterHelper.class);
		EmployeeExtension empExtensionMock = mock(EmployeeExtension.class);
		JobAssignment jobAssignmentMock = mock(JobAssignment.class);
		PrimaryLaborAccountSet laborAccMock = mock(PrimaryLaborAccountSet.class);
		Collection<PrimaryLaborAccount> collection = new ArrayList<PrimaryLaborAccount>();
		when(laborAccMock.getAllMembers()).thenReturn((List) collection);
		when(jobAssignmentMock.getPrimaryLaborAccounts()).thenReturn(laborAccMock);
		JobAssignmentDetails jobAssignDetailsMock= mock(JobAssignmentDetails.class);
		when(jobAssignmentMock.getJobAssignmentDetails()).thenReturn(jobAssignDetailsMock);
		
		
		employeeExtensionAdapterHelper.setConverterHelper(adapterHelperMock);
		employeeExtensionAdapterHelper.setJobAssignmentRelatedAttributes(empExtensionMock, jobAssignmentMock);
		verify(employeeExtensionAdapterHelper, times(1)).setJobAssignmentDetailsRelatedAttributes(empExtensionMock, jobAssignDetailsMock);
	}

	@Test
	public void getPrimaryJobName()
	{
		IOrgMapService orgMapService = mock(IOrgMapService.class);
		//when(orgMapService.).
		ObjectIdLong objectIdLong=new ObjectIdLong(123l);
		employeeExtensionAdapterHelper=new MockEmployeeExtensionAdapterHelper("abc");
		employeeExtensionAdapterHelper.orgmapservice = orgMapService;
		employeeExtensionAdapterHelper.converterHelper = new AdapterHelper(); 
		KDate kDate = KDate.create(2015, 12, 18);
		when(orgMapService.resolve(any(OrgObjectRef.class), any())).then(returnsFirstArg());
		String actualPrimaryJobName=employeeExtensionAdapterHelper.getPrimaryJobName(objectIdLong, kDate);
		assertEquals(actualPrimaryJobName, "abc");
		
		String actualPrimaryJobName2=employeeExtensionAdapterHelper.getPrimaryJobName(null, kDate);
		assertNull(actualPrimaryJobName2);
	}
	
	@Test
	public void testCombineUserAndDerivedWhenUserIsEmptyString(){
		//String[] userLLEs = {"user1", "user2", "user3"};
		String[] userLLEs = {""};
		String[] derivedLLEs = {"derived1", "derived2", "derived3"};
		List<String> result = employeeExtensionAdapterHelper.combineUserAndLALLEs(userLLEs, derivedLLEs);
		assertEquals(1, result.size());
	}
	
	/*@Test
	public void testCombineUserAndDerivedWhenTryToIndexOutOfBound(){
		//String[] userLLEs = {"user1", "user2", "user3"};
		String[] userLLEs = {"abc","xyz", "aaa","zzz","","ttt",null, "asdf"};
		String[] derivedLLEs = {"derived1", "derived2", "derived3"};
		List<String> result = employeeExtensionAdapterHelper.combineUserAndLALLEs(userLLEs, derivedLLEs);
		assertEquals(1, result.size());
	}*/
	
	@Test
	public void testCombineUserAndDerivedWhenUserIsNull(){

		String[] userLLEs = null;
		String[] derivedLLEs = {"derived1", "derived2", "derived3"};
		List<String> result = employeeExtensionAdapterHelper.combineUserAndLALLEs(userLLEs, derivedLLEs);
		assertEquals(3, result.size());
	}
	
	@Test
	public void testCombineUserAndDerivedWhenUserIsEmptyArray(){

		String[] userLLEs = {};
		String[] derivedLLEs = {"derived1", "derived2", "derived3"};
		List<String> result = employeeExtensionAdapterHelper.combineUserAndLALLEs(userLLEs, derivedLLEs);
		assertEquals(0, result.size());
	}
	
	@Test
	public void testCombineUserAndDerivedWhenUserAndDerivedIsNull(){

		String[] userLLEs = null;
		String[] derivedLLEs = null;
		List<String> result = employeeExtensionAdapterHelper.combineUserAndLALLEs(userLLEs, derivedLLEs);
		assertEquals(0, result.size());
	}
	
	@Test
	public void testCombineUserAndDerivedWhenUserAndDerivedBothEmpty(){

		String[] userLLEs = {};
		String[] derivedLLEs = {};
		List<String> result = employeeExtensionAdapterHelper.combineUserAndLALLEs(userLLEs, derivedLLEs);
		assertEquals(0, result.size());
	}
	
	@Test
	public void testCombineUserAndDerivedWhenDerivedIsNull(){

		String[] userLLEs = {"user1", "user2", "user3"};
		String[] derivedLLEs = {};
		List<String> result = employeeExtensionAdapterHelper.combineUserAndLALLEs(userLLEs, derivedLLEs);
		assertEquals(3, result.size());
	}
	
	/*@Test
	public void testgetLaborAccountId(){
		
		PrimaryLaborAccount primaryLaborAccountMock = mock(PrimaryLaborAccount.class);
		//ObjectIdLong new ObjectIdLong(5);
		when(primaryLaborAccountMock.getEffectiveDate()).thenReturn(KDate.create(2005, 9, 27));
		employeeExtensionAdapterHelper.getLaborAccountId(primaryLaborAccountMock, new ObjectIdLong(5));
	}*/

	//@Test
	public void testGetLaborAccountName() {
		employeeExtensionAdapterHelper = new EmployeeExtensionAdapterHelper();
		PrimaryLaborAccount pla = new PrimaryLaborAccount();
		assertEquals("",employeeExtensionAdapterHelper.getLaborAccountName(pla));
	}
	
	//@Test
	public void testGetLaborAccountNameWithValues() {
		employeeExtensionAdapterHelper = new EmployeeExtensionAdapterHelper();
		PrimaryLaborAccount pla = new PrimaryLaborAccount();
		assertEquals("",employeeExtensionAdapterHelper.getLaborAccountName(pla));
	}
	
	@Test
	public void testGetPrimaryJobName() {
		//assertEquals("",employeeExtensionAdapterHelper.getPrimaryJobName(null, null));
		//assertNull(employeeExtensionAdapterHelper.getPrimaryJobName(mock(PrimaryLaborAccount.class)));
	}
	
	@Test
	public void testFormatLaborLevels() {
		EmployeeExtensionAdapterHelper employeeExtensionAdapterHelper = new EmployeeExtensionAdapterHelper();
		assertEquals("",employeeExtensionAdapterHelper.formatLaborLevels(new ArrayList<String>()));
		List<String> lles = new ArrayList<String>();
		lles.add("abc");
		assertEquals("abc",employeeExtensionAdapterHelper.formatLaborLevels(lles ));
		lles.add("xyz");
		assertEquals("abc/xyz",employeeExtensionAdapterHelper.formatLaborLevels(lles ));
	}
	
	@Test
	public void testgetLaborAccountName() {
		AtomicBoolean shouldReturnNull = new AtomicBoolean(false);
		EmployeeExtensionAdapterHelper employeeExtensionAdapterHelper = new EmployeeExtensionAdapterHelper() {
			@Override
			protected List<String> getLaborLevels(PrimaryLaborAccount pla) {
				if (shouldReturnNull.get()) return null;
				List<String> lles = new ArrayList<>();
				return lles;
			}
			
			@Override
			protected String formatLaborLevels(List<String> lles) {
				return "List";
			}
		};
		assertEquals("List", employeeExtensionAdapterHelper.getLaborAccountName(null));
		shouldReturnNull.set(true);
		assertEquals("", employeeExtensionAdapterHelper.getLaborAccountName(null));
	}
	
	/*@Test
	public void testgetLaborLevels() {
		EmployeeExtensionAdapterHelper employeeExtensionAdapterHelper = new EmployeeExtensionAdapterHelper();
		employeeExtensionAdapterHelper.getLaborLevels(mock(PrimaryLaborAccount.class));
	}*/
}
