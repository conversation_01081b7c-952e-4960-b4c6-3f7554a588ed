package com.kronos.people.personality.model.extension;


import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.access.SpringContext;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.GroupAssignmentEntry;
import com.kronos.people.personality.model.extension.entry.JobTransferEntry;
import com.kronos.people.proxy.api.service.AccessAssignmentProxyService;
import com.kronos.people.proxy.api.service.MultiManagerRoleProxyService;
import com.kronos.releasetoggle.api.ReleaseToggleService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;


@ExtendWith(MockitoExtension.class)
public class SchedulingExtensionMicroTest {

	private static final long GROUP_SCHEDULE_ID = 6L;

	SchedulingExtension schedulingExtension;

	private MockedStatic<SpringContext> mockedSpringContext;

	
	@BeforeEach
	public void setup() {
		schedulingExtension = new SchedulingExtension();
		mockedSpringContext = mockStatic(SpringContext.class);
	}

	@AfterEach
	public void tearDown() {
		mockedSpringContext.close();
	}
	
	@Test
	public void testGetGroupAssignmentsForSnapshot(){
		
		
		EffectiveDatedCollection<GroupAssignmentEntry> effectiveDatedCollection = new EffectiveDatedCollection<>();
//		List<GroupAssignmentEntry> list = new ArrayList<GroupAssignmentEntry>();
		List<GroupAssignmentEntry> list = new ArrayList<>();
//		List<GroupAssignmentEntry> snapshotList = new ArrayList<GroupAssignmentEntry>();
		List<GroupAssignmentEntry> snapshotList = new ArrayList<>();

		
		GroupAssignmentEntry groupAssignmentEntry1 = new GroupAssignmentEntry(LocalDate.of(2015, 10, 30),LocalDate.of(2017, 10, 30),"Group 1", 1L);
		list.add(groupAssignmentEntry1);
		snapshotList.add(groupAssignmentEntry1);

		GroupAssignmentEntry groupAssignmentEntry2 = new GroupAssignmentEntry(LocalDate.of(2011, 8, 30),LocalDate.of(2015, 11, 30),"Group 2", 2L);
		list.add(groupAssignmentEntry2);

		GroupAssignmentEntry groupAssignmentEntry3 = new GroupAssignmentEntry(LocalDate.of(2015, 10, 30),LocalDate.of(2021, 10, 29),"Group 3", 3L);
		list.add(groupAssignmentEntry3);
		snapshotList.add(groupAssignmentEntry3);


		effectiveDatedCollection.setEffectiveDatedEntries(list);
		
		schedulingExtension.setEffDatedGroupAssignment(effectiveDatedCollection);
		
		assertTrue(schedulingExtension.getGroupAssignments(LocalDate.of(2011, 8, 29)).isEmpty());
		assertEquals(snapshotList,schedulingExtension.getGroupAssignments(LocalDate.of(2015,12,30)));
		assertEquals(list, schedulingExtension.getGroupAssignments());

		assertTrue(schedulingExtension.getGroupAssignments(null).isEmpty());
		assertEquals(snapshotList,schedulingExtension.getGroupAssignments(LocalDate.of(2015,12,30)));
		assertTrue(schedulingExtension.getGroupAssignments(LocalDate.of(2010,12,30)).isEmpty());
		
		schedulingExtension.setEffDatedGroupAssignment(null);
//		assertEquals(null,schedulingExtension.getGroupAssignments());
        assertNull(schedulingExtension.getGroupAssignments());
//		assertEquals(null,schedulingExtension.getGroupAssignments(LocalDate.of(2010,12,30)));
        assertNull(schedulingExtension.getGroupAssignments(LocalDate.of(2010, 12, 30)));

	}
	
	@Test
	public void testGetJobTransferForSnapshot(){
		MultiManagerRoleProxyService multiManagerRoleProxyService = mock(MultiManagerRoleProxyService.class);
		when(multiManagerRoleProxyService.isUserOnMultiManagerRole(any())).thenReturn(false);
//		PowerMockito.mockStatic(SpringContext.class);
//		PowerMockito.when(SpringContext.getBean(MultiManagerRoleProxyService.class)).thenReturn(multiManagerRoleProxyService);
		mockedSpringContext.when(() -> SpringContext.getBean(MultiManagerRoleProxyService.class)).thenReturn(multiManagerRoleProxyService);
		EffectiveDatedCollection<JobTransferEntry> effectiveDatedCollection = new EffectiveDatedCollection<>();

		List<JobTransferEntry> jobTranferEntries = new ArrayList<>();
		Collection<JobTransferEntry> jobTransferListForExtensionSnapshot = new ArrayList<>();
		Collection<JobTransferEntry> jobTransferListForSnapshot = new ArrayList<>();

		JobTransferEntry jobTransferEntry1 = new JobTransferEntry(LocalDate.of(2011,11,22),LocalDate.of(2016,10,21),"All Organizational Set 1");
		jobTranferEntries.add(jobTransferEntry1);
		jobTransferListForExtensionSnapshot.add(jobTransferEntry1);
		
		JobTransferEntry jobTransferEntry2 = new JobTransferEntry(LocalDate.of(2010,10,11),LocalDate.of(2015,12,22),"All Organizational Set 2");
		jobTranferEntries.add(jobTransferEntry2);
		jobTransferListForExtensionSnapshot.add(jobTransferEntry2);
		
		JobTransferEntry jobTransferEntry3 = new JobTransferEntry(LocalDate.of(2013,10,26),LocalDate.of(2018,9,25),"All Organizational Set 3");
		jobTranferEntries.add(jobTransferEntry3);
		jobTransferListForSnapshot.add(jobTransferEntry3);
		
		JobTransferEntry jobTransferEntry4 = new JobTransferEntry(LocalDate.of(2013,10,26),LocalDate.of(2018,9,25),"All Organizational Set 3");
		jobTranferEntries.add(jobTransferEntry4);
		jobTransferListForSnapshot.add(jobTransferEntry4);
		
		effectiveDatedCollection.setEffectiveDatedEntries(jobTranferEntries);
		
		schedulingExtension.setJobTransfer(effectiveDatedCollection);
		
		assertEquals(jobTransferListForExtensionSnapshot,schedulingExtension.getJobTransfer(LocalDate.of(2011,11,22)));
		assertTrue(schedulingExtension.getJobTransfer(LocalDate.of(2009,12,30)).isEmpty());

		assertTrue(schedulingExtension.getJobTransfer(null).isEmpty());
		assertEquals(jobTransferListForSnapshot,schedulingExtension.getJobTransfer(LocalDate.of(2016,10,26)));
		assertTrue(schedulingExtension.getJobTransfer(LocalDate.of(2008,12,30)).isEmpty());

		schedulingExtension.setJobTransfer(null);
//		assertEquals(null,schedulingExtension.getJobTransfer());
//		assertEquals(null,schedulingExtension.getJobTransfer(LocalDate.of(2008,12,30)));
        assertNull(schedulingExtension.getJobTransfer());
        assertNull(schedulingExtension.getJobTransfer(LocalDate.of(2008, 12, 30)));


	}
	
	@Test
	public void testGetIdentifier(){
		ExtensionIdentifier extensionIdentifier = new ExtensionIdentifier("SCHEDULING_EXTENSION");
		assertEquals(extensionIdentifier,schedulingExtension.getIdentifier());
	}

	@Test
	public void getGroupScheduleId_whenIsUserOnMultiManagerRole_thenReturnMultiManagerRoleGroupScheduleId(){
		MultiManagerRoleProxyService multiManagerRoleProxyService = mock(MultiManagerRoleProxyService.class);
//		PowerMockito.mockStatic(SpringContext.class);
//		PowerMockito.when(SpringContext.getBean(MultiManagerRoleProxyService.class)).thenReturn(multiManagerRoleProxyService);
		mockedSpringContext.when(() -> SpringContext.getBean(MultiManagerRoleProxyService.class)).thenReturn(multiManagerRoleProxyService);
		when(multiManagerRoleProxyService.isUserOnMultiManagerRole(any())).thenReturn(true);
		AccessAssignmentProxyService accessAssignmentProxyService = mock(AccessAssignmentProxyService.class);
//		PowerMockito.when(SpringContext.getBean(AccessAssignmentProxyService.class)).thenReturn(accessAssignmentProxyService);
		mockedSpringContext.when(() -> SpringContext.getBean(AccessAssignmentProxyService.class)).thenReturn(accessAssignmentProxyService);
		when(accessAssignmentProxyService.getGroupScheduleId()).thenReturn(GROUP_SCHEDULE_ID);
		IKProperties ikProperties = mock(IKProperties.class);
//		PowerMockito.when(SpringContext.getBean(IKProperties.class)).thenReturn(ikProperties);
		mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(ikProperties);
		when(ikProperties.getPropertyAsBoolean(anyString(), anyBoolean())).thenReturn(true);
		ReleaseToggleService releaseToggleService = mock(ReleaseToggleService.class);
//		PowerMockito.when(SpringContext.getBean(ReleaseToggleService.class)).thenReturn(releaseToggleService);
		mockedSpringContext.when(() -> SpringContext.getBean(ReleaseToggleService.class)).thenReturn(releaseToggleService);
		when(releaseToggleService.getValue(anyString())).thenReturn(true);

		Long groupScheduleId = schedulingExtension.getGroupScheduleId();

		assertEquals(GROUP_SCHEDULE_ID, groupScheduleId.longValue());
	}
}
