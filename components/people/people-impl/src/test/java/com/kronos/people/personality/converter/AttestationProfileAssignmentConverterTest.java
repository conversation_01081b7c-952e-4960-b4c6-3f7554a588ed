package com.kronos.people.personality.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;

import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.kronos.businesscommon.timekeeping.proxy.api.IPersonalityProxy;
import com.kronos.commonapp.orgmap.common.api.ISystemProperties;
import com.kronos.people.personality.dataaccess.entity.AttestationProfileAssignment;
import com.kronos.timekeeping.service.attestation.api.converter.AttestationProfileConverter;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileAssignmentDTO;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileDTO;
import com.kronos.timekeeping.service.attestation.api.entity.AttestationProfile;

@ExtendWith(MockitoExtension.class)
public class AttestationProfileAssignmentConverterTest {

    private static final LocalDate END_OF_TIME = LocalDate.of(1900, 1, 1);
    private static final String DEFAULT_ATTESTATION_PROFILE_NAME = "Default profile name";
    private static final Long DEFAULT_ATTESTATION_PROFILE_ID = 999L;
    private static final Long CURRENT_USER_ID = 3L;
    private static final Long DEFAULT_ATTESTATION_PROFILE_ASSIGNMENT_ID = 4L;
    private static final LocalDateTime DEFAULT_EFFECTIVE_DATE = LocalDateTime.of(2018, 1, 4, 0, 0);
    private static final LocalDateTime DEFAULT_EXPIRATION_DATE = LocalDateTime.of(2147, 1, 1, 0, 0);
    private static final LocalDateTime DEFAULT_UPDATE_DATE = LocalDateTime.of(2018, 1, 1, 0, 0);
    private static final LocalDate EFFECTIVE_DATE = LocalDate.now();
    private static final LocalDate EXPIRATION_DATE = EFFECTIVE_DATE.plusDays(5);

    @Mock
    private IPersonalityProxy personalityProxy;
    @Mock
    private AttestationProfileConverter attestationProfileConverter;
    @Mock
    private ISystemProperties systemProperties;
    @Mock
    private AdapterHelper adapterHelper;

    private AttestationProfileAssignmentConverter attestationProfileAssignmentConverter;

    @BeforeEach
    public void setUp() {
        attestationProfileAssignmentConverter = new AttestationProfileAssignmentConverter(systemProperties, adapterHelper);
        attestationProfileAssignmentConverter.setAttestationProfileConverter(attestationProfileConverter);

//        when(systemProperties.getEndOfTime()).thenReturn(END_OF_TIME);
//        when(personalityProxy.getCurrentUserPersonId()).thenReturn(CURRENT_USER_ID);
//        when(attestationProfileConverter.convertDtoToEntity(any(AttestationProfileDTO.class), eq(null))).thenReturn(null);
        AttestationProfileDTO profileDTO = new AttestationProfileDTO();
        profileDTO.setName(DEFAULT_ATTESTATION_PROFILE_NAME);
//        when(attestationProfileConverter.convertEntityToDto(any(AttestationProfile.class))).thenReturn(profileDTO);
    }

    @Test
    public void shouldConvertDTOWithCachedProfileWhenEntityIsNotEmpty() {
        AttestationProfileAssignment entity = buildEntity();

        AttestationProfileDTO profileDTO = buildProfileDTO();

        AttestationProfileAssignmentDTO dto = attestationProfileAssignmentConverter.convertEntityToDto(entity,
            Collections.singletonMap(profileDTO.getId(), profileDTO));

        assertEquals(entity.getId(), dto.getId());
        assertEquals(entity.getProfile().getName(), dto.getAttestationProfile().getName());
        assertEquals(entity.getEffectiveDate(), dto.getEffectiveDate());
        assertEquals(entity.getExpirationDate(), dto.getExpirationDate());
        assertEquals(entity.getUpdatedOn(), dto.getUpdatedOn());
        assertEquals(entity.getUpdatedUserId(), dto.getUpdatedUserId());
        assertEquals(entity.getVersion(), dto.getVersionCount());
    }

    @Test
    public void shouldConvertAttestationProfileAssignmentDTOToAttestationProfileAssignment() throws Exception {
        com.kronos.people.personality.model.AttestationProfileAssignmentDTO attestationProfileAssignmentDTO = new com.kronos.people.personality.model.AttestationProfileAssignmentDTO();
        attestationProfileAssignmentDTO.setEffectiveDate(EFFECTIVE_DATE);
        attestationProfileAssignmentDTO.setExpirationDate(EXPIRATION_DATE);
        AttestationProfile profile = buildProfileEntity();
        when(systemProperties.getEndOfTime()).thenReturn(END_OF_TIME);
        final AttestationProfileAssignment result = attestationProfileAssignmentConverter
                .convertToAttestationProfileAssignment(attestationProfileAssignmentDTO, CURRENT_USER_ID, profile);

        assertEquals(profile, result.getProfile());
        assertEquals(EFFECTIVE_DATE.atStartOfDay(), result.getEffectiveDate());
        assertEquals(EXPIRATION_DATE.atStartOfDay(), result.getExpirationDate());
        assertEquals(CURRENT_USER_ID, result.getUpdatedUserId());
        assertNotNull(result.getUpdatedOn());
    }

    private AttestationProfileAssignmentDTO buildDTO() {
        AttestationProfileAssignmentDTO dto = new AttestationProfileAssignmentDTO();
        dto.setId(DEFAULT_ATTESTATION_PROFILE_ASSIGNMENT_ID);
        dto.setEffectiveDate(DEFAULT_EFFECTIVE_DATE);
        dto.setVersionCount(0);
        dto.setAttestationProfile(buildProfileDTO());

        return dto;
    }

    private AttestationProfileDTO buildProfileDTO() {
        AttestationProfileDTO dto = new AttestationProfileDTO();
        dto.setName(DEFAULT_ATTESTATION_PROFILE_NAME);
        dto.setId(DEFAULT_ATTESTATION_PROFILE_ID);
        return dto;
    }

    private AttestationProfileAssignment buildEntity() {
        AttestationProfile profileEntity = buildProfileEntity();

        AttestationProfileAssignment entity = new AttestationProfileAssignment();
        entity.setId(DEFAULT_ATTESTATION_PROFILE_ASSIGNMENT_ID);
        entity.setProfile(profileEntity);
        entity.setProfileId(profileEntity.getId());
        entity.setEffectiveDate(DEFAULT_EFFECTIVE_DATE);
        entity.setExpirationDate(DEFAULT_EXPIRATION_DATE);
        entity.setUpdatedOn(DEFAULT_UPDATE_DATE);
        entity.setUpdatedUserId(CURRENT_USER_ID);
        entity.setVersion(0);

        return entity;
    }

    private AttestationProfile buildProfileEntity() {
        AttestationProfile profileEntity = new AttestationProfile();
        profileEntity.setName(DEFAULT_ATTESTATION_PROFILE_NAME);
        profileEntity.setId(DEFAULT_ATTESTATION_PROFILE_ID);
        return profileEntity;
    }
}
