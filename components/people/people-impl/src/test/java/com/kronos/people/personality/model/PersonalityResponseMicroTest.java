package com.kronos.people.personality.model;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import com.kronos.people.personality.model.extension.AccrualExtension;
import org.junit.jupiter.api.Test;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class PersonalityResponseMicroTest {

	@Test
	public void testCons() {
		AccrualExtension accrualExtension = new AccrualExtension();
		accrualExtension.setPersonId(123L);
		PersonalityExtensionException pex = new PersonalityExtensionException(PersonalityErrorCode.UNKNOWN_ERROR, "Unknown Error");
		PersonalityResponse<AccrualExtension> personalityResonse = new PersonalityResponse<>(accrualExtension, pex);
		assertEquals("Unknown Error", personalityResonse.getException().getMessage());
		assertEquals(Long.valueOf(123L), personalityResonse.getExtension().getPersonId());
	}

	@Test
	public void testIsExpPresent() {

		PersonalityResponse<AccrualExtension> personalityResonse = new PersonalityResponse<>(null, null);
		personalityResonse.setException(new PersonalityExtensionException(PersonalityErrorCode.UNKNOWN_ERROR, "Error"));
		assertEquals("Error", personalityResonse.getException().getMessage());
//		assertEquals(true, personalityResonse.isExceptionPresent());
        assertTrue(personalityResonse.isExceptionPresent());
		personalityResonse.setException(null);
//		assertEquals(false, personalityResonse.isExceptionPresent());
        assertFalse(personalityResonse.isExceptionPresent());
	}

	@Test
	public void testExtension() {
		AccrualExtension accrualExtension = new AccrualExtension();
		accrualExtension.setPersonId(123L);
		PersonalityResponse<AccrualExtension> personalityResponse = new PersonalityResponse<>();
		personalityResponse.setextension(accrualExtension);
		assertEquals(Long.valueOf(123L), personalityResponse.getExtension().getPersonId());
	}

	@Test
	public void testDefaultCons() {
		PersonalityResponse<AccrualExtension> personalityResonse = new PersonalityResponse<>();
		assertNotNull(personalityResonse);
	}

}
