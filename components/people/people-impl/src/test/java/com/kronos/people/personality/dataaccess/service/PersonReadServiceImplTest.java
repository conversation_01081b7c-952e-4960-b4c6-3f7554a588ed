/*******************************************************************************
 * EmployeeBaseExtensionServiceTest.java
 * Copyright © 2024 UKG Inc. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.dataaccess.service;

import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.dataaccess.entity.EmployeeDTO;
import com.kronos.people.personality.dataaccess.entity.Person;
import com.kronos.people.personality.dataaccess.repository.IPersonAOIDReadRepository;
import com.kronos.people.personality.dataaccess.repository.IPersonReadRepository;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.model.LightPersonInformationSearchCriteria;
import com.kronos.persons.rest.model.PersonWhereCriteria;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * Class for testing read operations on PersonReadServiceImpl
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonReadServiceImplTest {

	@InjectMocks
	private PersonReadServiceImpl personReadServiceImpl;

	@Mock
	private IPersonReadRepository iPersonReadRepository;
	@Mock
	private IPersonAOIDReadRepository iPersonAOIDReadRepository;
	@Mock
	private EntityManager entityManager;



	@Test
	public void testfindLightPersonRecords_PersonIdOnly_EmploymentStatus_error() {
		try {
			LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
			PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
			Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
			Mockito.when(personWhereCriteria.getReturnPersonIdOnly()).thenReturn(true);
			Mockito.when(personWhereCriteria.getEmploymentStatus()).thenReturn("WrongAccTiv");
			personReadServiceImpl.findLightPersonRecords(1L, 1L, LocalDateTime.now(), LocalDateTime.now(), searchCriteria);
			fail();
		} catch (APIException e) {
			assertEquals(ExceptionConstants.INVALID_EMPLOYMENT_STATUS, e.getErrorCode());
		}
	}
	@Test
	public void testfindLightPersonRecords_PersonIdOnly_UserStatus_error() {
		try {
			LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
			PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
			Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
			Mockito.when(personWhereCriteria.getReturnPersonIdOnly()).thenReturn(true);
			Mockito.when(personWhereCriteria.getUserAccountStatus()).thenReturn("WrongAccTiv");
			personReadServiceImpl.findLightPersonRecords(1L, 1L, LocalDateTime.now(), LocalDateTime.now(), searchCriteria);
			fail();
		} catch (APIException e) {
			assertEquals(ExceptionConstants.INVALID_USERACCOUNT_STATUS, e.getErrorCode());
		}
	}
	
	@Test
	public void testfindLightPersonIds_UserEmployemntStatusesAreNullNull() {
		LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
		PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
		Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
		Mockito.when(searchCriteria.getWhere().getReturnPersonIdOnly()).thenReturn(true);
		List<Long> tasks = new ArrayList<>();
		tasks.add(1L);
		Page<Long> pagedTasks = new PageImpl<>(tasks);
		Mockito.when(personWhereCriteria.getUserAccountStatus()).thenReturn(null);
		Mockito.when(personWhereCriteria.getEmploymentStatus()).thenReturn(null);
		Mockito.when(iPersonReadRepository.findLightPersonIdsRecords(
				Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(pagedTasks);
		List<EmployeeDTO> res = personReadServiceImpl.findLightPersonRecords(1L, 1L, LocalDateTime.now(), LocalDateTime.now(), searchCriteria);
		assertEquals(1, res.size());
	}
	
	@Test
	public void testfindLightPersonIds_UserEmployemntStatusHaveValidValue() {
		LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
		PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
		Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
		Mockito.when(searchCriteria.getWhere().getReturnPersonIdOnly()).thenReturn(true);
		List<Long> tasks = new ArrayList<>();
		tasks.add(1L);
		Page<Long> pagedTasks = new PageImpl<>(tasks);
		Mockito.when(personWhereCriteria.getUserAccountStatus()).thenReturn("Active");
		Mockito.when(personWhereCriteria.getEmploymentStatus()).thenReturn("Active");
		Mockito.when(iPersonReadRepository.findLightPersonIdsRecords(Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(pagedTasks);
		List<EmployeeDTO> res = personReadServiceImpl.findLightPersonRecords(1L, 1L, LocalDateTime.now(), LocalDateTime.now(), searchCriteria);
		assertEquals(1, res.size());
	}
	@Test
	public void testfindLightPersonIds_findLightPersonIdsRecordsByUserAccountStatus() {
		LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
		PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
		Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
		Mockito.when(searchCriteria.getWhere().getReturnPersonIdOnly()).thenReturn(true);
		List<Long> tasks = new ArrayList<>();
		tasks.add(1L);
		Page<Long> pagedTasks = new PageImpl<>(tasks);
		Mockito.when(personWhereCriteria.getUserAccountStatus()).thenReturn("Active");
		Mockito.when(personWhereCriteria.getEmploymentStatus()).thenReturn(null);
		Mockito.when(iPersonReadRepository.findLightPersonIdsRecordsByUserAccountStatus(Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any())).thenReturn(pagedTasks);
		List<EmployeeDTO> res = personReadServiceImpl.findLightPersonRecords(1L, 1L, LocalDateTime.now(), LocalDateTime.now(), searchCriteria);
		assertEquals(1, res.size());
	}
	
	@Test
	public void testfindLightPersonIds_findLightPersonIdsRecordsByEmploymentStatusId() {
		LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
		PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
		Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
		Mockito.when(searchCriteria.getWhere().getReturnPersonIdOnly()).thenReturn(true);
		List<Long> tasks = new ArrayList<>();
		tasks.add(1L);
		Page<Long> pagedTasks = new PageImpl<>(tasks);
		Mockito.when(personWhereCriteria.getUserAccountStatus()).thenReturn(null);
		Mockito.when(personWhereCriteria.getEmploymentStatus()).thenReturn("Active");
		Mockito.when(iPersonReadRepository.findLightPersonIdsRecordsByEmploymentStatusId(Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any())).thenReturn(pagedTasks);
		List<EmployeeDTO> res = personReadServiceImpl.findLightPersonRecords(1L, 1L, LocalDateTime.now(), LocalDateTime.now(), searchCriteria);
		assertEquals(1, res.size());
	}
	
	@Test
	public void testfindLightPersonRecords_UserEmployemntStatusHaveValidValue() {
		LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
		PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
		Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
		Mockito.when(searchCriteria.getWhere().getReturnPersonIdOnly()).thenReturn(false);
		Page<Object[]> pagedTasks = createTestData();
		Mockito.when(personWhereCriteria.getUserAccountStatus()).thenReturn("Active");
		Mockito.when(personWhereCriteria.getEmploymentStatus()).thenReturn("Active");
		Mockito.when(iPersonReadRepository.findLightPersonRecords(Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(pagedTasks);
		List<EmployeeDTO> res = personReadServiceImpl.findLightPersonRecords(1L, 1L, LocalDateTime.now(), LocalDateTime.now(), searchCriteria);
		assertEquals(1, res.size());
	}
	private Page<Object[]> createTestData() {
		List<Object[]> tasks = new ArrayList<>();
		Object[] task=new Object[6];
		task[0]=new BigInteger("1");
		task[1]="1l";
		task[2]="test";
		task[3]="king";
		task[4]=new BigInteger("1");
		task[5]=new BigInteger("1");
		tasks.add(task);
        return new PageImpl<>(tasks);
	}
	@Test
	public void testfindLightPersonRecords_findLightPersonIdsRecordsByUserAccountStatus() {
		LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
		PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
		Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
		Mockito.when(searchCriteria.getWhere().getReturnPersonIdOnly()).thenReturn(false);
		Page<Object[]> pagedTasks = createTestData();
		Mockito.when(personWhereCriteria.getUserAccountStatus()).thenReturn("Active");
		Mockito.when(personWhereCriteria.getEmploymentStatus()).thenReturn(null);
		Mockito.when(iPersonReadRepository.findLightPersonRecordsRecordsByUserAccountStatus(Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any())).thenReturn(pagedTasks);
		List<EmployeeDTO> res = personReadServiceImpl.findLightPersonRecords(1L, 1L, LocalDateTime.now(), LocalDateTime.now(), searchCriteria);
		assertEquals(1, res.size());
	}
	
	@Test
	public void testfindLightPersonRecords_findLightPersonIdsRecordsByEmploymentStatusId() {
		LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
		PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
		Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
		Mockito.when(searchCriteria.getWhere().getReturnPersonIdOnly()).thenReturn(false);
		Page<Object[]> pagedTasks = createTestData();
		Mockito.when(personWhereCriteria.getUserAccountStatus()).thenReturn(null);
		Mockito.when(personWhereCriteria.getEmploymentStatus()).thenReturn("Active");
		Mockito.when(iPersonReadRepository.findLightPersonRecordsByEmploymentStatusId(Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any())).thenReturn(pagedTasks);
		List<EmployeeDTO> res = personReadServiceImpl.findLightPersonRecords(1L, 1L, LocalDateTime.now(), LocalDateTime.now(), searchCriteria);
		assertEquals(1, res.size());
	}
	@Test
	public void testfindLightPersonRecords_userEmplStatuesAreNull() {
		LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
		PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
		Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
		Mockito.when(searchCriteria.getWhere().getReturnPersonIdOnly()).thenReturn(false);
		Page<Object[]> pagedTasks = createTestData();
		Mockito.when(personWhereCriteria.getUserAccountStatus()).thenReturn(null);
		Mockito.when(personWhereCriteria.getEmploymentStatus()).thenReturn(null);
		Mockito.when(iPersonReadRepository.findLightPersonRecords(Mockito.any(),
				Mockito.any(), Mockito.any())).thenReturn(pagedTasks);
		List<EmployeeDTO> res = personReadServiceImpl.findLightPersonRecords(1L, 1L, LocalDateTime.now(), LocalDateTime.now(), searchCriteria);
		assertEquals(1, res.size());
	}

	@Disabled // Cannot invoke "jakarta.persistence.EntityManager.createNativeQuery(String)" because "this.em" is null
	@Test
	public void testFindWithoutAoId_emptyEmployees() {
		Query mockedQuery = Mockito.mock(Query.class);
		Mockito.when(mockedQuery.getResultList()).thenReturn(Collections.emptyList());
		Mockito.when(entityManager.createNativeQuery(Mockito.anyString())).thenReturn(mockedQuery);
		List<Person> tasks = Collections.singletonList(new Person());
		Page<Person> pagedTasks = new PageImpl<>(tasks);
		Mockito.when(iPersonAOIDReadRepository.findByEmployeeWithoutAoid(Mockito.any(), Mockito.any(),
				Mockito.any())).thenReturn(pagedTasks);
		List<EmployeeDTO> res = personReadServiceImpl.findWithoutaoid(1L, 1L, LocalDateTime.now(), LocalDateTime.now());
		assertTrue(res.isEmpty());
	}

	@Test
	public void testFindWithoutAoId_emptyPage() {
		Page<Person> pagedTasks = new PageImpl<>(Collections.emptyList());
		Mockito.when(iPersonAOIDReadRepository.findByEmployeeWithoutAoid(Mockito.any(), Mockito.any(),
				Mockito.any())).thenReturn(pagedTasks);

		LocalDateTime today =  LocalDateTime.now();
		APIException apiException = assertThrows(
				APIException.class,
				() -> personReadServiceImpl.findWithoutaoid(1L, 1L, today, today)
		);

		assertNotNull(apiException.getErrorCode());
		assertEquals("NO_DATA_NOT_FOUND_FOR_PAYLOAD", apiException.getErrorCode());
	}

	@Disabled // Cannot invoke "jakarta.persistence.EntityManager.createNativeQuery(String)" because "this.em" is null
	@Test
	public void testFindWithoutAoId_notEmptyEmployees() {
		Query mockedQuery = Mockito.mock(Query.class);
		Mockito.when(mockedQuery.getResultList()).thenReturn(Collections.singletonList(getPersonDataAsObject()));
		Mockito.when(entityManager.createNativeQuery(Mockito.anyString())).thenReturn(mockedQuery);
		List<Person> tasks = new ArrayList<>();
		tasks.add(new Person());
		Page<Person> pagedTasks = new PageImpl<>(tasks);
		Mockito.when(iPersonAOIDReadRepository.findByEmployeeWithoutAoid(Mockito.any(), Mockito.any(),
				Mockito.any())).thenReturn(pagedTasks);
		List<EmployeeDTO> res = personReadServiceImpl.findWithoutaoid(1L, 1L, LocalDateTime.now(), LocalDateTime.now());
		assertEquals(1, res.size());
	}


	@Test
	public void testFindWithoutAoId_notEmptyEmployees_ApiException() {
		Query mockedQuery = Mockito.mock(Query.class);
		Mockito.when(mockedQuery.getResultList()).thenReturn(Collections.singletonList(getPersonDataAsObject()));
		Mockito.when(entityManager.createNativeQuery(Mockito.anyString())).thenReturn(mockedQuery);
		Mockito.when(iPersonAOIDReadRepository.findByEmployeeWithoutAoid(Mockito.any(), Mockito.any(),
				Mockito.any())).thenThrow(new APIException("errorCode"));

		LocalDateTime today =  LocalDateTime.now();
		APIException apiException = assertThrows(
				APIException.class,
				() -> personReadServiceImpl.findWithoutaoid(1L, 1L, today, today)
		);

		assertNotNull(apiException.getErrorCode());
		assertEquals("WCO-101336", apiException.getErrorCode());
	}


	private Object[] getPersonDataAsObject(){
		Object[] personObject=new Object[10];
		personObject[0]= 1L;
		personObject[1]="1L";
		personObject[2]="firstName";
		personObject[3]="lastName";
		personObject[4]="12-12-2002";
		personObject[5]="12-12-2023";
		personObject[6]="1234567890";
		personObject[7]="mobile";
		personObject[8]="<EMAIL>";
		personObject[9]="work";
		return personObject;
	}
}
