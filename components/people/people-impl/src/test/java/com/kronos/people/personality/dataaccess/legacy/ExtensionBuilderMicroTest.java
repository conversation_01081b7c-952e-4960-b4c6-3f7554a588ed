package com.kronos.people.personality.dataaccess.legacy;

import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.people.personality.dataaccess.adapter.*;
import com.kronos.people.personality.model.extension.*;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.notification.ExtensionCacheOperations;
import com.kronos.people.personality.notification.ExtensionCacheUpdaterImpl;
import com.kronos.people.personality.properties.KronosPropertiesFacade;
import com.kronos.people.personality.properties.TracePropertyHelper;
import com.kronos.people.personality.tenant.TenantHandlingFacade;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.system.business.info.ServerInfoService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class ExtensionBuilderMicroTest {
	@InjectMocks
	private ExtensionBuilder extensionBuilder;
	@Mock
	private PersonalityFacade personalityFacadeMock;
	@Mock
	private AccrualExtensionAdapter accrualExtensionAdapterMock;
	@Mock
	private DevicesExtensionAdapter devicesExtensionAdapterMock;
	@Mock
	private EmployeeExtensionAdapter employeeExtensionAdapterMock;
	@Mock
	private SchedulingExtensionAdapter schedulingExtensionAdapterMock;
	@Mock
	private TimeKeepingAdapter timeKeepingExtensionAdapterMock;
	@Mock
	private AdapterHelper adapterHelperMock;
	@Mock
	private KronosThreadPoolService kronosThreadPoolService;
	@Mock
	private TracePropertyHelper tracePropertyHelper;

	@Mock
	TenantHandlingFacade tenantHandlingFacade;

	@Mock
	KronosThreadPoolService threadPoolService;

	@Mock
	ExecutorService executor;

	@Mock
	KronosPropertiesFacade kronosPropertiesFacade;

	@Mock
	ConcurrencyHelper concurrencyHelper;

	private MockedStatic<ServerInfoService> mockedServerInfoService;

	@BeforeEach
	public void setup() {
		mockedServerInfoService = Mockito.mockStatic(ServerInfoService.class);
		ServerInfoService info = Mockito.mock(ServerInfoService.class);
		mockedServerInfoService.when(ServerInfoService::getInstance).thenReturn(info);
		Mockito.when(info.getDatadogServiceName("")).thenReturn("");
	}

	@AfterEach
	public void tearDown() {
		extensionBuilder = null;
		personalityFacadeMock = null;
		adapterHelperMock = null;
		accrualExtensionAdapterMock = null;
		devicesExtensionAdapterMock = null;
		employeeExtensionAdapterMock = null;
		schedulingExtensionAdapterMock = null;
		timeKeepingExtensionAdapterMock = null;
		mockedServerInfoService.close();
	}

	@Test
	public void testCreateExtensions() {
		Map<ExtensionAdapterEnum, ExtensionAdapter> map = new HashMap<>();

		map.put(ExtensionAdapterEnum.ACCRUAL, accrualExtensionAdapterMock);
		map.put(ExtensionAdapterEnum.DEVICES, devicesExtensionAdapterMock);
		map.put(ExtensionAdapterEnum.EMPLOYEE, employeeExtensionAdapterMock);
		map.put(ExtensionAdapterEnum.SCHEDULING, schedulingExtensionAdapterMock);
		map.put(ExtensionAdapterEnum.TIMEKEEPING, timeKeepingExtensionAdapterMock);
		extensionBuilder.setAccrualExtensionAdapter(accrualExtensionAdapterMock);
		extensionBuilder.setDevicesExtensionAdapter(devicesExtensionAdapterMock);
		extensionBuilder.setEmployeeExtensionAdapter(employeeExtensionAdapterMock);
		extensionBuilder.setSchedulingExtensionAdapter(schedulingExtensionAdapterMock);
		extensionBuilder.setTimeKeepingExtensionAdapter(timeKeepingExtensionAdapterMock);
		extensionBuilder.setTracePropertyHelper(tracePropertyHelper);
		//extensionBuilder.init();

		Long personId = Long.valueOf(1);
		ExtensionAdapterEnum extnToReturn = ExtensionAdapterEnum.ACCRUAL;
		Boolean loadAll = true;

		Personality personality = mock(Personality.class);

		when(personality.getPersonId()).thenReturn(new ObjectIdLong(1));
		when(personality.getPersonNumber()).thenReturn("223");
		when(personality.isActive()).thenReturn(true);
		when(adapterHelperMock.getLongFromObjectIdLong(new ObjectIdLong(1))).thenReturn(1L);

		AccrualExtension accrualExtensionMock = mock(AccrualExtension.class);

		when(personalityFacadeMock.findPersonality(personId)).thenReturn(personality);
		when(accrualExtensionAdapterMock.convert(personality,null)).thenReturn(accrualExtensionMock);

		extensionBuilder.cacheUpdater = mock(ExtensionCacheUpdaterImpl.class);

		BaseExtension extn = extensionBuilder.createExtensions(personId, extnToReturn,null);

		assertEquals(accrualExtensionMock, extn);
	}

	@Test
	public void testbuildAllExtensionAndPushToRedis() {
		buildExtensionMockBundle();

		ExtensionCacheOperations cacheOperationsMock = mock(ExtensionCacheOperations.class);
		extensionBuilder.cacheOperations = cacheOperationsMock;
		AllExtension allExtension = extensionBuilder.buildAllExtensionAndPushToRedis(1L);
		verify(extensionBuilder.cacheOperations, times(1)).putInRedisChannel(Mockito.anyMap());
		assertNotNull(allExtension);
		assertEquals(true, allExtension.isActive());
	}

	@Test
	public void testbuildExtensions() {
		Personality personalityMock = mock(Personality.class);
		KronosPropertiesFacade kronosPropertiesFacadeMock = mock(KronosPropertiesFacade.class);
		when(kronosPropertiesFacadeMock.getIntegerKronosProperty(anyString(), anyInt())).thenReturn(1);
		extensionBuilder.kronosPropertiesFacade=kronosPropertiesFacadeMock;
		when(personalityFacadeMock.findPersonality(1L)).thenReturn(personalityMock);
		when(kronosPropertiesFacade.getIntegerKronosProperty(any(), anyInt())).thenReturn(100);
		Map<String, BaseExtension> extensionsMap = extensionBuilder.buildExtensions(1L);
		assertNotNull(extensionsMap);
	}

	@Test
	public void testUpdateCommonAttributes(){
		BaseExtension be = null;
		extensionBuilder.updateCommonAttributes(new Personality(), be);
		assertNull(be);
	}

	@Test
	public void testUpdateCommonAttributes_whenBaseExtensionIsNotNull(){
		BaseExtension be = mock(BaseExtension.class);
		Personality personalityMock = mock(Personality.class);
		when(adapterHelperMock.getLongFromObjectIdLong(new ObjectIdLong(1))).thenReturn(1L);
		when(personalityMock.getPersonNumber()).thenReturn("test");
		when(personalityMock.isPersonActive()).thenReturn(true);
		extensionBuilder.updateCommonAttributes(personalityMock, be);
		doCallRealMethod().when(be).setActive(any());
		verify(be,times(1)).setActive(any());
	}


	@Test
	public void testGetCurrentUserId(){
		when(personalityFacadeMock.getCurrentUserId()).thenReturn(2l);
		Long personId = extensionBuilder.getCurrentUserId();
		assertNotNull(personId);
	}

	/**
	 *
	 */
	public void buildExtensionMockBundle() {
		extensionBuilder = new ExtensionBuilder(null, null, null, null, null, null, null, null, null, null, null, kronosThreadPoolService, null, null) {
			@Override
			public Map<String, BaseExtension> buildExtensions(Long personId) {
				Map<String, BaseExtension> map = new ConcurrentHashMap<>();
				BaseExtension baseExtensionAccrualMock = mock(AccrualExtension.class);
				when(baseExtensionAccrualMock.getPersonId()).thenReturn(Long.valueOf(1));
				when(baseExtensionAccrualMock.getPersonNumber()).thenReturn("223");
				when(baseExtensionAccrualMock.isActive()).thenReturn(true);

				BaseExtension baseExtensionDevicesMock = mock(DevicesExtension.class);
				when(baseExtensionDevicesMock.getPersonId()).thenReturn(Long.valueOf(1));
				when(baseExtensionDevicesMock.getPersonNumber()).thenReturn("223");
				when(baseExtensionDevicesMock.isActive()).thenReturn(true);

				BaseExtension baseExtensionEmployeeMock = mock(EmployeeExtension.class);
				when(baseExtensionEmployeeMock.getPersonId()).thenReturn(Long.valueOf(1));
				when(baseExtensionEmployeeMock.getPersonNumber()).thenReturn("223");
				when(baseExtensionEmployeeMock.isActive()).thenReturn(true);

				BaseExtension baseExtensionSchedulingMock = mock(SchedulingExtension.class);
				when(baseExtensionSchedulingMock.getPersonId()).thenReturn(Long.valueOf(1));
				when(baseExtensionSchedulingMock.getPersonNumber()).thenReturn("223");
				when(baseExtensionSchedulingMock.isActive()).thenReturn(true);

				BaseExtension baseExtensionTimekeepingMock = mock(TimekeepingExtension.class);
				when(baseExtensionTimekeepingMock.getPersonId()).thenReturn(Long.valueOf(1));
				when(baseExtensionTimekeepingMock.getPersonNumber()).thenReturn("223");
				when(baseExtensionTimekeepingMock.isActive()).thenReturn(true);

				map.put(ExtensionAdapterEnum.ACCRUAL.getIdentifier(), baseExtensionAccrualMock);
				map.put(ExtensionAdapterEnum.DEVICES.getIdentifier(), baseExtensionDevicesMock);
				map.put(ExtensionAdapterEnum.EMPLOYEE.getIdentifier(), baseExtensionEmployeeMock);
				map.put(ExtensionAdapterEnum.SCHEDULING.getIdentifier(), baseExtensionSchedulingMock);
				map.put(ExtensionAdapterEnum.TIMEKEEPING.getIdentifier(), baseExtensionTimekeepingMock);
				return map;
			}
		};
	}

	/**
	 *
	 */
	public void buildExtensionMockBundle_2() {
		extensionBuilder = new ExtensionBuilder(null, null, null, null, null, null, null, null, null, null, null, null, null, null) {
			@Override
			public Map<String, BaseExtension> buildExtensions(Long personId) {
				Map<String, BaseExtension> map = new ConcurrentHashMap<>();
				BaseExtension baseExtensionAccrualMock = mock(AccrualExtension.class);
				when(baseExtensionAccrualMock.getPersonId()).thenReturn(Long.valueOf(1));
				when(baseExtensionAccrualMock.getPersonNumber()).thenReturn("223");
				when(baseExtensionAccrualMock.isActive()).thenReturn(false);

				BaseExtension baseExtensionDevicesMock = mock(DevicesExtension.class);
				when(baseExtensionDevicesMock.getPersonId()).thenReturn(Long.valueOf(1));
				when(baseExtensionDevicesMock.getPersonNumber()).thenReturn("223");
				when(baseExtensionDevicesMock.isActive()).thenReturn(false);

				BaseExtension baseExtensionEmployeeMock = mock(EmployeeExtension.class);
				when(baseExtensionEmployeeMock.getPersonId()).thenReturn(Long.valueOf(1));
				when(baseExtensionEmployeeMock.getPersonNumber()).thenReturn("223");
				when(baseExtensionEmployeeMock.isActive()).thenReturn(false);

				BaseExtension baseExtensionSchedulingMock = mock(SchedulingExtension.class);
				when(baseExtensionSchedulingMock.getPersonId()).thenReturn(Long.valueOf(1));
				when(baseExtensionSchedulingMock.getPersonNumber()).thenReturn("223");
				when(baseExtensionSchedulingMock.isActive()).thenReturn(false);

				BaseExtension baseExtensionTimekeepingMock = mock(TimekeepingExtension.class);
				when(baseExtensionTimekeepingMock.getPersonId()).thenReturn(Long.valueOf(1));
				when(baseExtensionTimekeepingMock.getPersonNumber()).thenReturn("223");
				when(baseExtensionTimekeepingMock.isActive()).thenReturn(false);

				map.put(ExtensionAdapterEnum.ACCRUAL.getIdentifier(), baseExtensionAccrualMock);
				map.put(ExtensionAdapterEnum.DEVICES.getIdentifier(), baseExtensionDevicesMock);
				map.put(ExtensionAdapterEnum.EMPLOYEE.getIdentifier(), baseExtensionEmployeeMock);
				map.put(ExtensionAdapterEnum.SCHEDULING.getIdentifier(), baseExtensionSchedulingMock);
				map.put(ExtensionAdapterEnum.TIMEKEEPING.getIdentifier(), baseExtensionTimekeepingMock);
				return map;
			}
		};
	}

	@Test
	public void testCleanUp() {
		Map<ExtensionAdapterEnum, ExtensionAdapter> adapterMap = mock(HashMap.class);
		extensionBuilder.setAdapterMap(adapterMap);
		extensionBuilder.cleanUp();
		System.out.println("AAA");
		verify(adapterMap, times(1)).clear();
	}

	@Test
	public void testCreateExtensionSnapshot() {
		extensionBuilder.setAccrualExtensionAdapter(accrualExtensionAdapterMock);
		//extensionBuilder.init();
		AccrualExtension expectedExtension = new AccrualExtension();
		expectedExtension.setAccuralProfiles(new EffectiveDatedCollection<>());
		expectedExtension.setFullTimeEquivalency(new EffectiveDatedCollection<>());
		when(accrualExtensionAdapterMock.createSnapshot(expectedExtension)).thenReturn(expectedExtension);
		AccrualExtension actialExtension = extensionBuilder.createExtensionSnapshot(expectedExtension, ExtensionAdapterEnum.ACCRUAL);
		assertEquals(expectedExtension, actialExtension);
		verify(accrualExtensionAdapterMock).createSnapshot(expectedExtension);
	}

	@Test
	public void testCreateExtensionSnapshotWithException() {
		assertThrows(RuntimeException.class, () -> {
			extensionBuilder.setAccrualExtensionAdapter(accrualExtensionAdapterMock);
			//extensionBuilder.init();
			AccrualExtension expectedExtension = new AccrualExtension();
			expectedExtension.setAccuralProfiles(new EffectiveDatedCollection<>());
			expectedExtension.setFullTimeEquivalency(new EffectiveDatedCollection<>());
			when(accrualExtensionAdapterMock.createSnapshot(expectedExtension)).thenThrow(new RuntimeException());
			extensionBuilder.createExtensionSnapshot(expectedExtension, ExtensionAdapterEnum.ACCRUAL);
		});
	}
}
