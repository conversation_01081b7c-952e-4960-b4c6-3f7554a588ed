
package com.kronos.people.personality.properties;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.math.NumberUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.kronos.commonapp.kronosproperties.api.IKProperties;

//@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class KronosPropertiesFacadeMicroTest {
	
	KronosPropertiesFacade kronosPropertiesFacade;
	@Mock
	IKProperties iKPropertiesMock;
	
	@BeforeEach
	public void setup(){
		kronosPropertiesFacade = new KronosPropertiesFacade();
	}
	
	@AfterEach
	public void tearDown(){
		kronosPropertiesFacade = null;
	}
	
	@Test
	public void testGetKronosTenantProperty(){
		when(iKPropertiesMock.getProperty("primingProperty", "true")).thenReturn("primeVal");
		kronosPropertiesFacade.setKronosProperties(iKPropertiesMock);
		String result = kronosPropertiesFacade.getKronosTenantProperty("primingProperty");
		assertEquals("primeVal", result);
	}
	
	@Test
	public void testSetKronosTenantProperty(){
		kronosPropertiesFacade.setKronosProperties(iKPropertiesMock);
		kronosPropertiesFacade.setKronosTenantProperty("key", "value");
		verify(iKPropertiesMock, times(1)).setProperty("key", "value");
	}
	
		
	@Test
	public void testRemoveKronosTenantProperty(){
		kronosPropertiesFacade.setKronosProperties(iKPropertiesMock);
		kronosPropertiesFacade.removeKronosTenantProperty("primingProperty");
		verify(iKPropertiesMock, times(1)).removeProperty("primingProperty");
	}
	
	@Test
	public void testRemoveKronosProperty(){
		kronosPropertiesFacade.setKronosProperties(iKPropertiesMock);
		kronosPropertiesFacade.removeKronosProperty("primingProperty");
		verify(iKPropertiesMock, times(1)).removeProperty("primingProperty");
	}
	
	@Test
	public void testGetStringKronosProperty(){
		kronosPropertiesFacade.setKronosProperties(iKPropertiesMock);
		kronosPropertiesFacade.getStringKronosProperty("primingProperty");
		verify(iKPropertiesMock, times(1)).getProperty("primingProperty");
	}
	
	@Test
	public void testGetIntegerKronosProperty(){
		when(iKPropertiesMock.get("primingProperty")).thenReturn("primeVal");
		kronosPropertiesFacade.setKronosProperties(iKPropertiesMock);
		Integer result = kronosPropertiesFacade.getIntegerKronosProperty("primingProperty", 1);
		assertEquals(Integer.valueOf(1), result);
	}
	
	@Test
	public void testgetStringKronosProperty(){
		when(iKPropertiesMock.getProperty("primingProperty", "primingProperty")).thenReturn("outputVal");
		kronosPropertiesFacade.setKronosProperties(iKPropertiesMock);
		String result = kronosPropertiesFacade.getStringKronosProperty("primingProperty", "primingProperty");
		assertEquals("outputVal", result);
		System.out.println(result);
	}
	
	@Test
	public void testGetBooleanKronosProperty(){
		when(iKPropertiesMock.getPropertyAsBoolean("primingKey")).thenReturn(true);
		kronosPropertiesFacade.setKronosProperties(iKPropertiesMock);
		Boolean result = kronosPropertiesFacade.getBooleanKronosProperty("primingKey");
		assertTrue(result);
	}
	
	@Test
	public void conversionTest(){
		Map<String, Object> tmsPropertyDataMap = new HashMap<>();
		tmsPropertyDataMap.put("isTenantPrimingEnabled", "true");
		tmsPropertyDataMap.put("tenantID", "3.1");
		tmsPropertyDataMap.put("1.0", "1.0");
		tmsPropertyDataMap.put("0", "0");
		tmsPropertyDataMap.put("-2345", "-2345");
		tmsPropertyDataMap.put("34567898", "34567898");
		
		tmsPropertyDataMap.forEach((k,v)->System.out.println(k+">>>>" + NumberUtils.isNumber(v.toString())));
		//System.out.println(Long.valueOf("1.0"));
		//System.out.println("Chal be ullu");
		//NumberUtils.toLong(str);
	}

}
