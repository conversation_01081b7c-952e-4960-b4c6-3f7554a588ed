package com.kronos.people.personality.dataaccess.adapter;

import static java.util.Collections.EMPTY_LIST;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import com.kronos.datacollection.udm.service.bioconsentdetails.api.enums.BiometricConsentType;
import com.kronos.datacollection.udm.service.facescan.api.dto.FaceScan;
import com.kronos.datacollection.udm.service.facescan.api.dto.FaceScanDTO;
import com.kronos.datacollection.udm.service.ttipuserprofile.api.dto.TTIPEmployeeAttributesDTO;
import com.kronos.datacollection.udm.service.ttipuserprofile.api.dto.TTIPUserProfileDTO;
import com.kronos.datacollection.udm.service.fingerscan.api.dto.FingerScanDTO;
import com.kronos.datacollection.udm.service.bioconsentdetails.api.dto.BiometricConsentDetails;
import com.kronos.datacollection.udm.service.bioconsentdetails.api.dto.BiometricConsentDetailsDTO;
import com.kronos.datacollection.udm.service.bioconsentdetails.api.IBioConsentDetailsService;
import com.kronos.datacollection.udm.service.bioconsentdetails.api.enums.BiometricType;
import com.kronos.datacollection.udm.service.facescan.api.IFaceScanAttributesService;
import com.kronos.people.personality.model.extension.entry.BiometricConsentDetailsEntry;
import com.kronos.people.personality.model.extension.entry.FaceScanEntry;

import com.kronos.datacollection.udm.service.devicegroup.api.IDeviceGroupService;
import com.kronos.datacollection.udm.service.individualprofile.api.IIndividualProfileService;
import com.kronos.datacollection.udm.service.ttipuserprofile.api.ITTIPUserProfileService;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.exception.impl.ExceptionHelper;
import com.kronos.people.personality.model.extension.DevicesExtension;
import com.kronos.people.personality.model.extension.entry.BadgeDetailsEntry;
import com.kronos.wfc.commonapp.people.business.person.BadgeAssignment;
import com.kronos.wfc.commonapp.people.business.person.BadgeAssignmentSet;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.platform.utility.framework.datetime.KDateTime;
import com.kronos.wfc.platform.utility.framework.datetime.KTime;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DevicesExtensionAdapterMicroTest {

    private DevicesExtension devicesExtension;
    private DevicesExtensionAdapter devicesExtensionAdapter;
    private Personality personality;
    private Person person;
    private PersonalityFacade personalityFacadeSpy;
    private AdapterHelper adapterHelper;

    @BeforeEach
    public void setUp() throws Exception {
        devicesExtension = spy(DevicesExtension.class);
        devicesExtensionAdapter = spy(DevicesExtensionAdapter.class);
        devicesExtensionAdapter.setAdapterHelper(new AdapterHelper());
        person = mock(Person.class);
        personality = mock(Personality.class);
        personalityFacadeSpy = spy(new PersonalityFacade(null,null,null,null,null,null,null, null));
        personalityFacadeSpy.setAdapterHelper(new AdapterHelper());
        personalityFacadeSpy.setExceptionHelper(mock(ExceptionHelper.class));
        personalityFacadeSpy.setDeviceGroupService(mock(IDeviceGroupService.class));
        personalityFacadeSpy.setDeviceProfileService(mock(IIndividualProfileService.class));
        personalityFacadeSpy.setTtipUserProfileService(mock(ITTIPUserProfileService.class));
        personalityFacadeSpy.setFaceScanAttributesService(mock(IFaceScanAttributesService.class));
        personalityFacadeSpy.setBioConsentDetailsService(mock(IBioConsentDetailsService.class));
//        doReturn(null).when(personalityFacadeSpy).getJobAssignment(any());
        devicesExtensionAdapter.setPersonalityFacade(personalityFacadeSpy);
        adapterHelper = new AdapterHelper();
    }

    @AfterEach
    public void teardown() {
        devicesExtension = null;
        devicesExtensionAdapter = null;
        person = null;
        personality = null;
        personalityFacadeSpy = null;
    }

    @Test
    public void testConvertWithEmpty() {
        devicesExtensionAdapter.convert(personality,null);
        verify(devicesExtension, times(0)).setBadgeDetails(any());
    }
     
    @Test
    public void testConvertDeviceGroupId() {
        when(personalityFacadeSpy.getDeviceGroupIdByPersonId(any())).thenReturn(3L);
        DevicesExtension devicesExtension = devicesExtensionAdapter.convert(personality,null);
        assertEquals(3L, devicesExtension.getDeviceGroupId().longValue());
        
        when(personalityFacadeSpy.getDeviceGroupIdByPersonId(any())).thenReturn(null);
        devicesExtension = devicesExtensionAdapter.convert(personality,null);
        assertNull(devicesExtension.getDeviceGroupId());
    }
    
    @Test
    public void testConvertProfileId() {
        when(personalityFacadeSpy.getDeviceProfileIdByPersonId(any())).thenReturn(5L);
        DevicesExtension devicesExtension = devicesExtensionAdapter.convert(personality,null);
        assertEquals(5L, devicesExtension.getProfileId().longValue());
        
        when(personalityFacadeSpy.getDeviceProfileIdByPersonId(any())).thenReturn(null);
        devicesExtension = devicesExtensionAdapter.convert(personality,null);
        assertNull(devicesExtension.getProfileId());
    }
    
    @Test
    public void testConvertFingerScanAttributes() {
        FingerScanDTO fingerScanDTO = new FingerScanDTO();
        fingerScanDTO.setEnrolled(true);
        fingerScanDTO.setEnrolledForIdentification(true);
        fingerScanDTO.setPrimaryFingerThreshold("High");
        fingerScanDTO.setPrimaryFingerEnrollmentLocation("My Device (025096)");
        when(personalityFacadeSpy.getFingerScanByPersonId(any())).thenReturn(fingerScanDTO);

        
        DevicesExtension devicesExtension = devicesExtensionAdapter.convert(personality,null);
        assertTrue(devicesExtension.isFingerEnrolled());
        assertTrue(devicesExtension.isFingerEnrolledForIdentification());
        assertEquals("High", devicesExtension.getPrimaryFingerThreshold());
        assertEquals("My Device (025096)", devicesExtension.getPrimaryFingerEnrollmentLocation());


        fingerScanDTO.setEnrolled(false);
        fingerScanDTO.setEnrolledForIdentification(false);
        fingerScanDTO.setPrimaryFingerThreshold("Low");
        fingerScanDTO.setPrimaryFingerEnrollmentLocation("Your Device (000096)");
        when(personalityFacadeSpy.getFingerScanByPersonId(any())).thenReturn(fingerScanDTO);
        
        devicesExtension = devicesExtensionAdapter.convert(personality,null);
        assertFalse(devicesExtension.isFingerEnrolled());
        assertFalse(devicesExtension.isFingerEnrolledForIdentification());
        assertEquals("Low", devicesExtension.getPrimaryFingerThreshold());
        assertEquals("Your Device (000096)", devicesExtension.getPrimaryFingerEnrollmentLocation());
    }

    private FaceScan getFaceScanForFeatureTest(LocalDateTime consentDateTime) {
        FaceScanDTO faceScan = new FaceScanDTO();
        faceScan.setThreshold(2);
        faceScan.setQualityScore(1);
        faceScan.setEnrollmentLocation("N/A");
        faceScan.setEnrollmentDateTime(consentDateTime);
        faceScan.setIsEnrolled(true);
        faceScan.setDeleted(true);
        return faceScan;
    }

    @Test
    public void testConvertFaceScanAttributes() {
        LocalDateTime dateTime = LocalDateTime.parse("2020-07-23T13:53:02");

        when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        when(personality.getPersonNumber()).thenReturn("10000");

        FaceScan faceScan = getFaceScanForFeatureTest(dateTime);
        when(personalityFacadeSpy.getFaceScanByPersonId(any())).thenReturn(faceScan);

        when(personalityFacadeSpy.getFingerScanByPersonId(any())).thenReturn(null);

        DevicesExtension devicesExtension = devicesExtensionAdapter.convert(personality,null);
        assertNotNull(devicesExtension);
        assertNotNull(devicesExtension.getFaceScan());
        assertTrue(devicesExtension.getFaceScan().getEnrolled());
        assertEquals(dateTime, devicesExtension.getFaceScan().getEnrollmentDateTime());
        assertEquals(Integer.valueOf(2), devicesExtension.getFaceScan().getThreshold());
        assertEquals(Integer.valueOf(1), devicesExtension.getFaceScan().getQualityScore());
        assertEquals("N/A", devicesExtension.getFaceScan().getEnrollmentLocation());
        assertTrue(devicesExtension.getFaceScan().getTemplateDeleted());
    }
    
    @Test
    public void testConvertEffDatedBadgeDetails() {
        List<BadgeAssignment> list = new ArrayList<>();
        BadgeAssignmentSet badgeAssignmentSet = mock(BadgeAssignmentSet.class);
        BadgeAssignment badgeAssignment1 = mock(BadgeAssignment.class);
        // EffectiveDatedCollection<BadgeDetailsEntry> effectiveDatedCollection
        // = new EffectiveDatedCollection<>();

        when(badgeAssignment1.getBadgeAssignmentId()).thenReturn(new ObjectIdLong(101));
        when(badgeAssignment1.getBadgeNumber()).thenReturn("706132");
        KTime kTime = new KTime(10, 20, 40, 543);
        Date date = new Date();
        date.setDate(31);
        date.setMonth(11);
        date.setYear(1998 - 1900);
        KDate kDate = new KDate(date);
        KDateTime kDateTime = new KDateTime(kDate, kTime);
        when(badgeAssignment1.getEffectiveDateTime()).thenReturn(kDateTime);
        LocalDateTime date1 = adapterHelper.kDateTimeToLocalDateTime(kDateTime);
        kTime = new KTime(11, 21, 40, 543);
        date = new Date();
        date.setDate(11);
        date.setMonth(9);
        date.setYear(2011 - 1900);
        kDate = new KDate(date);
        kDateTime = new KDateTime(kDate, kTime);
        LocalDateTime date2 =  adapterHelper.kDateTimeToLocalDateTime(kDateTime);
        when(badgeAssignment1.getExpirationDateTime()).thenReturn(kDateTime);
        list.add(badgeAssignment1);
        BadgeDetailsEntry badgeDetailsEntry1 = new BadgeDetailsEntry(date1, date2);
        badgeDetailsEntry1.setBadgeAssignmentId(101l);
        badgeDetailsEntry1.setBadgeNumber("706132");
        badgeDetailsEntry1.setVersionCount(0L);

        BadgeAssignment badgeAssignment2 = mock(BadgeAssignment.class);
        when(badgeAssignment2.getBadgeAssignmentId()).thenReturn(new ObjectIdLong(102));
        when(badgeAssignment2.getBadgeNumber()).thenReturn("705244");
        kTime = new KTime(9, 21, 40, 546);
        date = new Date();
        date.setDate(18);
        date.setMonth(11);
        date.setYear(2015 - 1900);
        kDate = new KDate(date);
        kDateTime = new KDateTime(kDate, kTime);
        LocalDateTime date3 =  adapterHelper.kDateTimeToLocalDateTime(kDateTime);
        when(badgeAssignment2.getEffectiveDateTime()).thenReturn(kDateTime);
        kTime = new KTime(4, 20, 30, 441);
        date = new Date();
        date.setDate(11);
        date.setMonth(7);
        date.setYear(2021 - 1900);
        kDate = new KDate(date);
        kDateTime = new KDateTime(kDate, kTime);
        LocalDateTime date4 =  adapterHelper.kDateTimeToLocalDateTime(kDateTime);
        when(badgeAssignment2.getExpirationDateTime()).thenReturn(kDateTime);
        list.add(badgeAssignment2);
        BadgeDetailsEntry badgeDetailsEntry2 = new BadgeDetailsEntry(date3, date4);
        badgeDetailsEntry2.setBadgeAssignmentId(102l);
        badgeDetailsEntry2.setBadgeNumber("705244");
        badgeDetailsEntry2.setVersionCount(0L);

        BadgeAssignment badgeAssignment3 = mock(BadgeAssignment.class);
        when(badgeAssignment3.getBadgeAssignmentId()).thenReturn(new ObjectIdLong(103));
        when(badgeAssignment3.getBadgeNumber()).thenReturn("706266");
        kTime = new KTime(12, 20, 40, 543);
        date = new Date();
        date.setDate(17);
        date.setMonth(1);
        date.setYear(2013 - 1900);
        kDate = new KDate(date);
        kDateTime = new KDateTime(kDate, kTime);
        LocalDateTime date5 =  adapterHelper.kDateTimeToLocalDateTime(kDateTime);
        when(badgeAssignment3.getEffectiveDateTime()).thenReturn(kDateTime);
        kTime = new KTime(6, 20, 40, 543);
        date = new Date();
        date.setDate(16);
        date.setMonth(7);
        date.setYear(2018 - 1900);
        kDate = new KDate(date);
        kDateTime = new KDateTime(kDate, kTime);
        LocalDateTime date6 =  adapterHelper.kDateTimeToLocalDateTime(kDateTime);
        when(badgeAssignment3.getExpirationDateTime()).thenReturn(kDateTime);
        list.add(badgeAssignment3);
        BadgeDetailsEntry badgeDetailsEntry3 = new BadgeDetailsEntry(date5, date6);
        badgeDetailsEntry3.setBadgeAssignmentId(103l);
        badgeDetailsEntry3.setBadgeNumber("706266");
        badgeDetailsEntry3.setVersionCount(0L);

        when(badgeAssignmentSet.getAllBadgeAssignments()).thenReturn(list);
        Collection<BadgeDetailsEntry> badgeDetailsEntryCollection = new ArrayList<>();
        badgeDetailsEntryCollection.add(badgeDetailsEntry1);
        badgeDetailsEntryCollection.add(badgeDetailsEntry2);
        badgeDetailsEntryCollection.add(badgeDetailsEntry3);

        // effectiveDatedCollection.setEffectiveDatedEntries(badgeDetailsEntryCollection);

        devicesExtension.setBadgeDetails(devicesExtensionAdapter.convertEffDatedBadgeDetails(badgeAssignmentSet));

        assertEquals(badgeDetailsEntryCollection, devicesExtension.getBadgeDetails());

        assertEquals(null, devicesExtensionAdapter.convertEffDatedBadgeDetails(null));

    }

    @Test
    public void testFingerRequiredFalse() {
        when(person.getFingerRequiredSwitch()).thenReturn(0L);
        when(personality.getNameData()).thenReturn(person);
        
        DevicesExtension deviceExtension = devicesExtensionAdapter.convert(personality,null);
        assertFalse(deviceExtension.isFingerRequired());
        
        when(person.getFingerRequiredSwitch()).thenReturn(null);
        deviceExtension = devicesExtensionAdapter.convert(personality,null);
        assertFalse(deviceExtension.isFingerRequired());
    }
    
    
    @Test
    public void testFingerRequiredTrue() {
        when(person.getFingerRequiredSwitch()).thenReturn(1L);
        when(personality.getNameData()).thenReturn(person);
        
        DevicesExtension deviceExtension = devicesExtensionAdapter.convert(personality,null);
        assertTrue(deviceExtension.isFingerRequired());
    }

    @Test
    public void testConvertTeleTimeIPInfo() {
        String ttipId = "1";
        Long ttipUserProfileId = 2L;
        String ttipUserProfileName = "name";
        ObjectIdLong objectIdLong = new ObjectIdLong();

        Personality input = mock(Personality.class);

        DevicesExtension expected = new DevicesExtension();
        expected.setChangePasswordIsRequired(false);
        expected.isTeleTimeIPEmployee(true);
        expected.setTeleTimeIPId(ttipId);
        expected.setTeleTimeIPUserProfileId(ttipUserProfileId);
        expected.setTeleTimeIPUserProfileName(ttipUserProfileName);
        expected.setFaceConsentDetails(new BiometricConsentDetailsEntry());
        expected.setFingerConsentDetails(new BiometricConsentDetailsEntry());

        FaceScanEntry faceScanEntry = new FaceScanEntry();
        faceScanEntry.setEnrolled(false);
        faceScanEntry.setTemplateDeleted(false);
        expected.setFaceScan(faceScanEntry);

        when(input.getPersonId()).thenReturn(objectIdLong);
        
        TTIPUserProfileDTO ttipUserProfileDTO = new TTIPUserProfileDTO(ttipUserProfileId, ttipUserProfileName);
        TTIPEmployeeAttributesDTO ttipEmployeeAttributesDTO =
        		new TTIPEmployeeAttributesDTO(ttipUserProfileDTO, ttipId, false);
        
        when(personalityFacadeSpy.getTTIPEmployeeAttributes(objectIdLong)).thenReturn(ttipEmployeeAttributesDTO);
        DevicesExtension actual = devicesExtensionAdapter.convert(input,null);
        assertTrue(expected.equals(actual));
    }

    @Test
    public void testConvertTeleTimeIPInfoWithEmptyPersonality() {
        Personality input = mock(Personality.class);

        DevicesExtension expected = new DevicesExtension();
        expected.setFaceConsentDetails(new BiometricConsentDetailsEntry());
        expected.setFingerConsentDetails(new BiometricConsentDetailsEntry());

        DevicesExtension actual = devicesExtensionAdapter.convert(input,null);
        assertNull(actual.getTeleTimeIPId());
        assertNull(actual.isTeleTimeIPEmployee());
        assertNull(actual.getTeleTimeIPUserProfileId());
        assertNull(actual.getTeleTimeIPUserProfileName());
        assertNull(actual.getChangePasswordIsRequired());
    }

    @Test
    public void testConvertConsentDetailsByBioTypeWithEmptyPersonality() {
        Personality input = mock(Personality.class);

        DevicesExtension expected = new DevicesExtension();
        expected.setFaceConsentDetails(new BiometricConsentDetailsEntry());
        expected.setFingerConsentDetails(new BiometricConsentDetailsEntry());

        FaceScanEntry faceScanEntry = new FaceScanEntry();
        faceScanEntry.setEnrolled(false);
        faceScanEntry.setTemplateDeleted(false);
        expected.setFaceScan(faceScanEntry);

        when(personalityFacadeSpy.getBiometricConsentDetailsByPersonIdAndConsentType(null, BiometricType.FINGER_TYPE)).thenReturn(null);

        DevicesExtension actual = devicesExtensionAdapter.convert(input,null);

        assertTrue(expected.equals(actual));

        verify(personalityFacadeSpy).getBiometricConsentDetailsByPersonIdAndConsentType(null, BiometricType.FINGER_TYPE);
    }

    private BiometricConsentDetailsDTO getBiometricConsentDetails(LocalDateTime localDateTime, BiometricConsentType status, String location,
                                                                  String form, String text) {
        BiometricConsentDetailsDTO biometricConsentDetails = new BiometricConsentDetailsDTO();

        biometricConsentDetails.setConsentStatus(status);
        biometricConsentDetails.setConsentDateTime(localDateTime);
        biometricConsentDetails.setConsentForm(form);
        biometricConsentDetails.setConsentLocation(location);
        biometricConsentDetails.setConsentFormText(text);
        return biometricConsentDetails;
    }

    @Test
    public void testConvertConsentDetailsByBioType() {
        Personality input = mock(Personality.class);

        LocalDateTime localDateTime = LocalDateTime.now();
        String finger = "finger";
        String face = "face";
        String location = "Location";
        String form = "Form";
        String text = "Text";

        DevicesExtension expected = new DevicesExtension();
        expected.setFaceConsentDetails(getBiometricConsentDetailsEntry(localDateTime, BiometricConsentType.AFFIRMATIVE.name(), face + location, face + form, face + text));
        expected.setFingerConsentDetails(getBiometricConsentDetailsEntry(localDateTime, BiometricConsentType.AFFIRMATIVE.name(), finger + location, finger + form, finger + text));
        FaceScanEntry faceScanEntry = new FaceScanEntry();
        faceScanEntry.setEnrolled(false);
        faceScanEntry.setTemplateDeleted(false);
        expected.setFaceScan(faceScanEntry);

        BiometricConsentDetails biometricConsentDetailsFace = getBiometricConsentDetails(localDateTime, BiometricConsentType.AFFIRMATIVE, face + location, face + form, face + text);
        when(personalityFacadeSpy.getBiometricConsentDetailsByPersonIdAndConsentType(null, BiometricType.FACE_TYPE)).thenReturn(biometricConsentDetailsFace);

        BiometricConsentDetails biometricConsentDetailsFinger = getBiometricConsentDetails(localDateTime, BiometricConsentType.AFFIRMATIVE, finger + location, finger + form, finger + text);
        when(personalityFacadeSpy.getBiometricConsentDetailsByPersonIdAndConsentType(null, BiometricType.FINGER_TYPE)).thenReturn(biometricConsentDetailsFinger);

        DevicesExtension actual = devicesExtensionAdapter.convert(input,null);

        assertTrue(expected.equals(actual));

        verify(personalityFacadeSpy).getBiometricConsentDetailsByPersonIdAndConsentType(null, BiometricType.FACE_TYPE);
        verify(personalityFacadeSpy).getBiometricConsentDetailsByPersonIdAndConsentType(null, BiometricType.FINGER_TYPE);
    }

    @Test
    public void testConvertConsentDetailsByBioTypeWithEmptyConsentLocation() {
        Personality input = mock(Personality.class);

        LocalDateTime localDateTime = LocalDateTime.now();
        String finger = "finger";
        String face = "face";
        String location = "Location";
        String form = "Form";
        String text = "Text";

        DevicesExtension expected = new DevicesExtension();
        expected.setFaceConsentDetails(getBiometricConsentDetailsEntry(localDateTime, BiometricConsentType.AFFIRMATIVE.name(), face + location, face + form, face + text));
        expected.setFingerConsentDetails(getBiometricConsentDetailsEntry(localDateTime, BiometricConsentType.AFFIRMATIVE.name(), finger + location, finger + form, finger + text));
        FaceScanEntry faceScanEntry = new FaceScanEntry();
        faceScanEntry.setEnrolled(false);
        faceScanEntry.setTemplateDeleted(false);
        expected.setFaceScan(faceScanEntry);

        BiometricConsentDetails biometricConsentDetailsFace = getBiometricConsentDetails(localDateTime, BiometricConsentType.AFFIRMATIVE, null, face + form, face + text);
        when(personalityFacadeSpy.getFaceEnrollmentLocation(null)).thenReturn(face + location);
        when(personalityFacadeSpy.getBiometricConsentDetailsByPersonIdAndConsentType(null, BiometricType.FACE_TYPE)).thenReturn(biometricConsentDetailsFace);

        BiometricConsentDetails biometricConsentDetailsFinger = getBiometricConsentDetails(localDateTime, BiometricConsentType.AFFIRMATIVE, null, finger + form, finger + text);
        when(personalityFacadeSpy.getPrimaryFingerEnrollmentLocation(null)).thenReturn(finger + location);
        when(personalityFacadeSpy.getBiometricConsentDetailsByPersonIdAndConsentType(null, BiometricType.FINGER_TYPE)).thenReturn(biometricConsentDetailsFinger);

        DevicesExtension actual = devicesExtensionAdapter.convert(input,null);

        assertTrue(expected.equals(actual));

        verify(personalityFacadeSpy).getBiometricConsentDetailsByPersonIdAndConsentType(null, BiometricType.FACE_TYPE);
        verify(personalityFacadeSpy).getBiometricConsentDetailsByPersonIdAndConsentType(null, BiometricType.FINGER_TYPE);
        verify(personalityFacadeSpy).getFaceEnrollmentLocation(null);
        verify(personalityFacadeSpy).getPrimaryFingerEnrollmentLocation(null);
    }


    private BiometricConsentDetailsEntry getBiometricConsentDetailsEntry(LocalDateTime time, String status, String location, String form, String text) {
        return new BiometricConsentDetailsEntry.Builder()
                .withConsentStatus(status)
                .withConsentDateTime(time)
                .withConsentLocation(location)
                .withConsentForm(form)
                .withConsentText(text)
                .build();
    }

    @Test
    public void testCreateSnapshot() {
        DevicesExtension expectedExtension = new DevicesExtension();
        expectedExtension.setFingerRequired(true);
        expectedExtension.setFingerEnrolled(true);
        expectedExtension.setFingerEnrolledForIdentification(false);
        expectedExtension.setPrimaryFingerEnrollmentLocation("PrimaryFingerEnrollmentLocation");
        expectedExtension.setPrimaryFingerThreshold("PrimaryFingerThreshold");
        expectedExtension.setFingerScans(EMPTY_LIST);
        expectedExtension.setFaceRequired(true);
        expectedExtension.setFaceScan(new FaceScanEntry());

        DevicesExtension actualExtension = devicesExtensionAdapter.createSnapshot(expectedExtension);
        assertEquals(expectedExtension, actualExtension);
        assertNotSame(expectedExtension, actualExtension);
    }

    @Test
    public void testCreateSnapshotWithNull() {
        DevicesExtension actualExtension = devicesExtensionAdapter.createSnapshot(null);
        assertNull(actualExtension);
    }
}
