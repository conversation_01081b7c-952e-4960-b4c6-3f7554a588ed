package com.kronos.people.personality.model.extension;


import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

//@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class ExtensionIdentifierMicroTest {
	
	ExtensionIdentifier extensionIdentifier;
	
	@BeforeEach
	public void setup(){
		
		extensionIdentifier = new ExtensionIdentifier("employee");
	}
	
	@AfterEach
	public void tearDown(){
		
		extensionIdentifier = null;
	}
	
	@Test
	public void testEquals(){
		
		boolean result = extensionIdentifier.equals(extensionIdentifier);
		assertTrue(result);
	}
	
	@Test
	public void testEqualsWhenObjectIsNull(){
//		boolean result = extensionIdentifier.equals(null);
		boolean result = extensionIdentifier == null;
		assertFalse(result);
	}
	
	@Test
	public void testEqualsForDifferentObjectsType(){
		boolean result = extensionIdentifier.equals(new Object());
		assertFalse(result);
	}
	

}
