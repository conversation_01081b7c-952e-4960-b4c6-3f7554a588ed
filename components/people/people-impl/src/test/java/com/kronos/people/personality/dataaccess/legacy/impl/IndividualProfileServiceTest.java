/*******************************************************************************
 * IndividualProfileService.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.dataaccess.legacy.impl;

import com.ukg.container.selectiveloading.exception.NotImplementedException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;

/*******************************************************************************
 * Test class for IndividualProfileService
 * <AUTHOR>
 ******************************************************************************/
@ExtendWith(MockitoExtension.class)
public class IndividualProfileServiceTest {
    @InjectMocks
    private IndividualProfileService individualProfileService;

    @Test
    public void test_WhenGetAllIndividualProfiles_Invoked_ThenThrowsNotImplementedException(){
        assertThrows(NotImplementedException.class, () -> {
            individualProfileService.getAllIndividualProfiles();
        });
    }

    @Test
    public void test_WhenGetAllAssignedIndividualProfiles_Invoked_ThenThrowsNotImplementedException(){
        assertThrows(NotImplementedException.class, () -> {
            individualProfileService.getAllAssignedIndividualProfiles();
        });
    }

    @Test
    public void test_WhenGetIndividualProfileNameById_Invoked_ThenThrowsNotImplementedException(){
        assertThrows(NotImplementedException.class, () -> {
            individualProfileService.getIndividualProfileNameById(123L);
        });
    }

    @Test
    public void test_WhenGetIndividualProfileIdByName_Invoked_ThenThrowsNotImplementedException(){
        assertThrows(NotImplementedException.class, () -> {
            individualProfileService.getIndividualProfileIdByName("name");
        });
    }

    @Test
    public void test_WhenGetIndividualProfileNameByEmployeeId_Invoked_ThenThrowsNotImplementedException() {
        assertThrows(NotImplementedException.class, () -> {
            individualProfileService.getIndividualProfileNameByEmployeeId(123L);
        });
    }

    @Test
    public void test_WhenGetIndividualProfileIdByEmployeeId_Invoked_ThenThrowsNotImplementedException(){
        assertThrows(NotImplementedException.class, () -> {
            individualProfileService.getIndividualProfileIdByEmployeeId(123L);
        });
    }

    @Test
    public void test_WhenUpdateEmployeeIndividualProfile_Invoked_ThenThrowsNotImplementedException(){
        assertThrows(NotImplementedException.class, () -> {
            individualProfileService.updateEmployeeIndividualProfile(123L,456L);
        });
    }
}
