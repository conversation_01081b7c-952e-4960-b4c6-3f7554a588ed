/*******************************************************************************
 * PersonTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.dataaccess.entity;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

/*******************************************************************************
 * Test class for Person
 * <AUTHOR>
 ******************************************************************************/
@ExtendWith(MockitoExtension.class)
public class PersonTest {
    @InjectMocks
    private Person person;

    @Test
    public void test_GetPersonId(){
        person.setPersonid(123L);
        assertEquals(Long.valueOf(123L),  person.getPersonid());
    }

    @Test
    public void test_SetPersonId(){
        person.setPersonid(123L);
        assertEquals(Long.valueOf(123L),  person.getPersonid());
    }

    @Test
    public void test_GetPersonnum(){
        person.setPersonnum("Personnum");
        assertEquals("Personnum",person.getPersonnum());
    }

    @Test
    public void test_SetPersonnum(){
        person.setPersonnum("Personnum");
        assertEquals("Personnum",person.getPersonnum());
    }

    @Test
    public void test_GetFirstnm() {
        person.setFirstnm("firstnm");
        assertEquals("firstnm",person.getFirstnm());
    }

    @Test
    public void test_SetFirstnm() {
        person.setFirstnm("firstnm");
        assertEquals("firstnm",person.getFirstnm());
    }

    @Test
    public void test_GetLastnm() {
        person.setLastnm("lastnm");
        assertEquals("lastnm",person.getLastnm());
    }

    @Test
    public void test_SetLastnm() {
        person.setLastnm("lastnm");
        assertEquals("lastnm",person.getLastnm());
    }

    @Test
    public void test_GetMiddleinitialnm(){
        person.setMiddleinitialnm("middlenm");
        assertEquals("middlenm",person.getMiddleinitialnm());
    }

    @Test
    public void test_SetMiddleinitialnm(){
        person.setMiddleinitialnm("middlenm");
        assertEquals("middlenm",person.getMiddleinitialnm());
    }

    @Test
    public void test_GetBirthdtm(){
        LocalDateTime ldt=LocalDateTime.now();
        person.setBirthdtm(ldt);
        assertEquals(ldt,person.getBirthdtm());
    }

    @Test
    public void test_SetBirthdtm(){
        LocalDateTime ldt=LocalDateTime.now();
        person.setBirthdtm(ldt);
        assertEquals(ldt,person.getBirthdtm());
    }

    @Test
    public void test_GetUpdatedtm(){
        LocalDateTime ldt=LocalDateTime.now();
        person.setUpdatedtm(ldt);
        assertEquals(ldt,person.getUpdatedtm());
    }

    @Test
    public void test_SetUpdatedtm(){
        LocalDateTime ldt=LocalDateTime.now();
        person.setUpdatedtm(ldt);
        assertEquals(ldt,person.getUpdatedtm());
    }
}
