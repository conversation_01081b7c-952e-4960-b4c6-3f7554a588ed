package com.kronos.people.personality.service.impl.ia;

import com.kronos.commonapp.timezone.symbolicperiods.api.ISymbolicPeriodService;
import com.kronos.commonapp.timezone.symbolicperiods.model.LocalDateSpan;
import com.kronos.commonbusiness.datatypes.ia.*;
import com.kronos.people.personality.exception.BaseException;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.exception.impl.ExceptionHelper;
import com.kronos.people.personality.exception.impl.ia.PersonalityExceptionHelper;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.TimekeepingExtension;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.PayRuleEntry;
import com.kronos.people.personality.service.PersonalityService;
import com.kronos.people.personality.util.DataRangeHelper;
import com.kronos.wfc.commonapp.rules.business.PayRule;
import com.kronos.wfc.commonapp.rules.business.PayRuleCache;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.statement.DataStore;
import com.kronos.wfc.platform.utility.framework.datetime.KDateTime;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

//@RunWith(PowerMockRunner.class)
//@PrepareForTest({DataRangeHelper.class, PayRuleCache.class})
@ExtendWith(MockitoExtension.class)
public class PersonalityPayRuleHistoryServiceImplTest {

    private static final String PAY_RULE_HISTORY_COLUMN = "EMP_HIST_PAY_RULE_NAME";
    private static final String PAY_RULE_EFFECTIVE_DATE_HISTORY_COLUMN = "EMP_HIST_PAY_RULE_EFFECTIVE_DATE";
    private static final String PAY_RULE_EXPIRATION_DATE_HISTORY_COLUMN = "EMP_HIST_PAY_RULE_EXPIRATION_DATE";
    private static final String SYMBOLIC_SERVICE_QUALIFIER = "Current_Payperiod";
    private static final String PAY_RULE_NAME = "Current_Payperiod";
    private static final String PERSON_NUMBER = "1234";

    private static final LocalDate PAY_RULE_EFFECTIVE_DATE = LocalDate.of(2020, 1, 1);
    private static final LocalDate PAY_RULE_EXPIRATION_DATE = LocalDate.of(2020, 2, 1);

    @Mock
    private PersonalityService personalityService;
    @Mock
    private ISymbolicPeriodService symbolicPeriodService;
    @Mock
    private PersonalityExceptionHelper exceptionHelper;
    @InjectMocks
    private PersonalityPayRuleHistoryServiceImpl payRuleHistoryService;

    private IARequest request;
    private List<Long> employeeIds;
    private Long[] employeeIdsArray;
    private Map<Long, LocalDateSpan> employeeSpans;
    private LocalDateSpan employeeSpan;
    private Map<Long, PersonalityResponse<TimekeepingExtension>> personalityServiceResponse;
    private PayRule payRule;

    private MockedStatic<DataRangeHelper> mockedDataRangeHelper;
    private MockedStatic<PayRuleCache> mockedPayRuleCache;

    @BeforeEach
    public void setUp() throws IllegalAccessException, NoSuchFieldException {
//        PowerMockito.mockStatic(DataRangeHelper.class, PayRuleCache.class);

        mockedDataRangeHelper = Mockito.mockStatic(DataRangeHelper.class);
        mockedPayRuleCache = Mockito.mockStatic(PayRuleCache.class);

        request = new IARequest();

        request.setSymbolicPeriod(new com.kronos.commonbusiness.datatypes.ref.ObjectRef(1L, SYMBOLIC_SERVICE_QUALIFIER));
        request.setEmployees(Arrays.asList("1", "5", "10", "20", "34"));

        IAColumn payRuleColumn = new IAColumn(PAY_RULE_HISTORY_COLUMN, PAY_RULE_HISTORY_COLUMN);
        IAColumn payRuleEffectiveDateColumn = new IAColumn(PAY_RULE_EFFECTIVE_DATE_HISTORY_COLUMN, PAY_RULE_EFFECTIVE_DATE_HISTORY_COLUMN);
        IAColumn payRuleExpirationDateColumn = new IAColumn(PAY_RULE_EXPIRATION_DATE_HISTORY_COLUMN, PAY_RULE_EXPIRATION_DATE_HISTORY_COLUMN);
        request.setColumns(Arrays.asList(payRuleColumn, payRuleEffectiveDateColumn, payRuleExpirationDateColumn));

        employeeIdsArray = new Long[]{1L, 5L, 10L, 20L, 34L};
        employeeIds = Arrays.asList(employeeIdsArray);
        request.setEmployees(employeeIds.stream().map(Object::toString).collect(Collectors.toList()));

        employeeSpans = new HashMap<>();
        employeeSpan = new LocalDateSpan(LocalDate.of(2020, 1, 11), LocalDate.of(2020, 1, 20));
        employeeIds.forEach(id -> employeeSpans.put(id, employeeSpan));

        personalityServiceResponse = new HashMap<>();

        PayRuleEntry payRuleEntry = new PayRuleEntry();
        payRuleEntry.setPayRuleId(1L);
        payRuleEntry.setEffectiveDate(PAY_RULE_EFFECTIVE_DATE);
        payRuleEntry.setExpirationDate(PAY_RULE_EXPIRATION_DATE);

        personalityServiceResponse.put(1L, createTimekeepingExtension(Arrays.asList(payRuleEntry, payRuleEntry)));
        personalityServiceResponse.put(5L, createTimekeepingExtension(Collections.singletonList(payRuleEntry)));
        personalityServiceResponse.put(10L, createTimekeepingExtension(Collections.singletonList(payRuleEntry)));
        personalityServiceResponse.put(20L, createTimekeepingExtension(Collections.singletonList(payRuleEntry)));
        personalityServiceResponse.put(34L, createTimekeepingExtension(Arrays.asList(payRuleEntry, payRuleEntry, payRuleEntry)));

        payRule = new PayRule(new DataStore());
        Field payRuleNameField = payRule.getClass().getDeclaredField("name");
        payRuleNameField.setAccessible(true);
        payRuleNameField.set(payRule, PAY_RULE_NAME);
    }

    @AfterEach
    public void tearDown() {
//        verifyNoMoreInteractions(personalityService, symbolicPeriodService, exceptionHelper, DataRangeHelper.class, PayRuleCache.class);
        verifyNoMoreInteractions(personalityService, symbolicPeriodService, exceptionHelper);
        mockedDataRangeHelper.close();
        mockedPayRuleCache.close();

    }

    @Test
    public void getPayRuleHistory() {
        when(DataRangeHelper.resolveSymbolicPeriodAndDateRange(request, employeeIds, symbolicPeriodService)).thenReturn(employeeSpans);
        when(DataRangeHelper.isOverlap(new LocalDateSpan(PAY_RULE_EFFECTIVE_DATE, PAY_RULE_EXPIRATION_DATE), employeeSpan)).thenReturn(true);
        when(PayRuleCache.getPayRule(eq(new ObjectIdLong(1L)), any(KDateTime.class))).thenReturn(payRule);

        when(personalityService.findTimekeepingExtensions(employeeIdsArray)).thenReturn(personalityServiceResponse);

        IAResponse response = payRuleHistoryService.getPayRuleHistory(request);
        response.getData().forEach(row -> row.getAttributes().forEach(this::assertAttribute));

//        verifyStatic(times(1));
//        DataRangeHelper.resolveSymbolicPeriodAndDateRange(any(), any(), any());
//        verifyStatic(times(8));
//        DataRangeHelper.isOverlap(any(), any());
//        verifyStatic(times(8));
//        PayRuleCache.getPayRule(any(ObjectIdLong.class), any(KDateTime.class));
        mockedDataRangeHelper.verify(() -> DataRangeHelper.resolveSymbolicPeriodAndDateRange(any(), any(), any()), times(1));
        mockedDataRangeHelper.verify(() -> DataRangeHelper.isOverlap(any(), any()), times(8));
        mockedPayRuleCache.verify(() -> PayRuleCache.getPayRule(any(ObjectIdLong.class), any(KDateTime.class)), times(8));
        verify(personalityService, times(1)).findTimekeepingExtensions(employeeIdsArray);
    }

    @Test
    public void getPayRuleHistoryWithEmployeeErrorResponse() {
        when(DataRangeHelper.resolveSymbolicPeriodAndDateRange(request, employeeIds, symbolicPeriodService)).thenReturn(employeeSpans);
        when(DataRangeHelper.isOverlap(new LocalDateSpan(PAY_RULE_EFFECTIVE_DATE, PAY_RULE_EXPIRATION_DATE), employeeSpan)).thenReturn(true);
        when(PayRuleCache.getPayRule(eq(new ObjectIdLong(1L)), any(KDateTime.class))).thenReturn(payRule);

        personalityServiceResponse.get(1L).setException(new ExceptionHelper(null).createException(PersonalityErrorCode.UNKNOWN_ERROR));
        when(personalityService.findTimekeepingExtensions(employeeIdsArray)).thenReturn(personalityServiceResponse);
        IAErrorDetail errorDetail = new IAErrorDetail();
        when(exceptionHelper.buildEmployeeExtensionErrorDetail(anyList(),
                eq(request))).thenReturn(errorDetail);

        IAResponse response = payRuleHistoryService.getPayRuleHistory(request);
        response.getData().forEach(row -> row.getAttributes().forEach(this::assertAttribute));

//        verifyStatic(times(1));
//        DataRangeHelper.resolveSymbolicPeriodAndDateRange(any(), any(), any());
//        verifyStatic(times(6));
//        DataRangeHelper.isOverlap(any(), any());
//        verifyStatic(times(6));
//        PayRuleCache.getPayRule(any(ObjectIdLong.class), any(KDateTime.class));
        mockedDataRangeHelper.verify(() -> DataRangeHelper.resolveSymbolicPeriodAndDateRange(any(), any(), any()), times(1));
        mockedDataRangeHelper.verify(() -> DataRangeHelper.isOverlap(any(), any()), times(6));
        mockedPayRuleCache.verify(() -> PayRuleCache.getPayRule(any(ObjectIdLong.class), any(KDateTime.class)), times(6));
        verify(personalityService, times(1)).findTimekeepingExtensions(employeeIdsArray);
        verify(exceptionHelper, times(1)).buildEmployeeExtensionErrorDetail(any(), any());
        assertEquals(errorDetail, response.getErrorDetails(), "ErrorDetails are not equivalent");
    }

    @Test
    public void getPayRuleHistoryWithException() {
        String errorMessage = "Employee extension error Message";
        PersonalityErrorCode errorCode = PersonalityErrorCode.NOT_FOUND;
        IAErrorDetail errorDetail = new IAErrorDetail();
        when(exceptionHelper.buildCommonErrorDetail(eq(request))).thenReturn(errorDetail);
        when(DataRangeHelper.resolveSymbolicPeriodAndDateRange(request, employeeIds, symbolicPeriodService))
                .thenThrow(new BaseException(errorCode, errorMessage, null, null));

        IAResponse response = payRuleHistoryService.getPayRuleHistory(request);

//        verifyStatic(times(1));
//        DataRangeHelper.resolveSymbolicPeriodAndDateRange(any(), any(), any());
        mockedDataRangeHelper.verify(() -> DataRangeHelper.resolveSymbolicPeriodAndDateRange(any(), any(), any()), times(1));
        verify(exceptionHelper, times(1)).buildCommonErrorDetail(any());
        assertTrue(response.getData().isEmpty(), "Response data should be empty");
        assertEquals(errorDetail, response.getErrorDetails(), "ErrorDetails are not equivalent");

    }

    private PersonalityResponse<TimekeepingExtension> createTimekeepingExtension(List<PayRuleEntry> payRuleEntries) {
        TimekeepingExtension timekeepingExtension = new TimekeepingExtension();
        timekeepingExtension.setPersonNumber(PERSON_NUMBER);
        timekeepingExtension.setDirectPayRules(new EffectiveDatedCollection<>(payRuleEntries));
        return new PersonalityResponse<>(timekeepingExtension, null);
    }

    private void assertAttribute(IARowAttribute att) {
        switch (att.getName()) {
            case PAY_RULE_HISTORY_COLUMN:
                assertEquals(PAY_RULE_NAME, att.getValue(), "Pay rule name is not correct");
                break;
            case PAY_RULE_EFFECTIVE_DATE_HISTORY_COLUMN:
                assertEquals(PAY_RULE_EFFECTIVE_DATE.toString(), att.getValue(), "Pay rule effective date is not correct");
                break;
            case PAY_RULE_EXPIRATION_DATE_HISTORY_COLUMN:
                assertEquals(PAY_RULE_EXPIRATION_DATE.toString(), att.getValue(), "Pay rule expiration date is not correct");
                break;
        }
    }
}
