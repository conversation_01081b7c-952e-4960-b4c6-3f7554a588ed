package com.kronos.people.personality.tenant;

import java.util.Set;

import com.kronos.tenantprovider.api.TenantProvider;
import com.kronos.tenantprovider.api.exception.TenantProviderException;

public class MockTenantProvider implements TenantProvider {

	String tenantId;
	
	boolean shouldThrowException;
	
	@Override
	public String getTenantId() throws TenantProviderException {
		if (shouldThrowException) throw new TenantProviderException();
		return tenantId;
	}

	@Override
	public void setTenantId(String paramString) {
		this.tenantId = paramString;
	}

	@Override
	public void removeTenantId() {
		this.tenantId = null;			
	}

	@Override
	public Set<String> getAllTenantIds() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Long getInternalTenantId(String arg0) throws TenantProviderException {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean isTenantIdNullforCurrentThread() {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public void setInternalTenantId(Long arg0) throws TenantProviderException {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void validateTenantForTrxId(String arg0)
			throws TenantProviderException {
		// TODO Auto-generated method stub
		
	}
	public Long getInternalTenantId() throws TenantProviderException {
		return null;
	}
	
	public Boolean isHttpRequest() {
		return false;
	}
	
	public String getTenantSchemaForUpgrade() {
		return null;
	}
	
	public void removeTenantDataForUpgrade() throws TenantProviderException{
		
	}
	public void setTenantDataForUpgrade(Long tenantId, String extTenantId,
			String tenantSchemaName) {
		
	}
	
	public String getTenantType(Long aLong) {
		return null;
	}
	
	public 	Boolean isUpgradeRequest() {
		return false;
	}
}
