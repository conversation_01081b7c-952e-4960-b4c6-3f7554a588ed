package com.kronos.people.personality.exception;

import static com.kronos.people.personality.exception.PersonalityErrorCode.UNKNOWN_ERROR;
import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PersonalityExtensionExceptionMicroTest {

	@Test
	public void testConsWithMessage() {
		PersonalityExtensionException exception = new PersonalityExtensionException(UNKNOWN_ERROR, "Unknown Error");
		assertEquals("Unknown Error", exception.getMessage());
		assertEquals("UNKNOWN_ERROR", exception.getErrorCode());
		assertNull(exception.getDebugMessage());
		assertTrue(exception.getMessageArgs().isEmpty());

	}

	@Test
	public void testConsWithMessageArgsMap() {
		Map<String, String> map = new HashMap<String, String>();
		map.put("key", "value");
		PersonalityExtensionException exception = new PersonalityExtensionException(UNKNOWN_ERROR, "Unknown Error", map);
		assertEquals(map, exception.getMessageArgs());
		assertEquals("value", exception.getMessageArgs().get("key"));
	}

	@Test
	public void testConsWithDebugMessage() {
		PersonalityExtensionException exception = new PersonalityExtensionException(UNKNOWN_ERROR, "Unknown Error", "Debug Message");
		assertEquals("Debug Message", exception.getDebugMessage());
	}

}
