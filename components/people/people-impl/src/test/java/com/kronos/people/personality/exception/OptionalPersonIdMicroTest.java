package com.kronos.people.personality.exception;

import com.kronos.people.personality.util.OptionalPersonId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class OptionalPersonIdMicroTest {

	@Test
	public void testConstructor() {
		PersonalityExtensionException pex = new PersonalityExtensionException(PersonalityErrorCode.UNKNOWN_ERROR, "Unkonwn Message");
		OptionalPersonId optionalId = new OptionalPersonId(Long.valueOf(123l), pex);
		assertEquals("Unkonwn Message", optionalId.getException().getMessage());
		assertEquals(Long.valueOf(123l), optionalId.getPersonId());
	}

	@Test
	public void isExpPersentTest() {
		PersonalityExtensionException pex = new PersonalityExtensionException(PersonalityErrorCode.BADGE_NUMBER_NOT_VALID, "Message");
		OptionalPersonId optionalId = new OptionalPersonId(Long.valueOf(1234l), pex);
		optionalId.setException(pex);

		assertEquals(true, optionalId.isExceptionPresent());
		assertEquals("Message", optionalId.getException().getMessage());

		optionalId = new OptionalPersonId(Long.valueOf(1234l), null);
		assertEquals(false, optionalId.isExceptionPresent());
	}

	@Test
	public void testPersonId() {
		OptionalPersonId optionalPersonId= new OptionalPersonId(Long.valueOf(12345l), new Exception());
		optionalPersonId.setPersonId(Long.valueOf(123456l));
		assertEquals(Long.valueOf(123456l), optionalPersonId.getPersonId());
	}

}
