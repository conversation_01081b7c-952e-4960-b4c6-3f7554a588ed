package com.kronos.people.personality.exception.impl.ia;

import com.kronos.commonbusiness.datatypes.ia.IAColumn;
import com.kronos.commonbusiness.datatypes.ia.IAErrorDetail;
import com.kronos.commonbusiness.datatypes.ia.IARequest;
import com.kronos.container.api.util.IAPIExceptionHandler;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Collections;
import java.util.Locale;

import static com.kronos.people.personality.exception.ExceptionConstants.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonalityExceptionHelperTest {

	private static final String EMP_HIST_HOME_LABOR_CATEGORY = "EMP_HIST_HOME_LABOR_CATEGORY";

	@Mock
	private IAPIExceptionHandler apiExceptionHandler;
	@InjectMocks
	private PersonalityExceptionHelper exceptionHelper;

	@BeforeEach
	public void setUp() {
		when(apiExceptionHandler.getErrorMessage(anyString(), any(Locale.class), any()))
				.thenAnswer(invocationOnMock -> {
					String errorCode = invocationOnMock.getArguments()[0].toString();
					return getLocalizedMessage(errorCode);
				});
	}

	@AfterEach
	public void tearDown() {
		verifyNoMoreInteractions(apiExceptionHandler);
	}

	@Test
	public void buildEmployeeExtensionErrorDetail() {
		IARequest request = new IARequest();
		request.setColumns(Collections.singletonList(new IAColumn(EMP_HIST_HOME_LABOR_CATEGORY, EMP_HIST_HOME_LABOR_CATEGORY)));

		String errorMessage = "Employee extension error message";
		PersonalityErrorCode errorCode = PersonalityErrorCode.NOT_FOUND;
		PersonalityExtensionException extensionException = new PersonalityExtensionException(errorCode, errorMessage);

		when(apiExceptionHandler.getErrorMessage(anyString(), any(), any())).thenReturn("WCO-149101localized");
		IAErrorDetail errorDetailWrapper = exceptionHelper.buildEmployeeExtensionErrorDetail(Collections.singletonList(extensionException), request);

		verify(apiExceptionHandler, times(2)).getErrorMessage(anyString(), any(), any());

		assertEquals(PARTIAL_SUCCESS_ERROR_CODE, errorDetailWrapper.getCode());
		assertEquals("WCO-149101localized", errorDetailWrapper.getMessage());
		assertEquals(1, errorDetailWrapper.getDetails().size());
		IAErrorDetail firstError = errorDetailWrapper.getDetails().iterator().next();
		assertEquals(PERSONALITY_EXCEPTION_ERROR_CODE, firstError.getCode());
		assertEquals("WCO-149101localized", firstError.getMessage());
		assertEquals(EMP_HIST_HOME_LABOR_CATEGORY, firstError.getDetail());
	}

	@Test
	public void buildCommonErrorDetail() {
		IARequest request = new IARequest();
		request.setColumns(Collections.singletonList(new IAColumn(EMP_HIST_HOME_LABOR_CATEGORY, EMP_HIST_HOME_LABOR_CATEGORY)));
		when(apiExceptionHandler.getErrorMessage(anyString(), any(), any())).thenReturn("WCO-101272localized");
		IAErrorDetail errorDetail = exceptionHelper.buildCommonErrorDetail(request);

		verify(apiExceptionHandler, times(1)).getErrorMessage(anyString(), any(), any());

		assertEquals(PRIMARY_JOB_HISTORY_COMMON_ERROR_CODE, errorDetail.getCode());
		assertEquals(EMP_HIST_HOME_LABOR_CATEGORY, errorDetail.getDetail());
		assertEquals("WCO-101272localized", errorDetail.getMessage());
	}

	private String getLocalizedMessage(String key) {
		return key + "localized";
	}
}
