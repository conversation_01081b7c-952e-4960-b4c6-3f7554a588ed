package com.kronos.people.personality.dataaccess.adapter;

import com.kronos.commonapp.labortransfer.api.ILaborTransferService;
import com.kronos.commonapp.labortransfer.model.LaborCategoryItems;
import com.kronos.commonapp.orgmap.setup.model.OrgObjectRef;
import com.kronos.commonapp.orgmap.traversal.api.IOrgMapService;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.model.extension.entry.CostCenterEntry;
import com.kronos.people.personality.model.extension.entry.positions.*;
import com.kronos.people.personality.service.PersonalityExtendedAttributesService;
import com.kronos.wfc.commonapp.laborlevel.business.entries.LaborAccount;
import com.kronos.wfc.commonapp.people.bridge.OrgSet;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.commonapp.people.business.person.wageoverride.WageWorkRuleOverride;
import com.kronos.wfc.commonapp.people.business.person.wageoverride.WageWorkRuleOverrideSet;
import com.kronos.wfc.commonapp.people.business.positions.*;
import com.kronos.wfc.commonapp.people.business.positions.hrpositioncode.HrPositionCode;
import com.kronos.wfc.commonapp.people.business.positions.hrpositioncode.HrPositionCodeSet;
import com.kronos.wfc.commonapp.types.business.EmploymentStatusType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.currency.KCurrency;
import com.kronos.wfc.platform.utility.framework.datetime.KConstants;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.scheduling.core.business.profiles.shifttemplates.ShiftTemplateProfile;
import com.kronos.wfc.timekeeping.accruals.business.AccrualProfile;
import com.kronos.wfc.timekeeping.cascade.business.config.CascadeProfile;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PositionsAdapterHelperMicroTest {

    private static final Long FIRST_ID = 1L;
    private static final ObjectIdLong PERSON_ID = new ObjectIdLong(FIRST_ID);
    private static final Long SECOND_ID = 2L;

    private static final Long SHIFT_TEMPLATE_PROF_ID = 0L;
    private static final Long ACCRUAL_PROFILE_ID = 30L;
    private static final String JOB_NAME = "Job";
    private static final String EMPTY = "Empty";
    private static final String LABOR_CATEGORY = "LA";
    private static final String FIRST_ORG_SET_NAME = "OrgSet1";
    private static final String SECOND_ORG_SET_NAME = "OrgSet2";
    private static final String THIRD_ORG_SET_NAME = "OrgSet3";
    private static final String FOURTH_ORG_SET_NAME = "OrgSet4";
    private static final String GROUP_NAME = "Group1";
    private static final String ACCRUAL_PROFILE_NAME = "Accrual Profile 1";
    private static final KDate EFF_DATE = KDate.createDate(2023,1,1);
    private static final KDate EXP_DATE = KDate.createDate(2023,1,31);

    @InjectMocks
    private PositionsAdapterHelper positionsAdapterHelperMock;

    @Mock
    private PersonalityFacade personalityFacade;

    @Mock
    private ILaborTransferService laborTransferService;

    @Mock
    private IOrgMapService orgMapService;
    
    @Mock
    private PersonalityExtendedAttributesService personalityExtendedAttributesService;

    private final AdapterHelper adapterHelper = new AdapterHelper();

    private MockedStatic<ShiftTemplateProfile> mockedEmployeeStatusType;
    private MockedStatic<CascadeProfile> mockedCascadeProfile;
    private MockedStatic<AccrualProfile> mockedAccrualProfile;

    @BeforeEach
    public void setup() {
        positionsAdapterHelperMock.setAdapterHelper(adapterHelper);
        mockedEmployeeStatusType = Mockito.mockStatic(ShiftTemplateProfile.class);
        mockedCascadeProfile = Mockito.mockStatic(CascadeProfile.class);
        mockedAccrualProfile = Mockito.mockStatic(AccrualProfile.class);
    }

    @AfterEach
    public void teardown(){
        mockedEmployeeStatusType.close();
        mockedCascadeProfile.close();
        mockedAccrualProfile.close();
    }

    @Test
    public void testSetPositionDetailsAttributes() {
        Person person = mock(Person.class);
        String supervisorFullName = "Supervisor";
        when(person.getFullName()).thenReturn(supervisorFullName);
        when(personalityFacade.getPerson(any())).thenReturn(person);

        final String expectedSTPName = "abc";
        ShiftTemplateProfile mockSTP = Mockito.mock(ShiftTemplateProfile.class);
        when(mockSTP.getName()).thenReturn(expectedSTPName);
        when(ShiftTemplateProfile.getShiftTemplateProfile(new ObjectIdLong(SHIFT_TEMPLATE_PROF_ID))).thenReturn(mockSTP);

        final String expectedCascadeProfileName = "Cascade Profile";
        CascadeProfile cascadeProfileMock = Mockito.mock(CascadeProfile.class);
        when(cascadeProfileMock.getName()).thenReturn(expectedCascadeProfileName);
        when(CascadeProfile.retrieveById(new ObjectIdLong(SECOND_ID))).thenReturn(cascadeProfileMock);

        final String expectedAccrualProfileName = "Accrual Profile";
        AccrualProfile accrualProfileMock = Mockito.mock(AccrualProfile.class);
        when(accrualProfileMock.getName()).thenReturn(expectedAccrualProfileName);
        when(AccrualProfile.getAccrualProfile(new ObjectIdLong(ACCRUAL_PROFILE_ID))).thenReturn(accrualProfileMock);

        PositionEntry entry = new PositionEntry();
        KDate hireDate = KDate.today().minusField(KConstants.YEAR, 1).getDate();
        LocalDate expectedHireDate = LocalDate.of(hireDate.getYear(), hireDate.getMonth(), hireDate.getDay());
        KDate seniorityDate = KDate.today().minusField(KConstants.MONTH, 1).getDate();
        LocalDate expectedSeniority = LocalDate
                .of(seniorityDate.getYear(), seniorityDate.getMonth(), seniorityDate.getDay());
        PositionDetails details = createDetails(FIRST_ID, 3L, hireDate, seniorityDate, SHIFT_TEMPLATE_PROF_ID, SECOND_ID,
                ACCRUAL_PROFILE_ID);
        positionsAdapterHelperMock.setPositionDetailsRelatedAttributes(entry, details);
        assertNotNull(entry.getPositionDetails());
        assertEquals(3L, entry.getPositionDetails().getSupervisorPersonId().longValue());
        assertEquals(supervisorFullName, entry.getPositionDetails().getSupervisorFullName());
        assertEquals(expectedHireDate, entry.getPositionDetails().getHireDate());
        assertEquals(expectedSeniority, entry.getPositionDetails().getSeniorityDate());
        assertEquals(expectedSTPName, entry.getPositionDetails().getShiftTemplateProfile());
        assertEquals(expectedCascadeProfileName, entry.getPositionDetails().getCascadingProfile());
    }

    @Test
    public void testSetPositionStatusAttributes() {
        when(personalityFacade.getLegacyPositionStatus(any())).thenCallRealMethod();
        EmploymentStatusType naStatus = mock(EmploymentStatusType.class);
        when(naStatus.getEmploymentStatusTypeId()).thenReturn(new ObjectIdLong(0L));
        EmploymentStatusType activeStatus = mock(EmploymentStatusType.class);
        when(activeStatus.getEmploymentStatusTypeId()).thenReturn(new ObjectIdLong(1L));
        when(personalityFacade.getEmploymentStatusType(any(PositionStatusMapList.class)))
                .thenReturn(naStatus, activeStatus);
        PositionEntry entry = new PositionEntry();
        positionsAdapterHelperMock.setPositionStatusRelatedAttributes(entry, createStatuses());
        assertEquals(createExpectedStatuses(), entry.getEffDatedPositionStatus());
    }

    @Test
    public void testSetPositionJobAccountAttributes() {
        when(laborTransferService.getLaborCategoryItemsFromAccountIdList(any())).thenReturn(createLaborCategoryItems());
        when(orgMapService.resolve(any(OrgObjectRef.class), any())).thenReturn(new OrgObjectRef(3L, JOB_NAME));
        PositionEntry entry = new PositionEntry();
        positionsAdapterHelperMock.setPositionJobRelatedAttributes(entry, createLaborAccounts());
        assertEquals(createExpectedLaborAccounts(), entry.getEffDatedPositionJobAccount());
    }

    @Test
    public void testSetPositionJobTransferAttributes() {
        ObjectIdLong firstId = new ObjectIdLong(1L);
        ObjectIdLong secondId = new ObjectIdLong(2L);
        ObjectIdLong thirdId = new ObjectIdLong(3L);
        ObjectIdLong fourthId = new ObjectIdLong(4L);
        OrgSet firstOrgSet = createOrgSet(firstId, FIRST_ORG_SET_NAME);
        OrgSet secondOrgSet = createOrgSet(secondId, SECOND_ORG_SET_NAME);
        OrgSet thirdOrgSet = createOrgSet(thirdId, THIRD_ORG_SET_NAME);
        OrgSet fourthOrgSet = createOrgSet(fourthId, FOURTH_ORG_SET_NAME);
        when(personalityFacade.findOrgMapGroupById(firstId)).thenReturn(firstOrgSet);
        when(personalityFacade.findOrgMapGroupById(secondId)).thenReturn(secondOrgSet);
        when(personalityFacade.findOrgMapGroupById(thirdId)).thenReturn(thirdOrgSet);
        when(personalityFacade.findOrgMapGroupById(fourthId)).thenReturn(fourthOrgSet);
        PositionEntry entry = new PositionEntry();
        positionsAdapterHelperMock.setPositionJobTransferRelatedAttributes(entry, createAccessAssignments());
        assertEquals(createExpectedAccessAssignments(), entry.getEffDatedPositionJobTransfer());
    }

    @Test
    public void testSetPositionOrderAttributes() {
        IEmployeePosition position = createPosition(FIRST_ID, "Position1");
        PositionOrder order = new PositionOrder(new ObjectIdLong(FIRST_ID));
        order.setOrderNumber(1);
        order.setEffectiveDate(KDate.getNewSotDate());
        order.setExpirationDate(KDate.getEotDate());
        position.setPositionOrder(order);
        PositionEntry entry = new PositionEntry();
        positionsAdapterHelperMock.setPositionOrderRelatedAttributes(entry, position);
        assertEquals(createExpectedOrders(), entry.getEffDatedPositionOrder());
    }

    @Test
    public void testSetPositionWageWorkRuleOverrides() {
        KDate effDate = KDate.createDate();
        KDate expDate = effDate.plusDays(1);
        WageWorkRuleOverrideSet wageWorkRuleOverrideSet = new WageWorkRuleOverrideSet(new ObjectIdLong(PERSON_ID),
                Collections.singletonList(createWageWorkRuleOverride(effDate, expDate, new KCurrency(123.45),
                        new ObjectIdLong(FIRST_ID))));
        PositionEntry entry = new PositionEntry();
        positionsAdapterHelperMock.setPositionWageWorkRuleOverrides(entry, wageWorkRuleOverrideSet);
        assertEquals(1, entry.getEffDatedWageWorkRuleOverrides().size());
        PositionWageWorkRuleOverrideEntry wageWorkRuleOverrideEntry = entry.getEffDatedWageWorkRuleOverrides().iterator().next();
        assertEquals(FIRST_ID, wageWorkRuleOverrideEntry.getBaseWorkRuleId());
        assertEquals(123.45, wageWorkRuleOverrideEntry.getBaseWageRate().doubleValue(), 0);
        assertEquals(LocalDate.now(), wageWorkRuleOverrideEntry.getEffectiveDate());
        assertEquals(LocalDate.now().plusDays(1), wageWorkRuleOverrideEntry.getExpirationDate());
    }

    @Test
    public void testSetPositionWageWorkRuleOverridesNullWageRateAndWorkRuleID() {
        KDate effDate = KDate.createDate();
        KDate expDate = effDate.plusDays(1);
        WageWorkRuleOverrideSet wageWorkRuleOverrideSet = new WageWorkRuleOverrideSet(new ObjectIdLong(PERSON_ID),
                Collections.singletonList(createWageWorkRuleOverride(effDate, expDate, null, null)));
        PositionEntry entry = new PositionEntry();
        positionsAdapterHelperMock.setPositionWageWorkRuleOverrides(entry, wageWorkRuleOverrideSet);
        assertEquals(1, entry.getEffDatedWageWorkRuleOverrides().size());
        PositionWageWorkRuleOverrideEntry wageWorkRuleOverrideEntry = entry.getEffDatedWageWorkRuleOverrides().iterator().next();
        assertNull(wageWorkRuleOverrideEntry.getBaseWorkRuleId());
        assertNull(wageWorkRuleOverrideEntry.getBaseWageRate());
        assertEquals(LocalDate.now(), wageWorkRuleOverrideEntry.getEffectiveDate());
        assertEquals(LocalDate.now().plusDays(1), wageWorkRuleOverrideEntry.getExpirationDate());
    }

    @Test
    public void testSetPositionEmploymentTerms(){
        PositionEmploymentTermAssignmentSet assignmentSet = mock(PositionEmploymentTermAssignmentSet.class);
        PositionEntry entry = new PositionEntry();
        PositionEmploymentTermAssignment firstAssignment = new PositionEmploymentTermAssignment();
        firstAssignment.setScheduleGroupId(new ObjectIdLong(FIRST_ID));
        firstAssignment.setName(GROUP_NAME);
        firstAssignment.setEffectiveDate(EFF_DATE);
        firstAssignment.setExpirationDate(EXP_DATE);
        entry.setSnapShotDate(LocalDate.of(2023,1,10));
        when(assignmentSet.getAllPositionEmploymentTermAssignments())
                .thenReturn(Collections.singletonList(firstAssignment));

        positionsAdapterHelperMock.setPositionEmploymentTerms(entry, assignmentSet);
        List<PositionEmploymentTermAssignmentEntry> collection =
                new ArrayList<>(entry.getEffDatedPositionEmploymentTerms());

        assertEquals(1, collection.size());
        assertEquals(FIRST_ID, collection.get(0).getEmploymentTermId());
        assertEquals(EFF_DATE, KDate.create(Date.from(collection.get(0)
                .getEffectiveDate().atStartOfDay(ZoneId.systemDefault()).toInstant())));
        assertEquals(EXP_DATE, KDate.create(Date.from(collection.get(0)
                .getExpirationDate().atStartOfDay(ZoneId.systemDefault()).toInstant())));
    }

    @Test
    public void testSetPositionEmploymentTerms_whenSnapShotDateAfterExpDate(){
        PositionEmploymentTermAssignmentSet assignmentSet = mock(PositionEmploymentTermAssignmentSet.class);
        PositionEntry entry = new PositionEntry();
        PositionEmploymentTermAssignment firstAssignment = new PositionEmploymentTermAssignment();
        firstAssignment.setScheduleGroupId(new ObjectIdLong(FIRST_ID));
        firstAssignment.setName(GROUP_NAME);
        firstAssignment.setEffectiveDate(EFF_DATE);
        firstAssignment.setExpirationDate(EXP_DATE);
        entry.setSnapShotDate(LocalDate.of(2023,2,10));
        when(assignmentSet.getAllPositionEmploymentTermAssignments())
                .thenReturn(Collections.singletonList(firstAssignment));

        positionsAdapterHelperMock.setPositionEmploymentTerms(entry, assignmentSet);
        List<PositionEmploymentTermAssignmentEntry> collection =
                new ArrayList<>(entry.getEffDatedPositionEmploymentTerms());

        assertEquals(1, collection.size());

    }

    @Test
    public void testTetPositionScheduleGroupAssignments(){
        PositionScheduleGroupAssignmentSet assignmentSet = mock(PositionScheduleGroupAssignmentSet.class);
        PositionEntry entry = new PositionEntry();
        PositionScheduleGroupAssignment firstAssignment = new PositionScheduleGroupAssignment();
        firstAssignment.setScheduleGroupId(new ObjectIdLong(FIRST_ID));
        firstAssignment.setScheduleGroupId(new ObjectIdLong(FIRST_ID));
        firstAssignment.setGroupName(GROUP_NAME);
        firstAssignment.setEffectiveDate(EFF_DATE);
        firstAssignment.setExpirationDate(EXP_DATE);
        entry.setSnapShotDate(LocalDate.of(2023,1,10));
        when(assignmentSet.getAllPositionScheduleGroupAssignments())
                .thenReturn(Collections.singletonList(firstAssignment));

        positionsAdapterHelperMock.setPositionScheduleGroups(entry, assignmentSet);
        List<PositionScheduleGroupAssignmentEntry> collection =
                new ArrayList<>(entry.getPositionScheduleGroupAssignments());

        assertEquals(1, collection.size());
        assertEquals(FIRST_ID, collection.get(0).getGroupId());
        assertEquals(EFF_DATE, KDate.create(Date.from(collection.get(0)
                .getEffectiveDate().atStartOfDay(ZoneId.systemDefault()).toInstant())));
        assertEquals(EXP_DATE, KDate.create(Date.from(collection.get(0)
                .getExpirationDate().atStartOfDay(ZoneId.systemDefault()).toInstant())));
    }

    @Test
    public void testTetPositionScheduleGroupAssignments_whenSnapShotDateAfterExpDate(){
        PositionScheduleGroupAssignmentSet assignmentSet = mock(PositionScheduleGroupAssignmentSet.class);
        PositionEntry entry = new PositionEntry();
        PositionScheduleGroupAssignment firstAssignment = new PositionScheduleGroupAssignment();
        firstAssignment.setScheduleGroupId(new ObjectIdLong(FIRST_ID));
        firstAssignment.setScheduleGroupId(new ObjectIdLong(FIRST_ID));
        firstAssignment.setGroupName(GROUP_NAME);
        firstAssignment.setEffectiveDate(EFF_DATE);
        firstAssignment.setExpirationDate(EXP_DATE);
        entry.setSnapShotDate(LocalDate.of(2023,2,10));
        when(assignmentSet.getAllPositionScheduleGroupAssignments())
                .thenReturn(Collections.singletonList(firstAssignment));

        positionsAdapterHelperMock.setPositionScheduleGroups(entry, assignmentSet);
        List<PositionScheduleGroupAssignmentEntry> collection =
                new ArrayList<>(entry.getPositionScheduleGroupAssignments());

        assertEquals(1, collection.size());

    }

    @Test
    public void testSetPositionPayFromSchedules() {
        KDate today = KDate.today();
        KDate todayPlus5Days = today.plusDays(5);
        KDate todayPlus10Days = today.plusDays(10);
        KDate todayPlus20Days = today.plusDays(20);

        PositionPayFromScheduleAssignmentSet assignmentSet = mock(PositionPayFromScheduleAssignmentSet.class);
        PositionEntry entry = new PositionEntry();
        PositionPayFromScheduleAssignment assignment1 = createPFSAssignment(today, todayPlus5Days);
        PositionPayFromScheduleAssignment assignment2 = createPFSAssignment(todayPlus10Days, todayPlus20Days);

        when(assignmentSet.getAllPositionPayFromScheduleAssignments())
                .thenReturn(Arrays.asList(assignment1, assignment2));

        positionsAdapterHelperMock.setPositionPayFromSchedule(entry, assignmentSet);
        List<PositionPayFromScheduleAssignmentEntry> entities =
                new ArrayList<>(entry.getEffDatedPositionPayFromSchedules());

        assertEquals(5, entities.size());
        verifyPFSAssignment(createDefaultPFSAssignment(KDate.getNewSotDate()), entities.get(0));
        verifyPFSAssignment(assignment1, entities.get(1));
        verifyPFSAssignment(createDefaultPFSAssignment(todayPlus5Days), entities.get(2));
        verifyPFSAssignment(assignment2, entities.get(3));
        verifyPFSAssignment(createDefaultPFSAssignment(todayPlus20Days), entities.get(4));
    }

    @Test
    public void testSetPositionPayFromSchedulesEmptyList() {
        PositionPayFromScheduleAssignmentSet assignmentSet = mock(PositionPayFromScheduleAssignmentSet.class);
        PositionEntry entry = new PositionEntry();
        when(assignmentSet.getAllPositionPayFromScheduleAssignments()).thenReturn(Collections.emptyList());

        positionsAdapterHelperMock.setPositionPayFromSchedule(entry, assignmentSet);
        List<PositionPayFromScheduleAssignmentEntry> entities =
                new ArrayList<>(entry.getEffDatedPositionPayFromSchedules());

        assertEquals(1, entities.size());
        verifyPFSAssignment(createDefaultPFSAssignment(KDate.getNewSotDate()), entities.get(0));
    }

    @Test
    public void testSetPositionFullTimeEquivalencies() {
        PositionFullTimeEquivalencySet set = mock(PositionFullTimeEquivalencySet.class);
        PositionEntry entry = new PositionEntry();
        KDate effectiveDate1 = KDate.create(2023, 11, 1);
        KDate effectiveDate2 = KDate.create(2023, 11, 20);

        List<PositionFullTimeEquivalency> list = new ArrayList<>();
        PositionFullTimeEquivalency fte1 = new PositionFullTimeEquivalency();
        fte1.setFullTimeEquivalencyPercent(10.0);
        fte1.setEffectiveDate(effectiveDate1);
        list.add(fte1);
        PositionFullTimeEquivalency fte2 = new PositionFullTimeEquivalency();
        fte2.setFullTimeStandardHoursQuantity(40.0);
        fte2.setEmployeeStandardHoursQuantity(25.0);
        fte2.setEffectiveDate(effectiveDate2);
        list.add(fte2);
        when(set.getAllPositionFullTimeEquivalencies()).thenReturn(list);

        positionsAdapterHelperMock.setPositionFullTimeEquivalencies(entry, set);
        List<PositionFullTimeEquivalencyEntry> entryList = new ArrayList<>(entry.getPositionFullTimeEquivalencies());
        assertEquals(2, entryList.size());
        assertEquals(Double.valueOf(10), entryList.get(0).getFullTimeEquivalencyPercent());
        assertEquals(LocalDate.of(2023, 11, 1), entryList.get(0).getEffectiveDate());
        assertEquals(Double.valueOf(40), entryList.get(1).getFullTimeStandardHoursQuantity());
        assertEquals(Double.valueOf(25), entryList.get(1).getEmployeeStandardHoursQuantity());
        assertEquals(LocalDate.of(2023, 11, 20), entryList.get(1).getEffectiveDate());
    }

    public void testSetPositionAccrualProfiles(){
        PositionDirectAccrualProfileAssignmentSet assignmentSet = mock(PositionDirectAccrualProfileAssignmentSet.class);
        PositionEntry entry = new PositionEntry();
        PositionAccrualProfileAssignment firstAssignment = new PositionAccrualProfileAssignment();
        firstAssignment.setAccrualProfileId(new ObjectIdLong(FIRST_ID));
        firstAssignment.setName(ACCRUAL_PROFILE_NAME);
        firstAssignment.setEffectiveDate(EFF_DATE);
        firstAssignment.setExpirationDate(EXP_DATE);
        entry.setSnapShotDate(LocalDate.of(2023,1,10));
        when(assignmentSet.getAllPositionAccrualProfileAssignments())
                .thenReturn(Collections.singletonList(firstAssignment));

        positionsAdapterHelperMock.setPositionAccrualProfiles(entry, assignmentSet);
        List<PositionAccrualProfileAssignmentEntry> collection =
                new ArrayList<>(entry.getPositionAccrualProfileAssignments());

        assertEquals(1, collection.size());
        assertEquals(FIRST_ID, collection.get(0).getAccrualProfileId());
        assertEquals(ACCRUAL_PROFILE_NAME, collection.get(0).getAccrualProfileName());
        assertEquals(EFF_DATE, KDate.create(Date.from(collection.get(0)
                .getEffectiveDate().atStartOfDay(ZoneId.systemDefault()).toInstant())));
        assertEquals(EXP_DATE, KDate.create(Date.from(collection.get(0)
                .getExpirationDate().atStartOfDay(ZoneId.systemDefault()).toInstant())));
    }

    @Test
    public void testSetHrPositionCodes() {
        PositionEntry positionEntry = new PositionEntry();
        HrPositionCodeSet hrPositionCodeSet = new HrPositionCodeSet();
        List<HrPositionCode> hrPositionCodeList = new ArrayList<>();
        HrPositionCode hrPositionCode = new HrPositionCode();
        hrPositionCode.setPositionCodeId(new ObjectIdLong(1));
        hrPositionCode.setHrPositionCodeName("abc");
        hrPositionCode.setEffectiveDate(new KDate(2023, 06, 22));
        hrPositionCode.setExpirationDate(new KDate(2023, 07, 21));
        hrPositionCodeList.add(hrPositionCode);
        hrPositionCodeSet.setHrPositionCodes(hrPositionCodeList);
        positionsAdapterHelperMock.setHrPositionCodes(positionEntry, hrPositionCodeSet);
        assertEquals(1, positionEntry.getHrPositionCodeBean().size());


    }

    private List<LaborCategoryItems> createLaborCategoryItems() {
        LaborCategoryItems empty = new LaborCategoryItems();
        empty.setLaborAccountId(-2L);
        empty.setLaborAccountString(EMPTY);
        empty.setLaborCategoryString(EMPTY);

        LaborCategoryItems laborCategoryItems = new LaborCategoryItems();
        laborCategoryItems.setLaborAccountId(1L);
        laborCategoryItems.setLaborAccountString(LABOR_CATEGORY);
        laborCategoryItems.setLaborCategoryString(LABOR_CATEGORY);
        return Arrays.asList(empty, laborCategoryItems);
    }

    private PositionStatusSet createStatuses() {
        PositionStatus positionStatus1 = createPositionStatus(FIRST_ID, EmploymentStatusType.NOT_APPLICABLE,
                KDate.getSotDate(), KDate.createDate(2019, 10, 15), 1L);
        PositionStatus positionStatus2 = createPositionStatus(FIRST_ID, EmploymentStatusType.ACTIVE,
                KDate.create(2019, 10, 15), KDate.getEotDate(), 1L);
        return new PositionStatusSet(Arrays.asList(positionStatus1, positionStatus2), new ObjectIdLong(FIRST_ID),
                new ObjectIdLong(FIRST_ID));
    }

    private Collection<PositionStatusEntry> createExpectedStatuses() {
        PositionStatusEntry entry1 = new PositionStatusEntry();
        entry1.setPositionStatusTypeId(0L);
        adapterHelper.setDates(entry1, KDate.getSotDate(), KDate.createDate(2019, 10, 15));

        PositionStatusEntry entry2 = new PositionStatusEntry();
        entry2.setPositionStatusTypeId(1L);
        adapterHelper.setDates(entry2, KDate.create(2019, 10, 15), KDate.getEotDate());
        return Arrays.asList(entry1, entry2);
    }

    private WageWorkRuleOverride createWageWorkRuleOverride(KDate effDate, KDate expDate,
                                                            KCurrency baseWageRate, ObjectIdLong baseWorkRuleId) {
        return new WageWorkRuleOverride(new ObjectIdLong(FIRST_ID), new ObjectIdLong(PERSON_ID), new ObjectIdLong(FIRST_ID),
                effDate, expDate, null, FIRST_ID, baseWageRate,
                baseWorkRuleId);
    }

    private PositionLaborAccountSet createLaborAccounts() {
        PositionLaborAccount laborAccount1 = createPositionLaborAccount(FIRST_ID, LaborAccount.UNSPECIFIED_ID.toLong(),
                null, KDate.getSotDate(), KDate.createDate(2019, 11, 15));
        PositionLaborAccount laborAccount2 = createPositionLaborAccount(FIRST_ID, 1L, 3L,
                KDate.createDate(2019, 11, 15), KDate.getEotDate());
        return new PositionLaborAccountSet(Arrays.asList(laborAccount1, laborAccount2), PERSON_ID);
    }

    private Collection<PositionJobAccountEntry> createExpectedLaborAccounts() {
        PositionJobAccountEntry entry1 = new PositionJobAccountEntry();
        List<CostCenterEntry> costCenterHistoryList=new ArrayList<>();
    	CostCenterEntry costCenterHistory=new CostCenterEntry();
    	costCenterHistory.setCostCenterId(1L);
    	costCenterHistory.setEffectiveDate(LocalDate.of(2020,1,4));
    	costCenterHistory.setExpirationDate(LocalDate.of(2020,11,4));
    	costCenterHistoryList.add(costCenterHistory);
        entry1.setOrganizationId(null);
        entry1.setJob(null);
        entry1.setLaborAccountId(-2L);
        entry1.setLaborAccount(EMPTY);
        entry1.setLaborCategory(EMPTY);
        entry1.setVersionCount(1L);
        entry1.setEffDatedCostCenterEntries(costCenterHistoryList);
        adapterHelper.setDates(entry1, KDate.getSotDate(), KDate.createDate(2019, 11, 15));

        PositionJobAccountEntry entry2 = new PositionJobAccountEntry();
        List<CostCenterEntry> costCenterHistoryList2=new ArrayList<>();
        CostCenterEntry costCenterHistory2=new CostCenterEntry();
    	costCenterHistory2.setCostCenterId(1L);
    	costCenterHistory2.setEffectiveDate(LocalDate.of(2020,1,4));
    	costCenterHistory2.setExpirationDate(LocalDate.of(2020,11,4));
    	costCenterHistoryList.add(costCenterHistory2);
        entry2.setOrganizationId(3L);
        entry2.setJob(JOB_NAME);
        entry2.setLaborAccountId(1L);
        entry2.setLaborAccount(LABOR_CATEGORY);
        entry2.setLaborCategory(LABOR_CATEGORY);
        entry2.setVersionCount(1L);
        entry2.setEffDatedCostCenterEntries(costCenterHistoryList2);
        adapterHelper.setDates(entry2, KDate.createDate(2019, 11, 15), KDate.getEotDate());
        return Arrays.asList(entry1, entry2);
    }

    private PositionPayFromScheduleAssignment createPFSAssignment(KDate effectiveDate, KDate expirationDate) {
        PositionPayFromScheduleAssignment assignment = new PositionPayFromScheduleAssignment();
        assignment.setOverridePFS(true);
        assignment.setPayShiftFromSchedule(true);
        assignment.setNoncancellingTagId(new ObjectIdLong(SECOND_ID));
        assignment.setNoncancellingTagName(EMPTY);
        assignment.setPayEditsFromSchedule(true);
        assignment.setCancelPFSOnHolidays(true);
        assignment.setEffectiveDate(effectiveDate);
        assignment.setExpirationDate(expirationDate);
        return assignment;
    }

    private PositionPayFromScheduleAssignment createDefaultPFSAssignment(KDate effectiveDate) {
        PositionPayFromScheduleAssignment assignment = new PositionPayFromScheduleAssignment();
        assignment.setOverridePFS(false);
        assignment.setPayShiftFromSchedule(false);
        assignment.setPayEditsFromSchedule(false);
        assignment.setCancelPFSOnHolidays(false);
        assignment.setEffectiveDate(effectiveDate);
        return assignment;
    }

    private void verifyPFSAssignment(PositionPayFromScheduleAssignment assignment,
                                     PositionPayFromScheduleAssignmentEntry entry) {
        assertEquals(assignment.getOverridePFS(), entry.getOverridePayRule());
        assertEquals(assignment.getPayShiftFromSchedule(), entry.getPayShiftFromSchedule());
        assertEquals(assignment.getPayEditsFromSchedule(), entry.getPayEditsFromSchedule());
        assertEquals(assignment.getCancelPFSOnHolidays(), entry.getCancelPFSOnHolidays());
        ObjectIdLong paycodeTag = assignment.getNoncancellingTagId();
        if (Objects.nonNull(paycodeTag) && !paycodeTag.isNull()) {
            assertEquals(assignment.getNoncancellingTagId().toLong(), entry.getPaycodeTag().getId());
            assertEquals(assignment.getNoncancellingTagName(), entry.getPaycodeTag().getQualifier());
        } else {
            assertNull(entry.getPaycodeTag());
        }
        KDate effectiveDate = assignment.getEffectiveDate();
        LocalDate assignmentEffectiveDate = LocalDate.of(effectiveDate.getYear(), effectiveDate.getMonth(),
                effectiveDate.getDay());
        assertEquals(entry.getEffectiveDate(), assignmentEffectiveDate);
    }

    private PositionAccessAssignmentSet createAccessAssignments() {
        PositionAccessAssignment accessAssignment1 = createPositionAccessAssignment(SECOND_ID, -2L, 1L, 2L, 3L,
                KDate.getSotDate(), KDate.createDate(2019, 10, 15));
        PositionAccessAssignment accessAssignment2 = createPositionAccessAssignment(SECOND_ID, 3L, 2L, 1L, 4L,
                KDate.createDate(2019, 10, 15), KDate.getEotDate());
        return new PositionAccessAssignmentSet(Arrays.asList(accessAssignment1, accessAssignment2));
    }

    private Collection<PositionJobTransferEntry> createExpectedAccessAssignments() {
        PositionJobTransferEntry entry1 = new PositionJobTransferEntry();
        entry1.setManagerJobTransferSet(FIRST_ORG_SET_NAME);
        entry1.setEmployeeJobTransferSet(SECOND_ORG_SET_NAME);
        entry1.setEmpManagerJobTransferSet(THIRD_ORG_SET_NAME);
        entry1.setManagerAccessOrganizationSetId(-2L);
        entry1.setManagerTransferOrganizationSetId(1L);
        entry1.setProfessionalTransferOrganizationSetId(2L);
        entry1.setEmpMgrTransferOrganizationSetId(3L);
        adapterHelper.setDates(entry1, KDate.getSotDate(), KDate.createDate(2019, 10, 15));

        PositionJobTransferEntry entry2 = new PositionJobTransferEntry();
        entry2.setManagerJobTransferSet(SECOND_ORG_SET_NAME);
        entry2.setEmployeeJobTransferSet(FIRST_ORG_SET_NAME);
        entry2.setEmpManagerJobTransferSet(FOURTH_ORG_SET_NAME);
        entry2.setManagerAccessOrganizationSetId(-2L);
        entry2.setManagerTransferOrganizationSetId(2L);
        entry2.setProfessionalTransferOrganizationSetId(1L);
        entry2.setEmpMgrTransferOrganizationSetId(4L);
        adapterHelper.setDates(entry2, KDate.createDate(2019, 10, 15), KDate.getEotDate());
        return Arrays.asList(entry1, entry2);
    }

    private Collection<PositionOrderEntry> createExpectedOrders() {
        PositionOrderEntry entry = new PositionOrderEntry();
        entry.setPrimary(true);
        entry.setOrderNumber(1);
        adapterHelper.setDates(entry, KDate.getNewSotDate(), KDate.getEotDate());
        return Collections.singletonList(entry);
    }

    private PositionCustomDateSet createCustomDates() {
        PositionCustomDate customDate1 = createPositionCustomDate(FIRST_ID, FIRST_ID, KDate.createDate(2019, 11, 17));
        PositionCustomDate customDate2 = createPositionCustomDate(SECOND_ID, SECOND_ID, KDate.createDate(2019, 11, 19));
        PositionCustomDateSet positionCustomDateSet = new PositionCustomDateSet();
        positionCustomDateSet.addAll(Arrays.asList(customDate1, customDate2));
        return positionCustomDateSet;
    }

    private Collection<PositionDatesEntry> createExpectedCustomDates() {
        PositionDatesEntry entry1 = new PositionDatesEntry();
        entry1.setCustomDateTypeId(FIRST_ID);
        entry1.setDefaultDate(LocalDate.of(2019, 11, 1).toString());
        entry1.setDescription("CustomDate1");
        entry1.setOverrideDate(LocalDate.of(2019, 11, 17).toString());

        PositionDatesEntry entry2 = new PositionDatesEntry();
        entry2.setCustomDateTypeId(SECOND_ID);
        entry2.setDefaultDate(LocalDate.of(2019, 11, 1).toString());
        entry2.setDescription("CustomDate2");
        entry2.setOverrideDate(LocalDate.of(2019, 11, 19).toString());
        return Arrays.asList(entry1, entry2);
    }

    private PositionCustomDataSet createCustomData() {
        PositionCustomData customData1 = createPositionCustomData(FIRST_ID, FIRST_ID, "Added data 1");
        PositionCustomData customData2 = createPositionCustomData(SECOND_ID, SECOND_ID, "Added data 2");
        PositionCustomDataSet positionCustomDataSet = new PositionCustomDataSet();
        positionCustomDataSet.addAll(Arrays.asList(customData1, customData2));
        return positionCustomDataSet;
    }

    private Collection<PositionCustomDataEntry> createExpectedCustomData() {
        PositionCustomDataEntry entry1 = new PositionCustomDataEntry();
        entry1.setCustomDataTypeId(FIRST_ID);
        entry1.setCustomText("Text1");
        entry1.setVersionCount(1L);

        PositionCustomDataEntry entry2 = new PositionCustomDataEntry();
        entry2.setCustomDataTypeId(SECOND_ID);
        entry2.setCustomText("Text2");
        entry2.setVersionCount(1L);
        return Arrays.asList(entry1, entry2);
    }

    private IEmployeePosition createPosition(Long id, String name) {
        Position position = new Position(new ObjectIdLong(FIRST_ID));
        position.setPositionId(new ObjectIdLong(id));
        position.setName(name);
        position.setExempt(false);
        return position;
    }

    private PositionDetails createDetails(Long positionId, Long supervisorId, KDate hireDate,
                                          KDate seniorityDate, Long shiftTemplateProfileId, Long cascadingProfileId,
                                          Long accrualProfileId) {
        PositionDetails positionDetails = new PositionDetails();
        positionDetails.setPositionDetails(null, positionId, supervisorId, hireDate, seniorityDate, shiftTemplateProfileId,
                cascadingProfileId, accrualProfileId);
        return positionDetails;
    }

    private PositionStatus createPositionStatus(Long positionId, ObjectIdLong status, KDate start, KDate end,
                                                Long processed) {
        PositionStatus positionStatus3 = new PositionStatus();
        PositionStatusContext context = PositionStatusContext.Builder.positionStatusContext()
                .setPositionStatusId(null).setPositionId(positionId).setStatusId(status.toLong()).setEffectiveDate(start)
                .setExpirationDate(end).setProcessedSwitch(processed)
                .setUpdateDtm(null).setUpdateUser(FIRST_ID).setVersionCount(1L)
                .build();
        positionStatus3.setPositionStatus(context);
        return positionStatus3;
    }

    private PositionLaborAccount createPositionLaborAccount(Long positionId, Long laborCategoryId, Long orgId,
                                                            KDate effectiveDate, KDate expirationDate) {
        PositionLaborAccount laborAccount = new PositionLaborAccount();
        PositionLaborAccountContext context = PositionLaborAccountContext.Builder.positionLaborAccountContext()
                .setPositionLaborAccountId(null).setPositionId(positionId).setLaborCategoryId(laborCategoryId)
                .setPrimaryOrganizationId(orgId).setEffectiveDate(effectiveDate).setExpirationDate(expirationDate)
                .setUpdateDtm(null).setUpdateByUserId(FIRST_ID).setVersion(1L)
                .build();
        laborAccount.setPositionLaborAccount(context);
        return laborAccount;
    }

    private PositionAccessAssignment createPositionAccessAssignment(Long positionId, Long employeeGroupId,
                                                                    Long managerTransferSetId,
                                                                    Long employeeTransferSetId,
                                                                    Long empMgrTransferSetId,
                                                                    KDate effectiveDate, KDate expirationDate) {
        PositionAccessAssignment accessAssignment = spy(new PositionAccessAssignment());
        PositionAccessAssignmentContext context = PositionAccessAssignmentContext.Builder
                .positionAccessAssignmentContext().setPositionAccessAssignmentId(null).setPositionId(positionId)
                .setManagerTransferOrganizationSetId(managerTransferSetId)
                .setProfessionalTransferOrganizationSetId(employeeTransferSetId).setEmpMgrTransferOrganizationSetId(empMgrTransferSetId)
                .setEffectiveDate(effectiveDate).setExpirationDate(expirationDate)
                .build();
        accessAssignment.setPositionAccessAssignment(context);
        ObjectIdLong emptyId = new ObjectIdLong(-2L);
        doReturn(emptyId).when(accessAssignment).getManagerAccessOrganizationSetId();
        return accessAssignment;
    }

    private PositionCustomDate createPositionCustomDate(Long customDateTypeId, Long positionId,
                                                        KDate actualDate) {
        PositionCustomDate customDate = new PositionCustomDate();
        customDate.setPositionCustomDate(customDateTypeId, positionId, actualDate);
        return customDate;
    }

    private PositionCustomData createPositionCustomData(Long customDataTypeId, Long positionId, String text) {
        PositionCustomData customData = new PositionCustomData();
        customData.setPositionCustomData(customDataTypeId, positionId, text,null, FIRST_ID, 1L);
        return customData;
    }

    private OrgSet createOrgSet(ObjectIdLong firstId, String firstOrgSetName) {
        OrgSet firstOrgSet = new OrgSet();
        firstOrgSet.setId(firstId);
        firstOrgSet.setName(firstOrgSetName);
        return firstOrgSet;
    }
}
