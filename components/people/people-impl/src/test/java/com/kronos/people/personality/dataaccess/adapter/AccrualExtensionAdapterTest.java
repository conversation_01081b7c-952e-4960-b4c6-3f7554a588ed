package com.kronos.people.personality.dataaccess.adapter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;

import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.model.extension.AccrualExtension;
import com.kronos.people.personality.model.extension.entry.AccrualProfilesEntry;
import com.kronos.people.personality.model.extension.entry.FullTimeEquivalencyEntry;
import com.kronos.wfc.commonapp.people.business.person.accrualprofileassignment.AccrualProfileAssignment;
import com.kronos.wfc.commonapp.people.business.person.accrualprofileassignment.DirectAccrualProfileAssignmentSet;
import com.kronos.wfc.commonapp.people.business.person.fulltimeequivalency.FullTimeEquivalency;
import com.kronos.wfc.commonapp.people.business.person.fulltimeequivalency.FullTimeEquivalencySet;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class AccrualExtensionAdapterTest {

	private AccrualExtensionAdapter accrualExtensionAdapter;

	private Personality personality;

	AdapterHelper adapterHelper;

	PersonalityFacade personalityFacade;

	@BeforeEach
	public void initialize() {
		accrualExtensionAdapter = new AccrualExtensionAdapter();
		personality = mock(Personality.class);
		personalityFacade = mock(PersonalityFacade.class);
		accrualExtensionAdapter.setAdapterHelper(new AdapterHelper());
		accrualExtensionAdapter.setPersonalityFacade(personalityFacade);
	}

	@Test
	public void datedAccuralProfileEntryTest() {
		AccrualProfilesEntry accrualProfilesEntry = accrualExtensionAdapter.getDatedAccuralProfileEntry(accrualProfileAssignmentData3());

		assertEquals(Long.valueOf(123), accrualProfilesEntry.getAccuralProfileAssignmentId());
		assertEquals(LocalDate.of(2015, 8, 25), accrualProfilesEntry.getEffectiveDate());

	}

	@Test
	public void datedLegacyAccuralProfilesTest() {
		DirectAccrualProfileAssignmentSet directAccrualProfileAssignmentSet = mock(DirectAccrualProfileAssignmentSet.class);

		class MockAccuralProfileAssignment extends AccrualProfileAssignment {
			/**
			 * 
			 */
			private static final long serialVersionUID = 1L;
			@SuppressWarnings("unused")
			String myData;

			public MockAccuralProfileAssignment(String myData) {
				// TODO Auto-generated constructor stub
				this.myData = myData;
			}

		}
		List<AccrualProfileAssignment> list = new ArrayList<AccrualProfileAssignment>();
		list.add(new MockAccuralProfileAssignment("1"));
		list.add(new MockAccuralProfileAssignment("2"));
		list.add(new MockAccuralProfileAssignment("3"));

		when(directAccrualProfileAssignmentSet.getAllAccrualProfileAssignments()).thenReturn(list);

		assertEquals(list, accrualExtensionAdapter.getDatedLegacyAccuralProfiles(directAccrualProfileAssignmentSet));

	}

	@Test
	public void datedFullTimeEquivalencyEntryTest() {

		FullTimeEquivalencyEntry fullTimeEquivalencyEntry = accrualExtensionAdapter.getDatedFullTimeEquivalencyEntry(fullTimeEquivalencyData1());

		assertEquals(LocalDate.of(2015, 8, 25), fullTimeEquivalencyEntry.getEffectiveDate());
		assertEquals(LocalDate.of(2015, 9, 26), fullTimeEquivalencyEntry.getExpirationDate());
		assertEquals(Long.valueOf(123), fullTimeEquivalencyEntry.getFullTimeEquivalencyId());
		assertEquals(Double.valueOf(12.34), fullTimeEquivalencyEntry.getFullTimeEquivalencyPercent());
		assertEquals(Double.valueOf(12.35), fullTimeEquivalencyEntry.getFullTimeStandardHoursQuantity());
		assertEquals(Double.valueOf(12.36), fullTimeEquivalencyEntry.getEmployeeStandardHoursQuantity());
	}

	private FullTimeEquivalencySet fullTimeEquivalencySetData() {
		FullTimeEquivalencySet fullTimeEquivalencySet = mock(FullTimeEquivalencySet.class);
		List<FullTimeEquivalency> fullTimeEquivalenciesList = new ArrayList<FullTimeEquivalency>();
		fullTimeEquivalenciesList.add(fullTimeEquivalencyData1());
		fullTimeEquivalenciesList.add(fullTimeEquivalencyData2());
		when(fullTimeEquivalencySet.getAllMembers()).thenReturn(fullTimeEquivalenciesList);
		return fullTimeEquivalencySet;
	}

	@Test
	public void datedFullTimeEquivalencyTest() {

		Collection<FullTimeEquivalencyEntry> collection = accrualExtensionAdapter.getDatedFullTimeEquivalency(fullTimeEquivalencySetData());
		int i = 0;
		for (FullTimeEquivalencyEntry e : collection) {
			if (i == 0) {
				assertEquals(e.getEffectiveDate(), LocalDate.of(2015, 8, 25));
				assertEquals(e.getExpirationDate(), LocalDate.of(2015, 9, 26));
				assertEquals(e.getFullTimeEquivalencyId(), Long.valueOf(123));
				assertEquals(e.getFullTimeEquivalencyPercent(), Double.valueOf(12.34));
				assertEquals(e.getFullTimeStandardHoursQuantity(), Double.valueOf(12.35));
				assertEquals(e.getEmployeeStandardHoursQuantity(), Double.valueOf(12.36));

			}
			if (i == 1) {
				assertEquals(e.getEffectiveDate(), LocalDate.of(2015, 8, 26));
				assertEquals(e.getExpirationDate(), LocalDate.of(2015, 9, 27));
				assertEquals(e.getFullTimeEquivalencyId(), Long.valueOf(1234));
				assertEquals(e.getFullTimeEquivalencyPercent(), Double.valueOf(12.36));
				assertEquals(e.getFullTimeStandardHoursQuantity(), Double.valueOf(12.37));
				assertEquals(e.getEmployeeStandardHoursQuantity(), Double.valueOf(12.38));

			}
			i++;
		}

	}

	@Test
	public void datedAccrualProfileEntriesTest() {

		Collection<AccrualProfilesEntry> collection = accrualExtensionAdapter.getDatedAccrualProfileEntries(directAccrualProfileAssignmentSetData());

		int i = 0;
		for (AccrualProfilesEntry ac : collection) {
			if (i == 0) {
				assertEquals(ac.getAccuralProfileAssignmentId(), Long.valueOf(123));
				assertEquals(ac.getEffectiveDate(), LocalDate.of(2015, 8, 25));
				assertEquals(ac.getExpirationDate(), LocalDate.of(2015, 9, 26));
			}
			if (i == 1) {
				assertEquals(ac.getAccuralProfileAssignmentId(), Long.valueOf(12));
				assertEquals(ac.getEffectiveDate(), LocalDate.of(2015, 8, 26));
				assertEquals(ac.getExpirationDate(), LocalDate.of(2015, 9, 27));
			}
			i++;
		}

	}

	@Test
	public void convertEffDatedAccrualProfilesTest() {

		directAccrualProfileAssignmentSetData();

		DirectAccrualProfileAssignmentSet aSet = mock(DirectAccrualProfileAssignmentSet.class);
		when(personalityFacade.getDirectAccrualProfileAssignment(any())).thenReturn(aSet);
//
//		when(personality.getJobAssignmentId()).thenReturn(new ObjectIdLong(123456l));

		AccrualExtension accrualExtension = new AccrualExtension();

		accrualExtensionAdapter.convertEffDatedAccrualProfiles(accrualExtension, new ObjectIdLong(123456l));

		Collection<AccrualProfilesEntry> accrualProfilesEntriesCollection = accrualExtension.getAccuralProfiles();

		int i = 0;
		for (AccrualProfilesEntry ac : accrualProfilesEntriesCollection) {
			if (i == 0) {
				assertEquals(ac.getAccuralProfileAssignmentId(), Long.valueOf(123));
				assertEquals(ac.getEffectiveDate(), LocalDate.of(2015, 8, 25));
				assertEquals(ac.getExpirationDate(), LocalDate.of(2015, 9, 26));
			}
			if (i == 1) {
				assertEquals(ac.getAccuralProfileAssignmentId(), Long.valueOf(12));
				assertEquals(ac.getEffectiveDate(), LocalDate.of(2015, 8, 26));
				assertEquals(ac.getExpirationDate(), LocalDate.of(2015, 9, 27));
			}
			i++;
		}
	}

	@Test
	public void convertFullTimeEquivalencyTest() {

		FullTimeEquivalencySet fullTimeEquivalencySet = fullTimeEquivalencySetData();

//		when(personality.getFullTimeEquivalencySet()).thenReturn(fullTimeEquivalencySet);

		AccrualExtension accrualExtension = new AccrualExtension();

		accrualExtensionAdapter.convertFullTimeEquivalency(accrualExtension, fullTimeEquivalencySet);

		Collection<FullTimeEquivalencyEntry> fullTimeEquivalencyEntryCollection = accrualExtension.getFullTimeEquivalency();

		int i = 0;
		for (FullTimeEquivalencyEntry e : fullTimeEquivalencyEntryCollection) {
			if (i == 0) {
				assertEquals(e.getEffectiveDate(), LocalDate.of(2015, 8, 25));
				assertEquals(e.getExpirationDate(), LocalDate.of(2015, 9, 26));
				assertEquals(e.getFullTimeEquivalencyId(), Long.valueOf(123));
				assertEquals(e.getFullTimeEquivalencyPercent(), Double.valueOf(12.34));
				assertEquals(e.getFullTimeStandardHoursQuantity(), Double.valueOf(12.35));
				assertEquals(e.getEmployeeStandardHoursQuantity(), Double.valueOf(12.36));
				assertEquals(Long.valueOf(1L), e.getVersionCount());
			}
			if (i == 1) {
				assertEquals(e.getEffectiveDate(), LocalDate.of(2015, 8, 26));
				assertEquals(e.getExpirationDate(), LocalDate.of(2015, 9, 27));
				assertEquals(e.getFullTimeEquivalencyId(), Long.valueOf(1234));
				assertEquals(e.getFullTimeEquivalencyPercent(), Double.valueOf(12.36));
				assertEquals(e.getFullTimeStandardHoursQuantity(), Double.valueOf(12.37));
				assertEquals(e.getEmployeeStandardHoursQuantity(), Double.valueOf(12.38));
				assertEquals(Long.valueOf(2L), e.getVersionCount());
			}
			i++;
		}
	}

	@Test
	public void convertTest() {

		accrualExtensionAdapter.setAdapterHelper(new AdapterHelper());
		AccrualExtension act = accrualExtensionAdapter.convert(mock(Personality.class) ,null);
		assertNotNull(act);

	}

	@Test
	public void testCreateSnapshot() {
		AccrualExtension expectedExtension = new AccrualExtension();
		expectedExtension.setAccuralProfiles(new EffectiveDatedCollection<>());
		expectedExtension.setFullTimeEquivalency(new EffectiveDatedCollection<>());
		AccrualExtension actualExtension = accrualExtensionAdapter.createSnapshot(expectedExtension);
		assertEquals(expectedExtension, actualExtension);
		assertNotSame(expectedExtension, actualExtension);
	}

	@Test
	public void testCreateSnapshotWithNull() {
		AccrualExtension actualExtension = accrualExtensionAdapter.createSnapshot(null);
		assertNull(actualExtension);
	}

	private AccrualProfileAssignment accrualProfileAssignmentData1() {
		AccrualProfileAssignment accrualProfileAssignment = mock(AccrualProfileAssignment.class);
//		when(accrualProfileAssignment.getAccrualProfileName()).thenReturn("Amit");
//		when(accrualProfileAssignment.getAccrualProfileAssignmentId()).thenReturn(new ObjectIdLong(123));
//		when(accrualProfileAssignment.getEffectiveDate()).thenReturn(KDate.createDate(2015, 8, 25));
//		when(accrualProfileAssignment.getExpirationDate()).thenReturn(KDate.createDate(2015, 9, 26));
		return accrualProfileAssignment;
	}

	private AccrualProfileAssignment accrualProfileAssignmentData2() {
		AccrualProfileAssignment accrualProfileAssignment = mock(AccrualProfileAssignment.class);
//		when(accrualProfileAssignment.getAccrualProfileAssignmentId()).thenReturn(new ObjectIdLong("12"));
//		when(accrualProfileAssignment.getEffectiveDate()).thenReturn(KDate.createDate(2015, 8, 26));
//		when(accrualProfileAssignment.getExpirationDate()).thenReturn(KDate.createDate(2015, 9, 27));
		return accrualProfileAssignment;
	}

	private AccrualProfileAssignment accrualProfileAssignmentData3() {
		AccrualProfileAssignment accrualProfileAssignment = mock(AccrualProfileAssignment.class);
		when(accrualProfileAssignment.getAccrualProfileAssignmentId()).thenReturn(new ObjectIdLong(123));
		when(accrualProfileAssignment.getEffectiveDate()).thenReturn(KDate.createDate(2015, 8, 25));
		return accrualProfileAssignment;
	}

	private DirectAccrualProfileAssignmentSet directAccrualProfileAssignmentSetData() {

		List<AccrualProfileAssignment> list = new ArrayList<AccrualProfileAssignment>();
		list.add(accrualProfileAssignmentData1());
		list.add(accrualProfileAssignmentData2());
		DirectAccrualProfileAssignmentSet aSet = mock(DirectAccrualProfileAssignmentSet.class);
//		when(aSet.getAllAccrualProfileAssignments()).thenReturn(list);
//		when(personalityFacade.getDirectAccrualProfileAssignment(any())).thenReturn(aSet);
		return aSet;

	}

	private FullTimeEquivalency fullTimeEquivalencyData1() {
		FullTimeEquivalency fullTimeEquivalency = mock(FullTimeEquivalency.class);

		when(fullTimeEquivalency.getEffectiveDate()).thenReturn(KDate.createDate(2015, 8, 25));
		when(fullTimeEquivalency.getExpirationDate()).thenReturn(KDate.createDate(2015, 9, 26));
		when(fullTimeEquivalency.getFullTimeEquivalencyId()).thenReturn(new ObjectIdLong(123));
		when(fullTimeEquivalency.getFullTimeEquivalencyPercent()).thenReturn(12.34);
		when(fullTimeEquivalency.getFullTimeStandardHoursQuantity()).thenReturn(12.35);
		when(fullTimeEquivalency.getEmployeeStandardHoursQuantity()).thenReturn(12.36);
		when(fullTimeEquivalency.getVersionCnt()).thenReturn(1L);
		return fullTimeEquivalency;
	}

	private FullTimeEquivalency fullTimeEquivalencyData2() {
		FullTimeEquivalency fullTimeEquivalency = mock(FullTimeEquivalency.class);

		when(fullTimeEquivalency.getEffectiveDate()).thenReturn(KDate.createDate(2015, 8, 26));
		when(fullTimeEquivalency.getExpirationDate()).thenReturn(KDate.createDate(2015, 9, 27));
		when(fullTimeEquivalency.getFullTimeEquivalencyId()).thenReturn(new ObjectIdLong(1234));
		when(fullTimeEquivalency.getFullTimeEquivalencyPercent()).thenReturn(12.36);
		when(fullTimeEquivalency.getFullTimeStandardHoursQuantity()).thenReturn(12.37);
		when(fullTimeEquivalency.getEmployeeStandardHoursQuantity()).thenReturn(12.38);
		when(fullTimeEquivalency.getVersionCnt()).thenReturn(2L);
		return fullTimeEquivalency;
	}
}
