/*******************************************************************************
 * TTIPUserProfileServiceTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.dataaccess.legacy.impl;

import com.ukg.container.selectiveloading.exception.NotImplementedException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;

/*******************************************************************************
 * Test class for TTIPUserProfileService
 ******************************************************************************/
@ExtendWith(MockitoExtension.class)
public class TTIPUserProfileServiceTest {

    @InjectMocks
     private  TTIPUserProfileService ttipUserProfileService;

    @Test
    public void test_WhenGetTTIPEmployeeAttributesByPersonId_Invoke_ThenThrowNotImplementedException(){
        long l=23L;
        assertThrows(NotImplementedException.class,()->ttipUserProfileService.getTTIPEmployeeAttributesByPersonId(l));
    }

    @Test
    public void test_WhenGetAllTTIPUserProfiles_Invoke_ThenThrowNotImplementedException(){
        assertThrows(NotImplementedException.class,()-> ttipUserProfileService.getAllTTIPUserProfiles());
    }

    @Test
    public void test_WhenGetTTIPUserProfileNameById_Invoke_ThenThrowNotImplementedException(){
        long l=23L;
        assertThrows(NotImplementedException.class,()->ttipUserProfileService.getTTIPUserProfileNameById(l));
    }

    @Test
    public void test_WhenGetTTIPUserProfileIdByName_Invoke_ThenThrowNotImplementedException(){
        String name="name";
        assertThrows(NotImplementedException.class,()->ttipUserProfileService.getTTIPUserProfileIdByName(name));
    }

    @Test
    public void test_WhenGetTTIPUserProfileNameByEmployeeId_Invoke_ThenThrowNotImplementedException(){
        long l=23L;
        assertThrows(NotImplementedException.class,()->ttipUserProfileService.getTTIPUserProfileNameByEmployeeId(l));
    }

    @Test
    public void test_WhenGetTTIPUserProfileIdByEmployeeId_Invoke_ThenThrowNotImplementedException(){
        long l=23L;
        assertThrows(NotImplementedException.class,()->ttipUserProfileService.getTTIPUserProfileIdByEmployeeId(l));
    }

    @Test
    public void test_WhenUpdateEmployeeTTIPUserProfile_Invoke_ThenThrowNotImplementedException(){
        long l=23L;
        Long along= 23L;
        assertThrows(NotImplementedException.class,()->ttipUserProfileService.updateEmployeeTTIPUserProfile(l,along));
    }

    @Test
    public void test_WhenGetTTIPPWDUpdateReqdSwByEmployeeId_Invoke_ThenThrowNotImplementedException(){
        long l=23L;
        assertThrows(NotImplementedException.class,()->ttipUserProfileService.getTTIPPWDUpdateReqdSwByEmployeeId(l));
    }

    @Test
    public void test_WhenIsExistTTIPEmployee_Invoke_ThenThrowNotImplementedException(){
        long l=23L;
        assertThrows(NotImplementedException.class,()->ttipUserProfileService.isExistTTIPEmployee(l));
    }

    @Test
    public void test_WhenGetTTIPUserId_Invoke_ThenThrowNotImplementedException(){
        long l=23L;
        assertThrows(NotImplementedException.class,()->ttipUserProfileService.getTTIPUserId(l));
    }

    @Test
    public void test_WhenCheckTTIPUserIdExists_Invoke_ThenThrowNotImplementedException(){
        String s="string";
        assertThrows(NotImplementedException.class,()->ttipUserProfileService.checkTTIPUserIdExists(s));
    }
}
