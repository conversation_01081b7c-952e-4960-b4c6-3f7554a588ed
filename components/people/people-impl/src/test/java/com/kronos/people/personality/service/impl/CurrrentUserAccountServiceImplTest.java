/*******************************************************************************
 * CurrrentUserAccountServiceImplTest.java
 * Copyright © 2024 UKG Inc. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.service.impl;

import java.util.HashMap;
import java.util.Map;

//import javax.servlet.http.HttpServletRequest;


import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.kronos.auth.clientlib.util.CookieHelper;
import com.kronos.auth.domain.UserInfo;
import com.kronos.container.api.access.SpringContext;
import com.kronos.container.impl.auth.userdetail.UserInfoDetail;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.service.PersonalityService;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Class with unit tests for {@link CurrrentUserAccountServiceImpl}
 */
//@PrepareForTest({SpringContext.class, SecurityContextHolder.class, RequestContextHolder.class, CookieHelper.class})
//@RunWith(PowerMockRunner.class)
//@PowerMockIgnore("javax.management.*")
@ExtendWith(MockitoExtension.class)
public class CurrrentUserAccountServiceImplTest {

	@InjectMocks
	private CurrrentUserAccountServiceImpl currrentUserAccountServiceImpl;
	
	@Mock
	private SecurityContext securityContext;

	@Mock
	private Authentication authentication;

	@Mock
	private UserInfoDetail userInfoDetail;
	
	@Mock
	private ApplicationContext applicationContext;
	
	@Mock
	private PersonalityService personalityService;

	private MockedStatic<SpringContext> mockedSpringContext;
	private MockedStatic<SecurityContextHolder> mockedSecurityContextHolder;
	private MockedStatic<RequestContextHolder> mockedRequestContextHolder;
	private MockedStatic<CookieHelper> mockedCookieHelper;
	
	@BeforeEach
	public void setup() {
		mockedSpringContext = Mockito.mockStatic(SpringContext.class);
		mockedSecurityContextHolder = Mockito.mockStatic(SecurityContextHolder.class);
//		PowerMockito.mockStatic(SpringContext.class);
//		PowerMockito.mockStatic(SecurityContextHolder.class);
//		PowerMockito.mockStatic(RequestContextHolder.class);
		mockedRequestContextHolder = Mockito.mockStatic(RequestContextHolder.class);
		mockedCookieHelper = Mockito.mockStatic(CookieHelper.class);


	}

	@AfterEach
	public void tearDown() {
		mockedSpringContext.close();
		mockedSecurityContextHolder.close();
		mockedRequestContextHolder.close();
		mockedCookieHelper.close();
	}
	   
	@Test
	public void testGetUserAccountId() {
//		PowerMockito.mockStatic(CookieHelper.class);
		HttpServletRequest httpServletRequest =Mockito.mock(HttpServletRequest.class);
		ServletRequestAttributes requestAttributes = new ServletRequestAttributes(httpServletRequest);
//		PowerMockito.when(RequestContextHolder.getRequestAttributes()).thenReturn(requestAttributes);
		mockedRequestContextHolder.when(RequestContextHolder::getRequestAttributes).thenReturn(requestAttributes);
//		PowerMockito.when(CookieHelper.getCookieValue(httpServletRequest, "IMPERSONATION_COOKIE")).thenReturn("hello");
		mockedCookieHelper.when(() -> CookieHelper.getCookieValue(httpServletRequest, "IMPERSONATION_COOKIE")).thenReturn("hello");

		//PowerMockito.when(SecurityContextHolder.getContext()).thenReturn(securityContext);
		mockedSecurityContextHolder.when(SecurityContextHolder::getContext).thenReturn(securityContext);
//		PowerMockito.when(securityContext.getAuthentication()).thenReturn(authentication);
		mockedSecurityContextHolder.when(securityContext::getAuthentication).thenReturn(authentication);
//		PowerMockito.when(authentication.getPrincipal()).thenReturn(userInfoDetail);
		mockedSecurityContextHolder.when(authentication::getPrincipal).thenReturn(userInfoDetail);
		UserInfo userinfo = new UserInfo("ram", "raj");
		Mockito.when(userInfoDetail.getUserInfo()).thenReturn(userinfo);
		//PowerMockito.when(SpringContext.getApplicationContext()).thenReturn(applicationContext);
		mockedSpringContext.when(SpringContext::getApplicationContext).thenReturn(applicationContext);
		Mockito.when(applicationContext.getBean(PersonalityService.class)).thenReturn(personalityService);
		
		Map<Object, PersonalityResponse<EmployeeExtension>> empExtensions = new HashMap<>();
		PersonalityResponse<EmployeeExtension> pp = new PersonalityResponse<>();
		EmployeeExtension extension = new EmployeeExtension();
		extension.setUserName("ram");
		extension.setUserAccountId(123L);
		pp.setextension(extension);
		empExtensions.put("ram", pp);
		Mockito.when(personalityService.findEmployeeExtensionsByCriteria(Mockito.any())).thenReturn(empExtensions);
		long result = currrentUserAccountServiceImpl.getUserAccountId();
		assertEquals(123, result);
	}

	@Test
	public void testGivenNoEmployeeIsFoundWithCurrentUserName_WhenGetUserAccountId_ThenThrowsException(){
		CurrrentUserAccountServiceImpl currrentUserAccountServiceSpy = Mockito.spy(currrentUserAccountServiceImpl);
		Mockito.doReturn(true).when(currrentUserAccountServiceSpy).checkImpersonationCookie();
//		PowerMockito.when(SecurityContextHolder.getContext()).thenReturn(securityContext);
//		PowerMockito.when(securityContext.getAuthentication()).thenReturn(authentication);
//		PowerMockito.when(authentication.getPrincipal()).thenReturn(userInfoDetail);
		mockedSecurityContextHolder.when(SecurityContextHolder::getContext).thenReturn(securityContext);
		mockedSecurityContextHolder.when(securityContext::getAuthentication).thenReturn(authentication);
		mockedSecurityContextHolder.when(authentication::getPrincipal).thenReturn(userInfoDetail);
		String userName = "Patricia Yang";
		UserInfo userinfoMock = Mockito.mock(UserInfo.class);
		Mockito.when(userinfoMock.getUsername()).thenReturn(userName);
		Mockito.when(userInfoDetail.getUserInfo()).thenReturn(userinfoMock);
//		PowerMockito.when(SpringContext.getApplicationContext()).thenReturn(applicationContext);
		mockedSpringContext.when(SpringContext::getApplicationContext).thenReturn(applicationContext);
		Mockito.when(applicationContext.getBean(PersonalityService.class)).thenReturn(personalityService);
		String expectedMessage = "No employee found with userName: " + userName;

		IllegalArgumentException result = assertThrows(IllegalArgumentException.class,
				currrentUserAccountServiceSpy::getUserAccountId);
		assertEquals(expectedMessage, result.getMessage());
	}

	@Test
	public void testGivenAnIllegalStateExceptionIsThrownWhileGetRequestAttributes_WhenGetUserAccountId_ThenReturnNull(){
//		PowerMockito.when(RequestContextHolder.getRequestAttributes()).thenThrow(new IllegalStateException());
		mockedRequestContextHolder.when(RequestContextHolder::getRequestAttributes).thenThrow(new IllegalStateException());
		assertNull(currrentUserAccountServiceImpl.getUserAccountId());
	}
	
}
