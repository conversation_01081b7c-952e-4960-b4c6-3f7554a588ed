/*******************************************************************************
 * TenantHandlingFacadeMicroTest.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.tenant;

import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.Operation;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import com.kronos.people.personality.exception.impl.ExceptionHelper;
import com.kronos.tenantprovider.api.TenantProvider;
import com.kronos.tenantprovider.api.exception.TenantProviderException;
import com.kronos.wfc.platform.tenant.business.TenantCache;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for {@code TenantHandlingFacade} class
 */

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness= Strictness.LENIENT)
public class TenantHandlingFacadeMicroTest {

	@InjectMocks
	private TenantHandlingFacade tenantHandlingFacade;

	@Mock
	private Operation operation;

	@Mock
	private ExceptionHelper exceptionHelper;

	@Mock
	private TenantProvider tenantProvider;

	@Mock
	private TenantCache tenantCache;

	private MockedStatic<TenantCache> mockedTenantCache;

	@BeforeEach
	public void setup() {
		mockedTenantCache = Mockito.mockStatic(TenantCache.class);
		mockedTenantCache.when(TenantCache::getCache).thenReturn(tenantCache);
		when(tenantCache.getExternalTenantIdfromTenantId(Mockito.anyLong())).thenReturn("DEFAULT");
	}

	@AfterEach
	public void tearDown() {
		mockedTenantCache.close();
	}

	@Test
	public void testSetTenantId() throws TenantProviderException {
		tenantHandlingFacade.setTenantId("MyTenantId");
		verify(tenantProvider).setTenantId("MyTenantId");
		AtomicInteger a =new AtomicInteger(0);
		tenantHandlingFacade=new TenantHandlingFacade(){
			@Override
			public void removeTenantId() {
				a.getAndIncrement();
			}
		};
		tenantHandlingFacade.setTenantId(null);
//		assertEquals("Remove TenantId is called when null passed to settenantId",1,a.get());
		assertEquals(1, a.get(), "Remove TenantId is called when null passed to settenantId");
	}
	
	@Test
	public void testGetTenantIdWhenException() {
		tenantHandlingFacade.exceptionHelper = new ExceptionHelper(null);
		tenantHandlingFacade.setTenantProvider(new MockTenantProvider());
		((MockTenantProvider)(tenantHandlingFacade.tenantProvider)).shouldThrowException = true;
		try {
			tenantHandlingFacade.getTenantId();
			fail("Should have thrown exception");
		} catch (PersonalityExtensionException tpe) {
			
		} catch (Exception e) {
			fail("Should have thrown personalityExtensionException");
		}
	}
	
	public void testGetTenantIdWhenSuccess() {
		tenantHandlingFacade.setTenantProvider(new MockTenantProvider());
		((MockTenantProvider)(tenantHandlingFacade.tenantProvider)).tenantId = "TENANTID";
		assertEquals("TENANTID", tenantHandlingFacade.getTenantId());
	}
	
	
	@Test
	public void testPerformOperationsUsingTenantId(){
		AtomicInteger a =new AtomicInteger(0);
		StringBuilder sb=new StringBuilder();
		TenantProvider tenantProviderMock = new MockTenantProvider();
		tenantProviderMock.setTenantId("1");
//		Operation operation=()->a.getAndIncrement();
		Operation operation = a::getAndIncrement;
		String tenantId="1";
		tenantHandlingFacade=new TenantHandlingFacade(){
			public void removeTenantId() {
				sb.append("remove tenantId");
			}
		};
		tenantHandlingFacade.setTenantProvider(tenantProviderMock);
		tenantHandlingFacade.performOperationsUsingTenantId(operation, tenantId);
		assertEquals("1",tenantHandlingFacade.getTenantId());
		assertTrue(sb.indexOf("remove tenantId")>-1);
	}
	
	@Test
	public void testPerformOperationsUsingTenantIdWithRemoving(){
		AtomicInteger a =new AtomicInteger(0);
		TenantProvider tenantProviderMock = new MockTenantProvider();
		tenantProviderMock.setTenantId("1");
//		Operation operation=()->a.getAndIncrement();
		Operation operation = a::getAndIncrement;
		String tenantId="1";
		
		tenantHandlingFacade.setTenantProvider(tenantProviderMock);
		tenantHandlingFacade.performOperationsUsingTenantId(operation, tenantId);
//		assertNull("tenant id is removed",tenantHandlingFacade.getTenantId());
		assertNull(tenantHandlingFacade.getTenantId(), "tenant id is removed");
	}
	
	@Test
	public void testPerformOperationsUsingTenantIdForSupplier(){
		StringBuilder sb=new StringBuilder();
		TenantProvider tenantProviderMock = new MockTenantProvider();
		tenantProviderMock.setTenantId("1");
		Supplier<String> supplier=()->sb.append("Testing setTenantId").toString();
		String tenantId="1";
		tenantHandlingFacade=new TenantHandlingFacade(){
			public void removeTenantId() {
				sb.append("remove tenantId");
			}
		};
		tenantHandlingFacade.setTenantProvider(tenantProviderMock);
		assertEquals("Testing setTenantId",tenantHandlingFacade.performOperationsUsingTenantId(supplier, tenantId));
		assertEquals("1",tenantHandlingFacade.getTenantId());
		assertTrue(sb.indexOf("remove tenantId")>-1);
	}
	
	@Test
	public void testPerformOperationsUsingTenantIdWithRemovingForSupplier(){
		StringBuilder sb=new StringBuilder();
		TenantProvider tenantProviderMock = new MockTenantProvider();
		tenantProviderMock.setTenantId("1");
		Supplier<String> supplier=()->sb.append("Testing setTenantId").toString();
		String tenantId="1";
		
		tenantHandlingFacade.setTenantProvider(tenantProviderMock);
		assertEquals("Testing setTenantId",tenantHandlingFacade.performOperationsUsingTenantId(supplier, tenantId));
//		assertNull("tenant id is removed",tenantHandlingFacade.getTenantId());
		assertNull(tenantHandlingFacade.getTenantId(), "tenant id is removed");
	}
	
	@Test
	public void testPerformOperationsUsingTenantIdWithRemovingForSupplierForNullTenant(){
		StringBuilder sb=new StringBuilder();
		TenantProvider tenantProviderMock = new MockTenantProvider();
		tenantProviderMock.setTenantId(null);
		Supplier<String> supplier=()->sb.append("Testing setTenantId").toString();
		String tenantId=null;
		tenantHandlingFacade.setTenantProvider(tenantProviderMock);
		assertEquals("Testing setTenantId",tenantHandlingFacade.performOperationsUsingTenantId(supplier, tenantId));
//		assertNull("tenant id is never set",tenantHandlingFacade.getTenantId());
		assertNull(tenantHandlingFacade.getTenantId(), "tenant id is never set");
	}

	
	@Test
	public void testPerformOperationsUsingTenantIdWithRemovingForSupplierForException(){
		StringBuilder sb=new StringBuilder();
		TenantProvider tenantProviderMock = new MockTenantProvider();
		tenantProviderMock.setTenantId(null);
		Supplier<String> supplier=()->{
			throw new APIException("Exception occurred.");
		};
		String tenantId=null;
		
//		tenantHandlingFacade.setTenantProvider(tenantProviderMock);
//		assertEquals("Testing setTenantId",tenantHandlingFacade.performOperationsUsingTenantId(supplier, tenantId));
		tenantHandlingFacade.setTenantProvider(tenantProviderMock);
		assertThrows(APIException.class, () -> {
			tenantHandlingFacade.performOperationsUsingTenantId(supplier, tenantId);
		});
//		assertNull("tenant id is never set",tenantHandlingFacade.getTenantId());
		assertNull(tenantHandlingFacade.getTenantId(), "tenant id is never set");
	}

	@Test
	public void testIsTenantIdNullForCurrentThread() {
		doReturn(true).when(tenantProvider).isTenantIdNullforCurrentThread();
		boolean result = tenantHandlingFacade.isTenantIdNullForCurrentThread();
        assertTrue(result);
	}

	@Test
	public void testGetRegisteredTenants() {
		Set<Long> tenantIds = new HashSet<>();
		tenantIds.add(1L);
		tenantIds.add(2L);
//		PowerMockito.when(tenantCache.getAllTenantIds()).thenReturn(tenantIds);
		when(tenantCache.getAllTenantIds()).thenReturn(tenantIds);
		Set<Long> registeredTenants = tenantHandlingFacade.getRegisteredTenants();
		assertEquals(tenantIds, registeredTenants);

	}

	@Test
	public void testPerformOperationWithValidTenantId() {
		Long validTenantId = 123L;
		when(tenantHandlingFacade.getTenantId()).thenReturn("123");
		tenantHandlingFacade.performOperationsUsingTenantId(operation,validTenantId);
		verify(operation,times(1)).execute();
		assertEquals("123",tenantHandlingFacade.getTenantId());
	}

	@Test
	public void testPerformOperationWithNullTenantId() {
		Long tenantId = null;
		tenantHandlingFacade.performOperationsUsingTenantId(operation,tenantId);
		assertNull(tenantHandlingFacade.getTenantId());
	}

	@Test
	public void testSetExceptionHelper() {
		tenantHandlingFacade.setExceptionHelper(exceptionHelper);
		assertEquals(exceptionHelper,tenantHandlingFacade.getExceptionHelper());
	}

}
