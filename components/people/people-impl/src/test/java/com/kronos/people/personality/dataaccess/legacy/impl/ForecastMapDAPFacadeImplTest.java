/*******************************************************************************
 * ForecastMapDAPFacadeImplTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/

package com.kronos.people.personality.dataaccess.legacy.impl;

import static org.junit.jupiter.api.Assertions.assertThrows;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.ukg.container.selectiveloading.exception.NotImplementedException;

/*******************************************************************************
 * Test class for ForecastMapDAPFacadeImplTest
 ******************************************************************************/
@ExtendWith(MockitoExtension.class)
public class ForecastMapDAPFacadeImplTest {
	
	
	@InjectMocks
    private ForecastMapDAPFacadeImpl forecastMapDAPFacade;
    
	@Mock
	private ObjectIdLong objectIdLong;
	
    @Test
    public void testWhenGetForecastMapNameMethodInvokedThenThrowNotImplementedException() {
    	
        assertThrows(NotImplementedException.class, ()->forecastMapDAPFacade.getForecastMapName(objectIdLong));
    }

    @Test
    public void testWhenGetForecastMapIdMethodInvokedThenThrowNotImplementedException() {
        String name="name";
        assertThrows(NotImplementedException.class, ()->forecastMapDAPFacade.getForecastMapId(name));
    }
}
