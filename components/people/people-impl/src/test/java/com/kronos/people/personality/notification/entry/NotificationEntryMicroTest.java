/***********************************************************************
 * NotificationEntryMicroTest.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.notification.entry;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import com.kronos.cache.api.key.MultiExtensionKey;
import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.people.personality.dataaccess.adapter.ExtensionAdapterEnum;
import com.kronos.people.personality.facade.AllExtensionBuilder;
import com.kronos.people.personality.facade.PersonalityCacheFacade;
import com.kronos.people.personality.model.extension.AccrualExtension;
import com.kronos.people.personality.model.extension.AllExtension;
import com.kronos.people.personality.model.extension.BaseExtension;
import com.kronos.people.personality.model.extension.DevicesExtension;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.SchedulingExtension;
import com.kronos.people.personality.model.extension.TimekeepingExtension;
import com.kronos.people.personality.model.extension.entry.BadgeDetailsEntry;
import com.kronos.people.personality.notification.PersonalityChangeNotificationManager;

/**
 * <AUTHOR>
 */
public class NotificationEntryMicroTest {

    @Spy
    @InjectMocks
    private PersonalityChangeNotificationManager personalityChangeNotificationManager;

    @Mock
    private KronosThreadPoolService kronosThreadPoolService;


    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
        when(kronosThreadPoolService.newThreadPool(anyString())).thenReturn(mock(ThreadPoolExecutor.class));
        personalityChangeNotificationManager.init();
    }

    @Test
    public void testNotificationEntry() {
        PersonalityCacheFacade cacheFacade = spy(PersonalityCacheFacade.class);
        cacheFacade.setAllExtensionBuilder(new AllExtensionBuilder());

        //test insertion
        List<CacheEntry> insertedCacheEntries = getInsertedCacheEntries();
        NotificationEntry ne = new NotificationEntry("manufacturing", insertedCacheEntries, cacheFacade::getAllExtensionsFromMap);
        personalityChangeNotificationManager.addNotificationEntry(ne);
        Map<Long, AllExtension> insertedExtensions1 = ne.getInsertedPersonExtensions();
        assertEquals(1, insertedExtensions1.keySet().size());
        Map<Long, AllExtension> updatedExtensions1 = ne.getUpdatedPersonExtensions();
        assertEquals(0, updatedExtensions1.keySet().size());
        Set<Long> deletedEntries1 = ne.getDeleteEntries();
        assertEquals(0, deletedEntries1.size());

        //test updation
        List<CacheEntry> updatedCacheEntries = getUpdatedCacheEntries();
        NotificationEntry updateNE = new NotificationEntry("manufacturing", updatedCacheEntries, cacheFacade::getAllExtensionsFromMap);
        personalityChangeNotificationManager.addNotificationEntry(updateNE);
        Map<Long, AllExtension> insertedExtensions2 = updateNE.getInsertedPersonExtensions();
        assertEquals(0, insertedExtensions2.keySet().size());
        Map<Long, AllExtension> updatedExtensions2 = updateNE.getUpdatedPersonExtensions();
        assertEquals(1, updatedExtensions2.keySet().size());
        Set<Long> deletedEntries2 = updateNE.getDeleteEntries();
        assertEquals(0, deletedEntries2.size());

        //test deletion
        List<CacheEntry> deletedCacheEntries = getDeletedCacheEntries();
        NotificationEntry deleteNE = new NotificationEntry("manufacturing", deletedCacheEntries, cacheFacade::getAllExtensionsFromMap);
        personalityChangeNotificationManager.addNotificationEntry(deleteNE);
        Map<Long, AllExtension> insertedExtensions3 = deleteNE.getInsertedPersonExtensions();
        assertEquals(0, insertedExtensions3.keySet().size());
        Map<Long, AllExtension> updatedExtensions3 = deleteNE.getUpdatedPersonExtensions();
        assertEquals(0, updatedExtensions3.keySet().size());
        Set<Long> deletedEntries3 = deleteNE.getDeleteEntries();
        assertEquals(1, deletedEntries3.size());
        assertEquals("manufacturing", deleteNE.getTenantId());

        //test mixed cache entries
        List<CacheEntry> mixedCacheEntries = new ArrayList<>();
        mixedCacheEntries.addAll(deletedCacheEntries);
        mixedCacheEntries.addAll(insertedCacheEntries);
        mixedCacheEntries.addAll(updatedCacheEntries);

        NotificationEntry notificationEntry = new NotificationEntry("manufacturing", mixedCacheEntries, cacheFacade::getAllExtensionsFromMap);
        Map<Long, AllExtension> insertedExtensions = notificationEntry.getInsertedPersonExtensions();
        Map<Long, AllExtension> updatedExtensions = notificationEntry.getUpdatedPersonExtensions();
        Set<Long> deletedPersonIds = notificationEntry.getDeleteEntries();
//        Long insertedPersonId = 101l;
//        Long updatedPersonId = 102l;
//        Long deletedPersonId = 103l;
        Long insertedPersonId = 101L;
        Long updatedPersonId = 102L;
        Long deletedPersonId = 103L;
        for (Map.Entry<Long, AllExtension> entry : insertedExtensions.entrySet()) {
            assertEquals(insertedPersonId, entry.getKey());
        }
        for (Map.Entry<Long, AllExtension> entry : updatedExtensions.entrySet()) {
            assertEquals(updatedPersonId, entry.getKey());
        }
        deletedPersonIds.forEach(personId -> assertEquals(deletedPersonId, personId));
    }

    private List<CacheEntry> getInsertedCacheEntries() {
        List<CacheEntry> cacheEntries = new ArrayList<>();
//        MultiExtensionKey key = new MultiExtensionKey(101l, ExtensionAdapterEnum.EMPLOYEE.getIdentifier());
        MultiExtensionKey key = new MultiExtensionKey(101L, ExtensionAdapterEnum.EMPLOYEE.getIdentifier());
        Map<String, BaseExtension> extensionsMap = getExtensionsMap();
        CacheEntry insertedEntry = new CacheEntry(key, EventType.INSERT, extensionsMap, "manufacturing");
        cacheEntries.add(insertedEntry);
        return cacheEntries;
    }

    private List<CacheEntry> getUpdatedCacheEntries() {
        List<CacheEntry> cacheEntries = new ArrayList<>();
//        MultiExtensionKey key = new MultiExtensionKey(102l, ExtensionAdapterEnum.EMPLOYEE.getIdentifier());
        MultiExtensionKey key = new MultiExtensionKey(102L, ExtensionAdapterEnum.EMPLOYEE.getIdentifier());
        Map<String, BaseExtension> extensionsMap = getExtensionsMap();
        CacheEntry insertedEntry = new CacheEntry(key, EventType.UPDATE, extensionsMap, "manufacturing");
        cacheEntries.add(insertedEntry);
        return cacheEntries;
    }

    private List<CacheEntry> getDeletedCacheEntries() {
        List<CacheEntry> cacheEntries = new ArrayList<>();
//        MultiExtensionKey key = new MultiExtensionKey(103l, ExtensionAdapterEnum.EMPLOYEE.getIdentifier());
        MultiExtensionKey key = new MultiExtensionKey(103L, ExtensionAdapterEnum.EMPLOYEE.getIdentifier());
        Map<String, BaseExtension> extensionsMap = getExtensionsMap();
        CacheEntry insertedEntry = new CacheEntry(key, EventType.DELETE, extensionsMap, "manufacturing");
        cacheEntries.add(insertedEntry);
        return cacheEntries;
    }

    private Map<String, BaseExtension> getExtensionsMap() {
        Map<String, BaseExtension> extensionsMap = new HashMap<>();
        EmployeeExtension employeeExtension = mock(EmployeeExtension.class);
        extensionsMap.put("EMPLOYEE_EXTENSION", employeeExtension);
        SchedulingExtension schedulingExtension = mock(SchedulingExtension.class);
        extensionsMap.put("SCHEDULING_EXTENSION", schedulingExtension);
        TimekeepingExtension tkExtension = mock(TimekeepingExtension.class);
        extensionsMap.put("TIMEKEEPING_EXTENSION", tkExtension);
        AccrualExtension accExtension = mock(AccrualExtension.class);
        extensionsMap.put("ACCRUAL_EXTENSION", accExtension);
        DevicesExtension deviceExtension = mock(DevicesExtension.class);
        extensionsMap.put("DEVICE_EXTENSION", deviceExtension);
        Collection<BadgeDetailsEntry> value = new ArrayList<>();
//        value.add(new BadgeDetailsEntry(LocalDateTime.MIN, LocalDateTime.MAX).setBadgeAssignmentId(1l));
        value.add(new BadgeDetailsEntry(LocalDateTime.MIN, LocalDateTime.MAX).setBadgeAssignmentId(1L));
        Mockito.when(deviceExtension.getBadgeDetails()).thenReturn(value);
        return extensionsMap;
    }
}
