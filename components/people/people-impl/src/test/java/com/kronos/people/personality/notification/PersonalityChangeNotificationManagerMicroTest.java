/***********************************************************************
 * PersonalityChangeNotificationManagerMicroTest.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.people.personality.notification;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Consumer;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.people.personality.model.extension.AllExtension;
import com.kronos.people.personality.notification.entry.NotificationEntry;
import com.kronos.people.personality.tenant.TenantHandlingFacade;

/**
 * <AUTHOR>
 *
 */

public class PersonalityChangeNotificationManagerMicroTest {

    @InjectMocks
    PersonalityChangeNotificationManager personalityChangeNotificationManager;
    @Mock
    private TenantHandlingFacade tenantHandler;
    @Mock
    private KronosThreadPoolService kronosThreadPoolService;

    @BeforeEach
    public void init() {
//        MockitoAnnotations.initMocks(this);
        MockitoAnnotations.openMocks(this);
        when(kronosThreadPoolService.newThreadPool(anyString())).thenReturn(mock(ThreadPoolExecutor.class));
        personalityChangeNotificationManager.init();
    }

	@Test
	public void testNotiyInsert(){
		Map<Long, AllExtension> personExtensions = new HashMap<>();
		personExtensions.put(101L, mock(AllExtension.class));
		personExtensions.put(102L, mock(AllExtension.class));
		//test null check
		personalityChangeNotificationManager.notifyInsert(personExtensions);
		//optional empty check
		personalityChangeNotificationManager.personalityChangeListeners = null;
		personalityChangeNotificationManager.notifyInsert(personExtensions);
		//with mocked listeners check
		addMockedListeners();
		personalityChangeNotificationManager.notifyInsert(personExtensions);
	}

	private void addMockedListeners(){
		List<PersonalityChangeListener> personalityChangeListeners = new ArrayList<>();
		PersonalityChangeListener listener1 = mock(PersonalityChangeListener.class);
		PersonalityChangeListener listener2 = mock(PersonalityChangeListener.class);
		personalityChangeListeners.add(listener1);
		personalityChangeListeners.add(listener2);
		personalityChangeNotificationManager.personalityChangeListeners = personalityChangeListeners;

	}

	@Test
	public void testNotiyUpdate(){
		Map<Long, AllExtension> personExtensions = new HashMap<>();
		personExtensions.put(101L, mock(AllExtension.class));
		personExtensions.put(102L, mock(AllExtension.class));
		//test null check
		personalityChangeNotificationManager.notifyUpdate(personExtensions);
		//optional empty check
		personalityChangeNotificationManager.personalityChangeListeners = null;
		personalityChangeNotificationManager.notifyUpdate(personExtensions);
		//with mocked listeners check
		addMockedListeners();
		personalityChangeNotificationManager.notifyUpdate(personExtensions);
	}

	@Test
	public void testNotiyDelete(){
		Set<Long> personIds = new HashSet<>();
		personIds.add(101L);
		personIds.add(102L);
		//test null check
		personalityChangeNotificationManager.notifyDelete(personIds);
		//optional empty check
		personalityChangeNotificationManager.personalityChangeListeners = null;
		personalityChangeNotificationManager.notifyDelete(personIds);
		//with mocked listeners check
		addMockedListeners();
		personalityChangeNotificationManager.notifyDelete(personIds);
	}

	@Test
	public void testInit(){
		Consumer<NotificationEntry> consumer1 = new Consumer<NotificationEntry>() {
			@Override
			public void accept(NotificationEntry n) {
			}
		};
		personalityChangeNotificationManager.consumer = consumer1;
		personalityChangeNotificationManager.init();
	}

	@Test
	public void testCleanup(){
		personalityChangeNotificationManager.cleanUp();
	}
}
