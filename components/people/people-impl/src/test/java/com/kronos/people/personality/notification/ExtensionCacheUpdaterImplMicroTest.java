package com.kronos.people.personality.notification;

import com.kronos.people.personality.cache.PersonalityCacheAccessor;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import com.kronos.people.personality.exception.impl.ExceptionHelper;
import com.kronos.people.personality.facade.PersonalityCacheFacade;
import com.kronos.people.personality.notification.batch.PersonalityBatchProcessor;
import com.kronos.people.personality.notification.entry.EventType;
import com.kronos.people.personality.notification.entry.PersonalityEvent;
import com.kronos.people.personality.tenant.TenantHandlingFacade;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonNotification;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class ExtensionCacheUpdaterImplMicroTest {
	@InjectMocks
	private ExtensionCacheUpdaterImpl updaterImpl;
	ExtensionCacheUpdaterImpl cacheUpdaterImplSpy;

	private MockedStatic<PersonNotification> mockedPersonNotification;

	@BeforeEach
	public void setup(){
		cacheUpdaterImplSpy = spy(ExtensionCacheUpdaterImpl.class);
		cacheUpdaterImplSpy.adapterHelper = new AdapterHelper();
		cacheUpdaterImplSpy.tenantFacade = mock(TenantHandlingFacade.class);
		cacheUpdaterImplSpy.exceptionHandler = new ExceptionHelper(null);
		if (mockedPersonNotification == null) {
			mockedPersonNotification = Mockito.mockStatic(PersonNotification.class);
		}
	}
	

	
	@AfterEach
	public void tearDown(){

		cacheUpdaterImplSpy = null;
		if (mockedPersonNotification != null) {
			mockedPersonNotification.close();
			mockedPersonNotification = null;
		}
	}
	
	@Test
	public void updateTest(){
		Personality personalityMock = mock(Personality.class);
		when(personalityMock.getPersonId()).thenReturn(new ObjectIdLong(1L));
		ChangeNotificationChannelController changeNotificationChannelControllerMock = mock(ChangeNotificationChannelController.class);
		cacheUpdaterImplSpy.setExtensionsCacheUpdator(changeNotificationChannelControllerMock);
		cacheUpdaterImplSpy.update(personalityMock);
		mockedPersonNotification.verify(() -> PersonNotification.sendUpdate(new ObjectIdLong(1L)), times(1));
	}
	
	@Test
	public void insertTest(){
		Personality personalityMock = mock(Personality.class);
		Mockito.when(personalityMock.getPersonId()).thenReturn(new ObjectIdLong(1L));
		ChangeNotificationChannelController changeNotificationChannelControllerMock = mock(ChangeNotificationChannelController.class);
		cacheUpdaterImplSpy.setExtensionsCacheUpdator(changeNotificationChannelControllerMock);
		
		cacheUpdaterImplSpy.insert(personalityMock);
		
		verify(cacheUpdaterImplSpy, times(1)).addToProcessorChannel(personalityMock, EventType.INSERT);
		
		//Should not have called the notification
	}
	
	@Test
	public void testRegisterBatch(){
		List<Long> personIds = new ArrayList<>();
		personIds.add(1L);
		personIds.add(2L);
		StringBuilder sb = new StringBuilder();
		ExtensionCacheUpdaterImpl cacheUpdater = new ExtensionCacheUpdaterImpl();
		PersonalityBatchProcessor personalityBatchProcessor = Mockito.mock(PersonalityBatchProcessor.class);
		Mockito.doNothing().when(personalityBatchProcessor).addToBatchMap(personIds);
		cacheUpdater.personalityBatchProcessor = personalityBatchProcessor;
		cacheUpdater.registerBatch(personIds);
		Mockito.verify(personalityBatchProcessor, Mockito.times(1)).addToBatchMap(personIds);
		
	}
	
	@Test
	public void testClearFromCache(){
		List<Long> personIds = new ArrayList<>();
		personIds.add(1L);
		personIds.add(2L);
		personIds.add(3L);
		PersonalityCacheAccessor personalityCacheAccessor = Mockito.mock(PersonalityCacheAccessor.class);
			Mockito.doNothing().when(personalityCacheAccessor).deleteMultiplePersons(any());
		List<Long> personIds2 = new ArrayList<>();
	
	}
	
	@Test
	public void deleteTest(){
		Personality personalityMock = mock(Personality.class);
		when(personalityMock.getPersonId()).thenReturn(new ObjectIdLong(1L));
		ChangeNotificationChannelController changeNotificationChannelControllerMock = mock(ChangeNotificationChannelController.class);
		cacheUpdaterImplSpy.setExtensionsCacheUpdator(changeNotificationChannelControllerMock);
		cacheUpdaterImplSpy.delete(personalityMock);
		mockedPersonNotification.verify(() -> PersonNotification.sendDelete(new ObjectIdLong(1L)), times(1));
	}

	@Test
	public void addToProcessorChannelTest() {
		ChangeNotificationChannelController changeNotificationChannelController=Mockito.mock(ChangeNotificationChannelController.class);
		PersonalityEvent personalityEventMock = mock(PersonalityEvent.class);
		List<PersonalityEvent> personalityEventList = new ArrayList<>();
		personalityEventList.add(personalityEventMock);
		updaterImpl.setExtensionsCacheUpdator(changeNotificationChannelController);
		updaterImpl.addToProcessorChannel(personalityEventList);
		verify(changeNotificationChannelController, times(1)).notify(personalityEventList);
	}


	@Test
	public void testLongCUD(){
		StringBuilder output = new StringBuilder();
		ExtensionCacheUpdaterImpl cacheUpdaterImpl = new ExtensionCacheUpdaterImpl(){
			@Override
			public void update(Personality personality) {
				output.append("update");
			}
			
			@Override
			public void update(Long id) {
				output.append("update");
			}
			
			@Override
			public void insert(Personality personality) {
				output.append("insert");
			}
			
			@Override
			public void delete(Personality personality) {
				output.append("delete");
			}
			
			@Override
			public void delete(Long id) {
				output.append("delete");
			}
		};
//		PowerMockito.mockStatic(PersonNotification.class);
		PersonalityFacade personalityFacade = Mockito.mock(PersonalityFacade.class);
		cacheUpdaterImpl.personalityFacade = personalityFacade;
		when(personalityFacade.findPersonality(1L)).thenReturn(new Personality());
		cacheUpdaterImpl.insert(1L);
		cacheUpdaterImpl.update(1L);
		cacheUpdaterImpl.delete(1L);
		assertEquals("insertupdatedelete", output.toString());
	}

	@Test
	public void testUnregisterBatch(){
		ExtensionCacheUpdaterImpl cacheUpdaterImpl = new ExtensionCacheUpdaterImpl();
		List<Long> personIds = new ArrayList<>();
		personIds.add(1L);
		PersonalityBatchProcessor personalityBatchProcessor  = Mockito.mock(PersonalityBatchProcessor.class);
		PersonalityCacheFacade personalityCacheFacade   = spy(PersonalityCacheFacade.class);
		Mockito.doNothing().when(personalityBatchProcessor).removeFromBatchMap(personIds);
		Mockito.doNothing().when(personalityCacheFacade).evictFromOnHeap(personIds);
		cacheUpdaterImpl.personalityBatchProcessor = personalityBatchProcessor;
		cacheUpdaterImpl.personalityCacheFacade = personalityCacheFacade;
		cacheUpdaterImpl.unregisterBatch(personIds);
		Mockito.verify(personalityBatchProcessor, Mockito.times(1)).removeFromBatchMap(personIds);
		Mockito.verify(personalityCacheFacade, Mockito.times(1)).evictFromOnHeap(personIds);
	}
	
	@Test
	public void testGetPersonality(){
		Long personIds = 1L;
		Personality personality = cacheUpdaterImplSpy.getPersonality(personIds);
		assertEquals(personIds, personality.getTriplet().getPersonId().toLong());
	}
	
//	@Test(expected = PersonalityExtensionException.class)
	@Test
	public void testGetPersonalityWithNull(){
		Long personIds = null;
//		cacheUpdaterImplSpy.getPersonality(personIds);
		assertThrows(PersonalityExtensionException.class, () -> cacheUpdaterImplSpy.getPersonality(personIds));
	}
	
	@Test
	public void testRefreshNewPersonalityCacheOnDelete() {
		Personality personalityMock = mock(Personality.class);
		Mockito.when(cacheUpdaterImplSpy.getPersonality(1L)).thenReturn(personalityMock);
		ChangeNotificationChannelController changeNotificationChannelControllerMock = mock(ChangeNotificationChannelController.class);
		cacheUpdaterImplSpy.setExtensionsCacheUpdator(changeNotificationChannelControllerMock);
		cacheUpdaterImplSpy.refreshNewPersonalityCache(1L, PersonNotification.DELETE_EVENT);
		verify(cacheUpdaterImplSpy, times(1)).addToProcessorChannel(personalityMock, EventType.DELETE);
	}
	
	@Test
	public void testRefreshNewPersonalityCacheOnUpdate() {
		Personality personalityMock = mock(Personality.class);
		PersonalityFacade personalityFacade = Mockito.mock(PersonalityFacade.class);
		cacheUpdaterImplSpy.personalityFacade = personalityFacade;
		when(personalityFacade.findPersonality(1L)).thenReturn(personalityMock);
		ChangeNotificationChannelController changeNotificationChannelControllerMock = mock(ChangeNotificationChannelController.class);
		cacheUpdaterImplSpy.setExtensionsCacheUpdator(changeNotificationChannelControllerMock);
		cacheUpdaterImplSpy.refreshNewPersonalityCache(1l,PersonNotification.UPDATE_EVENT);
		verify(cacheUpdaterImplSpy, times(1)).addToProcessorChannel(Mockito.any(PersonalityEvent.class));
	}
	
}