package com.kronos.people.personality.exception;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
public class BaseExceptionMicroTest {
	
	private PersonalityErrorCode pr=PersonalityErrorCode.FULL_NAME_NOT_VALID;

	@Test
	public void testCostructor() {
		String message = "Message";
		String debugMessage = "Debug Message";
		Map<String, String> messageArgsMap = new HashMap<>();
		messageArgsMap.put("abc", "abc");

		BaseException baseExceptionObj = new BaseException(pr, message, debugMessage, messageArgsMap);

		assertEquals("FULL_NAME_NOT_VALID", baseExceptionObj.getErrorCode());
		assertEquals("commonbusiness.people.extension.error.personality5108", baseExceptionObj.getLocalizedMessageKey());
		assertEquals("Message", baseExceptionObj.getMessage());
		assertEquals("Debug Message", baseExceptionObj.getDebugMessage());
		assertEquals(messageArgsMap, baseExceptionObj.getMessageArgs());
		assertEquals("abc", messageArgsMap.get("abc"));

		messageArgsMap = null;
		assertNotNull(baseExceptionObj.getMessageArgs());
	}

	@Test
	public void testConsByNullMessage() {
		String defaultMessage = "Default Message";

		BaseException baseExceptionObj = new BaseException(pr, defaultMessage, null, null);
		assertEquals("Default Message", baseExceptionObj.getMessage());
	}

	@Test
	public void testConsByEmptyMessage() {
		String defaultMessage = "Default Message";

		BaseException baseExceptionObj = new BaseException(pr, defaultMessage, "",  null);
		assertEquals("Default Message", baseExceptionObj.getMessage());
	}
}
