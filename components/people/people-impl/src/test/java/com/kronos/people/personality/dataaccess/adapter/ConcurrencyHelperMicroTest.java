/**
 * 
 */
package com.kronos.people.personality.dataaccess.adapter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;

import com.kronos.concurrent.api.service.KronosThreadPoolService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;

import com.kronos.people.MockExecutorService;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import com.kronos.people.personality.exception.impl.ExceptionHelper;
import com.kronos.people.personality.model.IdentifierType;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.BaseExtension;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.properties.KronosPropertiesFacade;
import com.kronos.people.personality.tenant.TenantHandlingFacade;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
public class ConcurrencyHelperMicroTest {

	private ConcurrencyHelper concurrencyHelper;
	@Mock
	KronosPropertiesFacade propertiesFacade;
	@Mock
	TenantHandlingFacade tenantHandlingFacade;
	@Mock
	KronosThreadPoolService threadPoolService;
	@Mock
	ExecutorService executor;
	@BeforeEach
	public void setup() {
		when(propertiesFacade.getIntegerKronosProperty(any(),anyInt())).thenReturn(100);
//		Mockito.when(threadPoolService.newThreadPool(any())).thenReturn(executor);
		concurrencyHelper = new ConcurrencyHelper(tenantHandlingFacade,propertiesFacade,null);
		concurrencyHelper.tenantHandlingFacade = tenantHandlingFacade;
	}
	
	@Test
	public void testgetPersonalityResponseFromExtension(){
		
		BaseExtension extension = mock(EmployeeExtension.class);
		LocalDate snapShotDate = LocalDate.now();
		Boolean isActiveOnly = true;
		ExceptionHelper exceptionHelperMock = mock(ExceptionHelper.class);
		exceptionHelperMock = new ExceptionHelper(null){
			@Override
			public PersonalityExtensionException createException(IdentifierType idType, String debugMessage) {
				return new PersonalityExtensionException(null, "error");
			};
		};
		concurrencyHelper.exceptionHelper = exceptionHelperMock;
		PersonalityResponse<BaseExtension> extn = concurrencyHelper.getPersonalityResponseFromExtension( extension, snapShotDate, isActiveOnly );
		assertNotNull(extn);
	}
	
	
	@Test
	public void testSetKronosPropertiesFacade(){
		KronosPropertiesFacade kronosPropertiesFacade = new KronosPropertiesFacade(){
			
			public String getStringKronosProperty(String key) {
				return "Value";
			}
			
			public void setKronosTenantProperty(String key, String value){
			}
		};
		concurrencyHelper.setKronosPropertiesFacade(kronosPropertiesFacade);
		concurrencyHelper.kronosPropertiesFacade.setKronosTenantProperty("Key", "Value");
		assertTrue(concurrencyHelper.kronosPropertiesFacade.getStringKronosProperty("Key").equals("Value"));
	}
	
	@Test
	public void testGetKronosPropertiesFacade(){
		KronosPropertiesFacade kronosPropertiesFacade = new KronosPropertiesFacade(){
			
			public String getStringKronosProperty(String key) {
				return "Value";
			}
			
			public void setKronosTenantProperty(String key, String value){
			}
		};
		concurrencyHelper.setKronosPropertiesFacade(kronosPropertiesFacade);
		KronosPropertiesFacade kronosPropertiesFacadeGet = concurrencyHelper.getKronosPropertiesFacade();
		assertTrue(kronosPropertiesFacadeGet.getStringKronosProperty("AnyKey").equals(kronosPropertiesFacade.getStringKronosProperty("AnyKey")));
	}
	
	
	
	@Test
	public void testgetMissingIdsNotPresentInSet(){
		
		String[] allIds = {"aa", "bb"};
		Set<String> idsInSet = new HashSet<>();
		idsInSet.add("cc");
		AtomicInteger size = new AtomicInteger(0);
		Queue<String> result = concurrencyHelper.getMissingIdsNotPresentInSet(allIds, idsInSet,size);
		assertNotNull(result);
		assertEquals(2,size.get());
	}
	@Test
	public void testParallelProcessQueueItems(){
		AtomicInteger a =new AtomicInteger(0);
		concurrencyHelper= new ConcurrencyHelper(tenantHandlingFacade,propertiesFacade,null){
			public <T> int parallelProcessQueueItems(Queue<T> queue, Function<T, Boolean> supplier, ExecutorService executor, int numberOfThreads,int size) {
				a.getAndIncrement();
				return 1;
			}
		};
		Queue<String> queue = new ConcurrentLinkedQueue<String>();
		Function<String,Boolean> processingFunction=str->str.isEmpty();
		int numberOfThreads=3;
		int size=4;
		assertEquals(1,concurrencyHelper.parallelProcessQueueItems(queue, processingFunction, numberOfThreads, size));
		assertEquals(1,a.get());
	}
	
	@Test
	public void testparallelProcessQueueItems(){
		MockExecutorService executor = new MockExecutorService();
		int numberOfThreads=5;
		int size=2;
		Queue queue = new LinkedBlockingQueue();
		Function supplier = null;
		AtomicInteger a = new AtomicInteger(0);
		tenantHandlingFacade=Mockito.mock(TenantHandlingFacade.class);
		mockConcurrencyHelperStart(a);
		concurrencyHelper.tenantHandlingFacade=tenantHandlingFacade;
		when(tenantHandlingFacade.isTenantIdNullForCurrentThread()).thenReturn(true);
//		doNothing().when(tenantHandlingFacade).removeTenantId();

        int result = concurrencyHelper.parallelProcessQueueItems(queue, supplier, executor, numberOfThreads, size);
        assertEquals(1, result);
	}

	protected void mockConcurrencyHelperStart(AtomicInteger a) {
		concurrencyHelper = new ConcurrencyHelper(tenantHandlingFacade,propertiesFacade,null){
			@Override
			public AtomicInteger startProcessingAndGetProcessedCount(
					ExecutorService executor, int numberOfThreads,
					Callable<Integer> cacheEntryCreationRunnable, Callable<Integer> mainCallable, int queueSize) {
				a.getAndIncrement();
				return a;
			}
		};
		concurrencyHelper.setTenantHandlingFacade(mock(TenantHandlingFacade.class));
	}
	
	@Test
	public void testparallelProcessQueueItemsForNullAndNotNullQueueEntries(){
		tenantHandlingFacade=Mockito.mock(TenantHandlingFacade.class);
		concurrencyHelper.tenantHandlingFacade=tenantHandlingFacade;
		when(tenantHandlingFacade.isTenantIdNullForCurrentThread()).thenReturn(true);
//		doNothing().when(tenantHandlingFacade).removeTenantId();
		Queue<String> queue = new LinkedList<>();
		queue.add(null);
		queue.add("Sample");
		MockExecutorService executor = new MockExecutorService();
		assertTrue(concurrencyHelper.parallelProcessQueueItems(queue, str->true, executor, 1, queue.size()) == 1);
	}

	@Test
	public void testParallelProcessQueueItemsForReturnFalse(){
		tenantHandlingFacade=Mockito.mock(TenantHandlingFacade.class);
		concurrencyHelper.tenantHandlingFacade=tenantHandlingFacade;
		when(tenantHandlingFacade.isTenantIdNullForCurrentThread()).thenReturn(true);
//		doNothing().when(tenantHandlingFacade).removeTenantId();
		Queue<String> queue = new LinkedList<>();
		queue.add("Sample1");
		queue.add("Sample2");
		MockExecutorService executor = new MockExecutorService();
		assertTrue(concurrencyHelper.parallelProcessQueueItems(queue, str->false, executor, 1, queue.size()) == 2);
	}
	
	@Test
	public void testparallelProcessQueueItemsException(){
		tenantHandlingFacade=Mockito.mock(TenantHandlingFacade.class);
		concurrencyHelper.tenantHandlingFacade=tenantHandlingFacade;
		when(tenantHandlingFacade.isTenantIdNullForCurrentThread()).thenReturn(true);
//		doNothing().when(tenantHandlingFacade).removeTenantId();
		Queue<String> queue = new LinkedList<>();
		queue.add(null);
		queue.add("Sample");
		MockExecutorService executor = new MockExecutorService();
		assertTrue(concurrencyHelper.parallelProcessQueueItems(queue, str-> dummyMethod(str), executor, 1, queue.size()) == 1);
	}
	
	private Boolean dummyMethod(String str) {
		try{
			return dummy(str);
		} catch(IOException e){
			throw new UncheckedIOException(e);
		}
	}

	private Boolean dummy(String str) throws IOException{
		throw new IOException();
	}

	@Test
	public void testParallelProcessQueueItemsForMultiGet(){
		AtomicInteger a =new AtomicInteger(0);
		Queue<String> criteriaQueue = new ConcurrentLinkedQueue<String>();
		Function<String,Boolean> processingFunction=str->str.isEmpty();
		int size=4;
		concurrencyHelper = new ConcurrencyHelper(tenantHandlingFacade,propertiesFacade,null){
			public <T> int parallelProcessQueueItems(Queue<T> queue, Function<T, Boolean> processingFunction,int size) {
				a.getAndIncrement();
				return 1;
			}
		};
		concurrencyHelper.parallelProcessQueueItemsForMultiGet(criteriaQueue, processingFunction, size);
		assertEquals(1,a.get());
	}
	
	@Test
	public void testStartProcessingAndGetProcessedCount(){
		MockExecutorService executor=new MockExecutorService();
		int numberOfThreads=3;
		int queSize = 4;
		Callable<Integer> cacheEntryCreationRunnable=()->1;
		AtomicInteger ai = concurrencyHelper.startProcessingAndGetProcessedCount(executor, numberOfThreads, cacheEntryCreationRunnable, ()->1, queSize);
		assertEquals(4, ai.get());
		
		//O tasks need to be created, hence only main thread is working
		numberOfThreads = 0;
		ai = concurrencyHelper.startProcessingAndGetProcessedCount(executor, numberOfThreads, cacheEntryCreationRunnable, ()->2, queSize);
		assertEquals(2, ai.get());
		
		//1 task needs to be created, hence only main thread is sufficient here
		numberOfThreads = 1;
		ai = concurrencyHelper.startProcessingAndGetProcessedCount(executor, numberOfThreads, cacheEntryCreationRunnable, ()->1, queSize);
		assertEquals(2, ai.get());
	}
	@Test
	public void testCompareTaskCountWithSize(){
		
		assertEquals(2,concurrencyHelper.compareTaskCountWithSize(4, 2));
		assertEquals(5,concurrencyHelper.compareTaskCountWithSize(5, 6));
		assertEquals(5,concurrencyHelper.compareTaskCountWithSize(5, 5));
	}
	
	@Test
	public void testGetQueueFromList(){
		List<String> listOfStrings = new ArrayList<>();
		listOfStrings.add("First");
		listOfStrings.add("Second");
		Queue<String> queue = concurrencyHelper.getQueueFromList(listOfStrings);
		assertEquals("First", queue.poll());
		assertEquals("Second", queue.poll());
		assertNull(queue.poll());
	}
	
	@Test
	public void testSingleItemTest() {
		AtomicInteger startCalledAI = new AtomicInteger();
		mockConcurrencyHelperStart(startCalledAI);
		Queue<Long> queue = new ConcurrentLinkedQueue<>();
		for (Long l =0l; l<100L; l++) {
			queue.add(l);
		}
		AtomicLong al = new AtomicLong();
		concurrencyHelper.parallelProcessQueueItems(queue, l->{al.incrementAndGet();return true;}, null, 5, 1);
		assertEquals(1, startCalledAI.get());
		queue.clear();
		queue.add(1L);
		startCalledAI.set(0);
		int val = concurrencyHelper.parallelProcessQueueItems(queue, l->{al.incrementAndGet();return true;}, null, 5, 1);
		assertEquals(0, startCalledAI.get());
		assertEquals (1, val);
		
		queue.add(1L);
		startCalledAI.set(0);
		val = concurrencyHelper.parallelProcessQueueItems(queue, l->{al.incrementAndGet();return false;}, null, 5, 1);
		assertEquals(0, startCalledAI.get());
		assertEquals (0, val);
		
	}
}
