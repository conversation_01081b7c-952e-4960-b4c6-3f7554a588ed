package com.kronos.people.personality.service.impl;

import static com.kronos.people.personality.model.extension.entry.positions.PositionStatusEntry.ACTIVE;
import static java.util.Collections.singletonList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.kronos.people.personality.properties.TracePropertyHelper;

import com.kronos.people.personality.converter.PositionConverter;
import com.kronos.people.personality.dataaccess.adapter.ExtensionAdapterEnum;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import com.kronos.people.personality.facade.AllExtensionsFacade;
import com.kronos.people.personality.facade.ExtensionFacade;
import com.kronos.people.personality.model.Criteria;
import com.kronos.people.personality.model.IdentifierType;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.AccrualExtension;
import com.kronos.people.personality.model.extension.AllExtension;
import com.kronos.people.personality.model.extension.BaseExtension;
import com.kronos.people.personality.model.extension.DevicesExtension;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.SchedulingExtension;
import com.kronos.people.personality.model.extension.TimekeepingExtension;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.positions.PositionEntry;
import com.kronos.people.personality.model.extension.entry.positions.PositionOrderEntry;
import com.kronos.people.personality.model.extension.entry.positions.PositionStatusEntry;
import com.kronos.people.proxy.api.dto.GenericObjectRef;
import com.kronos.people.proxy.api.dto.GenericPosition;
import com.kronos.people.proxy.api.dto.PositionLaborAccount;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PersonalityServiceImpMicroTest {

	PersonalityServiceImpl perSer;
	ExtensionFacade extFac;
	AllExtensionsFacade allExtFac;
	PositionConverter positionConverter;

	@BeforeEach
	public void setUp() {
		perSer = new PersonalityServiceImpl();
		extFac = mock(ExtensionFacade.class);
		allExtFac = mock(AllExtensionsFacade.class);
		positionConverter = mock(PositionConverter.class);
		perSer.setExtensionFacade(extFac);
		perSer.setAllExtensionsFacade(allExtFac);
		perSer.setPositionConverter(positionConverter);
		perSer.setTracePropertyHelper(mock(TracePropertyHelper.class));
	}

	private PersonalityResponse<BaseExtension> getEmployeeExtData(LocalDate snapShotDate) {

		EmployeeExtension ex = new EmployeeExtension();
//		ex.setPersonId(Long.valueOf(123l));
		ex.setPersonId(123L);
//		PersonalityResponse<BaseExtension> perResult = new PersonalityResponse<>(ex, null);
        return new PersonalityResponse<>(ex, null);
	}

	private Map<Long, PersonalityResponse<BaseExtension>> getEmployeesData(LocalDate snapShotDate) {
		EmployeeExtension ex = new EmployeeExtension();
		ex.setPersonId(123L);

		PersonalityResponse<BaseExtension> perResult1 = new PersonalityResponse<>(ex,null);

		PersonalityResponse<BaseExtension> perResult2 = new PersonalityResponse<>(null, new PersonalityExtensionException(PersonalityErrorCode.FULL_NAME_NOT_VALID, "Not Valid"));

//		Map<Long, PersonalityResponse<BaseExtension>> mp = new HashMap<Long, PersonalityResponse<BaseExtension>>();
		Map<Long, PersonalityResponse<BaseExtension>> mp = new HashMap<>();
		mp.put(123L, perResult1);
		mp.put(1234L, perResult2);
		return mp;
	}

	private Map<Object, PersonalityResponse<BaseExtension>> getEmployeesCriteriaData(LocalDate snapShotDate) {

		EmployeeExtension ex = new EmployeeExtension();
		ex.setPersonId(123L);

		EmployeeExtension ex1 = new EmployeeExtension();
		ex1.setPersonId(1234L);

		PersonalityResponse<BaseExtension> perResult1 = new PersonalityResponse<>(ex,null);

		PersonalityResponse<BaseExtension> perResult2 = new PersonalityResponse<>(ex1,null);

//		Map<Object, PersonalityResponse<BaseExtension>> mp = new HashMap<Object, PersonalityResponse<BaseExtension>>();
		Map<Object, PersonalityResponse<BaseExtension>> mp = new HashMap<>();
		mp.put(123L, perResult1);
		mp.put(1234L, perResult2);
		return mp;
	}

	@Test
	public void findEmployeeExtensionByPersonIdTest() {

		Long personId = 123L;

		when(extFac.handlePersonId(personId, ExtensionAdapterEnum.EMPLOYEE)).thenReturn(getEmployeeExtData(null));

		PersonalityResponse<EmployeeExtension> perResp = perSer.findEmployeeExtension(personId);

		assertEquals(personId, perResp.getExtension().getPersonId());

	}

	@Test
	public void findEmployeeExtensionByPerIdAndSnapShotDateTest() {
		Long personId = 123L;
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		when(extFac.handlePersonId(personId, snapShotDate, ExtensionAdapterEnum.EMPLOYEE)).thenReturn(getEmployeeExtData(snapShotDate));
		PersonalityResponse<EmployeeExtension> perResp = perSer.findEmployeeExtension(personId, snapShotDate);
		assertEquals(personId, perResp.getExtension().getPersonId());

	}

	@Test
	public void findEmployeeExtensionsByPersonIdsTest() {
		Long[] personIds = {123L, 1234L};

		when(extFac.handlePersonIds(personIds, ExtensionAdapterEnum.EMPLOYEE)).thenReturn(getEmployeesData(null));
		Map<Long, PersonalityResponse<EmployeeExtension>> map = perSer.findEmployeeExtensions(personIds);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertNull(map.get(1234L).getExtension());
		assertEquals( "Not Valid", map.get(1234L).getException().getMessage());

	}

	@Test
	public void findEmployeeExtensionsByPerAndSnapShotDateTest() {
		Long[] personIds = {123L, 1234L};
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		when(extFac.handlePersonIds(personIds, snapShotDate, ExtensionAdapterEnum.EMPLOYEE)).thenReturn(getEmployeesData(snapShotDate));
		Map<Long, PersonalityResponse<EmployeeExtension>> map = perSer.findEmployeeExtensions(personIds, snapShotDate);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertNull(map.get(1234L).getExtension());
		assertEquals( "Not Valid", map.get(1234L).getException().getMessage());
	}

	@Test
	public void findEmployeeExtensionsByCriteriaTest() {
		Long[] personIds = {123L, 1234L};
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		Criteria cr = new Criteria(personIds, IdentifierType.PERSONID);
		when(extFac.handleCriteria(cr, ExtensionAdapterEnum.EMPLOYEE)).thenReturn(getEmployeesCriteriaData(snapShotDate));

		Map<Object, PersonalityResponse<EmployeeExtension>> map = perSer.findEmployeeExtensionsByCriteria(cr);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());
	}

	private PersonalityResponse<BaseExtension> getTimeKeepingExtData(LocalDate snapShotDate) {
		TimekeepingExtension ex = new TimekeepingExtension();
		ex.setPersonId(123L);
        return new PersonalityResponse<>(ex,null);
	}

	private Map<Long, PersonalityResponse<BaseExtension>> getTimeKeepingExtsData(LocalDate snapShotDate) {
		TimekeepingExtension ex = new TimekeepingExtension();
		ex.setPersonId(123L);

		TimekeepingExtension ex1 = new TimekeepingExtension();
		ex1.setPersonId(1234L);

		PersonalityResponse<BaseExtension> perResult1 = new PersonalityResponse<>(ex,null);

		PersonalityResponse<BaseExtension> perResult2 = new PersonalityResponse<>(ex1,null);

//		Map<Long, PersonalityResponse<BaseExtension>> mp = new HashMap<Long, PersonalityResponse<BaseExtension>>();
		Map<Long, PersonalityResponse<BaseExtension>> mp = new HashMap<>();
		mp.put(123L, perResult1);
		mp.put(1234L, perResult2);
		return mp;
	}

	private Map<Object, PersonalityResponse<BaseExtension>> getTimeKeepingExtCrData(LocalDate snapShotDate) {

		TimekeepingExtension ex = new TimekeepingExtension();
		ex.setPersonId(123L);

		TimekeepingExtension ex1 = new TimekeepingExtension();
		ex1.setPersonId(1234L);

		PersonalityResponse<BaseExtension> perResult1 = new PersonalityResponse<>(ex,null);

		PersonalityResponse<BaseExtension> perResult2 = new PersonalityResponse<>(ex1,null);

//		Map<Object, PersonalityResponse<BaseExtension>> mp = new HashMap<Object, PersonalityResponse<BaseExtension>>();
		Map<Object, PersonalityResponse<BaseExtension>> mp = new HashMap<>();
		mp.put(123L, perResult1);
		mp.put(1234L, perResult2);
		return mp;
	}

	@Test
	public void findTimekeepingExtensionByPersonIdTest() {

		Long personId = 123L;

		when(extFac.handlePersonId(personId, ExtensionAdapterEnum.TIMEKEEPING)).thenReturn(getTimeKeepingExtData(null));

		PersonalityResponse<TimekeepingExtension> perResp = perSer.findTimekeepingExtension(personId);

		assertEquals(personId, perResp.getExtension().getPersonId());
	}

	@Test
	public void findTimeKeepExtensionByPerIdAndSnapShotDateTest() {
		Long personId = 123L;
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		when(extFac.handlePersonId(personId, snapShotDate, ExtensionAdapterEnum.TIMEKEEPING)).thenReturn(getTimeKeepingExtData(snapShotDate));
		PersonalityResponse<TimekeepingExtension> perResp = perSer.findTimekeepingExtension(personId, snapShotDate);
		assertEquals(personId, perResp.getExtension().getPersonId());

	}

	@Test
	public void findTimeKeepExtensionsByPersonIdsTest() {
		Long[] personIds = {123L, 1234L};

		when(extFac.handlePersonIds(personIds, ExtensionAdapterEnum.TIMEKEEPING)).thenReturn(getTimeKeepingExtsData(null));
		Map<Long, PersonalityResponse<TimekeepingExtension>> map = perSer.findTimekeepingExtensions(personIds);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());

	}

	@Test
	public void findTimeKeepExtensionsByPerAndSnapShotDateTest() {
		Long[] personIds = {123L, 1234L};
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		when(extFac.handlePersonIds(personIds, snapShotDate, ExtensionAdapterEnum.TIMEKEEPING)).thenReturn(getTimeKeepingExtsData(snapShotDate));
		Map<Long, PersonalityResponse<TimekeepingExtension>> map = perSer.findTimekeepingExtensions(personIds, snapShotDate);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());
	}

	@Test
	public void findTimeKeepExtensionsByCriteriaTest() {
		Long[] personIds = {123L, 1234L};
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		Criteria cr = new Criteria(personIds, IdentifierType.PERSONID);
		when(extFac.handleCriteria(cr, ExtensionAdapterEnum.TIMEKEEPING)).thenReturn(getTimeKeepingExtCrData(snapShotDate));

		Map<Object, PersonalityResponse<TimekeepingExtension>> map = perSer.findTimekeepingExtensionsByCriteria(cr);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());
	}

	private PersonalityResponse<BaseExtension> getSchExtData(LocalDate snapShotDate) {
		SchedulingExtension ex = new SchedulingExtension();
		ex.setPersonId(123L);
        return new PersonalityResponse<>(ex,null);
	}

	private Map<Long, PersonalityResponse<BaseExtension>> getSchExtsData(LocalDate snapShotDate) {
		SchedulingExtension ex = new SchedulingExtension();
		ex.setPersonId(123L);

		SchedulingExtension ex1 = new SchedulingExtension();
		ex1.setPersonId(1234L);

		PersonalityResponse<BaseExtension> perResult1 = new PersonalityResponse<>(ex,null);

		PersonalityResponse<BaseExtension> perResult2 = new PersonalityResponse<>(ex1,null);

//		Map<Long, PersonalityResponse<BaseExtension>> mp = new HashMap<Long, PersonalityResponse<BaseExtension>>();
		Map<Long, PersonalityResponse<BaseExtension>> mp = new HashMap<>();
		mp.put(123L, perResult1);
		mp.put(1234L, perResult2);
		return mp;
	}

	private Map<Object, PersonalityResponse<BaseExtension>> getSchCrData(LocalDate snapShotDate) {

		SchedulingExtension ex = new SchedulingExtension();
		ex.setPersonId(123L);

		SchedulingExtension ex1 = new SchedulingExtension();
		ex1.setPersonId(1234L);

		PersonalityResponse<BaseExtension> perResult1 = new PersonalityResponse<>(ex,null);

		PersonalityResponse<BaseExtension> perResult2 = new PersonalityResponse<>(ex1,null);

//		Map<Object, PersonalityResponse<BaseExtension>> mp = new HashMap<Object, PersonalityResponse<BaseExtension>>();
		Map<Object, PersonalityResponse<BaseExtension>> mp = new HashMap<>();
		mp.put(123L, perResult1);
		mp.put(1234L, perResult2);
		return mp;
	}

	@Test
	public void findSchByPersonIdTest() {

		Long personId = 123L;

		when(extFac.handlePersonId(personId, ExtensionAdapterEnum.SCHEDULING)).thenReturn(getSchExtData(null));

		PersonalityResponse<SchedulingExtension> perResp = perSer.findSchedulingExtension(personId);

		assertEquals(personId, perResp.getExtension().getPersonId());
	}

	@Test
	public void findSchByPerIdAndSnapShotDateTest() {
		Long personId = 123L;
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		when(extFac.handlePersonId(personId, snapShotDate, ExtensionAdapterEnum.SCHEDULING)).thenReturn(getSchExtData(snapShotDate));
		PersonalityResponse<SchedulingExtension> perResp = perSer.findSchedulingExtension(personId, snapShotDate);
		assertEquals(personId, perResp.getExtension().getPersonId());

	}

	@Test
	public void findSchByPersonIdsTest() {
		Long[] personIds = {123L, 1234L};

		when(extFac.handlePersonIds(personIds, ExtensionAdapterEnum.SCHEDULING)).thenReturn(getSchExtsData(null));
		Map<Long, PersonalityResponse<SchedulingExtension>> map = perSer.findSchedulingExtensions(personIds);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());

	}

	@Test
	public void findSchByPerAndSnapShotDateTest() {
		Long[] personIds = {123L, 1234L};
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		when(extFac.handlePersonIds(personIds, snapShotDate, ExtensionAdapterEnum.SCHEDULING)).thenReturn(getSchExtsData(snapShotDate));
		Map<Long, PersonalityResponse<SchedulingExtension>> map = perSer.findSchedulingExtensions(personIds, snapShotDate);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());
	}

	@Test
	public void findSchByCriteriaTest() {
		Long[] personIds = {123L, 1234L};
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		Criteria cr = new Criteria(personIds, IdentifierType.PERSONID);
		when(extFac.handleCriteria(cr, ExtensionAdapterEnum.SCHEDULING)).thenReturn(getSchCrData(snapShotDate));

		Map<Object, PersonalityResponse<SchedulingExtension>> map = perSer.findSchedulingExtensionsByCriteria(cr);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());
	}

	private PersonalityResponse<BaseExtension> getAcrExtData(LocalDate snapShotDate) {
		AccrualExtension ex = new AccrualExtension();
		ex.setPersonId(123L);
        return new PersonalityResponse<>(ex,null);
	}

	private Map<Long, PersonalityResponse<BaseExtension>> getAcrExtsData(LocalDate snapShotDate) {
		AccrualExtension ex = new AccrualExtension();
		ex.setPersonId(123L);

		AccrualExtension ex1 = new AccrualExtension();
		ex1.setPersonId(1234L);

		PersonalityResponse<BaseExtension> perResult1 = new PersonalityResponse<>(ex,null);

		PersonalityResponse<BaseExtension> perResult2 = new PersonalityResponse<>(ex1,null);

//		Map<Long, PersonalityResponse<BaseExtension>> mp = new HashMap<Long, PersonalityResponse<BaseExtension>>();
		Map<Long, PersonalityResponse<BaseExtension>> mp = new HashMap<>();
		mp.put(123L, perResult1);
		mp.put(1234L, perResult2);
		return mp;
	}

	private Map<Object, PersonalityResponse<BaseExtension>> getAcrCrData(LocalDate snapShotDate) {

		AccrualExtension ex = new AccrualExtension();
		ex.setPersonId(123L);

		AccrualExtension ex1 = new AccrualExtension();
		ex1.setPersonId(1234L);

		PersonalityResponse<BaseExtension> perResult1 = new PersonalityResponse<>(ex,null);

		PersonalityResponse<BaseExtension> perResult2 = new PersonalityResponse<>(ex1,null);

//		Map<Object, PersonalityResponse<BaseExtension>> mp = new HashMap<Object, PersonalityResponse<BaseExtension>>();
		Map<Object, PersonalityResponse<BaseExtension>> mp = new HashMap<>();
		mp.put(123L, perResult1);
		mp.put(1234L, perResult2);
		return mp;
	}

	@Test
	public void findAcrByPersonIdTest() {

		Long personId = 123L;

		when(extFac.handlePersonId(personId, ExtensionAdapterEnum.ACCRUAL)).thenReturn(getAcrExtData(null));

		PersonalityResponse<AccrualExtension> perResp = perSer.findAccrualExtension(personId);

		assertEquals(personId, perResp.getExtension().getPersonId());
	}

	@Test
	public void findAcrByPerIdAndSnapShotDateTest() {
		Long personId = 123L;
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		when(extFac.handlePersonId(personId, snapShotDate, ExtensionAdapterEnum.ACCRUAL)).thenReturn(getAcrExtData(snapShotDate));
		PersonalityResponse<AccrualExtension> perResp = perSer.findAccrualExtension(personId, snapShotDate);
		assertEquals(personId, perResp.getExtension().getPersonId());

	}

	@Test
	public void findAcrByPersonIdsTest() {
		Long[] personIds = {123L, 1234L};

		when(extFac.handlePersonIds(personIds, ExtensionAdapterEnum.ACCRUAL)).thenReturn(getAcrExtsData(null));
		Map<Long, PersonalityResponse<AccrualExtension>> map = perSer.findAccrualExtensions(personIds);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());

	}

	@Test
	public void findAcrByPerAndSnapShotDateTest() {
		Long[] personIds = {123L, 1234L};
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		when(extFac.handlePersonIds(personIds, snapShotDate, ExtensionAdapterEnum.ACCRUAL)).thenReturn(getAcrExtsData(snapShotDate));
		Map<Long, PersonalityResponse<AccrualExtension>> map = perSer.findAccrualExtensions(personIds, snapShotDate);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());
	}

	@Test
	public void findAcrByCriteriaTest() {
		Long[] personIds = {123L, 1234L};
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		Criteria cr = new Criteria(personIds, IdentifierType.PERSONID);
		when(extFac.handleCriteria(cr, ExtensionAdapterEnum.ACCRUAL)).thenReturn(getAcrCrData(snapShotDate));

		Map<Object, PersonalityResponse<AccrualExtension>> map = perSer.findAccrualExtensionsByCriteria(cr);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());
	}

	private PersonalityResponse<BaseExtension> getDeviceExtData(LocalDate snapShotDate) {
		DevicesExtension ex = new DevicesExtension();
		ex.setPersonId(123L);
        return new PersonalityResponse<>(ex,null);
	}

	private Map<Long, PersonalityResponse<BaseExtension>> getDeviceExtsData(LocalDate snapShotDate) {
		DevicesExtension ex = new DevicesExtension();
		ex.setPersonId(123L);

		DevicesExtension ex1 = new DevicesExtension();
		ex1.setPersonId(1234L);

		PersonalityResponse<BaseExtension> perResult1 = new PersonalityResponse<>(ex,null);

		PersonalityResponse<BaseExtension> perResult2 = new PersonalityResponse<>(ex1,null);

//		Map<Long, PersonalityResponse<BaseExtension>> mp = new HashMap<Long, PersonalityResponse<BaseExtension>>();
		Map<Long, PersonalityResponse<BaseExtension>> mp = new HashMap<>();
		mp.put(123L, perResult1);
		mp.put(1234L, perResult2);
		return mp;
	}

	private Map<Object, PersonalityResponse<BaseExtension>> getDeviceCrData(LocalDate snapShotDate) {

		DevicesExtension ex = new DevicesExtension();
		ex.setPersonId(123L);

		DevicesExtension ex1 = new DevicesExtension();
		ex1.setPersonId(1234L);

		PersonalityResponse<BaseExtension> perResult1 = new PersonalityResponse<>(ex,null);

		PersonalityResponse<BaseExtension> perResult2 = new PersonalityResponse<>(ex1,null);

//		Map<Object, PersonalityResponse<BaseExtension>> mp = new HashMap<Object, PersonalityResponse<BaseExtension>>();
		Map<Object, PersonalityResponse<BaseExtension>> mp = new HashMap<>();
		mp.put(123L, perResult1);
		mp.put(1234L, perResult2);
		return mp;
	}

	@Test
	public void findDeviceByPersonIdTest() {

		Long personId = 123L;

		when(extFac.handlePersonId(personId, ExtensionAdapterEnum.DEVICES)).thenReturn(getDeviceExtData(null));

		PersonalityResponse<DevicesExtension> perResp = perSer.findDeviceExtension(personId);

		assertEquals(personId, perResp.getExtension().getPersonId());
	}

	@Test
	public void findDeviceByPerIdAndSnapShotDateTest() {
		Long personId = 123L;
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		when(extFac.handlePersonId(personId, snapShotDate, ExtensionAdapterEnum.DEVICES)).thenReturn(getDeviceExtData(snapShotDate));
		PersonalityResponse<DevicesExtension> perResp = perSer.findDeviceExtension(personId, snapShotDate);
		assertEquals(personId, perResp.getExtension().getPersonId());

	}

	@Test
	public void findDeviceByPersonIdsTest() {
		Long[] personIds = {123L, 1234L};

		when(extFac.handlePersonIds(personIds, ExtensionAdapterEnum.DEVICES)).thenReturn(getDeviceExtsData(null));
		Map<Long, PersonalityResponse<DevicesExtension>> map = perSer.findDeviceExtensions(personIds);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());

	}

	@Test
	public void findDeviceByPerAndSnapShotDateTest() {
		Long[] personIds = {123L, 1234L};
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		when(extFac.handlePersonIds(personIds, snapShotDate, ExtensionAdapterEnum.DEVICES)).thenReturn(getDeviceExtsData(snapShotDate));
		Map<Long, PersonalityResponse<DevicesExtension>> map = perSer.findDeviceExtensions(personIds, snapShotDate);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());
	}

	@Test
	public void findDeviceByCriteriaTest() {
		Long[] personIds = {123L, 1234L};
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		Criteria cr = new Criteria(personIds, IdentifierType.PERSONID);
		when(extFac.handleCriteria(cr, ExtensionAdapterEnum.DEVICES)).thenReturn(getDeviceCrData(snapShotDate));

		Map<Object, PersonalityResponse<DevicesExtension>> map = perSer.findDeviceExtensionsByCriteria(cr);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());
	}

	private PersonalityResponse<AllExtension> getAllExtData(LocalDate snapShotDate) {

		AllExtension ex = new AllExtension();
		ex.setPersonId(123L);
        return new PersonalityResponse<>(ex,null);
	}

	private Map<Long, PersonalityResponse<AllExtension>> getAllExtsData(LocalDate snapShotDate) {

		AllExtension ex = new AllExtension();
		ex.setPersonId(123L);

		AllExtension ex1 = new AllExtension();
		ex1.setPersonId(1234L);

		PersonalityResponse<AllExtension> perResult1 = new PersonalityResponse<>(ex,null);

		PersonalityResponse<AllExtension> perResult2 = new PersonalityResponse<>(ex1,null);

//		Map<Long, PersonalityResponse<AllExtension>> mp = new HashMap<Long, PersonalityResponse<AllExtension>>();
		Map<Long, PersonalityResponse<AllExtension>> mp = new HashMap<>();
		mp.put(123L, perResult1);
		mp.put(1234L, perResult2);
		return mp;
	}

	private Map<Object, PersonalityResponse<AllExtension>> getAllExtCrData(LocalDate snapShotDate) {

		AllExtension ex = new AllExtension();
		ex.setPersonId(123L);

		AllExtension ex1 = new AllExtension();
		ex1.setPersonId(1234L);

		PersonalityResponse<AllExtension> perResult1 = new PersonalityResponse<>(ex,null);

		PersonalityResponse<AllExtension> perResult2 = new PersonalityResponse<>(ex1,null);

//		Map<Object, PersonalityResponse<AllExtension>> mp = new HashMap<Object, PersonalityResponse<AllExtension>>();
		Map<Object, PersonalityResponse<AllExtension>> mp = new HashMap<>();
		mp.put(123L, perResult1);
		mp.put(1234L, perResult2);
		return mp;
	}

	@Test
	public void findAllByPersonIdTest() {

		Long personId = 123L;

		when(allExtFac.findAllExtensions(personId)).thenReturn(getAllExtData(null));

		PersonalityResponse<AllExtension> perResp = perSer.findAllExtensions(personId);

		assertEquals(personId, perResp.getExtension().getPersonId());
	}

	@Test
	public void findAllExtByPerIdAndSnapShotDateTest() {
		Long personId = 123L;
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		when(allExtFac.findAllExtensions(personId, snapShotDate)).thenReturn(getAllExtData(snapShotDate));
		PersonalityResponse<AllExtension> perResp = perSer.findAllExtensions(personId, snapShotDate);
		assertEquals(personId, perResp.getExtension().getPersonId());

	}

	@Test
	public void findAllExtByPersonIdsTest() {
		Long[] personIds = {123L, 1234L};

		when(allExtFac.findAllExtensions(personIds)).thenReturn(getAllExtsData(null));
		Map<Long, PersonalityResponse<AllExtension>> map = perSer.findAllExtensions(personIds);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());

	}

	@Test
	public void findAllExtByPerAndSnapShotDateTest() {
		Long[] personIds = {123L, 1234L};
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		when(allExtFac.findAllExtensions(personIds, snapShotDate)).thenReturn(getAllExtsData(snapShotDate));
		Map<Long, PersonalityResponse<AllExtension>> map = perSer.findAllExtensions(personIds, snapShotDate);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());
	}

	@Test
	public void findAllExtByCriteriaTest() {
		Long[] personIds = {123L, 1234L};
		LocalDate snapShotDate = LocalDate.of(2015, 3, 9);

		Criteria cr = new Criteria(personIds, IdentifierType.PERSONID);
		when(allExtFac.findAllExtensionsByCriteria(cr)).thenReturn(getAllExtCrData(snapShotDate));

		Map<Object, PersonalityResponse<AllExtension>> map = perSer.findAllExtensionsByCriteria(cr);

		assertEquals(Long.valueOf(123L), map.get(123L).getExtension().getPersonId());
		assertEquals(Long.valueOf(1234L), map.get(1234L).getExtension().getPersonId());
	}
	
	@Test
	public void testIfPersonIsManager() {
		Long personId = 123L;
		when(extFac.checkIfPersonIsManager()).thenReturn(true);
		when(extFac.checkIfPersonIsManager(personId)).thenReturn(true);
		assertTrue(perSer.isManager());
		assertTrue(perSer.isManager(personId));
	}
	
	@Test
	public void testIfPersonIsEmployee() {
		Long personId = 123L;
		when(extFac.checkIfPersonIsEmployee()).thenReturn(true);
		when(extFac.checkIfPersonIsEmployee(personId)).thenReturn(true);
		assertTrue(perSer.isEmployee());
		assertTrue(perSer.isEmployee(personId));
	}
	@Test
	public void testGetAssignedJobAndLocation(){
		Long positionId = 238L;
		Long employeeId = 234L;
		String date = "06/22/2022";

		List<PositionLaborAccount> positionLaborAccounts = new ArrayList<>();
		PositionLaborAccount positionLaborAccount = new PositionLaborAccount();
		LocalDate sDate = LocalDate.parse("2021-05-01");
		LocalDate eDate = LocalDate.parse("2023-05-02");
		positionLaborAccount.setEffectiveDate(sDate);
		positionLaborAccount.setExpirationDate(eDate);
		GenericObjectRef obj = new GenericObjectRef();
		obj.setQualifier("aaa/bbb/ccc");
		positionLaborAccount.setPrimaryJob(obj);
		positionLaborAccounts.add(positionLaborAccount);

		GenericPosition position = new GenericPosition();
		position.setPersonId(employeeId);
		position.setLaborAccounts(positionLaborAccounts);
		position.setId(positionId);

		when(extFac.handlePersonId(employeeId, ExtensionAdapterEnum.EMPLOYEE)).thenReturn(getEmployeeExtData1());
		when(positionConverter.convertPositionEntry(any(), any())).thenReturn(position);
		Map<String, String> jobLocation = perSer.getAssignedJobAndLocation(positionId,employeeId,date);
		assertEquals("ccc",jobLocation.get("EmployeeJob"));
		assertEquals("aaa/bbb",jobLocation.get("EmployeeLocation"));
	}

	@Test
	public void testGetAssignedJobAndLocation_whenPositionIsNotExist(){
		Long positionId = 238L;
		Long employeeId = 234L;
		String date = "06/22/2022";

		List<PositionLaborAccount> positionLaborAccounts = new ArrayList<>();
		PositionLaborAccount positionLaborAccount = new PositionLaborAccount();
		LocalDate sDate = LocalDate.parse("2021-05-01");
		LocalDate eDate = LocalDate.parse("2023-05-02");
		positionLaborAccount.setEffectiveDate(sDate);
		positionLaborAccount.setExpirationDate(eDate);
		GenericObjectRef obj = new GenericObjectRef();
		obj.setQualifier("aaa/bbb/ccc");
		positionLaborAccount.setPrimaryJob(obj);
		positionLaborAccounts.add(positionLaborAccount);
		when(extFac.handlePersonId(employeeId, ExtensionAdapterEnum.EMPLOYEE)).thenReturn(getEmployeeExtDataWithoutPosition());

		Map<String, String> jobLocation = perSer.getAssignedJobAndLocation(positionId,employeeId,date);

		assertTrue(jobLocation.isEmpty());
	}

	private PersonalityResponse<BaseExtension> getEmployeeExtData1() {
		EmployeeExtension ex = new EmployeeExtension();
		LocalDate effectiveDate = LocalDate.parse("2021-05-01");
		LocalDate expirationDate = LocalDate.parse("2023-05-02");
		EffectiveDatedCollection<PositionStatusEntry> effDatedPositionStatus = new EffectiveDatedCollection<>(singletonList(new PositionStatusEntry(ACTIVE, effectiveDate, expirationDate)));
		Collection<PositionEntry> positions = new ArrayList<>();
		PositionEntry positionEntry1 = new PositionEntry();
		positionEntry1.setPositionId(238L);
		positionEntry1.setEffDatedPositionStatus(effDatedPositionStatus);
		EffectiveDatedCollection<PositionOrderEntry> effDatedPositionOrder1 = new EffectiveDatedCollection<>(singletonList(new PositionOrderEntry(2, false, effectiveDate, expirationDate)));
		positionEntry1.setEffDatedPositionOrder(effDatedPositionOrder1);
		positions.add(positionEntry1);
		ex.setPositions(positions);
//		ex.setPersonId(Long.valueOf(123l));
		ex.setPersonId(123L);
//		PersonalityResponse<BaseExtension> perResult = new PersonalityResponse<>(ex, null);
        return new PersonalityResponse<>(ex, null);
	}
	private PersonalityResponse<BaseExtension> getEmployeeExtDataWithoutPosition() {
		EmployeeExtension ex = new EmployeeExtension();
		LocalDate effectiveDate = LocalDate.parse("2021-05-01");
		LocalDate expirationDate = LocalDate.parse("2023-05-02");
		EffectiveDatedCollection<PositionStatusEntry> effDatedPositionStatus = new EffectiveDatedCollection<>(singletonList(new PositionStatusEntry(ACTIVE, effectiveDate, expirationDate)));
		Collection<PositionEntry> positions = new ArrayList<>();
		ex.setPositions(positions);
//		ex.setPersonId(Long.valueOf(123l));
		ex.setPersonId(123L);
//		PersonalityResponse<BaseExtension> perResult = new PersonalityResponse<>(ex, null);
        return new PersonalityResponse<>(ex, null);
	}
}
