/*******************************************************************************
 * ApprovalReviewersOvertimeServiceTest.java
 * Copyright © 2024 UKG Inc. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.service.impl.ia;

import com.kronos.commonbusiness.datatypes.ia.*;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.SchedulingExtension;
import com.kronos.people.personality.model.extension.entry.ApproverEntry;
import com.kronos.people.personality.service.PersonalityExtendedAttributesService;
import com.kronos.people.personality.service.PersonalityService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.samePropertyValuesAs;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * Class with unit tests for ApprovalReviewersOvertimeService.java
 */
@ExtendWith(MockitoExtension.class)
public class ApprovalReviewersOvertimeServiceTest {

    private static final String APPROVER_NAME = "Patricia Yang";
    private static final String APPROVAL_REVIEW_OVERTIME_APPROVAL_LEVEL = "APPRV_REVIEW_OVERTIME_APPROVAL_LEVEL";
    private static final String APPROVAL_LEVEL_ALIAS = "Approval level";
    private static final String APPROVAL_REVIEW_OVERTIME_APPROVER = "APPRV_REVIEW_OVERTIME_APPROVER";
    private static final String APPROVER_ALIAS = "Overtime approver";
    private static final String APPROVAL_REVIEW_OVERTIME_APPROVE_DAYS = "APPRV_REVIEW_OVERTIME_APPROVE_DAYS";
    private static final String APPROVE_DAYS_ALIAS = "Approve days";
    private static final String EMPLOYEE_ENTITY = "EMP";
    private static final IAColumn APPROVE_DAYS_IACOLUMN = new IAColumn(APPROVE_DAYS_ALIAS, APPROVAL_REVIEW_OVERTIME_APPROVE_DAYS);
    private static final IAColumn APPROVER_IACOLUMN = new IAColumn(APPROVER_ALIAS, APPROVAL_REVIEW_OVERTIME_APPROVER);
    private static final IAColumn APPROVAL_LEVEL_IACOLUMN = new IAColumn(APPROVAL_LEVEL_ALIAS, APPROVAL_REVIEW_OVERTIME_APPROVAL_LEVEL);

    @InjectMocks
    private ApprovalReviewersOvertimeService approvalReviewersOvertimeService;
    @Mock
    private PersonalityService personalityService;
    @Mock
    private PersonalityExtendedAttributesService personalityExtendedAttributesService;
    @InjectMocks
    private PersonalityAssignmentsServiceImpl personalityAssignmentsService;

    private List<String> employeeIds;
    private String personNumber;
    private IARequest request;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        employeeIds = new ArrayList<>();
        employeeIds.add("000001");
        personNumber = "000001";
        request = new IARequest();
    }

    @Test
    public void testGetEntityName() {
        String result = approvalReviewersOvertimeService.getEntityName();
        assertEquals("overtime_approvals", result);
    }

    @Disabled //String cannot be returned by handlePersonIds()handlePersonIds() should return Map
    @Test
    public void testGetAssignmentDataShouldAddRowToResponseWhenOneEmplyeeIDAndOneApproverEntryIsProvided() {
        when(approvalReviewersOvertimeService.getEntityName()).thenReturn("overtime_approvals1");
        IAResponse response = personalityAssignmentsService.getDataByPath("overtime_approvals", request);

        assertEquals(1, response.getData().size(), "Data should have 1 row when employeeIds has only 1 id and there is only 1 approverEntry");
        IARow resultIARow = response.getData().get(0);
        assertEquals(personNumber, resultIARow.getDimensionKey(), "Row DimensionKey should be same as personNumber");
    }

    @Test
    public void testGetAssignmentdataShouldNotAddDataToResponseWhenEmployeeIdsIsEmpty(){
        IAResponse iaResponse = new IAResponse();
        IARequest iaRequest = new IARequest();
        iaRequest.setColumns(buildRequestIAColumnList());
        Map<Long, PersonalityResponse<SchedulingExtension>> employeeIdsMap = Collections.emptyMap();
        Mockito.when(personalityService.findSchedulingExtensions(Mockito.any())).thenReturn(employeeIdsMap);

        IAResponse result = approvalReviewersOvertimeService.getAssignmentData(iaRequest, iaResponse,
                Collections.emptyList(), Collections.emptyList());

//        assertSame("Should return same IAResponse instance", iaResponse, result);
        assertSame(iaResponse, result, "Should return same IAResponse instance");
//        assertEquals("Response Data should be empty", 0, result.getData().size());
        assertEquals(0, result.getData().size(), "Response Data should be empty");
    }

    @Test
    public void testGetAssignmentdataShouldNotAddDataToResponseWhenPersonalityResponseHasException(){
        IARequest iaRequest = new IARequest();
        iaRequest.setColumns(buildRequestIAColumnList());
        IAResponse iaResponse = new IAResponse();
        String personNumber = "000001";
        long employeeId = 1001L;
        List<PersonalityExtensionException> extensionExceptions = new ArrayList<>();
        PersonalityExtensionException expectedPersonalityException =
                new PersonalityExtensionException(PersonalityErrorCode.UNKNOWN_ERROR, "Test Error");
        List<ApproverEntry> approverEntries = buildApproverEntryList(1);
        PersonalityResponse<SchedulingExtension> personalityResponse = buildMockPersonalityResponse(personNumber,
                approverEntries, expectedPersonalityException);
        Map<Long, PersonalityResponse<SchedulingExtension>> employeesExtensions = new HashMap<>();
        employeesExtensions.put(employeeId, personalityResponse);
        Mockito.when(personalityService.findSchedulingExtensions(Mockito.any())).thenReturn(employeesExtensions);

        IAResponse result = approvalReviewersOvertimeService.getAssignmentData(iaRequest, iaResponse,
                Collections.emptyList(), extensionExceptions);

//        assertSame("Should return same IAResponse instance", iaResponse, result);
        assertSame(iaResponse, result, "Should return same IAResponse instance");
//        assertEquals("Response Data should be empty", 0, result.getData().size());
        assertEquals(0, result.getData().size(), "Response Data should be empty");
//        assertEquals("Exceptions list shouldn't be empty", 1, extensionExceptions.size());
        assertEquals(1, extensionExceptions.size(), "Exceptions list shouldn't be empty");
        assertThat(extensionExceptions.get(0), samePropertyValuesAs(expectedPersonalityException));
    }

    @Test
    public void testPopulateColumnsShouldAddAttributeToRowWhenColumnNameIsValid() {
        IARow targetRow = new IARow();
        ApproverEntry approverEntry = buildMockApproverEntry(1);
        Map<Long, String>  approverfullNameMap = new HashMap<>();
        approverfullNameMap.put(1L, APPROVER_NAME);

        Consumer<IAColumn> result = approvalReviewersOvertimeService.populateColumns(targetRow, approverEntry, approverfullNameMap);
        result.accept(APPROVE_DAYS_IACOLUMN);
        result.accept(APPROVER_IACOLUMN);
        result.accept(APPROVAL_LEVEL_IACOLUMN);
        List<IARowAttribute> resultAttributes = targetRow.getAttributes();

//        assertEquals("Row should have 3 attributes when 3 columns are processed", 3, resultAttributes.size());
        assertEquals(3, resultAttributes.size(), "Row should have 3 attributes when 3 columns are processed");
        assertEquals(APPROVE_DAYS_ALIAS, resultAttributes.get(0).getName());
        assertEquals("1", resultAttributes.get(0).getValue());
        assertEquals(APPROVER_ALIAS, resultAttributes.get(1).getName());
        assertEquals(APPROVER_NAME, resultAttributes.get(1).getValue());
        assertEquals(APPROVAL_LEVEL_ALIAS, resultAttributes.get(2).getName());
        assertEquals("1", resultAttributes.get(2).getValue());
    }

    @Test
    public void testPopulateColumnsShouldNotAddAttributeWhenColumnNameIsNotValid() {
        IARow targetRow = new IARow();
        ApproverEntry approverEntry = buildMockApproverEntry(1);
        Map<Long, String>  approverfullNameMap = new HashMap<>();
        approverfullNameMap.put(1L, APPROVER_NAME);
        String testAlias = "Test alias";
        String lowerCaseColumnName = APPROVAL_REVIEW_OVERTIME_APPROVER.toLowerCase();
        String columnNameWithSpaces = "APPRV REVIEW OVERTIME APPROVER";

        Consumer<IAColumn> result = approvalReviewersOvertimeService.populateColumns(targetRow, approverEntry, approverfullNameMap);
        result.accept(new IAColumn(testAlias, ""));
        result.accept(new IAColumn(testAlias, lowerCaseColumnName));
        result.accept(new IAColumn(testAlias, columnNameWithSpaces));

//        assertTrue("No attribute should have been added when column name is invalid", targetRow.getAttributes().isEmpty());
        assertTrue(targetRow.getAttributes().isEmpty(), "No attribute should have been added when column name is invalid");
    }

    private ApproverEntry buildMockApproverEntry(int approverFieldsNumber){
        ApproverEntry approverEntry = new ApproverEntry();
        approverEntry.setOrderNum(approverFieldsNumber);
        approverEntry.setDueDateAmt(approverFieldsNumber);
        approverEntry.setEmployeeId((long) approverFieldsNumber);
        return approverEntry;
    }

    private List<ApproverEntry> buildApproverEntryList(int quantity){
        List<ApproverEntry> approverEntries = new ArrayList<>();
        for (int i = 1; i <= quantity; i++){
            approverEntries.add(buildMockApproverEntry(i));
        }

        return approverEntries;
    }

    private Map<Long, String> buildApproverNamesMapFromList(List<ApproverEntry> approverList){
        return approverList.stream().collect(Collectors.toMap(
                ApproverEntry::getEmployeeId,
                approver -> APPROVER_NAME + approver.getEmployeeId()
        ));
    }

    private PersonalityResponse<SchedulingExtension> buildMockPersonalityResponse(String personNumber,
                                                                                  List<ApproverEntry> approvers,
                                                                                  PersonalityExtensionException exception){
        SchedulingExtension schedulingExtension = new SchedulingExtension();
        schedulingExtension.setPersonNumber(personNumber);
        schedulingExtension.setApprovers(approvers);

        return new PersonalityResponse<>(schedulingExtension, exception);
    }

    private List<IAColumn> buildRequestIAColumnList(){
        ArrayList<IAColumn> columns = new ArrayList<>();
        columns.add(APPROVER_IACOLUMN);
        columns.add(APPROVAL_LEVEL_IACOLUMN);
        columns.add(APPROVE_DAYS_IACOLUMN);
        return columns;
    }
}
