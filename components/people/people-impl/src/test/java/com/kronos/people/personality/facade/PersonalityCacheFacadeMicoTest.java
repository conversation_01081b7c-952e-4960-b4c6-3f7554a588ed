package com.kronos.people.personality.facade;

import com.kronos.cache.api.key.MultiExtensionKey;
import com.kronos.cache.api.notification.DisableCacheNotificationService;
import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.people.personality.Operation;
import com.kronos.people.personality.cache.PersonalityCacheAccessor;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.dataaccess.adapter.ConcurrencyHelper;
import com.kronos.people.personality.dataaccess.adapter.ExtensionAdapterEnum;
import com.kronos.people.personality.model.Criteria;
import com.kronos.people.personality.model.IdentifierType;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.*;
import com.kronos.people.personality.model.extension.entry.AccountStatusEntry;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.EmploymentStatusEntry;
import com.kronos.people.personality.notification.PersonalityChangeNotificationManager;
import com.kronos.people.personality.notification.batch.PersonalityBatchProcessor;
import com.kronos.people.personality.notification.entry.CacheEntry;
import com.kronos.people.personality.notification.entry.EventType;
import com.kronos.people.personality.properties.KronosPropertiesFacade;
import com.kronos.people.personality.tenant.TenantHandlingFacade;
import com.kronos.persons.cacheretry.service.FailedPersonCacheUpdateDataAccessService;
import com.kronos.tenantprovider.api.exception.TenantProviderException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static com.kronos.people.personality.dataaccess.adapter.ExtensionAdapterEnum.EMPLOYEE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonalityCacheFacadeMicoTest {

	PersonalityCacheFacade personalityCacheFacade = null;
	PersonalityCacheAccessor personalityCacheAccessor = null;
	@Mock
	TenantHandlingFacade tenantFacade;
	@Mock
	KronosPropertiesFacade propertiesFacade;
	@Mock
	KronosThreadPoolService threadPoolService;
	@Mock
	ExecutorService executor;

	@Mock
	FailedPersonCacheUpdateDataAccessService failedPersonCacheUpdateDataAccessService;

	ConcurrencyHelper concurrencyHelper;

	@BeforeEach
	public void init() throws TenantProviderException {
		when(propertiesFacade.getIntegerKronosProperty(any(),anyInt())).thenReturn(100);
		when(threadPoolService.newThreadPool(any())).thenReturn(executor);
        concurrencyHelper = new ConcurrencyHelper(tenantFacade,propertiesFacade,null) {
            public Integer getNumberOfProcessingThreads(String key) {
                return 3;
            }

            @Override
            public ExecutorService getExecutor() {
                return Executors.newCachedThreadPool();
            }
        };
		IKProperties ikPropertiesMock = mock(IKProperties.class);
		personalityCacheFacade = spy(PersonalityCacheFacade.class);
		//personalityCacheFacade = new PersonalityCacheFacade();
		personalityCacheAccessor = spy(PersonalityCacheAccessor.class);
		tenantFacade = spy(TenantHandlingFacade.class);
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		personalityCacheFacade.tenantHandler=tenantFacade;
		personalityCacheFacade.concurrencyHelper = concurrencyHelper;
		personalityCacheFacade.concurrencyHelper.setTenantHandlingFacade(mock(TenantHandlingFacade.class));
		personalityCacheFacade.tenantHandler = mock(TenantHandlingFacade.class);
		personalityCacheFacade.allExtensionBuilder = new AllExtensionBuilder();
		when(personalityCacheFacade.tenantHandler.getTenantId()).thenReturn("DEFAULT");
		personalityCacheFacade.personalityChangeNotificationManager = mock(PersonalityChangeNotificationManager.class);
		personalityCacheFacade.failedPersonCacheUpdateDataAccessService = failedPersonCacheUpdateDataAccessService;


	}

	@Test
	public void getOneExtensionByListTest() {
		MultiExtensionKey multiExtensionKey1 = new MultiExtensionKey(1l, ExtensionAdapterEnum.EMPLOYEE.getIdentifier());
		MultiExtensionKey multiExtensionKey2 = new MultiExtensionKey(2l, ExtensionAdapterEnum.EMPLOYEE.getIdentifier());
		List<MultiExtensionKey> multiExtensionKeyList = Arrays.asList(multiExtensionKey1, multiExtensionKey2);

		String extName = ExtensionAdapterEnum.EMPLOYEE.getIdentifier();

		TenantHandlingFacade tenantHandlingFacadeMock = Mockito.mock(TenantHandlingFacade.class);
		doReturn("-1L").when(tenantHandlingFacadeMock).getTenantId();
		doNothing().when(tenantHandlingFacadeMock).removeTenantId();
		HashMap<MultiExtensionKey, BaseExtension> employeeExtensions = new HashMap<>();
		when(personalityCacheAccessor.getOneExtensionByList(multiExtensionKeyList)).thenReturn(employeeExtensions);

		Map<Long, BaseExtension> result = personalityCacheFacade.getOneExtensionByList(multiExtensionKeyList, extName);
		assertEquals(0, result.size());

		EmployeeExtension employeeExtension1 = new EmployeeExtension();
		EmployeeExtension employeeExtension2 = new EmployeeExtension();

		EffectiveDatedCollection<EmploymentStatusEntry> mockEmploymentStatus = new EffectiveDatedCollection<>(Collections.emptyList());
		EffectiveDatedCollection<AccountStatusEntry> mockAccountStatus = new EffectiveDatedCollection<>(Collections.emptyList());

		employeeExtension1.setEffDatedEmploymentStatus(mockEmploymentStatus);
		employeeExtension1.setEffDatedAccountStatus(mockAccountStatus);
		employeeExtension1.setLastName("lastName");

		employeeExtension2.setEffDatedEmploymentStatus(mockEmploymentStatus);
		employeeExtension2.setEffDatedAccountStatus(mockAccountStatus);
		employeeExtension2.setLastName("lastName2");

		employeeExtensions.put(multiExtensionKey1, employeeExtension1);
		employeeExtensions.put(multiExtensionKey2, employeeExtension2);

		Map<Long, BaseExtension> resultMap = personalityCacheFacade.getOneExtensionByList(multiExtensionKeyList, extName);
		assertEquals(employeeExtension1, resultMap.get(1L));
		assertEquals(employeeExtension2, resultMap.get(2L));

	}

	//@Test
	public void getOneExtensionByListExceptionTest() {
		MultiExtensionKey multiExtensionKey1 = new MultiExtensionKey(1l, ExtensionAdapterEnum.EMPLOYEE.getIdentifier());
		MultiExtensionKey multiExtensionKey2 = new MultiExtensionKey(2l, ExtensionAdapterEnum.EMPLOYEE.getIdentifier());

		List<MultiExtensionKey> multiExtensionKeyList = new ArrayList<MultiExtensionKey>();
		multiExtensionKeyList.add(multiExtensionKey1);
		multiExtensionKeyList.add(multiExtensionKey2);

		String extName = ExtensionAdapterEnum.EMPLOYEE.getIdentifier();

		EmployeeExtension employeeExtension2 = new EmployeeExtension();

		when(personalityCacheAccessor.getExtensionByPersonId(multiExtensionKey1)).thenThrow(new RuntimeException());
		when(personalityCacheAccessor.getExtensionByPersonId(multiExtensionKey2)).thenReturn(employeeExtension2);
		assertEquals(1, personalityCacheFacade.getOneExtensionByList(multiExtensionKeyList, extName).size());

	}

	@Test
	public void getExtensionsFromCacheTest() {
		Long personIds[] = { 1l, 2l, 3l };
		EmployeeExtension empExt = new EmployeeExtension();
		DevicesExtension devicesExtension = new DevicesExtension();
		personalityCacheFacade = new PersonalityCacheFacade() {
			@SuppressWarnings("unchecked")
			@Override
			public <T extends BaseExtension> Map<Long, T> getOneExtensionByList(List<MultiExtensionKey> personalityKeys, String extName) {

				Map outputMap = new LinkedHashMap();
				personalityKeys.forEach(t -> {
					if (t.getObjectId().equals(2l)) {
						outputMap.put(t.getObjectId(), devicesExtension);
					} else {
						outputMap.put(t.getObjectId(), empExt);
					}
				});

				return outputMap;
			}
		};
		Map<Long, BaseExtension> resultMap = personalityCacheFacade.getExtensionsFromCache(personIds, EMPLOYEE);
		assertEquals(empExt, resultMap.get(1l));
		assertEquals(devicesExtension, resultMap.get(2l));
		assertEquals(empExt, resultMap.get(3l));
	}

	@Test
	public void getAllExtensionForPersonIdTest() {
		Long personId = 12l;
		LocalDate snapShotDate = LocalDate.now();
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		assertNull(personalityCacheFacade.getAllExtensionForPersonId(personId, snapShotDate));

		EmployeeExtension employeeExtension = mock(EmployeeExtension.class);
		AllExtension allExtension = new AllExtension();
		allExtension.setEmployeeExtension(employeeExtension);

		PersonalityResponse<AllExtension> expectedPersonalityResponse = new PersonalityResponse<>();
		expectedPersonalityResponse.setextension(allExtension);

		personalityCacheFacade = new PersonalityCacheFacade() {
			@Override
			public PersonalityResponse<AllExtension> getAllExtensionFromMap(Map<String, BaseExtension> map, LocalDate snapShotDate) {
				return expectedPersonalityResponse;
			}
		};

		personalityCacheAccessor = new PersonalityCacheAccessor() {
			@Override
			public Map<String, BaseExtension> getAllExtensionsByPersonId(MultiExtensionKey key) {
				return new HashMap<>();
			}
		};
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		PersonalityResponse<AllExtension> resultPersonalityResponse = personalityCacheFacade.getAllExtensionForPersonId(personId, snapShotDate);
		assertEquals(expectedPersonalityResponse.getExtension().getEmployeeExtension(), resultPersonalityResponse.getExtension().getEmployeeExtension());

		personalityCacheAccessor = new PersonalityCacheAccessor() {
			@Override
			public Map<String, BaseExtension> getAllExtensionsByPersonId(MultiExtensionKey key) {
				throw new RuntimeException();
			}
		};
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		PersonalityResponse<AllExtension> resultPersonalityResponse2 = personalityCacheFacade.getAllExtensionForPersonId(personId, snapShotDate);
		assertNull(resultPersonalityResponse2);

	}

	@Test
	public void getAllExtensionsFromMapTest() {
		Map<String, BaseExtension> expectedMap = new HashMap<>();
		AccrualExtension accrualExtension = mock(AccrualExtension.class);
		DevicesExtension deviceExtension = mock(DevicesExtension.class);
		EmployeeExtension employeeExtension = mock(EmployeeExtension.class);
		SchedulingExtension schedulingExtension = mock(SchedulingExtension.class);
		TimekeepingExtension timekeepingExtension = mock(TimekeepingExtension.class);

		expectedMap.put("ACCRUAL_EXTENSION", accrualExtension);
		expectedMap.put("DEVICE_EXTENSION", deviceExtension);
		expectedMap.put("EMPLOYEE_EXTENSION", employeeExtension);
		expectedMap.put("SCHEDULING_EXTENSION", schedulingExtension);
		expectedMap.put("TIMEKEEPING_EXTENSION", timekeepingExtension);

		AllExtension allExtension = personalityCacheFacade.getAllExtensionsFromMap(expectedMap);
		assertEquals(accrualExtension, allExtension.getAccrualExtension());
		assertEquals(deviceExtension, allExtension.getDeviceExtension());
		assertEquals(employeeExtension, allExtension.getEmployeeExtension());
		assertEquals(schedulingExtension, allExtension.getSchedulingExtension());
		assertEquals(timekeepingExtension, allExtension.getTimekeepingExtension());
	}

	private Map<String, BaseExtension> getExtensions(){
		Map<String, BaseExtension> expectedMap = new HashMap<>();
		AccrualExtension accrualExtension = mock(AccrualExtension.class);
		DevicesExtension deviceExtension = mock(DevicesExtension.class);
		EmployeeExtension employeeExtension = mock(EmployeeExtension.class);
		SchedulingExtension schedulingExtension = mock(SchedulingExtension.class);
		TimekeepingExtension timekeepingExtension = mock(TimekeepingExtension.class);

		expectedMap.put("ACCRUAL_EXTENSION", accrualExtension);
		expectedMap.put("DEVICE_EXTENSION", deviceExtension);
		expectedMap.put("EMPLOYEE_EXTENSION", employeeExtension);
		expectedMap.put("SCHEDULING_EXTENSION", schedulingExtension);
		expectedMap.put("TIMEKEEPING_EXTENSION", timekeepingExtension);
		return expectedMap;
	}

	@Test
	public void getAllExtensionFromMapTest() {
		LocalDate snapShotDate = LocalDate.now();
		EmployeeExtension employeeExtension = mock(EmployeeExtension.class);
		AllExtension allExtension = new AllExtension();
		allExtension.setEmployeeExtension(employeeExtension);
		Map<String, BaseExtension> expectedMap = new HashMap<>();
		expectedMap.put("EMPLOYEE_EXTENSION", employeeExtension);
		Mockito.when(employeeExtension.getLastName()).thenReturn("lastName");
		personalityCacheFacade = new PersonalityCacheFacade() {
			@Override
			public AllExtension getAllExtensionsFromMap(Map<String, BaseExtension> map) {
				// TODO Auto-generated method stub
				return allExtension;
			}
		};

		PersonalityResponse<AllExtension> personalityResponse = personalityCacheFacade.getAllExtensionFromMap(expectedMap, snapShotDate);
		assertEquals(employeeExtension, personalityResponse.getExtension().getEmployeeExtension());
	}

	@Test
	public void getAllExtensionsFromCacheTest() {
		MultiExtensionKey multiExtensionKey1 = new MultiExtensionKey(1l);
		MultiExtensionKey multiExtensionKey2 = new MultiExtensionKey(2l);
		List<MultiExtensionKey> multiExtensionKeyList = Arrays.asList(multiExtensionKey1, multiExtensionKey2);

		TenantHandlingFacade tenantHandlingFacadeMock = Mockito.mock(TenantHandlingFacade.class);
		doReturn("-1L").when(tenantHandlingFacadeMock).getTenantId();
		doNothing().when(tenantHandlingFacadeMock).removeTenantId();
        HashMap<MultiExtensionKey, Map<String, BaseExtension>> expectedMap = new HashMap<>();
        when(personalityCacheAccessor.getAllExtensionsByList(multiExtensionKeyList)).thenReturn(expectedMap);
        Map<Long, Map<String, BaseExtension>> allExtensionsFromCache = personalityCacheFacade.getAllExtensionsFromCache(multiExtensionKeyList);
        assertEquals(0, allExtensionsFromCache.size());

		EmployeeExtension employeeExtension = mock(EmployeeExtension.class);
		Map<String, BaseExtension> extnMap1 = new HashMap<>();
		extnMap1.put("EMPLOYEE_EXTENSION", employeeExtension);

		DevicesExtension devicesExtension = mock(DevicesExtension.class);
		Map<String, BaseExtension> extnMap2 = new HashMap<>();
		extnMap2.put("DEVICE_EXTENSION", devicesExtension);

        expectedMap.put(multiExtensionKey1, extnMap1);
        expectedMap.put(multiExtensionKey2, extnMap2);


        Map<Long, Map<String, BaseExtension>> resultMap = personalityCacheFacade.getAllExtensionsFromCache(multiExtensionKeyList);
		assertEquals(employeeExtension, resultMap.get(1l).get("EMPLOYEE_EXTENSION"));
		assertEquals(devicesExtension, resultMap.get(2l).get("DEVICE_EXTENSION"));

	}

	@Test
	public void getAllExtensionsTest() {
		Long personIds[] = { 1l, 2l, 3l };
		LocalDate snapShotDate = LocalDate.now();

		EmployeeExtension employeeExtension = mock(EmployeeExtension.class);
		DevicesExtension devicesExtension = mock(DevicesExtension.class);

		Map<String, BaseExtension> map1 = new HashMap<String, BaseExtension>();
		map1.put("EMPLOYEE_EXTENSION", employeeExtension);
		map1.put("DEVICE_EXTENSION", devicesExtension);

		Map<Long, Map<String, BaseExtension>> map2 = new HashMap<Long, Map<String, BaseExtension>>();
		map2.put(1l, map1);
		map2.put(2l, map1);

		AllExtension allExtension = new AllExtension();
		allExtension.setEmployeeExtension(employeeExtension);
		allExtension.setDeviceExtension(devicesExtension);

		PersonalityResponse<AllExtension> personalityResponse = new PersonalityResponse<AllExtension>();
		personalityResponse.setextension(allExtension);

		personalityCacheFacade = new PersonalityCacheFacade() {
			@Override
			protected Map<Long, Map<String, BaseExtension>> getAllExtensionsFromCache(List<MultiExtensionKey> multiExtensionKeys) {
				// TODO Auto-generated method stub
				return map2;
			}

			@Override
			public PersonalityResponse<AllExtension> getAllExtensionFromMap(Map<String, BaseExtension> map, LocalDate snapShotDate) {
				// TODO Auto-generated method stub
				return personalityResponse;
			}
		};

		Map<Long, PersonalityResponse<AllExtension>> resultMap = personalityCacheFacade.getAllExtensions(personIds, snapShotDate);
		assertEquals(employeeExtension, resultMap.get(1l).getExtension().getEmployeeExtension());
		assertEquals(devicesExtension, resultMap.get(2l).getExtension().getDeviceExtension());

		personalityCacheFacade = new PersonalityCacheFacade() {
			@Override
			protected Map<Long, Map<String, BaseExtension>> getAllExtensionsFromCache(List<MultiExtensionKey> multiExtensionKeys) {
				// TODO Auto-generated method stub
				return new HashMap<Long, Map<String, BaseExtension>>();
			}
		};

		assertEquals(0, personalityCacheFacade.getAllExtensions(personIds, snapShotDate).size());
	}

	@Test
	public void primeTest() {
		CacheEntry cacheEntry1 = new CacheEntry(new MultiExtensionKey(1L), EventType.INSERT, new HashMap<>(), "1");
		CacheEntry cacheEntry2 = new CacheEntry(new MultiExtensionKey(2L), EventType.INSERT, new HashMap<>(), "2");
		List<CacheEntry> list = new ArrayList<>();
		list.add(cacheEntry1);
		list.add(cacheEntry2);
		personalityCacheAccessor = mock(PersonalityCacheAccessor.class);
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		personalityCacheFacade.prime(list);
		verify(personalityCacheAccessor).primeCache(any(), any());
		// Mock the primeCache method to throw a RuntimeException
		doThrow(new RuntimeException()).when(personalityCacheAccessor).primeCache(any(), any());

		// Perform the second prime operation and expect a RuntimeException
		assertThrows(RuntimeException.class, () -> {
			personalityCacheFacade.prime(list);
		});
	}

	@Test
	public void getExtensionByPersonIdTest() {
		ExtensionAdapterEnum extensionAdapterEnum = ExtensionAdapterEnum.EMPLOYEE;
		Long personId = 123l;
		assertNull(personalityCacheFacade.getExtensionByPersonId(personId, extensionAdapterEnum));
		EmployeeExtension employeeExtension = mock(EmployeeExtension.class);
		Mockito.when(employeeExtension.getLastName()).thenReturn("lastName");
		personalityCacheAccessor = new PersonalityCacheAccessor() {
			@SuppressWarnings("unchecked")
			@Override
			public <T extends BaseExtension> T getExtensionByPersonId(MultiExtensionKey key) {

				return (T) employeeExtension;
			}
		};

		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);

		assertEquals(employeeExtension, personalityCacheFacade.getExtensionByPersonId(personId, extensionAdapterEnum));

		personalityCacheAccessor = new PersonalityCacheAccessor() {
			@Override
			public <T extends BaseExtension> T getExtensionByPersonId(MultiExtensionKey key) {

				throw new RuntimeException();
			}
		};

		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		assertNull(personalityCacheFacade.getExtensionByPersonId(personId, extensionAdapterEnum));
	}

	@Test
	public void insertOneExtensionTest() {
		EmployeeExtension employeeExtension = new EmployeeExtension();
		employeeExtension.setPersonId(123l);
		personalityCacheAccessor = mock(PersonalityCacheAccessor.class);
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		personalityCacheFacade.insertOneExtension(employeeExtension);
		verify(personalityCacheAccessor, times(1)).updateExtension(any(), any());
		// Mock the updateExtension method to throw a RuntimeException
		doThrow(new RuntimeException()).when(personalityCacheAccessor).updateExtension(any(), any());

		// Perform the second insertion
		assertThrows(RuntimeException.class, () -> {
			personalityCacheFacade.insertOneExtension(employeeExtension);
		});
	}

	@Test
	public void isSinglePutTest() {
		CacheEntry cacheEntry1 = new CacheEntry(new MultiExtensionKey(1l), EventType.INSERT, new HashMap<>(), "1");
		PersonalityBatchProcessor personalityBatchProcessor = Mockito.mock(PersonalityBatchProcessor.class);
		AtomicInteger updateAI = new AtomicInteger(0);
		Mockito.when(personalityBatchProcessor.isPersonPresentForGivenTenantIdAndRemoveFromMap(any())).thenReturn(false);
		personalityCacheFacade.personalityBatchProcessor = personalityBatchProcessor;
		DisableCacheNotificationService disableCacheNotificationService = Mockito.mock(DisableCacheNotificationService.class);
		personalityCacheFacade.disableCacheNotificationService = disableCacheNotificationService;
		List<CacheEntry> cacheEntryList = new ArrayList<CacheEntry>();
		cacheEntryList.add(cacheEntry1);
		tenantFacade=new TenantHandlingFacade(){
			@Override
			public void performOperationsUsingTenantId(Operation operation,
					String tenantId) {
				updateAI.getAndIncrement();
			}
			@Override
			public String getTenantId() {
				return "MyTenantId";
			}
		};
		personalityCacheFacade.tenantHandler=tenantFacade;
		assertTrue(personalityCacheFacade.isSinglePut(cacheEntryList));
		assertEquals(1, updateAI.get());

		cacheEntryList.add(cacheEntry1);
		assertFalse(personalityCacheFacade.isSinglePut(cacheEntryList));
		assertEquals(1, updateAI.get());

		cacheEntryList.remove(1);
		personalityCacheAccessor = new PersonalityCacheAccessor() {
			@Override
			public Map<String, BaseExtension> updateAllExtensions(MultiExtensionKey key, Map<String, BaseExtension> extensionsMap) {
				updateAI.getAndIncrement();
				throw new RuntimeException();
			}
		};
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		assertTrue(personalityCacheFacade.isSinglePut(cacheEntryList));
	}

	@Test
	public void isSinglePutTestPresentInBatchMap(){
		CacheEntry cacheEntry1 = new CacheEntry(new MultiExtensionKey(1l), EventType.INSERT, new HashMap<>(), "1");
		PersonalityBatchProcessor personalityBatchProcessor = Mockito.mock(PersonalityBatchProcessor.class);
		AtomicInteger updateAI = new AtomicInteger(0);
		Mockito.when(personalityBatchProcessor.isPersonPresentForGivenTenantIdAndRemoveFromMap(any())).thenReturn(true);
		personalityCacheFacade.personalityBatchProcessor = personalityBatchProcessor;
		DisableCacheNotificationService disableCacheNotificationService = Mockito.mock(DisableCacheNotificationService.class);
		personalityCacheFacade.disableCacheNotificationService = disableCacheNotificationService;
		List<CacheEntry> cacheEntryList = new ArrayList<CacheEntry>();
		cacheEntryList.add(cacheEntry1);
		tenantFacade=new TenantHandlingFacade(){
			@Override
			public void performOperationsUsingTenantId(Operation operation,
					String tenantId) {
				updateAI.getAndIncrement();
			}
			@Override
			public String getTenantId() {
				return "MyTenantId";
			}
		};
		personalityCacheFacade.tenantHandler=tenantFacade;
		assertTrue(personalityCacheFacade.isSinglePut(cacheEntryList));
		assertEquals(1, updateAI.get());

		cacheEntryList.add(cacheEntry1);
		assertFalse(personalityCacheFacade.isSinglePut(cacheEntryList));
		assertEquals(1, updateAI.get());

		cacheEntryList.remove(1);
		personalityCacheAccessor = new PersonalityCacheAccessor() {
			@Override
			public Map<String, BaseExtension> updateAllExtensions(MultiExtensionKey key, Map<String, BaseExtension> extensionsMap) {
				// TODO Auto-generated method stub
				throw new RuntimeException();
			}
		};

		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		assertTrue(personalityCacheFacade.isSinglePut(cacheEntryList));
	}

	@Test
	public void isSinglePutTestHasException() {
		personalityCacheFacade = new PersonalityCacheFacade();
		CacheEntry cacheEntry1 = new CacheEntry(new MultiExtensionKey(1L), EventType.UPDATE, new HashMap<>(), "tenant id 1");
		PersonalityBatchProcessor personalityBatchProcessor = Mockito.mock(PersonalityBatchProcessor.class);
		Mockito.when(personalityBatchProcessor.isPersonPresentForGivenTenantIdAndRemoveFromMap(any())).thenReturn(true);
		personalityCacheFacade.personalityBatchProcessor = personalityBatchProcessor;
		DisableCacheNotificationService disableCacheNotificationService = Mockito.mock(DisableCacheNotificationService.class);
		personalityCacheFacade.disableCacheNotificationService = disableCacheNotificationService;

		List<CacheEntry> cacheEntryList = new ArrayList<>();
		cacheEntryList.add(cacheEntry1);

		tenantFacade = new TenantHandlingFacade() {
			@Override
			public String getTenantId() {
				return "MyTenantId";
			}
			@Override
			public void performOperationsUsingTenantId(Operation operation,
													   String tenantId) {
				operation.execute();
			}
		};
		personalityCacheFacade.tenantHandler = tenantFacade;
		personalityCacheAccessor = new PersonalityCacheAccessor() {
			@Override
			public Map<String, BaseExtension> updateAllExtensions(MultiExtensionKey key, Map<String, BaseExtension> extensionsMap) {
				// TODO Auto-generated method stub
				throw new RuntimeException();
			}
		};
		personalityCacheFacade.failedPersonCacheUpdateDataAccessService = failedPersonCacheUpdateDataAccessService;
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		assertTrue(personalityCacheFacade.isSinglePut(cacheEntryList));
		Mockito.verify(failedPersonCacheUpdateDataAccessService, Mockito.times(1)).save(Mockito.any());
	}

	@Test
	public void multiPutTest() {
		CacheEntry cacheEntry1 = new CacheEntry(new MultiExtensionKey(1l), EventType.UPDATE, new HashMap<>(), "1");
		List<CacheEntry> cacheEntryList = new ArrayList<CacheEntry>();
		cacheEntryList.add(cacheEntry1);

		personalityCacheFacade = new PersonalityCacheFacade() {
			@Override
			public boolean isSinglePut(List<CacheEntry> t) {
				// TODO Auto-generated method stub
				return true;
			}
		};

		personalityCacheFacade.multiPut(cacheEntryList);

		personalityCacheFacade = new PersonalityCacheFacade() {
			@Override
			public boolean isSinglePut(List<CacheEntry> t) {
				// TODO Auto-generated method stub
				return false;
			}
		};
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		personalityCacheFacade.tenantHandler = mock(TenantHandlingFacade.class);
		personalityCacheFacade.personalityBatchProcessor = new PersonalityBatchProcessor(){
			public boolean isPersonPresentForGivenTenantIdAndRemoveFromMap(CacheEntry ce) {
				return false;
			}
		};
		personalityCacheFacade.adapterHelper = new AdapterHelper();
		StringBuilder validator = new StringBuilder();
		personalityCacheFacade.disableCacheNotificationService = getMockDisableCacheNotificationService(validator);
		personalityCacheFacade.personalityChangeNotificationManager = mock(PersonalityChangeNotificationManager.class);
		personalityCacheFacade.multiPut(cacheEntryList);
		assertEquals("", validator.toString());
		verify(personalityCacheAccessor, times(1)).updateMultiplePersonalityObjects(Mockito.anyList(), Mockito.anyMap());

		personalityCacheAccessor = new PersonalityCacheAccessor() {
			@Override
			public Map<MultiExtensionKey, Map<String, BaseExtension>> updateMultiplePersonalityObjects(List<MultiExtensionKey> keys,
					Map<MultiExtensionKey, Map<String, BaseExtension>> map) {
				// TODO Auto-generated method stub
				throw new RuntimeException();
			}
		};
		personalityCacheFacade.tenantHandler = mock(TenantHandlingFacade.class);
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		personalityCacheFacade.failedPersonCacheUpdateDataAccessService = failedPersonCacheUpdateDataAccessService;
		personalityCacheFacade.multiPut(cacheEntryList);
		verify(failedPersonCacheUpdateDataAccessService, times(1)).saveBatch(anySet());

		personalityCacheFacade.personalityBatchProcessor = new PersonalityBatchProcessor(){
			public boolean isPersonPresentForGivenTenantIdAndRemoveFromMap(CacheEntry ce) {
				return true;
			}
		};
		DisableCacheNotificationService disableCacheNotificationService = mock(DisableCacheNotificationService.class);
		personalityCacheFacade.disableCacheNotificationService = disableCacheNotificationService;

		personalityCacheFacade.multiPut(cacheEntryList);
	}

	@Test
	public void evictTest() {
		CacheEntry cacheEntry1 = new CacheEntry(new MultiExtensionKey(1l), EventType.DELETE,new HashMap<>(), "1");
		List<CacheEntry> cacheEntryList = new ArrayList<CacheEntry>();
		cacheEntryList.add(cacheEntry1);
		AtomicInteger a=new AtomicInteger(0);
		personalityCacheFacade.concurrencyHelper = new ConcurrencyHelper(tenantFacade,propertiesFacade,null){
			public Integer getNumberOfProcessingThreads(String key) {
				return 3;
			}
		};
		tenantFacade=new TenantHandlingFacade(){
			@Override
			public void performOperationsUsingTenantId(Operation operation,
					String tenantId) {
				a.getAndIncrement();
			}
			@Override
			public String getTenantId() {
				return "1";
			}
		};
		personalityCacheFacade.tenantHandler=tenantFacade;
		personalityCacheFacade.adapterHelper = new AdapterHelper();
		personalityCacheFacade.disableCacheNotificationService = mock(DisableCacheNotificationService.class);
		personalityCacheFacade.personalityBatchProcessor = new PersonalityBatchProcessor(){
			public boolean isPersonPresentForGivenTenantIdAndRemoveFromMap(CacheEntry ce) {
				return true;
			}
		};
		personalityCacheFacade.evict(cacheEntryList);
		assertEquals(1,a.get());
		personalityCacheAccessor = new PersonalityCacheAccessor() {
			@Override
			public void deletePerson(MultiExtensionKey key) {
				throw new RuntimeException();
			};

		};
		personalityCacheFacade = new PersonalityCacheFacade();
		tenantFacade=new TenantHandlingFacade(){
			@Override
			public void performOperationsUsingTenantId(Operation operation, String tenantId) {
				operation.execute();
			}
			@Override
			public String getTenantId() {
				return "1";
			}
		};
		personalityCacheFacade.concurrencyHelper = new ConcurrencyHelper(tenantFacade,propertiesFacade,null){
			public Integer getNumberOfProcessingThreads(String key) {
				return 3;
			}
		};
		personalityCacheFacade.tenantHandler = tenantFacade;
		personalityCacheFacade.adapterHelper = new AdapterHelper();
		personalityCacheFacade.concurrencyHelper.setTenantHandlingFacade(tenantFacade);
		personalityCacheFacade.personalityBatchProcessor = new PersonalityBatchProcessor(){
			public boolean isPersonPresentForGivenTenantIdAndRemoveFromMap(CacheEntry ce) {
				return true;
			}
		};
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		personalityCacheFacade.disableCacheNotificationService = mock(DisableCacheNotificationService.class);
		personalityCacheFacade.failedPersonCacheUpdateDataAccessService = failedPersonCacheUpdateDataAccessService;
		personalityCacheFacade.evict(cacheEntryList);
		verify(failedPersonCacheUpdateDataAccessService, times(1)).saveBatch(anySet());
	}

	@SuppressWarnings("unchecked")
	@Test
	public void putTest() {
		CacheEntry cacheEntry1 = new CacheEntry(new MultiExtensionKey(1l), EventType.INSERT, new HashMap<>(), "1");
		List<CacheEntry> cacheEntryList = new ArrayList<CacheEntry>();
		cacheEntryList.add(cacheEntry1);
		personalityCacheFacade = new PersonalityCacheFacade() {
			@Override
			public boolean isSinglePut(List<CacheEntry> t) {
				// TODO Auto-generated method stub
				return true;
			}
		};
		personalityCacheFacade.put(cacheEntryList);

		personalityCacheFacade = new PersonalityCacheFacade() {
			@Override
			public boolean isSinglePut(List<CacheEntry> t) {
				// TODO Auto-generated method stub
				return false;
			}
		};
		personalityCacheFacade.tenantHandler = mock(TenantHandlingFacade.class);
		cacheEntryList.add(cacheEntry1);
		AdapterHelper adapterHelper = spy(AdapterHelper.class);
		personalityCacheFacade.setAdapterHelper(adapterHelper);
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		personalityCacheFacade.personalityBatchProcessor = new PersonalityBatchProcessor(){
			public boolean isPersonPresentForGivenTenantIdAndRemoveFromMap(CacheEntry ce) {
				return false;
			}
		};
		StringBuilder valid = new StringBuilder("");
		personalityCacheFacade.disableCacheNotificationService = getMockDisableCacheNotificationService(valid);
		personalityCacheFacade.put(cacheEntryList);
		assertEquals("", valid.toString());
		verify(adapterHelper, times(2)).putInListInsideMap(Mockito.anyMap(), any(), any());
	}

	@SuppressWarnings("unchecked")
	@Test
	public void getExtensionsFromCacheSnapshotDateTest() {
		Long personIds[] = { 1l, 2l };
		LocalDate snapShotDate = LocalDate.now();
		ExtensionAdapterEnum adapterEnum = ExtensionAdapterEnum.EMPLOYEE;

		EmployeeExtension employeeExtension = new EmployeeExtension();
		DevicesExtension devicesExtension = new DevicesExtension();

		Map expectedMap = new HashMap();
		expectedMap.put(1l, employeeExtension);
		expectedMap.put(2l, devicesExtension);

		personalityCacheFacade = new PersonalityCacheFacade() {
			@Override
			public <T extends BaseExtension> Map<Long, T> getExtensionsFromCache(Long[] personIds, ExtensionAdapterEnum adapterEnum) {
				// TODO Auto-generated method stub
				return expectedMap;
			}
		};
		personalityCacheFacade.setAdapterHelper(new AdapterHelper());
		personalityCacheFacade.concurrencyHelper = new ConcurrencyHelper(tenantFacade,propertiesFacade,null);
		Map<Long, PersonalityResponse<BaseExtension>> resultmap = personalityCacheFacade.getExtensionsFromCache(personIds, snapShotDate, adapterEnum);
		assertEquals(employeeExtension, resultmap.get(1l).getExtension());
		assertEquals(devicesExtension, resultmap.get(2l).getExtension());
	}

	@Test
	public void getExtensionsForCriteriaTest() {
		Object[] ids = { "1", "2", "3" };
		Criteria criteria = new Criteria(ids, IdentifierType.BADGENUMBER);

        personalityCacheFacade.concurrencyHelper = concurrencyHelper;
		TenantHandlingFacade tenantHandlingFacadeMock = Mockito.mock(TenantHandlingFacade.class);
		doReturn("-1L").when(tenantHandlingFacadeMock).getTenantId();
		doNothing().when(tenantHandlingFacadeMock).removeTenantId();
		personalityCacheFacade.concurrencyHelper.setTenantHandlingFacade(tenantHandlingFacadeMock);

		assertEquals(0, personalityCacheFacade.getExtensionsForCriteria(criteria, ExtensionAdapterEnum.EMPLOYEE).size());

		EmployeeExtension employeeExtension = mock(EmployeeExtension.class);

		personalityCacheAccessor = new PersonalityCacheAccessor() {
			@Override
			public BaseExtension getExtensionBySecondaryKey(MultiExtensionKey key) {
				// TODO Auto-generated method stub
				return employeeExtension;
			}

			@Override
			public Map<String, BaseExtension> getAllExtensionsBySecondaryKey(MultiExtensionKey key) {
				// TODO Auto-generated method stub
				return new HashMap<>();
			}
		};

		AdapterHelper adapterHelper = new AdapterHelper();
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		personalityCacheFacade.setAdapterHelper(adapterHelper);
		Map<Object, PersonalityResponse<BaseExtension>> resultMap = personalityCacheFacade.getExtensionsForCriteria(criteria, ExtensionAdapterEnum.EMPLOYEE);
		assertEquals(employeeExtension, resultMap.get("1").getExtension());
		assertEquals(employeeExtension, resultMap.get("2").getExtension());
		assertEquals(employeeExtension, resultMap.get("3").getExtension());

		AllExtension allExtension = new AllExtension();
		allExtension.setEmployeeExtension(employeeExtension);

		personalityCacheFacade = new PersonalityCacheFacade() {
			@Override
			public AllExtension getAllExtensionsFromMap(Map<String, BaseExtension> map) {
				// TODO Auto-generated method stub
				return allExtension;
			}
		};
        personalityCacheFacade.concurrencyHelper = concurrencyHelper;
		personalityCacheFacade.setAdapterHelper(adapterHelper);

		personalityCacheFacade.concurrencyHelper.setTenantHandlingFacade(tenantHandlingFacadeMock);;
		personalityCacheFacade.concurrencyHelper.setTenantHandlingFacade(mock(TenantHandlingFacade.class));
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		Map<Object, PersonalityResponse<BaseExtension>> resultMap2 = personalityCacheFacade.getExtensionsForCriteria(criteria, null);
		assertEquals(allExtension, resultMap2.get("1").getExtension());
		assertEquals(employeeExtension, ((AllExtension) resultMap2.get("1").getExtension()).getEmployeeExtension());

		Object[] ids2 = { 1l, 2l };
		Criteria criteria2 = new Criteria(ids, IdentifierType.PERSONID);
		Map<Object, PersonalityResponse<BaseExtension>> resultMap3 = personalityCacheFacade.getExtensionsForCriteria(criteria, null);
		assertEquals(allExtension, resultMap2.get("1").getExtension());
		assertEquals(employeeExtension, ((AllExtension) resultMap3.get("1").getExtension()).getEmployeeExtension());

		personalityCacheAccessor = new PersonalityCacheAccessor() {
			@Override
			public BaseExtension getExtensionBySecondaryKey(MultiExtensionKey key) {
				// TODO Auto-generated method stub
				throw new RuntimeException();
			}

		};
		personalityCacheFacade.setAdapterHelper(adapterHelper);
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		assertEquals(0, personalityCacheFacade.getExtensionsForCriteria(criteria, ExtensionAdapterEnum.EMPLOYEE).size());
	}

	@Test
	public void testRemoveAllExtensions(){

		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		personalityCacheFacade.evictAll();

		verify(personalityCacheAccessor, times(1)).deleteAll();
	}

	@Test
	public void testPut(){
		CacheEntry entry = new CacheEntry(new MultiExtensionKey(1L),EventType.INSERT,new HashMap<>(),"1");
		StringBuilder sb=new StringBuilder();
		TenantHandlingFacade tenantHandlingFacade = new TenantHandlingFacade() {
			String tenantId ="MyTenantId";

			public void performOperationsUsingTenantId(Operation operation, String tenantId) {
				sb.append("\n called performOperationsUsingTenantId for operation");
				operation.execute();
			}
			@Override
			public String getTenantId() {
				return tenantId;
			}

		};
		personalityCacheFacade.tenantHandler=tenantHandlingFacade;
		personalityCacheFacade.put(entry );
		assertTrue(sb.indexOf("called performOperationsUsingTenantId for operation")>-1);
	}

	@Test
	public void testEvict(){
		CacheEntry entry = new CacheEntry(1L,"1");
		StringBuilder sb=new StringBuilder();
		PersonalityCacheFacade personalityCacheFacade = new PersonalityCacheFacade(){
			@Override
			protected void notifyPersonalityChange(List<CacheEntry> cacheEntries) {
				sb.append("\n called notifyPersonalityChange");
			}
		};
		TenantHandlingFacade tenantHandlingFacade = new TenantHandlingFacade() {
			String tenantId ="MyTenantId";

			public void performOperationsUsingTenantId(Operation operation, String tenantId) {
				sb.append("\n called performOperationsUsingTenantId for operation");
				operation.execute();
			}
			@Override
			public String getTenantId() {
				return tenantId;
			}

		};
		personalityCacheFacade.tenantHandler=tenantHandlingFacade;
		PersonalityCacheAccessor mockAccessor=new PersonalityCacheAccessor(){
			@Override
			public void deletePerson(MultiExtensionKey key) {
				sb.append("\n called deletePerson");
			}
		};
		personalityCacheFacade.cacheImpl=mockAccessor;
		personalityCacheFacade.evict(entry);
		assertTrue(sb.indexOf("called performOperationsUsingTenantId for operation")>-1);
		assertTrue(sb.indexOf("called deletePerson")>-1);
		assertTrue(sb.indexOf("called notifyPersonalityChange")>-1);
	}

	@Test
	public void testObjectToLowerCase(){
		Criteria criteria = new Criteria(null, IdentifierType.PERSONNUMBER, LocalDate.of(2015, 1, 18));
		Object result = personalityCacheFacade.objectToLowerCase(criteria, "SUPERUSER");
		assertEquals("superuser", result);
	}

	@Test
	public void testMultiPutWhilePriming(){
		List<CacheEntry> cacheEntries = new ArrayList<>();
		CacheEntry ce = new CacheEntry(new MultiExtensionKey(1l), EventType.INSERT, new HashMap<>(), "1");
		cacheEntries.add(ce);
		StringBuilder builder = new StringBuilder();
		PersonalityCacheFacade personalityCacheFacade = new PersonalityCacheFacade() {
			protected void operateCacheEntriesForUpdate(List<CacheEntry> cacheEntries) {
				builder.append("operateCacheEntriesForUpdate");
			}
		};
		personalityCacheFacade.disableCacheNotificationService = mock(DisableCacheNotificationService.class);
		personalityCacheFacade.cacheImpl = mock(PersonalityCacheAccessor.class);
		Mockito.doThrow(RuntimeException.class).when(personalityCacheFacade.cacheImpl).deleteMultiplePersons(any());
		personalityCacheFacade.multiPutWhilePriming(cacheEntries);
		assertEquals("operateCacheEntriesForUpdate", builder.toString());
	}

	@Test
	public void testObjectToLowerCaseWhenIdentifierTypeIsNotString(){
		Criteria criteria = new Criteria(null, IdentifierType.PERSONID, LocalDate.of(2015, 1, 18));
		Object result = personalityCacheFacade.objectToLowerCase(criteria, "SUPERUSER");
		assertEquals("SUPERUSER", result);
	}

	@Test
	public void checkIfReturnedKeyIsSameAsPassedForGetExtensionsForCriteriaKey(){
		Object[] ids = { "adminuser", "AdminUser", "ADMINUSER" };
		Criteria criteria = new Criteria(ids, IdentifierType.PERSONNUMBER);
		EmployeeExtension employeeExtension = mock(EmployeeExtension.class);

		personalityCacheAccessor = new PersonalityCacheAccessor() {
			@Override
			public BaseExtension getExtensionBySecondaryKey(MultiExtensionKey key) {
				return employeeExtension;
			}

			@Override
			public Map<String, BaseExtension> getAllExtensionsBySecondaryKey(MultiExtensionKey key) {
				return new HashMap<>();
			}
		};

		AdapterHelper adapterHelper = new AdapterHelper();
		personalityCacheFacade.setCacheImpl(personalityCacheAccessor);
		personalityCacheFacade.setAdapterHelper(adapterHelper);
        personalityCacheFacade.concurrencyHelper = concurrencyHelper;
		TenantHandlingFacade tenantHandlingFacadeMock = Mockito.mock(TenantHandlingFacade.class);
		doReturn("-1L").when(tenantHandlingFacadeMock).getTenantId();
		doNothing().when(tenantHandlingFacadeMock).removeTenantId();
		personalityCacheFacade.concurrencyHelper.setTenantHandlingFacade(tenantHandlingFacadeMock);
		Map<Object, PersonalityResponse<BaseExtension>> resultMap = personalityCacheFacade.getExtensionsForCriteria(criteria, ExtensionAdapterEnum.EMPLOYEE);
		assertTrue(resultMap.containsKey("adminuser"));
		assertTrue(resultMap.containsKey("AdminUser"));
		assertTrue(resultMap.containsKey("ADMINUSER"));
		assertFalse(resultMap.containsKey("ADMinUser"));
	}

	@Test
	public void testProcessWithoutNotification(){
		PersonalityCacheFacade pcf = new PersonalityCacheFacade();
		StringBuilder sb = new StringBuilder();
		pcf.disableCacheNotificationService = getMockDisableCacheNotificationService(sb);
		Operation operation = ()->{};
		pcf.processWithoutNotification(operation);
		assertEquals("disableNotification called.reEnableNotification called.", sb.toString());

		//Testing exception case.
		operation =()->{
			throw new RuntimeException();
		};
		sb.setLength(0);
		try {
			pcf.processWithoutNotification(operation);
		} catch (Exception e) {

		}
		assertEquals("disableNotification called.reEnableNotification called.", sb.toString());
	}

	private DisableCacheNotificationService getMockDisableCacheNotificationService(StringBuilder sb) {
		// TODO Auto-generated method stub
		return new DisableCacheNotificationService() {

			@Override
			public void reEnableNotification() {
				// TODO Auto-generated method stub
				sb.append("reEnableNotification called.");
			}

			@Override
			public void disableNotification() {
				// TODO Auto-generated method stub
				sb.append("disableNotification called.");
			}
		};

	}

	@Test
	public void testProcessCacheEntries(){
		StringBuilder output = new StringBuilder();
		PersonalityCacheFacade personalityCacheFacade = new PersonalityCacheFacade(){
			protected void processWithoutNotification(Operation operation) {
				output.append("processWithoutNotification");
			}
		};

		personalityCacheFacade.personalityBatchProcessor = new PersonalityBatchProcessor(){
			public boolean isPersonPresentForGivenTenantIdAndRemoveFromMap(CacheEntry ce) {
				return true;
			}
		};
		personalityCacheFacade.adapterHelper = new AdapterHelper();
		List<CacheEntry> cacheEntryList = new ArrayList<>();
		CacheEntry cacheEntry = new CacheEntry(1L, "manufacturing");
		cacheEntryList.add(cacheEntry);
		personalityCacheFacade.processCacheEntries(cacheEntryList, (ccheEntry)->output.append("consumer"));
		assertEquals("processWithoutNotification",output.toString());
	}

	@Test
	public void testProcessCacheEntriesNootPresentInMap(){
		StringBuilder output = new StringBuilder();
		PersonalityCacheFacade personalityCacheFacade = new PersonalityCacheFacade(){
			protected void processWithoutNotification(Operation operation) {
				output.append("processWithoutNotification");
			}
		};

		personalityCacheFacade.personalityBatchProcessor = new PersonalityBatchProcessor(){
			public boolean isPersonPresentForGivenTenantIdAndRemoveFromMap(CacheEntry ce) {
				return false;
			}
		};
		personalityCacheFacade.adapterHelper = new AdapterHelper();
		List<CacheEntry> cacheEntryList = new ArrayList<>();
		CacheEntry cacheEntry = new CacheEntry(1L, "manufacturing");
		cacheEntryList.add(cacheEntry);
		personalityCacheFacade.processCacheEntries(cacheEntryList, (ccheEntry)->output.append("consumer"));
		assertEquals("consumer",output.toString());
	}

	@AfterEach
	public void destroy() {
		personalityCacheAccessor = null;
		personalityCacheFacade = null;
		System.gc();
	}
}
