package com.kronos.people.personality.service.impl;

import com.kronos.people.personality.converter.BrazilAssignmentConverter;
import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilCompanyAssignmentEntity;
import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilEmployeeEntity;
import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilPcaAssignmentEntity;
import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilRepTypeAssignmentEntity;
import com.kronos.people.personality.dataaccess.repository.brazilcompliance.BrazilCompanyAssignmentRepository;
import com.kronos.people.personality.dataaccess.repository.brazilcompliance.BrazilEmployeeRepository;
import com.kronos.people.personality.dataaccess.repository.brazilcompliance.BrazilPcaAssignmentRepository;
import com.kronos.people.personality.dataaccess.repository.brazilcompliance.BrazilRepTypeAssignmentRepository;
import com.kronos.people.personality.model.brazilcompliance.BrazilEmployeeDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

//@RunWith(PowerMockRunner.class)
//@PowerMockIgnore("javax.management.*")
@ExtendWith(MockitoExtension.class)
public class BrazilAssignmentServiceImplTest {

    private static final Long PERSONID_1 = 243L;
    private static final Long PERSONID_2 = 244L;
    private static final Long OBJECTID = 1L;
    private static final Long COMPANYID = 33333L;
    private static final Long PCAID = 44444L;
    private static final Long REPTYPEID = 1L;
    private static final String PIS = "PIS";
    private static final String ESOCIAL = "eSocial";
    private static final String CPF = "cpf";

    @Mock
    BrazilAssignmentConverter brazilAssignmentConverter;
    @Mock
    private BrazilEmployeeRepository brazilEmployeeRepository;
    @Mock
    private BrazilCompanyAssignmentRepository brazilCompanyAssignmentRepository;
    @Mock
    private BrazilPcaAssignmentRepository brazilPcaAssignmentRepository;
    @Mock
    private BrazilRepTypeAssignmentRepository brazilRepTypeAssignmentRepository;
    @InjectMocks
    private BrazilAssignmentServiceImpl brazilAssignmentService;

    @Test
    @SuppressWarnings("unchecked")
    public void testFindByPersonIds() {
        when(brazilEmployeeRepository.findByPersonIds(anyList())).thenReturn(createBrazilEmployeeEntity());
        when(brazilCompanyAssignmentRepository.findByPersonIds(anyList())).thenReturn(createBrazilCompanyAssignmentEntity());
        when(brazilPcaAssignmentRepository.findByPersonIds(anyList())).thenReturn(createBrazilPcaAssignmentEntity());
        when(brazilRepTypeAssignmentRepository.findByPersonIds(anyList())).thenReturn(createBrazilRepTypeAssignmentEntity());



        BrazilEmployeeDTO empDTO = new BrazilEmployeeDTO();
        empDTO.setEmployeeId(PERSONID_1);
        when(brazilAssignmentConverter.convertSingleEmployeeAssignmentEntitiesToDTO(any(), any(), anyList(), anyList(), anyList())).thenReturn(empDTO);


        List<BrazilEmployeeDTO> employeeDTOList = brazilAssignmentService.findByPersonIds(Collections.singletonList(PERSONID_1));
        assertEquals(1, employeeDTOList.size());
        assertEquals(PERSONID_1, employeeDTOList.get(0).getEmployeeId());

    }



    /**
     * Create Mock Entities
     **/
    List<BrazilEmployeeEntity> createBrazilEmployeeEntity() {
//        List<BrazilEmployeeEntity> list = new ArrayList();
        List<BrazilEmployeeEntity> list = new ArrayList<>();
        BrazilEmployeeEntity entity = new BrazilEmployeeEntity();
        entity.setPersonId(PERSONID_1);
        entity.setCpf(CPF);
        entity.seteSocial(ESOCIAL);
        entity.setPis(PIS);
        list.add(entity);

        entity = new BrazilEmployeeEntity();
        entity.setPersonId(PERSONID_2);
        entity.setCpf(CPF);
        entity.seteSocial(ESOCIAL);
        entity.setPis(PIS);
        list.add(entity);
        return list;
    }

    List<BrazilCompanyAssignmentEntity> createBrazilCompanyAssignmentEntity() {
//        List<BrazilCompanyAssignmentEntity> list = new ArrayList();
        List<BrazilCompanyAssignmentEntity> list = new ArrayList<>();
        BrazilCompanyAssignmentEntity entity = new BrazilCompanyAssignmentEntity();
        entity.setId(OBJECTID);
        entity.setPersonId(PERSONID_1);
        entity.setCompanyId(COMPANYID);
        entity.setEffectiveDate(LocalDateTime.now());
        list.add(entity);

        entity = new BrazilCompanyAssignmentEntity();
        entity.setId(OBJECTID);
        entity.setPersonId(PERSONID_2);
        entity.setCompanyId(COMPANYID);
        entity.setEffectiveDate(LocalDateTime.now());
        list.add(entity);
        return list;
    }

    List<BrazilPcaAssignmentEntity> createBrazilPcaAssignmentEntity() {
//        List<BrazilPcaAssignmentEntity> list = new ArrayList();
        List<BrazilPcaAssignmentEntity> list = new ArrayList<>();
        BrazilPcaAssignmentEntity entity = new BrazilPcaAssignmentEntity();
        entity.setId(OBJECTID);
        entity.setPersonId(PERSONID_1);
        entity.setPayCodeAttrDefId(PCAID);
        entity.setEffectiveDate(LocalDateTime.now());
        list.add(entity);

        entity = new BrazilPcaAssignmentEntity();
        entity.setId(OBJECTID);
        entity.setPersonId(PERSONID_2);
        entity.setPayCodeAttrDefId(PCAID);
        entity.setEffectiveDate(LocalDateTime.now());
        list.add(entity);
        return list;
    }

    List<BrazilRepTypeAssignmentEntity> createBrazilRepTypeAssignmentEntity() {
        List<BrazilRepTypeAssignmentEntity> list = new ArrayList<>();
        BrazilRepTypeAssignmentEntity entity = new BrazilRepTypeAssignmentEntity();
        entity.setId(OBJECTID);
        entity.setPersonId(PERSONID_1);
        entity.setRepTypeId(REPTYPEID);
        entity.setEffectiveDate(LocalDateTime.now());
        list.add(entity);
        return list;
    }
}
