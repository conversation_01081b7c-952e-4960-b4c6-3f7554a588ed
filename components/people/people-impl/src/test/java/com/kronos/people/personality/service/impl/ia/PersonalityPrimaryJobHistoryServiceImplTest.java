package com.kronos.people.personality.service.impl.ia;

import com.kronos.commonapp.labortransfer.api.ILaborTransferService;
import com.kronos.commonapp.labortransfer.model.LaborCategoryItems;
import com.kronos.commonapp.orgmap.setup.model.OrgObjectRef;
import com.kronos.commonapp.orgmap.setup.model.OrgObjectRefSpan;
import com.kronos.commonbusiness.datatypes.ia.IAColumn;
import com.kronos.commonbusiness.datatypes.ia.IAErrorDetail;
import com.kronos.commonbusiness.datatypes.ia.IARequest;
import com.kronos.commonbusiness.datatypes.ia.IAResponse;
import com.kronos.commonbusiness.datatypes.ia.IARow;
import com.kronos.commonbusiness.datatypes.ia.IARowAttribute;
import com.kronos.commonbusiness.datatypes.time.LocalDateSpan;
import com.kronos.people.personality.exception.BaseException;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import com.kronos.people.personality.exception.impl.ia.PersonalityExceptionHelper;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.PrimaryJobAccountEntry;
import com.kronos.people.personality.service.PersonalityExtendedAttributesService;
import com.kronos.people.personality.service.PersonalityService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

//@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonalityPrimaryJobHistoryServiceImplTest {
    private static final String EMP_HIST_PRIMARY_JOB_PATH = "EMP_HIST_PRIMARY_JOB_PATH";
    private static final String EMP_HIST_HOME_LABOR_CATEGORY = "EMP_HIST_HOME_LABOR_CATEGORY";
    private static final String EMP_HIST_PRIMARY_JOB_EFFECTIVE_DATE = "EMP_HIST_PRIMARY_JOB_EFFECTIVE_DATE";
    private static final String EMP_HIST_PRIMARY_JOB_EXPIRATION_DATE = "EMP_HIST_PRIMARY_JOB_EXPIRATION_DATE";

    private List<OrgObjectRefSpan> orgJobs;
    private List<PrimaryJobAccountEntry> employeePrimaryJobAccounts;
    private List<LaborCategoryItems> employeeLaborCategoryItems;

    @Mock
    private PersonalityService personalityService;

    @Mock
    private PersonalityExceptionHelper exceptionHelper;

    @Mock
    private PersonalityExtendedAttributesService personalityExtendedAttributesService;

    @Mock
    private ILaborTransferService laborTransferService;

    @InjectMocks
    private PersonalityPrimaryJobHistoryServiceImpl personalityPrimaryJobHistoryService;

    @BeforeEach
    public void setUp() {
        PrimaryJobAccountEntry account1 = buildAccount(44L, 52L, LocalDate.of(2017, 1, 2), LocalDate.of(2018, 1, 1));
        PrimaryJobAccountEntry account2 = buildAccount(45L, 53L, LocalDate.of(2018, 1, 2), LocalDate.of(2019, 1, 1));
        PrimaryJobAccountEntry account3 = buildAccount(46L, 54L, LocalDate.of(2019, 1, 2), LocalDate.of(2020, 1, 1));
        PrimaryJobAccountEntry account4 = buildAccount(46L, 55L, LocalDate.of(2020, 1, 2), LocalDate.of(3000, 1, 1));
        employeePrimaryJobAccounts = Arrays.asList(account1, account2, account3, account4);

        OrgObjectRefSpan orgObjectRef1 = new OrgObjectRefSpan(new OrgObjectRef(44L, "Job 1"), LocalDate.of(2017, 1, 2),
                LocalDate.of(2020, 1, 2));
        OrgObjectRefSpan orgObjectRef2 = new OrgObjectRefSpan(new OrgObjectRef(45L, "Job 2"), LocalDate.of(2018, 1, 2),
                LocalDate.of(2019, 1, 2));
        OrgObjectRefSpan orgObjectRef3 = new OrgObjectRefSpan(new OrgObjectRef(46L, "Job 3"), LocalDate.of(2019, 1, 2),
                LocalDate.of(2020, 1, 2));
        OrgObjectRefSpan orgObjectRef4 = new OrgObjectRefSpan(new OrgObjectRef(46L, "Job 3 Updated"),
                LocalDate.of(2020, 1, 2), LocalDate.of(3000, 1, 1));
        orgJobs = Arrays.asList(orgObjectRef1, orgObjectRef2, orgObjectRef3, orgObjectRef4);

        employeeLaborCategoryItems = employeePrimaryJobAccounts.stream()
                .map(PrimaryJobAccountEntry::getLaborAccountId)
                .map(this::buildPrimaryLabourCategory)
                .collect(Collectors.toList());
    }

    @Test
    public void testGetPrimaryJobHistoryPathColumn() {
        Long employeeId = 4L;
        List<String> expectedAttributeValues = Arrays
                .asList(orgJobs.get(1).getRef().getQualifier(), orgJobs.get(2).getRef().getQualifier());
        testGetPrimaryJobColumn(EMP_HIST_PRIMARY_JOB_PATH, expectedAttributeValues, employeeId);
    }

    @Test
    public void testGetPrimaryJobHistoryPathColumnRenamedJob() {
        Long employeeId = 9L;
        List<String> expectedAttributeValues = Arrays
                .asList(orgJobs.get(1).getRef().getQualifier(), orgJobs.get(2).getRef().getQualifier(),
                        orgJobs.get(3).getRef().getQualifier());
        IARequest request = buildDefaultRequest(employeeId, EMP_HIST_PRIMARY_JOB_PATH);
        request.getDateRange()
                .setEndDate(employeePrimaryJobAccounts.get(employeePrimaryJobAccounts.size() - 1).getEffectiveDate());
        testGetPrimaryJobColumn(request, expectedAttributeValues, employeeId);
    }

    @Test
    public void testGetPrimaryJobLabourCategoryColumn() {
        Long employeeId = 5L;
        List<String> expectedAttributeValues = Arrays.asList(employeeLaborCategoryItems.get(1).getLaborCategoryString(),
                employeeLaborCategoryItems.get(2).getLaborCategoryString());
        testGetPrimaryJobColumn(EMP_HIST_HOME_LABOR_CATEGORY, expectedAttributeValues, employeeId);
    }

    @Test
    public void testGetPrimaryJobEffectiveDateColumn() {
        Long employeeId = 6L;
        List<String> expectedAttributeValues = Stream.of(employeePrimaryJobAccounts.get(1).getEffectiveDate(),
                employeePrimaryJobAccounts.get(2).getEffectiveDate())
                .map(Object::toString)
                .collect(Collectors.toList());
        testGetPrimaryJobColumn(EMP_HIST_PRIMARY_JOB_EFFECTIVE_DATE, expectedAttributeValues, employeeId);
    }

    @Test
    public void testGetPrimaryJobExpirationDateColumn() {
        Long employeeId = 7L;
        List<String> expectedAttributeValues = Stream.of(employeePrimaryJobAccounts.get(1).getExpirationDate(),
                employeePrimaryJobAccounts.get(2).getExpirationDate())
                .map(Object::toString)
                .collect(Collectors.toList());
        testGetPrimaryJobColumn(EMP_HIST_PRIMARY_JOB_EXPIRATION_DATE, expectedAttributeValues, employeeId);
    }

    @Test
    public void testGetPrimaryJobColumnExtensionException() {
        Long employeeId = 8L;
        IAErrorDetail errorDetail = new IAErrorDetail();

        when(exceptionHelper.buildEmployeeExtensionErrorDetail(anyList(),
                any(IARequest.class))).thenReturn(errorDetail);

        String errorMessage = "Employee extension error message";
        PersonalityErrorCode errorCode = PersonalityErrorCode.NOT_FOUND;
        PersonalityExtensionException extensionException = new PersonalityExtensionException(errorCode, errorMessage);
        PersonalityResponse<EmployeeExtension> personalityResponse = new PersonalityResponse<>(null,
                extensionException);
        Map<Long, PersonalityResponse<EmployeeExtension>> personalityResponses = Collections
                .singletonMap(employeeId, personalityResponse);

        when(personalityService.findEmployeeExtensions(new Long[] { employeeId })).thenReturn(personalityResponses);

        IARequest request = buildDefaultRequest(employeeId, EMP_HIST_HOME_LABOR_CATEGORY);

        IAResponse actualResponse = personalityPrimaryJobHistoryService.getPrimaryJobHistory(request);

        assertEquals(errorDetail, actualResponse.getErrorDetails());
    }

    @Test
    public void testGetPrimaryJobColumnException() {
        Long employeeId = 9L;
        IAErrorDetail errorDetail = new IAErrorDetail();
        String errorMessage = "Employee extension error Message";
        PersonalityErrorCode errorCode = PersonalityErrorCode.NOT_FOUND;
        BaseException exception = new BaseException(errorCode, errorMessage, null, null);

        when(exceptionHelper.buildCommonErrorDetail(any(IARequest.class))).thenReturn(errorDetail);
        when(personalityService.findEmployeeExtensions(new Long[] { employeeId })).thenThrow(exception);

        IARequest request = buildDefaultRequest(employeeId, EMP_HIST_HOME_LABOR_CATEGORY);

        IAResponse actualResponse = personalityPrimaryJobHistoryService.getPrimaryJobHistory(request);

        IAErrorDetail error = actualResponse.getErrorDetails();
        assertEquals(errorDetail, error);
    }

    public void testGetPrimaryJobColumn(String columnName, List<String> expectedAttributeValues, Long employeeId) {
        IARequest request = buildDefaultRequest(employeeId, columnName);
        testGetPrimaryJobColumn(request, expectedAttributeValues, employeeId);
    }

    public void testGetPrimaryJobColumn(IARequest request, List<String> expectedAttributeValues, Long employeeId) {
        when(personalityService.findEmployeeExtensions(new Long[] { employeeId }))
                .thenReturn(buildExtensionMap(employeeId));

        when(laborTransferService.getLaborCategoryItemsFromAccountId(anyList()))
                .thenReturn(employeeLaborCategoryItems);

        when(personalityExtendedAttributesService.getPrimaryJob(anyLong(), any(LocalDate.class)))
                .thenAnswer(invocation -> {
                    Long jobId = (Long) invocation.getArguments()[0];
                    LocalDate date = (LocalDate) invocation.getArguments()[1];
                    return orgJobs.stream()
                            .filter(orgJob -> orgJob.getRef().getId().equals(jobId) && isInRange(date,
                                    orgJob.getEffectiveDate(), orgJob.getExpirationDate()))
                            .findAny()
                            .map(orgJob -> orgJob.getRef().getQualifier())
                            .orElse(null);
                });

        int numberOfExpectedRows = expectedAttributeValues.size();

        IAResponse actualResponse = personalityPrimaryJobHistoryService.getPrimaryJobHistory(request);

        assertNotNull(actualResponse);
        List<IARow> actualRows = actualResponse.getData();
        assertEquals(numberOfExpectedRows, actualRows.size());

        verifyRows(actualRows, expectedAttributeValues, request.getColumns().get(0).getName(), employeeId);
    }

    private void verifyRows(List<IARow> actualRows, List<String> expectedAttributeValues, String expectedAttributeName,
            Long employeeId) {
        actualRows.forEach(row -> {
            assertEquals(buildPersonNumber(employeeId), row.getDimensionKey());
            assertNotNull(row.getRowKey());
            Map<String, com.kronos.commonbusiness.datatypes.ref.ObjectRef> coreEntities = row.getCoreEntities();
            assertNotNull(coreEntities);
            assertEquals(1, coreEntities.size());
            com.kronos.commonbusiness.datatypes.ref.ObjectRef emp = coreEntities.get("EMP");
            assertEquals(buildPersonId(employeeId), emp.getId());
            assertEquals(buildPersonNumber(employeeId), emp.getQualifier());
            List<IARowAttribute> attributes = row.getAttributes();
            assertEquals(1, attributes.size());
            IARowAttribute firstAttribute = attributes.iterator().next();
            assertEquals(expectedAttributeName, firstAttribute.getName());
            assertTrue(expectedAttributeValues.contains(firstAttribute.getValue()));
        });
    }

    private IARequest buildDefaultRequest(Long employeeId, String column) {
        IARequest request = new IARequest();
        LocalDate startDate = LocalDate.of(2018, 1, 2);
        LocalDate endDate = LocalDate.of(2020, 1, 1);
        LocalDateSpan dateRange = new LocalDateSpan(startDate, endDate);
        request.setDateRange(dateRange);
        request.setColumns(Collections.singletonList(new IAColumn(column, column)));
        request.setEmployees(Collections.singletonList(employeeId.toString()));
        return request;
    }

    private Map<Long, PersonalityResponse<EmployeeExtension>> buildExtensionMap(Long employeeId) {
        EmployeeExtension extension = buildExtension(employeeId);

        return Collections.singletonMap(employeeId, new PersonalityResponse<>(extension, null));
    }

    private Long buildPersonId(Long employeeId) {
        return employeeId + 4L;
    }

    private String buildPersonNumber(Long employeeId) {
        return "PersonNumber" + employeeId.toString();
    }

    private EmployeeExtension buildExtension(Long employeeId) {
        EmployeeExtension extension = new EmployeeExtension();
        extension.setPersonId(buildPersonId(employeeId));
        extension.setPersonNumber(buildPersonNumber(employeeId));

        EffectiveDatedCollection<PrimaryJobAccountEntry> accounts = new EffectiveDatedCollection<>();
        accounts.setEffectiveDatedEntries(employeePrimaryJobAccounts);

        extension.setEffDatedPrimaryJobAccount(accounts);

        return extension;
    }

    private PrimaryJobAccountEntry buildAccount(Long orgId, Long laborAccountId, LocalDate effectiveDate,
            LocalDate expirationDate) {
        PrimaryJobAccountEntry account = new PrimaryJobAccountEntry();
        account.setPrimaryOrganizationId(orgId);
        account.setLaborAccountId(laborAccountId);
        account.setEffectiveDate(effectiveDate);
        account.setExpirationDate(expirationDate);
        return account;
    }

    private LaborCategoryItems buildPrimaryLabourCategory(Long id) {
        LaborCategoryItems item = new LaborCategoryItems();
        item.setLaborAccountId(id);
        item.setLaborCategoryString(buildPrimaryLabourCategoryName(id));
        return item;
    }

    private String buildPrimaryLabourCategoryName(Long id) {
        return "PrimaryLabourCategory" + id;
    }

    private boolean isInRange(LocalDate date, LocalDate effectiveDate, LocalDate expirationDate) {
        return effectiveDate.isEqual(date) || (effectiveDate.isBefore(date) && expirationDate.isAfter(date));
    }
}
