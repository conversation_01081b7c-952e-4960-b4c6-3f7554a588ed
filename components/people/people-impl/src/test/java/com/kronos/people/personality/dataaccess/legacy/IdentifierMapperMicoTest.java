package com.kronos.people.personality.dataaccess.legacy;

import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.people.personality.dataaccess.adapter.ConcurrencyHelper;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import com.kronos.people.personality.exception.impl.ExceptionHelper;
import com.kronos.people.personality.model.Criteria;
import com.kronos.people.personality.properties.KronosPropertiesFacade;
import com.kronos.people.personality.tenant.TenantHandlingFacade;
import com.kronos.people.personality.util.OptionalPersonId;
import com.kronos.wfc.commonapp.people.business.personality.PersonalityTriplet;
import com.kronos.wfc.platform.businessobject.framework.BusinessProcessingException;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.PersistenceException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ExecutorService;

import static com.kronos.people.personality.model.IdentifierType.JOBASSIGNMENTID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class IdentifierMapperMicoTest {

	IdentifierMapper idMap;
	
	KronosPropertiesFacade kronosPropertiesFacade = new KronosPropertiesFacade();
	
	@Mock
	TenantHandlingFacade tenantHandlingFacade;
	@Mock
	KronosPropertiesFacade propertiesFacade;
	@Mock
	KronosThreadPoolService threadPoolService;
	@Mock
	ExecutorService executor;
	
	@BeforeEach
	public void setUp() {
		when(propertiesFacade.getIntegerKronosProperty(any(),anyInt())).thenReturn(100);
		when(threadPoolService.newThreadPool(any())).thenReturn(executor);
		idMap = new IdentifierMapper();
		idMap.setExceptionHandler(new ExceptionHelper(null));
		idMap.concurrencyHelper = new ConcurrencyHelper(tenantHandlingFacade,propertiesFacade,null);
		idMap.concurrencyHelper.setTenantHandlingFacade(mock((TenantHandlingFacade.class)));
		idMap.concurrencyHelper.setKronosPropertiesFacade(kronosPropertiesFacade);
	}

	
	@Test
	public void getPersonIdsSecondaryIdTest() {
		
		KronosPropertiesFacade kronosPropertiesFacade = new KronosPropertiesFacade(){
			protected String getPropertyFromKronosProperty(String key) {
				return "Value";
			}
		};
		idMap.concurrencyHelper.setKronosPropertiesFacade(kronosPropertiesFacade);
		Criteria ct = mock(Criteria.class);
		Object[] obj = { 12l};
		when(ct.getIds()).thenReturn(obj);
		when(ct.getIdsType()).thenReturn(JOBASSIGNMENTID);
		
		PersonalityFacade perFac=mock(PersonalityFacade.class);
		when(perFac.getPersonalityTriplet(12l, JOBASSIGNMENTID)).thenReturn(null);
		idMap.setPersonalityFacade(perFac);

		Map<Object, OptionalPersonId> map = idMap.getPersonIds(ct);

		Iterator iterator = map.entrySet().iterator();
		while (iterator.hasNext()) {

			Map.Entry<Object, OptionalPersonId> entry = (Map.Entry<Object, OptionalPersonId>) iterator.next();
			assertNull(entry.getValue().getException());
			assertNull(entry.getValue().getPersonId());
		}
	}
	
	@Test
	public void getPersonIdsSecondaryIdExcp2Test() {

		KronosPropertiesFacade kronosPropertiesFacade = new KronosPropertiesFacade(){
			protected String getPropertyFromKronosProperty(String key) {
				return "Value";
			}
		};
		idMap.concurrencyHelper.setKronosPropertiesFacade(kronosPropertiesFacade);
		Criteria ct = mock(Criteria.class);
		Object[] obj = { 12l};
		when(ct.getIds()).thenReturn(obj);
		when(ct.getIdsType()).thenReturn(JOBASSIGNMENTID);
		
		PersonalityFacade perFac=mock(PersonalityFacade.class);
		when(perFac.getPersonalityTriplet(12l, JOBASSIGNMENTID)).thenThrow(new PersistenceException());
		idMap.setPersonalityFacade(perFac);

		Map<Object, OptionalPersonId> map = idMap.getPersonIds(ct);

		Iterator iterator = map.entrySet().iterator();
		while (iterator.hasNext()) {

			Map.Entry<Object, OptionalPersonId> entry = (Map.Entry<Object, OptionalPersonId>) iterator.next();
			assertEquals("JobAssignmentId is not valid.", ((PersonalityExtensionException)entry.getValue().getException()).getMessage());
		}
	}
	

	@Test
	public void getPersonIdsSecondaryIdExcp3Test() {

		KronosPropertiesFacade kronosPropertiesFacade = new KronosPropertiesFacade(){
			protected String getPropertyFromKronosProperty(String key) {
				return "Value";
			}
		};
		idMap.concurrencyHelper.setKronosPropertiesFacade(kronosPropertiesFacade);
		Criteria ct = mock(Criteria.class);
		Object[] obj = { 12l};
		when(ct.getIds()).thenReturn(obj);
		when(ct.getIdsType()).thenReturn(JOBASSIGNMENTID);
		
		PersonalityFacade perFac=mock(PersonalityFacade.class);
		when(perFac.getPersonalityTriplet(12l, JOBASSIGNMENTID)).thenThrow(new RuntimeException());
		idMap.setPersonalityFacade(perFac);

		Map<Object, OptionalPersonId> map = idMap.getPersonIds(ct);

		Iterator iterator = map.entrySet().iterator();
		while (iterator.hasNext()) {

			Map.Entry<Object, OptionalPersonId> entry = (Map.Entry<Object, OptionalPersonId>) iterator.next();
			assertEquals("JobAssignmentId is not valid.", ((PersonalityExtensionException)entry.getValue().getException()).getMessage());
		}
	}
	
	@Test
	public void getPersonIdFromCacheTest() {

		KronosPropertiesFacade kronosPropertiesFacade = new KronosPropertiesFacade(){
			protected String getPropertyFromKronosProperty(String key) {
				return "Value";
			}
		};
		idMap.concurrencyHelper.setKronosPropertiesFacade(kronosPropertiesFacade);
		Criteria ct = mock(Criteria.class);
		Object[] obj = { 12l};
		when(ct.getIds()).thenReturn(obj);
		when(ct.getIdsType()).thenReturn(JOBASSIGNMENTID);
		
		PersonalityFacade perFac=mock(PersonalityFacade.class);
		when(perFac.getPersonalityTriplet(12l, JOBASSIGNMENTID)).thenThrow(new RuntimeException());
		idMap.setPersonalityFacade(perFac);

		Map<Object, OptionalPersonId> map = idMap.getPersonIds(ct);

		Iterator iterator = map.entrySet().iterator();
		while (iterator.hasNext()) {

			Map.Entry<Object, OptionalPersonId> entry = (Map.Entry<Object, OptionalPersonId>) iterator.next();
			assertEquals("JobAssignmentId is not valid.", ((PersonalityExtensionException)entry.getValue().getException()).getMessage());
		}
	}
	
	@Test
	public void getPersonIdTest()
	{
		PersonalityTriplet pTr=mock(PersonalityTriplet.class);
		when(pTr.getPersonId()).thenReturn(new ObjectIdLong(123l));
		
		Long ptr=idMap.getPersonId(pTr);
		assertEquals(Long.valueOf(123), ptr);
	}
	
	@Test
	public void getPersonIdsSecondaryId2Test() {

		KronosPropertiesFacade kronosPropertiesFacade = new KronosPropertiesFacade(){
			protected String getPropertyFromKronosProperty(String key) {
				return "Value";
			}
		};
		idMap.concurrencyHelper.setKronosPropertiesFacade(kronosPropertiesFacade);
		Criteria ct = mock(Criteria.class);
		Object[] obj = { 123l};
		when(ct.getIds()).thenReturn(obj);
		when(ct.getIdsType()).thenReturn(JOBASSIGNMENTID);
		
		PersonalityTriplet pTr=mock(PersonalityTriplet.class);
		when(pTr.getPersonId()).thenReturn(new ObjectIdLong(123l));
		
		PersonalityFacade perFac=mock(PersonalityFacade.class);
		when(perFac.getPersonalityTriplet(123l, JOBASSIGNMENTID)).thenReturn(pTr);
		idMap.setPersonalityFacade(perFac);

		Map<Object, OptionalPersonId> map = idMap.getPersonIds(ct);

		Iterator iterator = map.entrySet().iterator();
		while (iterator.hasNext()) {

			Map.Entry<Object, OptionalPersonId> entry = (Map.Entry<Object, OptionalPersonId>) iterator.next();
			assertNull(entry.getValue().getException());
			assertEquals(Long.valueOf(123),entry.getValue().getPersonId());
		}
	}

	@Test
	public void getPersonIdsSecondaryIdExcp4Test() {

		KronosPropertiesFacade kronosPropertiesFacade = new KronosPropertiesFacade(){
			protected String getPropertyFromKronosProperty(String key) {
				return "Value";
			}
		};
		idMap.concurrencyHelper.setKronosPropertiesFacade(kronosPropertiesFacade);
		Criteria ct = mock(Criteria.class);
		Object[] obj = { 12l};
		when(ct.getIds()).thenReturn(obj);
		when(ct.getIdsType()).thenReturn(JOBASSIGNMENTID);

		PersonalityFacade perFac=mock(PersonalityFacade.class);
		when(perFac.getPersonalityTriplet(12l, JOBASSIGNMENTID)).thenThrow(new PersonalityExtensionException(PersonalityErrorCode.JOB_ASSIGNMENT_ID_NOT_VALID, "JobAssignmentId is not valid."));
		idMap.setPersonalityFacade(perFac);

		Map<Object, OptionalPersonId> map = idMap.getPersonIds(ct);

		Iterator iterator = map.entrySet().iterator();
		while (iterator.hasNext()) {

			Map.Entry<Object, OptionalPersonId> entry = (Map.Entry<Object, OptionalPersonId>) iterator.next();
			assertEquals("JobAssignmentId is not valid.", ((PersonalityExtensionException)entry.getValue().getException()).getMessage());
		}
	}

	@Test
	public void getPersonIdsSecondaryIdExcp5Test() {

		KronosPropertiesFacade kronosPropertiesFacade = new KronosPropertiesFacade(){
			protected String getPropertyFromKronosProperty(String key) {
				return "Value";
			}
		};
		idMap.concurrencyHelper.setKronosPropertiesFacade(kronosPropertiesFacade);
		Criteria ct = mock(Criteria.class);
		Object[] obj = { 12l};
		when(ct.getIds()).thenReturn(obj);
		when(ct.getIdsType()).thenReturn(JOBASSIGNMENTID);

		PersonalityFacade perFac=mock(PersonalityFacade.class);
		when(perFac.getPersonalityTriplet(12l, JOBASSIGNMENTID)).thenThrow(new BusinessProcessingException());
		idMap.setPersonalityFacade(perFac);

		Map<Object, OptionalPersonId> map = idMap.getPersonIds(ct);

		Iterator iterator = map.entrySet().iterator();
		while (iterator.hasNext()) {

			Map.Entry<Object, OptionalPersonId> entry = (Map.Entry<Object, OptionalPersonId>) iterator.next();
			assertEquals("JobAssignmentId is not valid.", ((PersonalityExtensionException)entry.getValue().getException()).getMessage());
		}
	}
}
