/*******************************************************************************
 * LocalDateTimeConverterTest.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.dataaccess.converter;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * Verifies {@link LocalDateTimeConverter}.
 * Copyright (C) 2019 Kronos.com
 * Date: Jun 25, 2019
 *
 * <AUTHOR> Sharma
 */
@ExtendWith(MockitoExtension.class)
public class LocalDateTimeConverterTest {

    @InjectMocks
    LocalDateTimeConverter localDateTimeConverter;

    @Test
    public void testConvertToDatabaseColumn() {
        LocalDateTime localDateTime = LocalDateTime.now();
        Timestamp result = localDateTimeConverter.convertToDatabaseColumn(localDateTime);
        assertEquals(result, Timestamp.valueOf(localDateTime));
    }

    @Test
    public void testConvertToDatabaseColumnWhenLocalDateTimeIsNull() {
        Timestamp result = localDateTimeConverter.convertToDatabaseColumn(null);
        assertNull( result);
    }

    @Test
    public void testConvertToEntityAttribute() {
        LocalDateTime localDateTime = LocalDateTime.now();
        Timestamp timestamp = Timestamp.valueOf(localDateTime);
        LocalDateTime result = localDateTimeConverter.convertToEntityAttribute(timestamp);
        assertEquals(result, localDateTime);
    }

    @Test
    public void testConvertToEntityAttributeWhenTimeStampIsNull() {
        LocalDateTime result = localDateTimeConverter.convertToEntityAttribute(null);
        assertNull(result);
    }
}
