package com.kronos.people.personality.facade;

import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.dataaccess.adapter.ConcurrencyHelper;
import com.kronos.people.personality.dataaccess.adapter.EmployeeExtensionAdapter;
import com.kronos.people.personality.dataaccess.adapter.ExtensionAdapterEnum;
import com.kronos.people.personality.dataaccess.legacy.ExtensionBuilder;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.model.Criteria;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.AllExtension;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.properties.KronosPropertiesFacade;
import com.kronos.people.personality.tenant.TenantHandlingFacade;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutorService;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class AllExtensionsFacadeMicroTest {
	@Spy
	private AllExtensionsFacade allExtensionsFacade;
	@Mock
	PersonalityCacheFacade cacheFacadeMock;
	@Mock
	ExtensionBuilder extensionBuilderMock;
	@Mock
	AdapterHelper adapterHelperMock;
	@Mock
	KronosPropertiesFacade propertiesFacade;
	@Mock
	KronosThreadPoolService threadPoolService;
	@Mock
	ExecutorService executor;

	@Mock
	private EmployeeExtensionAdapter employeeExtensionAdapter;


	private ExtensionFacade extensionFacade;

	@BeforeEach
	public void setUp() throws Exception {
		when(propertiesFacade.getIntegerKronosProperty(any(),anyInt())).thenReturn(100);
		when(threadPoolService.newThreadPool(any())).thenReturn(executor);
		extensionFacade = new ExtensionFacade();
		allExtensionsFacade.setExtensionFacade(extensionFacade);
		//allExtensionsFacade.extensionCacheOperations=mock(ExtensionCacheOperations.class);
		allExtensionsFacade.adapterHelper = adapterHelperMock;
		allExtensionsFacade.concurrencyHelper = new ConcurrencyHelper(null,propertiesFacade,null);
		allExtensionsFacade.concurrencyHelper.setTenantHandlingFacade(mock(TenantHandlingFacade.class));
	}

	@AfterEach
	public void tearDown(){
		allExtensionsFacade = null;
		extensionFacade = null;
		cacheFacadeMock = null;
		extensionBuilderMock = null;
		adapterHelperMock = null;
	}

	@Test
	public void testCreateAllExtensionToGetAllExtensionFromRedisWithoutLocalDate(){
		allExtensionsFacade.extensionBuilder = extensionBuilderMock;
		extensionFacade.employeeExtensionAdapter = employeeExtensionAdapter;
		mockGetAllExtensionForPersonId();
		PersonalityResponse<AllExtension>  personalityResponse = allExtensionsFacade.findAllExtensions(1L);

		assertNotNull(personalityResponse.getExtension().getEmployeeExtension());
		assertEquals(Long.valueOf(1), personalityResponse.getExtension().getEmployeeExtension().getPersonId());
	}


	@Test
	public void testCreateAllExtensionToGetAllExtensionFromRedisWithLocalDate(){
		allExtensionsFacade.extensionBuilder = extensionBuilderMock;
		extensionFacade.employeeExtensionAdapter = employeeExtensionAdapter;
		mockGetAllExtensionForPersonId();
		PersonalityResponse<AllExtension>  personalityResponse = allExtensionsFacade.findAllExtensions(1L, LocalDate.now());

		assertNotNull(personalityResponse.getExtension().getEmployeeExtension());
		assertEquals(Long.valueOf(1), personalityResponse.getExtension().getEmployeeExtension().getPersonId());
	}

	@Test
	public void testCreateAllExtension(){
		cacheFacadeMock = new PersonalityCacheFacade(){
			@Override
			public PersonalityResponse<AllExtension> getAllExtensionForPersonId(
					Long personId, LocalDate snapShotDate) {
				//AllExtension allExtension = buildAllExtension();
				AllExtension allExtension = new AllExtension();
				PersonalityResponse<AllExtension> response =new PersonalityResponse<AllExtension>(null, null);
				return null;
			}
		};

		allExtensionsFacade.cacheFacade = cacheFacadeMock;
		mockExtensionBuilderAndPushToRedis();
		PersonalityResponse<AllExtension>  personalityResponse = allExtensionsFacade.createAllExtension(1L, LocalDate.now());
		assertNotNull(personalityResponse);
	}

	@Test
	public void testCreateAllExtensionNullPersonId(){
		assertEquals(PersonalityErrorCode.PERSON_ID_NOT_VALID.toString(), allExtensionsFacade.createAllExtension(null, LocalDate.now()).getException().getErrorCode());
	}

	@Test
	public void testCreateAllExtensionWhenAllExtensionIsNotPresent(){
		allExtensionsFacade.extensionBuilder = extensionBuilderMock;
		extensionFacade.employeeExtensionAdapter = employeeExtensionAdapter;
		mockGetAllExtensionForPersonId();
		mockExtensionBuilderAndPushToRedis();
		PersonalityResponse<AllExtension>  personalityResponse = allExtensionsFacade.createAllExtension(1L, LocalDate.now());

		assertNotNull(personalityResponse.getExtension().getEmployeeExtension());
		assertEquals(Long.valueOf(1), personalityResponse.getExtension().getEmployeeExtension().getPersonId());
	}

	@Test
	public void testCreateAllExtensionWithCacheAndPrimaryJobNotBlank() {
		allExtensionsFacade.extensionBuilder = extensionBuilderMock;
		allExtensionsFacade.cacheFacade = cacheFacadeMock;
		extensionFacade.employeeExtensionAdapter = employeeExtensionAdapter;
		LocalDate snapshotDate = LocalDate.of(2014, 1, 21);

		EmployeeExtension employeeExtensionMock = mock(EmployeeExtension.class);
		when(employeeExtensionMock.getPersonId()).thenReturn(1L);
		when(employeeExtensionMock.isPrimaryJobAccountBlank()).thenReturn(false);
		AllExtension allExtensionMock = mock(AllExtension.class);
		when(allExtensionMock.getEmployeeExtension()).thenReturn(employeeExtensionMock);


		PersonalityResponse<AllExtension> personalityResponse = mock(PersonalityResponse.class);

		when(personalityResponse.getExtension()).thenReturn(allExtensionMock);
		when(cacheFacadeMock.getAllExtensionForPersonId(1L, snapshotDate)).thenReturn(personalityResponse);

		PersonalityResponse<AllExtension> output = allExtensionsFacade.createAllExtension(1L, snapshotDate);

		assertNotNull(output.getExtension().getEmployeeExtension());
		assertEquals(Long.valueOf(1), output.getExtension().getEmployeeExtension().getPersonId());
		verify(employeeExtensionAdapter, times(0)).updateJobAssignment(1L, employeeExtensionMock);
	}

	@Test
	public void testCreateAllExtensionWithCacheAndPrimaryJobBlank() {
		allExtensionsFacade.extensionBuilder = extensionBuilderMock;
		allExtensionsFacade.cacheFacade = cacheFacadeMock;
		extensionFacade.employeeExtensionAdapter = employeeExtensionAdapter;
		LocalDate snapshotDate = LocalDate.of(2014, 1, 21);

		EmployeeExtension employeeExtensionMock = mock(EmployeeExtension.class);
		when(employeeExtensionMock.getPersonId()).thenReturn(1L);
		when(employeeExtensionMock.isPrimaryJobAccountBlank()).thenReturn(true);
		AllExtension allExtensionMock = mock(AllExtension.class);
		when(allExtensionMock.getEmployeeExtension()).thenReturn(employeeExtensionMock);


		PersonalityResponse<AllExtension> personalityResponse = mock(PersonalityResponse.class);

		when(personalityResponse.getExtension()).thenReturn(allExtensionMock);
		when(cacheFacadeMock.getAllExtensionForPersonId(1L, snapshotDate)).thenReturn(personalityResponse);

		PersonalityResponse<AllExtension> output = allExtensionsFacade.createAllExtension(1L, snapshotDate);

		assertNotNull(output.getExtension().getEmployeeExtension());
		assertEquals(Long.valueOf(1), output.getExtension().getEmployeeExtension().getPersonId());
		verify(employeeExtensionAdapter, times(1)).updateJobAssignment(1L, employeeExtensionMock);
	}

	@Test
	public void testFindAllExtensionsWithoutLocalDate(){
		getAllExtensionMock();
		Long[] personIds = {1L, 2L};
		allExtensionsFacade.concurrencyHelper = new ConcurrencyHelper(null,propertiesFacade,null){
			public Integer getNumberOfProcessingThreads(String key) {
				return 3;
			}
		};
		TenantHandlingFacade tenantHandlingFacadeMock = Mockito.mock(TenantHandlingFacade.class);
		Mockito.doReturn("-1L").when(tenantHandlingFacadeMock).getTenantId();
		Mockito.doNothing().when(tenantHandlingFacadeMock).removeTenantId();
		allExtensionsFacade.concurrencyHelper.setTenantHandlingFacade(tenantHandlingFacadeMock);
		allExtensionsFacade.cacheFacade = cacheFacadeMock;
		Map<Long, PersonalityResponse<AllExtension>> allExtMap = allExtensionsFacade.findAllExtensions(personIds);

		assertNotNull(allExtMap);
	}

	@Test
	public void testFindAllExtensionsWithLocalDate(){
		getAllExtensionMock();
		Long[] personIds = {1L, 2L};
		allExtensionsFacade.cacheFacade = cacheFacadeMock;
		allExtensionsFacade.concurrencyHelper = new ConcurrencyHelper(null,propertiesFacade,null){
			public Integer getNumberOfProcessingThreads(String key) {
				return 3;
			}
		};
		TenantHandlingFacade tenantHandlingFacadeMock = Mockito.mock(TenantHandlingFacade.class);
		Mockito.doReturn("-1L").when(tenantHandlingFacadeMock).getTenantId();
		Mockito.doNothing().when(tenantHandlingFacadeMock).removeTenantId();
		allExtensionsFacade.concurrencyHelper.setTenantHandlingFacade(tenantHandlingFacadeMock);
		Map<Long, PersonalityResponse<AllExtension>> allExtMap = allExtensionsFacade.findAllExtensions(personIds, LocalDate.now());

		assertNotNull(allExtMap);
	}

	@Test
	public void testFindAllWhenPersonIdEqulToOutputMap(){
		getAllExtensionMock();
		Long[] personIds = {1L};
		allExtensionsFacade.cacheFacade = cacheFacadeMock;
		Map<Long, PersonalityResponse<AllExtension>> allExtMap = allExtensionsFacade.findAllExtensions(personIds, LocalDate.now());

		assertNotNull(allExtMap);

	}

	@Test
	public void testFindAllExtensionWhenIdNotINSet(){
		getAllExtensionMock();
		Long[] personIds = {2L, 3L};
		allExtensionsFacade.concurrencyHelper = new ConcurrencyHelper(null,propertiesFacade,null){
			public Integer getNumberOfProcessingThreads(String key) {
				return 3;
			}
		};
		TenantHandlingFacade tenantHandlingFacadeMock = Mockito.mock(TenantHandlingFacade.class);
		Mockito.doReturn("-1L").when(tenantHandlingFacadeMock).getTenantId();
		Mockito.doNothing().when(tenantHandlingFacadeMock).removeTenantId();
		allExtensionsFacade.concurrencyHelper.setTenantHandlingFacade(tenantHandlingFacadeMock);
		allExtensionsFacade.cacheFacade = cacheFacadeMock;
		Set<Long> s = new HashSet<>();
		s.add(1L);
		List<Long> value = new ArrayList<Long>();
		value.add(1L);
		when(adapterHelperMock.getIdsNotInSet(personIds, s)).thenReturn(value);
		mockExtensionBuilderAndPushToRedis();
		Map<Long, PersonalityResponse<AllExtension>> allExtMap = allExtensionsFacade.findAllExtensions(personIds, LocalDate.now());

		assertNotNull(allExtMap);

	}

	@Test
	public void testGetDataFromNormalFlow(){

		Criteria criteriaMock = mock(Criteria.class);
		when(criteriaMock.isOnlyActivePerson()).thenReturn(false);
		getAllExtensionMock();
		ExtensionFacade extensionFacadeMock = mock(ExtensionFacade.class);
		Long[] personIds = {1L, 2L};
		when(extensionFacadeMock.getIdsArray(criteriaMock)).thenReturn(personIds);
		when(criteriaMock.getIds()).thenReturn(personIds);
		allExtensionsFacade.concurrencyHelper = new ConcurrencyHelper(null,propertiesFacade,null){
			public Integer getNumberOfProcessingThreads(String key) {
				return 3;
			}
		};
		TenantHandlingFacade tenantHandlingFacadeMock = Mockito.mock(TenantHandlingFacade.class);
		Mockito.doReturn("-1L").when(tenantHandlingFacadeMock).getTenantId();
		Mockito.doNothing().when(tenantHandlingFacadeMock).removeTenantId();
		allExtensionsFacade.concurrencyHelper.setTenantHandlingFacade(tenantHandlingFacadeMock);
		allExtensionsFacade.cacheFacade = cacheFacadeMock;
		Map<Object, PersonalityResponse<AllExtension>> map = allExtensionsFacade.getDataFromNormalFlow(criteriaMock , ExtensionAdapterEnum.EMPLOYEE);

		assertNotNull(map);
	}

	public void getAllExtensionMock() {
		cacheFacadeMock = new PersonalityCacheFacade(){
			@Override
			public Map<Long, PersonalityResponse<AllExtension>> getAllExtensions(
					Long[] personIds, LocalDate snapShotDate) {
				Map<Long, PersonalityResponse<AllExtension>> allExtensions = new HashMap<>();

				PersonalityResponse<AllExtension> value = new PersonalityResponse<>();
				AllExtension allExtension = new AllExtension();
		        EmployeeExtension employeeExtension = new EmployeeExtension();
		        employeeExtension.setPersonId(1L);
		        allExtension.setEmployeeExtension(employeeExtension );
				value.setextension(allExtension);
				allExtensions.put(1L, value);
				return allExtensions;
			}
		};
	}

	public void mockGetAllExtensionForPersonId() {
		cacheFacadeMock = new PersonalityCacheFacade(){
			@Override
			public PersonalityResponse<AllExtension> getAllExtensionForPersonId(
					Long personId, LocalDate snapShotDate) {
				AllExtension allExtension = buildAllExtension();
				PersonalityResponse<AllExtension> response =new PersonalityResponse<AllExtension>(allExtension, null);
				return response;
			}
		};

		allExtensionsFacade.cacheFacade = cacheFacadeMock;
	}

	/**
	 *
	 */
	public void mockExtensionBuilderAndPushToRedis() {
		extensionBuilderMock = new ExtensionBuilder(null, null, null, null, propertiesFacade, null, null, null, null, null, null, threadPoolService, null, null) {
			@Override
			public AllExtension buildAllExtensionAndPushToRedis(Long personId) {
				AllExtension allExtension = buildAllExtension();
				return allExtension;
			}
		};
		allExtensionsFacade.extensionBuilder = extensionBuilderMock;
	}

	/**
	 * @return
	 */
	public AllExtension buildAllExtension() {
		AllExtension allExtension = new AllExtension();
		EmployeeExtension employeeExtension = new EmployeeExtension();
		employeeExtension.setPersonId(1L);
		allExtension.setEmployeeExtension(employeeExtension );
		return allExtension;
	}	
	
	/*
	 * 
	@Test
	public void testCreateAllExtension(){
		allExtensionsFacade = new AllExtensionsFacade() {
			@Override
			protected <T> Map<T, PersonalityResponse<AllExtension>> mergeExtensionsMap(
					List<AllExtensionHelper> entries) {
				assertEquals(5,entries.size());
				assertEquals(this.accuralConsumer,entries.get(0).getConsumer());
				assertEquals(this.deviceConsumer,entries.get(1).getConsumer());
				assertEquals(this.employeeConsumer,entries.get(2).getConsumer());
				assertEquals(this.schedulingConsumer,entries.get(3).getConsumer());
				assertEquals(this.timekeepingConsumer,entries.get(4).getConsumer());
				myMethodCalled = true;
				return getMockAllExtensionMap();
			}

		};
		myMethodCalled = false;
		setExtensionFacadeWithId();
		allExtensionsFacade.setExtensionFacade(extensionFacade);
		allExtensionsFacade.cacheFacade = mock(PersonalityCacheFacade.class);
		PersonalityResponse<AllExtension> personalityResponse = allExtensionsFacade.createAllExtension(1L,LocalDate.of(2014, 1, 21));


		PersonalityResponse<AllExtension> personalityResponseResult = new PersonalityResponse(getAllExtension(),null);
		assertTrue(myMethodCalled);
		assertEquals(personalityResponseResult.getExtension(),personalityResponse.getExtension());
		assertEquals(personalityResponseResult.getExtension().getAccrualExtension().getPersonId(),personalityResponse.getExtension().getAccrualExtension().getPersonId());
		assertEquals(personalityResponseResult.getExtension().getEmployeeExtension().getPersonId(),personalityResponse.getExtension().getEmployeeExtension().getPersonId());
		assertEquals(personalityResponseResult.getExtension().getDeviceExtension().getPersonId(),personalityResponse.getExtension().getDeviceExtension().getPersonId());
		assertEquals(personalityResponseResult.getExtension().getTimekeepingExtension().getPersonId(),personalityResponse.getExtension().getTimekeepingExtension().getPersonId());
		assertEquals(personalityResponseResult.getExtension().getSchedulingExtension().getPersonId(),personalityResponse.getExtension().getSchedulingExtension().getPersonId());

	}

 
    @Test
	public void testCreateAllCallFromFindExtn(){
		allExtensionsFacade = new AllExtensionsFacade() {
			@Override
			protected PersonalityResponse<AllExtension> createAllExtension(
					Long personId, LocalDate snapShotDate) {
				myMethodCalled = true;
				return new PersonalityResponse<AllExtension>(getAllExtension(),null);
			}
		};
		 myMethodCalled = false;
		 allExtensionsFacade.findAllExtensions(1l,LocalDate.of(2014, 1, 21));
		 assertTrue(myMethodCalled);

	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Test
	public void testMergeOneItemWithExtension() {
		AccrualExtension accrualExtension = new AccrualExtension();
		accrualExtension.setPersonId(1L);
		accrualExtension.setActive(true);
		accrualExtension.setPersonNumber("1");
		accrualExtension.setSnapShotDate(LocalDate.of(2015,11,11));
		DevicesExtension devicesExtension = new DevicesExtension();
		EmployeeExtension employeeExtension = new EmployeeExtension();
		TimekeepingExtension timeKeepingExtension = new TimekeepingExtension();
		SchedulingExtension schedulingExtension = new SchedulingExtension();
		
		List<AllExtensionHelper> allExtensionHelperList = new ArrayList<>();
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(accrualExtension,null),allExtensionsFacade.accuralConsumer));
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(devicesExtension,null),allExtensionsFacade.deviceConsumer));
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(employeeExtension,null),allExtensionsFacade.employeeConsumer));
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(timeKeepingExtension,null),allExtensionsFacade.timekeepingConsumer));
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(schedulingExtension,null),allExtensionsFacade.schedulingConsumer));

		PersonalityResponse<AllExtension> response = allExtensionsFacade.mergeOneItem(allExtensionHelperList, null);
		assertNull(response.getException());
		assertEquals(accrualExtension,response.getExtension().getAccrualExtension());
		assertEquals(devicesExtension,response.getExtension().getDeviceExtension());
		assertEquals(employeeExtension,response.getExtension().getEmployeeExtension());
		assertEquals(timeKeepingExtension,response.getExtension().getTimekeepingExtension());
		assertEquals(schedulingExtension,response.getExtension().getSchedulingExtension());
		assertEquals(accrualExtension.getPersonId(),response.getExtension().getPersonId());
		assertEquals(accrualExtension.getPersonNumber(),response.getExtension().getPersonNumber());
		assertEquals(accrualExtension.getSnapShotDate(),response.getExtension().getSnapShotDate());
		assertEquals(accrualExtension.isActive(),response.getExtension().isActive());

	}
	
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Test
	public void testMergeOneItemWithFunction() {
		
		AccrualExtension accrualExtension = new AccrualExtension();
		DevicesExtension devicesExtension = new DevicesExtension();
		EmployeeExtension employeeExtension = new EmployeeExtension();
		TimekeepingExtension timeKeepingExtension = new TimekeepingExtension();
		SchedulingExtension schedulingExtension = new SchedulingExtension();

		timekeepingCalled = false;
		accuralCalled = false;
		deviceCalled = false;
		employeeCalled = false;
		schedulingCalled = false;

		List<AllExtensionHelper> allExtensionHelperList = new ArrayList<>();
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(accrualExtension,null),(t,u)->accuralCalled = true));
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(devicesExtension,null),(t,u)->deviceCalled = true));
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(employeeExtension,null),(t,u)->employeeCalled = true));
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(timeKeepingExtension,null),(t,u)->timekeepingCalled = true));
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(schedulingExtension,null),(t,u)->schedulingCalled = true));
		
		allExtensionsFacade.mergeOneItem(allExtensionHelperList, null);
		
		assertTrue(accuralCalled);
		assertTrue(deviceCalled);
		assertTrue(employeeCalled);
		assertTrue(timekeepingCalled);
		assertTrue(schedulingCalled);

	}
	
	@SuppressWarnings({ "rawtypes", "unchecked"})
	@Test
	public void testMergeOneItemWithException() {
		
		AccrualExtension accrualExtension = new AccrualExtension();
		DevicesExtension devicesExtension = new DevicesExtension();
		TimekeepingExtension timeKeepingExtension = new TimekeepingExtension();
		SchedulingExtension schedulingExtension = new SchedulingExtension();

		timekeepingCalled = false;
		deviceCalled = false;
		
		List<AllExtensionHelper> allExtensionHelperList = new ArrayList<>();
		allExtensionHelperList.clear();
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(accrualExtension,null),allExtensionsFacade.accuralConsumer));
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(devicesExtension,null),(t,u)->deviceCalled = true));
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(null,new PersonalityExtensionException(PersonalityErrorCode.UNKNOWN_ERROR, "Unknown Error.")),allExtensionsFacade.employeeConsumer));
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(timeKeepingExtension,null),(t,u)->timekeepingCalled = true));
		allExtensionHelperList.add(new AllExtensionHelper(new PersonalityResponse(schedulingExtension,null),allExtensionsFacade.schedulingConsumer));

		PersonalityResponse<AllExtension> response = allExtensionsFacade.mergeOneItem(allExtensionHelperList, null);
		assertEquals("Unknown Error.",response.getException().getMessage());
		assertNull(response.getExtension());
		assertTrue(deviceCalled);
		assertFalse(timekeepingCalled);
		

	}
	
	@SuppressWarnings("rawtypes")
	@Test
	public void testMergeOneItemWithMap(){
		
		Map<Long, PersonalityResponse<AccrualExtension>> accrualMap = new LinkedHashMap<Long, PersonalityResponse<AccrualExtension>>();
		AccrualExtension accrualExtension1 = new AccrualExtension();
		accrualExtension1.setPersonId(Long.valueOf(1));
		AccrualExtension accrualExtension2= new AccrualExtension();
		accrualExtension2.setPersonId(Long.valueOf(2));
		accrualMap.put(Long.valueOf(1),new PersonalityResponse<AccrualExtension>(accrualExtension1,null));
		accrualMap.put(Long.valueOf(2),new PersonalityResponse<AccrualExtension>(accrualExtension2,null));
		
		Map<Long, PersonalityResponse<EmployeeExtension>> employeeMap = new LinkedHashMap<Long, PersonalityResponse<EmployeeExtension>>();
		EmployeeExtension employeeExtension1 = new EmployeeExtension();
		employeeExtension1.setPersonId(Long.valueOf(1));
		EmployeeExtension employeeExtension2 = new EmployeeExtension();
		employeeExtension2.setPersonId(Long.valueOf(2));
		employeeMap.put(Long.valueOf(1),new PersonalityResponse<EmployeeExtension>(employeeExtension1,null));
		employeeMap.put(Long.valueOf(2),new PersonalityResponse<EmployeeExtension>(employeeExtension2,null));

		Map<Long, PersonalityResponse<TimekeepingExtension>> timekeepingMap = new LinkedHashMap<Long, PersonalityResponse<TimekeepingExtension>>();
		TimekeepingExtension timekeepingExtension1 = new TimekeepingExtension();
		timekeepingExtension1.setPersonId(Long.valueOf(1));
		TimekeepingExtension timekeepingExtension2 = new TimekeepingExtension();
		timekeepingExtension2.setPersonId(Long.valueOf(2));
		timekeepingMap.put(Long.valueOf(1),new PersonalityResponse<TimekeepingExtension>(timekeepingExtension1,null));
		timekeepingMap.put(Long.valueOf(2),new PersonalityResponse<TimekeepingExtension>(timekeepingExtension2,null));
		
		List<AllExtensionHelper> entries = new ArrayList<AllExtensionHelper>();
		entries.add(new AllExtensionHelper<Long, AccrualExtension>(accrualMap, allExtensionsFacade.accuralConsumer));
		entries.add(new AllExtensionHelper<Long, EmployeeExtension>(employeeMap, allExtensionsFacade.employeeConsumer));
		entries.add(new AllExtensionHelper<Long, TimekeepingExtension>(timekeepingMap, allExtensionsFacade.timekeepingConsumer));
		
		PersonalityResponse<AllExtension> response = allExtensionsFacade.mergeOneItem(entries, Long.valueOf(1));
		assertEquals(Long.valueOf(1),response.getExtension().getAccrualExtension().getPersonId());
		response = allExtensionsFacade.mergeOneItem(entries, Long.valueOf(2));
		assertEquals(Long.valueOf(2),response.getExtension().getAccrualExtension().getPersonId());

	}
	
	@SuppressWarnings("rawtypes")
	@Test
	public void testMergeExtensionsMap(){
		Map<Long, PersonalityResponse<AccrualExtension>> accrualMap = new LinkedHashMap<Long, PersonalityResponse<AccrualExtension>>();
		AccrualExtension accrualExtension1 = new AccrualExtension();
		accrualExtension1.setPersonId(Long.valueOf(1));
		AccrualExtension accrualExtension2= new AccrualExtension();
		accrualExtension2.setPersonId(Long.valueOf(2));
		accrualMap.put(Long.valueOf(1),new PersonalityResponse<AccrualExtension>(accrualExtension1,null));
		accrualMap.put(Long.valueOf(2),new PersonalityResponse<AccrualExtension>(accrualExtension2,null));
		
		Map<Long, PersonalityResponse<EmployeeExtension>> employeeMap = new LinkedHashMap<Long, PersonalityResponse<EmployeeExtension>>();
		EmployeeExtension employeeExtension1 = new EmployeeExtension();
		employeeExtension1.setPersonId(Long.valueOf(1));
		EmployeeExtension employeeExtension2 = new EmployeeExtension();
		employeeExtension2.setPersonId(Long.valueOf(2));
		employeeMap.put(Long.valueOf(1),new PersonalityResponse<EmployeeExtension>(employeeExtension1,null));
		employeeMap.put(Long.valueOf(2),new PersonalityResponse<EmployeeExtension>(employeeExtension2,null));

		Map<Long, PersonalityResponse<TimekeepingExtension>> timekeepingMap = new LinkedHashMap<Long, PersonalityResponse<TimekeepingExtension>>();
		TimekeepingExtension timekeepingExtension1 = new TimekeepingExtension();
		timekeepingExtension1.setPersonId(Long.valueOf(1));
		TimekeepingExtension timekeepingExtension2 = new TimekeepingExtension();
		timekeepingExtension2.setPersonId(Long.valueOf(2));
		timekeepingMap.put(Long.valueOf(1),new PersonalityResponse<TimekeepingExtension>(timekeepingExtension1,null));
		timekeepingMap.put(Long.valueOf(2),new PersonalityResponse<TimekeepingExtension>(timekeepingExtension2,null));
		
		List<AllExtensionHelper> entries = new ArrayList<AllExtensionHelper>();
		entries.add(new AllExtensionHelper<Long, AccrualExtension>(accrualMap, allExtensionsFacade.accuralConsumer));
		entries.add(new AllExtensionHelper<Long, EmployeeExtension>(employeeMap, allExtensionsFacade.employeeConsumer));
		entries.add(new AllExtensionHelper<Long, TimekeepingExtension>(timekeepingMap, allExtensionsFacade.timekeepingConsumer));
		
		Map<Object,PersonalityResponse<AllExtension>> response = allExtensionsFacade.mergeExtensionsMap(entries);
		assertEquals(Long.valueOf(1),response.get(Long.valueOf(1)).getExtension().getAccrualExtension().getPersonId());
		assertEquals(Long.valueOf(1),response.get(Long.valueOf(1)).getExtension().getEmployeeExtension().getPersonId());
		assertEquals(Long.valueOf(1),response.get(Long.valueOf(1)).getExtension().getTimekeepingExtension().getPersonId());
		assertEquals(Long.valueOf(2),response.get(Long.valueOf(2)).getExtension().getAccrualExtension().getPersonId());
		assertEquals(Long.valueOf(2),response.get(Long.valueOf(2)).getExtension().getEmployeeExtension().getPersonId());
		assertEquals(Long.valueOf(2),response.get(Long.valueOf(2)).getExtension().getTimekeepingExtension().getPersonId());

	}


	private boolean myMethodCalled = false;
	@Test
	public void testFindAllExtensions(){
		allExtensionsFacade = new AllExtensionsFacade() {
			@Override
			protected <T> Map<T, PersonalityResponse<AllExtension>> mergeExtensionsMap(List<AllExtensionHelper> entries, Map<T, PersonalityResponse<AllExtension>> outputMap) {
				assertEquals(5,entries.size());
				assertEquals(allExtensionsFacade.accuralConsumer,entries.get(0).getConsumer());
				assertEquals(allExtensionsFacade.deviceConsumer,entries.get(1).getConsumer());
				assertEquals(allExtensionsFacade.employeeConsumer,entries.get(2).getConsumer());
				assertEquals(allExtensionsFacade.schedulingConsumer,entries.get(3).getConsumer());
				assertEquals(allExtensionsFacade.timekeepingConsumer,entries.get(4).getConsumer());
				myMethodCalled = true;
				return getMockAllExtensionMap();
			}

		};
		setExtensionFacadeWithIds();
		allExtensionsFacade.setExtensionFacade(extensionFacade);
		allExtensionsFacade.cacheFacade = mock(PersonalityCacheFacade.class);
		Map<Long, PersonalityResponse<AllExtension>> allExtensionMap = allExtensionsFacade.findAllExtensions(new Long[]{1L,2L},LocalDate.of(2014, 1, 21));
		assertEquals(2,allExtensionMap.size());
		assertTrue(myMethodCalled);
		Map<Object, PersonalityResponse<AllExtension>> mockMap = getMockAllExtensionMap();
		for (Object k : mockMap.keySet()) {
			PersonalityResponse<AllExtension> mockExt = mockMap.get(k);
			PersonalityResponse<AllExtension> actualExt = allExtensionMap.get(k);
			assertEquals(mockExt.getExtension(), actualExt.getExtension());
			
		}
		
		assertEquals(getMockAllExtensionMap().keySet(),allExtensionMap.keySet());
		
		//assertEquals(getMockAllExtensionMap().equals(allExtensionMap)
		//assertEquals(getMockAllExtensionMap().values(),allExtensionMap.values());
	}
	
	@Test
	public void testFindAllExtensionsCall(){
		allExtensionsFacade = new AllExtensionsFacade() {
			@Override
			public Map<Long, PersonalityResponse<AllExtension>> findAllExtensions(
					Long[] personIds, LocalDate snapShotDate) {
				myMethodCalled = true;
				return getMockAllExtensionMap();
			}

		};
		myMethodCalled = false;
		 allExtensionsFacade.findAllExtensions(new Long[]{1L,2L});
		 assertTrue(myMethodCalled);
	}
	
	@Test
	public void testCreateAllCallFromFindExtn(){
		allExtensionsFacade = new AllExtensionsFacade() {
			@Override
			protected PersonalityResponse<AllExtension> createAllExtension(
					Long personId, LocalDate snapShotDate) {
				myMethodCalled = true;
				return new PersonalityResponse<AllExtension>(getAllExtension(),null);
			}
		};
		 myMethodCalled = false;
		 allExtensionsFacade.findAllExtensions(1l,LocalDate.of(2014, 1, 21));
		 assertTrue(myMethodCalled);

	}
	
	@Test
	public void testCreateCallFromFindExtnWithPersonId(){
		allExtensionsFacade = new AllExtensionsFacade() {
			@Override
			protected PersonalityResponse<AllExtension> createAllExtension(
					Long personId, LocalDate snapShotDate) {
				myMethodCalled = true;
				return new PersonalityResponse<AllExtension>(getAllExtension(),null);
			}
		};
		 myMethodCalled = false;
		 allExtensionsFacade.findAllExtensions(1l);
		 assertTrue(myMethodCalled);

	}

	
	
	
	@Test
	public void testCreateAllExtensionCall(){
		allExtensionsFacade = new AllExtensionsFacade() {
			@Override
			protected PersonalityResponse<AllExtension> createAllExtension(
					Long personId, LocalDate snapShotDate) {
				myMethodCalled = true;
				return new PersonalityResponse<AllExtension>(getAllExtension(),null);
			}
		};
		 myMethodCalled = false;
		 allExtensionsFacade.createAllExtension(1l);
		 assertTrue(myMethodCalled);

	}
	
	@Test
	public void testFindAllExtensionsByCriteria(){
		allExtensionsFacade = new AllExtensionsFacade() {
			@Override
			protected <T> Map<T, PersonalityResponse<AllExtension>> mergeExtensionsMap(
					List<AllExtensionHelper> entries, Map<T, PersonalityResponse<AllExtension>> map) {
				assertEquals(5,entries.size());
				assertEquals(allExtensionsFacade.accuralConsumer,entries.get(0).getConsumer());
				assertEquals(allExtensionsFacade.deviceConsumer,entries.get(1).getConsumer());
				assertEquals(allExtensionsFacade.employeeConsumer,entries.get(2).getConsumer());
				assertEquals(allExtensionsFacade.schedulingConsumer,entries.get(3).getConsumer());
				assertEquals(allExtensionsFacade.timekeepingConsumer,entries.get(4).getConsumer());
				myMethodCalled = true;
				return getMockAllExtensionMap();
			}

		};
		
		Criteria criteria = new Criteria();
		criteria.setIds(new Long[]{1l,2l});
		criteria.setOnlyActivePerson(false);
		criteria.setSnapShotDate(LocalDate.of(2014, 1, 21));

		myMethodCalled = false;
		setExtensionFacadeWithCriteria(criteria);
		allExtensionsFacade.setExtensionFacade(extensionFacade);
		allExtensionsFacade.cacheFacade = mock(PersonalityCacheFacade.class);
		Map<Object, PersonalityResponse<AllExtension>> allExtensionMap = allExtensionsFacade.findAllExtensionsByCriteria(criteria);
		assertEquals(2,allExtensionMap.size());
		assertTrue(myMethodCalled);
		Map<Object, PersonalityResponse<AllExtension>> mockMap = getMockAllExtensionMap();
		for (Object k : mockMap.keySet()) {
			PersonalityResponse<AllExtension> mockExt = mockMap.get(k);
			PersonalityResponse<AllExtension> actualExt = allExtensionMap.get(k);
			assertEquals(mockExt.getExtension(), actualExt.getExtension());
			
		}
		assertEquals(getMockAllExtensionMap().keySet(),allExtensionMap.keySet());

		
	}
	
	void setExtensionFacadeWithCriteria(Criteria criteria){
		AccrualExtension accrualExtension1 = new AccrualExtension();
		accrualExtension1.setPersonId(Long.valueOf(1));
		AccrualExtension accrualExtension2= new AccrualExtension();
		accrualExtension2.setPersonId(Long.valueOf(2));
		Map<Object, PersonalityResponse<BaseExtension>> accrualMap = new LinkedHashMap<Object, PersonalityResponse<BaseExtension>>();
		accrualMap.put(Long.valueOf(1),new PersonalityResponse<BaseExtension>(accrualExtension1,null));
		accrualMap.put(Long.valueOf(2),new PersonalityResponse<BaseExtension>(accrualExtension2,null));
		
		TimekeepingExtension timekeepingExtension1 = new TimekeepingExtension();
		timekeepingExtension1.setPersonId(Long.valueOf(1));
		TimekeepingExtension timekeepingExtension2 = new TimekeepingExtension();
		timekeepingExtension2.setPersonId(Long.valueOf(2));
		Map<Object, PersonalityResponse<BaseExtension>> timekeepingMap = new LinkedHashMap<Object, PersonalityResponse<BaseExtension>>();
		accrualMap.put(Long.valueOf(1),new PersonalityResponse<BaseExtension>(timekeepingExtension1,null));
		accrualMap.put(Long.valueOf(2),new PersonalityResponse<BaseExtension>(timekeepingExtension2,null));
		
		Map<Object, PersonalityResponse<BaseExtension>> employeeMap = new LinkedHashMap<Object, PersonalityResponse<BaseExtension>>();
		EmployeeExtension employeeExtension1 = new EmployeeExtension();
		employeeExtension1.setPersonId(Long.valueOf(1));
		EmployeeExtension employeeExtension2 = new EmployeeExtension();
		employeeExtension2.setPersonId(Long.valueOf(2));
		employeeMap.put(Long.valueOf(1),new PersonalityResponse<BaseExtension>(employeeExtension1,null));
		employeeMap.put(Long.valueOf(2),new PersonalityResponse<BaseExtension>(employeeExtension2,null));

		Map<Object, PersonalityResponse<BaseExtension>> devicesMap = new LinkedHashMap<Object, PersonalityResponse<BaseExtension>>();
		DevicesExtension devicesExtension1 = new DevicesExtension();
		devicesExtension1.setPersonId(Long.valueOf(1));
		DevicesExtension devicesExtension2 = new DevicesExtension();
		devicesExtension2.setPersonId(Long.valueOf(2));
		devicesMap.put(Long.valueOf(1),new PersonalityResponse<BaseExtension>(devicesExtension1,null));
		devicesMap.put(Long.valueOf(2),new PersonalityResponse<BaseExtension>(devicesExtension2,null));

		Map<Object, PersonalityResponse<BaseExtension>> schedulingMap = new LinkedHashMap<Object, PersonalityResponse<BaseExtension>>();
		SchedulingExtension schedulingExtension1 = new SchedulingExtension();
		schedulingExtension1.setPersonId(Long.valueOf(1));
		SchedulingExtension schedulingExtension2 = new SchedulingExtension();
		schedulingExtension2.setPersonId(Long.valueOf(2));
		schedulingMap.put(Long.valueOf(1),new PersonalityResponse<BaseExtension>(schedulingExtension1,null));
		schedulingMap.put(Long.valueOf(2),new PersonalityResponse<BaseExtension>(schedulingExtension2,null));
		
		extensionFacade = mock(ExtensionFacade.class);
		when(extensionFacade.handleCriteria(criteria, ExtensionAdapterEnum.ACCRUAL,false)).thenReturn(accrualMap);
		when(extensionFacade.handleCriteria(criteria, ExtensionAdapterEnum.TIMEKEEPING,false)).thenReturn(timekeepingMap);
		when(extensionFacade.handleCriteria(criteria, ExtensionAdapterEnum.SCHEDULING,false)).thenReturn(schedulingMap);
		when(extensionFacade.handleCriteria(criteria, ExtensionAdapterEnum.DEVICES,false)).thenReturn(devicesMap);
		when(extensionFacade.handleCriteria(criteria,ExtensionAdapterEnum.EMPLOYEE,false)).thenReturn(employeeMap);		

	}

	AllExtension getAllExtension() {
		AccrualExtension accrualExtension1 = new AccrualExtension();
		accrualExtension1.setPersonId(Long.valueOf(1));
		
		TimekeepingExtension timekeepingExtension1 = new TimekeepingExtension();
		timekeepingExtension1.setPersonId(Long.valueOf(1));
		
		EmployeeExtension employeeExtension1 = new EmployeeExtension();
		employeeExtension1.setPersonId(Long.valueOf(1));

		DevicesExtension devicesExtension1 = new DevicesExtension();
		devicesExtension1.setPersonId(Long.valueOf(1));
		
		SchedulingExtension schedulingExtension1 = new SchedulingExtension();
		schedulingExtension1.setPersonId(Long.valueOf(1));

		AllExtension allExtension1 = new AllExtension();
		allExtension1.setAccrualExtension(accrualExtension1);
		allExtension1.setTimekeepingExtension(timekeepingExtension1);
		allExtension1.setDeviceExtension(devicesExtension1);
		allExtension1.setEmployeeExtension(employeeExtension1);
		allExtension1.setSchedulingExtension(schedulingExtension1);
		return allExtension1;
	}
	
	void setExtensionFacadeWithIds(){
		AccrualExtension accrualExtension1 = new AccrualExtension();
		accrualExtension1.setPersonId(Long.valueOf(1));
		AccrualExtension accrualExtension2= new AccrualExtension();
		accrualExtension2.setPersonId(Long.valueOf(2));
		Map<Long, PersonalityResponse<BaseExtension>> accrualMap = new LinkedHashMap<Long, PersonalityResponse<BaseExtension>>();
		accrualMap.put(Long.valueOf(1),new PersonalityResponse<BaseExtension>(accrualExtension1,null));
		accrualMap.put(Long.valueOf(2),new PersonalityResponse<BaseExtension>(accrualExtension2,null));
		
		TimekeepingExtension timekeepingExtension1 = new TimekeepingExtension();
		timekeepingExtension1.setPersonId(Long.valueOf(1));
		TimekeepingExtension timekeepingExtension2 = new TimekeepingExtension();
		timekeepingExtension2.setPersonId(Long.valueOf(2));
		Map<Long, PersonalityResponse<BaseExtension>> timekeepingMap = new LinkedHashMap<Long, PersonalityResponse<BaseExtension>>();
		timekeepingMap.put(Long.valueOf(1),new PersonalityResponse<BaseExtension>(timekeepingExtension1,null));
		timekeepingMap.put(Long.valueOf(2),new PersonalityResponse<BaseExtension>(timekeepingExtension2,null));
		
		Map<Long, PersonalityResponse<BaseExtension>> employeeMap = new LinkedHashMap<Long, PersonalityResponse<BaseExtension>>();
		EmployeeExtension employeeExtension1 = new EmployeeExtension();
		employeeExtension1.setPersonId(Long.valueOf(1));
		EmployeeExtension employeeExtension2 = new EmployeeExtension();
		employeeExtension2.setPersonId(Long.valueOf(2));
		employeeMap.put(Long.valueOf(1),new PersonalityResponse<BaseExtension>(employeeExtension1,null));
		employeeMap.put(Long.valueOf(2),new PersonalityResponse<BaseExtension>(employeeExtension2,null));

		Map<Long, PersonalityResponse<BaseExtension>> devicesMap = new LinkedHashMap<Long, PersonalityResponse<BaseExtension>>();
		DevicesExtension devicesExtension1 = new DevicesExtension();
		devicesExtension1.setPersonId(Long.valueOf(1));
		DevicesExtension devicesExtension2 = new DevicesExtension();
		devicesExtension2.setPersonId(Long.valueOf(2));
		devicesMap.put(Long.valueOf(1),new PersonalityResponse<BaseExtension>(devicesExtension1,null));
		devicesMap.put(Long.valueOf(2),new PersonalityResponse<BaseExtension>(devicesExtension2,null));

		Map<Long, PersonalityResponse<BaseExtension>> schedulingMap = new LinkedHashMap<Long, PersonalityResponse<BaseExtension>>();
		SchedulingExtension schedulingExtension1 = new SchedulingExtension();
		schedulingExtension1.setPersonId(Long.valueOf(1));
		SchedulingExtension schedulingExtension2 = new SchedulingExtension();
		schedulingExtension2.setPersonId(Long.valueOf(2));
		schedulingMap.put(Long.valueOf(1),new PersonalityResponse<BaseExtension>(schedulingExtension1,null));
		schedulingMap.put(Long.valueOf(2),new PersonalityResponse<BaseExtension>(schedulingExtension2,null));
		
		
		extensionFacade = mock(ExtensionFacade.class);
		when(extensionFacade.handlePersonIds(new Long[]{1L,2L}, LocalDate.of(2014, 1, 21), ExtensionAdapterEnum.ACCRUAL,false)).thenReturn(accrualMap);
		when(extensionFacade.handlePersonIds(new Long[]{1L,2L}, LocalDate.of(2014, 1, 21), ExtensionAdapterEnum.TIMEKEEPING,false)).thenReturn(timekeepingMap);
		when(extensionFacade.handlePersonIds(new Long[]{1L,2L}, LocalDate.of(2014, 1, 21), ExtensionAdapterEnum.SCHEDULING,false)).thenReturn(schedulingMap);
		when(extensionFacade.handlePersonIds(new Long[]{1L,2L}, LocalDate.of(2014, 1, 21), ExtensionAdapterEnum.DEVICES,false)).thenReturn(devicesMap);
		when(extensionFacade.handlePersonIds(new Long[]{1L,2L}, LocalDate.of(2014, 1, 21), ExtensionAdapterEnum.EMPLOYEE,false)).thenReturn(employeeMap);
	}
	
	void setExtensionFacadeWithId(){
		AccrualExtension accrualExtension1 = new AccrualExtension();
		accrualExtension1.setPersonId(Long.valueOf(1));
		PersonalityResponse<BaseExtension> accrualPersonalityResponse = new PersonalityResponse<BaseExtension>(accrualExtension1,null);
		
		TimekeepingExtension timekeepingExtension1 = new TimekeepingExtension();
		timekeepingExtension1.setPersonId(Long.valueOf(1));
		PersonalityResponse<BaseExtension> timekeepingPersonalityResponse = new PersonalityResponse<BaseExtension>(timekeepingExtension1,null);
		
		EmployeeExtension employeeExtension1 = new EmployeeExtension();
		employeeExtension1.setPersonId(Long.valueOf(1));
		PersonalityResponse<BaseExtension> employeePersonalityResponse = new PersonalityResponse<BaseExtension>(employeeExtension1,null);

		DevicesExtension devicesExtension1 = new DevicesExtension();
		devicesExtension1.setPersonId(Long.valueOf(1));
		PersonalityResponse<BaseExtension> devicesPersonalityResponse = new PersonalityResponse<BaseExtension>(devicesExtension1,null);

		SchedulingExtension schedulingExtension1 = new SchedulingExtension();
		schedulingExtension1.setPersonId(Long.valueOf(1));
		PersonalityResponse<BaseExtension> schedulingPersonalityResponse = new PersonalityResponse<BaseExtension>(schedulingExtension1,null);
		
		
		extensionFacade = mock(ExtensionFacade.class);
		when(extensionFacade.handlePersonId(1L, LocalDate.of(2014, 1, 21), ExtensionAdapterEnum.ACCRUAL,false,false)).thenReturn(accrualPersonalityResponse);
		when(extensionFacade.handlePersonId(1L, LocalDate.of(2014, 1, 21), ExtensionAdapterEnum.TIMEKEEPING,false,false)).thenReturn(timekeepingPersonalityResponse);
		when(extensionFacade.handlePersonId(1L, LocalDate.of(2014, 1, 21), ExtensionAdapterEnum.SCHEDULING,false,false)).thenReturn(schedulingPersonalityResponse);
		when(extensionFacade.handlePersonId(1L, LocalDate.of(2014, 1, 21), ExtensionAdapterEnum.DEVICES,false,false)).thenReturn(devicesPersonalityResponse);
		when(extensionFacade.handlePersonId(1L, LocalDate.of(2014, 1, 21), ExtensionAdapterEnum.EMPLOYEE,false,false)).thenReturn(employeePersonalityResponse);
	}
	
	
	<T> Map<T, PersonalityResponse<AllExtension>> getMockAllExtensionMap() {
		AccrualExtension accrualExtension1 = new AccrualExtension();
		accrualExtension1.setPersonId(Long.valueOf(1));
		AccrualExtension accrualExtension2= new AccrualExtension();
		accrualExtension2.setPersonId(Long.valueOf(2));
		
		TimekeepingExtension timekeepingExtension1 = new TimekeepingExtension();
		timekeepingExtension1.setPersonId(Long.valueOf(1));
		TimekeepingExtension timekeepingExtension2 = new TimekeepingExtension();
		timekeepingExtension2.setPersonId(Long.valueOf(2));
		
		EmployeeExtension employeeExtension1 = new EmployeeExtension();
		employeeExtension1.setPersonId(Long.valueOf(1));
		EmployeeExtension employeeExtension2 = new EmployeeExtension();
		employeeExtension2.setPersonId(Long.valueOf(2));

		DevicesExtension devicesExtension1 = new DevicesExtension();
		devicesExtension1.setPersonId(Long.valueOf(1));
		DevicesExtension devicesExtension2 = new DevicesExtension();
		devicesExtension2.setPersonId(Long.valueOf(2));
		
		SchedulingExtension schedulingExtension1 = new SchedulingExtension();
		schedulingExtension1.setPersonId(Long.valueOf(1));
		SchedulingExtension schedulingExtension2 = new SchedulingExtension();
		schedulingExtension2.setPersonId(Long.valueOf(2));

		AllExtension allExtension1 = new AllExtension();
		allExtension1.setAccrualExtension(accrualExtension1);
		allExtension1.setTimekeepingExtension(timekeepingExtension1);
		allExtension1.setDeviceExtension(devicesExtension1);
		allExtension1.setEmployeeExtension(employeeExtension1);
		allExtension1.setSchedulingExtension(schedulingExtension1);
		
		AllExtension allExtension2 = new AllExtension();
		allExtension2.setAccrualExtension(accrualExtension2);
		allExtension2.setTimekeepingExtension(timekeepingExtension2);
		allExtension2.setDeviceExtension(devicesExtension2);
		allExtension2.setEmployeeExtension(employeeExtension2);
		allExtension2.setSchedulingExtension(schedulingExtension2);

		Map<Long, PersonalityResponse<AllExtension>> allExtensionMap = new LinkedHashMap<>();
		allExtensionMap.put(Long.valueOf(1),new PersonalityResponse(allExtension1,null));
		allExtensionMap.put(Long.valueOf(2),new PersonalityResponse(allExtension2,null));
		
		return (Map<T, PersonalityResponse<AllExtension>>) allExtensionMap;
	}
	
	@Test
	public void testGetDataFromNormalFlow(){
		
		Criteria criteriaMock = mock(Criteria.class);
		Long o1 =new Long(1);
		Long o2 =new Long(2);
		Object[] objs = {o1, o2};
		when(criteriaMock.getIds()).thenReturn(objs);
		when(criteriaMock.getSnapShotDate()).thenReturn(LocalDate.now());
		Long[] ids = {1L, 2L};
		ExtensionFacade extensionFacadeMock = mock(ExtensionFacade.class);
		when(extensionFacadeMock.getIdsArray(criteriaMock)).thenReturn(ids);
		
		
		allExtensionsFacade = new AllExtensionsFacade(){
			
			@Override
			public Map<Long, PersonalityResponse<AllExtension>> findAllExtensions(
					Long[] personIds, LocalDate snapShotDate) {
				
				AllExtension allExtension = new AllExtension();
				allExtension.setPersonId(1L);
				allExtension.setActive(true);
				allExtension.setPersonNumber("per1");
				allExtension.setSnapShotDate(LocalDate.now());

				PersonalityResponse<AllExtension> response = new PersonalityResponse<AllExtension>(allExtension, null);
				Long[] ids = {1L, 2L};
				LocalDate date;
				ids = personIds;
				date = snapShotDate;
				Map<Long, PersonalityResponse<AllExtension>> map = new HashMap<>();
				map.put(1L, response);
				return map;
			}
		};
		
		allExtensionsFacade.setExtensionFacade(extensionFacadeMock);
		when(criteriaMock.isOnlyActivePerson()).thenReturn(true);
		
		Map<Object, PersonalityResponse<AllExtension>> output = allExtensionsFacade.getDataFromNormalFlow(criteriaMock);
		assertNotNull(output);
		
	}

*/
}
