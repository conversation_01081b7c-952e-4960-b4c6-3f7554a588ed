package com.kronos.people.personality.model.extension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.spy;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;


import com.kronos.people.personality.model.extension.entry.AccrualProfilesEntry;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.FullTimeEquivalencyEntry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class AccuralExtensionMicroTest {

	private AccrualExtension accrualExtension;

	@BeforeEach
	public void setUp() throws Exception {
		accrualExtension = spy(AccrualExtension.class);
	}

	@Test
	public void accuralProfilesTest() {

		AccrualProfilesEntry accrualProfileEntry1 = new AccrualProfilesEntry();
//		accrualProfileEntry1.setAccuralProfileAssignmentId(Long.valueOf(12));
		accrualProfileEntry1.setAccuralProfileAssignmentId(12L);
		accrualProfileEntry1.setEffectiveDate(LocalDate.of(2015, 8, 23));
		accrualProfileEntry1.setExpirationDate(LocalDate.of(2015, 8, 24));

		AccrualProfilesEntry accrualProfileEntry2 = new AccrualProfilesEntry();
//		accrualProfileEntry2.setAccuralProfileAssignmentId(Long.valueOf(13));
		accrualProfileEntry2.setAccuralProfileAssignmentId(13L);
		accrualProfileEntry2.setEffectiveDate(LocalDate.of(2015, 8, 23));
		accrualProfileEntry2.setExpirationDate(LocalDate.of(2015, 8, 25));

		AccrualProfilesEntry accrualProfileEntry3 = new AccrualProfilesEntry();
//		accrualProfileEntry3.setAccuralProfileAssignmentId(Long.valueOf(14));
		accrualProfileEntry3.setAccuralProfileAssignmentId(14L);
		accrualProfileEntry3.setEffectiveDate(LocalDate.of(2015, 8, 23));
		accrualProfileEntry3.setExpirationDate(LocalDate.of(2015, 8, 26));

//		Collection<AccrualProfilesEntry> accrualProfilesEntriesCollection = new ArrayList<AccrualProfilesEntry>();
		Collection<AccrualProfilesEntry> accrualProfilesEntriesCollection = new ArrayList<>();
		accrualProfilesEntriesCollection.add(accrualProfileEntry1);
		accrualProfilesEntriesCollection.add(accrualProfileEntry2);
		accrualProfilesEntriesCollection.add(accrualProfileEntry3);

//		EffectiveDatedCollection<AccrualProfilesEntry> effectiveDatedCollection = new EffectiveDatedCollection<AccrualProfilesEntry>();
		EffectiveDatedCollection<AccrualProfilesEntry> effectiveDatedCollection = new EffectiveDatedCollection<>();
		effectiveDatedCollection.setEffectiveDatedEntries(accrualProfilesEntriesCollection);

		accrualExtension.setAccuralProfiles(effectiveDatedCollection);

//		Collection<AccrualProfilesEntry> testCollection = new ArrayList<AccrualProfilesEntry>();
		Collection<AccrualProfilesEntry> testCollection = new ArrayList<>();
		testCollection.add(accrualProfileEntry1);
		testCollection.add(accrualProfileEntry2);
		testCollection.add(accrualProfileEntry3);

		assertEquals(testCollection, accrualExtension.getAccuralProfiles());

		assertTrue(accrualExtension.getAccuralProfiles(LocalDate.of(1997, 2, 21)).isEmpty());

		assertEquals(testCollection, accrualExtension.getAccuralProfiles(LocalDate.of(2015, 8, 23)));
		assertTrue(accrualExtension.getAccuralProfiles(LocalDate.of(1997, 2, 21)).isEmpty());
		assertEquals(2, accrualExtension.getAccuralProfiles(LocalDate.of(2015, 8, 24)).size());

		accrualExtension.setAccuralProfiles(null);
//		assertEquals(null, accrualExtension.getAccuralProfiles());
        assertNull(accrualExtension.getAccuralProfiles());
//		assertEquals(null, accrualExtension.getAccuralProfiles(LocalDate.of(2011, 2, 21)));
        assertNull(accrualExtension.getAccuralProfiles(LocalDate.of(2011, 2, 21)));
	}

	@Test
	public void fullTimeEquivalencyTest() {
		FullTimeEquivalencyEntry fullTimeEquivalencyEntry1 = new FullTimeEquivalencyEntry();
		fullTimeEquivalencyEntry1.setEffectiveDate(LocalDate.of(2015, 8, 23));
		fullTimeEquivalencyEntry1.setExpirationDate(LocalDate.of(2015, 9, 22));
//		fullTimeEquivalencyEntry1.setFullTimeEquivalencyId(Long.valueOf(12));
//		fullTimeEquivalencyEntry1.setEmployeeStandardHoursQuantity(Double.valueOf(12.1));
//		fullTimeEquivalencyEntry1.setFullTimeStandardHoursQuantity(Double.valueOf(12.2));
//		fullTimeEquivalencyEntry1.setFullTimeEquivalencyPercent(Double.valueOf(12.3));
		fullTimeEquivalencyEntry1.setFullTimeEquivalencyId(12L);
		fullTimeEquivalencyEntry1.setEmployeeStandardHoursQuantity(12.1);
		fullTimeEquivalencyEntry1.setFullTimeStandardHoursQuantity(12.2);
		fullTimeEquivalencyEntry1.setFullTimeEquivalencyPercent(12.3);

		FullTimeEquivalencyEntry fullTimeEquivalencyEntry2 = new FullTimeEquivalencyEntry();
		fullTimeEquivalencyEntry2.setEffectiveDate(LocalDate.of(2015, 8, 23));
		fullTimeEquivalencyEntry2.setExpirationDate(LocalDate.of(2015, 9, 25));
//		fullTimeEquivalencyEntry2.setFullTimeEquivalencyId(Long.valueOf(13));
//		fullTimeEquivalencyEntry2.setEmployeeStandardHoursQuantity(Double.valueOf(12.2));
//		fullTimeEquivalencyEntry2.setFullTimeStandardHoursQuantity(Double.valueOf(12.3));
//		fullTimeEquivalencyEntry2.setFullTimeEquivalencyPercent(Double.valueOf(12.4));
		fullTimeEquivalencyEntry2.setFullTimeEquivalencyId(13L);
		fullTimeEquivalencyEntry2.setEmployeeStandardHoursQuantity(12.2);
		fullTimeEquivalencyEntry2.setFullTimeStandardHoursQuantity(12.3);
		fullTimeEquivalencyEntry2.setFullTimeEquivalencyPercent(12.4);

		Collection<FullTimeEquivalencyEntry> fullTimeEquivalencyEntriesCollection = new ArrayList<>();
		fullTimeEquivalencyEntriesCollection.add(fullTimeEquivalencyEntry1);
		fullTimeEquivalencyEntriesCollection.add(fullTimeEquivalencyEntry2);

		EffectiveDatedCollection<FullTimeEquivalencyEntry> effectiveDatedCollection = new EffectiveDatedCollection<>();
		effectiveDatedCollection.setEffectiveDatedEntries(fullTimeEquivalencyEntriesCollection);

		accrualExtension.setFullTimeEquivalency(effectiveDatedCollection);

		Collection<FullTimeEquivalencyEntry> testCollection = new ArrayList<>();
		testCollection.add(fullTimeEquivalencyEntry1);
		testCollection.add(fullTimeEquivalencyEntry2);

		assertEquals(testCollection, accrualExtension.getFullTimeEquivalency());

		assertEquals(testCollection, accrualExtension.getFullTimeEquivalency(LocalDate.of(2015, 8, 23)));
		assertTrue(accrualExtension.getFullTimeEquivalency(LocalDate.of(1997, 2, 21)).isEmpty());
		assertEquals(1, accrualExtension.getFullTimeEquivalency(LocalDate.of(2015, 9, 24)).size());
		assertEquals(2, accrualExtension.getFullTimeEquivalency(LocalDate.of(2015, 9, 21)).size());

		accrualExtension.setFullTimeEquivalency(null);
//		assertEquals(null, accrualExtension.getFullTimeEquivalency());
//		assertEquals(null, accrualExtension.getFullTimeEquivalency(LocalDate.of(2011, 2, 21)));
        assertNull(accrualExtension.getFullTimeEquivalency());
        assertNull(accrualExtension.getFullTimeEquivalency(LocalDate.of(2011, 2, 21)));

	}
}
