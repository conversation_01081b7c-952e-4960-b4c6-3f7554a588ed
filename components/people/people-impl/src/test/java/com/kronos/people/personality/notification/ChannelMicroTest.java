package com.kronos.people.personality.notification;

import com.kronos.people.personality.Operation;
import com.kronos.people.personality.notification.entry.EventType;
import com.kronos.people.personality.notification.entry.PersonalityEvent;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

//@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class ChannelMicroTest {
	
	private ExecutorService executor = null;
	@Spy
	Channel<PersonalityEvent> channelSpy;
	
	Channel<String> channel;
	
	@BeforeEach
	public void setup(){
		executor = Executors.newCachedThreadPool();
//		channel = new Channel<String>(executor, "MyChannel");
		channel = new Channel<>(executor, "MyChannel");
	}
	
	@AfterEach
	public void tearDown(){
		channelSpy = null;
		channel = null;
		executor.shutdown();
	}
	
	/**
	 * verified that add to queue has been called 1 time
	 */
	@SuppressWarnings("unchecked")
	@Test
	public void testAddToQueueVerificationWhenConsumerListIsEmpty(){
		
		PersonalityEvent personalityEventMock = mock(PersonalityEvent.class);
		//channelSpy =spy(Channel.class);
		@SuppressWarnings("rawtypes")
		ConcurrentLinkedQueue  concurrentLinkedQueueMock = mock(ConcurrentLinkedQueue.class);
		
		channelSpy.setQueue(concurrentLinkedQueueMock);
		channelSpy.add(personalityEventMock);
		
		verify(channelSpy, times(1)).add(personalityEventMock);
		
	}
	
	/**
	 * Added items to queue when consumer is registered
	 */
	@SuppressWarnings("unchecked")
	@Test
	public void testAddToQueueForConsumerList() {
//		Channel<PersonalityEvent> channel = new Channel<PersonalityEvent>(executor, "consumer1");
		Channel<PersonalityEvent> channel = new Channel<>(executor, "consumer1");
		PersonalityEvent personalityEventMock = mock(PersonalityEvent.class);
		personalityEventMock.setTenantId("1");
		personalityEventMock.setPersonality(mock(Personality.class));
		personalityEventMock.setEventType(EventType.INSERT);

		@SuppressWarnings("rawtypes")
		ConcurrentLinkedQueue concurrentLinkedQueueMock = mock(ConcurrentLinkedQueue.class);
		concurrentLinkedQueueMock.add(new Object());
		channel.setQueue(concurrentLinkedQueueMock);
		Queue<String> consumersListMock = mock(ConcurrentLinkedQueue.class);
		String strMock = "mockStr";
		consumersListMock.add(strMock);
		channel.setConsumersList(consumersListMock);
		channel.add(personalityEventMock);

		assertEquals(consumersListMock.size(), channel.getConsumersList().size());

	}
	
	/**
	 * Verified that list of items are added to queue.
	 */
	@SuppressWarnings("unchecked")
	@Test
	public void testAddListOfItemsToQueueWhenConsumerListIsEmpty(){
		//channelSpy =spy(Channel.class);
		ArrayList<PersonalityEvent> lstMock = mock(ArrayList.class);
        PersonalityEvent personalityEventMock = mock(PersonalityEvent.class);
        @SuppressWarnings("rawtypes")
		ConcurrentLinkedQueue  concurrentLinkedQueueMock = mock(ConcurrentLinkedQueue.class);
		channelSpy.setQueue(concurrentLinkedQueueMock);
        lstMock.add(personalityEventMock);
		channelSpy.add(lstMock);
		
		verify(channelSpy, times(1)).add(lstMock);
	}
	
	/**
	 * Added list of items to queue when consumer is registered.
	 */
	@SuppressWarnings("unchecked")
	@Test
	public void testAddListOfItemsToQueueForConsumerList() {
		ArrayList<PersonalityEvent> lstMock = mock(ArrayList.class);

//		Channel<PersonalityEvent> channel = new Channel<PersonalityEvent>(executor, "consumer1");
		Channel<PersonalityEvent> channel = new Channel<>(executor, "consumer1");
		PersonalityEvent personalityEventMock = mock(PersonalityEvent.class);
		personalityEventMock.setEventType(EventType.UPDATE);
		personalityEventMock.setTenantId("1");
		Personality personalityMock = mock(Personality.class);
		personalityEventMock.setPersonality(personalityMock);
		lstMock.add(personalityEventMock);

		@SuppressWarnings("rawtypes")
		ConcurrentLinkedQueue concurrentLinkedQueueMock = mock(ConcurrentLinkedQueue.class);
		concurrentLinkedQueueMock.add(new Object());
		channel.setQueue(concurrentLinkedQueueMock);
		Queue<String> consumersListMock = mock(ConcurrentLinkedQueue.class);
		String strMock = "mockString";
		consumersListMock.add(strMock);
		channel.setConsumersList(consumersListMock);
		channel.add(lstMock);

		assertEquals(consumersListMock.size(), channel.getConsumersList().size());
	}
	
	
	@SuppressWarnings("unchecked")
	@Test
	public void testRegister(){
		
		@SuppressWarnings("rawtypes")
		class MockChannel extends Channel {
			boolean called = false;
			@SuppressWarnings("unused")
			Runnable runnable;
			String consumerName;
			@Override
			public void putInExecutor(Operation runnable, String consumerName) {
				this.consumerName = consumerName;
				this.called = true;
			}
		}
		
		MockChannel channel = new MockChannel();
		Consumer<String> consumer = con -> con.length();
		channel.register(consumer, "consumer1");
		
		assertTrue(channel.called);
		assertEquals("consumer1", channel.consumerName);
		
		
	}
	
	@SuppressWarnings({ "static-access" })
	@Test
	public void testRegisterBatchConsumer(){
//		Channel<Integer> channel = new Channel<Integer>(executor, "consumer1");
		Channel<Integer> channel = new Channel<>(executor, "consumer1");

//		Integer i1 = new Integer(1);
//		Integer i2 = new Integer(2);
		Integer i1 = 1;
		Integer i2 = 2;
		
		Queue<String> clq = new ConcurrentLinkedQueue<>();
		
		channel.setConsumersList(clq);
		
//		List<Integer> ints = new ArrayList<Integer>();
		List<Integer> ints = new ArrayList<>();
		ints.add(i1);
		ints.add(i2);
		
//		Consumer<List<Integer>> consumer = intss -> intss.forEach(i -> i.intValue());
//		Consumer<List<Integer>> consumer = intss -> intss.forEach(i -> i);
		Consumer<List<Integer>> consumer = intss -> intss.forEach(Integer::intValue);

		channel.registerBatchConsumer(consumer, "consumer1");
		
		try {
			Thread.currentThread().sleep(100L);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		
		assertEquals(1, channel.getConsumersList().size());
	}
	
	@Test
	public void testGetFromQueueAndBatchProcess(){
		Channel<Integer> channel = new Channel<>(executor, 2, "consumer1");
//		Integer i1 = new Integer(1);
//		Integer i2 = new Integer(2);
		Integer i1 = 1;
		Integer i2 = 2;
		
		Queue<Integer> clq = new ConcurrentLinkedQueue<>();
		clq.add(i1);
		clq.add(i2);
		
		channel.setQueue(clq);
		
//		Consumer<List<Integer>> consumer = intss -> intss.forEach(i -> i.intValue());
//		Consumer<List<Integer>> consumer = intss -> intss.forEach(i -> i);
		Consumer<List<Integer>> consumer = intss -> intss.forEach(Integer::intValue);
		channel.getFromQueueAndBatchProcess(consumer, "consumer1");
//		assertEquals(true, channel.getQueue().isEmpty());
        assertTrue(channel.getQueue().isEmpty());
	}
	
	@Test
	public void testGetFromQueueAndProcess(){
//		Channel<Integer> channel = new Channel<Integer>();
		Channel<Integer> channel = new Channel<>();
//		Consumer<Integer> consumer = in -> in.intValue();
//		Consumer<Integer> consumer = in -> in;
		Consumer<Integer> consumer = Integer::intValue;
//		Integer i1 = new Integer(1);
		Integer i1 = 1;
		
		Queue<Integer> clq = new ConcurrentLinkedQueue<>();
		clq.add(i1);
		
		channel.setQueue(clq);
		channel.getFromQueueAndProcess(consumer, "consumer1" );

//		assertEquals(true, channel.getQueue().isEmpty());
        assertTrue(channel.getQueue().isEmpty());
	}
	
	@Test 
	public void testUnRegisterConsumer(){
		Queue<String> conQueue= new ConcurrentLinkedQueue<>();
		conQueue.add("consumer1");
		channel.setConsumersList(conQueue);
		
		channel.unregister("consumer1");
		
//		assertEquals(true, channel.getConsumersList().isEmpty());
        assertTrue(channel.getConsumersList().isEmpty());
	}
	
	@Test
	public void testGetExceptionMessage(){
		String output = channel.getExceptionMessage("consumer1", "item1");
		assertNotNull(output);
	}
	
	@Test
	public void testGetExceptionMessageForListItems(){
		List<String> items = new ArrayList<>();
		items.add("item1");
		items.add("item2");
		String output = channel.getExceptionMessage("consumer1", items);
		assertEquals("Exception occured in channel MyChannel for consumer : consumer1 while consuming for items :item1,item2", output);
	}
	
//	@Test(expected = Exception.class)
	@Test
	public void testProcessAndLogTimeException() {
		Consumer<PersonalityEvent> consumerMock = mock(Consumer.class);
		String strMock = "mockString";

		doThrow(new RuntimeException()).when(channelSpy).getFromQueueAndProcess(consumerMock, strMock);
		assertThrows(RuntimeException.class, () -> channelSpy.getFromQueueAndProcess(consumerMock, strMock));
	}
	
	class MockConsumer<T> implements Consumer<T> {
		public int count =0;
		@Override
		public void accept(T t) {
			count++;
		}
	}
	
	class MockBatchConsumer<T> implements Consumer<List<T>> {
		public int count =0;
		
		@Override
		public void accept(List<T> t) {
			count = t.size();
		}
	}
	
	@Test
	public void testNullHandlingForBatchConsumer() {
		MockBatchConsumer consumer = new MockBatchConsumer();
		Channel<Integer> channel = new Channel<>();
		try {
			Field f = channel.getClass().getDeclaredField("queue");
			f.setAccessible(true);
			f.set(channel, new LinkedList<>());
		} catch (Exception e) {
			e.printStackTrace();
		}
		
//		channel.add(Integer.valueOf(2));
		channel.add(2);
		Integer nullInteger = null;
		channel.add(nullInteger);
		channel.add(1);
		channel.getFromQueueAndBatchProcess(consumer, "my batchConsumer");
		assertEquals(2, consumer.count);
	}
	
	@Test
	public void testNullHandlingForConsumer() {
		MockConsumer consumer = new MockConsumer();
		Channel<Integer> channel = new Channel<>();
		try {
			Field f = channel.getClass().getDeclaredField("queue");
			f.setAccessible(true);
			f.set(channel, new LinkedList());
		} catch (Exception e) {
			e.printStackTrace();
		}
	//	ExecutorService newCachedThreadPool = Executors.newCachedThreadPool();
		
		//channel.setExecutorService(newCachedThreadPool);
//		channel.add(Integer.valueOf(2));
		channel.add(2);
		Integer nullInteger = null;
		channel.add(nullInteger);
		channel.add(1);
		//channel.register(consumer, "myTestChannel");
		channel.getFromQueueAndProcess(consumer, "myTestChannel");
		assertEquals(2, consumer.count);
		//newCachedThreadPool.shutdown();
	}



	@Test
	public void testAddPersonalityEventInQueue() {
		Channel<PersonalityEvent> channel = new Channel<>(executor, "MyChannel");
		PersonalityEvent event = mock(PersonalityEvent.class);
		Personality personality = mock(Personality.class);

		Mockito.when(event.getPersonality()).thenReturn(personality);
		Mockito.when(event.getTenantId()).thenReturn("test_nonprd_01");
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));

		channel.addPersonalityEventInQueue(event);

		assertEquals(1, channel.getQueue().size());
		assertTrue(channel.getEventMap().containsKey("test_nonprd_01"+1));
	}

	@Test
	public void testAddDuplicatePersonalityEventInQueue() {
		Channel<PersonalityEvent> channel = new Channel<>(executor, "MyChannel");
		PersonalityEvent event = mock(PersonalityEvent.class);
		Personality personality = mock(Personality.class);

		Mockito.when(event.getPersonality()).thenReturn(personality);
		Mockito.when(event.getTenantId()).thenReturn("test_nonprd_01");
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));

		channel.addPersonalityEventInQueue(event);
		channel.addPersonalityEventInQueue(event); // Adding the same event again

		assertEquals(1, channel.getQueue().size()); // Queue size should still be 1
		assertTrue(channel.getEventMap().containsKey("test_nonprd_01"+1));
	}

	@Test
	public void testAddDuplicatePersonalityEventInQueueWithForcedRefresh() {
		Channel<PersonalityEvent> channel = new Channel<>(executor, "MyChannel");
		PersonalityEvent event = mock(PersonalityEvent.class);
		Personality personality = mock(Personality.class);

		Mockito.when(event.getPersonality()).thenReturn(personality);
		Mockito.when(event.getTenantId()).thenReturn("test_nonprd_01");
		Mockito.when(event.getForcedRefresh()).thenReturn(true);
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));

		channel.addPersonalityEventInQueue(event);
		channel.addPersonalityEventInQueue(event); // Adding the same event again

		assertEquals(2, channel.getQueue().size()); // Queue size should be 2 because of forced refresh
		assertTrue(channel.getEventMap().containsKey("test_nonprd_01"+1));
	}

	@Test
	public void testAddPersonalityItems() {
		Channel<PersonalityEvent> channel = new Channel<>(executor, "MyChannel");
		List<PersonalityEvent> events = new ArrayList<>();
		PersonalityEvent event1 = mock(PersonalityEvent.class);
		PersonalityEvent event2 = mock(PersonalityEvent.class);
		Personality personality1 = mock(Personality.class);
		Personality personality2 = mock(Personality.class);

		when(event1.getPersonality()).thenReturn(personality1);
		when(event1.getTenantId()).thenReturn("test_nonprd_01");
		when(event2.getPersonality()).thenReturn(personality2);
		when(event2.getTenantId()).thenReturn("test_nonprd_01");
		when(personality1.getPersonId()).thenReturn(new ObjectIdLong(1L));
		when(personality2.getPersonId()).thenReturn(new ObjectIdLong(2L));

		events.add(event1);
		events.add(event2);

		channel.addPersonalityItems(events);

		assertEquals(2, channel.getQueue().size());
		assertTrue(channel.getEventMap().containsKey("test_nonprd_01"+1));
		assertTrue(channel.getEventMap().containsKey("test_nonprd_01"+2));
	}

	@Test
	public void testAddDuplicatePersonalityItems() {
		Channel<PersonalityEvent> channel = new Channel<>(executor, "MyChannel");
		List<PersonalityEvent> events = new ArrayList<>();
		PersonalityEvent event1 = mock(PersonalityEvent.class);
		PersonalityEvent event2 = mock(PersonalityEvent.class);
		Personality personality1 = mock(Personality.class);

		when(event1.getPersonality()).thenReturn(personality1);
		when(event1.getTenantId()).thenReturn("test_nonprd_01");
		when(event2.getPersonality()).thenReturn(personality1);
		when(event2.getTenantId()).thenReturn("test_nonprd_01");// Same personality as event1
		when(personality1.getPersonId()).thenReturn(new ObjectIdLong(1L));

		events.add(event1);
		events.add(event2);

		channel.addPersonalityItems(events);

		assertEquals(1, channel.getQueue().size()); // Only one event should be added
		assertTrue(channel.getEventMap().containsKey("test_nonprd_01"+1));
	}

	@Test
	public void testAddDuplicatePersonalityItemsWithForcedRefresh() {
		Channel<PersonalityEvent> channel = new Channel<>(executor, "MyChannel");
		List<PersonalityEvent> events = new ArrayList<>();
		PersonalityEvent event1 = mock(PersonalityEvent.class);
		PersonalityEvent event2 = mock(PersonalityEvent.class);
		Personality personality1 = mock(Personality.class);
		when(event2.getForcedRefresh()).thenReturn(true);
		when(event1.getPersonality()).thenReturn(personality1);
		when(event1.getTenantId()).thenReturn("test_nonprd_01");
		when(event2.getPersonality()).thenReturn(personality1); // Same personality as event1
		when(event2.getTenantId()).thenReturn("test_nonprd_01");
		when(personality1.getPersonId()).thenReturn(new ObjectIdLong(1L));

		events.add(event1);
		events.add(event2);

		channel.addPersonalityItems(events);

		assertEquals(2, channel.getQueue().size()); // Only one event should be added
		assertTrue(channel.getEventMap().containsKey("test_nonprd_01"+1));
	}
}


