package com.kronos.people.personality.exception;

import static com.kronos.people.personality.exception.PersonalityErrorCode.PERSON_NUMBER_NOT_VALID;
import static com.kronos.people.personality.exception.PersonalityErrorCode.USER_ACCOUNT_ID_NOT_VALID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.Map;

import com.kronos.people.personality.exception.impl.ExceptionHelper;
import com.kronos.people.personality.model.IdentifierType;
import com.kronos.people.personality.properties.KronosPropertiesFacade;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ExceptionHelperMicroTest {

	@InjectMocks
	ExceptionHelper expHelp;

	@BeforeEach
	public void setUp() {
	}

	@Test
	public void createExceptionCons1Test() {
		PersonalityExtensionException pextExp = expHelp.createException(PersonalityErrorCode.BADGE_NUMBER_NOT_VALID);
		assertEquals(PersonalityErrorCode.BADGE_NUMBER_NOT_VALID.getDefaultMessage(), pextExp.getMessage());

	}

	@Test
	public void createExceptionCons2Test() {

		Map<String, String> map = new HashMap<String, String>();
		map.put("Key", "Value");
		PersonalityExtensionException pextExp = expHelp.createException(PersonalityErrorCode.NOT_ACTIVE, map);
		assertEquals("Value", pextExp.getMessageArgs().get("Key"));
		assertEquals(PersonalityErrorCode.NOT_ACTIVE.getDefaultMessage(), pextExp.getMessage());

	}

	@Test
	public void createExceptionCons3Test() {

		Map<String, String> map = new HashMap<String, String>();
		map.put("Key", "Value");
		PersonalityExtensionException pextExp = expHelp.createException(PersonalityErrorCode.NOT_ACTIVE, map, "Debug Message");
		assertEquals("Value", pextExp.getMessageArgs().get("Key"));
		assertEquals(PersonalityErrorCode.NOT_ACTIVE.getDefaultMessage(), pextExp.getMessage());
		assertEquals("Debug Message", pextExp.getDebugMessage());

	}

	@Test
	public void transformExceptionTest() {

		RuntimeException rex = new RuntimeException("My Exception");

		PersonalityExtensionException pextExp = expHelp.transformException(rex);
		assertEquals(PersonalityErrorCode.UNKNOWN_ERROR.getDefaultMessage(), pextExp.getMessage());
		assertEquals("My Exception", pextExp.getDebugMessage());

		PersonalityExtensionException pext = new PersonalityExtensionException(PERSON_NUMBER_NOT_VALID, "My Message");
		PersonalityExtensionException pex = expHelp.transformException(pext);
		assertEquals("My Message", pex.getMessage());
		assertNull(pex.getDebugMessage());

	}

	@Test
	public void createExceptionCons4Test() {

		PersonalityExtensionException pextExp = expHelp.createException(IdentifierType.USERACCOUNTID);
		assertEquals(USER_ACCOUNT_ID_NOT_VALID.getDefaultMessage(), pextExp.getMessage());

	}
	
	@Test
	public void getLocalizedMessageForKeyTest()
	{
		KronosPropertiesFacade facade = mock(KronosPropertiesFacade.class);
		expHelp.setKronosPropertiesFacade(facade);
		when(expHelp.getValueFromKronosProperties("abc")).thenThrow(new RuntimeException());
		
		String message=expHelp.getLocalizedMessageForKey("abc");
		assertNull(null, message);
	}

}
