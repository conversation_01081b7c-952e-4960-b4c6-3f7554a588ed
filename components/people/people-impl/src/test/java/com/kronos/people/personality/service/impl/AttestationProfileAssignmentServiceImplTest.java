/*******************************************************************************
 * AttestationProfileAssignmentServiceImplTest.java
 * Copyright © 2024 UKG Inc. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.service.impl;

import com.kronos.people.personality.converter.AttestationProfileAssignmentConverter;
import com.kronos.people.personality.dataaccess.entity.AttestationProfileAssignment;
import com.kronos.people.personality.dataaccess.repository.AttestationProfileAssignmentRepository;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileAssignmentDTO;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileDTO;
import com.kronos.timekeeping.service.attestation.api.entity.AttestationProfile;
import com.kronos.timekeeping.service.attestation.api.service.AttestationProfileSetupService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * Class with unit tests for {@link AttestationProfileAssignmentServiceImpl}
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class AttestationProfileAssignmentServiceImplTest {

    private static final Long VALID_PERSON_ASSIGNMENT_ID = 1L;
    private static final LocalDateTime END_OF_TIME = LocalDate.of(3000, 1, 1).atStartOfDay();
    private AttestationProfile defaultProfile;
    private AttestationProfileAssignment attestationProfileAssignment;
    private List<String> ATTESTATION_PROFILE_NAMES;
    private Map<String, AttestationProfileDTO> PROFILE_DTOS;

    @Mock
    private AttestationProfileAssignmentRepository attestationProfileAssignmentRepository;

    @Mock
    private AttestationProfileAssignmentConverter attestationProfileAssignmentConverter;

    @Mock
    private AttestationProfileSetupService attestationProfileSetupService;

    @InjectMocks
    private AttestationProfileAssignmentServiceImpl attestationProfileAssignmentServiceImpl;

    @BeforeEach
    public void setUp() {
        attestationProfileAssignmentServiceImpl.setAttestationProfileSetupService(attestationProfileSetupService);
        attestationProfileAssignment = new AttestationProfileAssignment();
        attestationProfileAssignment.setId(444L);
        attestationProfileAssignment.setProfileId(1L);

        when(attestationProfileSetupService.getAttestationProfile(anyLong())).thenAnswer(i -> {
            Long profileId = (Long) i.getArguments()[0];
            AttestationProfileDTO profile = new AttestationProfileDTO();
            profile.setId(profileId);
            profile.setName(ATTESTATION_PROFILE_NAMES.get(profileId.intValue()));

            return profile;
        });

        defaultProfile = new AttestationProfile();
        defaultProfile.setId(-1L);
        defaultProfile.setName("Empty Profile");
        ATTESTATION_PROFILE_NAMES  = Arrays.asList(defaultProfile.getName(), "First",
                "Second", "Third", "Fourth");

        PROFILE_DTOS = ATTESTATION_PROFILE_NAMES.stream()
                .map(name -> {
                    AttestationProfileDTO dto = new AttestationProfileDTO();
                    dto.setId(getIdByName(name));
                    dto.setName(name);
                    return dto;
                })
                .collect(Collectors.toMap(AttestationProfileDTO::getName, Function.identity()));
    }

    @Test
    public void shouldReturnPersonAssignmentsOfSpecifiedPersonOnGetAttestationProfileAssignments() {
        when(attestationProfileAssignmentRepository.findByPersonIdAndManagerRole(VALID_PERSON_ASSIGNMENT_ID, false)).thenAnswer(
            i -> Collections.singletonList(attestationProfileAssignment));
        stubConvertEntityToDtoSetup();

        List<AttestationProfileAssignmentDTO> actual = attestationProfileAssignmentServiceImpl.getAttestationProfileAssignments(
            VALID_PERSON_ASSIGNMENT_ID);

//        verify(attestationProfileAssignmentConverter, times(1)).convertEntityToDto(
//            any(AttestationProfileAssignment.class), anyMap());
        verify(attestationProfileAssignmentConverter, times(1)).convertEntityToDto(
                any(AttestationProfileAssignment.class), ArgumentMatchers.<Map<Long, AttestationProfileDTO>>any());
        assertEquals(attestationProfileAssignment.getId(), actual.get(0).getId());
        assertEquals(attestationProfileAssignment.getProfileId(), actual.get(0).getAttestationProfile().getId());
    }

    @Test
    public void shouldReturnPersonAssignmentsOfSpecifiedPersonWhenIsManagerRole() {
        when(attestationProfileAssignmentRepository.findByPersonIdAndManagerRole(VALID_PERSON_ASSIGNMENT_ID, true)).thenAnswer(
                i -> Collections.singletonList(attestationProfileAssignment));
        stubConvertEntityToDtoSetup();

        List<AttestationProfileAssignmentDTO> actual = attestationProfileAssignmentServiceImpl.getAttestationProfileAssignments(
                VALID_PERSON_ASSIGNMENT_ID, true);

//        verify(attestationProfileAssignmentConverter, times(1)).convertEntityToDto(
//                any(AttestationProfileAssignment.class), anyMap());
        verify(attestationProfileAssignmentConverter, times(1)).convertEntityToDto(
                any(AttestationProfileAssignment.class), ArgumentMatchers.<Map<Long, AttestationProfileDTO>>any());
        assertEquals(attestationProfileAssignment.getId(), actual.get(0).getId());
        assertEquals(attestationProfileAssignment.getProfileId(), actual.get(0).getAttestationProfile().getId());
    }


    @Test
    public void shouldReturnTrueWhenPersonAssignmentExistsOnIsProfileAssigned() {
        when(attestationProfileAssignmentRepository.countByProfileId(anyLong())).thenReturn(1L);

        assertTrue(attestationProfileAssignmentServiceImpl.isProfileAssigned(1L));
    }

    @Test
    public void shouldReturnFalseWhenPersonAssignmentDoesNotExistOnIsProfileAssigned() {
        when(attestationProfileAssignmentRepository.countByProfileId(anyLong())).thenReturn(0L);

        assertFalse(attestationProfileAssignmentServiceImpl.isProfileAssigned(1L));
    }

    @Test
    public void shouldReturnTrueWhenPersonAssignmentExistsOnIsAssignmentAssigned() {
        when(attestationProfileAssignmentRepository.countByAssignmentId(anyLong())).thenReturn(1L);

        assertTrue(attestationProfileAssignmentServiceImpl.isAssignmentAssigned(1L));
    }

    @Test
    public void shouldReturnFalseWhenPersonAssignmentDoesNotExistOnIsAssignmentAssigned() {
        when(attestationProfileAssignmentRepository.countByAssignmentId(anyLong())).thenReturn(0L);

        assertFalse(attestationProfileAssignmentServiceImpl.isAssignmentAssigned(1L));
    }

    @Test
    public void testGivenAttestationProfileAssignmentExistsWithGivenId_WhenGetAttestationProfileAssignment_ThenReturnsItsDTO(){
        AttestationProfileDTO attestationProfileDTOMock = mock(AttestationProfileDTO.class);
        AttestationProfileAssignmentDTO attestationProfileAssignmentDTOMock = mock(AttestationProfileAssignmentDTO.class);
        LocalDateTime localDateTime = LocalDateTime.of(2024, 1,1,12,0);
        when(attestationProfileAssignmentRepository.findByPersonIdAndManagerRole(1L, false))
                .thenReturn(Collections.singletonList(attestationProfileAssignment));
        when(attestationProfileSetupService.getAttestationProfile(1L)).thenReturn(attestationProfileDTOMock);
//        when(attestationProfileAssignmentConverter.convertEntityToDto(eq(attestationProfileAssignment), anyMap()))
//                .thenReturn(attestationProfileAssignmentDTOMock);
        when(attestationProfileAssignmentConverter.convertEntityToDto(eq(attestationProfileAssignment), ArgumentMatchers.<Map<Long, AttestationProfileDTO>>any()))
                .thenReturn(attestationProfileAssignmentDTOMock);
        when(attestationProfileAssignmentDTOMock.getEffectiveDate()).thenReturn(localDateTime.minusDays(1L));
        when(attestationProfileAssignmentDTOMock.getExpirationDate()).thenReturn(localDateTime.plusDays(1L));

        AttestationProfileAssignmentDTO result = attestationProfileAssignmentServiceImpl
                .getAttestationProfileAssignment(1L, localDateTime, false);

        assertEquals(attestationProfileAssignmentDTOMock, result);
    }

    @Test
    public void testGivenNoAssignmentsAreRetrievedWithGivenId_WhenGetAttestationProfileAssignment_ThenReturnsNull(){
        assertNull(attestationProfileAssignmentServiceImpl.getAttestationProfileAssignment(1L, null));
    }

    private long getIdByName(String name) {
        return defaultProfile.getName()
            .equals(name) ? defaultProfile.getId() : (long) ATTESTATION_PROFILE_NAMES.indexOf(name);
    }

    private AttestationProfileAssignmentDTO buildAssignmentDTO(String profileName,
                                                                      AttestationProfileAssignment entity) {
        AttestationProfileAssignmentDTO assignmentDTO = new AttestationProfileAssignmentDTO();
        AttestationProfileDTO profileDTO = PROFILE_DTOS.get(profileName);
        assignmentDTO.setId(entity == null ? null : entity.getId());
        assignmentDTO.setAttestationProfile(profileDTO);
        assignmentDTO.setEffectiveDate(entity == null ? LocalDateTime.MAX : entity.getEffectiveDate());
        assignmentDTO.setExpirationDate(entity == null ? END_OF_TIME : entity.getExpirationDate());

        return assignmentDTO;
    }


    private AttestationProfileAssignmentDTO convertEntityToDto(AttestationProfileAssignment entity,
                                                                      Map<Long, AttestationProfileDTO> profilesMap) {
        return buildAssignmentDTO(profilesMap.get(entity.getProfileId()).getName(), entity);
    }

    @SuppressWarnings("unchecked") //to suppress warning in Map
    private void stubConvertEntityToDtoSetup() {
//        when(attestationProfileAssignmentConverter.convertEntityToDto(any(AttestationProfileAssignment.class), anyMap()))
//                .thenAnswer(i -> convertEntityToDto((AttestationProfileAssignment) i.getArguments()[0],
//                        (Map<Long, AttestationProfileDTO>) i.getArguments()[1]));

        when(attestationProfileAssignmentConverter.convertEntityToDto(any(AttestationProfileAssignment.class), ArgumentMatchers.<Map<Long, AttestationProfileDTO>>any()))
                .thenAnswer(i -> convertEntityToDto((AttestationProfileAssignment) i.getArguments()[0],
                        (Map<Long, AttestationProfileDTO>) i.getArguments()[1]));
    }
}
