package com.kronos.people.personality.service.impl.ia;

import com.kronos.commonapp.timezone.symbolicperiods.api.ISymbolicPeriodService;
import com.kronos.commonapp.timezone.symbolicperiods.model.LocalDateSpan;
import com.kronos.commonbusiness.datatypes.ia.*;
import com.kronos.people.personality.exception.BaseException;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.exception.impl.ExceptionHelper;
import com.kronos.people.personality.exception.impl.ia.PersonalityExceptionHelper;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.TimekeepingExtension;
import com.kronos.people.personality.model.extension.entry.BaseWageEntry;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.service.PersonalityService;
import com.kronos.people.personality.util.DataRangeHelper;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

//@RunWith(PowerMockRunner.class)
//@PrepareForTest({DataRangeHelper.class})
@ExtendWith(MockitoExtension.class)
public class PersonalityWageRateHistoryServiceImplTest {
    private static final String EMP_HIST_HOURLY_WAGE_RATE_VALUE_COLUMN = "EMP_HIST_HOURLY_WAGE_RATE_VALUE";
    private static final String EMP_HIST_HOURLY_WAGE_RATE_EFFECTIVE_DATE_COLUMN = "EMP_HIST_HOURLY_WAGE_RATE_EFFECTIVE_DATE";
    private static final String EMP_HIST_HOURLY_WAGE_RATE_EXPIRATION_DATE_COLUMN = "EMP_HIST_HOURLY_WAGE_RATE_EXPIRATION_DATE";
    private static final String SYMBOLIC_SERVICE_QUALIFIER = "Current_Payperiod";
    private static final LocalDate WAGE_RATE_EFFECTIVE_DATE = LocalDate.of(2023, 1, 1);
    private static final LocalDate WAGE_RATE_EXPIRATION_DATE = LocalDate.of(2023, 2, 1);
    private static final String WAGE_RATE_VALUE = "55.0";

    @Mock
    private PersonalityService personalityService;
    @Mock
    private ISymbolicPeriodService symbolicPeriodService;
    @Mock
    private PersonalityExceptionHelper exceptionHelper;
    @InjectMocks
    private PersonalityWageRateHistoryServiceImpl wageRateHistoryService;

    private IARequest request;
    private List<Long> employeeIds;
    private Long[] employeeIdsArray;
    private Map<Long, LocalDateSpan> employeeSpans;
    private LocalDateSpan employeeSpan;
    private Map<Long, PersonalityResponse<TimekeepingExtension>> personalityServiceResponse;

    private MockedStatic<DataRangeHelper> mockedDataRangeHelper;

    @BeforeEach
    public void setUp() throws IllegalAccessException, NoSuchFieldException {
//        PowerMockito.mockStatic(DataRangeHelper.class);
        mockedDataRangeHelper = mockStatic(DataRangeHelper.class);
        request = new IARequest();
        request.setSymbolicPeriod(new com.kronos.commonbusiness.datatypes.ref.ObjectRef(1L, SYMBOLIC_SERVICE_QUALIFIER));
        request.setEmployees(Arrays.asList("1401", "1402", "1403", "1404", "1405"));
        IAColumn wageRateColumn = new IAColumn(EMP_HIST_HOURLY_WAGE_RATE_VALUE_COLUMN, EMP_HIST_HOURLY_WAGE_RATE_VALUE_COLUMN);
        IAColumn wageRateEffectiveDateColumn = new IAColumn(EMP_HIST_HOURLY_WAGE_RATE_EFFECTIVE_DATE_COLUMN, EMP_HIST_HOURLY_WAGE_RATE_EFFECTIVE_DATE_COLUMN);
        IAColumn wageRateExpirationDateColumn = new IAColumn(EMP_HIST_HOURLY_WAGE_RATE_EXPIRATION_DATE_COLUMN, EMP_HIST_HOURLY_WAGE_RATE_EXPIRATION_DATE_COLUMN);
        request.setColumns(Arrays.asList(wageRateColumn, wageRateEffectiveDateColumn, wageRateExpirationDateColumn));
        employeeIdsArray = new Long[]{1401L, 1402L, 1403L, 1404L, 1405L};
        employeeIds = Arrays.asList(employeeIdsArray);
        request.setEmployees(employeeIds.stream().map(Object::toString).collect(Collectors.toList()));
        employeeSpans = new HashMap<>();
        employeeSpan = new LocalDateSpan(LocalDate.of(2023, 1, 11), LocalDate.of(2023, 1, 20));
        employeeIds.forEach(id -> employeeSpans.put(id, employeeSpan));
        personalityServiceResponse = new HashMap<>();
        BaseWageEntry wageRateEntry = new BaseWageEntry();
        wageRateEntry.setHourlyRate(55D);
        wageRateEntry.setEffectiveDate(WAGE_RATE_EFFECTIVE_DATE);
        wageRateEntry.setExpirationDate(WAGE_RATE_EXPIRATION_DATE);
        personalityServiceResponse.put(1401L, createTimekeepingExtension(List.of(wageRateEntry), "661401"));
        personalityServiceResponse.put(1402L, createTimekeepingExtension(List.of(wageRateEntry),"661402"));
        personalityServiceResponse.put(1403L, createTimekeepingExtension(List.of(wageRateEntry),"661403"));
        personalityServiceResponse.put(1404L, createTimekeepingExtension(Arrays.asList(wageRateEntry, wageRateEntry),"661404"));
        personalityServiceResponse.put(1405L, createTimekeepingExtension(Arrays.asList(wageRateEntry, wageRateEntry, wageRateEntry), "661405"));
    }

    @AfterEach
    public void tearDown() {
        mockedDataRangeHelper.close();
    }

    @Test
    public void getWageRateHistory() {
//        when(DataRangeHelper.resolveSymbolicPeriodAndDateRange(request, employeeIds, symbolicPeriodService)).thenReturn(employeeSpans);
//        when(DataRangeHelper.isDateOverlap(Mockito.anyObject(),Mockito.anyObject(),Mockito.anyObject(),Mockito.anyObject())).thenReturn(true);
//        when(personalityService.findTimekeepingExtensions(employeeIdsArray)).thenReturn(personalityServiceResponse);
//        IAResponse response = wageRateHistoryService.getWageRateHistory(request);
//        response.getData().forEach(row -> row.getAttributes().forEach(this::assertAttribute));
//        assertEquals(8, response.getData().size());
//        verifyStatic(times(1));
//        DataRangeHelper.resolveSymbolicPeriodAndDateRange(any(), any(), any());
        mockedDataRangeHelper.when(() -> DataRangeHelper.resolveSymbolicPeriodAndDateRange(request, employeeIds, symbolicPeriodService)).thenReturn(employeeSpans);
        mockedDataRangeHelper.when(() -> DataRangeHelper.isDateOverlap(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(true);
        when(personalityService.findTimekeepingExtensions(employeeIdsArray)).thenReturn(personalityServiceResponse);
        IAResponse response = wageRateHistoryService.getWageRateHistory(request);
        response.getData().forEach(row -> row.getAttributes().forEach(this::assertAttribute));
        assertEquals(8, response.getData().size());
        mockedDataRangeHelper.verify(() -> DataRangeHelper.resolveSymbolicPeriodAndDateRange(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any()), times(1));
        verify(personalityService, times(1)).findTimekeepingExtensions(employeeIdsArray);
    }

    @Test
    public void getWageRateHistoryWithEmployeeErrorResponse() {
//        when(DataRangeHelper.resolveSymbolicPeriodAndDateRange(request, employeeIds, symbolicPeriodService)).thenReturn(employeeSpans);
//        when(DataRangeHelper.isDateOverlap(Mockito.anyObject(),Mockito.anyObject(),Mockito.anyObject(),Mockito.anyObject())).thenReturn(true);
//        personalityServiceResponse.get(1401L).setException(new ExceptionHelper(null).createException(PersonalityErrorCode.UNKNOWN_ERROR));
//        when(personalityService.findTimekeepingExtensions(employeeIdsArray)).thenReturn(personalityServiceResponse);
//        IAErrorDetail errorDetail = new IAErrorDetail();
//        when(exceptionHelper.buildEmployeeExtensionErrorDetail(anyListOf(PersonalityExtensionException.class),
//                eq(request))).thenReturn(errorDetail);
//        IAResponse response = wageRateHistoryService.getWageRateHistory(request);
//        response.getData().forEach(row -> row.getAttributes().forEach(this::assertAttribute));
//        assertEquals(7, response.getData().size());
//        verifyStatic(times(1));
//        DataRangeHelper.resolveSymbolicPeriodAndDateRange(any(), any(), any());
        mockedDataRangeHelper.when(() -> DataRangeHelper.resolveSymbolicPeriodAndDateRange(request, employeeIds, symbolicPeriodService)).thenReturn(employeeSpans);
        mockedDataRangeHelper.when(() -> DataRangeHelper.isDateOverlap(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(true);
        personalityServiceResponse.get(1401L).setException(new ExceptionHelper(null).createException(PersonalityErrorCode.UNKNOWN_ERROR));
        when(personalityService.findTimekeepingExtensions(employeeIdsArray)).thenReturn(personalityServiceResponse);
        IAErrorDetail errorDetail = new IAErrorDetail();
        when(exceptionHelper.buildEmployeeExtensionErrorDetail(ArgumentMatchers.anyList(), ArgumentMatchers.eq(request))).thenReturn(errorDetail);
        IAResponse response = wageRateHistoryService.getWageRateHistory(request);
        response.getData().forEach(row -> row.getAttributes().forEach(this::assertAttribute));
        assertEquals(7, response.getData().size());
        mockedDataRangeHelper.verify(() -> DataRangeHelper.resolveSymbolicPeriodAndDateRange(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any()), times(1));
        verify(personalityService, times(1)).findTimekeepingExtensions(employeeIdsArray);
        verify(exceptionHelper, times(1)).buildEmployeeExtensionErrorDetail(ArgumentMatchers.any(), ArgumentMatchers.any());
        assertEquals(errorDetail, response.getErrorDetails(), "ErrorDetails are not equivalent");
    }

    @Test
    public void getWageRateHistoryWithException() {
        String errorMessage = "Employee extension error Message";
        PersonalityErrorCode errorCode = PersonalityErrorCode.NOT_FOUND;
        IAErrorDetail errorDetail = new IAErrorDetail();
//        when(exceptionHelper.buildCommonErrorDetail(eq(request))).thenReturn(errorDetail);
//        when(DataRangeHelper.resolveSymbolicPeriodAndDateRange(request, employeeIds, symbolicPeriodService))
//                .thenThrow(new BaseException(errorCode, errorMessage, null, null));
//        IAResponse response = wageRateHistoryService.getWageRateHistory(request);
//        assertEquals(0, response.getData().size());
//        verifyStatic(times(1));
//        DataRangeHelper.resolveSymbolicPeriodAndDateRange(any(), any(), any());
//        verify(exceptionHelper, times(1)).buildCommonErrorDetail(any());
        when(exceptionHelper.buildCommonErrorDetail(ArgumentMatchers.eq(request))).thenReturn(errorDetail);
        mockedDataRangeHelper.when(() -> DataRangeHelper.resolveSymbolicPeriodAndDateRange(request, employeeIds, symbolicPeriodService))
                .thenThrow(new BaseException(errorCode, errorMessage, null, null));
        IAResponse response = wageRateHistoryService.getWageRateHistory(request);
        assertEquals(0, response.getData().size());
        mockedDataRangeHelper.verify(() -> DataRangeHelper.resolveSymbolicPeriodAndDateRange(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any()), times(1));
        verify(exceptionHelper, times(1)).buildCommonErrorDetail(ArgumentMatchers.any());
        assertTrue(response.getData().isEmpty(), "Response data should be empty");
        assertEquals(errorDetail, response.getErrorDetails(), "ErrorDetails are not equivalent");
    }

    private PersonalityResponse<TimekeepingExtension> createTimekeepingExtension(List<BaseWageEntry> wageRateEntries, String personNumber) {
        TimekeepingExtension timekeepingExtension = new TimekeepingExtension();
        timekeepingExtension.setPersonNumber(personNumber);
        timekeepingExtension.setBaseWage(new EffectiveDatedCollection<>(wageRateEntries));
        return new PersonalityResponse<>(timekeepingExtension, null);
    }

    private void assertAttribute(IARowAttribute att) {
        switch (att.getName()) {
            case EMP_HIST_HOURLY_WAGE_RATE_VALUE_COLUMN:
                assertEquals(WAGE_RATE_VALUE, att.getValue(), "Wage Rate Value is not correct");
                break;
            case EMP_HIST_HOURLY_WAGE_RATE_EFFECTIVE_DATE_COLUMN:
                assertEquals(WAGE_RATE_EFFECTIVE_DATE.toString(), att.getValue(), "Wage Rate effective date is not correct");
                break;
            case EMP_HIST_HOURLY_WAGE_RATE_EXPIRATION_DATE_COLUMN:
                assertEquals(WAGE_RATE_EXPIRATION_DATE.toString(), att.getValue(), "Wage Rate expiration date is not correct");
                break;
        }
    }
}