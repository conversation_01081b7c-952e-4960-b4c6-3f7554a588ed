package com.kronos.people.personality.dataaccess.entity;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class PersonStatusMicroTest {
	@InjectMocks
	PersonStatus personStatusMicroTest;

	@Test
	public void getPersonidTest() {
		Long personId = 123l;
		personStatusMicroTest.setPersonid(personId);
		assertEquals(personId, personStatusMicroTest.getPersonid());
	}
	
	@Test
	public void getEmployeeStatusIdTest()
	{
		Long empStatusId=123l;
		personStatusMicroTest.setEmployeeStatusId(empStatusId);
		assertEquals(empStatusId, personStatusMicroTest.getEmployeeStatusId());
	}

	@Test
	public void getUserAccountStatusIdTest()
	{
		Long userAccountStatusId=123l;
		personStatusMicroTest.setUserAccountStatusId(userAccountStatusId);
		assertEquals(userAccountStatusId, personStatusMicroTest.getUserAccountStatusId());
	}

	@Test
	public void test_getId(){
		Long id=123l;
		personStatusMicroTest.setId(id);
		assertEquals(id, personStatusMicroTest.getId());
	}

	@Test
	public void test_getEffectivedtm(){
		LocalDateTime localDateTime=LocalDateTime.now();
		personStatusMicroTest.setEffectivedtm(localDateTime);
		assertEquals(localDateTime,personStatusMicroTest.getEffectivedtm());
	}
    @Test
	public void test_getExpirationdtm(){
		LocalDateTime localDateTime=LocalDateTime.now();
		personStatusMicroTest.setExpirationdtm(localDateTime);
		assertEquals(localDateTime,personStatusMicroTest.getExpirationdtm());
	}
}
