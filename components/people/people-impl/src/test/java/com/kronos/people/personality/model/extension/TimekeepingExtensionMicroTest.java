package com.kronos.people.personality.model.extension;

import com.kronos.commonbusiness.currencypolicy.api.business.CurrencyPolicyService;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.container.api.access.SpringContext;
import com.kronos.people.personality.model.extension.entry.CurrencyDetailsEntry;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class TimekeepingExtensionMicroTest {

    TimekeepingExtension timekeepingExtension;
    CurrencyPolicyService currencyPolicyService;

    private MockedStatic<SpringContext> mockedSpringContext;

    @BeforeEach
    public void setup() {
//        PowerMockito.mockStatic(SpringContext.class);
        mockedSpringContext  = Mockito.mockStatic(SpringContext.class);
        currencyPolicyService = Mockito.mock(CurrencyPolicyService.class);
//        PowerMockito.when(SpringContext.getBean(CurrencyPolicyService.class)).thenReturn(currencyPolicyService);
        mockedSpringContext.when(() -> SpringContext.getBean(CurrencyPolicyService.class)).thenReturn(currencyPolicyService);
        timekeepingExtension = new TimekeepingExtension();
        CurrencyDetailsEntry currencyDetailsEntry = new CurrencyDetailsEntry();
//        currencyDetailsEntry.setCurrencyId(new Long("-1"));
        currencyDetailsEntry.setCurrencyId(Long.valueOf("-1"));
        currencyDetailsEntry.setCurrencyCode("UNS");
        timekeepingExtension.setEmployeeCurrency(currencyDetailsEntry);

    }

    @AfterEach
    public void tearDown(){
        mockedSpringContext.close();
    }

    @Test
    public void testGetFallbackCurrency(){
//        ObjectRef fallbackCurrencyref = new ObjectRef(new Long("1"), "USD");
        ObjectRef fallbackCurrencyref = new ObjectRef(Long.valueOf("1"), "USD");
        Mockito.when(currencyPolicyService.getDefaultFallbackCurrencyRef()).thenReturn(fallbackCurrencyref);

        TimekeepingExtension mock = Mockito.mock(TimekeepingExtension.class);
        CurrencyDetailsEntry entry = Mockito.mock(CurrencyDetailsEntry.class);
        Mockito.when(mock.getFallbackCurrency(any())).thenReturn(entry);
        Mockito.when(entry.getCurrencyCode()).thenReturn(null);

        CurrencyDetailsEntry currencyDetailsEntry = timekeepingExtension.getEmployeeCurrency();
        assertEquals(currencyDetailsEntry.getCurrencyCode(), "USD");

    }

    @Test
    public void testGetFallbackCurrencyWhenFallbackCurrencyCodeIsNull(){
//        ObjectRef fallbackCurrencyref = new ObjectRef(new Long("2"), "TestNull");
        ObjectRef fallbackCurrencyref = new ObjectRef(Long.valueOf("2"), "TestNull");
        Mockito.when(currencyPolicyService.getDefaultFallbackCurrencyRef()).thenReturn(fallbackCurrencyref);
        CurrencyDetailsEntry entry = new CurrencyDetailsEntry();
        timekeepingExtension.getFallbackCurrency(entry).setCurrencyCode(null);

        CurrencyDetailsEntry currencyDetailsEntry = timekeepingExtension.getEmployeeCurrency();
        assertEquals(currencyDetailsEntry.getCurrencyCode(), "TestNull");
    }

}
