package com.kronos.people.personality.facade;


import com.kronos.people.personality.model.extension.AccrualExtension;
import com.kronos.people.personality.model.extension.DevicesExtension;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.SchedulingExtension;
import com.kronos.people.personality.model.extension.TimekeepingExtension;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
public class AllExtensionBuilderMicroTest {

	AllExtensionBuilder allExtensionBuilder = new AllExtensionBuilder();
	
	@Test
	public void testGetAllExtensionFromMap(){
		EmployeeExtension employeeExtensionObj = new EmployeeExtension();
		employeeExtensionObj.setActive(true);
		AccrualExtension accrualExtensionObj = new AccrualExtension();
		DevicesExtension deviceExtensionObj = new DevicesExtension();
		SchedulingExtension schedulingExtensionObj = new SchedulingExtension();
		TimekeepingExtension timekeepingExtensionObj = new TimekeepingExtension();
		assertNotNull(allExtensionBuilder.getAllExtensionFromMap(employeeExtensionObj, accrualExtensionObj, deviceExtensionObj, schedulingExtensionObj, timekeepingExtensionObj));
	}
}
