package com.kronos.people.personality.converter;


import com.kronos.commonapp.labortransfer.api.ILaborTransferService;
import com.kronos.commonapp.orgmap.traversal.api.IOrgMapService;
import com.kronos.people.personality.exception.ExceptionPropertyReader;
import com.kronos.people.personality.exception.ProxyException;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.positions.PositionEntry;
import com.kronos.people.personality.model.extension.entry.positions.PositionOrderEntry;
import com.kronos.people.personality.model.extension.entry.positions.PositionStatusEntry;
import com.kronos.people.proxy.api.dto.GenericPosition;
import com.kronos.people.proxy.api.dto.PositionOrder;
import com.kronos.people.proxy.api.dto.PositionStatus;
import com.kronos.people.proxy.api.dto.PositionStatusType;
import com.kronos.wfc.commonapp.people.business.positions.IEmployeePosition;
import com.kronos.wfc.commonapp.people.business.positions.PositionLaborAccountSet;
import com.kronos.wfc.commonapp.people.business.positions.PositionStatusSet;
import com.kronos.wfc.commonapp.types.business.EmploymentStatusType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.ThreadLocalRandom;

import static com.kronos.people.personality.exception.PeopleProxyException.ERROR_CODE_NOT_FOUND;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PositionConverterTest {
    private static final Long ID = 1L;
    private static final Long EMPLOYEE_ID = 100L;
    private static final ObjectIdLong OBJECT_ID = new ObjectIdLong(ID);
    private static final String QUALIFIER = "Qualifier";
    private static final String POSITION_STATUS_NAME = "Position Status Name";

    @InjectMocks
    PositionConverter converter;

    @Mock
    private ILaborTransferService transferService;

    @Mock
    private IOrgMapService orgMapService;

    @Mock
    private IEmployeePosition position;

    @Mock
    private PositionLaborAccountSet positionLaborAccountSet;

    @Mock
    private PositionStatusSet positionStatusSet;

    @Mock
    private ExceptionPropertyReader exceptionReader;

    @Mock
    private EmploymentStatusType mockEmploymentStatusType;

    private MockedStatic<EmploymentStatusType> mockedEmployeeStatusType;

    private LocalDate effectiveDate;
    private LocalDate expirationDate;

    @BeforeEach
    public void setUp() {
        mockedEmployeeStatusType = Mockito.mockStatic(EmploymentStatusType.class);
        when(position.getPositionId()).thenReturn(OBJECT_ID);
        when(position.getName()).thenReturn(QUALIFIER);
        when(position.getPersonId()).thenReturn(OBJECT_ID);
        when(position.getPositionLaborAccounts()).thenReturn(positionLaborAccountSet);
        when(positionLaborAccountSet.getPositionLaborAccounts()).thenReturn(Collections.emptyList());
        when(position.getPositionStatuses()).thenReturn(positionStatusSet);
        when(positionStatusSet.getAllStatuses()).thenReturn(Collections.emptyList());

        effectiveDate = LocalDate.now().minusDays(2);
        expirationDate = LocalDate.now().plusDays(2);
    }

    @AfterEach
    public void tearDown() {
        if (mockedEmployeeStatusType != null) {
            mockedEmployeeStatusType.close();
            mockedEmployeeStatusType = null;
        }
    }

    @Test
    public void convertPositionShouldReturnPositionWithPopulatedFields() {
        GenericPosition convertedPosition = converter.convertPosition(position);
        assertEquals(position.getPositionId().toLong(), convertedPosition.getId());
        assertEquals(position.getName(), convertedPosition.getName());
        assertEquals(position.getPersonId().toLong(), convertedPosition.getPersonId());
    }

    @Test
    public void convertPositionEntry_whenPositionEntryContainsPositionWithUnknownPositionEntry_thenThrowException() {
        int orderNumber = ThreadLocalRandom.current().nextInt(10);
        boolean primary = ThreadLocalRandom.current().nextBoolean();
        PositionEntry entry = createPositionEntry(-99999L, orderNumber, primary);
        when(exceptionReader.getProperty(PositionConverter.POSITION_STATUS_TYPE_NOT_FOUND_ERROR)).thenReturn(StringUtils.EMPTY);

        try{
            converter.convertPositionEntry(entry, EMPLOYEE_ID);
            fail("PositionStatusEntry conversion should throw exception due to unknown status");
        } catch (ProxyException pExc) {
            assertEquals(ERROR_CODE_NOT_FOUND, pExc.getErrorCode());
        }
    }

    @Test
    public void convertPositionEntry_whenPassValidPositionEntry_thenReturnConvertedPosition() {
        int orderNumber = ThreadLocalRandom.current().nextInt(10);
        boolean primary = ThreadLocalRandom.current().nextBoolean();
        PositionEntry entry = createPositionEntry(PositionStatusEntry.ACTIVE, orderNumber, primary);
        when(EmploymentStatusType.getEmploymentStatusType(new ObjectIdLong(PositionStatusEntry.ACTIVE))).thenReturn(mockEmploymentStatusType);
        when(mockEmploymentStatusType.getName()).thenReturn(POSITION_STATUS_NAME);
        GenericPosition expectedPosition = createPosition(PositionStatusType.ACTIVE, orderNumber, primary);

        GenericPosition actualPosition = converter.convertPositionEntry(entry, EMPLOYEE_ID);

        assertThat(actualPosition, is(equalTo(expectedPosition)));
    }

    private PositionEntry createPositionEntry(Long positionStatusTypeId, Integer orderNumber, Boolean primary) {
        PositionEntry entry = new PositionEntry();
        entry.setPositionId(ID);
        entry.setPositionName(QUALIFIER);
        PositionStatusEntry entryStatus = createPositionStatus(positionStatusTypeId);
        Collection<PositionStatusEntry> effectiveDatedEntries = Collections.singletonList(entryStatus);
        EffectiveDatedCollection<PositionStatusEntry> effDatedPositionStatus = new EffectiveDatedCollection<>(effectiveDatedEntries);
        entry.setEffDatedPositionStatus(effDatedPositionStatus);
        PositionOrderEntry positionOrderEntry = createPositionOrder(orderNumber, primary);
        Collection<PositionOrderEntry> positionOrderEntries = Collections.singletonList(positionOrderEntry);
        EffectiveDatedCollection<PositionOrderEntry> effDatedPositionOrder = new EffectiveDatedCollection<>(positionOrderEntries);
        entry.setEffDatedPositionOrder(effDatedPositionOrder);
        return entry;
    }

    private PositionStatusEntry createPositionStatus(Long positionStatusTypeId) {
        PositionStatusEntry entryStatus = new PositionStatusEntry();
        entryStatus.setPositionStatusTypeId(positionStatusTypeId);
        entryStatus.setEffectiveDate(effectiveDate);
        entryStatus.setExpirationDate(expirationDate);

        return entryStatus;
    }

    private PositionOrderEntry createPositionOrder(Integer orderNumber, Boolean primary) {
        PositionOrderEntry positionOrderEntry = new PositionOrderEntry();
        positionOrderEntry.setOrderNumber(orderNumber);
        positionOrderEntry.setPrimary(primary);
        positionOrderEntry.setEffectiveDate(effectiveDate);
        positionOrderEntry.setExpirationDate(expirationDate);
        return positionOrderEntry;
    }

    private GenericPosition createPosition(PositionStatusType positionStatusType, Integer orderNumber, Boolean primary) {
        GenericPosition position = new GenericPosition();
        position.setId(ID);
        position.setName(QUALIFIER);
        position.setPersonId(EMPLOYEE_ID);

        PositionStatus statusDto = new PositionStatus();
        statusDto.setName(POSITION_STATUS_NAME);
        statusDto.setPositionStatusType(positionStatusType);
        statusDto.setEffectiveDate(effectiveDate);
        statusDto.setExpirationDate(expirationDate);

        position.setStatuses(Collections.singletonList(statusDto));

        PositionOrder positionOrder = new PositionOrder();
        positionOrder.setOrderNumber(orderNumber);
        positionOrder.setPrimary(primary);
        positionOrder.setEffectiveDate(effectiveDate);
        positionOrder.setExpirationDate(expirationDate);
        position.setOrders(Collections.singletonList(positionOrder));
        return position;
    }
}
