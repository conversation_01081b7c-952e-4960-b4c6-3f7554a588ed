package com.kronos.people.personality.service.impl;

import com.kronos.commonapp.orgmap.common.api.ISystemProperties;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.converter.AttestationProfileAssignmentConverter;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.dataaccess.entity.AttestationProfileAssignment;
import com.kronos.people.personality.dataaccess.repository.PersonAssignmentRepository;
import com.kronos.people.personality.model.PersonAttestationProfileAssignmentsDTO;
import com.kronos.people.personality.util.PersonAssignmentServiceHelper;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileAssignmentDTO;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileDTO;
import com.kronos.timekeeping.service.attestation.api.entity.AttestationProfile;
import com.kronos.timekeeping.service.attestation.api.service.AttestationProfileService;
import com.kronos.wfc.commonapp.people.business.person.AssignAttestationProfile;
import com.kronos.wfc.commonapp.people.business.person.AssignAttestationProfileSet;
import com.kronos.wfc.commonapp.people.shared.PeopleEditorConstants;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

//@RunWith(PowerMockRunner.class)
//@PrepareForTest({  AssignAttestationProfile.class})
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonAssignmentServiceImplTest {
    private static final AttestationProfile defaultProfile;
    private static final Long PERSON_ID = 1L;
    private static final Integer SEQUENCE_1 = 1;
    private static final Long PROFILE_ID = 2L;
    private static final String PROFILE_QUALIFIER = "PROFILE_QUALIFIER";

    static {
        defaultProfile = new AttestationProfile();
        defaultProfile.setId(-1L);
        defaultProfile.setName("Empty Profile");
    }

    private static final Long VALID_PERSON_ASSIGNMENT_ID = 1L;

    private static final LocalDateTime END_OF_TIME = LocalDate.of(3000, 1, 1).atStartOfDay();
    private static final LocalDateTime BEGINNING_OF_TIME = LocalDate.of(1900, 1, 1).atStartOfDay();

    private static final List<String> ATTESTATION_PROFILE_NAMES = Arrays.asList(defaultProfile.getName(), "First",
        "Second", "Third", "Fourth");

    private static Map<String, AttestationProfileDTO> PROFILE_DTOS;
    private static Map<String, AttestationProfile> CONVERTED_PROFILES;

//    @Rule
//    public final ExpectedException expected = ExpectedException.none();
    @Test
    public void testMethodThatThrowsException() {
        Exception exception = assertThrows(Exception.class, () -> {
            throw new Exception("Expected exception message");
        });
        assertEquals("Expected exception message", exception.getMessage());
    }

    @Mock
    private AttestationProfileAssignmentConverter profileAssignmentConverter;

    @Mock
    private AttestationProfileService attestationProfileService;

    @Mock
    private ISystemProperties systemProperties;

    @Mock
    private PersonAssignmentRepository personAssignmentRepository;
    @Mock
    private PersonAssignmentServiceHelper personAssignmentServiceHelper;

    @Mock
    AdapterHelper adapterHelper;

    @Mock
    AssignAttestationProfileSet assignAttestationProfileSet;

    @Mock
    AssignAttestationProfile assignAttestationProfile;

    private PersonAssignmentServiceImpl personAssignmentServiceImpl;
    private MockedStatic<AssignAttestationProfile> mockedAssignAttestationProfile;

    @BeforeEach
//    @SuppressWarnings("unchecked")
    public void setUp() {
        personAssignmentServiceImpl = new PersonAssignmentServiceImpl(profileAssignmentConverter,
                personAssignmentServiceHelper, adapterHelper, personAssignmentRepository);
        personAssignmentServiceImpl.setAttestationProfileService(attestationProfileService);
        mockedAssignAttestationProfile = Mockito.mockStatic(AssignAttestationProfile.class);

        CONVERTED_PROFILES = ATTESTATION_PROFILE_NAMES.stream()
            .map(name -> {
                AttestationProfile profile = new AttestationProfile();
                profile.setId(getIdByName(name));
                profile.setName(name);
                return profile;
            })
            .collect(Collectors.toMap(AttestationProfile::getName, Function.identity()));

        PROFILE_DTOS = ATTESTATION_PROFILE_NAMES.stream()
            .map(name -> {
                AttestationProfileDTO dto = new AttestationProfileDTO();
                dto.setId(getIdByName(name));
                dto.setName(name);
                return dto;
            })
            .collect(Collectors.toMap(AttestationProfileDTO::getName, Function.identity()));

        when(attestationProfileService.findByName(any(String.class))).thenAnswer(i -> {
            String name = (String) i.getArguments()[0];
            return Optional.of(CONVERTED_PROFILES.get(name));
        });

        when(systemProperties.getBeginningOfTime()).thenReturn(BEGINNING_OF_TIME.toLocalDate());
        when(systemProperties.getEndOfTime()).thenReturn(END_OF_TIME.toLocalDate());

        when(profileAssignmentConverter.convertEntityToDto(any(AttestationProfileAssignment.class),
            any(AttestationProfileDTO.class))).thenAnswer(
            i -> convertEntityToDto((AttestationProfileAssignment) i.getArguments()[0],
                (AttestationProfileDTO) i.getArguments()[1]));

        when(profileAssignmentConverter.convertEntityToDto(any(AttestationProfileAssignment.class),
            anyMap())).thenAnswer(
            i -> convertEntityToDto((AttestationProfileAssignment) i.getArguments()[0],
                (Map<Long, AttestationProfileDTO>) i.getArguments()[1]));

    }

    @AfterEach
    public void tearDown() {
        mockedAssignAttestationProfile.close();
    }

    @Test
    public void shouldReturnNullOnUpdateWhenNullPersonAssignmentPassed() {
        AttestationProfileAssignmentDTO expected = personAssignmentServiceImpl.addProfileAssignment(
            VALID_PERSON_ASSIGNMENT_ID, null);
        assertNull(expected);
    }

    @Test
    public void shouldReturnProfileAssignmentOnAddProfileAssignment() {
        AttestationProfileAssignmentDTO dto = new AttestationProfileAssignmentDTO();
        dto.setAttestationProfile(buildProfileDTO(ATTESTATION_PROFILE_NAMES.get(2)));
        dto.getAttestationProfile().setId(null);
        dto.setEffectiveDate(LocalDate.now().plusDays(1).atStartOfDay());

        ObjectIdLong id = new ObjectIdLong(VALID_PERSON_ASSIGNMENT_ID);

//        PowerMockito.mockStatic(AssignAttestationProfile.class);
//        PowerMockito.when(AssignAttestationProfile.getByPersonId(id, false))
//                .thenReturn(assignAttestationProfileSet);
        mockedAssignAttestationProfile.when(() -> AssignAttestationProfile.getByPersonId(id, false))
                .thenReturn(assignAttestationProfileSet);

        doNothing().when(assignAttestationProfileSet).sortAndSetAssignAttestationProfile(any());
        doNothing().when(assignAttestationProfileSet).update();
        when(assignAttestationProfileSet.getAssignAttestationProfileForDate(any())).thenReturn(assignAttestationProfile);

        when(assignAttestationProfile.getEffectiveDate()).thenReturn(KDate.createDate().plusDays(1));

        when(profileAssignmentConverter.convertEntityToDto(any(AssignAttestationProfile.class),
                any(AttestationProfile.class))).thenReturn(dto);

        when(personAssignmentServiceHelper.createEmptyAttestationProfile(any(), anyBoolean())).thenCallRealMethod();

        AttestationProfileAssignmentDTO actual = personAssignmentServiceImpl.addProfileAssignment(
            VALID_PERSON_ASSIGNMENT_ID, dto);

        assertEquals(dto.getEffectiveDate(), actual.getEffectiveDate());
        assertNotNull(actual.getAttestationProfile());
        assertEquals(dto.getAttestationProfile().getName(), actual.getAttestationProfile().getName());
    }

    @Test
    public void multiUpsert() throws Exception {
        Map<Integer, APIException> exceptionHolder = new HashMap<>();
        Map<Integer, PersonAttestationProfileAssignmentsDTO> profileAssignments = new HashMap<>();
        PersonAttestationProfileAssignmentsDTO assignmentDTO = new PersonAttestationProfileAssignmentsDTO();
        assignmentDTO.setPerson(new ObjectRef(PERSON_ID));
        assignmentDTO.setAttestationProfileAssignments(Collections.emptyList());
        profileAssignments.put(SEQUENCE_1, assignmentDTO);
        List<ObjectRef> profiles = new ArrayList<>();
        ObjectRef profile = new ObjectRef(PROFILE_ID, PROFILE_QUALIFIER);
        profiles.add(profile);
        when(personAssignmentServiceHelper.extractPersonProfilesDistinct(anyList())).thenReturn(profiles);
        Map<Long, List<AttestationProfileAssignment>> attestationProfileAssignmentsToPersonId = new HashMap<>();
        List<AttestationProfileAssignment> attestationProfileAssignments = new ArrayList<>();
        AttestationProfileAssignment attestationProfileAssignment = new AttestationProfileAssignment();
        attestationProfileAssignments.add(attestationProfileAssignment);
        attestationProfileAssignmentsToPersonId.put(PERSON_ID, attestationProfileAssignments);
        when(personAssignmentServiceHelper.combineProfilesAndRequestInfo(anyList(), anyMap(), anyMap(), anyMap()))
                .thenReturn(attestationProfileAssignmentsToPersonId);

//        PowerMockito.mockStatic(AssignAttestationProfile.class);
//        PowerMockito.when(AssignAttestationProfile.getByPersonId(any(ObjectIdLong.class), anyBoolean()))
//                .thenReturn(assignAttestationProfileSet);
        mockedAssignAttestationProfile.when(() -> AssignAttestationProfile.getByPersonId(any(ObjectIdLong.class), anyBoolean()))
                .thenReturn(assignAttestationProfileSet);

        doNothing().when(assignAttestationProfileSet).sortAndSetAssignAttestationProfile(any());
        doNothing().when(assignAttestationProfileSet).update();
        when(assignAttestationProfileSet.getAssignAttestationProfileForDate(any())).thenReturn(assignAttestationProfile);



        final Map<Integer, PersonAttestationProfileAssignmentsDTO> result = personAssignmentServiceImpl
                .multiUpsert(exceptionHolder, profileAssignments,false);

        verify(personAssignmentServiceHelper).checkMissingProfiles(any(), any(), any(), any());
        assertEquals(result, profileAssignments);
    }


    @Test
    public void testMultiUpsertWithMergeEffectiveDating() {
        Map<Integer, APIException> exceptionHolder = new HashMap<>();
        Map<Integer, PersonAttestationProfileAssignmentsDTO> profileAssignments = new HashMap<>();
        Map<Long, List<AttestationProfileAssignment>> attestationProfileAssignmentsToPersonId = new HashMap<>();

        setupForUpsert(profileAssignments, attestationProfileAssignmentsToPersonId);

        Map<Integer, PersonAttestationProfileAssignmentsDTO> result = personAssignmentServiceImpl
                .multiUpsert(exceptionHolder, profileAssignments,true);

        verify(personAssignmentServiceHelper).checkMissingProfiles(any(), any(), any(), any());
        assertEquals(result, profileAssignments);
    }


    @Test
    public void testMultiUpsertWithNotMergeEffectiveDating() {
        Map<Integer, APIException> exceptionHolder = new HashMap<>();
        Map<Integer, PersonAttestationProfileAssignmentsDTO> profileAssignments = new HashMap<>();
        Map<Long, List<AttestationProfileAssignment>> attestationProfileAssignmentsToPersonId = new HashMap<>();

        setupForUpsert(profileAssignments, attestationProfileAssignmentsToPersonId);

        Map<Integer, PersonAttestationProfileAssignmentsDTO> result = personAssignmentServiceImpl
                .multiUpsert(exceptionHolder, profileAssignments,false);

        verify(personAssignmentServiceHelper).checkMissingProfiles(any(), any(), any(), any());
        assertEquals(result, profileAssignments);
    }

    private void setupForUpsert(Map<Integer, PersonAttestationProfileAssignmentsDTO> profileAssignments, Map<Long, List<AttestationProfileAssignment>> attestationProfileAssignmentsToPersonId) {
        profileAssignments.put(SEQUENCE_1, buildPersonAttestationProfileAssignmentsDTO());
        List<AttestationProfileAssignment> attestationProfileAssignments = new ArrayList<>();
        AttestationProfileAssignment attestationProfileAssignment = new AttestationProfileAssignment();
        attestationProfileAssignments.add(attestationProfileAssignment);
        attestationProfileAssignmentsToPersonId.put(PERSON_ID, attestationProfileAssignments);

        List<ObjectRef> profiles = List.of(new ObjectRef(PROFILE_ID, PROFILE_QUALIFIER));
        when(personAssignmentServiceHelper.extractPersonProfilesDistinct(anyList())).thenReturn(profiles);
        when(personAssignmentServiceHelper.combineProfilesAndRequestInfo(anyList(), anyMap(), anyMap(), anyMap()))
                .thenReturn(attestationProfileAssignmentsToPersonId);
        when(personAssignmentServiceHelper.createEmptyAttestationProfile(any(), any())).thenCallRealMethod();

//        PowerMockito.mockStatic(AssignAttestationProfile.class);
//        PowerMockito.when(AssignAttestationProfile.getByPersonId(any(ObjectIdLong.class), anyBoolean()))
//                .thenReturn(assignAttestationProfileSet);
        mockedAssignAttestationProfile.when(() -> AssignAttestationProfile.getByPersonId(any(ObjectIdLong.class), anyBoolean()))
                .thenReturn(assignAttestationProfileSet);
        doNothing().when(assignAttestationProfileSet).sortAndSetAssignAttestationProfile(any());
        doNothing().when(assignAttestationProfileSet).update();

        when(assignAttestationProfileSet.getAssignAttestationProfileForDate(any())).thenReturn(assignAttestationProfile);
    }

    private static PersonAttestationProfileAssignmentsDTO buildPersonAttestationProfileAssignmentsDTO() {
        PersonAttestationProfileAssignmentsDTO assignmentDTO = new PersonAttestationProfileAssignmentsDTO();
        assignmentDTO.setPerson(new ObjectRef(PERSON_ID));
        com.kronos.people.personality.model.AttestationProfileAssignmentDTO attestationProfileAssignmentDTO
                = new com.kronos.people.personality.model.AttestationProfileAssignmentDTO();
        attestationProfileAssignmentDTO.setProfile(new ObjectRef(PROFILE_ID, PROFILE_QUALIFIER));
        attestationProfileAssignmentDTO.setEffectiveDate(LocalDate.now().minusMonths(1));
        attestationProfileAssignmentDTO.setExpirationDate(LocalDate.now().plusMonths(1));
        assignmentDTO.setManagerRoleAttestationProfileAssignments(List.of(attestationProfileAssignmentDTO));
        return assignmentDTO;
    }

    @Test
    public void shouldReplaceTheEmptyAndTheSameAttestation() {
        ObjectIdLong personId = new ObjectIdLong(PERSON_ID);

        AssignAttestationProfile profile1 = Mockito.mock(AssignAttestationProfile.class);
        when(profile1.getEffectiveDate()).thenReturn(KDate.getNewSotDate());
        when(profile1.getAttestationProfileId()).thenReturn(new ObjectIdLong(PeopleEditorConstants.EMPTY_ATTESTATION_PROFILE_ID));
        when(profile1.getObjectId()).thenReturn(new ObjectIdLong(2));
        AssignAttestationProfile profile2 = Mockito.mock(AssignAttestationProfile.class);
        when(profile2.getEffectiveDate()).thenReturn(KDate.createDate());
        when(profile2.getAttestationProfileId()).thenReturn(new ObjectIdLong(PROFILE_ID));
        when(profile1.getObjectId()).thenReturn(new ObjectIdLong(3));

//        AssignAttestationProfileSet spy = Mockito.spy(new AssignAttestationProfileSet(new ArrayList<AssignAttestationProfile>(Arrays.asList(profile1,profile2)), personId));
        AssignAttestationProfileSet spy = Mockito.spy(new AssignAttestationProfileSet(new ArrayList<>(Arrays.asList(profile1, profile2)), personId));
        spy.setPersonId(personId);

//        PowerMockito.mockStatic(AssignAttestationProfile.class);
//        PowerMockito.when(AssignAttestationProfile.getByPersonId(personId, false)).thenReturn(spy);
        mockedAssignAttestationProfile.when(() -> AssignAttestationProfile.getByPersonId(personId, false)).thenReturn(spy);
        doNothing().when(spy).sortAndSetAssignAttestationProfile(any());
        doNothing().when(spy).update();

        List<AssignAttestationProfile> newAssignments = Collections.singletonList(new AssignAttestationProfile(new ObjectIdLong(PROFILE_ID), new KDate(), personId));
        personAssignmentServiceImpl.addNewAssignments(personId, newAssignments, false);

        assertEquals(1, spy.getAllAssignAttestationProfiles().size());
    }

    @Test
    public void findPersonIdsByDisplayProfileIds_whenDisplayProfileIdsPass_thenReturnEmployeeIds() {
        List<Long> displayProfileIds = Arrays.asList(1L,2L);
        List<Long> expectedPersonIds = Arrays.asList(3L,4L);
        when(personAssignmentRepository.findPersonIdsByDisplayProfileIds(displayProfileIds)).thenReturn(expectedPersonIds);

        List<Long> result = personAssignmentServiceImpl.findPersonIdsByDisplayProfileIds(displayProfileIds);
        assertThat(result, is(expectedPersonIds));
    }

    private static long getIdByName(String name) {
        return defaultProfile.getName()
            .equals(name) ? defaultProfile.getId() : (long) ATTESTATION_PROFILE_NAMES.indexOf(name);
    }

    private AttestationProfileDTO buildProfileDTO(String name) {
        AttestationProfileDTO dto = new AttestationProfileDTO();
        dto.setId(getIdByName(name));
        dto.setName(name);
        return dto;
    }

    private AttestationProfile buildProfile(String name) {
        AttestationProfile profile = new AttestationProfile();
        profile.setId(getIdByName(name));
        profile.setName(name);
        return profile;
    }

    private static AttestationProfileAssignmentDTO buildAssignmentDTO(String profileName,
                                                                      AttestationProfileAssignment entity) {
        AttestationProfileAssignmentDTO assignmentDTO = new AttestationProfileAssignmentDTO();
        AttestationProfileDTO profileDTO = PROFILE_DTOS.get(profileName);
        assignmentDTO.setId(entity == null ? null : entity.getId());
        assignmentDTO.setAttestationProfile(profileDTO);
        assignmentDTO.setEffectiveDate(entity == null ? LocalDateTime.MAX : entity.getEffectiveDate());
        assignmentDTO.setExpirationDate(entity == null ? END_OF_TIME : entity.getExpirationDate());

        return assignmentDTO;
    }

    private static AttestationProfileAssignmentDTO convertEntityToDto(AttestationProfileAssignment entity) {
        return buildAssignmentDTO(entity.getProfile().getName(), entity);
    }

    private static AttestationProfileAssignmentDTO convertEntityToDto(AttestationProfileAssignment entity,
            AttestationProfileDTO profile) {
        return buildAssignmentDTO(entity.getProfile().getName(), entity);
    }

    private static AttestationProfileAssignmentDTO convertEntityToDto(AttestationProfileAssignment entity,
                                                                      Map<Long, AttestationProfileDTO> profilesMap) {

        return buildAssignmentDTO(profilesMap.get(entity.getProfileId()).getName(), entity);
    }

    private static AttestationProfileAssignment convertDtoToEntity(AttestationProfileAssignmentDTO dto) {
        AttestationProfileAssignment entity = new AttestationProfileAssignment();
        entity.setId(dto.getId());
        entity.setExpirationDate(dto.getExpirationDate() == null ? END_OF_TIME : dto.getExpirationDate());
        entity.setEffectiveDate(dto.getEffectiveDate());
        entity.setProfile(CONVERTED_PROFILES.get(dto.getAttestationProfile().getName()));
        entity.getProfile().setId(dto.getAttestationProfile().getId());

        return entity;
    }
}