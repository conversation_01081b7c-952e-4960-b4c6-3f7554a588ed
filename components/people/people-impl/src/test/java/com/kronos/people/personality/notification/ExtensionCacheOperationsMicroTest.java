package com.kronos.people.personality.notification;

import com.kronos.cache.api.key.MultiExtensionKey;
import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.people.personality.cache.CachingKeyHelper;
import com.kronos.people.personality.cache.PersonalityCacheAccessor;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.dataaccess.adapter.ConcurrencyHelper;
import com.kronos.people.personality.dataaccess.adapter.ExtensionAdapterEnum;
import com.kronos.people.personality.dataaccess.legacy.ExtensionBuilder;
import com.kronos.people.personality.facade.PersonalityCacheFacade;
import com.kronos.people.personality.model.extension.*;
import com.kronos.people.personality.notification.entry.CacheEntry;
import com.kronos.people.personality.notification.entry.EventType;
import com.kronos.people.personality.notification.entry.PersonalityEvent;
import com.kronos.people.personality.properties.KronosPropertiesFacade;
import com.kronos.people.personality.tenant.MockTenantProvider;
import com.kronos.people.personality.tenant.TenantHandlingFacade;
import com.kronos.tenantprovider.api.TenantProvider;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

//@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class ExtensionCacheOperationsMicroTest {

	@InjectMocks
	private ExtensionCacheOperations extnCacheOperationSpy = null;
	@Mock
	private KronosThreadPoolService kronosThreadPoolService;
	private EmployeeExtension empExt = null;
	private DevicesExtension deviceExt = null;
	AccrualExtension accExt = null;
	SchedulingExtension schExt = null;
	TimekeepingExtension timeKepExt = null;
	Map<String, BaseExtension> extMap = null;

	@Mock
	KronosThreadPoolService threadPoolService;

	@Mock
	ExecutorService executor;

	@Mock
	KronosPropertiesFacade kronosPropertiesFacade;

	@Mock
	private ExtensionBuilder extensionBuilder;
	@Mock
	public PersonalityCacheAccessor personalityCacheAccessor;

	@BeforeEach
	public void init() {
		Mockito.when(threadPoolService.newThreadPool(any())).thenReturn(executor);

//		MockitoAnnotations.initMocks(this);
		MockitoAnnotations.openMocks(this);

		empExt = new EmployeeExtension();
//		empExt.setPersonId(1234l);
		empExt.setPersonId(1234L);
		deviceExt = new DevicesExtension();
		accExt = new AccrualExtension();
		schExt = new SchedulingExtension();
		timeKepExt = new TimekeepingExtension();
		extMap = new HashMap<>();

		extMap.put("EMPLOYEE_EXTENSION", empExt);
		extMap.put("DEVICE_EXTENSION", deviceExt);
		extMap.put("ACCRUAL_EXTENSION", accExt);
		extMap.put("SCHEDULING_EXTENSION", schExt);
		extMap.put("TIMEKEEPING_EXTENSION", timeKepExt);

	}

	private AllExtension getAllExt() {

		AllExtension allExt = new AllExtension();

		allExt.setAccrualExtension(accExt);
		allExt.setDeviceExtension(deviceExt);
		allExt.setEmployeeExtension(empExt);
		allExt.setSchedulingExtension(schExt);
		allExt.setTimekeepingExtension(timeKepExt);
		return allExt;

	}

	@Test
	public void getCacheEntryTest() {
		String tenantId = null;
		Map<String, BaseExtension> extensionMap = null;
		extnCacheOperationSpy = new ExtensionCacheOperations() {
			@Override
			protected String getCurrentTenantId() {
				return "1";
			}
		};

		CachingKeyHelper cachingKeyHelper = new CachingKeyHelper() {
			@Override
			public MultiExtensionKey generatePutKey(Map<String, BaseExtension> extensionsMap) {

				return new MultiExtensionKey(1L);
			}

		};
		extnCacheOperationSpy.setCachingKeyHelper(cachingKeyHelper);

		CacheEntry cachEntry = extnCacheOperationSpy.getCacheEntry(extMap,EventType.INSERT, "2");

		Map<String, BaseExtension> map = cachEntry.getExtensions();

		assertEquals(empExt, map.get("EMPLOYEE_EXTENSION"));
		assertEquals(deviceExt, map.get("DEVICE_EXTENSION"));

	}

	@Test
	public void getExtensionsMapTest() {

		Map<String, BaseExtension> map = extnCacheOperationSpy.getExtensionsMap(getAllExt());
		assertEquals(accExt, map.get(ExtensionAdapterEnum.ACCRUAL.getIdentifier()));
		assertEquals(deviceExt, map.get(ExtensionAdapterEnum.DEVICES.getIdentifier()));
		assertEquals(empExt, map.get(ExtensionAdapterEnum.EMPLOYEE.getIdentifier()));
		assertEquals(empExt, map.get(ExtensionAdapterEnum.EMPLOYEE.getIdentifier()));
		assertEquals(timeKepExt, map.get(ExtensionAdapterEnum.TIMEKEEPING.getIdentifier()));
	}

	@Test
	public void putInRedisChannelTest() {

		AllExtension allExt = mock(AllExtension.class);

		EmployeeExtension empExt = mock(EmployeeExtension.class);
		when(empExt.getPersonId()).thenReturn(1234L);
		when(empExt.getPersonNumber()).thenReturn("56789");
		when(empExt.getUserName()).thenReturn("OliviaAdams");

		DevicesExtension deviceExt = mock(DevicesExtension.class);
		TimekeepingExtension timeKeepingExt = mock(TimekeepingExtension.class);
		AccrualExtension accrualExt = mock(AccrualExtension.class);
		SchedulingExtension schExt = mock(SchedulingExtension.class);

		when(allExt.getEmployeeExtension()).thenReturn(empExt);
		when(allExt.getDeviceExtension()).thenReturn(deviceExt);
		when(allExt.getTimekeepingExtension()).thenReturn(timeKeepingExt);
		when(allExt.getAccrualExtension()).thenReturn(accrualExt);
		when(allExt.getSchedulingExtension()).thenReturn(schExt);

		CachingKeyHelper cachingKeyHelper = mock(CachingKeyHelper.class);

		CacheEntry cacheEntry = mock(CacheEntry.class);

		extnCacheOperationSpy = new ExtensionCacheOperations() {
			@Override
			public CacheEntry getCacheEntry(Map<String, BaseExtension> extensionsMap, EventType eventType,  String tenantId) {
				return cacheEntry;
			}
		};

		TenantProvider tenantProvider = mock(TenantProvider.class);
		TenantHandlingFacade tenantFacade = new TenantHandlingFacade();

		tenantFacade.setTenantProvider(tenantProvider);

		extnCacheOperationSpy.tenantFacade = tenantFacade;

		extnCacheOperationSpy.setCachingKeyHelper(cachingKeyHelper);
		KronosPropertiesFacade kronosPropertiesFacade = mock(KronosPropertiesFacade.class);
		extnCacheOperationSpy.setKronosPropertiesFacade(kronosPropertiesFacade );
         
		DistributedCacheChannelController distributedChannelController = mock(DistributedCacheChannelController.class);
		extnCacheOperationSpy.setDistributedChannelController(distributedChannelController);

		Map<EventType, Channel<CacheEntry>> channelMap = new HashMap<>();
		Channel<CacheEntry> value = new Channel<>();
		channelMap.put(EventType.INSERT, value);
		distributedChannelController.setChannelMap(channelMap);
		extnCacheOperationSpy.putInRedisChannel(allExt, "2");
		verify(distributedChannelController, times(1)).notify(EventType.INSERT, cacheEntry);

	}

	@Test
	public void buildExtensionsPerTest() {
		EmployeeExtension empExt = new EmployeeExtension();

		Personality personality = mock(Personality.class);
		when(personality.getPersonId()).thenReturn(new ObjectIdLong(123L));

		Map<String, BaseExtension> expectedMap = new ConcurrentHashMap<>();
		expectedMap.put("EMPLOYEE_EXTENSION", empExt);

		ExtensionBuilder extBuilder = new ExtensionBuilder(null, null, null, null, null, null, null, null, null, null, null, kronosThreadPoolService, null, null) {
			@Override
			public BaseExtension createExtensions(ExtensionAdapterEnum extnToReturn, Personality personality, LocalDate localDate) {
				empExt.setPersonId(personality.getPersonId().longValue());
				return empExt;
			}

		};

		extnCacheOperationSpy.setExtensionBuilder(extBuilder);
		/*Map<String, BaseExtension> resultMap = extnCacheOperationSpy.buildExtensions(personality);

		assertEquals(empExt, resultMap.get("EMPLOYEE_EXTENSION"));
		assertEquals(empExt.getPersonId(), resultMap.get("EMPLOYEE_EXTENSION").getPersonId());
*/
	}

	@Test
	public void buildExtensionsPerEventTest() {
		PersonalityEvent per = mock(PersonalityEvent.class);

		EmployeeExtension empExt = mock(EmployeeExtension.class);

		/*ExtensionCacheOperations extCacheOper = new ExtensionCacheOperations() {
			@Override
			public Map<String, BaseExtension> buildExtensions(Personality p) {
				Map<String, BaseExtension> mapExt = new ConcurrentHashMap<String, BaseExtension>();
				mapExt.put("EMPLOYEE_EXTENSION", empExt);
				return mapExt;
			}
		};
		Map<String, BaseExtension> expectedResult = extCacheOper.buildExtensions(per);
		assertEquals(empExt, expectedResult.get("EMPLOYEE_EXTENSION"));*/
	}

	@Test
	public void createEntryForPersonalityTest() {
		CacheEntry cacheEntry = mock(CacheEntry.class);
		Personality per = mock(Personality.class);

		/*ExtensionCacheOperations extCacheOper = new ExtensionCacheOperations() {
			@Override
			public Map<String, BaseExtension> buildExtensions(Personality p) {
				Map<String, BaseExtension> mapExt = new ConcurrentHashMap<String, BaseExtension>();
				mapExt.put("EMPLOYEE_EXTENSION", empExt);
				return mapExt;
			}

			@Override
			public CacheEntry getCacheEntry(Map<String, BaseExtension> extensionsMap, Long tenantId) {
				// TODO Auto-generated method stub
				return cacheEntry;
			}
		};*/

		//CacheEntry extectedCacheEntry = extCacheOper.createEntryForPersonality(per, 123l);
		//assertEquals(extectedCacheEntry, cacheEntry);
	}

	@Test
	public void sendToChannelTest() {
		PersonalityEvent per = mock(PersonalityEvent.class);
		when(per.getEventType()).thenReturn(EventType.INSERT);
		CacheEntry cacheEntry = mock(CacheEntry.class);
		Map<String, BaseExtension> map = new ConcurrentHashMap<>();
		DistributedCacheChannelController distCacheChannelContr = mock(DistributedCacheChannelController.class);
		ExtensionCacheOperations extCacheOper = new ExtensionCacheOperations() {

			@Override
			public CacheEntry getCacheEntry(Map<String, BaseExtension> extensionsMap, EventType eventType, String tenantId) {
				return cacheEntry;
			}
		};
		Map<EventType, Channel<CacheEntry>> channelMap = new HashMap<>();
		Channel<CacheEntry> value = new Channel<>();
		channelMap.put(EventType.INSERT, value);
		distCacheChannelContr.setChannelMap(channelMap);
		extCacheOper.setDistributedChannelController(distCacheChannelContr);
		extCacheOper.sendToChannel(per, map);
		verify(distCacheChannelContr, times(1)).notify(EventType.INSERT, cacheEntry);
	}

	@Test
	public void initTest() {
		ChangeNotificationChannelController changeNotChaCont = spy(ChangeNotificationChannelController.class);
		DistributedCacheChannelController distChanlController = mock(DistributedCacheChannelController.class);
		PersonalityCacheFacade perCacheFacade = mock(PersonalityCacheFacade.class);
		Map<EventType, Channel<CacheEntry>> channelMap2 = new HashMap<>();

//		Consumer<List<CacheEntry>> consumer2 = new Consumer<List<CacheEntry>>() {
		Consumer<List<CacheEntry>> consumer2 = t -> {
        };
//		Consumer<PersonalityEvent> consumer1 = new Consumer<PersonalityEvent>() {
		Consumer<PersonalityEvent> consumer1 = t -> {
        };

		Channel<PersonalityEvent> channelMap = mock(Channel.class);
		when(channelMap.getEventMap()).thenReturn(new ConcurrentHashMap<>());
		changeNotChaCont.setChannel(channelMap);

		extnCacheOperationSpy.setChangeNotificationChannelController(changeNotChaCont);
		extnCacheOperationSpy.setConsumer(consumer1);
		extnCacheOperationSpy.setDistributedChannelController(distChanlController);
		distChanlController.setChannelMap(channelMap2);
		extnCacheOperationSpy.setPersonalityCacheFacade(perCacheFacade);
		Channel<CacheEntry> channel = new Channel<>() {
            @Override
            public void registerBatchConsumer(Consumer<List<CacheEntry>> consumer, String name) {
            }
        };
		channelMap2.put(EventType.UPDATE, channel);
		channelMap2.put(EventType.INSERT, channel);
		channelMap2.put(EventType.DELETE, channel);
		extnCacheOperationSpy.setConsumerListPut(consumer2);
		extnCacheOperationSpy.setConsumerListEvict(consumer2);
		KronosPropertiesFacade kronosPropertiesFacade = mock(KronosPropertiesFacade.class);
		when(kronosPropertiesFacade.getIntegerKronosProperty(anyString(), anyInt())).thenReturn(Integer.valueOf(1));
		extnCacheOperationSpy.setKronosPropertiesFacade(kronosPropertiesFacade );
		
		extnCacheOperationSpy.init();
		verify(changeNotChaCont, times(1)).registerListener(consumer1, "PersonalityEventConsumer0");
		verify(distChanlController, times(3)).registerListener(any(), any(), any());

	}

	@Test
	public void cleanUpTest() {

		when(kronosPropertiesFacade.getIntegerKronosProperty(any(), anyInt())).thenReturn(100);
		ChangeNotificationChannelController changeNotChaCont = spy(ChangeNotificationChannelController.class);
		DistributedCacheChannelController distChanlController = mock(DistributedCacheChannelController.class);
		PersonalityCacheFacade perCacheFacade = mock(PersonalityCacheFacade.class);
		Map<EventType, Channel<CacheEntry>> channelMap2 = new HashMap<>();

		Consumer<PersonalityEvent> consumer1 = personalityEvent -> {};

		AtomicBoolean concurCalled = new AtomicBoolean(false);
		ConcurrencyHelper concurrencyHelper = new ConcurrencyHelper(null,kronosPropertiesFacade,null) {
			@Override
			public void shutDownExecutor(ExecutorService executor) {
				concurCalled.set(true);
			}
		};

		Channel<PersonalityEvent> channelMap = mock(Channel.class);
		changeNotChaCont.setChannel(channelMap);

		extnCacheOperationSpy.concurrencyHelper = concurrencyHelper;
		extnCacheOperationSpy.setChangeNotificationChannelController(changeNotChaCont);
		extnCacheOperationSpy.setConsumer(consumer1);
		extnCacheOperationSpy.setDistributedChannelController(distChanlController);
		distChanlController.setChannelMap(channelMap2);
		extnCacheOperationSpy.setPersonalityCacheFacade(perCacheFacade);
//		Channel<CacheEntry> channel = new Channel<CacheEntry>() {
		Channel<CacheEntry> channel = new Channel<>() {
            @Override
            public void registerBatchConsumer(Consumer<List<CacheEntry>> consumer, String name) {
            }
        };
		channelMap2.put(EventType.UPDATE, channel);
		channelMap2.put(EventType.INSERT, channel);
		channelMap2.put(EventType.DELETE, channel);
		KronosPropertiesFacade kronosPropertiesFacade = mock(KronosPropertiesFacade.class);
//		when(kronosPropertiesFacade.getIntegerKronosProperty(anyString(), anyInt())).thenReturn(new Integer(1));
		when(kronosPropertiesFacade.getIntegerKronosProperty(anyString(), anyInt())).thenReturn(1);
		extnCacheOperationSpy.setKronosPropertiesFacade(kronosPropertiesFacade );
		extnCacheOperationSpy.cleanUp();
		verify(changeNotChaCont, times(1)).unregister("PersonalityEventConsumer0");
	}
	
	@SuppressWarnings("unchecked")
	@Test
	public void testConsumerForInsertEvent(){
		StringBuilder sb = new StringBuilder();
		Map<String, BaseExtension> mockExtensions = mock(Map.class);
		CacheEntry mockCacheEntry = mock(CacheEntry.class);
		PersonalityEvent mockPersonalityEvent = mock(PersonalityEvent.class);
		Personality personality = mock(Personality.class);
		when(personality.getPersonId()).thenReturn(mock(ObjectIdLong.class));
		when(mockPersonalityEvent.getPersonality()).thenReturn(personality);
		when(mockPersonalityEvent.getTenantId()).thenReturn("test_non_prd_01");
		extnCacheOperationSpy = new ExtensionCacheOperations() {
			@Override
			public Map<String, BaseExtension> buildExtensions(PersonalityEvent t) {
				sb.append("\n").append("buildExtensions is called with personalityEvent ").append(t).append("\n");
				return mockExtensions;
			}
			
			@Override
			protected CacheEntry buildCacheEntry(
					PersonalityEvent personalityEvent) {
				sb.append("\n").append("buildCacheEntry called").append("\n");
				return mockCacheEntry;
			}
			
			@Override
			public void sendToChannel(PersonalityEvent t,
					Map<String, BaseExtension> extensionsMap) {
				sb.append("\nsendToChannel called\n");
				if (t.equals(mockPersonalityEvent)) {
					sb.append("\npersonalityEventMatched\n");
				}
				if (extensionsMap.equals(mockExtensions)) {
					sb.append("\nextensionMapMatched\n");
				}
			}
		};
		DistributedCacheChannelController distributedChannelController = new DistributedCacheChannelController() {
			@Override
			public void notify(EventType eventType, CacheEntry entry) {
				sb.append("\nEventType ").append(eventType.name()).append("\n");
				if (entry == mockCacheEntry)sb.append("\nCacheEntry mached\n");
			}
		};
		extnCacheOperationSpy.setDistributedChannelController(distributedChannelController);
		TenantHandlingFacade tenantFacade = new TenantHandlingFacade() {
			@Override
			public void setTenantId(String tenantId) {
				sb.append("\nsetTenantId is called with ").append(tenantId).append("\n");
			}
			@Override
			public void removeTenantId() {
				sb.append("\nremoveTenantId is called with ").append("\n");
			}
		};
		extnCacheOperationSpy.tenantFacade = tenantFacade;

		Consumer<PersonalityEvent> consumerPer = extnCacheOperationSpy.consumer;
		PersonalityCacheAccessor personalityCacheAccessorMock = mock(PersonalityCacheAccessor.class);
		extnCacheOperationSpy.personalityCacheAccessor=personalityCacheAccessorMock;
		when(personalityCacheAccessorMock.getAllExtensionsByPersonId(Mockito.any())).thenReturn(null);
		ChangeNotificationChannelController changeNotChaCont = spy(ChangeNotificationChannelController.class);
		Channel<PersonalityEvent> channelMap = mock(Channel.class);
		when(channelMap.getEventMap()).thenReturn(new ConcurrentHashMap<>());
		changeNotChaCont.setChannel(channelMap);
		extnCacheOperationSpy.setChangeNotificationChannelController(changeNotChaCont);
		extensionBuilder.cacheUpdater = mock(ExtensionCacheUpdaterImpl.class);
		KronosPropertiesFacade kronosPropertiesFacade = mock(KronosPropertiesFacade.class);
		extnCacheOperationSpy.setKronosPropertiesFacade(kronosPropertiesFacade );
		consumerPer.accept(mockPersonalityEvent);
		
		
		String recordings  = sb.toString();
		assertTrue(recordings.contains("buildExtensions is called with personalityEvent"), "Build extensions not ");
		assertTrue(recordings.contains("\nsendToChannel called\n"), "Send to channel not called");
		assertTrue(recordings.contains("\npersonalityEventMatched\n"));
		assertTrue(recordings.contains("\nsetTenantId is called with "));
		assertTrue(recordings.contains("\nextensionMapMatched\n"));
		
		assertFalse(recordings.contains("\nCacheEntry mached\n"));
		assertFalse(recordings.contains("\nEventType DELETE"));
		
		sb.delete(0, sb.length());
		when(mockPersonalityEvent.getEventType()).thenReturn(EventType.DELETE);
		consumerPer.accept(mockPersonalityEvent);
		recordings = sb.toString();
		assertTrue(recordings.contains("\nCacheEntry mached\n"));
		assertTrue(recordings.contains("\nEventType DELETE"));
	}
	
//	@Ignore
	@Disabled
	@Test
	public void testConsumerForDeleteEvent(){
		AdapterHelper adapterHelperMock = mock(AdapterHelper.class);
		extnCacheOperationSpy.setAdapterHelper(adapterHelperMock );
		PersonalityEvent personalityEventMock = mock(PersonalityEvent.class);
		when(personalityEventMock.getTenantId()).thenReturn("1");
		Personality personalityMock = mock(Personality.class);
		when(personalityEventMock.getPersonality()).thenReturn(personalityMock);
		when(personalityMock.getPersonId()).thenReturn(mock(ObjectIdLong.class));
		when(personalityEventMock.getEventType()).thenReturn(EventType.DELETE);
        Consumer<PersonalityEvent> consumerPer = extnCacheOperationSpy.consumer;
       DistributedCacheChannelController distributedChannelControllerMock = mock(DistributedCacheChannelController.class);
		
       //DistributedCacheChannelController distributedChannelControllerMock = new DistributedCacheChannelController();
       distributedChannelControllerMock = new DistributedCacheChannelController(){
			@Override
			public void notify(EventType eventType, CacheEntry entry) {}
		};
		extnCacheOperationSpy.setDistributedChannelController(distributedChannelControllerMock);
		PersonalityCacheAccessor personalityCacheAccessorMock = mock(PersonalityCacheAccessor.class);
		extnCacheOperationSpy.personalityCacheAccessor=personalityCacheAccessorMock;
		when(personalityCacheAccessorMock.getAllExtensionsByPersonId(Mockito.any())).thenReturn(null);
		ChangeNotificationChannelController changeNotChaCont = spy(ChangeNotificationChannelController.class);
		Channel<PersonalityEvent> channelMap = mock(Channel.class);
		when(channelMap.getEventMap()).thenReturn(new ConcurrentHashMap<>());
		changeNotChaCont.setChannel(channelMap);
		extnCacheOperationSpy.setChangeNotificationChannelController(changeNotChaCont);
		consumerPer.accept(personalityEventMock);
		
		verify(extnCacheOperationSpy, never()).buildExtensions(personalityEventMock);
		
	}
	
	@Test
	public void testCreateEntryForPersonality(){
		
		Personality personalityMock = mock(Personality.class);
		ExtensionBuilder extensionBuilderMock= mock(ExtensionBuilder.class, RETURNS_SMART_NULLS);
		extensionBuilderMock.buildExtensions(1L);
		
		CachingKeyHelper cachingKeyHelperMock= mock(CachingKeyHelper.class, RETURNS_SMART_NULLS);
		cachingKeyHelperMock.generatePutKey(anyMap());
		extnCacheOperationSpy.setCachingKeyHelper(cachingKeyHelperMock);
		extnCacheOperationSpy.extensionBuilder = extensionBuilderMock;
		
		CacheEntry ce = extnCacheOperationSpy.createEntryForPersonality(personalityMock , "1");
		assertNotNull(ce);
		System.out.println("DD");
	}
	
	@Test
	public void testConsumerForInsertForTenantTest() {
		Personality personality = mock(Personality.class);
		when(personality.getPersonId()).thenReturn(new ObjectIdLong(-2));
		extensionBuilder.cacheUpdater = mock(ExtensionCacheUpdaterImpl.class);
		ChangeNotificationChannelController changeNotChaCont = spy(ChangeNotificationChannelController.class);
		Channel<PersonalityEvent> channelMap = mock(Channel.class);
		when(channelMap.getEventMap()).thenReturn(new ConcurrentHashMap<>());
		changeNotChaCont.setChannel(channelMap);
		extnCacheOperationSpy.setChangeNotificationChannelController(changeNotChaCont);
		PersonalityEvent pe = new PersonalityEvent(EventType.INSERT, personality, "MyTenantWhileTesting");
		extnCacheOperationSpy.tenantFacade = new TenantHandlingFacade();
		extnCacheOperationSpy.tenantFacade.setTenantProvider(new MockTenantProvider());
		extnCacheOperationSpy.consumer.accept(pe);
		//assertEquals("MyTenantWhileTesting", extnCacheOperationSpy.tenantFacade.getTenantId());
		assertNull(extnCacheOperationSpy.tenantFacade.getTenantId());
	}
	
	

}
