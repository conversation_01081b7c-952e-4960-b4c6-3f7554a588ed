package com.kronos.people.personality.service.impl;

import com.kronos.wfc.commonapp.accessgrp.business.DataAccessGroup;
import com.kronos.wfc.commonapp.accessgrp.business.DataAccessGroupCache;
import com.kronos.wfc.commonapp.hyperfind.business.profile.LightWeightQuery;
import com.kronos.wfc.commonapp.hyperfind.business.profile.LightWeightQueryCache;
import com.kronos.wfc.commonapp.localepolicy.bridge.ILocalePolicyAccessorService;
import com.kronos.wfc.commonapp.localepolicy.bridge.LocalePolicy;
import com.kronos.wfc.commonapp.people.business.person.delegation.DelegateProfile;
import com.kronos.wfc.commonapp.types.business.AnalyticsLaborType;
import com.kronos.wfc.commonapp.types.business.LogonProfile;
import com.kronos.wfc.commonapp.types.business.TimeEntryType;
import com.kronos.wfc.commonapp.types.business.TimeEntryTypeCache;
import com.kronos.wfc.platform.notification.framework.NotificationProfile;
import com.kronos.wfc.platform.notification.framework.NotificationProfileCache;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

//@RunWith(PowerMockRunner.class)
//@PowerMockIgnore("javax.management.*")
//@PrepareForTest({ DataAccessGroupCache.class, DataAccessGroup.class, AnalyticsLaborType.class, NotificationProfileCache.class,
//	DelegateProfile.class ,LightWeightQueryCache.class, TimeEntryTypeCache.class, TimeEntryTypeCache.class })
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonalityAttributeNameResolverTest {

	@InjectMocks
	PersonalityAttributeNameResolver personalityAttributeNameResolver;
	@Mock
	DelegateProfile delegateProfile;
	@Mock
	ILocalePolicyAccessorService localePolicyAccessorService;
	@Mock
	TimeEntryType timeEntryType;

	private MockedStatic<DataAccessGroupCache> mockedDataAccessGroupCache;
	private MockedStatic<NotificationProfileCache> mockedNotificationProfileCache;
	private MockedStatic<DataAccessGroup> mockedDataAccessGroup;
	private MockedStatic<AnalyticsLaborType> mockedAnalyticsLaborType;
	private MockedStatic<DelegateProfile> mockedDelegateProfile;
	private MockedStatic<LightWeightQueryCache> mockedLightWeightQueryCache;
	private MockedStatic<TimeEntryTypeCache> mockedTimeEntryTypeCache;


	private static final String ANALYTIC_LABOR_TYPE_FT = "Full Time";
//	private static final Long ANALYTIC_LABOR_TYPE_ID_FT = -10l;
	private static final Long ANALYTIC_LABOR_TYPE_ID_FT = -10L;
	private static final String ANALYTIC_LABOR_TYPE_PT = "Part Time";
//	private static final Long ANALYTIC_LABOR_TYPE_ID_PT = -11l;
	private static final Long ANALYTIC_LABOR_TYPE_ID_PT = -11L;
//	private static final Long ANALYTIC_LABOR_TYPE_ID_NF = -9l;
	private static final Long ANALYTIC_LABOR_TYPE_ID_NF = -9L;
//	private static final Long HF_Q_ID_1 = 1l;
	private static final Long HF_Q_ID_1 = 1L;
//	private static final Long HF_Q_ID_2 = 2l;
	private static final Long HF_Q_ID_2 = 2L;
	private static final String HF_Q_NAME_1 = "HFQ1";
	private static final String HF_Q_NAME_2 = "HFQ2";
	private static final String DEFAULT_NAME = "name";


	@BeforeEach
	public void setup() {
		mockedDataAccessGroupCache = Mockito.mockStatic(DataAccessGroupCache.class);
		mockedNotificationProfileCache = Mockito.mockStatic(NotificationProfileCache.class);
		mockedDataAccessGroup = Mockito.mockStatic(DataAccessGroup.class);
		mockedAnalyticsLaborType = Mockito.mockStatic(AnalyticsLaborType.class);
		mockedDelegateProfile = Mockito.mockStatic(DelegateProfile.class);
		mockedLightWeightQueryCache = Mockito.mockStatic(LightWeightQueryCache.class);
		mockedTimeEntryTypeCache = Mockito.mockStatic(TimeEntryTypeCache.class);
	}

	@AfterEach
	public void tearDown() {
		mockedDataAccessGroupCache.close();
		mockedNotificationProfileCache.close();
		mockedDataAccessGroup.close();
		mockedAnalyticsLaborType.close();
		mockedDelegateProfile.close();
		mockedLightWeightQueryCache.close();
		mockedTimeEntryTypeCache.close();
	}


	@Disabled //com.kronos.wfc.platform.persistence.framework.PersistenceException: null
	@Test
	public void testGetGDAPProfileName() {
		try (MockedStatic<LogonProfile> mockedLogonProfile = Mockito.mockStatic(LogonProfile.class)) {
			DataAccessGroup dag = new DataAccessGroup("Payroll Manager", "Payroll manager");
			when(DataAccessGroupCache.getDataAccessGroup(any())).thenReturn(dag);
			String name = personalityAttributeNameResolver.getGDAPProfileName(131L);
			assertEquals("Payroll Manager", name);
			when(DataAccessGroupCache.getDataAccessGroup(any())).thenReturn(null);
			String profileName = personalityAttributeNameResolver.getGDAPProfileName(131L);
			assertNull(profileName);
		}
	}

	@Test
	public void testGetAnalyticsLaborTypesName() {
		AnalyticsLaborType type1 = new AnalyticsLaborType();
		type1.setAnalyticsLaborType(ANALYTIC_LABOR_TYPE_ID_FT, ANALYTIC_LABOR_TYPE_FT);
		AnalyticsLaborType type2 = new AnalyticsLaborType();
		type2.setAnalyticsLaborType(ANALYTIC_LABOR_TYPE_ID_PT, ANALYTIC_LABOR_TYPE_PT);

		assertMockAnalyticLaborType(new ObjectIdLong(ANALYTIC_LABOR_TYPE_ID_FT), type1);
		assertMockAnalyticLaborType(new ObjectIdLong(ANALYTIC_LABOR_TYPE_ID_PT), type2);

		String analyticLaborTypeFTName = personalityAttributeNameResolver
				.getAnalyticsLaborTypeName(ANALYTIC_LABOR_TYPE_ID_FT);

		String analyticLaborTypePTName = personalityAttributeNameResolver
				.getAnalyticsLaborTypeName(ANALYTIC_LABOR_TYPE_ID_PT);

		String analyticLaborTypeNFName = personalityAttributeNameResolver
				.getAnalyticsLaborTypeName(ANALYTIC_LABOR_TYPE_ID_NF);

		assertEquals(ANALYTIC_LABOR_TYPE_FT, analyticLaborTypeFTName);
		assertEquals(ANALYTIC_LABOR_TYPE_PT, analyticLaborTypePTName);
		assertNull(analyticLaborTypeNFName);
	}

	@Test
	public void testGetHyperFindProfiles() {
		when(LightWeightQueryCache.getLightWeightQueries()).thenReturn(new ArrayList<>());
		Map<Long,String> hyperfindProfiles =  personalityAttributeNameResolver
				.getHyperFindProfiles();
		assertTrue(hyperfindProfiles.isEmpty());
		List<LightWeightQuery> lightWeightQueryList = new ArrayList<>();
		lightWeightQueryList.add(getLightWeightQuery(HF_Q_ID_1, HF_Q_NAME_1));
		lightWeightQueryList.add(getLightWeightQuery(HF_Q_ID_2, HF_Q_NAME_2));
		when(LightWeightQueryCache.getLightWeightQueries()).thenReturn(lightWeightQueryList);
		hyperfindProfiles =  personalityAttributeNameResolver
				.getHyperFindProfiles();
		assertEquals(HF_Q_NAME_1, hyperfindProfiles.get(HF_Q_ID_1));
		assertEquals(HF_Q_NAME_2, hyperfindProfiles.get(HF_Q_ID_2));

	}

	@Test
	public void testGetDelegateProfileNameWhenObject() {
		when(DelegateProfile.getDelegateProfile(any(ObjectIdLong.class))).thenReturn(delegateProfile);
		when(delegateProfile.getName()).thenReturn("test");
		String name =personalityAttributeNameResolver.getDelegateProfileName(10L);
		assertEquals("test", name);
	}

	@Test
	public void testGetDelegateProfileNameWhenObjectIsNullInCache() {
		when(DelegateProfile.getDelegateProfile(any(ObjectIdLong.class))).thenReturn(null);
		when(delegateProfile.getName()).thenReturn("test");
		String name =personalityAttributeNameResolver.getDelegateProfileName(10L);
		assertNull(name);
	}

	@Test
	public void testGetDelegateProfileNameWhenIdPassedIsNull() {
		when(DelegateProfile.getDelegateProfile((ObjectIdLong)null)).thenReturn(null);
		when(delegateProfile.getName()).thenReturn("test");
		String name =personalityAttributeNameResolver.getDelegateProfileName(10L);
		assertNull(name);
	}

	@Test
	public void testGetNotificationProfileWhenObjectIsNullInCache() {
//		PowerMockito.mockStatic(NotificationProfileCache.class);
//		when(NotificationProfileCache.getAllNotificationProfiles()).thenReturn(Collections.EMPTY_LIST);
		mockedNotificationProfileCache.when(NotificationProfileCache::getAllNotificationProfiles).thenReturn(Collections.emptyList());
		Map<Long, String> notificationProfileMap=personalityAttributeNameResolver.getAllNotificationProfiles();
		assertEquals(Collections.emptyMap(),notificationProfileMap);
	}

	@Test
	public void testGetNotificationProfile() {
//		PowerMockito.mockStatic(NotificationProfileCache.class);
//		when(NotificationProfileCache.getAllNotificationProfiles()).thenReturn(getAllNotificationProfile());
		mockedNotificationProfileCache.when(NotificationProfileCache::getAllNotificationProfiles).thenReturn(getAllNotificationProfile());		Map<Long, String> notificationProfileMap=personalityAttributeNameResolver.getAllNotificationProfiles();
		assertNotNull(notificationProfileMap);
		assertTrue(notificationProfileMap.containsKey(1L));
	}

	private  List<NotificationProfile> getAllNotificationProfile(){
		NotificationProfile notificationProfile=new NotificationProfile();
		List<NotificationProfile> notificationProfiles=new ArrayList<>();
		notificationProfile.setId(new ObjectIdLong(1L));
		notificationProfiles.add(notificationProfile);
		return notificationProfiles;
	}

	@Test
	public void testGetLocalePolicy() {
		LocalePolicy localePolicy = new LocalePolicy();
		localePolicy.setName("name");
		localePolicy.setId(1L);
		List<LocalePolicy> list = List.of(localePolicy);
//		PowerMockito.when(localePolicyAccessorService.findAll()).thenReturn(list);
		when(localePolicyAccessorService.findAll()).thenReturn(list);
		Map<Long,String>localePolicyMap = personalityAttributeNameResolver.getLocalePolicies();
		assertNotNull(localePolicyMap);
		assertEquals("name", localePolicyMap.get(1L));
	}

	@Test
	public void testGetLocalePolicyNull() {
//		PowerMockito.when(localePolicyAccessorService.findAll()).thenReturn(null);
		when(localePolicyAccessorService.findAll()).thenReturn(null);
		Map<Long,String>localePolicyMap = personalityAttributeNameResolver.getLocalePolicies();
		assertNotNull(localePolicyMap);
		assertEquals(0, localePolicyMap.size());
	}

	private LightWeightQuery getLightWeightQuery(Long queryId,String queryName) {
		ObjectIdLong queryObjectId= new ObjectIdLong(queryId);
		return new LightWeightQuery(queryObjectId, queryName, queryName, queryObjectId, queryId);
	}

	private void assertMockAnalyticLaborType(ObjectIdLong id, AnalyticsLaborType type) {
		when(AnalyticsLaborType.getAnalyticsLaborType(id)).thenReturn(type);
	}

    @Test
    public void getTimeEntryName() {
//        PowerMockito.mockStatic(TimeEntryTypeCache.class);
        Mockito.when(timeEntryType.getName()).thenReturn(DEFAULT_NAME);

//        PowerMockito.when(TimeEntryTypeCache.getTimeEntryType(new ObjectIdLong(1L))).thenReturn(timeEntryType);
		mockedTimeEntryTypeCache.when(() -> TimeEntryTypeCache.getTimeEntryType(new ObjectIdLong(1L))).thenReturn(timeEntryType);

        String timeEntryTypeName = personalityAttributeNameResolver.getTimeEntryName(1L);
        assertNotNull(timeEntryTypeName);
        assertEquals(DEFAULT_NAME, timeEntryTypeName);
    }

	@Test
	public void getTimeEntryNameReturnNull() {
//		PowerMockito.mockStatic(TimeEntryTypeCache.class);
//		PowerMockito.when(TimeEntryTypeCache.getTimeEntryType(new ObjectIdLong(1L))).thenReturn(null);
		mockedTimeEntryTypeCache.when(() -> TimeEntryTypeCache.getTimeEntryType(new ObjectIdLong(1L))).thenReturn(null);
		String timeEntryTypeName = personalityAttributeNameResolver.getTimeEntryName(1L);
		assertNull(timeEntryTypeName);
	}
}
