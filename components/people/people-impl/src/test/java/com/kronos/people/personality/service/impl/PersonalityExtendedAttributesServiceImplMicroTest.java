package com.kronos.people.personality.service.impl;

import com.kronos.commonapp.hfsecuritymask.api.ICombinedHomeAccountService;
import com.kronos.commonapp.hfsecuritymask.api.IPositionHomeAccountService;
import com.kronos.commonapp.hfsecuritymask.model.CombinedHomeAccount;
import com.kronos.commonapp.hfsecuritymask.model.position.PositionHomeAccount;
import com.kronos.commonapp.laborcategory.laboraccount.model.LaborAccount;
import com.kronos.datacollection.udm.service.devicegroup.api.dto.DeviceGroup;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacade;
import com.kronos.people.personality.dataaccess.legacy.PersonalityFacadeForCachedAttributes;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.entry.CostCenterEntry;
import com.kronos.wfc.commonapp.employment.business.terms.EmploymentTerm;
import com.kronos.wfc.commonapp.people.bridge.IOrgMapAccessorService;
import com.kronos.wfc.commonapp.people.bridge.OrgSet;
import com.kronos.wfc.commonapp.processmanager.business.profiles.workflow.WorkflowProfile;
import com.kronos.wfc.commonapp.profiles.framework.Profile;
import com.kronos.wfc.commonapp.requestreviewers.business.RequestPurpose;
import com.kronos.wfc.commonapp.requestreviewers.business.RequestReviewerList;
import com.kronos.wfc.commonapp.rules.business.PayRule;
import com.kronos.wfc.commonapp.types.business.*;
import com.kronos.wfc.platform.member.framework.ObjectIdLongMember;
import com.kronos.wfc.platform.member.framework.ObjectIdLongProperties;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.business.authentication.types.AuthenticationType;
import com.kronos.wfc.scheduling.core.business.profiles.groups.ScheduleGroupProfile;
import com.kronos.wfc.scheduling.core.business.profiles.patterns.SchedulePatternProfile;
import com.kronos.wfc.scheduling.core.business.profiles.shifttemplates.ShiftTemplateProfile;
import com.kronos.wfc.timekeeping.accruals.business.AccrualProfile;
import com.kronos.wfc.timekeeping.wages.business.WageProfile;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

//@RunWith(PowerMockRunner.class)
//@PrepareForTest({LogonProfile.class, SchedulePatternProfile.class, ScheduleGroupProfile.class, ShiftTemplateProfile.class})
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonalityExtendedAttributesServiceImplMicroTest {
	
	@Mock
	PersonalityFacadeForCachedAttributes personalityServiceLegacyHelper;
	
	@InjectMocks
	PersonalityExtendedAttributesServiceImpl personalityExtendedService;
	
	@Mock
	PersonalityFacade personalityFacade;
	
	@Mock 
	PersonalityServiceImpl personalityServiceImpl;
	
	@Mock
	LogonProfile logonProfile;
	
	@Mock
	LogonProfileCache logonProfileCache;

	@Mock
	ScheduleGroupProfile scheduleGroupProfile;
	
	@Mock
	IPositionHomeAccountService positionHomeAccountService;

	@Mock
	ICombinedHomeAccountService combinedHomeAccountService;

	private MockedStatic<SchedulePatternProfile> mockedSchedulePatternProfile;
	private MockedStatic<ScheduleGroupProfile> mockedScheduleGroupProfile;
	private MockedStatic<ShiftTemplateProfile> mockedShiftTemplateProfile;


		
	AdapterHelper adapeterHelper = new AdapterHelper();
	
	static List<Long> idList = Arrays.asList(1L, 2L);
	private final static String MANAGER_SCHEDULE_GROUP_NAME = "shift group";
	private final static String MANAGER_SHIFT_TEMPLATE_PROFILE_NAME = "Shift Template";

	LocalDate effectiveDate=LocalDate.of(2020, 12, 1);

	LocalDate expirationDate=LocalDate.of(2021, 12, 1);

	@BeforeEach
	public void setUp(){
		personalityExtendedService.personalityFacadeForCachedAttributes = personalityServiceLegacyHelper;
		personalityExtendedService.personalityfacade = personalityFacade;
		personalityExtendedService.adapterHelper = adapeterHelper;
		personalityExtendedService.personalityServiceImpl=personalityServiceImpl;
		personalityExtendedService.positionHomeAccountService=positionHomeAccountService;
		personalityExtendedService.combinedHomeAccountService=combinedHomeAccountService;

		mockedScheduleGroupProfile = Mockito.mockStatic(ScheduleGroupProfile.class);
		mockedShiftTemplateProfile = Mockito.mockStatic(ShiftTemplateProfile.class);
		mockedSchedulePatternProfile = Mockito.mockStatic(SchedulePatternProfile.class);



	}

	@AfterEach
	public void tearDown(){
		mockedScheduleGroupProfile.close();
		mockedShiftTemplateProfile.close();
		mockedSchedulePatternProfile.close();
	}

	@Test
	public void testGetJobTransferSet(){
		IOrgMapAccessorService orgMapAccessService = Mockito.mock(IOrgMapAccessorService.class);
		OrgSet orgSet = Mockito.mock(OrgSet.class); 
		when(orgSet.getName()).thenReturn("value");
		when(orgMapAccessService.findOrgMapGroupById(any())).thenReturn(orgSet);
		when(personalityFacade.getOrgMapAccessorService()).thenReturn(orgMapAccessService);
		String output = personalityExtendedService.getJobTransferSet(1L);
		assertEquals("value", output);
	}
	
	@Test
	public void testGetJobTransferSetNullId(){
		IOrgMapAccessorService orgMapAccessService = Mockito.mock(IOrgMapAccessorService.class);
		OrgSet orgSet = Mockito.mock(OrgSet.class); 
		when(orgSet.getName()).thenReturn("value");
		when(orgMapAccessService.findOrgMapGroupById(any())).thenReturn(orgSet);
		when(personalityFacade.getOrgMapAccessorService()).thenReturn(orgMapAccessService);
		Long id = null;
//		assertNull("value", personalityExtendedService.getJobTransferSet(id));
		assertNull(personalityExtendedService.getJobTransferSet(id));
	}
	
	@Test
	public void testGetJobTransferSetFromList(){
		PersonalityExtendedAttributesServiceImpl personalityExtendedService = new PersonalityExtendedAttributesServiceImpl(){
			@Override
			public String getJobTransferSet(Long id) {
				return "value";
			}
		};
		Map<Long, String> output = personalityExtendedService.getJobTransferSet(idList);
		assertEquals(2, output.size());
	}
	
	@Test
	public void testGetJobTransferSetFromListNullCases(){
		PersonalityExtendedAttributesServiceImpl personalityExtendedService = new PersonalityExtendedAttributesServiceImpl(){
			@Override
			public String getJobTransferSet(Long id) {
				return "value";
			}
		};
		List<Long> jobIds = null;
		Map<Long, String> output = personalityExtendedService.getJobTransferSet(jobIds);
		assertEquals(0, output.size());
		List<Long> jobIds2 = new ArrayList<>();
		jobIds2.add(null);
		jobIds2.add(null);
		Map<Long, String> output2 = personalityExtendedService.getJobTransferSet(jobIds2);
		assertEquals(1, output2.size());
		assertNull(output2.keySet().iterator().next());
	}
	
	@Test
	public void testGetWorkerType() {
		WorkerType workerType = Mockito.mock(WorkerType.class);
		when(personalityServiceLegacyHelper.getWorkerTypeFromId(any())).thenReturn(workerType);
		when(workerType.getName()).thenReturn("name");
		String output = personalityExtendedService.getWorkerType(1L);
		assertEquals("name", output);
	}
	
	@Test
	public void testGetWorkerTypeNull() {
		WorkerType workerType = Mockito.mock(WorkerType.class);
		when(personalityServiceLegacyHelper.getWorkerTypeFromId(any())).thenReturn(null);
		when(workerType.getName()).thenReturn("name");
		Long id = null;
		assertNull(personalityExtendedService.getWorkerType(id));
	}
	
	@Test
	public void testGetWorkerTypeFromList() {
		Map<Long, String> output = personalityExtendedService.getWorkerType(idList);
		assertEquals(2, output.size());
	}
	
	@Test
	public void testGetPayRule() {
		PayRule payRule = Mockito.mock(PayRule.class);
		when(payRule.getName()).thenReturn("payruleName");
		when(personalityServiceLegacyHelper.getPayRuleFromId(any())).thenReturn(payRule);
		String output = personalityExtendedService.getPayRule(1L);
		assertEquals("payruleName", output);
	}
	
	@Test
	public void testGetPayRuleNull() {
		PayRule payRule = Mockito.mock(PayRule.class);
		when(payRule.getName()).thenReturn("payruleName");
		when(personalityServiceLegacyHelper.getPayRuleFromId(any())).thenReturn(payRule);
		Long id = null;
		assertEquals("payruleName", personalityExtendedService.getPayRule(id));
	}
	
	@Test
	public void testGetPayRuleFromList() {
		Map<Long, String> output = personalityExtendedService.getPayRule(idList);
		assertEquals(2, output.size());
	}
	
	@Test
	public void testGetEmploymentTerms() {
		EmploymentTerm employmentTerm = Mockito.mock(EmploymentTerm.class);
		when(employmentTerm.getName()).thenReturn("value");
		when(personalityServiceLegacyHelper.getEmploymentTermFromId(any())).thenReturn(employmentTerm);
		String output = personalityExtendedService.getEmploymentTerms(1L); 
		assertEquals("value", output);
	}
	
	@Test
	public void testGetEmploymentTermsFromList() {
		EmploymentTerm e1 = Mockito.mock(EmploymentTerm.class);
		when(e1.getId()).thenReturn(new ObjectIdLong(1L));
		when(e1.getName()).thenReturn("name1");
		EmploymentTerm e2 = Mockito.mock(EmploymentTerm.class);
		when(e2.getId()).thenReturn(new ObjectIdLong(2L));
		when(e2.getName()).thenReturn("name2");
		List<EmploymentTerm> employmentTermList = Arrays.asList(e1,e2);
		when(personalityServiceLegacyHelper.getEmploymentTermFromIdList(anyList())).thenReturn(employmentTermList);
		Map<Long, String> output = personalityExtendedService.getEmploymentTerms(idList);
		assertEquals(2, output.size());
	}
		
	@Test
	public void testGetAccrualProfileName() {
		AccrualProfile aProfileMock = Mockito.mock(AccrualProfile.class);
		when(aProfileMock.getShortName()).thenReturn("accrualName");
		when(personalityServiceLegacyHelper.getAccrualProfileFromId(any())).thenReturn(aProfileMock);
		String output = personalityExtendedService.getAccrualProfileName(1L);
		assertEquals("accrualName", output);
		Long id = null;
		assertEquals("accrualName", personalityExtendedService.getAccrualProfileName(id));
		
	}

	@Test
	public void testGetAccrualProfileNameFromList() {
		Map<Long, String> output = personalityExtendedService.getAccrualProfileName(idList);
		assertEquals(2, output.size());
		List<Long> list = null;
		assertTrue(personalityExtendedService.getAccrualProfileName(list).isEmpty());
		List<Long> list2 = new ArrayList<>();
		list2.add(null);
		list2.add(null);
		assertTrue(personalityExtendedService.getAccrualProfileName(list).isEmpty());	
	}

	@Test
	public void testGetExpectedHrsName() {
		TimePeriodType tPeriod = Mockito.mock(TimePeriodType.class);
		when(tPeriod.getShortName()).thenReturn("value");
		when(personalityServiceLegacyHelper.getTimePeriodTypeFromId(any())).thenReturn(tPeriod);
		String output = personalityExtendedService.getExpectedHrsName(1L);
		assertEquals("value", output);
	}
	
	@Test
	public void testGetExpectedHrsNameNull() {
		TimePeriodType tPeriod = Mockito.mock(TimePeriodType.class);
		when(tPeriod.getShortName()).thenReturn("value");
		when(personalityServiceLegacyHelper.getTimePeriodTypeFromId(any())).thenReturn(tPeriod);
		Long id = null;
		assertEquals("value", personalityExtendedService.getExpectedHrsName(id));
	}

	@Test
	public void testGetExpectedHrsNameFromList() {
		Map<Long, String> output = personalityExtendedService.getExpectedHrsName(idList);
		assertEquals(2, output.size());
		List<Long> list = null;
		assertTrue(personalityExtendedService.getExpectedHrsName(list).isEmpty());
	}

	@Test
	public void testGetPurposeName() {
		RequestPurpose requestPurpose = Mockito.mock(RequestPurpose.class);
		when(requestPurpose.getName()).thenReturn("purposeName");
		when(personalityServiceLegacyHelper.getRequestPurposeFromId(any())).thenReturn(requestPurpose);
		String output = personalityExtendedService.getPurposeName(1L);
		assertEquals("purposeName", output);
	}

	@Test
	public void testGetPurposeNameFromList() {
		Map<Long, String> output = personalityExtendedService.getPurposeName(idList);
		assertEquals(2, output.size());
	}

	@Test
	public void testGetReviewerListName() {
		RequestReviewerList requestReviewerList = Mockito.mock(RequestReviewerList.class);
		when(requestReviewerList.fetchAssociatedName()).thenReturn("value");
		when(personalityServiceLegacyHelper.getRequestReviewerListFromId(any())).thenReturn(requestReviewerList);
		String output = personalityExtendedService.getReviewerListName(1L);
		assertEquals("value", output);
	}

	@Test
	public void testGetReviewerListNameFromList() {
		Map<Long, String> output = personalityExtendedService.getReviewerListName(idList);
		assertEquals(2, output.size());
	}

	@Test
	public void testGetWageProfileName() {
		WageProfile wageProfile = Mockito.mock(WageProfile.class);
		when(wageProfile.getName()).thenReturn("value");
		when(personalityServiceLegacyHelper.getWageProfileFromId(any())).thenReturn(wageProfile);
		String output = personalityExtendedService.getWageProfileName(1L);
		assertEquals("value", output);
	}
	
	@Test
	public void testGetWageProfileNameNull() {
		WageProfile wageProfile = Mockito.mock(WageProfile.class);
		when(wageProfile.getName()).thenReturn("value");
		when(personalityServiceLegacyHelper.getWageProfileFromId(any())).thenReturn(wageProfile);
		Long id = null;
		String output = personalityExtendedService.getWageProfileName(id);
		assertEquals("value", output);
	}

	@Test
	public void testGetWageProfileNameFromList() {
		Map<Long, String> output = personalityExtendedService.getWageProfileName(idList);
		assertEquals(2, output.size());
	}

	@Test
	public void testGetLogonProfileName() {
		LogonProfile logonProfile = Mockito.mock(LogonProfile.class);
		when(logonProfile.getName()).thenReturn("value");
		when(personalityServiceLegacyHelper.getLogonProfileFromId(any())).thenReturn(logonProfile);
		String output = personalityExtendedService.getLogonProfileName(1L);
		assertEquals("value", output);
	}

	@Test
	public void testGetLogonProfileNameFromList() {
		Map<Long, String> output = personalityExtendedService.getLogonProfileName(idList);
		assertEquals(2, output.size());
	}
	
	@Test
	public void testGetAuthenticationType(){
		AuthenticationType authType = Mockito.mock(AuthenticationType.class);
		when(authType.getName()).thenReturn("value");
		when(personalityServiceLegacyHelper.getAuthenticationType(any())).thenReturn(authType);
		String output = personalityExtendedService.getAuthenticationType(1L);
		assertEquals("value", output);
	}
	
	@Test
	public void testGetAuthenticationTypeFromList(){
		Map<Long, String> output = personalityExtendedService.getAuthenticationType(idList);
		assertEquals(2, output.size());
	}
	
	@Test
	public void getCostCenterEntryForAPositionTest(){
		List<PositionHomeAccount> positionHomeAccountList=new ArrayList<>();
		PositionHomeAccount positionHomeAccount =new PositionHomeAccount();
		LaborAccount account=new LaborAccount();
		account.setId(1L);
		account.setLlid7(1L);
		positionHomeAccount.setEffectiveDate(effectiveDate);
		positionHomeAccount.setExpirationDate(expirationDate);
		positionHomeAccount.setOrgNodeId(1008L);
		positionHomeAccount.setLaborAccount(account);
		positionHomeAccountList.add(positionHomeAccount);
		when(positionHomeAccountService.getHomeAccounts(153L, effectiveDate, expirationDate)).thenReturn(positionHomeAccountList);
		List<CostCenterEntry> costCenterEntryList=personalityExtendedService.getCostCentersForAPosition(153L, effectiveDate, expirationDate, 1008L);
		CostCenterEntry costCenterEntry=costCenterEntryList.iterator().next();
		assertEquals(positionHomeAccountList.get(0).getLaborAccount().getLlid7(), costCenterEntry.getCostCenterId());
	}


	@Test
	public void getCostCenterEntryForAEmployeeTest(){
		List<CombinedHomeAccount> combinedHomeAccountList=new ArrayList<>();
		CombinedHomeAccount combinedHomeAccount =new CombinedHomeAccount();
		LaborAccount account=new LaborAccount();
		account.setId(1L);
		account.setLlid7(1L);
		combinedHomeAccount.setEffectiveDate(effectiveDate);
		combinedHomeAccount.setExpirationDate(expirationDate);
		combinedHomeAccount.setOrgNodeId(1008L);
		combinedHomeAccount.setLaborAccount(account);
		combinedHomeAccountList.add(combinedHomeAccount);
		when(combinedHomeAccountService.getHomeAccounts(153L, effectiveDate, expirationDate)).thenReturn(combinedHomeAccountList);
		List<CostCenterEntry> costCenterEntryList=personalityExtendedService.getCostCentersForAPerson(153L, effectiveDate, expirationDate, 1008L);
		CostCenterEntry costCenterEntry=costCenterEntryList.iterator().next();
		assertEquals(combinedHomeAccountList.get(0).getLaborAccount().getLlid7(), costCenterEntry.getCostCenterId());
	}

	@Test
	public void testGetSuperviserFullName(){
		EmployeeExtension empExtension=Mockito.mock(EmployeeExtension.class);
		empExtension.setSupervisorFullName("Full Name");
		PersonalityResponse<EmployeeExtension> personalityResponse =Mockito.mock(PersonalityResponse.class);
		when(personalityResponse.isExceptionPresent()).thenReturn(false);
		when(personalityServiceImpl.findEmployeeExtension(anyLong())).thenReturn(personalityResponse);
		when(personalityResponse.getExtension()).thenReturn(empExtension);
		when(empExtension.getFullName()).thenReturn("Full Name");
		String output = personalityExtendedService.getFullName(1L);
		assertEquals("Full Name", output);
	}

	@Disabled //Cannot invoke "com.kronos.people.personality.model.PersonalityResponse.isExceptionPresent()" because "extension" is null
	@Test
	public void testGetSuperviserFullNameNull(){
		EmployeeExtension empExtension=Mockito.mock(EmployeeExtension.class);
		empExtension.setSupervisorFullName("Full Name");
		PersonalityResponse<EmployeeExtension> personalityResponse =Mockito.mock(PersonalityResponse.class);
		when(personalityResponse.isExceptionPresent()).thenReturn(false);
		when(personalityServiceImpl.findEmployeeExtension(anyLong())).thenReturn(personalityResponse);
		when(personalityResponse.getExtension()).thenReturn(empExtension);
		when(empExtension.getFullName()).thenReturn("Full Name");
		Long id = null;
		String output = personalityExtendedService.getFullName(id);
		assertEquals("Full Name", output);
	}
	
	@Test
	public void testGetSuperviserFullNameFromList(){
		EmployeeExtension empExtension=Mockito.mock(EmployeeExtension.class);
		empExtension.setSupervisorFullName("Full Name");
		PersonalityResponse<EmployeeExtension> personalityResponse =Mockito.mock(PersonalityResponse.class);
		when(personalityResponse.isExceptionPresent()).thenReturn(false);
		when(personalityServiceImpl.findEmployeeExtension(anyLong())).thenReturn(personalityResponse);
		when(personalityResponse.getExtension()).thenReturn(empExtension);
		when(empExtension.getFullName()).thenReturn("Full Name");
		Map<Long, String> output = personalityExtendedService.getFullName(idList);
		assertEquals(2, output.size());
	}
	
	
	@Test
	public void testGetDateName()
	{
		CustomDateType customDateType=Mockito.mock(CustomDateType.class);
		when(customDateType.getName()).thenReturn("Date Name");
		when(personalityServiceLegacyHelper.getCustomDateName(any())).thenReturn(customDateType);
		String output=personalityExtendedService.getDateName(1L);
		assertEquals("Date Name", output);
	}
	
	@Test
	public void testGetDateNameFromList()
	{
		Map<Long, String> output = personalityExtendedService.getDateName(idList);
		assertEquals(2, output.size());
	}
	
	@Test
	public void testGetProcessProfile(){
		WorkflowProfile workflowProfile =  Mockito.mock(WorkflowProfile.class);
		when(workflowProfile.getName()).thenReturn("processProfile");
		when(personalityServiceLegacyHelper.getProcessProfile(anyLong())).thenReturn(workflowProfile);
		String output = personalityExtendedService.getProcessProfile(1L);
		assertEquals("processProfile", output);
	}
	
	@Test
	public void testGetProcessProfileFromList(){
		Map<Long, String> output = personalityExtendedService.getProcessProfile(idList);
		assertEquals(2, output.size());
	}
	
	@Test
	public void testGetPrimaryJob(){
		Long id = 1L;
		when(personalityServiceLegacyHelper.getPrimaryJob(any(), any())).thenReturn("Sample");
		assertEquals("Sample", personalityExtendedService.getPrimaryJob(id, LocalDate.now()));
	}
	
	@Test
	public void testGetPrimaryJobNullId(){
		Long id = null;
		assertNull(personalityExtendedService.getPrimaryJob(id, LocalDate.now()));
	}
	
	@Test
	public void testGetPrimaryJobNullList(){
		List<Long> ids = null;
		assertTrue(personalityExtendedService.getPrimaryJob(ids, LocalDate.now()).isEmpty());
	}

	@Test
	public void testGetPrimaryJobNullDate(){
		assertNull(personalityExtendedService.getPrimaryJob(1L, null));
		Long id = null;
		assertNull(personalityExtendedService.getPrimaryJob(id, null));
		List<Long> ids = new ArrayList<>();
		ids.add(1L);
        assertEquals(1, personalityExtendedService.getPrimaryJob(ids, null).size());
		List<Long> ids2 = new ArrayList<>();
		ids2.add(null);
        assertEquals(1, personalityExtendedService.getPrimaryJob(ids2, null).size());
		List<Long> ids3 = null;
		assertTrue(personalityExtendedService.getPrimaryJob(ids3, null).isEmpty());

	}
	
	@Test
	public void testGetPrimaryJobNullIdsList(){
		List<Long> ids = new ArrayList<>();
		ids.add(null);
		ids.add(null);
		Map<Long, String> map = personalityExtendedService.getPrimaryJob(ids, LocalDate.now());
		assertNotNull(map);
		map.keySet().forEach(Assertions::assertNull);
	}
	
	@Test
	public void testGetDeviceGroupName(){
            when(personalityServiceLegacyHelper.getDeviceGroupNameFromId(1L)).thenReturn("TestDeviceGroup");
            String actual = personalityExtendedService.getDeviceGroupName(1L);
            assertEquals("TestDeviceGroup", actual);
        }

	@Test
	public void testGetDeviceGroupNameFromList() {
		List<Long> deviceGroupIds = Arrays.asList(1L, 3L);
		List<DeviceGroup> expected = deviceGroupIds.stream()
				.map(id -> new DeviceGroup() {
					@Override
					public long getId() {
						return id;
					}

					@Override
					public String getName() {
						return id.toString();
					}
				}).collect(Collectors.toList());
		when(personalityServiceLegacyHelper.getDeviceGroupsFromIds(deviceGroupIds)).thenReturn(expected);
		Map<Long, String> actual = personalityExtendedService.getDeviceGroupName(deviceGroupIds);
		assertNotNull(actual);
		assertEquals(expected.size(), actual.size());
//		assertTrue("Service should return the map with all the requested Device Group names",
//				actual.values().containsAll(expected.stream().map(DeviceGroup::getName).collect(Collectors.toList())));
		assertTrue(actual.values().containsAll(expected.stream().map(DeviceGroup::getName).toList()),
                () -> "Service should return the map with all the requested Device Group names");

		deviceGroupIds = Collections.emptyList();
		when(personalityServiceLegacyHelper.getDeviceGroupsFromIds(deviceGroupIds)).thenReturn(Collections.emptyList());
		actual = personalityExtendedService.getDeviceGroupName(deviceGroupIds);
		assertNotNull(actual);
		assertEquals(Collections.emptyMap(), actual);
	}
	
	@Test
        public void testGetDeviceProfileName(){
            when(personalityServiceLegacyHelper.getDeviceProfileNameFromId(6L)).thenReturn("TestDeviceProfile");
            String actual = personalityExtendedService.getDeviceProfileName(6L);
            assertEquals("TestDeviceProfile", actual);
        }
	
	@Test
        public void testGetDeviceProfileNameFromList(){
                when(personalityServiceLegacyHelper.getDeviceProfileNameFromId(1L)).thenReturn("TestDeviceProfile 1");
                when(personalityServiceLegacyHelper.getDeviceProfileNameFromId(2L)).thenReturn("TestDeviceProfile 2");
                Map<Long,String> actual = personalityExtendedService.getDeviceProfileName(idList);
                assertEquals(2, actual.size());
                assertEquals("TestDeviceProfile 1", actual.get(1L));
                assertEquals("TestDeviceProfile 2", actual.get(2L));
        }

	@Test
	public void testGetTTIPUserProfileName() {
		when(personalityServiceLegacyHelper.getTTIPUserProfileNameFromId(6L)).thenReturn("TestTTIPUserProfileName");
		String actual = personalityExtendedService.getTTIPUserProfileName(6L);
		assertEquals("TestTTIPUserProfileName", actual);
	}

	@Test
	public void testGetTTIPUserProfileNameFromList() {
		when(personalityServiceLegacyHelper.getTTIPUserProfileNameFromId(1L)).thenReturn("TestTTIPUserProfileName 1");
		when(personalityServiceLegacyHelper.getTTIPUserProfileNameFromId(2L)).thenReturn("TestTTIPUserProfileName 2");
		Map<Long, String> actual = personalityExtendedService.getTTIPUserProfileName(idList);
		assertEquals(2, actual.size());
		assertEquals("TestTTIPUserProfileName 1", actual.get(1L));
		assertEquals("TestTTIPUserProfileName 2", actual.get(2L));
	}


	@Test
        public void testIsFingerEnrolled(){
            when(personalityServiceLegacyHelper.isFingerEnrolled(1L)).thenReturn(Boolean.TRUE);
            when(personalityServiceLegacyHelper.isFingerEnrolled(2L)).thenReturn(Boolean.FALSE);
            assertTrue(personalityExtendedService.isFingerEnrolled(1L));
            assertFalse(personalityExtendedService.isFingerEnrolled(2L));
        }
	
	@Test
        public void testIsFingerEnrolledFromList(){
            when(personalityServiceLegacyHelper.isFingerEnrolled(1L)).thenReturn(Boolean.TRUE);
            when(personalityServiceLegacyHelper.isFingerEnrolled(2L)).thenReturn(Boolean.FALSE);
            Map<Long,Boolean> actual = personalityExtendedService.isFingerEnrolled(idList);
            assertEquals(2, actual.size());
            assertTrue(actual.get(1L));
            assertFalse(actual.get(2L));
        }
	
	@Test
        public void testIsFingerEnrolledForIdentification(){
            when(personalityServiceLegacyHelper.isFingerEnrolledForIdentification(1L)).thenReturn(Boolean.TRUE);
            when(personalityServiceLegacyHelper.isFingerEnrolledForIdentification(2L)).thenReturn(Boolean.FALSE);
            assertTrue(personalityExtendedService.isFingerEnrolledForIdentification(1L));
            assertFalse(personalityExtendedService.isFingerEnrolledForIdentification(2L));
        }
        
        @Test
        public void testIsFingerEnrolledForIdentificationFromList(){
            when(personalityServiceLegacyHelper.isFingerEnrolledForIdentification(1L)).thenReturn(Boolean.TRUE);
            when(personalityServiceLegacyHelper.isFingerEnrolledForIdentification(2L)).thenReturn(Boolean.FALSE);
            Map<Long,Boolean> actual = personalityExtendedService.isFingerEnrolledForIdentification(idList);
            assertEquals(2, actual.size());
            assertTrue(actual.get(1L));
            assertFalse(actual.get(2L));
        }
        
        @Test
        public void testGetPrimaryFingerThreshold(){
            when(personalityServiceLegacyHelper.getPrimaryFingerThreshold(1L)).thenReturn("High");
            String actual = personalityExtendedService.getPrimaryFingerThreshold(1L);
            assertEquals("High", actual);
        }
        
        @Test
        public void testGetPrimaryFingerThresholdFromList(){
            when(personalityServiceLegacyHelper.getPrimaryFingerThreshold(1L)).thenReturn("High");
            when(personalityServiceLegacyHelper.getPrimaryFingerThreshold(2L)).thenReturn("Low");
            Map<Long,String> actual = personalityExtendedService.getPrimaryFingerThreshold(idList);
            assertEquals(2, actual.size());
            assertEquals("High", actual.get(1L));
            assertEquals("Low", actual.get(2L));
        }
        
        @Test
        public void testGetPrimaryFingerEnrollmentLocation(){
            when(personalityServiceLegacyHelper.getPrimaryFingerEnrollmentLocation(1L)).thenReturn("My Device (025096)");
            String actual = personalityExtendedService.getPrimaryFingerEnrollmentLocation(1L);
            assertEquals("My Device (025096)", actual);
        }
        
        @Test
        public void testGetPrimaryFingerEnrollmentLocationFromList(){
            when(personalityServiceLegacyHelper.getPrimaryFingerEnrollmentLocation(1L)).thenReturn("My Device (025096)");
            when(personalityServiceLegacyHelper.getPrimaryFingerEnrollmentLocation(2L)).thenReturn("Your Device (037992)");
            Map<Long,String> actual = personalityExtendedService.getPrimaryFingerEnrollmentLocation(idList);
            assertEquals(2, actual.size());
            assertEquals("My Device (025096)", actual.get(1L));
            assertEquals("Your Device (037992)", actual.get(2L));
        }
        
       @Test
        public void testGetAllLoginProfilesWhenListIsEmpty(){
//    	   PowerMockito.mockStatic(LogonProfile.class);
//    	   PowerMockito.when(LogonProfile.getLogonProfiles()).thenReturn(logonProfileList);
//    	   Map<Long, String> map=personalityExtendedService.getAllLogonProfilesMap();
//    	   assertEquals(Collections.emptyMap(), map);
		   try (MockedStatic<LogonProfile> mockedLogonProfile = Mockito.mockStatic(LogonProfile.class)) {
			   List<LogonProfile> logonProfileList=new ArrayList<>();
			   mockedLogonProfile.when(LogonProfile::getLogonProfiles).thenReturn(logonProfileList);
			   Map<Long, String> map = personalityExtendedService.getAllLogonProfilesMap();
			   assertEquals(Collections.emptyMap(), map);
		   }
        }
       
       @Test
	   public void testGetAllLoginProfiles() {
		   try (MockedStatic<LogonProfile> mockedLogonProfile = Mockito.mockStatic(LogonProfile.class)) {
			   List<LogonProfile> logonProfileList = new ArrayList<>();
			   LogonProfile logonProfile = Mockito.mock(LogonProfile.class);
			   logonProfileList.add(logonProfile);

			   when(logonProfile.getName()).thenReturn("test");
			   when(logonProfile.getLogonProfileId()).thenReturn(new ObjectIdLong(1L));
			   mockedLogonProfile.when(LogonProfile::getLogonProfiles).thenReturn(logonProfileList);

			   Map<Long, String> map = personalityExtendedService.getAllLogonProfilesMap();
			   assertNotNull(map);
			   assertNotNull(map.get(1L));
			   assertEquals("test", map.get(1L));
		   }
	   }

    @Test
	public void testGetScheduleGroupProfileName() {
//		PowerMockito.mockStatic(ScheduleGroupProfile.class);

//		when(ScheduleGroupProfile.getScheduleGroupProfile(new ObjectIdLong(anyLong()))).thenReturn(scheduleGroupProfile);
		mockedScheduleGroupProfile.when(() -> ScheduleGroupProfile.getScheduleGroupProfile(new ObjectIdLong(1L))).thenReturn(scheduleGroupProfile);
//		when(scheduleGroupProfile.getName()).thenReturn(MANAGER_SCHEDULE_GROUP_NAME);
		when(scheduleGroupProfile.getName()).thenReturn(MANAGER_SCHEDULE_GROUP_NAME);
		assertEquals(MANAGER_SCHEDULE_GROUP_NAME, personalityExtendedService.getScheduleGroupProfileName(1L));
	}

	@Test
	public void getAllPatternTemplateProfiles() {
//		PowerMockito.mockStatic(SchedulePatternProfile.class);
		try {
			SchedulePatternProfile profile = new SchedulePatternProfile("pattern_template_profile", "description");
			Field field = Profile.class.getDeclaredField("profileId");
			field.setAccessible(true);
			field.set(profile, new ObjectIdLongMember(new ObjectIdLong(1), new ObjectIdLongProperties()));
			Mockito.when(SchedulePatternProfile.getSchedulePatternProfiles()).thenReturn(Arrays.asList(profile));
		} catch (NoSuchFieldException | IllegalAccessException e) {
//			assertTrue(false);
            fail();
		}
		Map<Long, String> result = personalityExtendedService.getAllPatternTemplateProfiles();
		assertNotNull(result);
		assertEquals("pattern_template_profile", result.get(1L));
	}

	@Test
	public void testGetShiftTemplateProfileName(){
//		PowerMockito.mockStatic(ShiftTemplateProfile.class);
		ShiftTemplateProfile stp = new ShiftTemplateProfile(MANAGER_SHIFT_TEMPLATE_PROFILE_NAME, MANAGER_SHIFT_TEMPLATE_PROFILE_NAME);
		when(ShiftTemplateProfile.getShiftTemplateProfile(new ObjectIdLong(1L))).thenReturn(stp);

		assertEquals(MANAGER_SHIFT_TEMPLATE_PROFILE_NAME, personalityExtendedService.getShiftTemplateProfileName(1L));

	}
}