package com.kronos.people.personality.notification.batch;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.notification.entry.CacheEntry;
import com.kronos.people.personality.tenant.TenantHandlingFacade;

import static org.junit.jupiter.api.Assertions.*;

public class PersonalityBatchProcessorMicroTest {

	PersonalityBatchProcessor personalityBatchProcessor = new PersonalityBatchProcessor();
	
	TenantHandlingFacade tenantFacade;
	
	@Test
	public void testAddToBatchMap(){
		personalityBatchProcessor.adapterHelper = new AdapterHelper();
		
		List<Long> personIds = new ArrayList<>();
		personIds.add(1L);
		personIds.add(2L);
		TenantHandlingFacade tenantFacade = Mockito.mock(TenantHandlingFacade.class);
		Mockito.when(tenantFacade.getTenantId()).thenReturn("manufacturing");
		personalityBatchProcessor.tenantFacade = tenantFacade;
		personalityBatchProcessor.addToBatchMap(personIds);
		assertEquals(2, personalityBatchProcessor.personalityBatchMap.get("manufacturing").size());
		
		List<Long> personIds2 = new ArrayList<>();
		personIds2.add(1L);
		personIds2.add(3L);
		personalityBatchProcessor.addToBatchMap(personIds2);
		assertEquals(3, personalityBatchProcessor.personalityBatchMap.get("manufacturing").size());
		
		TenantHandlingFacade tenantFacade2 = Mockito.mock(TenantHandlingFacade.class);
		Mockito.when(tenantFacade2.getTenantId()).thenReturn("healthcare");
		personalityBatchProcessor.tenantFacade = tenantFacade2;
		personalityBatchProcessor.addToBatchMap(null);
		assertNull(personalityBatchProcessor.personalityBatchMap.get("healthcare"));
	}
	
	@Test
	public void testIsPersonPresentForGivenTenantIdAndRemoveFromMap(){
		CacheEntry ce = new CacheEntry(1L, "combined");
		Set<Long> set = new HashSet<>();
		set.add(1L);
		set.add(2L);
		personalityBatchProcessor.personalityBatchMap.put("combined", set);
		assertTrue(personalityBatchProcessor.isPersonPresentForGivenTenantIdAndRemoveFromMap(ce));
		assertEquals(1, personalityBatchProcessor.personalityBatchMap.get("combined").size());
	}
	
	@Test
	public void testIsPersonPresentForGivenTenantIdAndRemoveFromMapEmptyEntry(){
		CacheEntry ce = null;
		Assertions.assertFalse(personalityBatchProcessor.isPersonPresentForGivenTenantIdAndRemoveFromMap(ce));
	}
	
	@Test
	public void testIsPersonPresentForGivenTenantIdAndRemoveFromMapNotInMap(){
		CacheEntry ce = new CacheEntry(1L, "combined");
		Set<Long> set = new HashSet<>();
		set.add(3L);
		set.add(2L);
		personalityBatchProcessor.personalityBatchMap.put("combined", set);
		assertFalse(personalityBatchProcessor.isPersonPresentForGivenTenantIdAndRemoveFromMap(ce));
		assertEquals(2, personalityBatchProcessor.personalityBatchMap.get("combined").size());
	}
	
	@Test
	public void testIsPersonPresentForGivenTenantIdAndRemoveFromMapTenantNotInMap(){
		CacheEntry ce = new CacheEntry(1L, "combined");
		Set<Long> set = new HashSet<>();
		set.add(3L);
		set.add(2L);
		personalityBatchProcessor.personalityBatchMap.put("manufacturing", set);
		assertFalse(personalityBatchProcessor.isPersonPresentForGivenTenantIdAndRemoveFromMap(ce));
		assertEquals(2, personalityBatchProcessor.personalityBatchMap.get("manufacturing").size());
	}
	
	@Test
	public void testRemoveFromBatchMap() {
		List<Long> personIds = new ArrayList<>();
		personIds.add(1L);
		personIds.add(2L);
		personIds.add(3L);
		personIds.add(3L);
		
		List<Long> personIdsRemoval = new ArrayList<>();
		personIdsRemoval.add(1L);
		personIdsRemoval.add(2L);
		
		TenantHandlingFacade tenantFacade = Mockito.mock(TenantHandlingFacade.class);
		Mockito.when(tenantFacade.getTenantId()).thenReturn("healthcare");
		personalityBatchProcessor.adapterHelper = new AdapterHelper();
		personalityBatchProcessor.tenantFacade = tenantFacade;
		
		personalityBatchProcessor.addToBatchMap(personIds);
		personalityBatchProcessor.removeFromBatchMap(personIdsRemoval);
		personalityBatchProcessor.removeFromBatchMap(null);
//		assertTrue(personalityBatchProcessor.personalityBatchMap.get("healthcare").size() == 1);
        assertEquals(1, personalityBatchProcessor.personalityBatchMap.get("healthcare").size());
	}
	
	@Test
	public void testRemoveFromBatchMapEmptyMap() {
		List<Long> personIdsRemoval = new ArrayList<>();
		personIdsRemoval.add(1L);
		personIdsRemoval.add(2L);
		
		TenantHandlingFacade tenantFacade = Mockito.mock(TenantHandlingFacade.class);
		Mockito.when(tenantFacade.getTenantId()).thenReturn("healthcare");
		personalityBatchProcessor.adapterHelper = new AdapterHelper();
		personalityBatchProcessor.tenantFacade = tenantFacade;
		
		personalityBatchProcessor.removeFromBatchMap(personIdsRemoval);
		assertNull(personalityBatchProcessor.personalityBatchMap.get("healthcare"));
	}
	
	@Test
	public void testRemoveFromBatchMapEmptyInput() {
		List<Long> personIds = new ArrayList<>();
		personIds.add(1L);
		personIds.add(2L);
		personIds.add(3L);
		personIds.add(3L);
		
		List<Long> personIdsRemoval = new ArrayList<>();

		TenantHandlingFacade tenantFacade = Mockito.mock(TenantHandlingFacade.class);
		Mockito.when(tenantFacade.getTenantId()).thenReturn("healthcare");
		personalityBatchProcessor.adapterHelper = new AdapterHelper();
		personalityBatchProcessor.tenantFacade = tenantFacade;
		
		personalityBatchProcessor.addToBatchMap(personIds);
		personalityBatchProcessor.removeFromBatchMap(personIdsRemoval);
//		assertTrue(personalityBatchProcessor.personalityBatchMap.get("healthcare").size() == 3);
        assertEquals(3, personalityBatchProcessor.personalityBatchMap.get("healthcare").size());
	}
}
