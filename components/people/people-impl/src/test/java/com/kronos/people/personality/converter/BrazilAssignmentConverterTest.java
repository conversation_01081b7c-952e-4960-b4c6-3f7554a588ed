package com.kronos.people.personality.converter;

import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilCompanyAssignmentEntity;
import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilEmployeeEntity;
import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilPcaAssignmentEntity;
import com.kronos.people.personality.dataaccess.entity.brazilcompliance.BrazilRepTypeAssignmentEntity;
import com.kronos.people.personality.model.brazilcompliance.BrazilAssignmentDTO;
import com.kronos.people.personality.model.brazilcompliance.BrazilAssignmentRepTypeDTO;
import com.kronos.people.personality.model.brazilcompliance.BrazilEmployeeDTO;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.junit.jupiter.api.Test;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class BrazilAssignmentConverterTest {
    private static final Long PERSONID = 243L;
    private static final Long OBJECTID = 1L;
    private static final Long COMPANYID = 33333L;
    private static final Long PCAID = 44444L;
    private static final Long REPTYPEID = 1L;

    private static final String UNION_AGREEMENT_NUMBER = "unionAgreementNumber";
    private static final String PIS = "PIS";
    private static final String ESOCIAL = "eSocial";
    private static final String CPF = "cpf";
    private final LocalDateTime EFFECTIVE_DATE = LocalDateTime.now().minusYears(10);
    @InjectMocks
    BrazilAssignmentConverter brazilAssignmentConverter;

    @Test
    public void testConvertSingleEmployeeAssignmentEntitiesToDTO() {

        BrazilEmployeeDTO dto = brazilAssignmentConverter.convertSingleEmployeeAssignmentEntitiesToDTO(PERSONID,
                createBrazilEmployeeEntity(), createBrazilCompanyAssignmentEntity(), createBrazilPcaAssignmentEntity(),
                createBrazilRepTypeAssignmentEntity());
        assertEquals(PERSONID, dto.getEmployeeId());
        assertEquals(CPF, dto.getCpf());
        assertEquals(PIS, dto.getPis());
        assertEquals(ESOCIAL, dto.geteSocial());

        assertEquals(1, dto.getCompanyAssignments().size());
        BrazilAssignmentDTO companyDetail = dto.getCompanyAssignments().get(0);
        assertEquals(PERSONID, companyDetail.getPersonId());
        assertEquals(COMPANYID, companyDetail.getAttributeId());
        assertEquals(EFFECTIVE_DATE.toLocalDate(), companyDetail.getEffectiveDate());
        assertEquals(1, dto.getPcaAssignments().size());
        BrazilAssignmentDTO pcaDetail = dto.getPcaAssignments().get(0);

        assertEquals(PERSONID, pcaDetail.getPersonId());
        assertEquals(PCAID, pcaDetail.getAttributeId());

        assertEquals(EFFECTIVE_DATE.toLocalDate(), pcaDetail.getEffectiveDate());


        assertEquals(1, dto.getRepTypeAssignments().size());
        BrazilAssignmentRepTypeDTO reptypeDetail = (BrazilAssignmentRepTypeDTO) dto.getRepTypeAssignments().get(0);

        assertEquals(PERSONID, reptypeDetail.getPersonId());
        assertEquals(REPTYPEID, reptypeDetail.getAttributeId());
        assertEquals(UNION_AGREEMENT_NUMBER, reptypeDetail.getUnionAgreementNumber());
        assertEquals(EFFECTIVE_DATE.toLocalDate(), reptypeDetail.getEffectiveDate());
    }

    /**
     * Create Mock Entities
     **/
    BrazilEmployeeEntity createBrazilEmployeeEntity() {
        BrazilEmployeeEntity entity = new BrazilEmployeeEntity();
        entity.setPersonId(PERSONID);
        entity.setCpf(CPF);
        entity.seteSocial(ESOCIAL);
        entity.setPis(PIS);
        return entity;
    }

    List<BrazilCompanyAssignmentEntity> createBrazilCompanyAssignmentEntity() {
        List<BrazilCompanyAssignmentEntity> list = new ArrayList();
        BrazilCompanyAssignmentEntity entity = new BrazilCompanyAssignmentEntity();
        entity.setId(OBJECTID);
        entity.setPersonId(PERSONID);
        entity.setCompanyId(COMPANYID);
        entity.setEffectiveDate(EFFECTIVE_DATE);
        list.add(entity);
        return list;
    }

    List<BrazilPcaAssignmentEntity> createBrazilPcaAssignmentEntity() {
        List<BrazilPcaAssignmentEntity> list = new ArrayList();
        BrazilPcaAssignmentEntity entity = new BrazilPcaAssignmentEntity();
        entity.setId(OBJECTID);
        entity.setPersonId(PERSONID);
        entity.setPayCodeAttrDefId(PCAID);
        entity.setEffectiveDate(EFFECTIVE_DATE);
        list.add(entity);
        return list;
    }

    List<BrazilRepTypeAssignmentEntity> createBrazilRepTypeAssignmentEntity() {
        List<BrazilRepTypeAssignmentEntity> list = new ArrayList();
        BrazilRepTypeAssignmentEntity entity = new BrazilRepTypeAssignmentEntity();
        entity.setId(OBJECTID);
        entity.setPersonId(PERSONID);
        entity.setRepTypeId(REPTYPEID);
        entity.setUnionAgreementNumber(UNION_AGREEMENT_NUMBER);
        entity.setEffectiveDate(EFFECTIVE_DATE);
        list.add(entity);
        return list;
    }

}