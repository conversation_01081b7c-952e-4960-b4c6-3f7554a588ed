package com.kronos.people.personality.dataaccess.legacy;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.kronos.datacollection.udm.service.ttipuserprofile.api.ITTIPUserProfileService;
import com.kronos.datacollection.udm.service.devicegroup.api.dto.DeviceGroup;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;

import com.kronos.commonapp.orgmap.setup.model.OrgObjectRef;
import com.kronos.commonapp.orgmap.traversal.api.IOrgMapService;
import com.kronos.datacollection.udm.service.devicegroup.api.IDeviceGroupService;
import com.kronos.datacollection.udm.service.fingerscan.api.IFingerScanAttributesService;
import com.kronos.datacollection.udm.service.individualprofile.api.IIndividualProfileService;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PersonalityFacadeForCachedAttributesMicroTest {

	PersonalityFacadeForCachedAttributes personalityFacadeCachedAttributes = new PersonalityFacadeForCachedAttributes();
	IOrgMapService orgmapservice = Mockito.mock(IOrgMapService.class);
	IDeviceGroupService deviceGroupService = Mockito.mock(IDeviceGroupService.class);
	IFingerScanAttributesService fingerScanAttributesService = Mockito.mock(IFingerScanAttributesService.class);
	IIndividualProfileService deviceProfileService = Mockito.mock(IIndividualProfileService.class);
	ITTIPUserProfileService ittipUserProfileService = Mockito.mock(ITTIPUserProfileService.class);

	@BeforeEach
	public void setup(){
		personalityFacadeCachedAttributes.adapterHelper = new AdapterHelper();
		personalityFacadeCachedAttributes.orgmapservice = orgmapservice;
		personalityFacadeCachedAttributes.deviceGroupService = deviceGroupService;
		personalityFacadeCachedAttributes.fingerScanAttributesService = fingerScanAttributesService;
		personalityFacadeCachedAttributes.deviceProfileService = deviceProfileService;
		personalityFacadeCachedAttributes.ittipUserProfileService = ittipUserProfileService;
	}
	
	@Test
	public void testGetObjectIdLong(){
		assertEquals(1L, personalityFacadeCachedAttributes.getObjectIdLong(1L).longValue());
	}
	
	@Test
	public void testGetPrimaryJob(){
		LocalDate localDate = LocalDate.now();
		OrgObjectRef obj = Mockito.mock(OrgObjectRef.class);
		when(orgmapservice.resolve(any(OrgObjectRef.class), any(LocalDate.class))).thenReturn(obj);
		when(obj.getQualifier()).thenReturn("Sample");
		assertEquals("Sample", personalityFacadeCachedAttributes.getPrimaryJob(1L, localDate));
		
		when(orgmapservice.resolve(any(OrgObjectRef.class), any(LocalDate.class))).thenReturn(null);
		assertNull(personalityFacadeCachedAttributes.getPrimaryJob(1L, localDate));
	}
	
	@Test
	public void testGetDeviceGroupNameFromId() {
	    when(deviceGroupService.getDeviceGroupNameById(1L)).thenReturn("TestDeviceGroup");
            String actual = personalityFacadeCachedAttributes.getDeviceGroupNameFromId(1L);
            assertEquals("TestDeviceGroup", actual);
            assertNull(personalityFacadeCachedAttributes.getDeviceGroupNameFromId(null));
	}
	
	@Test
        public void testGetDeviceProfileNameFromId() {
            when(deviceProfileService.getIndividualProfileNameById(3L)).thenReturn("TestDeviceProfile");
            String actual = personalityFacadeCachedAttributes.getDeviceProfileNameFromId(3L);
            assertEquals("TestDeviceProfile", actual);
            assertNull(personalityFacadeCachedAttributes.getDeviceProfileNameFromId(null));
        }

	@Test
	public void testGetTTIPUserProfileNameFromId() {
		when(ittipUserProfileService.getTTIPUserProfileNameById(3L)).thenReturn("TestTTIPUserProfile");
		String actual = personalityFacadeCachedAttributes.getTTIPUserProfileNameFromId(3L);
		assertEquals("TestTTIPUserProfile", actual);
		assertNull(personalityFacadeCachedAttributes.getTTIPUserProfileNameFromId(null));
	}

	@Test
	public void testIsFingerEnrolled() {
	    when(fingerScanAttributesService.isFingerEnrolled(1L)).thenReturn(Boolean.TRUE);
            when(fingerScanAttributesService.isFingerEnrolled(2L)).thenReturn(Boolean.FALSE);
            assertTrue(personalityFacadeCachedAttributes.isFingerEnrolled(1L));
            assertFalse(personalityFacadeCachedAttributes.isFingerEnrolled(2L));
            assertFalse(personalityFacadeCachedAttributes.isFingerEnrolled(null));
	}
	
	@Test
        public void testIsFingerEnrolledForIdentification() {
            when(fingerScanAttributesService.isFingerEnrolledForIdentification(1L)).thenReturn(Boolean.TRUE);
            when(fingerScanAttributesService.isFingerEnrolledForIdentification(2L)).thenReturn(Boolean.FALSE);
            assertTrue(personalityFacadeCachedAttributes.isFingerEnrolledForIdentification(1L));
            assertFalse(personalityFacadeCachedAttributes.isFingerEnrolledForIdentification(2L));
            assertFalse(personalityFacadeCachedAttributes.isFingerEnrolledForIdentification(null));
        }
	
	@Test
        public void testGetPrimaryFingerThreshold(){
            when(fingerScanAttributesService.getPrimaryFingerThreshold(1L)).thenReturn("High");
            String actual = personalityFacadeCachedAttributes.getPrimaryFingerThreshold(1L);
            assertEquals("High", actual);
            assertNull(personalityFacadeCachedAttributes.getPrimaryFingerThreshold(null));
        }
	
	@Test
        public void testGetPrimaryFingerEnrollmentLocation(){
            when(fingerScanAttributesService.getPrimaryFingerEnrollmentLocation(1L)).thenReturn("My Device (025096)");
            String actual = personalityFacadeCachedAttributes.getPrimaryFingerEnrollmentLocation(1L);
            assertEquals("My Device (025096)", actual);
            assertNull(personalityFacadeCachedAttributes.getPrimaryFingerEnrollmentLocation(null));
        }

	@Test
	public void testGetDeviceGroupsFromIds() {
		List<Long> deviceGroupIds = Arrays.asList(1L, 3L);
		List<DeviceGroup> expected = deviceGroupIds.stream()
				.map(id -> new DeviceGroup() {
					@Override
					public long getId() {
						return id;
					}

					@Override
					public String getName() {
						return null;
					}
				}).collect(Collectors.toList());

		when(deviceGroupService.getDeviceGroupsByIds(deviceGroupIds)).thenReturn(expected);
		List<DeviceGroup> actual = personalityFacadeCachedAttributes.getDeviceGroupsFromIds(deviceGroupIds);
		assertNotNull(actual, "Device group list should not be null");
		assertEquals(expected, actual, "Actual list of Device Groups should match the expected one");
	}
}