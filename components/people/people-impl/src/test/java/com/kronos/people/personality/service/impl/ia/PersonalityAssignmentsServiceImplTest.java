package com.kronos.people.personality.service.impl.ia;

import com.kronos.commonbusiness.datatypes.ia.IAColumn;
import com.kronos.commonbusiness.datatypes.ia.IAErrorDetail;
import com.kronos.commonbusiness.datatypes.ia.IARequest;
import com.kronos.commonbusiness.datatypes.ia.IAResponse;
import com.kronos.people.personality.exception.PersonalityErrorCode;
import com.kronos.people.personality.exception.PersonalityExtensionException;
import com.kronos.people.personality.exception.impl.ia.PersonalityExceptionHelper;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.SchedulingExtension;
import com.kronos.people.personality.model.extension.entry.ApproverEntry;
import com.kronos.people.personality.service.PersonalityExtendedAttributesService;
import com.kronos.people.personality.service.PersonalityService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
//@RunWith(PowerMockRunner.class)
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonalityAssignmentsServiceImplTest {

    @Mock
    private PersonalityService personalityService;

    @Mock
    private PersonalityExtendedAttributesService personalityExtendedAttributesService;

    @Mock
    private PersonalityExceptionHelper exceptionHelper;

//    @Mock
//    private BasePersonalityAssignmentsService[] basePersonalityAssignmentsServices =
//            new BasePersonalityAssignmentsService[]{PowerMockito.mock(ApprovalReviewersOvertimeService.class)};

    @InjectMocks
    private PersonalityAssignmentsServiceImpl personalityAssignmentsService;

    private static final String APPRV_REVIEW_OVERTIME_APPROVAL_LEVEL = "APPRV_REVIEW_OVERTIME_APPROVAL_LEVEL";

    private static final String APPRV_REVIEW_OVERTIME_APPROVER = "APPRV_REVIEW_OVERTIME_APPROVER";

    private static final String APPRV_REVIEW_OVERTIME_APPROVE_DAYS = "APPRV_REVIEW_OVERTIME_APPROVE_DAYS";

    private static final String SYMBOLIC_SERVICE_QUALIFIER = "Current_Payperiod";

    private Map<Long, PersonalityResponse<SchedulingExtension>> personalityServiceResponse;

    private IARequest request;
    private BasePersonalityAssignmentsService approvalReviewersOvertimeServiceMock;

    @BeforeEach
    public void setUp() {
        request = new IARequest();
        request.setSymbolicPeriod(new com.kronos.commonbusiness.datatypes.ref.ObjectRef(1L, "Current_Payperiod"));
        request.setEmployees(Arrays.asList("1401", "1402", "1403", "1404"));

        approvalReviewersOvertimeServiceMock = mock(ApprovalReviewersOvertimeService.class);
        BasePersonalityAssignmentsService[] basePersonalityAssignmentsServices = new BasePersonalityAssignmentsService[]{approvalReviewersOvertimeServiceMock};

        // Use reflection to set the private field
        try {
            Field field = PersonalityAssignmentsServiceImpl.class.getDeclaredField("basePersonalityAssignmentsServices");
            field.setAccessible(true);
            field.set(personalityAssignmentsService, basePersonalityAssignmentsServices);
        } catch (Exception e) {
            e.printStackTrace();
        }

        personalityServiceResponse = new HashMap<>();
    }

    @Test
    public void testGetDataByPathForOvertimeApprovals() {
        request.setColumns(createIAColumnsForOvertimeApprovals());
        when(personalityExtendedAttributesService.getFullName(anyList())).thenReturn(createApproveFullnameMap());
        when(personalityService.findSchedulingExtensions(any())).thenReturn(personalityServiceResponse);
        when(approvalReviewersOvertimeServiceMock.getEntityName()).thenReturn("overtime_approvals");
        when(approvalReviewersOvertimeServiceMock.getData(any())).thenReturn(new IAResponse());
        IAResponse response = personalityAssignmentsService.getDataByPath("overtime_approvals", request);

        assertNotNull(response);
    }

    @Test
    public void testGetDataByPathForOvertimeApprovalsWithException() {
        request.setColumns(createIAColumnsForOvertimeApprovals());
        personalityServiceResponse.put(1401L, new PersonalityResponse<>(null, new PersonalityExtensionException(PersonalityErrorCode.UNKNOWN_ERROR, "Error Test")));
        IAErrorDetail errorDetail = new IAErrorDetail();

        IAResponse IAresponse = new IAResponse();
        IAresponse.setErrorDetails(errorDetail);
        when(personalityExtendedAttributesService.getFullName(anyList())).thenReturn(createApproveFullnameMap());
        when(personalityService.findSchedulingExtensions(any())).thenReturn(personalityServiceResponse);
        when(approvalReviewersOvertimeServiceMock.getEntityName()).thenReturn("overtime_approvals");
        when(approvalReviewersOvertimeServiceMock.getData(any())).thenReturn(IAresponse);
        when(exceptionHelper.buildEmployeeExtensionErrorDetail(any(), any())).thenReturn(errorDetail);
        IAResponse response = personalityAssignmentsService.getDataByPath("overtime_approvals", request);

        assertNotNull(response.getErrorDetails());
    }
    private void fillPersonalityServiceResponse() {
        ApproverEntry approverEntry = new ApproverEntry();
        approverEntry.setEmployeeId(1L);
        approverEntry.setOrderNum(1);
        approverEntry.setDueDateAmt(123456);

        personalityServiceResponse.put(1401L, createSchedulingExtension(List.of(approverEntry), "111401"));
        personalityServiceResponse.put(1402L, createSchedulingExtension(List.of(approverEntry), "111402"));
        personalityServiceResponse.put(1403L, createSchedulingExtension(List.of(approverEntry), "111403"));
        personalityServiceResponse.put(1404L, createSchedulingExtension(List.of(approverEntry), "111404"));
    }
    private PersonalityResponse<SchedulingExtension> createSchedulingExtension(List<ApproverEntry> approvers, String personNum) {
        SchedulingExtension schedulingExtension = new SchedulingExtension();
        schedulingExtension.setPersonNumber(personNum);
        schedulingExtension.setApprovers(approvers);
        return new PersonalityResponse<>(schedulingExtension, null);
    }
    private List<IAColumn> createIAColumnsForOvertimeApprovals() {
        IAColumn approverColumn = new IAColumn(APPRV_REVIEW_OVERTIME_APPROVER, APPRV_REVIEW_OVERTIME_APPROVER);
        IAColumn approvalLevelColumn = new IAColumn(APPRV_REVIEW_OVERTIME_APPROVAL_LEVEL, APPRV_REVIEW_OVERTIME_APPROVAL_LEVEL);
        IAColumn approveDaysColumn = new IAColumn(APPRV_REVIEW_OVERTIME_APPROVE_DAYS, APPRV_REVIEW_OVERTIME_APPROVE_DAYS);

        return Arrays.asList(approverColumn, approvalLevelColumn, approveDaysColumn);
    }

    private Map<Long, String> createApproveFullnameMap() {
        Map<Long, String> approverFullNameMap = new HashMap<>();
        approverFullNameMap.put(1L, "TestApprover");

        return approverFullNameMap;
    }

    @Test
    public void testGetDataByPathForOvertimeApprovals1() {
        when(approvalReviewersOvertimeServiceMock.getEntityName()).thenReturn("overtime_approvals1");
        IAResponse response = personalityAssignmentsService.getDataByPath("overtime_approvals", request);
        assertNull(response);
    }
}
