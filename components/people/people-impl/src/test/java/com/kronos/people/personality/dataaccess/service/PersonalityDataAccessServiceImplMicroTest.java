/*******************************************************************************
 * PersonalityDataAccessServiceImplTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.dataaccess.service;


import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.kronos.people.personality.dataaccess.repository.PersonRepository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;
/*******************************************************************************
 * Test class for PersonalityDataAccessServiceImpl
 ******************************************************************************/
@ExtendWith(MockitoExtension.class)
public class PersonalityDataAccessServiceImplMicroTest {

    @Mock
	private PersonRepository personRepository;
	@InjectMocks
	private PersonalityDataAccessServiceImpl personalityDataAccessService;

	@Test
	public void test_setPersonRepository(){
		  personalityDataAccessService.setPersonRepository(personRepository);
		  assertEquals(personRepository, personalityDataAccessService.getPersonRepository());
	  }

	@Test
	public void test_GetPersonRepository(){
		personalityDataAccessService.setPersonRepository(personRepository);
		assertEquals(personRepository, personalityDataAccessService.getPersonRepository());
	}

	@Test
	public void test_getPersonIds(){
		List<Long> arrays=new ArrayList<>();
		  when(personRepository.getPersonIds()).thenReturn(arrays);
		 assertEquals(arrays, personalityDataAccessService.getPersonIds());
	}
	@Test
	public void test_getPrefferedPersonIds(){
		List<Long> arrays=new ArrayList<>();
		when(personRepository.getPersonIdspreferred()).thenReturn(arrays);
		assertEquals(arrays, personalityDataAccessService.getPersonIdspreferred());
	}

	@Test
	public void test_getPersonIds_withModulo() {
		List<Long> arrays = new ArrayList<>();
		arrays.add(1L);
		int moduloRemainder = 1;
		int moduloFactor = 2;
		when(personRepository.getPersonIds(moduloRemainder, moduloFactor)).thenReturn(arrays);
		assertEquals(arrays, personalityDataAccessService.getPersonIds(moduloRemainder, moduloFactor));
	}

}
