package com.kronos.people.personality.notification.entry;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.kronos.wfc.commonapp.people.business.personality.Personality;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class PersonalityEventMicroTest {

	PersonalityEvent personalityEvent = null;
	Personality personality = null;

	@BeforeEach
	public void setUp() {
		personality = Mockito.mock(Personality.class);
		personalityEvent = new PersonalityEvent(EventType.INSERT, personality, "123");
	}

	@Test
	public void setEventTypeTest() {
		personalityEvent.setEventType(EventType.UPDATE);
		EventType eventType = personalityEvent.getEventType();
		assertEquals(eventType.name(), "UPDATE");

	}

	@Test
	public void setDeleteEventTest() {
		personalityEvent.setEventType(EventType.DELETE);
		EventType eventType2 = personalityEvent.getEventType();
		assertEquals(eventType2.name(), "DELETE");
	}

	@Test
	public void setPersonalityTest() {
		personalityEvent.setPersonality(personality);
		Personality actualPersonality = personalityEvent.getPersonality();
		assertEquals(actualPersonality, personality);
	}

	@Test
	public void setTenantIdTest() {
		String tenantId = "-1";
		personalityEvent.setTenantId(tenantId);
		String actualTenantId = personalityEvent.getTenantId();
		assertEquals(actualTenantId, tenantId);

	}
}
