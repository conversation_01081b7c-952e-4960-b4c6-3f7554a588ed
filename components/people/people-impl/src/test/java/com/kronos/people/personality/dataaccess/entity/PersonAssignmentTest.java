/*******************************************************************************
 * PersonAssignmentTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.people.personality.dataaccess.entity;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

/*******************************************************************************
 * Test class for PersonAssignment
 ******************************************************************************/
@ExtendWith(MockitoExtension.class)
public class PersonAssignmentTest {

    private List<AttestationProfileAssignment> attestationProfileAssignments;
    @Spy
   private PersonAssignment personAssignment;

    @Test
    public void test_getAttestationProfileAssignments(){
        personAssignment.setAttestationProfileAssignments(attestationProfileAssignments);
        assertEquals(attestationProfileAssignments,personAssignment.getAttestationProfileAssignments());
    }

    @Test
    public void test_SetAttestationProfileAssignments(){
        personAssignment.setAttestationProfileAssignments(attestationProfileAssignments);
        assertEquals(attestationProfileAssignments,personAssignment.getAttestationProfileAssignments());
    }

    @Test
    public void test_getId()
    {    personAssignment.getId();
        verify(personAssignment).getId();
    }
}
