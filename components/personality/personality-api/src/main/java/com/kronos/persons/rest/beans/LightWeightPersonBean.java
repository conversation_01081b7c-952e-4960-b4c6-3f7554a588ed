package com.kronos.persons.rest.beans;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.beans.extensions.ExceptionBean;

@Schema(description = "@v1.0.lightweightpersonbean.apimodel.description", name = "lightWeight<PERSON>erson")
public class LightWeightPersonBean implements ExceptionBean{
    /**
     * The instance number.
     */
    @Schema(description = "@v1.0.lightweightpersonbean.apimodelproperty.personnumber.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String personNumber;

    /**
     * The full name.
     */
    @Schema(description = "@v1.0.lightweightpersonbean.apimodelproperty.fullname.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fullName;

    /**
     * The person ID.
     */
    @Schema(description = "@v1.0.lightweightpersonbean.apimodelproperty.personid.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long personId;

    /**
     * The Display name of the object
     */
    @Schema(description = "@v1.0.lightweightpersonbean.apimodelproperty.displayname.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String displayName;
    
    @Schema(description = "@v1.0.lightweightpersonbean.apimodelproperty.exception.description")
	private APIException error;

	/**
	 * @return the personNumber
	 */
	public String getPersonNumber() {
		return personNumber;
	}

	/**
	 * @param personNumber
	 *            the personNumber to set
	 */
	public void setPersonNumber(String personNumber) {
		this.personNumber = personNumber;
	}

	/**
	 * @return the fullName
	 */
	public String getFullName() {
		return fullName;
	}

	/**
	 * @param fullName
	 *            the fullName to set
	 */
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	/**
	 * @return the personId
	 */
	public Long getPersonId() {
		return personId;
	}

	/**
	 * @param personId
	 *            the personId to set
	 */
	public void setPersonId(Long personId) {
		this.personId = personId;
	}

	/**
	 * Gets the objects name. The name of the object and its display name are
	 * the same.
	 *
	 * @return Returns a reference to the name.
	 */
	public String getName() {

		return getDisplayName();
	}

	/**
	 * Gets the objects display name. The display name is the combination of the
	 * full name and the person number.
	 *
	 * @return Returns a reference to the display name.
	 */
	public String getDisplayName() {

		if (displayName == null) {
			String dName = "";
			String fuName = getFullName();
			String personNum = getPersonNumber();

			// Add the full name t the display name
			if (fuName != null && fuName.length() > 0) {
				dName = fuName + " ";
			}
			if (personNum != null && personNum.length() > 0) {
				dName = dName + "(" + personNum + ")";
			}
			if (StringUtils.isNotBlank(dName)) {
				displayName = dName;
			}
		}
		return displayName;
	}

	/**
	 * @return the error
	 */
	public APIException getError() {
		return error;
	}

	/**
	 * @param exception the error to set
	 */
	public void setError(APIException error) {
		this.error = error;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "LightWeightPersonBean [personNumber=" + personNumber
				+ ", fullName=" + fullName + ", personId=" + personId
				+ ", displayName=" + displayName + ", error=" + error
				+ "]";
	}
	
}
