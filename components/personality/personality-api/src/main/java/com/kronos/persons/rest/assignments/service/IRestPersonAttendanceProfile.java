package com.kronos.persons.rest.assignments.service;

import java.util.List;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

import com.kronos.persons.rest.assignments.model.PersonAttendanceProfileBean;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;

@OpenAPIDefinition(tags = {
		@Tag(description = "@v1.0.commons-persons-attendance_profile.description", extensions = @Extension(properties = {
				@ExtensionProperty(name = "parent", value = "@root.personassignments.default") }), name = "@v1.0.commons-persons-attendance_profile.name") })
@Tag(name = "@v1.0.commons-persons-attendance_profile.name")
@Publishable(type = RestAPITypes.PUBLIC, value = true, productType = ProductTypes.WAT)
@Path("/v1/commons/persons/attendance_profile")
public interface IRestPersonAttendanceProfile {

	@PUT
	@Produces(MediaType.APPLICATION_JSON)
	@Operation( tags = "@v1.0.commons-persons-attendance_profile.put.currentpath.nickname", description = "@v1.0.commons-persons-attendance_profile.put.currentpath.notes", summary = "@v1.0.commons-persons-attendance_profile.put.currentpath.summary")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-attendance_profile.put.currentpath.response.200.message", content = @Content(schema = @Schema(implementation = PersonAttendanceProfileBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-attendance_profile.put.currentpath.response.400.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-attendance_profile.put.currentpath.response.500.message")})
	@Path("/")
	public PersonAttendanceProfileBean update(PersonAttendanceProfileBean requestBean);

	@GET
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(tags = "@v1.0.commons-persons-attendance_profile.get.{personId}.nickname", description = "@v1.0.commons-persons-attendance_profile.get.{personId}.notes", summary = "@v1.0.commons-persons-attendance_profile.get.{personId}.summary")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-attendance_profile.get.{personId}.response.200.message", content = @Content(schema = @Schema(implementation = PersonAttendanceProfileBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-attendance_profile.get.{personId}.response.400.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-attendance_profile.get.{personId}.response.500.message")})
	@Path("/{personId:[0-9]+}")
	public PersonAttendanceProfileBean retrieveByPersonId(@Parameter(description = "@v1.0.commons-persons-attendance_profile.get.{personId}.pathparam.personid.value", required = true) @PathParam("personId") Long personId);
	
	@GET
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(tags = "@v1.0.commons-persons-attendance_profile.get.currentpath.nickname", description = "@v1.0.commons-persons-attendance_profile.get.currentpath.notes", summary = "@v1.0.commons-persons-attendance_profile.get.currentpath.summary")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-attendance_profile.get.currentpath.response.200.message", content = @Content(schema = @Schema(implementation = PersonAttendanceProfileBean.class))),
            @ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-attendance_profile.get.currentpath.response.400.message"),
            @ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-attendance_profile.get.currentpath.response.500.message")})
    @Path("/")
    public PersonAttendanceProfileBean retrieveByPersonNumber(@Parameter(description = "@v1.0.commons-persons-attendance_profile.get.currentpath.queryparam.person_number.value", required = true) @QueryParam("person_number") String personNumber);

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(tags = "@v1.0.commons-persons-attendance_profile.post.multi_read.nickname", description = "@v1.0.commons-persons-attendance_profile.post.multi_read.notes", summary = "@v1.0.commons-persons-attendance_profile.post.multi_read.summary")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-attendance_profile.post.multi_read.response.200.message", content = @Content(schema = @Schema(type="list", implementation = PersonAttendanceProfileBean.class))),
			@ApiResponse(responseCode = "207", description = "@v1.0.commons-persons-attendance_profile.post.multi_read.response.207.message"),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-attendance_profile.post.multi_read.response.400.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-attendance_profile.post.multi_read.response.500.message")})
	@Path("/multi_read")
	public List<PersonAttendanceProfileBean> retrieveList(ExtensionSearchCriteria searchCriteria);

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(tags = "@v1.0.commons-persons-attendance_profile.post.multi_update.nickname", description = "@v1.0.commons-persons-attendance_profile.get.multi_update.notes", summary = "@v1.0.commons-persons-attendance_profile.post.multi_update.summary")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-attendance_profile.post.multi_update.response.200.message", content = @Content(schema = @Schema(type="list", implementation = PersonAttendanceProfileBean.class))),
			@ApiResponse(responseCode = "207", description = "@v1.0.commons-persons-attendance_profile.post.multi_update.response.207.message"),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-attendance_profile.post.multi_update.response.400.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-attendance_profile.post.multi_update.response.500.message")})
	@Path("/multi_update")
	public List<PersonAttendanceProfileBean> multiUpdate(List<PersonAttendanceProfileBean> requestBean);

}
