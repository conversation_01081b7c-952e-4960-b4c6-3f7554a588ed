package com.kronos.persons.cacheretry.model;

import jakarta.persistence.*;

@Entity
@Table(name = "failed_person_cache_update", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"person_id", "event_type"})})
public class FailedPersonCacheUpdateEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "person_id")
    private Long personId;

    @Column(name = "event_type")
    private String eventType;

    public FailedPersonCacheUpdateEntity() {
    }

    public FailedPersonCacheUpdateEntity(Long personId, String eventType) {
        this.personId = personId;
        this.eventType = eventType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPersonId() {
        return personId;
    }

    public void setPersonId(Long personId) {
        this.personId = personId;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        FailedPersonCacheUpdateEntity entity = (FailedPersonCacheUpdateEntity) o;

        if (id != null ? !id.equals(entity.id) : entity.id != null) return false;
        if (!personId.equals(entity.personId)) return false;
        return eventType.equals(entity.eventType);
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + personId.hashCode();
        result = 31 * result + eventType.hashCode();
        return result;
    }
}
