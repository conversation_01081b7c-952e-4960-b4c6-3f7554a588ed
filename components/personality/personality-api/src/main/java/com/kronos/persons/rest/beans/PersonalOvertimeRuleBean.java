package com.kronos.persons.rest.beans;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.personalovertimerulebean.apimodel.description", name = "personalOvertimeRuleDetails")
@JsonPropertyOrder(alphabetic = true)
public class PersonalOvertimeRuleBean {
    @Schema(description = "@v1.0.personalovertimerulebean.apimodelproperty.personalovertimelimits.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<PersonalOvertimeLimitBean> personalOvertimeLimits;

    @Schema(description = "@v1.0.personalovertimerulebean.apimodelproperty.personalovertimeruledisplayname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String personalOvertimeRuleDisplayName;

    @Schema(description = "@v1.0.personalovertimerulebean.apimodelproperty.usescheduleflag.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Boolean useScheduleFlag;

	/**
	 * @return the personalOvertimeLimits
	 */
	public List<PersonalOvertimeLimitBean> getPersonalOvertimeLimits() {
		return personalOvertimeLimits;
	}

	/**
	 * @param personalOvertimeLimits
	 *            the personalOvertimeLimits to set
	 */
	public void setPersonalOvertimeLimits(
			List<PersonalOvertimeLimitBean> personalOvertimeLimits) {
		this.personalOvertimeLimits = personalOvertimeLimits;
	}

	/**
	 * @return the personalOvertimeRuleDisplayName
	 */
	public String getPersonalOvertimeRuleDisplayName() {
		return personalOvertimeRuleDisplayName;
	}

	/**
	 * @param personalOvertimeRuleDisplayName
	 *            the personalOvertimeRuleDisplayName to set
	 */
	public void setPersonalOvertimeRuleDisplayName(
			String personalOvertimeRuleDisplayName) {
		this.personalOvertimeRuleDisplayName = personalOvertimeRuleDisplayName;
	}

	/**
	 * @return the useScheduleFlag
	 */
	public Boolean getUseScheduleFlag() {
		return useScheduleFlag;
	}

	/**
	 * @param useScheduleFlag
	 *            the useScheduleFlag to set
	 */
	public void setUseScheduleFlag(Boolean useScheduleFlag) {
		this.useScheduleFlag = useScheduleFlag;
	}

}
