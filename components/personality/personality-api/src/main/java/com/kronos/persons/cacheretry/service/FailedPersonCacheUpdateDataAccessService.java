package com.kronos.persons.cacheretry.service;

import com.kronos.persons.cacheretry.dto.FailedPersonCacheUpdateDto;

import java.util.List;
import java.util.Set;

public interface FailedPersonCacheUpdateDataAccessService {

    void save(FailedPersonCacheUpdateDto failedPersonCacheUpdateDto);

    void saveBatch(Set<FailedPersonCacheUpdateDto> failedPersonCacheUpdateDtoSet);

    void delete(Long id);

    void deleteBatch(List<Long> ids);

    FailedPersonCacheUpdateDto findByPersonIdAndEventType(Long personId, String eventType);

    List<FailedPersonCacheUpdateDto> findAll();

    List<FailedPersonCacheUpdateDto> findPage(int page, int size);

}
