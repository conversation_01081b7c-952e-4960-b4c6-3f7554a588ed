package com.kronos.persons.rest.assignments.model;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.model.BeanWrapper;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * Implementation of {@link BeanWrapper} for assignment profile.
 * instance of {@link AssignmentProfileRequestBean}
 * Copyright (C) 2019 Kronos.com
 * Date: Jun 25, 2019
 *
 * <AUTHOR>
 */
public class AssignmentProfileRequestBeanWrapper implements BeanWrapper<AssignmentProfileRequestBean> {
    private AssignmentProfileRequestBean bean;
    private APIException apiException;

    /**
     * Constructor.
     *
     * @param bean instance of {@link AssignmentProfileRequestBean}
     */
    public AssignmentProfileRequestBeanWrapper(AssignmentProfileRequestBean bean) {
        this.bean = bean;
    }

    @Override
    public APIException getApiException() {
        return apiException;
    }

    @Override
    public void setApiException(APIException apiException) {
        this.apiException = apiException;
    }

    @Override
    public AssignmentProfileRequestBean getBean() {
        return bean;
    }

    @Override
    public void setBean(AssignmentProfileRequestBean bean) {
        this.bean = bean;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AssignmentProfileRequestBeanWrapper that = (AssignmentProfileRequestBeanWrapper) o;
        return new EqualsBuilder()
                .append(bean, that.bean)
                .append(apiException, that.apiException)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder()
                .append(bean)
                .append(apiException)
                .toHashCode();
    }
}
