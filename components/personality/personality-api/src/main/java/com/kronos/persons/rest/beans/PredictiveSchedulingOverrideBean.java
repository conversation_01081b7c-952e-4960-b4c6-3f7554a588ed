package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.predictiveschedulingoverridebean.apimodel.description", name = "predictiveSchedulingOverrideDetails")
@JsonPropertyOrder(alphabetic = true)
public class PredictiveSchedulingOverrideBean {

   @Schema(description = "@v1.0.PredictiveSchedulingOverrideBean.apimodelproperty.scheduleruleid.description")
   private String predictiveSchedulingRule;

   @Schema(description = "@v1.0.PredictiveSchedulingOverrideBean.apimodelproperty.effectivedate.description")
   private String effectiveDate;

   @Schema(description = "@v1.0.PredictiveSchedulingOverrideBean.apimodelproperty.expirationdate.description")
   private String expirationDate;

   public String getPredictiveSchedulingRule() {
      return predictiveSchedulingRule;
   }

   public void setPredictiveSchedulingRule(String predictiveSchedulingRule) {
      this.predictiveSchedulingRule = predictiveSchedulingRule;
   }

   public String getEffectiveDate() {
      return effectiveDate;
   }

   public void setEffectiveDate(String effectiveDate) {
      this.effectiveDate = effectiveDate;
   }

   public String getExpirationDate() {
      return expirationDate;
   }

   public void setExpirationDate(String expirationDate) {
      this.expirationDate = expirationDate;
   }
}
