package com.kronos.persons.accountmanagement.service;

import com.kronos.accountmanagement.domain.AccountManagementResponse;
import com.kronos.accountmanagement.domain.UserDTO;
import com.kronos.accountmanagement.domain.UserFailureListDTO;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import java.util.List;
import java.util.Map;


/**
 * This interface is for accessing the auth0 functions.
 *
 * <AUTHOR>
 */
public interface PersonAccountManagementService {

  /**
   * This method return the auth0 feature flag.
   *
   * @return Boolean value
   */
  Boolean getAuthFlag();
  
  /**
   * This method return the is auth type federated customer flag.
   *
   * @param personality
   * @return Boolean value
   */
  Boolean isAuthTypeFederated(Personality personality);

  /**
   * This method return Auth sync enable flag
   * @return Boolean Value
   */
  Boolean getAuthSyncEnableFlag();


  /** This method is to create User
   *
   * @param personality
   */
  void createUser(Personality personality);


  /** This method is to delete user
   *
   * @param personality
   */
  void deleteUser(Personality personality);



  /** This method is to update user
   *
   * @param oldPersonality
   */
  void updateUser(Personality oldPersonality, boolean isUpdated, boolean isStatusChanged , boolean isPasswordChanged, UserDTO userDTOAMS,  Map<Boolean, com.kronos.ums.userservice.model.UserDTO> operationUserDTO);

  /**
   * This method performs a bulk call to AMS server to create
   * multiple accounts in single call
   * @param personalities List of personality info being sent to AMS
   */
  AccountManagementResponse<UserFailureListDTO> createUsers(List<Personality> personalities);


  /**
   * This method performs a bulk call to AMS server to update
   * multiple accounts in single call
   * @param personalities List of personality info being sent to AMS
   */

  AccountManagementResponse<UserFailureListDTO> updateUsers(List<Personality> personalities);

  AccountManagementResponse<UserFailureListDTO> deleteUsers(List<Personality> personalities);

  Boolean getSSOUsernameFlag();

  public boolean isSuiteCustomerAndFederated(Personality personality);

}
