package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.beans.extensions.ExceptionBean;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.photobean.apimodel.description", name = "photobean")
public class PhotoBean implements ExceptionBean {
	
	
    @Schema(description = "@v1.0.photobean.apimodelproperty.personidentity.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private PersonIdentityBean personIdentity;
    
    @Schema(description = "@v1.0.photobean.apimodelproperty.photo.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private PhotoInfoBean photo;
    
    @JsonIgnore
    @Schema(description = "@v1.0.photobean.apimodelproperty.exception.description")
	private APIException error;

	public PersonIdentityBean getPersonIdentity() {
		return personIdentity;
	}

	public void setPersonIdentity(PersonIdentityBean personIdentity) {
		this.personIdentity = personIdentity;
	}

	public PhotoInfoBean getPhoto() {
		return photo;
	}

	public void setPhoto(PhotoInfoBean photo) {
		this.photo = photo;
	}

	public APIException getError() {
		return error;
	}

	public void setError(APIException error) {
		this.error = error;
	}

	@Override
	public String toString() {
		return "PhotoBean [personIdentity=" + personIdentity + ", photo=" + photo + ", error=" + error + "]";
	}


	
	
}
