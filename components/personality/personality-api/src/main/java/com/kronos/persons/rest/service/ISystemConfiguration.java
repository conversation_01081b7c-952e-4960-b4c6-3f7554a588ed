package com.kronos.persons.rest.service;

/**
 * Interface for SystemConfiguration information
 * <AUTHOR>
 */
public interface ISystemConfiguration {
	
	/**
	 * Returns the gracePeriod.
	 * @return integer parameter
	 */
	public abstract int getGracePeriod();

	/**
	 * Sets the Grace Period.
	 * @param gracePeriod integer parameter
	 */
	public abstract void setGracePeriod(int gracePeriod);

	/**
	 * Returns the SystemTimeout.
	 * @return integer parameter
	 */
	public abstract int getSystemTimeOut();

	/**
	 * Sets the systemTimeout
	 * @param systemTimeOut integer parameter
	 */
	public abstract void setSystemTimeOut(int systemTimeOut);

	/**
	 * Returns the version.
	 * @return String parameter
	 */
	public abstract String getVersion();

	/**
	 * Sets the version.
	 * @param version String parameter
	 */
	public abstract void setVersion(String version);
}
