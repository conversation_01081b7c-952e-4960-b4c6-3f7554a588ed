/***********************************************************************
 * IApplicationContextService.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.persons.context.service;

import java.util.List;

import com.kronos.commonapp.authz.api.fap.permissions.IPermission;
import com.kronos.commonapp.userpreferences.api.IKPreferences;

/**
 * The {@code IApplicationContextService} is the interface which defines API
 * like getting application context, current user, getting preferences and
 * permissions respectively.
 * 
 * <AUTHOR>
 *
 */
public interface IApplicationContext {

	/**
	 * This API gets the current logged in user.
	 * @return The {@link ICurrentUser} instance.
	 */
	public ICurrentUser getCurrentUser();
	
	/**
	 * This API gets the permissions.
	 * @return The {@link List} instance.
	 */
	public List<IPermission> getPermissions();

	/**
	 * This API gets the manager permissions.
	 * @return The {@link List} instance.
	 */
	public List<IPermission> getManagerPermissions();
	
	/**
	 * This API gets the Prefernces.
	 * @return {@link IKPreferences} instance.
	 */
	public IKPreferences getPreference();

}
