package com.kronos.persons.rest.model;

import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.batchresponsedetailsbean.apimodel.description", name = "batchResponseDetails")
public class BatchResponseDetailsBean {
    @Schema(description = "@v1.0.batchresponsedetailsbean.apimodelproperty.results.description")
    private List<BatchResponseResultsBean> results;

    @Schema(description = "@v1.0.batchresponsedetailsbean.apimodelproperty.erroroffsets.description")
	private List<Integer> errorOffSets;
	/**
	 * @return the results
	 */
	public List<BatchResponseResultsBean> getResults() {
		return results;
	}
	/**
	 * @param results the results to set
	 */
	public void setResults(List<BatchResponseResultsBean> results) {
		this.results = results;
	}
	/**
	 * @return the errorOffSets
	 */
	public List<Integer> getErrorOffSets() {
		return errorOffSets;
	}
	/**
	 * @param errorOffSets the errorOffSets to set
	 */
	public void setErrorOffSets(List<Integer> errorOffSets) {
		this.errorOffSets = errorOffSets;
	}
	

}
