/**
 * 
 */
package com.kronos.persons.rest.model;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.kronos.persons.rest.beans.HyperFindFilterBean;

/**
 * <AUTHOR>
 */
public class ExtensionCriteria implements Serializable {

	private static final long serialVersionUID = 1L;

	private String extensionType;

	private String snapshotDate;
	private String snapshotDateTime;

	private Boolean onlyActivePerson = false;

	private String includeBaseWages;

	private String searchBy;

	private List<String> searchValue;

	private List<String> multiKey;

	private List<SearchValues> multiKeyValues;

	private HyperFindFilterBean hyperFindFilter;

	private Boolean isEmployeeAllowed = false;

	private Boolean includeAccrualPolicyDetails = false;

	/**
	 * @return the extensionType
	 */
	public String getExtensionType() {
		return extensionType;
	}

	/**
	 * @param extensionType the extensionType to set
	 */
	public void setExtensionType(String extensionType) {
		this.extensionType = extensionType;
	}

	/**
	 * @return the snapShotDate
	 */
	public String getSnapshotDate() {
		return snapshotDate;
	}

	/**
	 * @param snapshotDate the snapshotDate to set
	 */
	public void setSnapshotDate(String snapshotDate) {
		this.snapshotDate = snapshotDate;
	}

	/**
	 * @return the onlyActivePerson
	 */
	public Boolean getOnlyActivePerson() {
		return onlyActivePerson;
	}

	/**
	 * @param onlyActivePerson the onlyActivePerson to set
	 */
	public void setOnlyActivePerson(Boolean onlyActivePerson) {
		this.onlyActivePerson = onlyActivePerson;
	}

	/**
	 * @return the searchBy
	 */
	public String getSearchBy() {
		return searchBy;
	}

	/**
	 * @param searchBy the searchBy to set
	 */
	public void setSearchBy(String searchBy) {
		this.searchBy = searchBy;
	}

	/**
	 * @return the searchValue
	 */
	public List<String> getSearchValue() {
		return searchValue;
	}

	/**
	 * @param searchValue the searchValue to set
	 */
	public void setSearchValue(List<String> searchValue) {
		this.searchValue = searchValue;
	}

	public List<String> getMultiKey() {
		return multiKey;
	}

	public void setMultiKey(List<String> multiKey) {
		this.multiKey = multiKey;
	}

	public List<SearchValues> getMultiKeyValues() {
		return multiKeyValues;
	}

	public void setMultiKeyValues(List<SearchValues> multiKeyValues) {
		this.multiKeyValues = multiKeyValues;
	}

	public HyperFindFilterBean getHyperFindFilter() {
		return hyperFindFilter;
	}

	public void setHyperFindFilter(HyperFindFilterBean hyperFindFilter) {
		this.hyperFindFilter = hyperFindFilter;
	}

	/**
	 * @return the isEmployeeAllowed
	 */
	public Boolean getIsEmployeeAllowed() {
		return isEmployeeAllowed;
	}

	/**
	 * @param isEmployeeAllowed the isEmployeeAllowed to set
	 */
	public void setIsEmployeeAllowed(Boolean isEmployeeAllowed) {
		this.isEmployeeAllowed = isEmployeeAllowed;
	}

	public String getSnapshotDateTime() {
		return snapshotDateTime;
	}

	public void setSnapshotDateTime(String snapshotDateTime) {
		this.snapshotDateTime = snapshotDateTime;
	}

	public Boolean getIncludeAccrualPolicyDetails() {
		return includeAccrualPolicyDetails;
	}

	public void setIncludeAccrualPolicyDetails(Boolean includeAccrualPolicyDetails) {
		this.includeAccrualPolicyDetails = includeAccrualPolicyDetails;
	}

	public String getIncludeBaseWages() {
		return includeBaseWages;
	}

	public void setIncludeBaseWages(String includeBaseWages) {
		this.includeBaseWages = includeBaseWages;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("{ ");
		if (StringUtils.isNotBlank(getSearchBy())) {
			builder.append("searchBy:" + getSearchBy() + ", ");
			builder.append("searchValue:" + getSearchValue() + ", ");
		}
		if (getMultiKey() != null && !getMultiKey().isEmpty()) {
			builder.append("multiKey:" + getMultiKey() + ", ");
			builder.append("multiKeyValues:" + getMultiKeyValues() + ", ");
		}
		builder.append("onlyActivePerson:" + getOnlyActivePerson() + ", ");
		builder.append("baseWages:" + getIncludeBaseWages() + ", ");
		builder.append("snapshotDate:" + getSnapshotDate() + ", ");
		builder.append("snapshotDateTime:" + getSnapshotDateTime() + ", ");
		builder.append("extensionType:" + getExtensionType() + ", ");
		builder.append("hyperFindFilter:" + getHyperFindFilter() + ", ");
		builder.append("isEmployeeAllowed:" + getIsEmployeeAllowed() + " ");
		builder.append("includeAccrualPolicyDetails:" + getIncludeAccrualPolicyDetails() + " ");
		builder.append("}");
		return builder.toString();
	}

}
