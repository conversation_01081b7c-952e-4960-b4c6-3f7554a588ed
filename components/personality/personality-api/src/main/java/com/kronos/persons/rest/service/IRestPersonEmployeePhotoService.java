package com.kronos.persons.rest.service;

import java.util.List;
import com.kronos.persons.rest.beans.PhotoBean;
import com.kronos.persons.rest.model.PhotoSearchCriteria;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;

@OpenAPIDefinition(tags = {
		@Tag(name = "@v1.0.commons-persons-employee_photo.name", description = "@v1.0.commons-persons-employee_photo.description", extensions = @Extension(properties = {
				@ExtensionProperty(name = "parent", value = "@v1.0.commons-persons-employee_photo.parent") })) })
@Tag(name = "@v1.0.commons-persons-employee_photo.name")
@Publishable(type = RestAPITypes.PUBLIC, value = true, productType = ProductTypes.WFP)
@Path(value = PersonPhotoServiceConstants.EMPLOYEE_PHOTO_REST_ENDPOINT)
public interface  IRestPersonEmployeePhotoService {

	@POST
    @Path(value = "/multi_read")
    @Consumes(value = MediaType.APPLICATION_JSON)
    @Produces(value = MediaType.APPLICATION_JSON)
    @Operation(tags = "@v1.0.commons-persons-profile_photos.post.multi_read.nickname", description = "@v1.0.commons-persons-profile_photos.post.multi_read.notes", summary = "@v1.0.commons-persons-profile_photos.post.multi_read.summary")
    @ApiResponses(value = {
    		@ApiResponse(description = "@v1.0.commons-persons-profile_photos.post.multi_read.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = PhotoBean.class))),
    		@ApiResponse(description = "@v1.0.commons-persons-profile_photos.post.multi_read.response.207.message", responseCode = "207"),
    		@ApiResponse(description = "@v1.0.commons-persons-profile_photos.post.multi_read.response.400.message", responseCode = "400"),
    		@ApiResponse(description = "@v1.0.commons-persons-profile_photos.post.multi_read.response.500.message", responseCode = "500") })
    public List<PhotoBean> multiRead(PhotoSearchCriteria searchCriteria, @Context final HttpServletResponse response);

}
