/***********************************************************************
 * PersonRestService.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.persons.rest.service;

import java.util.List;

import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

import com.kronos.persons.context.service.ICurrentUser;
import com.kronos.persons.context.switchrole.IRoleDelegateContext;
import com.kronos.persons.rest.model.UserFeatures;

/**
 * This is the REST respresentation for Person related REST calls.
 * <AUTHOR>
 *
 */
@Path(value = "/v1/commons/persons")
public interface PersonRestService {

	/**
	 * Gets the user details.
	 * @return current logged in user details.
	 */
	@GET
	@Produces(value = MediaType.APPLICATION_JSON)
	@Path(value = "/getUserDetails")
	public ICurrentUser getUserDetails();


	/**
	 * Validates whether is photo enabled.
	 * @return boolean value
	 */
	@GET
	@Path("/isPhotosEnabled")
	@Produces("application/json")
	public Boolean isPhotosEnabled();


	/**
	 * Fetches UserPhotoId.
	 * @return String containing the photoId
	 */
	@GET
	@Path("/getUserPhotoId")
	@Produces("application/json")
	public String getUserPhotoId();

	/**
	 * This method returns the SystemConfiguration information
	 * @return ISystemConfiguration instance
	 */
	@GET
	@Path("/getSystemConfiguration")
	@Produces("application/json")
	public ISystemConfiguration getSystemConfiguration();


	/**
	 * gets details of User
	 * @return
	 * @throws NGUIException
	 */
	@GET
	@Path("/getUserDelegateContext")
	@Produces("application/json")
	public IRoleDelegateContext geUsertDelegateContext();
	
	
	/**
	 * This rest call is for handling delete person from people editor.
	 * @param personIds List of Long ids
	 */
	@DELETE
    @Path ("/deletePerson")
    @Produces(MediaType.APPLICATION_JSON)
    public void deletePersons(@QueryParam("personIds") List<Long> personIds);
	
	
	
	/**
	 * This rest call is used to validate person delete is allowed on not
	 * @return
	 */
	@GET
    @Path ("/isDeleteAllowed")
    @Produces(MediaType.APPLICATION_JSON)
    public Boolean isDeleteAllowed();

	
	/**
	 * This rest call is used to user features
	 * @return
	 */
	@GET
    @Path ("/userFeatures")
    @Produces(MediaType.APPLICATION_JSON)
    public UserFeatures getUserFeatures();
	
	/**
	 * This rest call is used to get offline timeout
	 * @return
	 */
	@GET
    @Path ("/offlineTimeout")
    @Produces(MediaType.APPLICATION_JSON)
    public Integer getOfflineTimeout();
 
}