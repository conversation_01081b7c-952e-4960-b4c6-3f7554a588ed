package com.kronos.persons.rest.assignments.model;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.persons.rest.beans.PersonIdentityBean;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

@Schema(title = "adminProfileDetail", description = "@v1.0.adminprofilebean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class AdminProfileBean extends PersonIdentityBean {

	@Schema(description = "@v1.0.adminprofilebean.apimodelproperty.fullName.description")
	private String fullName;
	@Schema(description = "@v1.0.adminprofilebean.apimodelproperty.displayName.description")
	private String displayName;

	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	public String getDisplayName() {

		if (displayName == null) {
			String dName = "";
			String fuName = getFullName();
			String personNum = getPersonNumber();

			// Add the full name t the display name
			if (fuName != null && fuName.length() > 0) {
				dName = fuName + " ";
			}
			if (personNum != null && personNum.length() > 0) {
				dName = dName + "(" + personNum + ")";
			}
			if (StringUtils.isNotBlank(dName)) {
				displayName = dName;
			}
		}
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	@Override
	public Boolean isEmpty() {
		return super.isEmpty() && this.fullName == null && this.displayName == null ? Boolean.TRUE
				: Boolean.FALSE;
	}
	
	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		AdminProfileBean that = (AdminProfileBean) o;
		return new EqualsBuilder()
				.append(fullName, that.fullName)
				.append(displayName, that.displayName)
				.appendSuper(super.equals(o))
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder()
				.append(fullName)
				.append(displayName)
				.appendSuper(super.hashCode())
				.toHashCode();
	}
}
