package com.kronos.persons.rest.model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kronos.wfc.platform.datetime.framework.KServer;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.exceptions.framework.WfpInstallationException;
import com.kronos.wfc.platform.logging.framework.Log;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import com.kronos.wfc.platform.xml.api.bean.APIProcessingException;
import com.kronos.wfc.platform.xml.api.bean.APIWrapExceptionIfc;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.resterrorbean.apimodel.description", title = "error")
public class RestErrorBean {
    
	@Schema(description = "@v1.0.resterrorbean.apimodelproperty.message.description")
    private String message;

    @Schema(description = "@v1.0.resterrorbean.apimodelproperty.errorcode.description")
    private Long errorCode;

    @Schema(description = "@v1.0.resterrorbean.apimodelproperty.detailerrors.description")
    private List<RestErrorBean> detailErrors;

    @Schema(description = "@v1.0.resterrorbean.apimodelproperty.beanname.description")
    private String beanName;

    @Schema(description = "@v1.0.resterrorbean.apimodelproperty.propertyname.description")
    private String propertyName;

    @Schema(description = "@v1.0.resterrorbean.apimodelproperty.propertyvalue.description")
    private String propertyValue;

    @Schema(description = "@v1.0.resterrorbean.apimodelproperty.propertytype.description")
    private String propertyType;

    @Schema(description = "@v1.0.resterrorbean.apimodelproperty.parametername.description")
    private String parameterName;

    @Schema(description = "@v1.0.resterrorbean.apimodelproperty.parametervalue.description")
    private String parameterValue;

    @Schema(description = "@v1.0.resterrorbean.apimodelproperty.parametertype.description")
    private String parameterType;

    @JsonIgnore
    @Schema(description = "@v1.0.resterrorbean.apimodelproperty.errordata.description")
	private Collection<RestErrorBean> errorData;

	/**
	 * Constant fields
	 */
	public static final String MESSAGE = "message";
	public static final String ERROR_CODE = "errorCode";
	public static final String ERROR_DATA = "errorData";
	public static final String AT_INDEX = "atIndex";
	public static final String BEAN_NAME = "beanName";
	public static final String DETAIL_ERRORS = "detailErrors";
	public static final String ACTION_NAME = "actionName";
	public static final String PROPERTY_NAME = "propertyName";
	public static final String PROPERTY_VALUE = "propertyValue";
	public static final String PROPERTY_TYPE = "propertyType";
	public static final String PARAMETER_NAME = "parameterName";
	public static final String PARAMETER_VALUE = "parameterValue";
	public static final String PARAMETER_TYPE = "parameterType";

	/**
	 * The set of property name constants.
	 */
	private static final String propertyNames[] = { MESSAGE, ERROR_CODE, DETAIL_ERRORS, BEAN_NAME,
			ERROR_DATA, PROPERTY_NAME, PROPERTY_VALUE, PROPERTY_TYPE, PARAMETER_NAME, PARAMETER_VALUE,
			PARAMETER_TYPE };

	/**
	 * The default constructor for APIErrorBean.
	 */
	public RestErrorBean() {
		// Use the superclass constructor
		super();
	}

	/**
	 * Construct an RestErrorBean from a throwable. We populate as many of the
	 * properties as we can.
	 *
	 * @param throwable
	 *            The throwable object to wrap.
	 */
	public RestErrorBean(Throwable throwable) {
		// Use the empty constructor
		this();

		if (throwable.getClass() == java.lang.reflect.InvocationTargetException.class) {
			throwable = throwable.getCause();
		}

		// If this is a Kronos Exception, try to find the standard user
		// parameters and copy them to properties. Any other exception
		// parameters get appended to the "data" property.
		if (throwable instanceof GenericException) {
			// Process the generic exception
			GenericException e = (GenericException) throwable;
			if (e.isToBeLogged())
				// Log the exception
				Log.log(Log.API, Log.ERROR, e);
			try {
				setMessage(e.getLocalizedMessage());
			} catch (Exception x) {
			}

			// Go through the list of internal parameters
			Map parameters = e.getInternalParameters();
			parameters.putAll(e.getUserParameters());
			for (Iterator i = parameters.keySet().iterator(); i.hasNext();) { // Add
																				// the
																				// parameters
				String name = (String) i.next();
				String value = null;
				Object testValue = parameters.get(name);
				if (testValue instanceof String)
					value = (String) testValue;
				else if (testValue != null)
					value = KServer.objectToString(testValue, true);
				// Look for the property
				boolean found = false;
				for (int j = 0; j < propertyNames.length && !found; j++)
					found = propertyNames[j].equalsIgnoreCase(name);

				// If the property was found, set the value within the bean.
				if (found)
					setProperty(name, value);

				else { // Otherwise, add the name/value pair to the error data.
					if (getErrorData() == null)
						setErrorData(new ArrayList());

					String data = name + "=" + value;
					getErrorData().add(data);
				}
			}

			// Start a list to hold wrapped exceptions
			List detailList = new ArrayList();

			// if there's a wrapped exception, put it on the detail list...
			if (e.getWrappedThrowable() != null)
				detailList.add(new RestErrorBean(e.getWrappedThrowable()));

			// If this is an API exception, put wrapped exceptions on the
			// detail list... and turn them into detail APIErrorBeans.
			if (throwable instanceof APIWrapExceptionIfc) { // If the exception
															// can have wrapped
															// exceptions, get
															// them.
				Exception[] details = ((APIWrapExceptionIfc) throwable).getWrappedExceptions();

				// Add the other wrapped exception to this one
				for (int i = 0; i < details.length; i++)
					detailList.add(new RestErrorBean(details[i]));
			}

			// If there's anything wrapped on the detail list,
			// add it to the bean.
			if (!detailList.isEmpty())
				setDetailErrors(detailList);

			// Set the error code for the message
			setErrorCode(Long.valueOf(exceptionToErrorCode(throwable)));
		}

		else { // If it's not a Kronos exception, there's not much we can to but
				// save the message or class.
				// Log the exception
			StringBuffer message = new StringBuffer();
			message.append(throwable.getClass().getName());
			if (throwable.getMessage() != null) {
				message.append(": ");
				message.append(throwable.getMessage());
			}
			Throwable cause = throwable.getCause();
			if (cause != null) {
				RestErrorBean causeBean = new RestErrorBean(cause);
				List<RestErrorBean> details = new ArrayList<RestErrorBean>();
				details.add(causeBean);
				setDetailErrors(details);
			}

			// Append the see log for details message.
			message.append(" - See log for details.");
			setMessage(message.toString());
			setErrorCode(Long.valueOf(-1));
			// Log the exception
			Log.log(Log.API, 1, "Unexpected API exception:", throwable);
		}
	}
	
	
	/**
	 * @return the message
	 */
	public String getMessage() {
		return message;
	}

	/**
	 * @param message
	 *            the message to set
	 */
	public void setMessage(String message) {
		this.message = message;
	}

	/**
	 * @return the beanName
	 */
	public String getBeanName() {
		return beanName;
	}

	/**
	 * @param beanName
	 *            the beanName to set
	 */
	public void setBeanName(String beanName) {
		this.beanName = beanName;
	}

	/**
	 * @return the errorCode
	 */
	public Long getErrorCode() {
		return errorCode;
	}

	/**
	 * @param errorCode
	 *            the errorCode to set
	 */
	public void setErrorCode(Long errorCode) {
		this.errorCode = errorCode;
	}

	/**
	 * @return the detailErrors
	 */
	public List<RestErrorBean> getDetailErrors() {
		return detailErrors;
	}

	/**
	 * @param detailErrors
	 *            the detailErrors to set
	 */
	public void setDetailErrorList(List<RestErrorBean> detailErrors) {
		this.detailErrors = detailErrors;
	}

	/**
	 * @return the propertyName
	 */
	public String getPropertyName() {
		return propertyName;
	}

	/**
	 * @param propertyName
	 *            the propertyName to set
	 */
	public void setPropertyName(String propertyName) {
		this.propertyName = propertyName;
	}

	/**
	 * @return the propertyValue
	 */
	public String getPropertyValue() {
		return propertyValue;
	}

	/**
	 * @param propertyValue
	 *            the propertyValue to set
	 */
	public void setPropertyValue(String propertyValue) {
		this.propertyValue = propertyValue;
	}

	/**
	 * @return the propertyType
	 */
	public String getPropertyType() {
		return propertyType;
	}

	/**
	 * @param propertyType
	 *            the propertyType to set
	 */
	public void setPropertyType(String propertyType) {
		this.propertyType = propertyType;
	}

	/**
	 * @return the parameterName
	 */
	public String getParameterName() {
		return parameterName;
	}

	/**
	 * @param parameterName
	 *            the parameterName to set
	 */
	public void setParameterName(String parameterName) {
		this.parameterName = parameterName;
	}

	/**
	 * @return the parameterValue
	 */
	public String getParameterValue() {
		return parameterValue;
	}

	/**
	 * @param parameterValue
	 *            the parameterValue to set
	 */
	public void setParameterValue(String parameterValue) {
		this.parameterValue = parameterValue;
	}

	/**
	 * @return the parameterType
	 */
	public String getParameterType() {
		return parameterType;
	}

	/**
	 * @param parameterType
	 *            the parameterType to set
	 */
	public void setParameterType(String parameterType) {
		this.parameterType = parameterType;
	}

	/**
	 * @return the propertynames
	 */
	public static String[] getPropertynames() {
		return propertyNames;
	}

	/**
	 * Changes the DetailErrors property.
	 *
	 * @param value
	 *            The new value of DetailErrors.
	 * @exception APIProcessingException
	 *                Throws an exception when the type check fails.
	 */
	public void setDetailErrors(Object value) {
		// Check type value's type
		if (value instanceof Collection) { // Set the value of the detailed
											// errors
			setDetailErrorList((List)value);
		}

		else { // Throw the invalid type exception
			if (value != null)
				throw APIProcessingException.invalidPropertyType(DETAIL_ERRORS, value.getClass().toString());
			else
				throw APIProcessingException.invalidPropertyType(DETAIL_ERRORS, null);
		}
	}

	/**
	 * Retrieves the ErrorData property.
	 *
	 * @return Returns the value of ErrorData.
	 */
	public Collection getErrorData() {
		// Return the ErrorData to the caller
		return errorData;
	}

	/**
	 * Changes the ErrorData property.
	 *
	 * @param value
	 *            The new value of ErrorData.
	 * @exception APIProcessingException
	 *                Throws an exception when the type check fails.
	 */
	public void setErrorData(Object value) {
		// Make sure the value is a collection
		if (value instanceof Collection) { // Set the value of the error data
			this.errorData = (Collection) value;
		}

		else { // Throw the invalid property type exception
			if (value == null)
				throw APIProcessingException.invalidPropertyType(ERROR_DATA, null);
			else
				throw APIProcessingException.invalidPropertyType(ERROR_DATA, value.getClass().getName());
		}
	}

	/**
	 * Changes a property, given its name and value.
	 *
	 * @param name
	 *            The property name.
	 * @param value
	 *            The property value.
	 * @exception APIProcessingException
	 *                An exception is thrown if the name is null or unknown.
	 */
	public void setProperty(String name, Object value) {
		// Reset the validity state and check for a null property name
		if (name == null) { // Throw the invalid property name exception
			throw APIProcessingException.invalidPropertyName(name);
		}

		else if (name.equalsIgnoreCase(ERROR_CODE)) { // Set the value of the
														// error code
			setErrorCode(getPropertyLong(name, value));
		}

		else if (name.equalsIgnoreCase(ERROR_DATA)) { // Set the value of the
														// error data
			setErrorData(value);
		}

		else if (name.equalsIgnoreCase(MESSAGE)) { // Set the value of the
													// message
			setMessage(value.toString());
		}

		else if (name.equalsIgnoreCase(BEAN_NAME)) {
			setBeanName(value.toString());
		}

		else if (name.equalsIgnoreCase(DETAIL_ERRORS)) { // Set the value of the
															// detail errors
			setDetailErrors(value);
		}

		else if (name.equalsIgnoreCase(PROPERTY_NAME)) { // Set the value of the
															// property name
			setPropertyName(value.toString());
		}

		else if (name.equalsIgnoreCase(PROPERTY_VALUE)) { // Set the value of
															// the property
															// value
			setPropertyValue(value.toString());
		}

		else if (name.equalsIgnoreCase(PROPERTY_TYPE)) { // Set the value of the
															// property type
			setPropertyType(value.toString());
		}

		else if (name.equalsIgnoreCase(PARAMETER_NAME)) { // Set the value of
															// the parameter
															// name
			setParameterName(value.toString());
		}

		else if (name.equalsIgnoreCase(PARAMETER_VALUE)) { // Set the value of
															// the parameter
															// value
			setParameterValue(value.toString());
		}

		else if (name.equalsIgnoreCase(PARAMETER_TYPE)) { // Set the value of
															// the parameter
															// type
			setParameterType(value.toString());
		}

		else { // Throw the invalid property name exception
			throw APIProcessingException.invalidPropertyName(name);
		}
	}

	/////////////////////////////////////////////////////////////////////
	//
	// The static methods
	//
	/////////////////////////////////////////////////////////////////////

	/**
	 * Converts an exception to a unique error code. It does this by: (a)
	 * looking up the base number for the class (integer multiple of 1000), (b)
	 * if it is a Kronos error, adding the per-class error code. The base number
	 * comes from a system property of the form:
	 *
	 * api.error.<class> = <code> where <class> is a prefix of the class name.
	 *
	 * @param e
	 *            The throwable exception.
	 * @return Returns the error code of the exception.
	 */
	public static long exceptionToErrorCode(Throwable e) {
		// Initialize the value and name
		String propValue = null;
		String propName = "api.error." + e.getClass().getName();

		// see if we get a hit...
		try {
			propValue = KronosProperties.getProperty(propName);
		} catch (WfpInstallationException x) {
			propValue = null;
		}

		// now form the base error code
		long errorCode = 0;
		if (propValue != null) {
			// we found the property - use the integer value
			try {
				errorCode = Long.valueOf(propValue).longValue();
			} catch (NumberFormatException x) {
			}
		}

		// if this is a Kronos exception, add its error code
		if (e instanceof GenericException) {
			int gErrorCode = ((GenericException) e).getErrorCode();
			if (gErrorCode < 0)
				// If the error code is negative, use the absolute value for
				// backwards
				// compatibility. Is this an issue with duplicate error codes?
				gErrorCode = -1 * gErrorCode;

			errorCode += gErrorCode;
		} else
			// For all other exceptions, return an error code of -1.
			// Some system exception occurred.
			errorCode = -1;

		// Return the error code to the caller
		return errorCode;
	}

	/**
	 * Log the API exception.
	 *
	 * @param exception
	 *            The exception thrown during the action.
	 */
	public static void logAPIException(Throwable exception) {
		// Log the exception
		Log.log(Log.API, Log.ERROR, exception);

		// If this is an API exception, check for wrapped exceptions.
		if (exception instanceof APIWrapExceptionIfc) { // If the exception can
														// have wrapped
														// exceptions, get them.
			Exception[] details = ((APIWrapExceptionIfc) exception).getWrappedExceptions();

			// Add the other wrapped exception to this one
			for (int i = 0; i < details.length; i++)
				logAPIException(details[i]);
		}
	}

	protected Long getPropertyLong(final String name, Object value) {
		if ((value instanceof Long) || (value == null)) {
			return (Long) value;
		} else {
			String text = getStringValue(value);
			if (text == null) {
				return null;
			} else {
				value = KServer.stringToLong(text);
				if (value == null) {
					throw APIProcessingException.invalidPropertyValue(name, text);
				}
				return (Long) value;
			}
		}
	}

	protected String getStringValue(final Object value) {
		// Convert the object to a string. The value is null when the trimmed
		// length is zero.
		String text = KServer.objectToString(value, true);
		if (text != null) { // Trim while space and check for a zero length
							// string
			text = text.trim();
			text = (text.length() == 0) ? null : text;
		}
		return text;
	}

	protected Integer getPropertyInteger(final String name, Object value) {
		if ((value instanceof Integer) || (value == null)) {
			return (Integer) value;
		} else {
			String text = getStringValue(value);
			if (text == null) {
				return null;
			} else {
				value = KServer.stringToInteger(text);
				if (value == null) {
					throw APIProcessingException.invalidPropertyValue(name, text);
				}
				return (Integer) value;
			}
		}
	}
}
