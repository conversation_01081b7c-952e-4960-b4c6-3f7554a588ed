package com.kronos.persons.rest.beans.extensions;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;


@Schema(description = "@v1.0.fulltimeequivalencydataextension.apimodel.description", name = "fullTimeEquivalency", allOf = ExtensionEffectiveDatedEntry.class)
@JsonPropertyOrder(alphabetic = true)
public class FullTimeEquivalencyDataExtension extends ExtensionEffectiveDatedEntry {
    public static final long serialVersionUID = 1L;

    @Schema(description = "@v1.0.fulltimeequivalencydataextension.apimodelproperty.employeestandardhoursquantity.description")
    private Double employeeStandardHoursQuantity;

    @Schema(description = "@v1.0.fulltimeequivalencydataextension.apimodelproperty.fulltimeequivalencyid.description")
    private Long fullTimeEquivalencyId;

    @Schema(description = "@v1.0.fulltimeequivalencydataextension.apimodelproperty.fulltimeequivalencypercent.description")
    private Double fullTimeEquivalencyPercent;

    @Schema(description = "@v1.0.fulltimeequivalencydataextension.apimodelproperty.fulltimestandardhoursquantity.description")
	private Double fullTimeStandardHoursQuantity;

	/**
	 * @return the employeeStandardHoursQuantity
	 */
	public Double getEmployeeStandardHoursQuantity() {
		return employeeStandardHoursQuantity;
	}

	/**
	 * @param employeeStandardHoursQuantity
	 *            the employeeStandardHoursQuantity to set
	 */
	public void setEmployeeStandardHoursQuantity(Double employeeStandardHoursQuantity) {
		this.employeeStandardHoursQuantity = employeeStandardHoursQuantity;
	}

	/**
	 * @return the fullTimeEquivalencyId
	 */
	public Long getFullTimeEquivalencyId() {
		return fullTimeEquivalencyId;
	}

	/**
	 * @param fullTimeEquivalencyId
	 *            the fullTimeEquivalencyId to set
	 */
	public void setFullTimeEquivalencyId(Long fullTimeEquivalencyId) {
		this.fullTimeEquivalencyId = fullTimeEquivalencyId;
	}

	/**
	 * @return the fullTimeEquivalencyPercent
	 */
	public Double getFullTimeEquivalencyPercent() {
		return fullTimeEquivalencyPercent;
	}

	/**
	 * @param fullTimeEquivalencyPercent
	 *            the fullTimeEquivalencyPercent to set
	 */
	public void setFullTimeEquivalencyPercent(Double fullTimeEquivalencyPercent) {
		this.fullTimeEquivalencyPercent = fullTimeEquivalencyPercent;
	}

	/**
	 * @return the fullTimeStandardHoursQuantity
	 */
	public Double getFullTimeStandardHoursQuantity() {
		return fullTimeStandardHoursQuantity;
	}

	/**
	 * @param fullTimeStandardHoursQuantity
	 *            the fullTimeStandardHoursQuantity to set
	 */
	public void setFullTimeStandardHoursQuantity(Double fullTimeStandardHoursQuantity) {
		this.fullTimeStandardHoursQuantity = fullTimeStandardHoursQuantity;
	}

}
