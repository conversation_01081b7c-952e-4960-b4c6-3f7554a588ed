/***********************************************************************
 * EmployeeSurrogate.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/

package com.kronos.persons.rest.model;

import java.util.Date;

import jakarta.inject.Named;

import com.kronos.commonbusiness.converter.model.ObjectId;
import com.kronos.commonbusiness.converter.utils.ObjectUtils;
import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.persons.rest.service.PersonRestServiceConstants;

/**
 * EmployeeSurrogate is a very light version of an employee.
 * <p>
 * It contains a minimal set of attributes to act in place of an employee. When
 * the info contained in the surrogate is not enough, a full employee has to be
 * retrieved from the com.kronos.wfc.services.employee service. The employee id
 * the surrogate replaces can be retrieve by a call to
 * <code>getEmployeeId()</code>.
 * <p>
 * The surrogate is not implemented as the proxy design pattern described in the
 * GoF book even if the goal is the same.
 *
 * <AUTHOR>
 */
@Named
public class EmployeeSurrogate implements Cloneable {

	/**
	 * The logger.
	 */
	private static final KLogger LOGGER = KLoggerFactory.getKLogger(EmployeeSurrogate.class);

	/**
	 * The employeeID.
	 */
	private ObjectId employeeId;

	/**
	 * The personNumber as a string.
	 */
	private String personNumber;

	/**
	 * The short name.
	 */
	private String shortName;

	/**
	 * The full name.
	 */
	private String fullName;

	/** Employee's currency locale. */
	private String currencySymbol;

	/**
	 * boolean value for wfsLicensed.
	 */
	private boolean wfsLicensed;

	/**
	 * the snapshotDate
	 */
	private Date snapshotDateUTC;

	/**
	 * The String containing the photoId.
	 */
	private String photoId;

	/**
	 * Default Constructor
	 */
	public EmployeeSurrogate() {

	}

	/**
	 * Constructor with objectId initialization.
	 * @param objectId
	 */
	public EmployeeSurrogate(ObjectId objectId) {
		employeeId = objectId;
	}

	/**
	 * Gets the identifier of the Employee the surrogate acts for
	 *
	 * @return the the identifier of the employee the surrogate acts for. Cannot
	 *         be null.
	 * @exception IllegalStateException
	 *                if employeeId is not initialized
	 */
	public ObjectId getEmployeeId() {
		return employeeId;
	}

	/**
	 * Gets the identifier of the person number the surrogate acts for
	 *
	 * @return the the identifier of the person the surrogate acts for. Cannot
	 *         be null.
	 * @exception IllegalStateException
	 *                if personNumber is not initialized
	 */
	public String getPersonNumber() {
		return personNumber;
	}

	/**
	 * @return Returns the shortName.
	 */
	public String getShortName() {
		return shortName;
	}

	/**
	 * @return Returns the fullName.
	 */
	public String getFullName() {
		return fullName;
	}

	/**
	 * Gets the identifier of the Employee the surrogate acts for
	 *
	 * @return the the identifier of the employee the surrogate acts for. Cannot
	 *         be null.
	 * @exception IllegalArgumentException
	 *                if employeeId is null
	 */
	public void setEmployeeId(ObjectId id) {
		this.employeeId = id;
	}

	/**
	 * Gets the number of the person the surrogate acts for
	 *
	 * @return the number of the person the surrogate acts for. Cannot be null.
	 * @exception IllegalArgumentException
	 *                if personNumber is null
	 */
	public void setPersonNumber(String personNumber) {
		this.personNumber = personNumber;
	}

	/**
	 * @param shortName
	 *            The shortName to set.
	 */
	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	/**
	 * @param fullName
	 *            The fullName to set.
	 */
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	/**
	 * @return The symbol for currency
	 */
	public String getCurrencySymbol() {
		return currencySymbol;
	}

	/**
	 * @param currencySymbol String containing the currency symbol
	 */
	public void setCurrencySymbol(String currencySymbol) {
		this.currencySymbol = currencySymbol;
	}

	/** Avoid serializing this to XML */
	public boolean getWfsLicensed() {
		return wfsLicensed;
	}

	/**
	 * @param wfsLicensed boolean containing the wfsLicensed value
	 */
	public void setWfsLicensed(boolean wfsLicensed) {
		this.wfsLicensed = wfsLicensed;
	}

	public Date getSnapshotDateUTC() {
		return snapshotDateUTC;
	}

	/**
	 * @param snapshotDateUTC Date object
	 */
	public void setSnapshotDateUTC(Date snapshotDateUTC) {
		this.snapshotDateUTC = snapshotDateUTC;
	}

	/**
	 * @return String containing the photoId
	 */
	public String getPhotoId() {
		return photoId;
	}

	/**
	 * @param photoId sets the photoId
	 */
	public void setPhotoId(String photoId) {
		this.photoId = photoId;
	}

	
	/* (non-Javadoc)
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + getCurrencySymbolHashCode();
		result = prime * result + getEmployeeIdHashCode();
		result = prime * result + getFullNameHashCode();
		result = prime * result + getPersonNumberHashCode();
		result = prime * result + getShortNameHashCode();
		result = prime * result + getWFSLicensedHashCode();
		result = prime * result + getSnapshotDateHashCode();
		result = prime * result + getPhotoIdHashCode();
		return result;
	}

	protected int getPhotoIdHashCode() {
		return (photoId == null) ? 0 : photoId.hashCode();
	}

	protected int getSnapshotDateHashCode() {
		return (snapshotDateUTC == null) ? 0 : snapshotDateUTC.hashCode();
	}

	protected int getWFSLicensedHashCode() {
		return wfsLicensed ? 1231 : 1237;
	}

	protected int getShortNameHashCode() {
		return (shortName == null) ? 0 : shortName.hashCode();
	}

	protected int getPersonNumberHashCode() {
		return (personNumber == null) ? 0 : personNumber.hashCode();
	}

	protected int getFullNameHashCode() {
		return (fullName == null) ? 0 : fullName.hashCode();
	}

	protected int getEmployeeIdHashCode() {
		return (employeeId == null) ? 0 : employeeId.hashCode();
	}

	protected int getCurrencySymbolHashCode() {
		return (currencySymbol == null) ? 0 : currencySymbol.hashCode();
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		EmployeeSurrogate other = (EmployeeSurrogate) obj;
		return equalityCheck(other) && equalityWFSLicensedPrimaryJobCheck(other) && equalitySnapshotPhotoidCheck(other);
	}
	
	boolean equalityCheck(EmployeeSurrogate other){
		return equalityEmployeeidFullnamePersonnumberCheck(other) && equalityPrimaryjobShortNameCurrencyCheck(other);
	}

	boolean equalityEmployeeidFullnamePersonnumberCheck(EmployeeSurrogate other){
		return ObjectUtils.equalsOfObjects(employeeId, other.employeeId) && ObjectUtils.equalsOfObjects(fullName, other.fullName)
				&& ObjectUtils.equalsOfObjects(personNumber, other.personNumber);
	}
	
	boolean equalityPrimaryjobShortNameCurrencyCheck(EmployeeSurrogate other){
		return ObjectUtils.equalsOfObjects(shortName, other.shortName) && ObjectUtils.equalsOfObjects(currencySymbol, other.currencySymbol);
	}
	
	boolean equalityWFSLicensedPrimaryJobCheck(EmployeeSurrogate other){
		return ObjectUtils.equalsOfObjects(wfsLicensed, other.wfsLicensed);
	}
	
	boolean equalitySnapshotPhotoidCheck(EmployeeSurrogate other){
		return ObjectUtils.equalsOfObjects(snapshotDateUTC, other.snapshotDateUTC) && ObjectUtils.equalsOfObjects(photoId, other.photoId);
	}
	/**
	 * @return object clone
	 * @see java.lang.Object#clone()
	 */
	@Override
	public EmployeeSurrogate clone() {
		EmployeeSurrogate newInst = null;
		try {
			newInst = (EmployeeSurrogate) super.clone();
			if (employeeId != null) {
				newInst.setEmployeeId(employeeId.clone());
			}
			newInst.setFullName(fullName);
			newInst.setPersonNumber(personNumber);
			newInst.setShortName(shortName);
			newInst.setCurrencySymbol(currencySymbol);
			newInst.setWfsLicensed(wfsLicensed);
			newInst.setSnapshotDateUTC(snapshotDateUTC);
			if (photoId != null) {
				newInst.setPhotoId(photoId);
			}
		} catch (CloneNotSupportedException exception) {
			LOGGER.error(PersonRestServiceConstants.ERROR_CLONING_EMPLOYEESURROGATE_MESSAGE);
			throw new APIException(PersonRestServiceConstants.ERROR_WHILE_CLONING, exception);
		}
		return newInst;
	}

	/**
	 * @return representation as String
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("EmployeeSurrogate [employeeId=");
		builder.append(employeeId);
		builder.append(", fullName=");
		builder.append(fullName);
		builder.append(", personNumber=");
		builder.append(personNumber);
		builder.append(", shortName=");
		builder.append(shortName);
		builder.append(", currencySymbol=");
		builder.append(currencySymbol);
		builder.append(", wfsLicensed=");
		builder.append(wfsLicensed);
		builder.append(", snapshotDateUTC=");
		builder.append(snapshotDateUTC);
		builder.append(", photoId=");
		builder.append(photoId);
		builder.append("]");
		return builder.toString();
	}

}
