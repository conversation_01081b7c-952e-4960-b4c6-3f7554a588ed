package com.kronos.persons.rest.exception;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.access.SpringContext;
import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.persons.rest.model.RestErrorBean;
import com.kronos.wfc.commonapp.people.business.personality.PersonalityException;
import com.kronos.wfc.platform.datetime.framework.KServer;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.logging.framework.Log;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class PrsnException {
	
	private static final String MSSG_SUFFIX = ".MESSAGE";
	private static final String AMS_REST_ERROR_CODE = "WCO-101561";
	private static final String AMS_USER_DEFINED_ERROR_CODE = "WCO-101563";
	
	private static final String ERROR = "EXCEPTION : ";
	
	private static final KLogger LOGGER = KLoggerFactory.getKLogger(PrsnException.class);
	
	public static APIException getAPIException(RestErrorBean restError){
		APIException apiEx = null;
    	List<RestErrorBean> detailErrors = restError.getDetailErrors();
    	if (detailErrors != null && detailErrors.size() >0){
    		PrsnInvalidBeanException invalidBean = new PrsnInvalidBeanException();
    		for (RestErrorBean rError : detailErrors){
    			APIException detailEx = new APIException(ExceptionConstants.BUSINESS_EXCEPTION);
    			detailEx.addUserParameter("errorMessage", rError.getMessage());
    			invalidBean.addApiExceptionToList(detailEx);
    		}
    		apiEx = invalidBean.getApiException();
    	} else {
    		apiEx = new APIException(ExceptionConstants.BUSINESS_EXCEPTION);
        	apiEx.addUserParameter("errorMessage", restError.getMessage());
    	}
    	return apiEx;
	}
	
	public static APIException getAPIException(GenericException genExp){
		if (PersonalityException.UNKNOWN_UMS_ERROR == genExp.getErrorCode()) {
			genExp = new PersonalityException(PersonalityException.UNKNOWN_UMS_CLIENT_ERROR);
		}

		return getNoOverrdieAPIException(genExp);
	}

	public static APIException getNoOverrdieAPIException(GenericException genExp) {
		int errorCode = genExp.getErrorCode();
		String fullErrorCodeName = genExp.fullErrorCodeName();

		Log.log(Log.API, 4, "GenericException to APIException : before, error code :" + errorCode + "error message : ", fullErrorCodeName);

		if (errorCode > 100)
			errorCode = errorCode % 100;

		int wcoErrorCode = 101500 + errorCode;

		IKProperties propertyService = loadPropertyService();

		String errorMssg = propertyService.get(fullErrorCodeName);

		String newErrorCode = "WCO-"+ wcoErrorCode;
		propertyService.setProperty(newErrorCode+MSSG_SUFFIX, errorMssg);

		Log.log(Log.API, 4, "GenericException to APIException : after, error code :" + wcoErrorCode + "error message : ", propertyService.get(newErrorCode));


		//create & populate APIException
		APIException newAPIEx = new APIException(newErrorCode);
		Map<String, String> userParamMap = genExp.getUserParameters();
		for (Map.Entry<String, String> entry : userParamMap.entrySet()){
			newAPIEx.addUserParameter(entry.getKey(), KServer.objectToDisplay(entry.getValue()));
		}

		return newAPIEx;
	}

	private static IKProperties loadPropertyService() {
    	return SpringContext.getBean(IKProperties.class);
	}

	public static APIException getAPIException(Exception e) {
		LOGGER.error(ERROR, e);
		if(e instanceof APIException){
			return (APIException)e;
		} else if(e instanceof GenericException) {
			return  PrsnException.getAPIException((GenericException)e);
		}
		return new APIException(ExceptionConstants.EXCEPTION_MESSAGE);
	}

	public static APIException getAmsRestSideError(String error) {
		APIException exception = new APIException(AMS_REST_ERROR_CODE);
		exception.addUserParameter("errorMessage", error);
		return exception;
	}

	public static APIException getAmsUserDefinedError(String error) {
		APIException exception = new APIException(AMS_USER_DEFINED_ERROR_CODE);
		exception.addUserParameter("errorMessage", error);
		return exception;
	}
}
