package com.kronos.persons.rest.assignments.model.brazilcompliance;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CompanyAttributesRest {

	Long id;
	private String qualifier;
	private String identifier;
	private ObjectRef identityType;
	private String cei;
	private String location;
	private ObjectRef deviceGroup;
	private String caepf;
	private String cno;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getQualifier() {
		return qualifier;
	}

	public void setQualifier(String qualifier) {
		this.qualifier = qualifier;
	}

	public String getIdentifier() {
		return identifier;
	}

	public void setIdentifier(String identifier) {
		this.identifier = identifier;
	}

	public ObjectRef getIdentityType() {
		return identityType;
	}

	public void setIdentityType(ObjectRef identityType) {
		this.identityType = identityType;
	}

	public String getCei() {
		return cei;
	}

	public void setCei(String cei) {
		this.cei = cei;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public ObjectRef getDeviceGroup() {
		return deviceGroup;
	}

	public void setDeviceGroup(ObjectRef deviceGroup) {
		this.deviceGroup = deviceGroup;
	}

	public String getCaepf() {
		return caepf;
	}

	public void setCaepf(String caepf) {
		this.caepf = caepf;
	}

	public String getCno() {
		return cno;
	}

	public void setCno(String cno) {
		this.cno = cno;
	}
}