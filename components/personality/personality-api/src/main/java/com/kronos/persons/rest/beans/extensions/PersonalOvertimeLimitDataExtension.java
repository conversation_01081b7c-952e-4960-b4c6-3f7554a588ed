package com.kronos.persons.rest.beans.extensions;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;


@Schema(description = "@v1.0.personalovertimelimitdataextension.apimodel.description", name = "personalOvertimeLimit")
public class PersonalOvertimeLimitDataExtension implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "@v1.0.personalovertimelimitdataextension.apimodelproperty.amount.description")
    private String amount;

    @Schema(description = "@v1.0.personalovertimelimitdataextension.apimodelproperty.minimumamount.description")
    private String minimumAmount;

    @Schema(description = "@v1.0.personalovertimelimitdataextension.apimodelproperty.personalovertimeamounttype.description")
    private String personalOvertimeAmountType;

    @Schema(description = "@v1.0.personalovertimelimitdataextension.apimodelproperty.personalovertimeamounttypeid.description")
	private Long personalOvertimeAmountTypeId;

	/**
	 * @return the amount
	 */
	public String getAmount() {
		return amount;
	}

	/**
	 * @param amount
	 *            the amount to set
	 */
	public void setAmount(String amount) {
		this.amount = amount;
	}

	/**
	 * @return the minimumAmount
	 */
	public String getMinimumAmount() {
		return minimumAmount;
	}

	/**
	 * @param minimumAmount
	 *            the minimumAmount to set
	 */
	public void setMinimumAmount(String minimumAmount) {
		this.minimumAmount = minimumAmount;
	}

	/**
	 * @return the personalOvertimeAmountType
	 */
	public String getPersonalOvertimeAmountType() {
		return personalOvertimeAmountType;
	}

	/**
	 * @param personalOvertimeAmountType
	 *            the personalOvertimeAmountType to set
	 */
	public void setPersonalOvertimeAmountType(String personalOvertimeAmountType) {
		this.personalOvertimeAmountType = personalOvertimeAmountType;
	}

	/**
	 * @return the personalOvertimeAmountTypeId
	 */
	public Long getPersonalOvertimeAmountTypeId() {
		return personalOvertimeAmountTypeId;
	}

	/**
	 * @param personalOvertimeAmountTypeId
	 *            the personalOvertimeAmountTypeId to set
	 */
	public void setPersonalOvertimeAmountTypeId(Long personalOvertimeAmountTypeId) {
		this.personalOvertimeAmountTypeId = personalOvertimeAmountTypeId;
	}

}
