package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignment;
import com.kronos.wfc.commonapp.people.business.person.group.employment.EmploymentTermAssignment;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */

@Schema(description = "@v1.0.employmenttermbean.apimodel.description", name = "employmentTerm")
@JsonPropertyOrder(alphabetic = true)
public class EmploymentTermBean implements IPersonGroupBean, Serializable {
    @Schema(description = "@v1.0.employmenttermbean.apimodelproperty.name.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String name;

    @Schema(description = "@v1.0.employmenttermbean.apimodelproperty.startdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String startDate;

    @Schema(description = "@v1.0.employmenttermbean.apimodelproperty.enddate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String endDate;

    @Schema(description = "@v1.0.employmenttermbean.apimodelproperty.originalstartdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String originalStartDate;

	/**
	 * The EmploymentTerm ID of the EmploymentTerm which is named in this
	 * assignment bean
	 */
	@JsonIgnore
	private ObjectIdLong employmentTermId = null;

	/**
	 * The EmploymentTermAssignment that is being edited (only used in the case
	 * where this bean represents an edit of an existing assignment)
	 */
	@JsonIgnore
	private EmploymentTermAssignment assignmentBeingEdited = null;

	/**
	 * The JobAssignment in which this EmploymentTermAssignment resides
	 */
	@JsonIgnore
	private JobAssignment jobAssignment = null;

	@JsonIgnore
	private ActionType actionType = ActionType.UNKNOWN;

	/**
	 * @return the name
	 */
	@Override
	public String getName() {
		return name;
	}

	/**
	 * @param name
	 *            the name to set
	 */
	@Override
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * @return the startDate
	 */
	@Override
	public String getStartDate() {
		return startDate;
	}

	/**
	 * @param startDate
	 *            the startDate to set
	 */
	@Override
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	/**
	 * @return the endDate
	 */
	@Override
	public String getEndDate() {
		return endDate;
	}

	/**
	 * @param endDate
	 *            the endDate to set
	 */
	@Override
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	/**
	 * @return the originalStartDate
	 */
	@Override
	public String getOriginalStartDate() {
		return originalStartDate;
	}

	/**
	 * @param originalStartDate
	 *            the originalStartDate to set
	 */
	@Override
	public void setOriginalStartDate(String originalStartDate) {
		this.originalStartDate = originalStartDate;
	}

	/**
	 * @return the employmentTermId
	 */
	public ObjectIdLong getEmploymentTermId() {
		return employmentTermId;
	}

	/**
	 * @param employmentTermId
	 *            the employmentTermId to set
	 */
	public void setEmploymentTermId(ObjectIdLong employmentTermId) {
		this.employmentTermId = employmentTermId;
	}

	/**
	 * @return the assignmentBeingEdited
	 */
	public EmploymentTermAssignment getAssignmentBeingEdited() {
		return assignmentBeingEdited;
	}

	/**
	 * @param assignmentBeingEdited
	 *            the assignmentBeingEdited to set
	 */
	public void setAssignmentBeingEdited(
			EmploymentTermAssignment assignmentBeingEdited) {
		this.assignmentBeingEdited = assignmentBeingEdited;
	}

	/**
	 * @return the jobAssignment
	 */
	public JobAssignment getJobAssignment() {
		return jobAssignment;
	}

	/**
	 * @param jobAssignment
	 *            the jobAssignment to set
	 */
	public void setJobAssignment(JobAssignment jobAssignment) {
		this.jobAssignment = jobAssignment;
	}

	/**
	 * @return the actionType
	 */
	@Override
	public ActionType getActionType() {
		return actionType;
	}

	/**
	 * @param actionType
	 *            the actionType to set
	 */
	@Override
	public void setActionType(ActionType actionType) {
		this.actionType = actionType;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;

		if (o == null || getClass() != o.getClass()) return false;

		EmploymentTermBean bean = (EmploymentTermBean) o;

		return new EqualsBuilder().append(name, bean.name)
				.append(startDate, bean.startDate)
				.append(endDate, bean.endDate)
				.append(originalStartDate, bean.originalStartDate)
				.append(employmentTermId, bean.employmentTermId)
				.append(assignmentBeingEdited, bean.assignmentBeingEdited)
				.append(jobAssignment, bean.jobAssignment)
				.append(actionType, bean.actionType)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37)
				.append(name)
				.append(startDate)
				.append(endDate)
				.append(originalStartDate)
				.append(employmentTermId)
				.append(assignmentBeingEdited)
				.append(jobAssignment)
				.append(actionType)
				.toHashCode();
	}
}
