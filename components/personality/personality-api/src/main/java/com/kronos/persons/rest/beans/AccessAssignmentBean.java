package com.kronos.persons.rest.beans;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.accessassignmentbean.apimodel.description", name = "CommonBusinessAccessAssignment")
@JsonPropertyOrder(alphabetic = true)
public class AccessAssignmentBean {
    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.accessprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accessProfileName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.availabilitypatternname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String availabilityPatternName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.delegateprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String delegateProfileName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.effectivedatedtimeentrymethods.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<EffectiveDatedTimeEntryMethodBean> effectiveDatedTimeEntryMethods;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.employeelaborcategoryprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String employeeLaborCategoryProfileName;
    
    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.unassignemployeelaborcategoryprofile.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean unassignEmployeeLaborCategoryProfile;

	@Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.mgrempllaborcategoryprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String mgrEmplLaborCategoryProfileName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.unassignmgrempllaborcategoryprofile.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean unassignMgrEmplLaborCategoryProfile;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.groupschedulename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String groupScheduleName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.guidedactionprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String guidedActionProfileName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.hyperfindschedulevisibilityname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String hyperFindScheduleVisibilityName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.localepolicyname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String localePolicyName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.managerlaborcategoryprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String managerLaborCategoryProfileName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.unassignmanagerlaborcategoryprofile.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean unassignManagerLaborCategoryProfile;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.managerpaycodename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String managerPayCodeName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.managerviewpaycodename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String managerViewPayCodeName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.managerworkrulename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String managerWorkRuleName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.notificationprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String notificationProfileName;
    
    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.accessmethodprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String accessMethodProfileName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.preferenceprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String preferenceProfileName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.professionalpaycodename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String professionalPayCodeName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.professionalworkrulename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String professionalWorkRuleName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.reportname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String reportName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.schedulepatternname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String schedulePatternName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.shiftcodename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String shiftCodeName;

	@Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.professionalShiftCodeName.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String professionalShiftCodeName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.timeentrytypeeffectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String timeEntryTypeEffectiveDate;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.timeentrytypename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String timeEntryTypeName;

	@Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.timeentryactivitytrackingstatus.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String timeEntryActivityTrackingStatus;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.transferemployeeflag.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean transferEmployeeFlag;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.approveovertimeflag.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Boolean approveOvertimeflag;
    
    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.forecastingcategoryprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String forecastingCategoryProfileName;

    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.approvalmethodname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String approvalMethodName;

    @JsonProperty(access = Access.WRITE_ONLY)
    @Schema(description = "@v1.0.accessassignmentbean.apimodelproperty.preservetrackingstatus.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean preserveTrackingStatus;

	/**
	 * @return the accessProfileName
	 */
	public String getAccessProfileName() {
		return accessProfileName;
	}

	/**
	 * @param accessProfileName
	 *            the accessProfileName to set
	 */
	public void setAccessProfileName(String accessProfileName) {
		this.accessProfileName = accessProfileName;
	}

	/**
	 * @return the availabilityPatternName
	 */
	public String getAvailabilityPatternName() {
		return availabilityPatternName;
	}

	/**
	 * @param availabilityPatternName
	 *            the availabilityPatternName to set
	 */
	public void setAvailabilityPatternName(String availabilityPatternName) {
		this.availabilityPatternName = availabilityPatternName;
	}

	/**
	 * @return the delegateProfileName
	 */
	public String getDelegateProfileName() {
		return delegateProfileName;
	}

	/**
	 * @param delegateProfileName
	 *            the delegateProfileName to set
	 */
	public void setDelegateProfileName(String delegateProfileName) {
		this.delegateProfileName = delegateProfileName;
	}

	/**
	 * @return the effectiveDatedTimeEntryMethods
	 */
	public List<EffectiveDatedTimeEntryMethodBean> getEffectiveDatedTimeEntryMethods() {
		return effectiveDatedTimeEntryMethods;
	}

	/**
	 * @param effectiveDatedTimeEntryMethods
	 *            the effectiveDatedTimeEntryMethods to set
	 */
	public void setEffectiveDatedTimeEntryMethods(
			List<EffectiveDatedTimeEntryMethodBean> effectiveDatedTimeEntryMethods) {
		this.effectiveDatedTimeEntryMethods = effectiveDatedTimeEntryMethods;
	}

	/**
	 * @return the employeeLaborCategoryProfileName
	 */
	public String getEmployeeLaborCategoryProfileName() {
		return employeeLaborCategoryProfileName;
	}

	/**
	 * @param employeeLaborCategoryProfileName
	 *            the employeeLaborCategoryProfileName to set
	 */
	public void setEmployeeLaborCategoryProfileName(
			String employeeLaborCategoryProfileName) {
		this.employeeLaborCategoryProfileName = employeeLaborCategoryProfileName;
	}

	/**
	 * @return the groupScheduleName
	 */
	public String getGroupScheduleName() {
		return groupScheduleName;
	}

	/**
	 * @param groupScheduleName
	 *            the groupScheduleName to set
	 */
	public void setGroupScheduleName(String groupScheduleName) {
		this.groupScheduleName = groupScheduleName;
	}

	/**
	 * @return the guidedActionProfileName
	 */
	public String getGuidedActionProfileName() {
		return guidedActionProfileName;
	}

	/**
	 * @param guidedActionProfileName
	 *            the guidedActionProfileName to set
	 */
	public void setGuidedActionProfileName(String guidedActionProfileName) {
		this.guidedActionProfileName = guidedActionProfileName;
	}

	/**
	 * @return the hyperFindScheduleVisibilityName
	 */
	public String getHyperFindScheduleVisibilityName() {
		return hyperFindScheduleVisibilityName;
	}

	/**
	 * @param hyperFindScheduleVisibilityName
	 *            the hyperFindScheduleVisibilityName to set
	 */
	public void setHyperFindScheduleVisibilityName(
			String hyperFindScheduleVisibilityName) {
		this.hyperFindScheduleVisibilityName = hyperFindScheduleVisibilityName;
	}

	/**
	 * @return the localePolicyName
	 */
	public String getLocalePolicyName() {
		return localePolicyName;
	}

	/**
	 * @param localePolicyName
	 *            the localePolicyName to set
	 */
	public void setLocalePolicyName(String localePolicyName) {
		this.localePolicyName = localePolicyName;
	}

	/**
	 * @return the managerLaborCategoryProfileName
	 */
	public String getManagerLaborCategoryProfileName() {
		return managerLaborCategoryProfileName;
	}

	/**
	 * @param managerLaborCategoryProfileName
	 *            the managerLaborCategoryProfileName to set
	 */
	public void setManagerLaborCategoryProfileName(
			String managerLaborCategoryProfileName) {
		this.managerLaborCategoryProfileName = managerLaborCategoryProfileName;
	}

	/**
	 * @return the managerPayCodeName
	 */
	public String getManagerPayCodeName() {
		return managerPayCodeName;
	}

	/**
	 * @param managerPayCodeName
	 *            the managerPayCodeName to set
	 */
	public void setManagerPayCodeName(String managerPayCodeName) {
		this.managerPayCodeName = managerPayCodeName;
	}

	/**
	 * @return the managerViewPayCodeName
	 */
	public String getManagerViewPayCodeName() {
		return managerViewPayCodeName;
	}

	/**
	 * @param managerViewPayCodeName
	 *            the managerViewPayCodeName to set
	 */
	public void setManagerViewPayCodeName(String managerViewPayCodeName) {
		this.managerViewPayCodeName = managerViewPayCodeName;
	}

	/**
	 * @return the managerWorkRuleName
	 */
	public String getManagerWorkRuleName() {
		return managerWorkRuleName;
	}

	/**
	 * @param managerWorkRuleName
	 *            the managerWorkRuleName to set
	 */
	public void setManagerWorkRuleName(String managerWorkRuleName) {
		this.managerWorkRuleName = managerWorkRuleName;
	}

	/**
	 * @return the notificationProfileName
	 */
	public String getNotificationProfileName() {
		return notificationProfileName;
	}

	/**
	 * @param notificationProfileName
	 *            the notificationProfileName to set
	 */
	public void setNotificationProfileName(String notificationProfileName) {
		this.notificationProfileName = notificationProfileName;
	}

	/**
	 *
	 * @return
	 */
	public String getAccessMethodProfileName() {
		return accessMethodProfileName;
	}

	/**
	 *
	 * @param accessMethodProfileName
	 */
	public void setAccessMethodProfileName(String accessMethodProfileName) {
		this.accessMethodProfileName = accessMethodProfileName;
	}

	/**
	 * @return the preferenceProfileName
	 */
	public String getPreferenceProfileName() {
		return preferenceProfileName;
	}

	/**
	 * @param preferenceProfileName
	 *            the preferenceProfileName to set
	 */
	public void setPreferenceProfileName(String preferenceProfileName) {
		this.preferenceProfileName = preferenceProfileName;
	}

	/**
	 * @return the professionalPayCodeName
	 */
	public String getProfessionalPayCodeName() {
		return professionalPayCodeName;
	}

	/**
	 * @param professionalPayCodeName
	 *            the professionalPayCodeName to set
	 */
	public void setProfessionalPayCodeName(String professionalPayCodeName) {
		this.professionalPayCodeName = professionalPayCodeName;
	}

	/**
	 * @return the professionalWorkRuleName
	 */
	public String getProfessionalWorkRuleName() {
		return professionalWorkRuleName;
	}

	/**
	 * @param professionalWorkRuleName
	 *            the professionalWorkRuleName to set
	 */
	public void setProfessionalWorkRuleName(String professionalWorkRuleName) {
		this.professionalWorkRuleName = professionalWorkRuleName;
	}

	/**
	 * @return the reportName
	 */
	public String getReportName() {
		return reportName;
	}

	/**
	 * @param reportName
	 *            the reportName to set
	 */
	public void setReportName(String reportName) {
		this.reportName = reportName;
	}

	/**
	 * @return the schedulePatternName
	 */
	public String getSchedulePatternName() {
		return schedulePatternName;
	}

	/**
	 * @param schedulePatternName
	 *            the schedulePatternName to set
	 */
	public void setSchedulePatternName(String schedulePatternName) {
		this.schedulePatternName = schedulePatternName;
	}

	/**
	 * @return the shiftCodeName
	 */
	public String getShiftCodeName() {
		return shiftCodeName;
	}

	/**
	 * @param shiftCodeName
	 *            the shiftCodeName to set
	 */
	public void setShiftCodeName(String shiftCodeName) {
		this.shiftCodeName = shiftCodeName;
	}

	/**
	 * @return the professionalShiftCodeName
	 */
	public String getProfessionalShiftCodeName() {
		return professionalShiftCodeName;
	}

	/**
	 * @param professionalShiftCodeName
	 *            the professionalShiftCodeName to set
	 */
	public void setProfessionalShiftCodeName(String professionalShiftCodeName) {
		this.professionalShiftCodeName = professionalShiftCodeName;
	}

	/**
	 * @return the timeEntryTypeEffectiveDate
	 */
	public String getTimeEntryTypeEffectiveDate() {
		return timeEntryTypeEffectiveDate;
	}

	/**
	 * @param timeEntryTypeEffectiveDate
	 *            the timeEntryTypeEffectiveDate to set
	 */
	public void setTimeEntryTypeEffectiveDate(
			String timeEntryTypeEffectiveDate) {
		this.timeEntryTypeEffectiveDate = timeEntryTypeEffectiveDate;
	}

	/**
	 * @return the timeEntryTypeName
	 */
	public String getTimeEntryTypeName() {
		return timeEntryTypeName;
	}

	/**
	 * @param timeEntryTypeName
	 *            the timeEntryTypeName to set
	 */
	public void setTimeEntryTypeName(String timeEntryTypeName) {
		this.timeEntryTypeName = timeEntryTypeName;
	}



	/**
	 * @return timeEntryActivityTrackingStatus
	 */
	public String getTimeEntryActivityTrackingStatus(){
		return timeEntryActivityTrackingStatus;
	}

	/*
	 * @param timeEntryActivityTrackingStatus
	*/

	public void setTimeEntryActivityTrackingStatus(String timeEntryActivityTrackingStatus) {
		this.timeEntryActivityTrackingStatus = timeEntryActivityTrackingStatus;
	}


	/**
	 * @return the transferEmployeeFlag
	 */
	public Boolean getTransferEmployeeFlag() {
		return transferEmployeeFlag;
	}

	/**
	 * @param transferEmployeeFlag
	 *            the transferEmployeeFlag to set
	 */
	public void setTransferEmployeeFlag(Boolean transferEmployeeFlag) {
		this.transferEmployeeFlag = transferEmployeeFlag;
	}

	/**
	 * @return the approveOvertimeflag
	 */
	public Boolean getApproveOvertimeflag() {
		return approveOvertimeflag;
	}

	/**
	 * @param approveOvertimeflag
	 *            the approveOvertimeflag to set
	 */
	public void setApproveOvertimeflag(Boolean approveOvertimeflag) {
		this.approveOvertimeflag = approveOvertimeflag;
	}

	/**
     * @return the forecastingCategoryProfileName
     */
    public String getForecastingCategoryProfileName() {
        return forecastingCategoryProfileName;
    }
    
    /**
     * @param forecastingCategoryProfileName
     *            the forecastingCategoryProfileName to set
     */
    public void setForecastingCategoryProfileName(String forecastingCategoryProfileName) {
        this.forecastingCategoryProfileName = forecastingCategoryProfileName;
    }

	public String getMgrEmplLaborCategoryProfileName() {
		return mgrEmplLaborCategoryProfileName;
	}

	public void setMgrEmplLaborCategoryProfileName(String mgrEmplLaborCategoryProfileName) {
		this.mgrEmplLaborCategoryProfileName = mgrEmplLaborCategoryProfileName;
	}

	public Boolean getUnassignEmployeeLaborCategoryProfile() {
		return unassignEmployeeLaborCategoryProfile;
	}

	public void setUnassignEmployeeLaborCategoryProfile(Boolean unassignEmployeeLaborCategoryProfile) {
		this.unassignEmployeeLaborCategoryProfile = unassignEmployeeLaborCategoryProfile;
	}

	public Boolean getUnassignManagerLaborCategoryProfile() {
		return unassignManagerLaborCategoryProfile;
	}

	public void setUnassignManagerLaborCategoryProfile(Boolean unassignManagerLaborCategoryProfile) {
		this.unassignManagerLaborCategoryProfile = unassignManagerLaborCategoryProfile;
	}

    public Boolean getUnassignMgrEmplLaborCategoryProfile() {
    	return unassignMgrEmplLaborCategoryProfile;
    }

    public void setUnassignMgrEmplLaborCategoryProfile(Boolean unassignMgrEmplLaborCategoryProfile) {
    	this.unassignMgrEmplLaborCategoryProfile = unassignMgrEmplLaborCategoryProfile;
    }

    public Boolean getPreserveTrackingStatus() {
        return preserveTrackingStatus;
    }

    public void setPreserveTrackingStatus(Boolean preserveTrackingStatus) {
        this.preserveTrackingStatus = preserveTrackingStatus;
    }

    /**
     *
     * @return the approvalMethodName.
     */
    public String getApprovalMethodName() {
        return approvalMethodName;
    }

    /**
     *
     * @param approvalMethodName the a approvalMethodName to set.
     */
    public void setApprovalMethodName(String approvalMethodName) {
        this.approvalMethodName = approvalMethodName;
    }

}
