package com.kronos.persons.rest.assignments.model;

import com.kronos.persons.rest.beans.PersonIdentityBean;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "processorToEmployeeCriteria", description = "@v1.0.processortoemployeecriteria.apimodel.description")
public class ProcessorToEmployeeCriteriaDTO implements RuleCriteriaBean {

    @Schema(description = "@v1.0.processortoemployeecriteria.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String effectiveDate;

    @Schema(description = "@v1.0.processortoemployeecriteria.apimodelproperty.personidentity.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private PersonIdentityBean personIdentity;

    @Schema(description = "@v1.0.processortoemployeecriteria.apimodelproperty.processor.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String processor;

    public String getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public PersonIdentityBean getPersonIdentity() {
        return personIdentity;
    }

    public void setPersonIdentity(PersonIdentityBean personIdentity) {
        this.personIdentity = personIdentity;
    }

    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }
}
