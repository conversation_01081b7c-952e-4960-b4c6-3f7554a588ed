package com.kronos.persons.rest.service;

import com.kronos.persons.rest.model.PayCodeTag;

/**
 * IPayCodeTagFindService
 *
 * This is the interface to find PayCodeTags
 *
 * Copyright (C) 2021 UKG.com
 * Date: December 9, 2021
 *
 * <AUTHOR>
 */
public interface IPayCodeTagFindService {

    /**
     * Find payCode tags by id
     * @param id - id to find payCode tag
     * @return payCode tag
     */
    PayCodeTag findById(Long id);

    /**
     * Find payCode tags by name
     * @param name - name to find payCode tag
     * @return payCode tag
     */
    PayCodeTag findByName(String name);
}
