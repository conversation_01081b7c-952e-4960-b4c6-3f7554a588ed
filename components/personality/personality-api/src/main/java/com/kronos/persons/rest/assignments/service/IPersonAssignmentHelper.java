package com.kronos.persons.rest.assignments.service;

import java.util.List;
import java.util.function.Function;

import com.kronos.persons.rest.assignments.model.BaseAssignmentBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;

public interface IPersonAssignmentHelper<T extends BaseAssignmentBean> {

   T getPersonAssignmentByPersonNumber(String personNumber, Function<PersonIdentityBean, T> getDataListFunction);
   
   T getPersonAssignmentByPersonId(Long personId, Function<PersonIdentityBean, T> getDataListFunction);
   
   List<T> getPersonAssignmentList(ExtensionSearchCriteria extensionSearchCriteria,
         Function<PersonIdentityBean, T> getDataListFunction);

   List<T> getBulkPersonAssignmentList(ExtensionSearchCriteria extensionSearchCriteria,
                                   Function<PersonIdentityBean, List<T>> getDataListFunction);
   
   // This method was added to preserve the existing behavior of the RestPercentageAllocationRuleAssignment
   // and still fix the behavior of the framework.  This should be removed if we are able to rev the API 
   // and fix these behaviors.
   @Deprecated
   List<T> getBulkPersonAssignmentList(ExtensionSearchCriteria extensionSearchCriteria,
                                          Function<PersonIdentityBean, List<T>> getDataListFunction, boolean failOnNoSuccesses);


}
