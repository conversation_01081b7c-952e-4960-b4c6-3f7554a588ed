package com.kronos.persons.rest.beans;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.io.Serializable;

@Schema(description = "@v1.0.activityidlevaluebean.apimodel.description", name = "idleActivities")
public class ActivityIdleValueBean implements Serializable {
    @Schema(description = "@v1.0.activityidlevaluebean.apimodelproperty.activity.description")
    private ObjectRef activity;

    @Schema(description = "@v1.0.activityidlevaluebean.apimodelproperty.percentage.description")
    private Integer percentage;

    public ActivityIdleValueBean() {
    }

    public ActivityIdleValueBean(ObjectRef activity, Integer percentage) {
        this.activity = activity;
        this.percentage = percentage;
    }

    public ObjectRef getActivity() {
        return activity;
    }

    public void setActivity(ObjectRef activity) {
        this.activity = activity;
    }

    public Integer getPercentage() {
        return percentage;
    }

    public void setPercentage(Integer percentage) {
        this.percentage = percentage;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ActivityIdleValueBean that = (ActivityIdleValueBean) o;
        return new EqualsBuilder().append(activity, that.activity).append(percentage, that.percentage).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(activity).append(percentage).toHashCode();
    }
}
