package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.commonapp.types.business.EmploymentStatusType;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.useraccountstatusbean.apimodel.description", name = "userAccountStatus")
@JsonPropertyOrder(alphabetic = true)
public class UserAccountStatusBean {
    @Schema(description = "@v1.0.useraccountstatusbean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String effectiveDate;

    @Schema(description = "@v1.0.useraccountstatusbean.apimodelproperty.expirationdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String expirationDate;

    @Schema(description = "@v1.0.useraccountstatusbean.apimodelproperty.useraccountstatusname.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String userAccountStatusName;

	/**
	 * The employment status business object for the type.
	 */
    @JsonIgnore
	private EmploymentStatusType userStatus;

	/**
	 * @return the effectiveDate
	 */
	public String getEffectiveDate() {
		return effectiveDate;
	}

	/**
	 * @param effectiveDate
	 *            the effectiveDate to set
	 */
	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	/**
	 * @return the expirationDate
	 */
	public String getExpirationDate() {
		return expirationDate;
	}

	/**
	 * @param expirationDate
	 *            the expirationDate to set
	 */
	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}

	/**
	 * @return the userAccountStatusName
	 */
	public String getUserAccountStatusName() {
		return userAccountStatusName;
	}

	/**
	 * @param userAccountStatusName
	 *            the userAccountStatusName to set
	 */
	public void setUserAccountStatusName(String userAccountStatusName) {
		this.userAccountStatusName = userAccountStatusName;
	}

	/**
	 * @return the userStatus
	 */
	public EmploymentStatusType getUserStatus() {
		return userStatus;
	}

	/**
	 * @param userStatus
	 *            the userStatus to set
	 */
	public void setUserStatus(EmploymentStatusType userStatus) {
		this.userStatus = userStatus;
	}

}
