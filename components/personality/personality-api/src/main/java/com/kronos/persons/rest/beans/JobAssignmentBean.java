package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.jobassignmentbean.apimodel.description", name = "jobAssignment")
@JsonPropertyOrder(alphabetic = true)
public class JobAssignmentBean implements Serializable {
    @Schema(description = "@v1.0.jobassignmentbean.apimodelproperty.basewagerates.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<BaseWageRateBean> baseWageRates;

    @Schema(description = "@v1.0.jobassignmentbean.apimodelproperty.haspersonalovertimeflag.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean hasPersonalOvertimeFlag;

    @Schema(description = "@v1.0.jobassignmentbean.apimodelproperty.jobassignmentdetails.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private JobAssignmentDetailsBean jobAssignmentDetails;

    @Schema(description = "@v1.0.jobassignmentbean.apimodelproperty.primarylaboraccounts.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<PrimaryLaborAccountBean> primaryLaborAccounts;

	@Schema(description = "@v1.0.jobassignmentbean.apimodelproperty.employmenttermassignmentspan.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private AssignmentSpanBean employmentTermAssignmentSpan;

    @Schema(description = "@v1.0.jobassignmentbean.apimodelproperty.employmenttermassignments.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<EmploymentTermBean> employmentTermAssignments;

    @Schema(description = "@v1.0.jobassignmentbean.apimodelproperty.schedulegroupassignmentspan.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private AssignmentSpanBean scheduleGroupAssignmentSpan;

    @Schema(description = "@v1.0.jobassignmentbean.apimodelproperty.schedulegroupassignments.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<ScheduleGroupBean> scheduleGroupAssignments;

    @Schema(description = "@v1.0.jobassignmentbean.apimodelproperty.personalovertimeassignments.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<PersonalOvertimeAssignmentBean> personalOvertimeAssignments;

    @Schema(description = "@v1.0.jobassignmentbean.apimodelproperty.usemultipleassignmentsflag.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean useMultipleAssignmentsFlag;

    @Schema(description = "@v1.0.jobassignmentbean.apimodelproperty.schedulegroupname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String scheduleGroupName;

	@Schema(description = "@v1.0.jobassignmentbean.apimodelproperty.predictiveschedulingeligibilitylist.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
   private List<PredictiveSchedulingEligibilityBean> predictiveSchedulingEligibilityList;

	@Schema(description = "@v1.0.jobassignmentbean.apimodelproperty.predictiveschedulingoverridelist.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private List<PredictiveSchedulingOverrideBean> predictiveSchedulingRuleOverrideList;		

	/**
	 * @return the baseWageRates
	 */
	public List<BaseWageRateBean> getBaseWageRates() {
		return baseWageRates;
	}
	/**
	 * @param baseWageRates the baseWageRates to set
	 */
	public void setBaseWageRates(List<BaseWageRateBean> baseWageRates) {
		this.baseWageRates = baseWageRates;
	}
	/**
	 * @return the hasPersonalOvertimeFlag
	 */
	public Boolean getHasPersonalOvertimeFlag() {
		return hasPersonalOvertimeFlag;
	}
	/**
	 * @param hasPersonalOvertimeFlag the hasPersonalOvertimeFlag to set
	 */
	public void setHasPersonalOvertimeFlag(Boolean hasPersonalOvertimeFlag) {
		this.hasPersonalOvertimeFlag = hasPersonalOvertimeFlag;
	}
	/**
	 * @return the jobAssignmentDetails
	 */
	public JobAssignmentDetailsBean getJobAssignmentDetails() {
		return jobAssignmentDetails;
	}
	/**
	 * @param jobAssignmentDetails the jobAssignmentDetails to set
	 */
	public void setJobAssignmentDetails(JobAssignmentDetailsBean jobAssignmentDetails) {
		this.jobAssignmentDetails = jobAssignmentDetails;
	}
	/**
	 * @return the primaryLaborAccounts
	 */
	public List<PrimaryLaborAccountBean> getPrimaryLaborAccounts() {
		return primaryLaborAccounts;
	}
	/**
	 * @param primaryLaborAccounts the primaryLaborAccounts to set
	 */
	public void setPrimaryLaborAccounts(List<PrimaryLaborAccountBean> primaryLaborAccounts) {
		this.primaryLaborAccounts = primaryLaborAccounts;
	}
	/**
	 * @return the assignmentSpan
	 */
	public AssignmentSpanBean getEmploymentTermAssignmentSpan() {
		return employmentTermAssignmentSpan;
	}
	/**
	 * @param employmentTermAssignmentSpan the assignmentSpan to set
	 */
	public void setEmploymentTermAssignmentSpan(AssignmentSpanBean employmentTermAssignmentSpan) {
		this.employmentTermAssignmentSpan = employmentTermAssignmentSpan;
	}

	/**
	 * @return the employmentTermAssignments
	 */
	public List<EmploymentTermBean> getEmploymentTermAssignments() {
		return employmentTermAssignments;
	}
	/**
	 * @param employmentTermAssignments the employmentTermAssignments to set
	 */
	public void setEmploymentTermAssignments(List<EmploymentTermBean> employmentTermAssignments) {
		this.employmentTermAssignments = employmentTermAssignments;
	}
	
	/**
	 * @return the personalOvertimeAssignments
	 */
	public List<PersonalOvertimeAssignmentBean> getPersonalOvertimeAssignments() {
		return personalOvertimeAssignments;
	}
	/**
	 * @param personalOvertimeAssignments the personalOvertimeAssignments to set
	 */
	public void setPersonalOvertimeAssignments(List<PersonalOvertimeAssignmentBean> personalOvertimeAssignments) {
		this.personalOvertimeAssignments = personalOvertimeAssignments;
	}
	
	/**
	 * @return the useMultipleAssignmentsFlag
	 */
	public Boolean getUseMultipleAssignmentsFlag() {
		return useMultipleAssignmentsFlag;
	}
	/**
	 * @param useMultipleAssignmentsFlag the useMultipleAssignmentsFlag to set
	 */
	public void setUseMultipleAssignmentsFlag(Boolean useMultipleAssignmentsFlag) {
		this.useMultipleAssignmentsFlag = useMultipleAssignmentsFlag;
	}
	/**
	 * @return the scheduleGroupName
	 */
	public String getScheduleGroupName() {
		return scheduleGroupName;
	}
	/**
	 * @param scheduleGroupName the scheduleGroupName to set
	 */
	public void setScheduleGroupName(String scheduleGroupName) {
		this.scheduleGroupName = scheduleGroupName;
	}

	public List<PredictiveSchedulingEligibilityBean> getPredictiveSchedulingEligibilityList() {
		return predictiveSchedulingEligibilityList;
	}

	public void setPredictiveSchedulingEligibilityList(List<PredictiveSchedulingEligibilityBean> predictiveSchedulingEligibilityList) {
		this.predictiveSchedulingEligibilityList = predictiveSchedulingEligibilityList;
	}

	public List<PredictiveSchedulingOverrideBean> getPredictiveSchedulingRuleOverrideList() {
		return predictiveSchedulingRuleOverrideList;
	}

	public void setPredictiveSchedulingRuleOverrideList(List<PredictiveSchedulingOverrideBean> predictiveSchedulingRuleOverrideList) {
		this.predictiveSchedulingRuleOverrideList = predictiveSchedulingRuleOverrideList;
	}

	public List<ScheduleGroupBean> getScheduleGroupAssignments() {
		return scheduleGroupAssignments;
	}

	public void setScheduleGroupAssignments(List<ScheduleGroupBean> scheduleGroupAssignments) {
		this.scheduleGroupAssignments = scheduleGroupAssignments;
	}

    public AssignmentSpanBean getScheduleGroupAssignmentSpan() {
        return scheduleGroupAssignmentSpan;
    }

    public void setScheduleGroupAssignmentSpan(AssignmentSpanBean scheduleGroupAssignmentSpan) {
        this.scheduleGroupAssignmentSpan = scheduleGroupAssignmentSpan;
    }

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;

		if (o == null || getClass() != o.getClass()) return false;

		JobAssignmentBean that = (JobAssignmentBean) o;

		return new EqualsBuilder().append(baseWageRates, that.baseWageRates)
				.append(hasPersonalOvertimeFlag, that.hasPersonalOvertimeFlag)
				.append(jobAssignmentDetails, that.jobAssignmentDetails)
				.append(primaryLaborAccounts, that.primaryLaborAccounts)
				.append(employmentTermAssignmentSpan, that.employmentTermAssignmentSpan)
				.append(employmentTermAssignments, that.employmentTermAssignments)
				.append(scheduleGroupAssignmentSpan, that.scheduleGroupAssignmentSpan)
				.append(scheduleGroupAssignments, that.scheduleGroupAssignments)
				.append(personalOvertimeAssignments, that.personalOvertimeAssignments)
				.append(useMultipleAssignmentsFlag, that.useMultipleAssignmentsFlag)
				.append(scheduleGroupName, that.scheduleGroupName)
				.append(predictiveSchedulingEligibilityList, that.predictiveSchedulingEligibilityList)
				.append(predictiveSchedulingRuleOverrideList, that.predictiveSchedulingRuleOverrideList)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).append(baseWageRates)
				.append(hasPersonalOvertimeFlag)
				.append(jobAssignmentDetails)
				.append(primaryLaborAccounts)
				.append(employmentTermAssignmentSpan)
				.append(employmentTermAssignments)
				.append(scheduleGroupAssignmentSpan)
				.append(scheduleGroupAssignments)
				.append(personalOvertimeAssignments)
				.append(useMultipleAssignmentsFlag)
				.append(scheduleGroupName)
				.append(predictiveSchedulingEligibilityList)
				.append(predictiveSchedulingRuleOverrideList)
				.toHashCode();
	}
}
