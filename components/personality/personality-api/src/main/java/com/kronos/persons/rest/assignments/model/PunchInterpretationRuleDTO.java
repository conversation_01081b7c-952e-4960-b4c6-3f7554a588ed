package com.kronos.persons.rest.assignments.model;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.punchInterpretationRule.apimodel.description", title = "punchInterpretationRule")

@JsonPropertyOrder(alphabetic = true)
public class PunchInterpretationRuleDTO {
   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.id.description")
   private Long id;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.name.description")
   private String name;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.punchRestrictions.description")
   private String punchRestrictions;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.allowOverrides.description")
   private Boolean allowOverrides;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.scheduleEarlyStartMargin.description")
   private Integer scheduleEarlyStartMargin;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.scheduleEarlyStartRestriction.description")
   private Integer scheduleEarlyStartRestriction;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.scheduleLateStartRestriction.description")
   private Integer scheduleLateStartRestriction;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.scheduleLateStartMargin.description")
   private Integer scheduleLateStartMargin;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.missedInEndMargin.description")
   private Integer missedInEndMargin;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.maximumOut.description")
   private Integer maximumOut;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.minimumMeal.description")
   private Integer minimumMeal;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.enforceBreaks.description")
   private Boolean enforceBreaks;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.earlyStartBreakMargin.description")
   private Integer earlyStartBreakMargin;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.lateStartBreakMargin.description")
   private Integer lateStartBreakMargin;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.lateEndBreakMargin.description")
   private Integer lateEndBreakMargin;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.scheduleBeginEarlyEndRestriction.description")
   private Integer scheduleBeginEarlyEndRestriction;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.scheduleLiftEarlyEndRestriction.description")
   private Integer scheduleLiftEarlyEndRestriction;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.scheduleLateEndRestriction.description")
   private Integer scheduleLateEndRestriction;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.maximumShiftLength.description")
   private Integer maximumShiftLength;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.unscheduledIn.description")
   private Boolean unscheduledIn;

   @Schema(description = "@v1.0.punchInterpretationRule.apimodelproperty.unscheduledShiftLength.description")
   private Integer unscheduledShiftLength;

   public Long getId() {
      return id;
   }

   public void setId(Long id) {
      this.id = id;
   }

   public String getName() {
      return name;
   }

   public void setName(String name) {
      this.name = name;
   }

   public String getPunchRestrictions() {
      return punchRestrictions;
   }

   public void setPunchRestrictions(String punchRestrictions) {
      this.punchRestrictions = punchRestrictions;
   }
   
   public Boolean getAllowOverrides() {
      return allowOverrides;
   }

   public void setAllowOverrides(Boolean allowOverrides) {
      this.allowOverrides = allowOverrides;
   }

   public Integer getScheduleEarlyStartMargin() {
      return scheduleEarlyStartMargin;
   }

   public void setScheduleEarlyStartMargin(Integer scheduleEarlyStartMargin) {
      this.scheduleEarlyStartMargin = scheduleEarlyStartMargin;
   }

   public Integer getScheduleEarlyStartRestriction() {
      return scheduleEarlyStartRestriction;
   }

   public void setScheduleEarlyStartRestriction(Integer scheduleEarlyStartRestriction) {
      this.scheduleEarlyStartRestriction = scheduleEarlyStartRestriction;
   }

   public Integer getScheduleLateStartRestriction() {
      return scheduleLateStartRestriction;
   }

   public void setScheduleLateStartRestriction(Integer scheduleLateStartRestriction) {
      this.scheduleLateStartRestriction = scheduleLateStartRestriction;
   }

   public Integer getScheduleLateStartMargin() {
      return scheduleLateStartMargin;
   }

   public void setScheduleLateStartMargin(Integer scheduleLateStartMargin) {
      this.scheduleLateStartMargin = scheduleLateStartMargin;
   }

   public Integer getMissedInEndMargin() {
      return missedInEndMargin;
   }

   public void setMissedInEndMargin(Integer missedInEndMargin) {
      this.missedInEndMargin = missedInEndMargin;
   }

   public Integer getMaximumOut() {
      return maximumOut;
   }

   public void setMaximumOut(Integer maximumOut) {
      this.maximumOut = maximumOut;
   }

   public Integer getMinimumMeal() {
      return minimumMeal;
   }

   public void setMinimumMeal(Integer minimumMeal) {
      this.minimumMeal = minimumMeal;
   }

   public Boolean getEnforceBreaks() {
      return enforceBreaks;
   }

   public void setEnforceBreaks(Boolean enforceBreaks) {
      this.enforceBreaks = enforceBreaks;
   }

   public Integer getEarlyStartBreakMargin() {
      return earlyStartBreakMargin;
   }

   public void setEarlyStartBreakMargin(Integer earlyStartBreakMargin) {
      this.earlyStartBreakMargin = earlyStartBreakMargin;
   }

   public Integer getLateStartBreakMargin() {
      return lateStartBreakMargin;
   }

   public void setLateStartBreakMargin(Integer lateStartBreakMargin) {
      this.lateStartBreakMargin = lateStartBreakMargin;
   }

   public Integer getLateEndBreakMargin() {
      return lateEndBreakMargin;
   }

   public void setLateEndBreakMargin(Integer lateEndBreakMargin) {
      this.lateEndBreakMargin = lateEndBreakMargin;
   }

   public Integer getScheduleBeginEarlyEndRestriction() {
      return scheduleBeginEarlyEndRestriction;
   }

   public void setScheduleBeginEarlyEndRestriction(Integer scheduleBeginEarlyEndRestriction) {
      this.scheduleBeginEarlyEndRestriction = scheduleBeginEarlyEndRestriction;
   }

   public Integer getScheduleLiftEarlyEndRestriction() {
      return scheduleLiftEarlyEndRestriction;
   }

   public void setScheduleLiftEarlyEndRestriction(Integer scheduleLiftEarlyEndRestriction) {
      this.scheduleLiftEarlyEndRestriction = scheduleLiftEarlyEndRestriction;
   }

   public Integer getScheduleLateEndRestriction() {
      return scheduleLateEndRestriction;
   }

   public void setScheduleLateEndRestriction(Integer scheduleLateEndRestriction) {
      this.scheduleLateEndRestriction = scheduleLateEndRestriction;
   }

   public Integer getMaximumShiftLength() {
      return maximumShiftLength;
   }

   public void setMaximumShiftLength(Integer maximumShiftLength) {
      this.maximumShiftLength = maximumShiftLength;
   }

   public Boolean getUnscheduledIn() {
      return unscheduledIn;
   }

   public void setUnscheduledIn(Boolean unscheduledIn) {
      this.unscheduledIn = unscheduledIn;
   }

   public Integer getUnscheduledShiftLength() {
      return unscheduledShiftLength;
   }

   public void setUnscheduledShiftLength(Integer unscheduledShiftLength) {
      this.unscheduledShiftLength = unscheduledShiftLength;
   }
}
