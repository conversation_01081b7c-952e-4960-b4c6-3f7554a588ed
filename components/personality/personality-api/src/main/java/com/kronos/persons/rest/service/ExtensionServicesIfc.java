/**
 * 
 */
package com.kronos.persons.rest.service;

import java.util.Map;

import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.persons.rest.beans.extensions.ExtensionResponseBean;
import com.kronos.persons.rest.model.ExtensionCriteria;

/**
 * The {@link ExtensionServicesIfc} interface to find the extensions.
 * 
 * <AUTHOR>
 *
 */
public interface ExtensionServicesIfc {

	/**
	 * Find the extension for a person number.
	 * 
	 * @param personNum
	 *            The {@link String} person number
	 * @return {@link PersonalityResponse} object having extension
	 */
	ExtensionResponseBean retrieve(String personNum);
	
	/**
	 * Find the extension for a person number.
	 * 
	 * @param personNum
	 *            The {@link String} person number
	 * @param includeAccrualPolicyDetails
	 *            whether to include accrual policies in response or not
	 * @return {@link PersonalityResponse} object having extension
	 */
	default ExtensionResponseBean retrieve(String personNum, boolean includeAccrualPolicyDetails, String includeBaseWages) {
		return new ExtensionResponseBean();
	}

	/**
	 * Find extensions corresponding to criteria.
	 * 
	 * @param extensionCriteria
	 *            The {@link ExtensionCriteria} object
	 * @return {@link Map} of passed Key and {@link PersonalityResponse} object
	 *         having extension
	 */
	Map<Object, ExtensionResponseBean> retrieve(ExtensionCriteria extensionCriteria);

}
