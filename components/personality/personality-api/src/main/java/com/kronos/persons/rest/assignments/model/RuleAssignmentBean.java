package com.kronos.persons.rest.assignments.model;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kronos.persons.rest.assignments.model.RestEntity;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.totalizing.business.extensibility.ProcessorToEmployee;

public abstract class RuleAssignmentBean extends RestEntity
{
    public abstract String getEffectiveDate();

    public abstract void setEffectiveDate(String effectiveDate);

    public abstract String getExpirationDate();

    public abstract void setExpirationDate(String expirationDate);

    public abstract PersonIdentityBean getPersonIdentity();

    public abstract void setPersonIdentity(PersonIdentityBean personIdentity);

    public abstract String getOriginalEffectiveDate();

    public abstract void setOriginalEffectiveDate(String originalEffectiveDate);

    public abstract String getProcessorIdentifier();
    
    public abstract Long getProcessorId();

    public abstract void setProcessorIdentifier(String processorIdentifier);

    public abstract void setProcessorId(Long processorId);

    public RuleAssignmentBean() {
        this(new ProcessorToEmployee());
        KDate date = KDate.createDate();
        this.setEffectiveDate(LocalDate.of(date.getYear(), date.getMonth(), date.getDay()).toString());
    }

    /**
     * Creates a new EmployeeAdjustmentRuleAssignmentBean and sets the wrapped
     * object to the given Processor.
     *
     * @param ProcessorToEmployee
     */
    public RuleAssignmentBean(ProcessorToEmployee processorToEmployee) {
        super();
        setWrappedObject(processorToEmployee);
    }

    /**
     * Gets the wrapped ProcessorToPayRule.
     *
     * @return Returns the wrapped ProcessorToPayRule.
     */
    @JsonIgnore
    public ProcessorToEmployee getWrappedProcessorToEmployee() {
        return (ProcessorToEmployee) getWrappedObject();
    }

    @JsonIgnore
    public String getProcessorName()
    {
        return getWrappedProcessorToEmployee().getProcessor().getName();
    }

    /**
     * Gets the EffectiveDate.
     *
     * @return Returns the EffectiveDate.
     */
    @JsonIgnore
    public KDate getProcessorEffectiveDate() {
        return getWrappedProcessorToEmployee().getEffectiveDate();
    }


    /**
     * Sets the EffectiveDate.
     *
     * @param effectiveDate
     *            The EffectiveDate to set.
     */
    @JsonIgnore
    public void setProcessorEffectiveDate(KDate effectiveDate) {
        if (null == effectiveDate || effectiveDate.isNull())
            return;

        getWrappedProcessorToEmployee().setEffectiveDate(effectiveDate);
    }

    /**
     * Sets the ExpirationDate.
     *
     * @param expirationDate
     *            The ExpirationDate to set.
     */
    @JsonIgnore
    public void setProcessorExpirationDate(KDate expirationDate) {
        getWrappedProcessorToEmployee().setExpirationDate(expirationDate);
    }

    /**
     * Gets the ExpirationDate.
     *
     * @param expirationDate
     *            The ExpirationDate to set.
     */
    @JsonIgnore
    public KDate getProcessorExpirationDate() {
        return getWrappedProcessorToEmployee().getExpirationDate();
    }
}
