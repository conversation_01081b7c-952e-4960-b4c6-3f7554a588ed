package com.kronos.persons.rest.assignments.model;

import com.kronos.commonbusiness.datatypes.ref.ObjectRefList;
import com.kronos.persons.rest.beans.HyperFindFilterBean;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "EmployeeRefs", description = "@v1.0.employeerefs.apimodel.description")
public class EmployeeRefs {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "@v1.0.employeerefs.apimodelproperty.employees.description")
    private ObjectRefList employees;

    @Schema(description = "@v1.0.employeerefs.apimodelproperty.hyperFindFilter.description")
    private HyperFindFilterBean hyperFindFilter;

    public EmployeeRefs() {
    }

    public EmployeeRefs(ObjectRefList employees) {
        this.employees = employees;
    }

    public ObjectRefList getEmployees() {
        return employees;
    }

    public void setEmployees(ObjectRefList employees) {
        this.employees = employees;
    }

    public HyperFindFilterBean getHyperFindFilter() {
        return hyperFindFilter;
    }

    public void setHyperFindFilter(HyperFindFilterBean hyperFindFilter) {
        this.hyperFindFilter = hyperFindFilter;
    }
}
