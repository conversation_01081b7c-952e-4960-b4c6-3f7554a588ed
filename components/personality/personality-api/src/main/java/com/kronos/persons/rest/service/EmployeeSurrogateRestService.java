/***********************************************************************
 * EmployeeTransferRestService.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/

package com.kronos.persons.rest.service;

import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

import com.kronos.persons.rest.model.EmployeeSurrogate;

/**
 * This class provides a method which will return an object of EmployeeSurrogate for a given employee Id.
 * <AUTHOR>
 *
 */
@Path("/v1/commons/employee")
public interface EmployeeSurrogateRestService {

	/**
	 * This method return an object of EmployeeSurrogate for a given employee Id.
	 * @param employeeId
	 * @return
	 */
	@GET
	@Path("/getSurrogateEmployee")
	@Produces(MediaType.APPLICATION_JSON)
	public EmployeeSurrogate getSurrogateEmployee(@QueryParam("employeeId") String employeeId);
}
