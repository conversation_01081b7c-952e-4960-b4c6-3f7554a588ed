package com.kronos.persons.rest.assignments.model;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kronos.wfc.platform.businessobject.framework.KBusinessObject;
import com.kronos.wfc.platform.entity.business.Entity;
import com.kronos.wfc.platform.entity.business.exceptions.EntityException;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;

public class RestEntity implements KBusinessObject {

	/**
	 * The wrapped business object.
	 */
	private Entity wrappedObject = null;

	/**
	 * Default constructor.
	 * 
	 * public APIEntity() { super(); }
	 */

	/**
	 * Get the object wrapped by this class.
	 * 
	 * @return Wrapped object.
	 */
	@JsonIgnore
	public Entity getWrappedObject() {
		return wrappedObject;
	}

	/**
	 * @param object
	 */
	protected void setWrappedObject(Entity object) {
		this.wrappedObject = object;
	}

	/**
	 * Creates a list of the objects wrapped by each API wrapper object.
	 * 
	 * @param apiObjects
	 *            List of API wrapper objects.
	 * @return List of objects wrapped by API wrappers.
	 */
	protected List getWrappedObjects(List apiObjects) {
		if (null == apiObjects)
			return null;

		List wrappedObjects = new ArrayList(apiObjects.size());

		for (Iterator i = apiObjects.iterator(); i.hasNext();) {
			RestEntity apiWrapper = (RestEntity) i.next();
			wrappedObjects.add(apiWrapper.getWrappedObject());
		}

		return wrappedObjects;
	}

	/**
	 * The wrapped object is only accessible by subclasses. It is not exposed in
	 * the API.
	 * 
	 * @return Always returns false.
	 */
	@JsonIgnore
	public boolean isWrappedObjectReadable() {
		return false;
	}

	/**
	 * The wrapped object is only accessible by subclasses. It is not exposed in
	 * the API.
	 * 
	 * @return Always returns false.
	 */
	@JsonIgnore
	public boolean isWrappedObjectWriteable() {
		return false;
	}

	/**
	 * The wrapped object is only accessible by subclasses. It is not exposed in
	 * the API.
	 * 
	 * @return Always returns false.
	 */
	@JsonIgnore
	public boolean isBusinessObjectWriteable() {
		return false;
	}

	/**
	 * The wrapped object is only accessible by subclasses. It is not exposed in
	 * the API.
	 * 
	 * @return Always returns false.
	 */
	@JsonIgnore
	public boolean isBusinessObjectReadable() {
		return false;
	}

	/**
	 * Get the id of the object.
	 * 
	 * @return The object's id.
	 */
	@JsonIgnore
	public String getId() {
		return getWrappedObject().getId().toString();
	}

	/**
	 * Set the id of the object.
	 * 
	 * @param id
	 *            The id to set.
	 */
	public void setId(String id) {
		getWrappedObject().setId(new ObjectIdLong(id));
	}

	/**
	 * Make the ID non-writeable.
	 * 
	 * @return false
	 */
	@JsonIgnore
	public boolean isIdWriteable() {
		return false;
	}

	/**
	 * Make the ID readable.
	 * 
	 * @return false
	 */
	@JsonIgnore
	public boolean isIdReadable() {
		return false;
	}

	/**
	 * Adds the object.
	 */
	public void doAddOnly() {
		Entity entity = getWrappedObject();

		if (entity.isNew()) {
			validateDoesNotExist();
			getWrappedObject().insert();
		} else {
			throw new EntityException(EntityException.ADD_ONLY_ALREADY_EXISTS);
		}
	}

	/**
	 * Updates the object.
	 */
	public void doUpdateOnly() {
		Entity entity = getWrappedObject();
		if (entity.isNew()) {
    		throw new EntityException(EntityException.UPDATE_ONLY_DOES_NOT_EXIST);
    	}
		validateExists();
		entity.update();
	}

	/**
	 * Deletes the object.
	 */
	public void doDelete() {
		getWrappedObject().delete();
	}

	/**
	 * Adds the object if it is new, or updates the object if it exists.
	 */
	public void doUpdate() {
		if (getWrappedObject().isNew()) {
			doAddOnly();
		} else {
			doUpdateOnly();
		}
	}

	/**
	 * Adds the object if it is new, or updates if it exists. Functionally
	 * identical to doUpdate. Forwards to doUpdate.
	 */
	public void doAdd() {
		doUpdate();
	}

	/**
	 * Override this method to validate that an entity does not exist. This
	 * method is called by doAddOnly.
	 */
	protected void validateDoesNotExist() {
		// no-op
	}

	/**
	 * Override this method to validate that an entity exists. This method is
	 * called by doUpdateOnly.
	 */
	protected void validateExists() {
		// no-op
	}
}
