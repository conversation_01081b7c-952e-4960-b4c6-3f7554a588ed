package com.kronos.persons.rest.exception;

import java.util.ArrayList;
import java.util.List;

import com.kronos.container.api.exception.APIException;

/**
 * 
 * <AUTHOR>
 *
 */
public class PrsnInvalidBeanException {

	private APIException apiException;
	
	private List<APIException> apiExceptionList;
	
	public PrsnInvalidBeanException(){
		initApiException();
	}
	
	private void initApiException(){
		apiException = new APIException(ExceptionConstants.INVALID_BEAN);
		apiExceptionList = new ArrayList<>(0);
		apiException.setErrorsDetail(apiExceptionList);
	}
	
	/**
	 * @return the apiException
	 */
	public APIException getApiException() {
		return apiException;
	}

	
	public void addApiExceptionToList(APIException apiEx){
		this.apiExceptionList.add(apiEx);
	}

	/**
	 * @return the apiExceptionList
	 */
	public List<APIException> getApiExceptionList() {
		return this.apiExceptionList;
	}
	
	public int numberOfWrappedExceptions(){
		if (apiExceptionList != null)
			return apiExceptionList.size();
		
		return 0;
	}
	
}
