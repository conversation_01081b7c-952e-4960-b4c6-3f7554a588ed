package com.kronos.persons.rest.assignments.model.adjustmentrule;

import java.util.List;

import com.kronos.persons.rest.beans.PeopleAssignmentBean;
/*
 *  Bean with Mapping of PersonIdentity To Multiple Employee AdjustmentRuleAssignments
 */
public class AdjustmentRuleMultipleAssignmentBean extends PeopleAssignmentBean {

	List<EmployeeAdjustmentRuleAssignmentBean> assignments;

	public List<EmployeeAdjustmentRuleAssignmentBean> getAssignments() {
		return assignments;
	}

	public void setAssignments(List<EmployeeAdjustmentRuleAssignmentBean> assignments) {
		this.assignments = assignments;
	}
	
	
}
