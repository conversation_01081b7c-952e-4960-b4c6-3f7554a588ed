package com.kronos.persons.rest.assignments.model.brazilcompliance;


import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.brazilmultireadwhere.apimodel.description", name = "brazilMultiReadWhere")
public class BrazilMultiReadWhere {

    @Schema(description = "@v1.0.brazilmultireadwhere.apimodelproperty.where.description", requiredMode = Schema.RequiredMode.REQUIRED)
    BrazilWhereCriteria where;

    public BrazilWhereCriteria getWhere() {
        return where;
    }

    public void setWhere(BrazilWhereCriteria where) {
        this.where = where;
    }

}
