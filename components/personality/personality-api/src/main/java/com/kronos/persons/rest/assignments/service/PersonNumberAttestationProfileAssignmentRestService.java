package com.kronos.persons.rest.assignments.service;


import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignment;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignmentRequest;

import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignmentDTO;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.List;

@Tag(name = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.name")
@OpenAPIDefinition(tags = {
    @Tag(
        description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.description",
        extensions = @Extension(properties = {
            @ExtensionProperty(name = "parent", value = "@root.personassignments.default")
        }),
        name = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.name"
    )
})
@Publishable(type = RestAPITypes.PUBLIC, value = true, productType = ProductTypes.WTK)
@Path("/v1/commons/persons/attestation_profile_assignments")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface PersonNumberAttestationProfileAssignmentRestService {

    @Operation(
        tags = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.nickname",
        description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.notes",
        summary = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.summary"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.response.200.message", content = @Content(schema = @Schema(type="list", implementation = PersonAttestationProfileAssignmentDTO.class))),
        @ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.response.400.message"),
        @ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.response.500.message")
    })
    @GET
    Response retrieveByPersonNumber(
        @Parameter(description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.queryparam.person_number.value", required = true) @QueryParam("person_number") String personNumber,
        @Parameter(description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.get.currentpath.queryparam.assignToManagerRole.value") @QueryParam("assignToManagerRole") boolean isAssignToManagerRole);

    @Operation(
        tags = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.nickname",
        description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.notes",
        summary = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.summary"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.response.201.message", content = @Content(schema = @Schema(implementation = PersonAttestationProfileAssignmentDTO.class))),
        @ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.response.400.message"),
        @ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.response.500.message")})
    @POST
    Response create(
        @Parameter(description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.currentpath.dto.value", required = true) PersonAttestationProfileAssignmentDTO dto);

    @Operation(
            tags = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.nickname",
            description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.notes",
            summary = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.summary"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.response.200.message", content = @Content(schema = @Schema(type="list", implementation = PersonAttestationProfileAssignment.class))),
            @ApiResponse(responseCode = "207", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.response.207.message"),
            @ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.response.400.message"),
            @ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_update.response.500.message")})
    @POST()
    @Path("/multi_update")
    Response multiUpdate(
            @Parameter(description = "@v1.0.commons-persons-attestation_profile_assignments_multi_update.post.currentpath.personattestationprofileassignments.value", required = true)
                  List<PersonAttestationProfileAssignment> personAttestationProfileAssignments,
                @Parameter(description = "@v1.0.commons-persons-attestation_profile_assignments_multi_update.post.currentpath.queryparam.mergeEffectiveDating.value", required=false)
            	@DefaultValue("false")
            	@QueryParam("mergeEffectiveDating") Boolean mergeEffectiveDating);

    @Operation(
            tags = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.nickname",
            description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.notes",
            summary = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.summary"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.response.200.message", content = @Content(schema = @Schema(type="list", implementation = PersonAttestationProfileAssignment.class))),
            @ApiResponse(responseCode = "207", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.response.207.message"),
            @ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.response.400.message"),
            @ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.response.500.message")})
    @POST()
    @Path("/multi_read")
    List<PersonAttestationProfileAssignment> multiRead(
          @Parameter(description = "@v1.0.commons-persons-attestation_profile_assignments_by_person_number.post.multi_read.requestbody.value",
                required = true) PersonAttestationProfileAssignmentRequest request);
}