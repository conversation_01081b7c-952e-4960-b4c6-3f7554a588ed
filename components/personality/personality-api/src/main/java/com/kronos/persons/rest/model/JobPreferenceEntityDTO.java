package com.kronos.persons.rest.model;

public class JobPreferenceEntityDTO {
   private String job;

   private String location;

   private String seniorityDate;

   private String preference;

   private boolean fromEmployeeRequest;

   private String context;

   public String getJob() {
      return job;
   }

   public void setJob(String job) {
      this.job = job;
   }

   public String getLocation() {
      return location;
   }

   public void setLocation(String location) {
      this.location = location;
   }

   public String getSeniorityDate() {
      return seniorityDate;
   }

   public void setSeniorityDate(String seniorityDate) {
      this.seniorityDate = seniorityDate;
   }

   public String getPreference() {
      return preference;
   }

   public void setPreference(String preference) {
      this.preference = preference;
   }

   public String getContext() {
      return context;
   }

   public void setContext(String context) {
      this.context = context;
   }

   public boolean isFromEmployeeRequest() {
      return fromEmployeeRequest;
   }

   public void setFromEmployeeRequest(boolean fromEmployeeRequest) {
      this.fromEmployeeRequest = fromEmployeeRequest;
   }
}
