package com.kronos.persons.rest.service;

import java.time.LocalDate;
import java.util.Collection;

import com.kronos.persons.rest.assignments.model.PunchInterpretationRuleDTO;
import com.kronos.persons.rest.assignments.model.PunchInterpretationRulesDTO;
import com.kronos.persons.rest.beans.extensions.ExtensionResponseBean;
import com.kronos.persons.rest.model.PunchInterpretationRuleMultiDeleteRequest;
import com.kronos.persons.rest.model.PunchInterpretationRulePaginationResponse;

/**
 * 
 * <AUTHOR>
 *
 */


/**
 * PunchInterpretationRuleService is used for retrieving the Punch interpretation rule of an Employee based on the ET or PR
 *
 */
public interface PunchInterpretationRuleService {
   /**
    * Retrieves The PIR's when there is an ET alloted to person with PR in it and if there is no ET alloted, PIR is Retrieved from the alloted PR
    * @param extensionResponseBean
    * @param snapshotDate
    * @return PunchInterpretationRuleDTO
    */
   default PunchInterpretationRuleDTO getPunchInterpretationRule(ExtensionResponseBean extensionResponseBean, LocalDate snapshotDate) {
      return new PunchInterpretationRuleDTO();
   }

   /**
    * Retrieves all PIR's
    * @param index - the pagination index
    * @param count - the pagination count
    * @return PunchInterpretationRulePaginationResponse
    */

   PunchInterpretationRulePaginationResponse getAllPunchInterpretationRules(String id, String name, boolean allDetails, String index, String count);

   /**
    * Delete Punch Interpretation Rule By Id
    * @param id - the id of the punch interpretation rule
    */
   void deletePunchInterpretationRule(String id);

   /**
    * Multi Delete Punch Interpretation Rules
    *
    * @param punchInterpretationRuleMultiDeleteRequest - the multi delete request
    */
   void multiDelete(PunchInterpretationRuleMultiDeleteRequest punchInterpretationRuleMultiDeleteRequest);

   /**
    * Create & Update Punch Interpretation Rules
    *
    * @param punchInterpretationRuleList - List of punch interpretation rule dto {@link PunchInterpretationRulesDTO}
    * @return all punch interpretation rules objects
    */
   Collection<PunchInterpretationRulesDTO> multiUpsert(Collection<PunchInterpretationRulesDTO> punchInterpretationRuleList);
}
