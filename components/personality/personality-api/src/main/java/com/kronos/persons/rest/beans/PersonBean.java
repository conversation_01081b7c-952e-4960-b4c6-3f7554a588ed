package com.kronos.persons.rest.beans;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */

@Schema(description = "@v1.0.personbean.apimodel.description", name = "person")
@JsonPropertyOrder(alphabetic = true)
public class PersonBean {
    @Schema(description = "@v1.0.personbean.apimodelproperty.personnumber.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String personNumber;

    @Schema(description = "@v1.0.personbean.apimodelproperty.firstname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String firstName;

    @Schema(description = "@v1.0.personbean.apimodelproperty.lastname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String lastName;

    @Schema(description = "@v1.0.personbean.apimodelproperty.middleinitial.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String middleInitial;

    @Schema(description = "@v1.0.personbean.apimodelproperty.fullname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fullName;

    @Schema(description = "@v1.0.personbean.apimodelproperty.shortname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String shortName;

	@Schema(description = "@v1.0.personbean.apimodelproperty.employmentName.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String employmentName;

	@Schema(description = "@v1.0.personbean.apimodelproperty.employmentLink.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String employmentLink;

    @Schema(description = "@v1.0.personbean.apimodelproperty.romanizedfullname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String romanizedFullName;

    @Schema(description = "@v1.0.personbean.apimodelproperty.phoneticfullname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String phoneticFullName;

    @Schema(description = "@v1.0.personbean.apimodelproperty.birthdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String birthDate;

    @Schema(description = "@v1.0.personbean.apimodelproperty.hiredate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String hireDate;

    @Schema(description = "@v1.0.personbean.apimodelproperty.accrualprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accrualProfileName;

    @Schema(description = "@v1.0.personbean.apimodelproperty.accrualprofileeffectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accrualProfileEffectiveDate;

    @Schema(description = "@v1.0.personbean.apimodelproperty.accrualprofileenddate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String accrualProfileEndDate;

    @Schema(description = "@v1.0.personbean.apimodelproperty.fulltimepercentage.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Double fullTimePercentage;

    @Schema(description = "@v1.0.personbean.apimodelproperty.fulltimestandardhours.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Double fullTimeStandardHours;

    @Schema(description = "@v1.0.personbean.apimodelproperty.employeestandardhours.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Double employeeStandardHours;

    @Schema(description = "@v1.0.personbean.apimodelproperty.managersignoffthrudatetime.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String managerSignoffThruDateTime;

	@Schema(description = "@v1.0.personbean.apimodelproperty.signoffpreparationdatetime.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String signoffPreparationDateTime;

    @Schema(description = "@v1.0.personbean.apimodelproperty.payrolllockoutthrudatetime.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String payrollLockoutThruDateTime;

    @Schema(description = "@v1.0.personbean.apimodelproperty.fingerrequiredflag.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean fingerRequiredFlag;

	@Schema(description = "@v1.0.personbean.apimodelproperty.facerequiredflag.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Boolean faceRequiredFlag;

    @Schema(description = "@v1.0.personbean.apimodelproperty.haskmailnotificationdelivery.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean hasKmailNotificationDelivery;

    @Schema(description = "@v1.0.personbean.apimodelproperty.fulltimeequivalencies.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private List<FullTimeEquivalencyBean> fullTimeEquivalencies;

	@Schema(description = "@v1.0.personbean.apimodelproperty.globalid.description", required = false)
	private String globalId;

	/**
	 * @return the personNumber
	 */
	public String getPersonNumber() {
		return personNumber;
	}

	/**
	 * @param personNumber
	 *            the personNumber to set
	 */
	public void setPersonNumber(String personNumber) {
		this.personNumber = personNumber;
	}

	/**
	 * @return the firstName
	 */
	public String getFirstName() {
		return firstName;
	}

	/**
	 * @param firstName
	 *            the firstName to set
	 */
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	/**
	 * @return the lastName
	 */
	public String getLastName() {
		return lastName;
	}

	/**
	 * @param lastName
	 *            the lastName to set
	 */
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	/**
	 * @return the birthDate
	 */
	public String getBirthDate() {
		return birthDate;
	}

	/**
	 * @param birthDate
	 *            the birthDate to set
	 */
	public void setBirthDate(String birthDate) {
		this.birthDate = birthDate;
	}

	/**
	 * @return the hireDate
	 */
	public String getHireDate() {
		return hireDate;
	}

	/**
	 * @param hireDate
	 *            the hireDate to set
	 */
	public void setHireDate(String hireDate) {
		this.hireDate = hireDate;
	}

	/**
	 * @return the accrualProfileName
	 */
	public String getAccrualProfileName() {
		return accrualProfileName;
	}

	/**
	 * @param accrualProfileName
	 *            the accrualProfileName to set
	 */
	public void setAccrualProfileName(String accrualProfileName) {
		this.accrualProfileName = accrualProfileName;
	}

	/**
	 * @return the accrualProfileEffectiveDate
	 */
	public String getAccrualProfileEffectiveDate() {
		return accrualProfileEffectiveDate;
	}

	/**
	 * @param accrualProfileEffectiveDate
	 *            the accrualProfileEffectiveDate to set
	 */
	public void setAccrualProfileEffectiveDate(
			String accrualProfileEffectiveDate) {
		this.accrualProfileEffectiveDate = accrualProfileEffectiveDate;
	}

	/**
	 * @return the accrualProfileEndDate
	 */
	public String getAccrualProfileEndDate() {
		return accrualProfileEndDate;
	}

	/**
	 * @param accrualProfileEndDate
	 *            the accrualProfileEndDate to set
	 */
	public void setAccrualProfileEndDate(String accrualProfileEndDate) {
		this.accrualProfileEndDate = accrualProfileEndDate;
	}

	/**
	 * @return the middleInitial
	 */
	public String getMiddleInitial() {
		return middleInitial;
	}

	/**
	 * @param middleInitial
	 *            the middleInitial to set
	 */
	public void setMiddleInitial(String middleInitial) {
		this.middleInitial = middleInitial;
	}

	/**
	 * @return the fullName
	 */
	public String getFullName() {
		return fullName;
	}

	/**
	 * @param fullName
	 *            the fullName to set
	 */
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	/**
	 * @return the shortName
	 */
	public String getShortName() {
		return shortName;
	}

	/**
	 * @param shortName
	 *            the shortName to set
	 */
	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public String getEmploymentName() {
		return employmentName;
	}

	public void setEmploymentName(String employmentName) {
		this.employmentName = employmentName;
	}

	public String getEmploymentLink() {
		return employmentLink;
	}

	public void setEmploymentLink(String employmentLink) {
		this.employmentLink = employmentLink;
	}

	/**
	 * @return the romanizedFullName
	 */
	public String getRomanizedFullName() {
		return romanizedFullName;
	}

	/**
	 * @param romanizedFullName
	 *            the romanizedFullName to set
	 */
	public void setRomanizedFullName(String romanizedFullName) {
		this.romanizedFullName = romanizedFullName;
	}

	/**
	 * @return the phoneticFullName
	 */
	public String getPhoneticFullName() {
		return phoneticFullName;
	}

	/**
	 * @param phoneticFullName
	 *            the phoneticFullName to set
	 */
	public void setPhoneticFullName(String phoneticFullName) {
		this.phoneticFullName = phoneticFullName;
	}

	/**
	 * @return the fullTimePercentage
	 */
	public Double getFullTimePercentage() {
		return fullTimePercentage;
	}

	/**
	 * @param fullTimePercentage
	 *            the fullTimePercentage to set
	 */
	public void setFullTimePercentage(Double fullTimePercentage) {
		this.fullTimePercentage = fullTimePercentage;
	}

	/**
	 * @return the fullTimeStandardHours
	 */
	public Double getFullTimeStandardHours() {
		return fullTimeStandardHours;
	}

	/**
	 * @param fullTimeStandardHours
	 *            the fullTimeStandardHours to set
	 */
	public void setFullTimeStandardHours(Double fullTimeStandardHours) {
		this.fullTimeStandardHours = fullTimeStandardHours;
	}

	/**
	 * @return the employeeStandardHours
	 */
	public Double getEmployeeStandardHours() {
		return employeeStandardHours;
	}

	/**
	 * @param employeeStandardHours
	 *            the employeeStandardHours to set
	 */
	public void setEmployeeStandardHours(Double employeeStandardHours) {
		this.employeeStandardHours = employeeStandardHours;
	}

	/**
	 * @return the fingerRequiredFlag
	 */
	public Boolean getFingerRequiredFlag() {
		return fingerRequiredFlag;
	}

	/**
	 * @param fingerRequiredFlag
	 *            the fingerRequiredFlag to set
	 */
	public void setFingerRequiredFlag(Boolean fingerRequiredFlag) {
		this.fingerRequiredFlag = fingerRequiredFlag;
	}

	public Boolean getFaceRequiredFlag() {
		return faceRequiredFlag;
	}

	public void setFaceRequiredFlag(Boolean faceRequiredFlag) {
		this.faceRequiredFlag = faceRequiredFlag;
	}

	/**
	 * @return the hasKmailNotificationDelivery
	 */
	public Boolean getHasKmailNotificationDelivery() {
		return hasKmailNotificationDelivery;
	}

	/**
	 * @param hasKmailNotificationDelivery
	 *            the hasKmailNotificationDelivery to set
	 */
	public void setHasKmailNotificationDelivery(
			Boolean hasKmailNotificationDelivery) {
		this.hasKmailNotificationDelivery = hasKmailNotificationDelivery;
	}

	/**
	 * @return the managerSignoffThruDateTime
	 */
	public String getManagerSignoffThruDateTime() {
		return managerSignoffThruDateTime;
	}

	/**
	 * @param managerSignoffThruDateTime
	 *            the managerSignoffThruDateTime to set
	 */
	public void setManagerSignoffThruDateTime(
			String managerSignoffThruDateTime) {
		this.managerSignoffThruDateTime = managerSignoffThruDateTime;
	}

	public String getSignoffPreparationDateTime(){ return signoffPreparationDateTime;}

	public void setSignoffPreparationDateTime(String signoffPreparationDateTime) {
		this.signoffPreparationDateTime = signoffPreparationDateTime;
	}

	/**
	 * @return the payrollLockoutThruDateTime
	 */
	public String getPayrollLockoutThruDateTime() {
		return payrollLockoutThruDateTime;
	}

	/**
	 * @param payrollLockoutThruDateTime
	 *            the payrollLockoutThruDateTime to set
	 */
	public void setPayrollLockoutThruDateTime(
			String payrollLockoutThruDateTime) {
		this.payrollLockoutThruDateTime = payrollLockoutThruDateTime;
	}

	/**
	 * @return the fullTimeEquivalencies
	 */
	public List<FullTimeEquivalencyBean> getFullTimeEquivalencies() {
		return fullTimeEquivalencies;
	}

	/**
	 * @param fullTimeEquivalencies
	 *            the fullTimeEquivalencies to set
	 */
	public void setFullTimeEquivalencies(
			List<FullTimeEquivalencyBean> fullTimeEquivalencies) {
		this.fullTimeEquivalencies = fullTimeEquivalencies;
	}

	/**
	 * @return the globalId
	 */
	public String getGlobalId() {
		return globalId;
	}

	/**
	 * @param globalId
	 *            the globalId to set
	 */
	public void setGlobalId(String globalId) {
		this.globalId = globalId;
	}
}
