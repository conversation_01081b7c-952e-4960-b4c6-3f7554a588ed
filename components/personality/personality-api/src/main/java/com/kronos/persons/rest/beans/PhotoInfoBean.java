package com.kronos.persons.rest.beans;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.photoinfobean.apimodel.description", name = "photoInfoBean")
public class PhotoInfoBean implements Serializable{
	
	private static final long serialVersionUID = 1L;

	private String image;
	
	@Schema(description="@v1.0.photoinfobean.apimodelproperty.imageType.description")
	private String imageType;
	
	@Schema(description="@v1.0.photoinfobean.apimodelproperty.lastUpdateDateTime.description")
	private LocalDateTime lastUpdateDateTime;

	public String getImage() {
		return image;
	}

	public void setImage(String image) {
		this.image = image;
	}

	public String getImageType() {
		return imageType;
	}

	public void setImageType(String imageType) {
		this.imageType = imageType;
	}

	public LocalDateTime getLastUpdateDateTime() {
		return lastUpdateDateTime;
	}

	public void setLastUpdateDateTime(LocalDateTime lastUpdateDateTime) {
		this.lastUpdateDateTime = lastUpdateDateTime;
	}

	@Override
	public int hashCode() {
		return Objects.hash(image, imageType, lastUpdateDateTime);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PhotoInfoBean other = (PhotoInfoBean) obj;
		return Objects.equals(image, other.image) && Objects.equals(imageType, other.imageType)
				&& Objects.equals(lastUpdateDateTime, other.lastUpdateDateTime);
	}
	
	
	

}
