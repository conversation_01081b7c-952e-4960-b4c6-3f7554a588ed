package com.kronos.persons.rest.assignments.service;

import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilEmployeeAssignmentsRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilMultiReadWhere;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;

import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.tags.Tags;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.List;

@Tags(value = {@Tag(name = "@v1.0.commons-persons-brazil_employee_assignments.name")})
@OpenAPIDefinition(tags = {
        @Tag(
                description = "@v1.0.commons-persons-brazil_employee_assignments.description",
                name = "@v1.0.commons-persons-brazil_employee_assignments.name",
                extensions =
                    @Extension(properties =
                            @ExtensionProperty(
                                    name = "parent",
                                    value = "@v1.0.commons-persons-brazil_employee_assignments.parent"
                ))
        )
})

@Publishable(type = RestAPITypes.PUBLIC, productType = ProductTypes.WTK)
@Path("/v1/commons/persons/brazil_employee_assignments")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface IBrazilAssignmentRestService {

    @Operation(
            operationId = "@v1.0.commons-persons-brazil_employee_assignments.get.{personId}.nickname",
            description = "@v1.0.commons-persons-brazil_employee_assignments.get.{personId}.notes",
            summary = "@v1.0.commons-persons-brazil_employee_assignments.get.{personId}.summary",
            responses = {
                    @ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-brazil_employee_assignments.get.{personId}.response.200.message", content = @Content(schema = @Schema(implementation = BrazilEmployeeAssignmentsRest.class))),
                    @ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-brazil_employee_assignments.get.{personId}.response.400.message"),
                    @ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-brazil_employee_assignments.get.{personId}.response.500.message")
            })
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Path(value = "/{personId:[0-9]+}")
    BrazilEmployeeAssignmentsRest retrieveByPersonId(
            @Parameter(description = "@v1.0.commons-persons-brazil_employee_assignments.get.{personId}.pathparam.personid.value", required = true)
            @PathParam(value = "personId") Long personId,
            @Parameter(description = "@v1.0.commons-persons-brazil_employee_assignments.get.{personId}.queryparam.companyDetails.value", required = false)
            @QueryParam(value = "company_details") boolean companyDetails);

    @Operation(
            operationId = "@v1.0.commons-persons-brazil_employee_assignments.get.currentpath.nickname",
            description = "@v1.0.commons-persons-brazil_employee_assignments.get.currentpath.notes",
            summary = "@v1.0.commons-persons-brazil_employee_assignments.get.currentpath.summary",
            responses = {
                    @ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-brazil_employee_assignments.get.currentpath.response.200.message", content = @Content(schema = @Schema(implementation = BrazilEmployeeAssignmentsRest.class))),
                    @ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-brazil_employee_assignments.get.currentpath.response.400.message"),
                    @ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-brazil_employee_assignments.get.currentpath.response.500.message")
            })
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    BrazilEmployeeAssignmentsRest retrieveByPersonIdOrNumber(
            @Parameter(description = "@v1.0.commons-persons-brazil_employee_assignments.get.currentpath.queryparam.personid.value", required = false)
            @QueryParam(value = "employee_id") Long personId,
            @Parameter(description = "@v1.0.commons-persons-brazil_employee_assignments.get.currentpath.queryparam.personNumber.value", required = false)
            @QueryParam(value = "person_number") String personNumber,
            @Parameter(description = "@v1.0.commons-persons-brazil_employee_assignments.get.currentpath.queryparam.companyDetails.value", required = false)
            @QueryParam(value = "company_details") boolean companyDetails);

    @Operation(
            operationId = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_read.nickname",
            description = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_read.notes",
            summary = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_read.summary",
            responses = {
                    @ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_read.response.200.message", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BrazilEmployeeAssignmentsRest.class)))),
                    @ApiResponse(responseCode = "207", description = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_read.response.207.message"),
                    @ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_read.response.400.message"),
                    @ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_read.response.500.message")
            })
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/multi_read")
    Response multiRead(
            @Parameter(description = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_read.where.value", required = true)
            BrazilMultiReadWhere where);

    @Operation(
            operationId = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_upsert.nickname",
            description = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_upsert.notes",
            summary = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_upsert.summary",
            responses = {
                    @ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_upsert.response.200.message", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BrazilEmployeeAssignmentsRest.class)))),
                    @ApiResponse(responseCode = "207", description = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_upsert.response.207.message"),
                    @ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_upsert.response.400.message"),
                    @ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_upsert.response.500.message")
            })
    @POST
    @Path("/multi_upsert")
    Response multiUpsert(
            @Parameter(description = "@v1.0.commons-persons-brazil_employee_assignments.post.multi_upsert.requestbody.value", required = true)
            List<BrazilEmployeeAssignmentsRest> request);

}
