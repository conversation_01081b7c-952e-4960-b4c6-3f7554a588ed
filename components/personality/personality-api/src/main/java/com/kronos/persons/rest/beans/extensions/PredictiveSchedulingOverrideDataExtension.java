package com.kronos.persons.rest.beans.extensions;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;


@Schema(description = "@v1.0.predictiveschedulingoverridedataextension.apimodel.description", name = "predictiveSchedulingOverrideDetails", allOf = ExtensionEffectiveDatedEntry.class)
@JsonPropertyOrder(alphabetic = true)
public class PredictiveSchedulingOverrideDataExtension extends ExtensionEffectiveDatedEntry {
    public static final long serialVersionUID = 1L;

    @Schema(description = "@v1.0.predictiveschedulingoverridedataextension.apimodelproperty.predictiveruleoverride.description")
    private String predictiveRuleOverride;

    @Schema(description = "@v1.0.predictiveschedulingoverridedataextension.apimodelproperty.predictiveruleoverrideid.description")
	private Long predictiveRuleOverrideId;

	/**
	 * @return the predictiveRule
	 */
	public String getPredictiveRuleOverride() {
		return predictiveRuleOverride;
	}

	/**
	 * @param predictiveRuleOverride
	 *            the predictiveRule to set
	 */
	public void setPredictiveRuleOverride(String predictiveRuleOverride) {
		this.predictiveRuleOverride = predictiveRuleOverride;
	}

	/**
	 * @return the predictiveRuleId
	 */
	public Long getPredictiveRuleOverrideId() {
		return predictiveRuleOverrideId;
	}

	/**
	 * @param predictiveRuleOverrideId
	 *            the predictiveRuleOverrideId to set
	 */
	public void setPredictiveRuleOverrideId(Long predictiveRuleOverrideId) {
		this.predictiveRuleOverrideId = predictiveRuleOverrideId;
	}

}
