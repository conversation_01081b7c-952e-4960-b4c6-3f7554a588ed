package com.kronos.persons.rest.model;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * DTO to hold Employee Wage and Work Rules.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * <p>
 * Date: April 1, 2020
 *
 * <AUTHOR>
 */
@Schema(name = "EmployeeWageWorkRules",
        description = "@v1.0.commons-persons-wage_work_rules.employeewageworkrules.apimodel.description")
public class EmployeeWageWorkRulesDTO {

    @Schema(description = "@v1.0.commons-persons-wage_work_rules.employeewageworkrules.apimodelproperty.employee.description")
    private ObjectRef employee;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.employeewageworkrules.apimodelproperty.position.description")
    private PositionRef position;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.employeewageworkrules.apimodelproperty.effectivedatedwageworkrules.description")
    private List<EffectiveDatedWageWorkRulesDTO> effectiveDatedWageWorkRules;

    /**
     * @return employee
     */
    public ObjectRef getEmployee() {
        return employee;
    }

    /**
     * Sets employee.
     *
     * @param employee employee, to be set
     */
    public void setEmployee(ObjectRef employee) {
        this.employee = employee;
    }

    /**
     * @return effective dated wage work rules
     */
    public List<EffectiveDatedWageWorkRulesDTO> getEffectiveDatedWageWorkRules() {
        return effectiveDatedWageWorkRules;
    }

    /**
     * Sets effective dated wage work rules.
     *
     * @param effectiveDatedWageWorkRules effective dated wage work rule, to be set
     */
    public void setEffectiveDatedWageWorkRules(List<EffectiveDatedWageWorkRulesDTO> effectiveDatedWageWorkRules) {
        this.effectiveDatedWageWorkRules = effectiveDatedWageWorkRules;
    }

    public PositionRef getPosition() {
        return position;
    }

    public void setPosition(PositionRef position) {
        this.position = position;
    }
}
