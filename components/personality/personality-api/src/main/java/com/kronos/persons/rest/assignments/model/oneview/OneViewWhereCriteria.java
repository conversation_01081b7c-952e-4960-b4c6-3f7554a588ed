package com.kronos.persons.rest.assignments.model.oneview;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.kronos.persons.rest.model.EmployeeCriteria;

import java.time.LocalDate;

public class OneViewWhereCriteria {

    @JsonProperty(value = "effectiveDate", required = false)
    private LocalDate effectiveDate;

    @JsonProperty(value = "expirationDate", required = false)
    private LocalDate expirationDate;

    @JsonProperty(value = "employees", required = true)
    private EmployeeCriteria employees;

    public LocalDate getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDate effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public LocalDate getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDate expirationDate) {
        this.expirationDate = expirationDate;
    }

    public EmployeeCriteria getEmployees() {
        return employees;
    }

    public void setEmployees(EmployeeCriteria employees) {
        this.employees = employees;
    }
}
