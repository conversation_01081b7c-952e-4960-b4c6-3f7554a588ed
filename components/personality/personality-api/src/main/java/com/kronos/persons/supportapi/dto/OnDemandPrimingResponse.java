package com.kronos.persons.supportapi.dto;

public class OnDemandPrimingResponse {
    private String tenantShortName;

    private String message;

    private String trxid;

    public String getTenantShortName() {
        return tenantShortName;
    }

    public void setTenantShortName(String tenantShortName) {
        this.tenantShortName = tenantShortName;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getTrxid(){return trxid;}
    public void setTrxid(String trxid){this.trxid = trxid;}

    @Override
    public String toString() {
        return "OnDemandPrimingResponse [tenantShortName=" + tenantShortName + ", message=" + message + "trxid=" + trxid +"]";
    }
}

