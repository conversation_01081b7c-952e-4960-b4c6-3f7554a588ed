package com.kronos.persons.rest.model.wageoverride;

import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * This class is used as an intermediate object for storing data
 * from WageWorkRuleOverride and JobTransferEntry while working
 * EmployeeWageWorkRuleConverter.
 * Copyright (C) 2020 Kronos.com
 * Date: Apr 04, 2020
 *
 * <AUTHOR>
 */
public class WageWorkRule implements Comparable<WageWorkRule> {

    private KDate effectiveDate;
    private KDate expirationDate;
    private Integer totalElements;
    private Map<Long, WageWorkRuleDetails> wageWorkRuleDetails;
    private BigDecimal baseWage;
    private Long baseWorkRuleId;
    /**
     * This set used for calculation inactive wage work rule overrides.
     *
     * @see com.kronos.persons.rest.converter.WageWorkRuleDateResolver#removeInactiveOverrides(java.util.List, java.util.List)
     */
    private Set<Long> inactiveJobIds;
    /**
     * The flag is this wage work rule override.
     */
    private boolean isOverride = false;

    /**
     * Constructor.
     */
    public WageWorkRule() {
    }

    /**
     * Constructor.
     *
     * @param effectiveDate  the effective date
     * @param expirationDate the expiration date
     */
    public WageWorkRule(KDate effectiveDate, KDate expirationDate) {
        this.effectiveDate = effectiveDate;
        this.expirationDate = expirationDate;
    }

    /**
     * Constructor.
     *
     * @param effectiveDate       the effective date
     * @param expirationDate      the expiration date
     * @param wageWorkRuleDetails wage work rule details
     * @param isOverride          is this wageWorkRule override
     */
    public WageWorkRule(KDate effectiveDate, KDate expirationDate,
                        Map<Long, WageWorkRuleDetails> wageWorkRuleDetails, boolean isOverride) {
        this.effectiveDate = effectiveDate;
        this.expirationDate = expirationDate;
        setWageWorkRuleDetails(wageWorkRuleDetails);
        this.isOverride = isOverride;
    }

    /**
     * Gets effective date.
     *
     * @return effective date.
     */
    public KDate getEffectiveDate() {
        return effectiveDate;
    }

    /**
     * Set effectiveDate.
     *
     * @param effectiveDate the effective date
     */
    public void setEffectiveDate(KDate effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    /**
     * Gets expiration date.
     *
     * @return expiration date
     */
    public KDate getExpirationDate() {
        return expirationDate;
    }

    /**
     * Set expiration date.
     *
     * @param expirationDate the expiration date
     */
    public void setExpirationDate(KDate expirationDate) {
        this.expirationDate = expirationDate;
    }

    /**
     * Gets wage work rule details.
     *
     * @return wage work rule details
     */
    public Map<Long, WageWorkRuleDetails> getWageWorkRuleDetails() {
        return wageWorkRuleDetails;
    }

    /**
     * Set wage work rule details.
     *
     * @param wageWorkRuleDetails wage work rule details.
     */
    public void setWageWorkRuleDetails(Map<Long, WageWorkRuleDetails> wageWorkRuleDetails) {
        this.wageWorkRuleDetails = wageWorkRuleDetails;
        this.inactiveJobIds = new HashSet<>(wageWorkRuleDetails.keySet());
    }

    /**
     * Gets total existing elements number.
     *
     * @return total existing elements number
     */
    public Integer getTotalElements() {
        return totalElements;
    }

    /**
     * Set total existing elements number.
     *
     * @param totalElements total existing elements number
     */
    public void setTotalElements(Integer totalElements) {
        this.totalElements = totalElements;
    }

    /**
     * Is this wage work rule override.
     *
     * @return is override
     */
    public boolean isOverride() {
        return isOverride;
    }

    /**
     * Set is this wage work rule override.
     *
     * @param override is override
     */
    public void setOverride(boolean override) {
        isOverride = override;
    }

    /**
     * Gets ids of inactive jobs.
     *
     * @return ids of inactive jobs
     */
    public Set<Long> getInactiveJobIds() {
        return inactiveJobIds;
    }

    /**
     * Set ids of inactive jobs.
     *
     * @param inactiveJobIds ids of inactive jobs
     */
    public void setInactiveJobIds(Set<Long> inactiveJobIds) {
        this.inactiveJobIds = inactiveJobIds;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WageWorkRule that = (WageWorkRule) o;
        return new EqualsBuilder()
                .append(isOverride(), that.isOverride())
                .append(getEffectiveDate(), that.getEffectiveDate())
                .append(getExpirationDate(), that.getExpirationDate())
                .append(getWageWorkRuleDetails(), that.getWageWorkRuleDetails())
                .append(getInactiveJobIds(), that.getInactiveJobIds())
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(getEffectiveDate())
                .append(getExpirationDate())
                .append(getWageWorkRuleDetails())
                .append(getInactiveJobIds())
                .append(isOverride())
                .toHashCode();
    }

    @Override
    public int compareTo(WageWorkRule wageWorkRule) {
        return this.getEffectiveDate().compareTo(wageWorkRule.getEffectiveDate());
    }

    public BigDecimal getBaseWage() {
        return baseWage;
    }

    public void setBaseWage(BigDecimal baseWage) {
        this.baseWage = baseWage;
    }

    public Long getBaseWorkRuleId() {
        return baseWorkRuleId;
    }

    public void setBaseWorkRuleId(Long baseWorkRuleId) {
        this.baseWorkRuleId = baseWorkRuleId;
    }
}
