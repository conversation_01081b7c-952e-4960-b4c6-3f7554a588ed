package com.kronos.persons.rest.assignments.model;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.persons.rest.beans.PeopleAssignmentBean;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "employeeJobPreferences", description = "@v1.0.employeeJobPreferencesBean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class EmployeeJobPreferencesAssignmentBean extends PeopleAssignmentBean implements BaseAssignmentBean,Serializable {

	private static final long serialVersionUID = 123456787567653456L;
	
	@Schema(description = "@v1.0.employeeJobPreferencesBean.apimodelproperty.employeeJobPreferences.description")
	private List<JobPreferenceAssignmentBean> employeeJobPreferences;
	
	@Schema(description = "@v1.0.employeeJobPreferencesBean.apimodelproperty.effectiveDate.description")
	private LocalDate effectiveDate;
	
	public List<JobPreferenceAssignmentBean> getEmployeeJobPreferences() {
		return employeeJobPreferences;
	}

	public void setEmployeeJobPreferences(List<JobPreferenceAssignmentBean> employeeJobPreferences) {
		this.employeeJobPreferences = employeeJobPreferences;
	}

	public LocalDate getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(LocalDate effectiveDate) {
		this.effectiveDate = effectiveDate;
	}
}