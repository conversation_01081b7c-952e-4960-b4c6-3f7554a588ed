package com.kronos.persons.rest.model;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * DTO to hold list of date ranges for a person.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * <p>
 * Date: March 6, 2020
 *
 * <AUTHOR>
 */
@Schema(name = "DateRangeList",
        description = "@v1.0.commons-persons-wage_work_rules.daterangelist.apimodel.description")
public class DateRangeListDTO {

    @Schema(description = "@v1.0.commons-persons-wage_work_rules.daterangelist.apimodelproperty.personid.description")
    private ObjectRef personId;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.daterangelist.apimodelproperty.positionid.description")
    private Long positionId;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.daterangelist.apimodelproperty.availabledateranges.description")
    private List<DateRangeDTO> availableDateRanges;

    /**
     * @return person id
     */
    public ObjectRef getPersonId() {
        return personId;
    }

    /**
     * Sets person id.
     *
     * @param personId person id, to be set
     */
    public void setPersonId(ObjectRef personId) {
        this.personId = personId;
    }

    /**
     * @return position id
     */
    public Long getPositionId() {
        return positionId;
    }

    /**
     * Sets position id.
     *
     * @param positionId
     */
    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    /**
     * @return list of available date ranges
     */
    public List<DateRangeDTO> getAvailableDateRanges() {
        return availableDateRanges;
    }

    /**
     * Sets available date ranges.
     *
     * @param availableDateRanges available date ranges, to be set
     */
    public void setAvailableDateRanges(List<DateRangeDTO> availableDateRanges) {
        this.availableDateRanges = availableDateRanges;
    }
}
