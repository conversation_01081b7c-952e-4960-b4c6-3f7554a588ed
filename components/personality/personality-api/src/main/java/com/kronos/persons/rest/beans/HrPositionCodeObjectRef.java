package com.kronos.persons.rest.beans;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.io.Serializable;

public class HrPositionCodeObjectRef implements Serializable {
    /**
     * Serial version unique identifier
     */
    private static final long serialVersionUID = 1L;
    @Schema(description = "@v1.0.positioncode.apimodelproperty.id.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected Long id;
    @Schema(description = "@v1.0.positioncode.apimodelproperty.name.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected String name;
    @Schema(description = "@v1.0.positioncode.apimodelproperty.startDate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected String startDate;
    @Schema(description = "@v1.0.positioncode.apimodelproperty.endDate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected String endDate;

    /**
     * @param id the id to set
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return Id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param name the positionCodeName to set
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return name the name to set
     */
    public String getName() {
        return name;
    }

    /**
     * @param startDate the startDate to set
     */
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    /**
     * @return startDate
     */
    public String getStartDate() {
        return startDate;
    }

    /**
     * @param endDate the endDate to set
     */
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    /**
     * @return endDate
     */
    public String getEndDate() {
        return endDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        PositionCodeBean that = (PositionCodeBean) o;

        return new EqualsBuilder()
                .append(id, that.id)
                .append(name, that.name)
                .append(startDate, that.startDate)
                .append(endDate, that.endDate)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(id)
                .append(name)
                .append(startDate)
                .append(endDate)
                .toHashCode();
    }
}
