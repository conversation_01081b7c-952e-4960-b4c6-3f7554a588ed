package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.jobassignmentdetailsbean.apimodel.description", name =  "jobAssignmentDetails")
@JsonPropertyOrder(alphabetic = true)
public class JobAssignmentDetailsBean {

    private String deviceGroupName;

    //deviceProfileName is only applicable to the new Sirius device which is not released
    //yet. Commenting out Swagger annotation so this attribute is not part of the documentation until
    //the device is released
    //@Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.udmdeviceprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String deviceProfileName;
    
    @Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.payrulename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String payRuleName;

    @Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.payruleeffectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String payRuleEffectiveDate;

    @Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.seniorityrankdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String seniorityRankDate;

    @Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.supervisorname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String supervisorName;

    @Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.supervisorpersonnumber.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String supervisorPersonNumber;

    @Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.timezonename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String timeZoneName;

    @Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.basewageeffectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String baseWageEffectiveDate;

    @Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.basewagehourly.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Double baseWageHourly;

    @Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.workertypename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String workerTypeName;

	@Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.teletimeipemployee.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Boolean teletimeIPEmployee;

	@Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.teletimeipchangepasswordisrequired.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Boolean teletimeIPChangePasswordIsRequired;

	@Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.teletimeipid.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String teletimeIPId;

	@Schema(description =  "@v1.0.jobassignmentdetailsbean.Schema.udmteeltimeipprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String teletimeIPProfileName;

	/**
	 * @return the deviceGroupName
	 */
	public String getDeviceGroupName() {
		return deviceGroupName;
	}

	/**
	 * @param deviceGroupName
	 *            the deviceGroupName to set
	 */
	public void setDeviceGroupName(String deviceGroupName) {
		this.deviceGroupName = deviceGroupName;
	}

	/**
         * @return the deviceProfileName
         */
        public String getDeviceProfileName() {
                return deviceProfileName;
        }

        /**
         * @param deviceProfileName
         *            the deviceProfileName to set
         */
        public void setDeviceProfileName(String deviceProfileName) {
                this.deviceProfileName = deviceProfileName;
        }
	
	/**
	 * @return the payRuleName
	 */
	public String getPayRuleName() {
		return payRuleName;
	}

	/**
	 * @param payRuleName
	 *            the payRuleName to set
	 */
	public void setPayRuleName(String payRuleName) {
		this.payRuleName = payRuleName;
	}

	/**
	 * @return the payRuleEffectiveDate
	 */
	public String getPayRuleEffectiveDate() {
		return payRuleEffectiveDate;
	}

	/**
	 * @param payRuleEffectiveDate
	 *            the payRuleEffectiveDate to set
	 */
	public void setPayRuleEffectiveDate(String payRuleEffectiveDate) {
		this.payRuleEffectiveDate = payRuleEffectiveDate;
	}

	/**
	 * @return the seniorityRankDate
	 */
	public String getSeniorityRankDate() {
		return seniorityRankDate;
	}

	/**
	 * @param seniorityRankDate
	 *            the seniorityRankDate to set
	 */
	public void setSeniorityRankDate(String seniorityRankDate) {
		this.seniorityRankDate = seniorityRankDate;
	}

	/**
	 * @return the supervisorName
	 */
	public String getSupervisorName() {
		return supervisorName;
	}

	/**
	 * @param supervisorName
	 *            the supervisorName to set
	 */
	public void setSupervisorName(String supervisorName) {
		this.supervisorName = supervisorName;
	}

	/**
	 * @return the supervisorPersonNumber
	 */
	public String getSupervisorPersonNumber() {
		return supervisorPersonNumber;
	}

	/**
	 * @param supervisorPersonNumber
	 *            the supervisorPersonNumber to set
	 */
	public void setSupervisorPersonNumber(String supervisorPersonNumber) {
		this.supervisorPersonNumber = supervisorPersonNumber;
	}

	/**
	 * @return the timeZoneName
	 */
	public String getTimeZoneName() {
		return timeZoneName;
	}

	/**
	 * @param timeZoneName
	 *            the timeZoneName to set
	 */
	public void setTimeZoneName(String timeZoneName) {
		this.timeZoneName = timeZoneName;
	}

	/**
	 * @return the baseWageEffectiveDate
	 */
	public String getBaseWageEffectiveDate() {
		return baseWageEffectiveDate;
	}

	/**
	 * @param baseWageEffectiveDate
	 *            the baseWageEffectiveDate to set
	 */
	public void setBaseWageEffectiveDate(String baseWageEffectiveDate) {
		this.baseWageEffectiveDate = baseWageEffectiveDate;
	}

	/**
	 * @return the baseWageHourly
	 */
	public Double getBaseWageHourly() {
		return baseWageHourly;
	}

	/**
	 * @param baseWageHourly
	 *            the baseWageHourly to set
	 */
	public void setBaseWageHourly(Double baseWageHourly) {
		this.baseWageHourly = baseWageHourly;
	}

	/**
	 * @return the workerTypeName
	 */
	public String getWorkerTypeName() {
		return workerTypeName;
	}

	/**
	 * @param workerTypeName
	 *            the workerTypeName to set
	 */
	public void setWorkerTypeName(String workerTypeName) {
		this.workerTypeName = workerTypeName;
	}

	public Boolean getTeletimeIPEmployee() {
		return teletimeIPEmployee;
	}

	public void setTeletimeIPEmployee(Boolean teletimeIPEmployee) {
		this.teletimeIPEmployee = teletimeIPEmployee;
	}

	public Boolean getTeletimeIPChangePasswordIsRequired() {
		return teletimeIPChangePasswordIsRequired;
	}

	public void setTeletimeIPChangePasswordIsRequired(Boolean teletimeIPChangePasswordIsRequired) {
		this.teletimeIPChangePasswordIsRequired = teletimeIPChangePasswordIsRequired;
	}

	public String getTeletimeIPId() {
		return teletimeIPId;
	}

	public void setTeletimeIPId(String teletimeIPId) {
		this.teletimeIPId = teletimeIPId;
	}

	public String getTeletimeIPProfileName() {
		return teletimeIPProfileName;
	}

	public void setTeletimeIPProfileName(String teletimeIPProfileName) {
		this.teletimeIPProfileName = teletimeIPProfileName;
	}

	@Override
	public String toString() {
	        return "JobAssignmentDetailsBean [deviceGroupName=" + deviceGroupName
	                + ", deviceProfileName=" + deviceProfileName
                    + ", payRuleName=" + payRuleName
                    + ", payRuleEffectiveDate=" + payRuleEffectiveDate
                    + ", seniorityRankDate=" + seniorityRankDate
                    + ", supervisorPersonNumber=" + supervisorPersonNumber
                    + ", supervisorName=" + supervisorName
                    + ", timeZoneName=" + timeZoneName
                    + ", baseWageEffectiveDate=" + baseWageEffectiveDate
                    + ", baseWageHourly=" + baseWageHourly
                    + ", workerTypeName="+ workerTypeName
					+ ", teletimeIPEmployee=" + teletimeIPEmployee
					+ ", teletimeIPChangePasswordIsRequired=" + teletimeIPChangePasswordIsRequired
					+ ", teletimeIPId=" + teletimeIPId
                    + ", teletimeIPProfileName="+ teletimeIPProfileName
                    + "]";

	}
}