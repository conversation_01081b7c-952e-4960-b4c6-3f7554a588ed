package com.kronos.persons.rest.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO to hold Wage and Work Rule for Position and Job.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * <p>
 * Date: March 6, 2020
 *
 * <AUTHOR>
 */
@Schema(name = "PositionWageWorkRule",
        description = "@v1.0.commons-persons-wage_work_rules.positionwageworkrule.apimodel.description")
public class PositionWageWorkRuleDTO {

    @Schema(description = "@v1.0.commons-persons-wage_work_rules.positionwageworkrule.apimodelproperty.positionid.description")
    @JsonIgnore
    private Long positionId;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.positionwageworkrule.apimodelproperty.job.description")
    private ObjectRef job;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.positionwageworkrule.apimodelproperty.wage.description")
    private String wage;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.positionwageworkrule.apimodelproperty.workrule.description")
    private ObjectRef workRule;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.positionwageworkrule.apimodelproperty.version.description")
    private Integer version;

    /**
     * @return position id
     */
    public Long getPositionId() {
        return positionId;
    }

    /**
     * Sets position id.
     *
     * @param positionId position id, to be set
     */
    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    /**
     * @return job
     */
    public ObjectRef getJob() {
        return job;
    }

    /**
     * Sets job.
     *
     * @param job job, to be set
     */
    public void setJob(ObjectRef job) {
        this.job = job;
    }

    /**
     * @return wage
     */
    public String getWage() {
        return wage;
    }

    /**
     * Sets wage.
     *
     * @param wage wage, to be set
     */
    public void setWage(String wage) {
        this.wage = wage;
    }

    /**
     * @return work rule
     */
    public ObjectRef getWorkRule() {
        return workRule;
    }

    /**
     * Sets work rule.
     *
     * @param workRule work rule, to be set
     */
    public void setWorkRule(ObjectRef workRule) {
        this.workRule = workRule;
    }

    /**
     * @return version
     */
    public Integer getVersion() {
        return version;
    }

    /**
     * Sets version.
     *
     * @param version version, to be set
     */
    public void setVersion(Integer version) {
        this.version = version;
    }
}
