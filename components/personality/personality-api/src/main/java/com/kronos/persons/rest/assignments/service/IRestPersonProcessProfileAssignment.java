package com.kronos.persons.rest.assignments.service;

import java.util.List;

import com.kronos.persons.rest.assignments.model.ProcessProfileAssignmentRequestBean;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

/**
 * The rest api's are used to retrieve and update process profiles of a person. Bulk retrieve and update are also supported.
 * <AUTHOR>
 *
 */
@OpenAPIDefinition(tags = {
		@Tag(description = "@v1.0.commons-persons-process_profile.description", extensions = @Extension(properties = {
				@ExtensionProperty(name = "parent", value = "@root.personassignments.default") }), name = "@v1.0.commons-persons-process_profile.name") })
@Tag(name = "@v1.0.commons-persons-process_profile.name")
@Publishable(type = RestAPITypes.PUBLIC, value = true, productType = ProductTypes.WFP)
@Path("/v1/commons/persons/process_profiles")
public interface IRestPersonProcessProfileAssignment {
	
	@GET
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "@v1.0.commons-persons-process_profile.get.currentpath.value", description = "@v1.0.commons-persons-process_profile.get.currentpath.notes", tags = "@v1.0.commons-persons-process_profile.get.currentpath.nickname")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-process_profile.get.currentpath.response.200.message", content = @Content(schema = @Schema(implementation = ProcessProfileAssignmentRequestBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-process_profile.get.currentpath.response.400.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-process_profile.get.currentpath.response.500.message")})
	@Path("/")
	public ProcessProfileAssignmentRequestBean retrieveByPersonNumber(
			@Parameter(description = "@v1.0.commons-persons_assignments-process_profile.get.currentpath.queryparam.person_number.value", required = false) @QueryParam(value = "person_number") String personNumber
	);
	
	@GET
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "@v1.0.commons-persons-process_profile.get.{personid}.value", description = "@v1.0.commons-persons-process_profile.get.{personid}.notes", tags = "@v1.0.commons-persons-process_profile.get.{personid}.nickname")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-process_profile.get.{personid}.response.200.message", content = @Content(schema = @Schema(implementation = ProcessProfileAssignmentRequestBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-process_profile.get.{personid}.response.400.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-process_profile.get.{personid}.response.500.message")})
	@Path(value = "/{personId:[0-9]+}")
	public ProcessProfileAssignmentRequestBean retrieveByPersonId(
			@Parameter(description = "@v1.0.commons-persons-process_profile.get.{personid}.pathparam.personid.value") @PathParam(value = "personId") Long personId);

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "@v1.0.commons-persons-process_profile.post.multi_read.name", description = "@v1.0.commons-persons-process_profile.post.multi_read.notes", tags = "@v1.0.commons-persons-process_profile.post.multi_read.nickname")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-process_profile.post.multi_read.response.200.message",content = @Content(schema = @Schema(type="list", implementation = ProcessProfileAssignmentRequestBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-process_profile.post.multi_read.response.400.message"),
			@ApiResponse(responseCode = "207", description = "@v1.0.commons-persons-process_profile.post.multi_read.response.207.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-process_profile.post.multi_read.response.500.message")})
	@Path("/multi_read")
	public List<ProcessProfileAssignmentRequestBean> retrieveList(ExtensionSearchCriteria searchCriteria);

	@PUT
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "@v1.0.commons-persons-process_profile.put.currentpath.summary", description = "@v1.0.commons-persons-process_profile.put.currentpath.notes", tags = "@v1.0.commons-persons-process_profile.put.currentpath.nickname")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-process_profile.put.currentpath.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = ProcessProfileAssignmentRequestBean.class))),
			@ApiResponse(description = "@v1.0.commons-persons-process_profile.put.currentpath.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-process_profile.put.currentpath.response.500.message", responseCode = "500") })
	@Path("/update")
	public ProcessProfileAssignmentRequestBean update(ProcessProfileAssignmentRequestBean requestBean);

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "@v1.0.commons-persons-process_profile.post.multi_update.summary", description = "@v1.0.commons-persons-process_profile.post.multi_update.notes", tags = "@v1.0.commons-persons-process_profile.post.multi_update.nickname")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-process_profile.post.multi_update.response.200.message", content = @Content(schema = @Schema(type="list", implementation = ProcessProfileAssignmentRequestBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-process_profile.post.multi_update.response.400.message"),
			@ApiResponse(responseCode = "207", description = "@v1.0.commons-persons-process_profile.post.multi_update.response.207.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-process_profile.post.multi_update.response.500.message")})
	@Path("/multi_update")
	public List<ProcessProfileAssignmentRequestBean> multiUpdate(List<ProcessProfileAssignmentRequestBean> requestBean);

}
