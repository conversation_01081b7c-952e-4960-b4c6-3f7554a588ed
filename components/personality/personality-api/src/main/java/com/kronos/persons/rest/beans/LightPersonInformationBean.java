package com.kronos.persons.rest.beans;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.lightpersoninformationbean.apimodel.description", title = "lightPersonInformationBean")

public class LightPersonInformationBean{
    @Schema(description = "@v1.0.lightpersoninformationbean.apimodelproperty.personid.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String employeeID;
    
    @Schema(description = "@v1.0.lightpersoninformationbean.apimodelproperty.personnum.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long personNum;

    @Schema(description = "@v1.0.lightpersoninformationbean.apimodelproperty.firstName.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String firstName;
    
    @Schema(description = "@v1.0.lightpersoninformationbean.apimodelproperty.lastName.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String lastName;
    
    @Schema(description = "@v1.0.lightpersoninformationbean.apimodelproperty.birthDate.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String birthDate;
    
    @Schema(description = "@v1.0.lightpersoninformationbean.apimodelproperty.updatedTime.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String updatedTime;
    
    @Schema(description = "@v1.0.lightpersoninformationbean.apimodelproperty.emailAddresses.description", requiredMode = Schema.RequiredMode.REQUIRED)
    Set<EmailAddressMissingPersonBean> emailAddresses=new HashSet<EmailAddressMissingPersonBean>();
    
    @Schema(description = "@v1.0.lightpersoninformationbean.apimodelproperty.telephoneNumbers.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<TelephoneNumberMissingPersonBean> telephoneNumbers= new ArrayList<TelephoneNumberMissingPersonBean>();
    

	public String getEmployeeID() {
		return employeeID;
	}

	public void setEmployeeID(String employeeID) {
		this.employeeID = employeeID;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getBirthDate() {
		return birthDate;
	}

	public void setBirthDate(String birthDate) {
		this.birthDate = birthDate;
	}

	public String getUpdatedTime() {
		return updatedTime;
	}

	public void setUpdatedTime(String updatedTime) {
		this.updatedTime = updatedTime;
	}

	

	public Long getPersonNum() {
		return personNum;
	}

	public void setPersonNum(Long personNum) {
		this.personNum = personNum;
	}

	public Set<EmailAddressMissingPersonBean> getEmailAddresses() {
		return emailAddresses;
	}

	public void setEmailAddresses(Set<EmailAddressMissingPersonBean> emailAddresses) {
		this.emailAddresses = emailAddresses;
	}

	public List<TelephoneNumberMissingPersonBean> getTelephoneNumbers() {
		return telephoneNumbers;
	}

	public void setTelephoneNumbers(List<TelephoneNumberMissingPersonBean> telephoneNumbers) {
		this.telephoneNumbers = telephoneNumbers;
	}

}
