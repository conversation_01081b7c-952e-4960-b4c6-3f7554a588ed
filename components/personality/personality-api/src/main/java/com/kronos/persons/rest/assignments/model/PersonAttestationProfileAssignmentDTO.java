package com.kronos.persons.rest.assignments.model;

import java.io.Serializable;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "personAttestationProfileAssignmentDTO", description = "@v1.0.personattestationprofileassignmentdto.apimodel.description")
@JsonPropertyOrder
public class PersonAttestationProfileAssignmentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "@v1.0.personattestationprofileassignmentdto.apimodelproperty.person.description")
    private ObjectRef person;

    @Schema(description = "@v1.0.personattestationprofileassignmentdto.apimodelproperty.profile.description")
    private ObjectRef profile;

    @Schema(description = "@v1.0.personattestationprofileassignmentdto.apimodelproperty.effectiveDate.description")
    private LocalDate effectiveDate;

    @Schema(description = "@v1.0.personattestationprofileassignmentdto.apimodelproperty.expirationDate.description")
    private LocalDate expirationDate;

    @Schema(description = "@v1.0.personattestationprofileassignmentdto.apimodelproperty.assignToManagerRole.description")
    private Boolean assignToManagerRole;

    public ObjectRef getPerson() {
        return person;
    }

    public void setPerson(ObjectRef person) {
        this.person = person;
    }

    public ObjectRef getProfile() {
        return profile;
    }

    public void setProfile(ObjectRef profile) {
        this.profile = profile;
    }

    public LocalDate getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDate effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public LocalDate getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDate expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Boolean getAssignToManagerRole() {
        return assignToManagerRole;
    }

    public void setAssignToManagerRole(Boolean assignToManagerRole) {
        this.assignToManagerRole = assignToManagerRole;
    }
}
