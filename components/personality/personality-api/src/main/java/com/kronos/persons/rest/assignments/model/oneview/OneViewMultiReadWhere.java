package com.kronos.persons.rest.assignments.model.oneview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "@v1.0.oneviewmultireadwhere.apimodel.description", value = "oneViewMultiReadWhere")
public class OneViewMultiReadWhere {

    @ApiModelProperty(value = "@v1.0.oneviewmultireadwhere.apimodelproperty.where.description", required = true)
    OneViewWhereCriteria where;

    public OneViewWhereCriteria getWhere() {
        return where;
    }

    public void setWhere(OneViewWhereCriteria where) {
        this.where = where;
    }
}