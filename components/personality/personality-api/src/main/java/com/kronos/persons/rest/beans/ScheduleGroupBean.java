package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignment;
import com.kronos.wfc.commonapp.people.business.person.group.ScheduleGroupAssignment;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

@Schema(description = "@v1.0.schedulegroupbean.apimodel.description", name = "scheduleGroup")
@JsonPropertyOrder(alphabetic = true)
public class ScheduleGroupBean implements IPersonGroupBean, Serializable {
    @Schema(description = "@v1.0.schedulegroupbean.apimodelproperty.name.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String name;

    @Schema(description = "@v1.0.schedulegroupbean.apimodelproperty.startdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String startDate;

    @Schema(description = "@v1.0.schedulegroupbean.apimodelproperty.enddate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String endDate;

    @Schema(description = "@v1.0.schedulegroupbean.apimodelproperty.originalstartdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String originalStartDate;

    @Schema(description = "@v1.0.schedulegroupbean.apimodelproperty.removefromothergroups.description")
    private Boolean removeFromOtherGroups;

    /**
     * The ScheduleGroup ID of the ScheduleGroup which is named in this
     * assignment bean
     */
    @JsonIgnore
    private ObjectIdLong scheduleGroupId = null;

    /**
     * The ScheduleGroupAssignment that is being edited (only used in the case
     * where this bean represents an edit of an existing assignment)
     */
    @JsonIgnore
    private ScheduleGroupAssignment assignmentBeingEdited = null;

    /**
     * The ScheduleGroupAssignments that are being deleted (only used in the case
     * where this bean represents a delete of existing assignments)
     */
    @JsonIgnore
    private transient List<ObjectIdLong> assignmentIdsBeingDeleted = null;

    /**
     * The JobAssignment in which this ScheduleGroupAssignment resides
     */
    @JsonIgnore
    private JobAssignment jobAssignment = null;

    @JsonIgnore
    private ActionType actionType = ActionType.UNKNOWN;

    /**
     * Flag that indicates whether AssignmentSpan cut logic was executed during update of ScheduleGroups.
     */
    @JsonIgnore
    private Boolean assignmentSpanCutLogicExecuted = Boolean.FALSE;

    @Override
    public String getName() {
        return name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getStartDate() {
        return startDate;
    }

    @Override
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    @Override
    public String getEndDate() {
        return endDate;
    }

    @Override
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @Override
    public String getOriginalStartDate() {
        return originalStartDate;
    }

    @Override
    public void setOriginalStartDate(String originalStartDate) {
        this.originalStartDate = originalStartDate;
    }

    /**
     * @return the scheduleGroupId
     */
    public ObjectIdLong getScheduleGroupId() {
        return scheduleGroupId;
    }

    /**
     * @param scheduleGroupId the scheduleGroupId to set
     */
    public void setScheduleGroupId(ObjectIdLong scheduleGroupId) {
        this.scheduleGroupId = scheduleGroupId;
    }

    /**
     * @return the assignmentBeingEdited
     */
    public ScheduleGroupAssignment getAssignmentBeingEdited() {
        return assignmentBeingEdited;
    }

    /**
     * @param assignmentBeingEdited the assignmentBeingEdited to set
     */
    public void setAssignmentBeingEdited(ScheduleGroupAssignment assignmentBeingEdited) {
        this.assignmentBeingEdited = assignmentBeingEdited;
    }

    /**
     * @return the assignmentIdsBeingDeleted list
     */
    public List<ObjectIdLong> getAssignmentIdsBeingDeleted() {
        return assignmentIdsBeingDeleted;
    }

    /**
     * @param assignmentIdsBeingDeleted the assignmentIdsBeingDeleted list to set
     */
    public void setAssignmentIdsBeingDeleted(List<ObjectIdLong> assignmentIdsBeingDeleted) {
        this.assignmentIdsBeingDeleted = assignmentIdsBeingDeleted;
    }

    /**
     * @return the jobAssignment
     */
    public JobAssignment getJobAssignment() {
        return jobAssignment;
    }

    /**
     * @param jobAssignment the jobAssignment to set
     */
    public void setJobAssignment(JobAssignment jobAssignment) {
        this.jobAssignment = jobAssignment;
    }

    @Override
    public ActionType getActionType() {
        return actionType;
    }

    @Override
    public void setActionType(ActionType actionType) {
        this.actionType = actionType;
    }

    @Override
    @JsonIgnore(value = false)
    public Boolean getRemoveFromOtherGroups() {
        return removeFromOtherGroups;
    }

    @Override
    public void setRemoveFromOtherGroups(Boolean removeFromOtherGroups) {
        this.removeFromOtherGroups = removeFromOtherGroups;
    }

    @Override
    public Boolean isAssignmentSpanCutLogicExecuted() {
        return assignmentSpanCutLogicExecuted;
    }

    @Override
    public void setAssignmentSpanCutLogicExecuted(Boolean assignmentSpanCutLogicExecuted) {
        this.assignmentSpanCutLogicExecuted = assignmentSpanCutLogicExecuted;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ScheduleGroupBean that = (ScheduleGroupBean) o;

        if (name != null ? !name.equals(that.name) : that.name != null) return false;
        if (startDate != null ? !startDate.equals(that.startDate) : that.startDate != null) return false;
        if (endDate != null ? !endDate.equals(that.endDate) : that.endDate != null) return false;
        if (removeFromOtherGroups != null ? !removeFromOtherGroups.equals(that.removeFromOtherGroups) : that.removeFromOtherGroups != null) return false;
        return scheduleGroupId != null ? scheduleGroupId.equals(that.scheduleGroupId) : that.scheduleGroupId == null;
    }

    @Override
    public int hashCode() {
        int result = name != null ? name.hashCode() : 0;
        result = 31 * result + (startDate != null ? startDate.hashCode() : 0);
        result = 31 * result + (endDate != null ? endDate.hashCode() : 0);
        result = 31 * result + (scheduleGroupId != null ? scheduleGroupId.hashCode() : 0);
        result = 31 * result + (removeFromOtherGroups != null ? removeFromOtherGroups.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ScheduleGroupBean {" +
                "name='" + name + '\'' +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", scheduleGroupId=" + scheduleGroupId +
                ", removeFromOtherGroups='" + removeFromOtherGroups +
                '}';
    }
}
