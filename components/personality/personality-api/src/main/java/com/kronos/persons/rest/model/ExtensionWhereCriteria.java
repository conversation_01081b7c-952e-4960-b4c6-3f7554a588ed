/**
 * 
 */
package com.kronos.persons.rest.model;

import java.io.Serializable;

import com.kronos.persons.rest.beans.HyperFindFilterBean;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
@Schema(description = "@v1.0.extensionwherecriteria.apimodel.description", title = "extensionWhereCriteria")
public class ExtensionWhereCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "@v1.0.extensionwherecriteria.apimodelproperty.employees.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private EmployeeCriteria employees;

    @Schema(description = "@v1.0.extensionwherecriteria.apimodelproperty.extensiontype.description", allowableValues = "accrual,device,employee,scheduling,timekeeping", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String extensionType;

    @Schema(description = "@v1.0.extensionwherecriteria.apimodelproperty.onlyactiveperson.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean onlyActivePerson = false;
    
    @Schema(description = "@v1.0.extensionwherecriteria.apimodelproperty.basewages.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String includeBaseWages;

	@Schema(description = "@v1.0.extensionwherecriteria.apimodelproperty.snapshotdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String snapshotDate;

    @Schema(description = "@v1.0.extensionwherecriteria.apimodelproperty.snapshotDateTime.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String snapshotDateTime;
    
    @Schema(description = "@v1.0.extensionwherecriteria.apimodelproperty.hyperfindfilter.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private HyperFindFilterBean hyperFindFilter;
    
    @Schema(description = "@v1.0.extensionwherecriteria.apimodelproperty.isEmployeeAllowed.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean isEmployeeAllowed = false;
    
	@Schema(description = "@v1.0.extensionwherecriteria.apimodelproperty.includeaccrualpolicydetails.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Boolean includeAccrualPolicyDetails = false;
    

    public ExtensionWhereCriteria() {
        this.employees = new EmployeeCriteria();
    }

    /**
     * @return the employee
     */
    public EmployeeCriteria getEmployees() {
        return employees;
    }

    /**
     * @param employees
     *            the employee to set
     */
    public void setEmployees(EmployeeCriteria employees) {
        this.employees = employees;
    }

    /**
     * @return the extensionType
     */
    public String getExtensionType() {
        return extensionType;
    }

    /**
     * @param extensionType
     *            the extensionType to set
     */
    public void setExtensionType(String extensionType) {
        this.extensionType = extensionType;
    }

    /**
     * @return the onlyActivePerson
     */
    public Boolean getOnlyActivePerson() {
        return onlyActivePerson;
    }

    /**
     * @param onlyActivePerson
     *            the onlyActivePerson to set
     */
    public void setOnlyActivePerson(Boolean onlyActivePerson) {
        this.onlyActivePerson = onlyActivePerson;
    }

    /**
     * @return the snapshotDate
     */
    public String getSnapshotDate() {
        return snapshotDate;
    }

    /**
     * @param snapshotDate
     *            the snapshotDate to set
     */
    public void setSnapshotDate(String snapshotDate) {
        this.snapshotDate = snapshotDate;
    }

    public HyperFindFilterBean getHyperFindFilter() {
        return hyperFindFilter;
    }

    public void setHyperFindFilter(HyperFindFilterBean hyperFindFilter) {
        this.hyperFindFilter = hyperFindFilter;
    }

    /**
	 * @return the isEmployeeAllowed
	 */
	public Boolean getIsEmployeeAllowed() {
		return isEmployeeAllowed;
	}

	/**
	 * @param isEmployeeAllowed the isEmployeeAllowed to set
	 */
	public void setIsEmployeeAllowed(Boolean isEmployeeAllowed) {
		this.isEmployeeAllowed = isEmployeeAllowed;
	}


    public String getSnapshotDateTime() {
        return snapshotDateTime;
    }

    public void setSnapshotDateTime(String snapshotDateTime) {
        this.snapshotDateTime = snapshotDateTime;
    }
    

	public Boolean getIncludeAccrualPolicyDetails() {
		return includeAccrualPolicyDetails;
	}

	public void setIncludeAccrualPolicyDetails(Boolean includeAccrualPolicyDetails) {
		this.includeAccrualPolicyDetails = includeAccrualPolicyDetails;
	}
	
	public String getIncludeBaseWages() {
		return includeBaseWages;
	}

	public void setIncludeBaseWages(String includeBaseWages) {
		this.includeBaseWages = includeBaseWages;
	}


	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("{");
		builder.append("employees:" + getEmployees() + ", ");
		builder.append("onlyActivePerson:" + getOnlyActivePerson() + ", ");
		builder.append("baseWages:" + getIncludeBaseWages() + ", ");
		builder.append("snapshotDate:" + getSnapshotDate() + ", ");
		builder.append("snapshotDateTime:" + getSnapshotDateTime() + ", ");
		builder.append("extensionType:" + getExtensionType() + ", ");
		builder.append("hyperFindFilter:" + getHyperFindFilter() + ", ");
		builder.append("isEmplyeeAllowed:" + getIsEmployeeAllowed() + " ");
		builder.append("includeAccrualPolicyDetails:" + getIncludeAccrualPolicyDetails() + " ");
		builder.append("}");
		return builder.toString();
	}

}
