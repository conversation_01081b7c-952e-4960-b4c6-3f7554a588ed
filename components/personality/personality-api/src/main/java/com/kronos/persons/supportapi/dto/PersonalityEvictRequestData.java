package com.kronos.persons.supportapi.dto;

import java.util.List;

/**
 *This class represents the object respresentation of request template for evict_person_cache rest API.
 *This class contains objects field mapped with its json request structure for Caching evict Support API.
 * 
 * <AUTHOR>
 */

public class PersonalityEvictRequestData {

	private String tenantShortName;
	private String evictFrom;
	private List<PersonDetail> personNumberList;
	
	public String getEvictFrom() {
		return evictFrom;
	}

	public void setEvictFrom(String evictFrom) {
		this.evictFrom = evictFrom;
	}

	public String getTenantShortName() {
		return tenantShortName;
	}

	public void setTenantShortName(String tenantShortName) {
		this.tenantShortName = tenantShortName;
	}

	public List<PersonDetail> getPersonNumberList() {
		return personNumberList;
	}

	public void setPersonNumberList(List<PersonDetail> personNumberList) {
		this.personNumberList = personNumberList;
	}

	@Override
	public String toString() {
		return "PersonalityEvictRequestData [tenantShortName=" + tenantShortName + ", evictFrom=" + evictFrom + ", personNumberList="
				+ personNumberList + "]";
	}
}
