package com.kronos.persons.rest.assignments.model.percentageallocationrule;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.model.BeanWrapper;

public class PercentageAllocationRuleAssignmentWrapper implements BeanWrapper<PercentageAllocationRuleAssignmentBean> {

    private PercentageAllocationRuleAssignmentBean wrapperObject;
    private APIException apiException;

    public PercentageAllocationRuleAssignmentWrapper(PercentageAllocationRuleAssignmentBean wrapperObject) {
        this.wrapperObject = wrapperObject;
    }

    @Override
    public APIException getApiException() {
        return apiException;
    }

    @Override
    public void setApiException(APIException apiException) {
        this.apiException = apiException;
    }

    @Override
    public PercentageAllocationRuleAssignmentBean getBean() {
        return wrapperObject;
    }

    @Override
    public void setBean(PercentageAllocationRuleAssignmentBean bean) {
        this.wrapperObject = bean;
    }
}
