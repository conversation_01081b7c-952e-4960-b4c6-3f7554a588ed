package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.socialaccountsbean.apimodel.description", name = "socialAccounts")
@JsonPropertyOrder(alphabetic = true)
public class SocialAccountsBean {
    @Schema(description = "@v1.0.socialaccountsbean.apimodelproperty.socialchannel.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String socialChannel;

    @Schema(description = "@v1.0.socialaccountsbean.apimodelproperty.socialaccount.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String socialAccount;

	public String getSocialChannel() {
		return socialChannel;
	}

	public void setSocialChannel(String socialChannel) {
		this.socialChannel = socialChannel;
	}

	public String getSocialAccount() {
		return socialAccount;
	}

	public void setSocialAccount(String socialAccount) {
		this.socialAccount = socialAccount;
	}

	@Override
	public String toString() {
		return "SocialAccountsBean [socialChannel=" + socialChannel
				+ ", socialAccount=" + socialAccount + "]";
	}
}
