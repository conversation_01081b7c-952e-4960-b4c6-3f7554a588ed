package com.kronos.persons.rest.assignments.model.percentageallocationrule;

import com.kronos.persons.rest.assignments.model.RuleCriteriaBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "percentageAllocationRuleCriteria", description = "@v1.0.percentageallocationrulecriteriabean.apimodel.description")
public class PercentageAllocationRuleCriteriaBean implements RuleCriteriaBean
{
    @Schema(description = "@v1.0.percentageallocationrulecriteriabean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    String effectiveDate;

    @Schema(description = "@v1.0.percentageallocationrulecriteriabean.apimodelproperty.personidentity.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    PersonIdentityBean personIdentity;

    @Schema(description = "@v1.0.percentageallocationrulecriteriabean.apimodelproperty.processor.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    String processor;

    public String getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public PersonIdentityBean getPersonIdentity() {
        return personIdentity;
    }

    public void setPersonIdentity(PersonIdentityBean personIdentity) {
        this.personIdentity = personIdentity;
    }

    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }
}
