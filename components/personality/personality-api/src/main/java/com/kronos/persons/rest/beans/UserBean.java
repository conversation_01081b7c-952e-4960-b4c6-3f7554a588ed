package com.kronos.persons.rest.beans;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.userbean.apimodel.description", name = "user")
public class UserBean {
    @Schema(description = "@v1.0.userbean.apimodelproperty.passwordhistories.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<PasswordHistoryBean> passwordHistories;

    @Schema(description = "@v1.0.userbean.apimodelproperty.useraccount.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private UserAccountBean userAccount;
    
	@JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "@v1.0.userbean.apimodelproperty.usercurrency.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private UserCurrencyAssignmentBean userCurrency;

	/**
	 * @return the passwordHistories
	 */
	public List<PasswordHistoryBean> getPasswordHistories() {
		return passwordHistories;
	}

	/**
	 * @param passwordHistories
	 *            the passwordHistories to set
	 */
	public void setPasswordHistories(List<PasswordHistoryBean> passwordHistories) {
		this.passwordHistories = passwordHistories;
	}

	/**
	 * @return the userAccount
	 */
	public UserAccountBean getUserAccount() {
		return userAccount;
	}

	/**
	 * @param userAccount
	 *            the userAccount to set
	 */
	public void setUserAccount(UserAccountBean userAccount) {
		this.userAccount = userAccount;
	}
	
	/**
	 * @return the userCurrency
	 */
	public UserCurrencyAssignmentBean getUserCurrency() {
		return userCurrency;
	}

	/**
	 * @param userCurrency
	 *            the userCurrency to set
	 */
	public void setUserCurrency(UserCurrencyAssignmentBean userCurrency) {
		this.userCurrency = userCurrency;
	}
}
