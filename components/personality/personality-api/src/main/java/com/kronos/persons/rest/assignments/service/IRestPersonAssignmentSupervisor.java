package com.kronos.persons.rest.assignments.service;

import com.kronos.persons.rest.assignments.model.SupervisorAssignmentRequestBean;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import java.util.List;

@OpenAPIDefinition(tags = {
		@Tag(description = "@v1.0.commons-persons-supervisor-name.description", extensions = @Extension(properties = {
				@ExtensionProperty(name = "parent", value = "@root.personassignments.default") }), name = "@v1.0.commons-persons-supervisor-name.name") })
@Tag(name = "@v1.0.commons-persons-supervisor-name.name")
@Publishable(type = RestAPITypes.PUBLIC, value = true, productType = ProductTypes.WFP)
@Path("/v1/commons/persons/supervisor")
public interface IRestPersonAssignmentSupervisor {
	
	@GET
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "@v1.0.commons-persons-supervisor-name.get.{personid}.value", description = "@v1.0.commons-persons-supervisor-name.get.{personid}.notes", tags = "@v1.0.commons-persons-supervisor-name.get.{personid}.nickname")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-supervisor-name.get.{personid}.response.200.message", content = @Content(schema = @Schema(implementation = SupervisorAssignmentRequestBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-supervisor-name.get.{personid}.response.400.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-supervisor-name.get.{personid}.response.500.message")})
	@Path("/{personId:[0-9]+}")
	public SupervisorAssignmentRequestBean retrieve(
			@Parameter(description = "@v1.0.commons-persons_supervisor-name.get.{personid}.pathparam.personid.value", required = false) @PathParam(value = "personId" ) Long personId
	);


	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "@v1.0.commons-persons-supervisor-name.post.multi_read.name", description = "@v1.0.commons-persons-supervisor-name.post.multi_read.notes", tags = "@v1.0.commons-persons-supervisor-name.post.multi_read.nickname")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-supervisor-name.post.multi_read.response.200.message",content = @Content(schema = @Schema(type="list", implementation = SupervisorAssignmentRequestBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-supervisor-name.post.multi_read.response.400.message"),
			@ApiResponse(responseCode = "207", description = "@v1.0.commons-persons-supervisor-name.post.multi_read.response.207.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-supervisor-name.post.multi_read.response.500.message")})
	@Path("/multi_read")
	public List<SupervisorAssignmentRequestBean> retrieveList(ExtensionSearchCriteria extensionCriteria);


	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@ResponseStatus(code = HttpStatus.OK)
	@Operation(summary = "@v1.0.commons-persons-supervisor-name.post.multi_upsert.summary", description = "@v1.0.commons-persons-supervisor-name.post.multi_upsert.notes", tags = "@v1.0.commons-persons-supervisor-name.post.multi_upsert.nickname")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-supervisor-name.post.multi_upsert.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = SupervisorAssignmentRequestBean.class))),
			@ApiResponse(description = "@v1.0.commons-persons-supervisor-name.post.multi_upsert.response.207.message", responseCode = "207"),
			@ApiResponse(description = "@v1.0.commons-persons-supervisor-name.post.multi_upsert.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-supervisor-name.post.multi_upsert.response.500.message", responseCode = "500") })
	@Path("/multi_upsert")
	public List<SupervisorAssignmentRequestBean> multiUpsert(List<SupervisorAssignmentRequestBean> requestBean);

}
