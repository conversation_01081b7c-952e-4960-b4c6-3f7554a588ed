package com.kronos.persons.rest.beans;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "peopleAssignment", description = "@v1.0.peopleAssignmentBean.apimodel.description")
public class PeopleAssignmentBean implements Serializable{
	
	private static final long serialVersionUID = -8784901854063346819L;

	@Schema(description = "@v1.0.peopleAssignmentBean.apimodelproperty.unAssignExisting.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	protected Boolean unAssignExisting;

	@Schema(description = "@v1.0.peopleAssignmentBean.apimodelproperty.personidentity.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private PersonIdentityBean personIdentity;
	
	/**
	 * @return the personIdentity
	 */
	public PersonIdentityBean getPersonIdentity() {
		return personIdentity;
	}

	/**
	 * @param personIdentity
	 *            the personIdentity to set
	 */
	public void setPersonIdentity(PersonIdentityBean personIdentity) {
		this.personIdentity = personIdentity;
	}
	
	public Boolean getUnAssignExisting() {
		return unAssignExisting;
	}

	public void setUnAssignExisting(Boolean unAssignExisting) {
		this.unAssignExisting = unAssignExisting;
	}

}
