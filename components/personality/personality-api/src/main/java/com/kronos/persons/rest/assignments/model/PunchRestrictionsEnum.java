package com.kronos.persons.rest.assignments.model;

/**
 * <AUTHOR>
 * 
 *         This Enum was Introduced on behalf of the PunchInterpretationRule API to resemble the UI Options of punch Restrictions in punch
 *         Interpretation rules page.As of now "Full is false & Simple is true" "Reference DIM-449385".
 * 
 */
public enum PunchRestrictionsEnum {

   SIMPLE("com.kronos.punchinterpretation.punchrestrictions.simple"), FULL("com.kronos.punchinterpretation.punchrestrictions.full");
   private String punchRestriction;

   PunchRestrictionsEnum(String punchRestriction) {
      this.punchRestriction = punchRestriction;
   }

   public String getpunchRestriction() {
      return punchRestriction;
   }

}
