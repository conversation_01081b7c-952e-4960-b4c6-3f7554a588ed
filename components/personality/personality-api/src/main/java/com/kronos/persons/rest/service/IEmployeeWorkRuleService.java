package com.kronos.persons.rest.service;

import com.kronos.persons.rest.model.WorkRuleDTO;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;

import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import java.util.List;

/**
 * Interface intended for Employee Work Rule Service.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * <p>
 * Date: April 7, 2020
 *
 * <AUTHOR>
 */
@Path(value = "/v1/commons/persons/wage_work_rules/work_rules")
@Publishable(productType = ProductTypes.WFP, type = RestAPITypes.PRIVATE, value = true)
@Consumes(value = MediaType.APPLICATION_JSON)
@Produces(value = MediaType.APPLICATION_JSON)
public interface IEmployeeWorkRuleService {

    /**
     * This method retrieves all PayCode objects in the database and filters access to the
     * objects based on the current logged on user's active GDAP Set.
     *
     * @param sortOrderAscending sort direction
     * @return list of all work rules, filtered by the GDAP profile assigned to the logged user
     */
    @GET
    @Path("/")
    List<WorkRuleDTO> getWorkRulesFilteredByGDAP(@DefaultValue("false") @QueryParam("sortOrderAscending") boolean sortOrderAscending);
}
