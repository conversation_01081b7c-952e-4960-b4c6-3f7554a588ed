package com.kronos.persons.rest.model.wageoverride;

import java.math.BigDecimal;

/**
 * This class is used as an intermediate object for storing data
 * from WageWorkRuleOverride and JobTransferEntry while working
 * EmployeeWageWorkRuleConverter.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * Date: Apr 04, 2020
 *
 * <AUTHOR>
 */
public class WageWorkRuleDetails {
    private Long positionId;
    private Long jobId;
    private String jobPath;
    private BigDecimal wage;
    private Long workRuleId;
    private String workRuleName;
    private Integer version;

    /**
     * Gets position id.
     *
     * @return position id
     */
    public Long getPositionId() {
        return positionId;
    }

    /**
     * Gets job id.
     *
     * @return job id
     */
    public Long getJobId() {
        return jobId;
    }

    /**
     * Get job path.
     *
     * @return job path
     */
    public String getJobPath() {
        return jobPath;
    }

    /**
     * Gets wage.
     *
     * @return wage
     */
    public BigDecimal getWage() {
        return wage;
    }

    /**
     * Gets workRule id.
     *
     * @return workRule id
     */
    public Long getWorkRuleId() {
        return workRuleId;
    }

    /**
     * Gets name of workRule.
     *
     * @return name of workRule
     */
    public String getWorkRuleName() {
        return workRuleName;
    }

    /**
     * Gets version.
     *
     * @return version
     */
    public Integer getVersion() {
        return version;
    }

    /**
     * Gets builder.
     *
     * @return the builder for WageWorkRuleDetails.
     */
    public static Builder builder() {
        return new WageWorkRuleDetails().new Builder();
    }

    /**
     * Builder for WageWorkRuleDetails.
     */
    public class Builder {

        /**
         * Private constructor.
         */
        private Builder() {

        }

        /**
         * Sets position id.
         *
         * @param positionId position id
         * @return the builder.
         */
        public Builder positionId(Long positionId) {
            WageWorkRuleDetails.this.positionId = positionId;
            return this;
        }

        /**
         * Sets job id.
         *
         * @param jobId job id
         * @return the builder.
         */
        public Builder jobId(Long jobId) {
            WageWorkRuleDetails.this.jobId = jobId;
            return this;
        }

        /**
         * Sets job path.
         *
         * @param jobPath job path
         * @return the builder.
         */
        public Builder jobPath(String jobPath) {
            WageWorkRuleDetails.this.jobPath = jobPath;
            return this;
        }

        /**
         * Sets wage.
         *
         * @param wage wage
         * @return the builder.
         */
        public Builder wage(BigDecimal wage) {
            WageWorkRuleDetails.this.wage = wage;
            return this;
        }

        /**
         * Sets workRule id.
         *
         * @param workRuleId workRule id
         * @return the builder.
         */
        public Builder workRuleId(Long workRuleId) {
            WageWorkRuleDetails.this.workRuleId = workRuleId;
            return this;
        }

        /**
         * Sets name of workRule.
         *
         * @param workRuleName name of workRule
         * @return the builder.
         */
        public Builder workRuleName(String workRuleName) {
            WageWorkRuleDetails.this.workRuleName = workRuleName;
            return this;
        }

        /**
         * Sets version.
         *
         * @param version version
         * @return the builder.
         */
        public Builder version(Integer version) {
            WageWorkRuleDetails.this.version = version;
            return this;
        }

        /**
         * Gets WageWorkRuleDetails.
         *
         * @return WageWorkRuleDetails
         */
        public WageWorkRuleDetails build() {
            return WageWorkRuleDetails.this;
        }
    }

}
