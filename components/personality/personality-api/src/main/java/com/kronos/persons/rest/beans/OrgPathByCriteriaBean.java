package com.kronos.persons.rest.beans;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.orgpathbycriteriabean.apimodel.description", name = "orgPathByCriteria")
public class OrgPathByCriteriaBean {

	@Schema(description = "@v1.0.orgpathbycriteriabean.apimodelproperty.orgnodes.description", requiredMode = Schema.RequiredMode.REQUIRED)
	List<OrgNodesBean> orgNodes;

	@Schema(description = "@v1.0.orgpathbycriteriabean.apimodelproperty.matchfullpath.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	Boolean matchFullPath = Boolean.FALSE;

	/**
	 * @return the orgNodes
	 */
	public List<OrgNodesBean> getOrgNodes() {
		return orgNodes;
	}

	/**
	 * @param orgNodes the orgNodes to set
	 */
	public void setOrgNodes(List<OrgNodesBean> orgNodes) {
		this.orgNodes = orgNodes;
	}

	/**
	 * @return the matchFullPath
	 */
	public Boolean getMatchFullPath() {
		return matchFullPath;
	}

	/**
	 * @param matchFullPath the matchFullPath to set
	 */
	public void setMatchFullPath(Boolean matchFullPath) {
		this.matchFullPath = matchFullPath;
	}

}
