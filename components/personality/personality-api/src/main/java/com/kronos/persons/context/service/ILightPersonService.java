/***********************************************************************
 * ILightPersonService.java
 *
 * Copyright 2022, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.persons.context.service;

import com.kronos.persons.rest.beans.LightPersonInfoBean;
import com.kronos.persons.rest.model.LightPersonInformationSearchCriteria;

/**
 * <AUTHOR>
 *
 */
public interface ILightPersonService {

	/**
	 * Find light weight persons by search criteria
	 * @param criteria
	 * @return
	 */
	public LightPersonInfoBean findLightPersonRecords(LightPersonInformationSearchCriteria criteria);

}
