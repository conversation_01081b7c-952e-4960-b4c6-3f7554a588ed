package com.kronos.persons.rest.assignments.model;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "PersonAttestationProfileAssignmentRequest", description = "@v1.0.personattestationprofileassignmentrequest.apimodel.description")
public class PersonAttestationProfileAssignmentRequest {

   @Schema(description = "@v1.0.personattestationprofileassignmentrequest.apimodelproperty.where.description")
   private EmployeeRefs where;

   @Schema(description = "@v1.0.personattestationprofileassignmentrequest.apimodelproperty.select.description",
           allowableValues = "@v1.0.personattestationprofileassignmentrequest.apimodelproperty.select.allowablevalues")
   private AttestationProfileAssignRoleSelect select;

   public PersonAttestationProfileAssignmentRequest() {
   }

   public EmployeeRefs getWhere() {
      return where;
   }

   public void setWhere(EmployeeRefs where) {
      this.where = where;
   }

   public AttestationProfileAssignRoleSelect getSelect() {
      return select;
   }

   public void setSelect(AttestationProfileAssignRoleSelect select) {
      this.select = select;
   }
}