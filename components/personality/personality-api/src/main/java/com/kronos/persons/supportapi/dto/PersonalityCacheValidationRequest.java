/*
 * *****************************************************************************
 * Copyright (c) 2020 Kronos, Inc. All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * Kronos, Inc. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with Kronos.
 *
 * KRONOS MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE
 * SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
 * PURPOSE, OR NON-INFRINGEMENT. KRONOS SHALL NOT BE LIABLE FOR ANY DAMAGES
 * SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING
 * THIS SOFTWARE OR ITS DERIVATIVES.
 ****************************************************************************
 *
 **/
package com.kronos.persons.supportapi.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * This DTO is used for containing request details for Caching Support APIs
 * 
 * <AUTHOR>
 */
public class PersonalityCacheValidationRequest {

	private List<PersonalityRequestData> personalityRequestDataList = new ArrayList<>();

	public List<PersonalityRequestData> getPersonalityRequestDataList() {
		return personalityRequestDataList;
	}

	public void setPersonalityRequestDataList(List<PersonalityRequestData> personalityRequestDataList) {
		this.personalityRequestDataList = personalityRequestDataList;
	}

	@Override
	public String toString() {
		return "PersonalityCacheValidationRequest [personalityRequestDataList=" + personalityRequestDataList + "]";
	}

	
}
