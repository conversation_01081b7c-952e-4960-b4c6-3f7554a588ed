/***********************************************************************
 * IRoleDelegateContext Copyright 2018, Kronos Incorporated. All rights
 * reserved.
 **********************************************************************/
package com.kronos.persons.context.switchrole;

import com.kronos.wfc.commonapp.people.business.person.delegateauthority.MultiManagerRoleAssignmentDTO;

import java.util.List;

/**
 * Interface to describe role of a user
 * 
 * <AUTHOR>
public interface IRoleDelegateContext {
	/**
	 * get the list of delegation
	 * "switchRoleDelegates": [
        {
            "id": "-1337",
            "delegatorId": "-1227",
            "delegator": "Myself",
            "role": ""
        },
	 * @return {@link}
	 */
   public java.util.List<IDelegationsList> getSwitchRoleDelegates();
   
/**
 * get the name of Current Delegation Name
 * @return {@String}
 */
   public String getCurrentDelegationName();
   
   /**
    * get the name of current DelegationRole
    * @return {@String}
    */
   public String getCurrentDelegationRole();

   /**
    * get the name of currentTaskId
    * @return {@String}
    */
   public String getCurrentTaskId();
   
   /**
    * get boolean flag of is myself or not
    * @return {@Boolean}
    */
   public boolean getIsMySelf();

   IDelegationsList getActiveDelegateTask();

   public MultiManagerRoleAssignmentDTO getActiveRole();

   public List<MultiManagerRoleAssignmentDTO> getMyRoles();

}
