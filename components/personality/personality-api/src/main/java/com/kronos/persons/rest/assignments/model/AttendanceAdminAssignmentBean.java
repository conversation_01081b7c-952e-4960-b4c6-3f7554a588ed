package com.kronos.persons.rest.assignments.model;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.persons.rest.beans.PeopleAssignmentBean;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "attendanceAdminAssignment", description = "@v1.0.attendanceadminassignmentbean.apimodel.value")
@JsonPropertyOrder(alphabetic = true)
public class AttendanceAdminAssignmentBean extends PeopleAssignmentBean implements BaseAssignmentBean{

	@Schema(description = "@v1.0.attendanceadminassignmentbean.apimodelproperty.administrator.description")
	AdminProfileBean administrator;

	/**
	 * @return the administrator
	 */
	public AdminProfileBean getAdministrator() {
		return administrator;
	}

	/**
	 * @param administrator
	 *            the administrator to set
	 */
	public void setAdministrator(AdminProfileBean administrator) {
		this.administrator = administrator;
	}
}
