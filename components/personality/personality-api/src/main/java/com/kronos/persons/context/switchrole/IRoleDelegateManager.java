/***********************************************************************
 * IRoleDelegateContext Copyright 2018, Kronos Incorporated. All rights
 * reserved.
 **********************************************************************/
package com.kronos.persons.context.switchrole;

import com.kronos.persons.rest.model.DelegationRoleAssignmentDTO;
import java.util.List;

/**
 * Interface to getDelegateContext
 * <AUTHOR>
 *
 */
public interface IRoleDelegateManager {
   
	/**
	 * it gets RoleDelegateContext 
	 * @return
	 */
   public IRoleDelegateContext getRoleDelegateContext();
   
   public List<DelegationRoleAssignmentDTO> getDelegationRoles();
   
}
