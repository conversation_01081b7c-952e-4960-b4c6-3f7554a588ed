package com.kronos.persons.rest.model;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.photosearchpotion.apimodel.description", title = "photoSearchOption")
public class PhotoSearchOption implements Serializable{

	private static final long serialVersionUID = 1L;

	@Schema(description = "@v1.0.photosearchpotion.apimodelproperty.modifiedSinceDateTime.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
	private LocalDateTime modifiedSinceDateTime;

	public LocalDateTime getModifiedSinceDateTime() {
		return modifiedSinceDateTime;
	}

	public void setModifiedSinceDateTime(LocalDateTime modifiedSinceDateTime) {
		this.modifiedSinceDateTime = modifiedSinceDateTime;
	}

	@Override
	public String toString() {
		return "PhotoSearchOption [modifiedSinceDateTime=" + modifiedSinceDateTime + "]";
	}

}
