/***********************************************************************
 * Employee.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.persons.rest.model;

import java.util.Date;

import jakarta.inject.Named;

import com.kronos.commonbusiness.converter.model.ObjectId;
import com.kronos.commonbusiness.converter.utils.ObjectUtils;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.persons.rest.service.PersonRestServiceConstants;

/**
 * Represents an employee. Defines a light set of employee attributes,
 * sufficient for schedule display and assignment needs.
 * 
 * <AUTHOR>
 * <AUTHOR>
 */
@Named
public class Employee implements Cloneable {

	/** Internal employee Id, null when not yet persisted */
	private ObjectId id = null;

	/** User-defined person id */
	private String personNum;

	/** Full name */
	private String fullName;

	/** Short name */
	private String shortName;

	/** If the employee has a WTK license */
	private boolean isWTKLicensed;

	/** If the employee has a WFS license */
	private boolean isWFSLicensed;

	/** If the employee has an employee license */
	private boolean isEmployeeLicensed;

	/** If the employee has a manager license */
	private boolean isManagerLicensed;

	/** String of phone number defined as 'Phone 1' in people editor */
	private String phoneNumberOne;

	/** Employee's currency symbol. */
	private String currencySymbol;

	/**
	 * The snapshotDateUTC.
	 */
	private Date snapshotDateUTC;

	/**
	 * The employee photoId.
	 */
	private String photoId;

	/**
	 * The Logger
	 */
	private static final KLogger LOGGER = KLoggerFactory.getKLogger(Employee.class);

	/**
	 * @return Returns the employee id
	 */
	public ObjectId getId() {
		return id;
	}

	/**
	 * @param The
	 *            employeeId to set.
	 */
	public void setId(ObjectId id) {
		this.id = id;
	}

	/**
	 * @return The employee person number
	 */
	public String getPersonNum() {
		return personNum;
	}

	/**
	 * @param personNum
	 *            The personNum to set.
	 */
	public void setPersonNum(String personNum) {
		this.personNum = personNum;
	}

	/**
	 * @return The employee's short name.
	 */
	public String getShortName() {
		return shortName;
	}

	/**
	 * @param shortName
	 *            The short name to set.
	 */
	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	/**
	 * @return The employee's full name.
	 */
	public String getFullName() {
		return fullName;
	}

	/**
	 * @param fullName
	 *            The full name to set.
	 */
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	/**
	 * @return true if the employee has a WTK license, false otherwise.
	 */
	public boolean getIsWTKLicensed() {
		return isWTKLicensed;
	}

	/**
	 * @param isWTKLicensed
	 *            Set this to true if the employee has a WTK license.
	 */
	public void setIsWTKLicensed(boolean isWTKLicensed) {
		this.isWTKLicensed = isWTKLicensed;
	}

	/**
	 * @return true if the employee has a WFS license, false otherwise.
	 */
	public boolean getIsWFSLicensed() {
		return isWFSLicensed;
	}

	/**
	 * @param isWFSLicensed
	 *            Set this to true if the employee has a WFS license.
	 */
	public void setIsWFSLicensed(boolean isWFSLicensed) {
		this.isWFSLicensed = isWFSLicensed;
	}

	/**
	 * @return true if the employee has a Workforce Employee license, false
	 *         otherwise.
	 */
	public boolean getIsEmployeeLicensed() {
		return isEmployeeLicensed;
	}

	/**
	 * @param isEmployeeLicensed
	 *            Set this to true if the employee has a Workforce Employee
	 *            license.
	 */
	public void setIsEmployeeLicensed(boolean isEmployeeLicensed) {
		this.isEmployeeLicensed = isEmployeeLicensed;
	}

	/**
	 * @return true if the employee has a Workforce Manager license, false
	 *         otherwise.
	 */
	public boolean getIsManagerLicensed() {
		return isManagerLicensed;
	}

	/**
	 * @param isManagerLicensed
	 *            Set this to true if the employee has a Workforce Manager
	 *            license.
	 */
	public void setIsManagerLicensed(boolean isManagerLicensed) {
		this.isManagerLicensed = isManagerLicensed;
	}

	/**
	 * @return The phone number.
	 */
	public String getPhoneNumberOne() {
		return phoneNumberOne;
	}

	/**
	 * @param phoneNumberOne
	 *            The phone number to set.
	 */
	public void setPhoneNumberOne(String phoneNumberOne) {
		this.phoneNumberOne = phoneNumberOne;
	}

	/**
	 * @return currency symbol
	 */
	public String getCurrencySymbol() {
		return currencySymbol;
	}

	/**
	 * @param currencySymbol String containing the currency symbol
	 */
	public void setCurrencySymbol(String currencySymbol) {
		this.currencySymbol = currencySymbol;
	}

	/**
	 * @return snapshotDateUTC
	 */
	public Date getSnapshotDateUTC() {
		return snapshotDateUTC;
	}

	/**
	 * @param snaphotDateUTC Date instance containing the snaphotDateUTC
	 */
	public void setSnapshotDateUTC(Date snaphotDateUTC) {
		this.snapshotDateUTC = snaphotDateUTC;
	}

	/**
	 * @return The hash code.
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + getFullNameHashCode();
		result = prime * result + getIdHashCode();
		result = prime * result + getIsEmployeeLicensedHashCode();
		result = prime * result + getIsMAnagerLicensedHashCode();
		result = prime * result + getIsWFSLicensedHashCode();
		result = prime * result + getIsWTKLicensedHashCode();
		result = prime * result + getPersonNumHashCode();
		result = prime * result + getPhoneNumberOneHashCode();
		result = prime * result + getCurrencySymbolHashCode();
		result = prime * result + getShortNameHashCode();
		result = prime * result + getSnapshotDateHashCode();
		result = prime * result + getPhotoIdHashCode();
		return result;
	}

	protected int getPhotoIdHashCode() {
		return (photoId == null) ? 0 : photoId.hashCode();
	}

	protected int getSnapshotDateHashCode() {
		return (snapshotDateUTC == null) ? 0 : snapshotDateUTC.hashCode();
	}

	protected int getShortNameHashCode() {
		return (shortName == null) ? 0 : shortName.hashCode();
	}

	protected int getCurrencySymbolHashCode() {
		return (currencySymbol == null) ? 0 : currencySymbol.hashCode();
	}

	protected int getPhoneNumberOneHashCode() {
		return (phoneNumberOne == null) ? 0 : phoneNumberOne.hashCode();
	}

	protected int getPersonNumHashCode() {
		return (personNum == null) ? 0 : personNum.hashCode();
	}

	protected int getIsWTKLicensedHashCode() {
		return isWTKLicensed ? 1231 : 1237;
	}

	protected int getIsWFSLicensedHashCode() {
		return isWFSLicensed ? 1231 : 1237;
	}

	protected int getIsMAnagerLicensedHashCode() {
		return isManagerLicensed ? 1231 : 1237;
	}

	protected int getIsEmployeeLicensedHashCode() {
		return isEmployeeLicensed ? 1231 : 1237;
	}

	protected int getIdHashCode() {
		return (id == null) ? 0 : id.hashCode();
	}

	protected int getFullNameHashCode() {
		return (fullName == null) ? 0 : fullName.hashCode();
	}

	/**
	 * @param obj
	 *            The object to compare with
	 * @return true if the object is equal to the one passed, false otherwise.
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Employee other = (Employee) obj;
		return equalityCheck(other) && equalityIsWFSLicensedisWTKLicensedPrimaryJob(other)
				&& equalitysnapshotDatePhotoid(other);
	}

	boolean equalityCheck(Employee other) {
		return equalityFullNameIdPersonnum(other) && equalityPhonenumCurrencyPrimaryOrg(other)
				&& equalityShortnameEmployeelicensedManagerlicenced(other);
	}

	boolean equalityFullNameIdPersonnum(Employee other) {
		return ObjectUtils.equalsOfObjects(fullName, other.fullName) && ObjectUtils.equalsOfObjects(id, other.id)
				&& ObjectUtils.equalsOfObjects(personNum, other.personNum);
	}

	boolean equalityPhonenumCurrencyPrimaryOrg(Employee other) {
		return ObjectUtils.equalsOfObjects(phoneNumberOne, other.phoneNumberOne)
				&& ObjectUtils.equalsOfObjects(currencySymbol, other.currencySymbol);
	}

	boolean equalityShortnameEmployeelicensedManagerlicenced(Employee other) {
		return ObjectUtils.equalsOfObjects(shortName, other.shortName) && (isEmployeeLicensed == other.isEmployeeLicensed)
				&& (isManagerLicensed == other.isManagerLicensed);
	}

	boolean equalityIsWFSLicensedisWTKLicensedPrimaryJob(Employee other) {
		return (isWFSLicensed == other.isWFSLicensed) && (isWTKLicensed == other.isWTKLicensed);
	}

	boolean equalitysnapshotDatePhotoid(Employee other) {
		return ObjectUtils.equalsOfObjects(snapshotDateUTC, other.snapshotDateUTC) && ObjectUtils.equalsOfObjects(photoId, other.photoId);
	}

	/**
	 * @return A cloned object.
	 * @see java.lang.Object#clone()
	 */
	@Override
	public Employee clone() {
		Employee emp = null;
		try {
			emp = (Employee) super.clone();
			emp.setId(id.clone());
			emp.setFullName(fullName);
			emp.setShortName(shortName);
			emp.setPersonNum(personNum);
			emp.setPhoneNumberOne(phoneNumberOne);
			emp.setCurrencySymbol(currencySymbol);
			emp.setSnapshotDateUTC(snapshotDateUTC);
			if (photoId != null) {
				emp.setPhotoId(photoId);
			}
		} catch (CloneNotSupportedException cex) {
			LOGGER.debug(PersonRestServiceConstants.CLONENOTSUPPORTEDEXCEPTION_OCCURRED_WHILE_CLONING_EMPLOYEE_MESSAGE, cex);
		}
		return emp;
	}

	/**
	 * @return A string representation of the object.
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("Employee [employeeId=").append(id);
		builder.append(", personNum=").append(personNum);
		builder.append(", fullName=").append(fullName);
		builder.append(", shortName=").append(shortName);
		builder.append(", isWFSLicensed=").append(isWFSLicensed);
		builder.append(", isWTKLicensed=").append(isWTKLicensed);
		builder.append(", isEmployeeLicensed=").append(isEmployeeLicensed);
		builder.append(", isManagerLicensed=").append(isManagerLicensed);
		builder.append(", phoneNumberOne=").append(phoneNumberOne);
		builder.append(", currencySymbol=").append(currencySymbol);
		builder.append(", snapshotDateUTC=").append(snapshotDateUTC);
		builder.append(", photoId=").append(photoId);
		builder.append("]");
		return builder.toString();
	}

	/**
	 * @return photoId
	 */
	public String getPhotoId() {
		return photoId;
	}

	/**
	 * @param photoId
	 *            sets String photoId
	 */
	public void setPhotoId(String photoId) {
		this.photoId = photoId;
	}
}
