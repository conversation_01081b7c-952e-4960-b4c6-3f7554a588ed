package com.kronos.persons.rest.model;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "@v1.0.delegationinfobean.apimodel.description", name = "delegationInfoModel")
public class DelegationInfoBean {
  
    @Schema(description = "@v1.0.delegationinfobean.apimodelproperty.delegationroles.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DelegationRoleAssignmentDTO> delegationRoles;
    
    public List<DelegationRoleAssignmentDTO> getDelegationRoles() {
        return this.delegationRoles;
    }

    public void setSwitchRoleDelegates(List<DelegationRoleAssignmentDTO> delegationRoles) {
        this.delegationRoles = delegationRoles;
    }
}