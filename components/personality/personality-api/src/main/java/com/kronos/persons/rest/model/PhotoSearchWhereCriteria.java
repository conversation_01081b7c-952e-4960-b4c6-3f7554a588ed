package com.kronos.persons.rest.model;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.photosearchwherecriteria.apimodel.description", title = "photoSearchWhereCriteria")
public class PhotoSearchWhereCriteria implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @Schema(description = "@v1.0.photosearchwherecriteria.apimodelproperty.employees.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private EmployeeCriteria employees;

	public EmployeeCriteria getEmployees() {
		return employees;
	}

	public void setEmployees(EmployeeCriteria employees) {
		this.employees = employees;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("{");
		builder.append("employees:" + getEmployees());
		builder.append("}");
		return builder.toString();
	}
    
}
