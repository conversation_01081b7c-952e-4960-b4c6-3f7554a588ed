package com.kronos.persons.accountmanagement.service;

public final class PersonAccountManagementConstants {
  public static final String SUITE_CUSTOMER = "ukgpro";

  public static final String AUTH_SYNC_ENABLED_VALUE = "global.personality.auth0.sync.enabled";
  public static final String USER_CREATED_STATUS_FLAG = "ignore.auth0.user.already.created.status";
  public static final String DUPLICATE_USERNAME = "DUPLICATE_USERNAME";
  public static final String USERID = "userId";
  public static final String PEOPLE_AUTH_AMSBULKAPI = "com.kronos.people.auth.amsbulkapi.enabled";
  public static final String PEOPLE_SSO_USERNAME = "global.people.personality.ssousername.feature";
  
  private PersonAccountManagementConstants() {
  }

}
