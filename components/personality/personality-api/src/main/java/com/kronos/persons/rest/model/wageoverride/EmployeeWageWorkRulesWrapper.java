package com.kronos.persons.rest.model.wageoverride;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.model.EmployeeWageWorkRulesDTO;

/**
 * The wrapper for {@link EmployeeWageWorkRulesDTO}.
 * Contains the object {@link EmployeeWageWorkRulesDTO} and {@link APIException} of it was throw in time of validation.
 * Copyright (C) 2020 Kronos.com
 * Date: May 25, 2020
 *
 * <AUTHOR>
 */
public class EmployeeWageWorkRulesWrapper {

    private EmployeeWageWorkRulesDTO wrapperObject;

    private APIException apiException;

    private boolean isMultiKey;

    private Object errorInput;

    /**
     * Default constructor.
     */
    public EmployeeWageWorkRulesWrapper() {
    }

    /**
     * Constructor.
     *
     * @param employeeWageWorkRulesDTO instance of {@link EmployeeWageWorkRulesDTO}
     */
    public EmployeeWageWorkRulesWrapper(EmployeeWageWorkRulesDTO employeeWageWorkRulesDTO) {
        this.wrapperObject = employeeWageWorkRulesDTO;
    }

    /**
     * @return the {@link APIException}
     */
    public APIException getApiException() {
        return apiException;
    }

    /**
     * Set apiException.
     *
     * @param apiException the {@link APIException}
     */
    public void setApiException(APIException apiException) {
        this.apiException = apiException;
    }


    /**
     * @return {@link EmployeeWageWorkRulesDTO}
     */
    public EmployeeWageWorkRulesDTO getDTO() {
        return wrapperObject;
    }

    /**
     * Set dto.
     *
     * @param dto the {@link EmployeeWageWorkRulesDTO}
     */
    public void setDTO(EmployeeWageWorkRulesDTO dto) {
        this.wrapperObject = dto;
    }

    public boolean getMultiKey() {
        return isMultiKey;
    }

    public void setMultiKey(boolean multiKey) {
        isMultiKey = multiKey;
    }

    public Object getErrorInput() {
        return errorInput;
    }

    public void setErrorInput(Object errorInput) {
        this.errorInput = errorInput;
    }
}
