package com.kronos.persons.rest.beans.extensions;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;


@Schema(description = "@v1.0.jobassignmentswitchdataextension.apimodel.description", name = "jobAssignmentSwitch")
public class JobAssignmentSwitchDataExtension implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "@v1.0.jobassignmentswitchdataextension.apimodelproperty.assignpersonovertimeswitch.description")
    private Long assignPersonOvertimeSwitch;

    @Schema(description = "@v1.0.jobassignmentswitchdataextension.apimodelproperty.deletedswitch.description")
    private Long deletedSwitch;

    @Schema(description = "@v1.0.jobassignmentswitchdataextension.apimodelproperty.usemaswitch.description")
	private Long useMASwitch;

	/**
	 * @return the assignPersonOvertimeSwitch
	 */
	public Long getAssignPersonOvertimeSwitch() {
		return assignPersonOvertimeSwitch;
	}

	/**
	 * @param assignPersonOvertimeSwitch
	 *            the assignPersonOvertimeSwitch to set
	 */
	public void setAssignPersonOvertimeSwitch(Long assignPersonOvertimeSwitch) {
		this.assignPersonOvertimeSwitch = assignPersonOvertimeSwitch;
	}

	/**
	 * @return the deletedSwitch
	 */
	public Long getDeletedSwitch() {
		return deletedSwitch;
	}

	/**
	 * @param deletedSwitch
	 *            the deletedSwitch to set
	 */
	public void setDeletedSwitch(Long deletedSwitch) {
		this.deletedSwitch = deletedSwitch;
	}

	/**
	 * @return the useMASwitch
	 */
	public Long getUseMASwitch() {
		return useMASwitch;
	}

	/**
	 * @param useMASwitch
	 *            the useMASwitch to set
	 */
	public void setUseMASwitch(Long useMASwitch) {
		this.useMASwitch = useMASwitch;
	}

}
