package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.postaladdressbean.apimodel.description", name = "postalAddressDetails")
@JsonPropertyOrder(alphabetic = true)
public class PostalAddressBean {
    @Schema(description = "@v1.0.postaladdressbean.apimodelproperty.city.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    public String city;

    @Schema(description = "@v1.0.postaladdressbean.apimodelproperty.country.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    public String country;

    @Schema(description = "@v1.0.postaladdressbean.apimodelproperty.postalcode.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    public String postalCode;

    @Schema(description = "@v1.0.postaladdressbean.apimodelproperty.state.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    public String state;

    @Schema(description = "@v1.0.postaladdressbean.apimodelproperty.street.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    public String street;

    @Schema(description = "@v1.0.postaladdressbean.apimodelproperty.contacttypename.description", requiredMode = Schema.RequiredMode.REQUIRED)
	public String contactTypeName;

	/**
	 * The object ID of the postal address type.
	 */
    @JsonIgnore
	protected ObjectIdLong postalAddressTypeId = null;

	/**
	 * @return the city
	 */
	public String getCity() {
		return city;
	}

	/**
	 * @param city
	 *            the city to set
	 */
	public void setCity(String city) {
		this.city = city;
	}

	/**
	 * @return the country
	 */
	public String getCountry() {
		return country;
	}

	/**
	 * @param country
	 *            the country to set
	 */
	public void setCountry(String country) {
		this.country = country;
	}

	/**
	 * @return the postalCode
	 */
	public String getPostalCode() {
		return postalCode;
	}

	/**
	 * @param postalCode
	 *            the postalCode to set
	 */
	public void setPostalCode(String postalCode) {
		this.postalCode = postalCode;
	}

	/**
	 * @return the state
	 */
	public String getState() {
		return state;
	}

	/**
	 * @param state
	 *            the state to set
	 */
	public void setState(String state) {
		this.state = state;
	}

	/**
	 * @return the street
	 */
	public String getStreet() {
		return street;
	}

	/**
	 * @param street
	 *            the street to set
	 */
	public void setStreet(String street) {
		this.street = street;
	}

	/**
	 * @return the contactTypeName
	 */
	public String getContactTypeName() {
		return contactTypeName;
	}

	/**
	 * @param contactTypeName
	 *            the contactTypeName to set
	 */
	public void setContactTypeName(String contactTypeName) {
		this.contactTypeName = contactTypeName;
	}

	/**
	 * @return the postalAddressTypeId
	 */
	public ObjectIdLong getPostalAddressTypeId() {
		return postalAddressTypeId;
	}

	/**
	 * @param postalAddressTypeId
	 *            the postalAddressTypeId to set
	 */
	public void setPostalAddressTypeId(ObjectIdLong postalAddressTypeId) {
		this.postalAddressTypeId = postalAddressTypeId;
	}

}
