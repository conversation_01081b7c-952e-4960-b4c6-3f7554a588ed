package com.kronos.persons.rest.service;

import com.kronos.persons.rest.model.DateRangeListDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;

/**
 * Interface intended for obtaining available date ranges, for which person has some Wage and Work Rule overrides.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * <p>
 * Date: April 4, 2020
 *
 * <AUTHOR>
 */
@Path(value = "/v1/commons/persons/wage_work_rules")
@Consumes(value = MediaType.APPLICATION_JSON)
@Produces(value = MediaType.APPLICATION_JSON)
public interface IEmployeeWageWorkRuleDateRangeService {

    /**
     * Method used to retrieve available date ranges for a person.
     *
     * @param personId ID of a person for whom to retrieve data
     * @return list of date ranges for specified person
     */
    @GET
    @Path("/date_ranges/{personId}")
    @Operation(summary = "@v1.0.commons-persons-wage_work_rules.get.date_ranges.{personid}.value",
            tags = "@v1.0.commons-persons-wage_work_rules.get.date_ranges.{personid}.nickname",
            description = "@v1.0.commons-persons-wage_work_rules.get.date_ranges.{personid}.notes")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "@v1.0.commons-persons-wage_work_rules.get.date_ranges.{personid}.response.200.message",
                    content = @Content(schema = @Schema(implementation = DateRangeListDTO.class))),
            @ApiResponse(responseCode = "400",
                    description = "@v1.0.commons-persons-wage_work_rules.get.date_ranges.{personid}.response.400.message"),
            @ApiResponse(responseCode = "404",
                    description = "@v1.0.commons-persons-wage_work_rules.get.date_ranges.{personid}.response.404.message"),
            @ApiResponse(responseCode = "500",
                    description = "@v1.0.commons-persons-wage_work_rules.get.date_ranges.{personid}.response.500.message")
    })
    DateRangeListDTO getEmployeeDateRanges(
            @Parameter(description = "@v1.0.commons-persons-wage_work_rules.get.date_ranges.{personid}.pathparam.personid.value", required = true) @PathParam("personId") String personId,
            @Parameter(description = "@v1.0.commons-persons-wage_work_rules.get.date_ranges.positionid.value", required = false) @QueryParam("positionId") Long positionId);
}
