package com.kronos.persons.rest.assignments.service;

import java.util.List;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.kronos.persons.rest.assignments.model.PersonCommonIdRequest;
import com.kronos.persons.rest.beans.LightPersonInformation;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.rest.model.LightPersonInformationSearchCriteria;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;


/**
 * 
 * <AUTHOR>
 *
 */
@OpenAPIDefinition(tags = {
		@Tag(description = "@v1.0.commons-persons-external_id.description", extensions = @Extension(properties = {
				@ExtensionProperty(name = "parent", value = "@root.personassignments.default") }), name = "@v1.0.commons-persons-external_id.name") })
@Tag(name = "@v1.0.commons-persons-external_id.name")
@Publishable(type = RestAPITypes.PUBLIC, value = true, productType = ProductTypes.WFP)
@Path(value = "/v1/commons/persons/external_id")
public interface IRestPersonCommonId {
	

    @PUT
    @Consumes(value = MediaType.APPLICATION_JSON)
    @Produces(value = MediaType.APPLICATION_JSON)
	@Operation(tags = "@v1.0.commons-persons-external_id.put.currentpath.nickname", description = "@v1.0.commons-persons-external_id.put.currentpath.notes", summary = "@v1.0.commons-persons-external_id.put.currentpath.summary")
	@ApiResponses(value = {
		@ApiResponse(description = "@v1.0.commons-persons-external_id.put.currentpath.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = PersonCommonIdRequest.class))),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.put.currentpath.response.400.message", responseCode = "400"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.put.currentpath.response.500.message", responseCode = "500") })
    public PersonCommonIdRequest update(PersonCommonIdRequest requestData);
    
    @DELETE
    @Consumes(value = MediaType.APPLICATION_JSON)
    @Produces(value = MediaType.APPLICATION_JSON)
    @Path("/")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @Operation(tags = "@v1.0.commons-persons-external_id.delete.currentpath.nickname", description = "@v1.0.commons-persons-external_id.delete.currentpath.notes", summary = "@v1.0.commons-persons-external_id.delete.currentpath.summary")
	@ApiResponses(value = { 
		@ApiResponse(description = "@v1.0.commons-persons-external_id.delete.currentpath.response.204.message", responseCode = "204"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.delete.currentpath.response.400.message", responseCode = "400"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.delete.currentpath.response.500.message", responseCode = "500") })
    public void delete(PersonCommonIdRequest requestData);
	
    
    @POST
    @Consumes(value = MediaType.APPLICATION_JSON)
    @Produces(value = MediaType.APPLICATION_JSON)
    @Path(value ="/multi_update")
    @Operation(tags = "@v1.0.commons-persons-external_id.post.multi_update.nickname", description = "@v1.0.commons-persons-external_id.post.multi_update.notes", summary = "@v1.0.commons-persons-external_id.post.multi_update.summary")
	@ApiResponses(value = { 
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.multi_update.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = PersonCommonIdRequest.class))),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.multi_update.response.207.message", responseCode = "207"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.multi_update.response.400.message", responseCode = "400"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.multi_update.response.500.message", responseCode = "500") })
    public List<PersonCommonIdRequest> multiUpdate(List<PersonCommonIdRequest> requestDataList);

    @POST
    @Consumes(value = MediaType.APPLICATION_JSON)
    @Produces(value = MediaType.APPLICATION_JSON)
    @Path(value ="/multi_delete")
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @Operation(tags = "@v1.0.commons-persons-external_id.post.multi_delete.nickname", description = "@v1.0.commons-persons-external_id.post.multi_delete.notes", summary = "@v1.0.commons-persons-external_id.post.multi_delete.summary")
	@ApiResponses(value = { 
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.multi_delete.response.204.message", responseCode = "204"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.multi_delete.response.207.message", responseCode = "207"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.multi_delete.response.400.message", responseCode = "400"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.multi_delete.response.500.message", responseCode = "500") })
    public void multiDelete(List<PersonCommonIdRequest> requestDataList);

	@GET
	@Produces(value = MediaType.APPLICATION_JSON)
	@Path(value = "/{personId:[0-9]+}")
	@Operation(tags = "@v1.0.commons-persons-external_id.get.{personid}.nickname", description = "@v1.0.commons-persons-external_id.get.{personid}.notes", summary = "@v1.0.commons-persons-external_id.get.{personid}.summary")
	@ApiResponses(value = { 
		@ApiResponse(description = "@v1.0.commons-persons-external_id.get.{personid}.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = PersonCommonIdRequest.class))),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.get.{personid}.response.400.message", responseCode = "400"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.get.{personid}.response.500.message", responseCode = "500") })
	PersonCommonIdRequest retrieveByPersonId(@Parameter(description = "@v1.0.commons-persons-external_id.get.{personid}.pathparam.personid.value") @PathParam(value = "personId") Long personId);
    
    
	@GET
	@Produces(MediaType.APPLICATION_JSON)
	@Path("/")
	@Operation(tags = "@v1.0.commons-persons-external_id.get.currentpath.nickname", description = "@v1.0.commons-persons-external_id.get.currentpath.notes", summary = "@v1.0.commons-persons-external_id.get.currentpath.summary")
	@ApiResponses(value = { 
		@ApiResponse(description = "@v1.0.commons-persons-external_id.get.currentpath.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = PersonCommonIdRequest.class))),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.get.currentpath.response.400.message", responseCode = "400"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.get.currentpath.response.500.message", responseCode = "500") })
	public PersonCommonIdRequest retrieve(@Parameter(description = "@v1.0.commons-persons-external_id.get.currentpath.queryparam.person_number.value") @QueryParam(value = "person_number") String personNumber);
	

    @POST
    @Path(value = "/multi_read")
    @Consumes(value = MediaType.APPLICATION_JSON)
    @Produces(value = MediaType.APPLICATION_JSON)
    @Operation(tags = "@v1.0.commons-persons-external_id.post.multi_read.nickname", description = "@v1.0.commons-persons-external_id.post.multi_read.notes", summary = "@v1.0.commons-persons-external_id.post.multi_read.summary")
	@ApiResponses(value = { 
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.multi_read.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = PersonCommonIdRequest.class))),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.multi_read.response.207.message", responseCode = "207"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.multi_read.response.400.message", responseCode = "400"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.multi_read.response.500.message", responseCode = "500") })
    public List<PersonCommonIdRequest>  multiRead(ExtensionSearchCriteria searchCriteria);
    
    /**
	 * This method fetch Employee details without aoid.
	 * 
	 * @param   LightPersonInformationSearchCriteria
	 
	 * @return LightPersonInformation
	 */
    @POST
    @Path(value = "/apply_read")
    @Consumes(value = MediaType.APPLICATION_JSON)
    @Produces(value = MediaType.APPLICATION_JSON)
    @Operation(tags = "@v1.0.commons-persons-external_id.post.apply_read.nickname", description = "@v1.0.commons-persons-external_id.post.apply_read.notes", summary = "@v1.0.commons-persons-external_id.post.apply_read.summary")
	@ApiResponses(value = { 
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.apply_read.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = LightPersonInformation.class))),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.apply_read.response.400.message", responseCode = "400"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.apply_read.response.403.message", responseCode = "403"),
		@ApiResponse(description = "@v1.0.commons-persons-external_id.post.apply_read.response.413.message", responseCode = "413"),
		 })
    public Response applyRead(LightPersonInformationSearchCriteria searchCriteria);
	
 

}
