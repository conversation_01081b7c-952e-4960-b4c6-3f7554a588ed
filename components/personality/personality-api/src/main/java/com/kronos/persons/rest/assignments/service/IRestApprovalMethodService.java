package com.kronos.persons.rest.assignments.service;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

/**
 * Interface intended to retrieve information about Approval Methods.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * Date: Mar 26, 2020
 *
 * <AUTHOR> Kuchynski
 */
@Path(value = "/v1/commons/timecard_approval_methods")
@Publishable(productType = ProductTypes.WFP, type = RestAPITypes.PUBLIC, value = true)
@Consumes(value = MediaType.APPLICATION_JSON)
@Produces(value = MediaType.APPLICATION_JSON)
@Tag(name = "@v1.0.commons-timecard_approval_methods.name")
@OpenAPIDefinition(tags = {
        @Tag(description = "@v1.0.commons-timecard_approval_methods.description",
                extensions = @Extension(properties = {@ExtensionProperty(
                        name = "parent",
                        value = "@v1.0.commons-timecard_approval_methods.parent")}),
                name = "@v1.0.commons-timecard_approval_methods.name")})
public interface IRestApprovalMethodService {

    /**
     * Method used to retrieve all available approval methods.
     *
     * @return list of all available approval methods
     */
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "@v1.0.commons-timecard_approval_methods.get.currentpath.summary",
            tags = "@v1.0.commons-timecard_approval_methods.get.currentpath.nickname",
            description = "@v1.0.commons-timecard_approval_methods.get.currentpath.notes")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "@v1.0.commons-timecard_approval_methods.get.response.200.message",
                    content = @Content(schema = @Schema(type="list", implementation = ObjectRef.class))),
            @ApiResponse(responseCode = "500",
                    description = "@v1.0.commons-timecard_approval_methods.get.response.500.message")})
    Response getAllApprovalMethods();
}
