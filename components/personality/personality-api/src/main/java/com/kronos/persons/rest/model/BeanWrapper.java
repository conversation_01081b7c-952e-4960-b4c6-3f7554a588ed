package com.kronos.persons.rest.model;

import com.kronos.container.api.exception.APIException;

/**
 * BeanWrapper.
 * Contains bean and {@link APIException} if it was threw in time of validation of bean
 * Copyright (C) 2019 Kronos.com
 * Date: Jun 17, 2019
 *
 * @param <T> type of bean
 * <AUTHOR>
 */
public interface BeanWrapper<T> {

    /**
     * Gets exception what was throw in time validation of bean.
     *
     * @return exception instance of {@link APIException}
     */
    APIException getApiException();

    /**
     * Sets exception what was throw in time validation of bean.
     *
     * @param apiException the exception
     */
    void setApiException(APIException apiException);

    /**
     * Gets bean what contains in wrapper.
     *
     * @return bean
     */
    T getBean();

    /**
     * Sets bean into wrapper.
     *
     * @param bean the bean
     */
    void setBean(T bean);
}
