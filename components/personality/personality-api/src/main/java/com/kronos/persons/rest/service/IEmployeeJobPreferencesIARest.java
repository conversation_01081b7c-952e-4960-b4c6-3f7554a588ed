package com.kronos.persons.rest.service;

import com.kronos.commonbusiness.datatypes.ia.IARequest;
import com.kronos.commonbusiness.datatypes.ia.IAResponse;

import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

@Path("/v1/commons/ia/persons/job_preferences")
public interface IEmployeeJobPreferencesIARest {

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    IAResponse getData(IARequest request);
}
