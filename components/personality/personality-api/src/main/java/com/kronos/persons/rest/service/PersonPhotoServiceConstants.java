package com.kronos.persons.rest.service;

public final class PersonPhotoServiceConstants {
	
	private PersonPhotoServiceConstants() {
		
	}
	
	public static final String EMPLOYEE_PHOTO_FAP="EMPLOYEE_PHOTO";
	public static final String FAP_ACTION_VIEW="VIEW";
	public static final String PHOTO_IMAGE_FORMAT_TYPE = "image/png";
	
	public static final String EMPLOYEE_MISSING_PROP_DETAILS = " employees details are missing in request criteria.";
	public static final String EMPLOYEE_MISSING_IDENTIFIER_NAME = "employees";
	public static final String EMPLOYEE_PHOTO_REST_ENDPOINT="/v1/commons/persons/profile_photos";

	public static final String PERSON_NUM_IDENTIFIER = "personNumber";
}
