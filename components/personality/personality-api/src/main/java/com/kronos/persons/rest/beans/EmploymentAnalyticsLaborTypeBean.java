/***********************************************************************
 * EmploymentAnalyticsLaborTypeEntry.java
 *
 * Copyright 2019, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.commonapp.types.business.AnalyticsLaborType;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.analyticslabortypebean.apimodel.description", name = "analyticslabortypebeandetails")
@JsonPropertyOrder(alphabetic = true)
public class EmploymentAnalyticsLaborTypeBean {
	@Schema(description = "@v1.0.analyticslabortypebean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String effectiveDate;

	@Schema(description = "@v1.0.analyticslabortypebean.apimodelproperty.analyticslabortypename.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String analyticsLaborTypeName;

	@Schema(description = "@v1.0.analyticslabortypebean.apimodelproperty.expirationdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String expirationDate;

	@JsonIgnore
	private AnalyticsLaborType analyticsLaborType;

	public String getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public AnalyticsLaborType getAnalyticsLaborType() {
		return analyticsLaborType;
	}

	public void setAnalyticsLaborType(AnalyticsLaborType analyticsLaborType) {
		this.analyticsLaborType = analyticsLaborType;
	}

	public String getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}

	public String getAnalyticsLaborTypeName() {
		return analyticsLaborTypeName;
	}

	public void setAnalyticsLaborTypeName(String analyticslbaorTypeName) {
		this.analyticsLaborTypeName = analyticslbaorTypeName;
	}

	@Override
	public String toString() {
		return "EmploymentAnalyticsLaborTypeBean [effectiveDate=" + effectiveDate
				+ ", expirationDate=" + expirationDate
				+ ", analyticsLaborTypeName=" + analyticsLaborTypeName
				+ ", analyticsLaborType=" + analyticsLaborType + "]";
	}
}
