package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.personalovertimeassignmentbean.apimodel.description", name = "personalOvertimeAssignmentDetail")
@JsonPropertyOrder(alphabetic = true)
public class PersonalOvertimeAssignmentBean {
    @Schema(description = "@v1.0.personalovertimeassignmentbean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String effectiveDate;

    @Schema(description = "@v1.0.personalovertimeassignmentbean.apimodelproperty.expirationdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String expirationDate;

    @Schema(description = "@v1.0.personalovertimeassignmentbean.apimodelproperty.overtimelevel.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long overtimeLevel;

    @Schema(description = "@v1.0.personalovertimeassignmentbean.apimodelproperty.stopovertimeflag.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean stopOvertimeFlag;

    @Schema(description = "@v1.0.personalovertimeassignmentbean.apimodelproperty.overtimetypename.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String overtimeTypeName;

    @Schema(description = "@v1.0.personalovertimeassignmentbean.apimodelproperty.personalovertimerule.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private PersonalOvertimeRuleBean personalOvertimeRule;

    @JsonIgnore
	private ObjectIdLong overtimeTypeId = ObjectIdLong.NULL;

	/**
	 * @return the effectiveDate
	 */
	public String getEffectiveDate() {
		return effectiveDate;
	}

	/**
	 * @param effectiveDate
	 *            the effectiveDate to set
	 */
	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	/**
	 * @return the expirationDate
	 */
	public String getExpirationDate() {
		return expirationDate;
	}

	/**
	 * @param expirationDate
	 *            the expirationDate to set
	 */
	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}

	/**
	 * @return the overtimeLevel
	 */
	public Long getOvertimeLevel() {
		return overtimeLevel;
	}

	/**
	 * @param overtimeLevel
	 *            the overtimeLevel to set
	 */
	public void setOvertimeLevel(Long overtimeLevel) {
		this.overtimeLevel = overtimeLevel;
	}

	/**
	 * @return the stopOvertimeFlag
	 */
	public Boolean getStopOvertimeFlag() {
		return stopOvertimeFlag;
	}

	/**
	 * @param stopOvertimeFlag
	 *            the stopOvertimeFlag to set
	 */
	public void setStopOvertimeFlag(Boolean stopOvertimeFlag) {
		this.stopOvertimeFlag = stopOvertimeFlag;
	}

	/**
	 * @return the overtimeTypeName
	 */
	public String getOvertimeTypeName() {
		return overtimeTypeName;
	}

	/**
	 * @param overtimeTypeName
	 *            the overtimeTypeName to set
	 */
	public void setOvertimeTypeName(String overtimeTypeName) {
		this.overtimeTypeName = overtimeTypeName;
	}

	/**
	 * @return the personalOvertimeRule
	 */
	public PersonalOvertimeRuleBean getPersonalOvertimeRule() {
		return personalOvertimeRule;
	}

	/**
	 * @param personalOvertimeRule
	 *            the personalOvertimeRule to set
	 */
	public void setPersonalOvertimeRule(
			PersonalOvertimeRuleBean personalOvertimeRule) {
		this.personalOvertimeRule = personalOvertimeRule;
	}

	/**
	 * @return the overtimeTypeId
	 */
	public ObjectIdLong getOvertimeTypeId() {
		return overtimeTypeId;
	}

	/**
	 * @param overtimeTypeId
	 *            the overtimeTypeId to set
	 */
	public void setOvertimeTypeId(ObjectIdLong overtimeTypeId) {
		this.overtimeTypeId = overtimeTypeId;
	}

}
