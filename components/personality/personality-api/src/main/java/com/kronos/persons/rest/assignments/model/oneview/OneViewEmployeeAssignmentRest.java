package com.kronos.persons.rest.assignments.model.oneview;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "@v1.0.oneviewemployeeassignmentsrest.apimodel.description", value = "oneViewEmployeeAssignmentsRest")
public class OneViewEmployeeAssignmentRest {

    @ApiModelProperty(value = "@v1.0.oneviewemployeeassignmentsrest.apimodelproperty.personIdentity.description", required = true)
    @JsonProperty(value = "person", required = true)
    private PersonIdentityBean personIdentity;

    @ApiModelProperty(value = "@v1.0.oneviewemployeeassignmentsrest.apimodelproperty.paytypes.description", required = false)
    private List<OneViewPayTypeRest> payTypes;

    @ApiModelProperty(value = "@v1.0.oneviewemployeeassignmentsrest.apimodelproperty.paygroups.description", required = false)
    private List<OneViewPayGroupRest> payGroups;

    public PersonIdentityBean getPersonIdentity() {
        return personIdentity;
    }

    public void setPersonIdentity(PersonIdentityBean personIdentity) {
        this.personIdentity = personIdentity;
    }

    public List<OneViewPayTypeRest> getPayTypes() {
        return payTypes;
    }

    public void setPayTypes(List<OneViewPayTypeRest> payTypes) {
        this.payTypes = payTypes;
    }

    public List<OneViewPayGroupRest> getPayGroups() {
        return payGroups;
    }

    public void setPayGroups(List<OneViewPayGroupRest> payGroups) {
        this.payGroups = payGroups;
    }
}