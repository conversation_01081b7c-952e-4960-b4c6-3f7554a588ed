package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.badgeassignmentbean.apimodel.description", name = "badgeAssignment")
@JsonPropertyOrder(alphabetic = true)
public class BadgeAssignmentBean {
    @Schema(description = "@v1.0.badgeassignmentbean.apimodelproperty.badgenumber.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String badgeNumber;

    @Schema(description = "@v1.0.badgeassignmentbean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String effectiveDate;

    @Schema(description = "@v1.0.badgeassignmentbean.apimodelproperty.expirationdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String expirationDate;

	public String getBadgeNumber() {
		return badgeNumber;
	}

	public void setBadgeNumber(String badgeNumber) {
		this.badgeNumber = badgeNumber;
	}

	public String getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public String getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}

	@Override
	public String toString() {
		return "BadgeAssignmentBean [badgeNumber=" + badgeNumber
				+ ", effectiveDate=" + effectiveDate + ", expirationDate="
				+ expirationDate + "]";
	}
}
