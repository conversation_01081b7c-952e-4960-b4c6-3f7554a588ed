package com.kronos.persons.rest.assignments.model;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.model.BeanWrapper;

/**
 * AttendanceAdminAssignmentWrapper.
 *
 * Implementation of {@link BeanWrapper} for bean
 * instance of {@link AttendanceAdminAssignmentBean}
 *
 * Copyright (C) 2019 Kronos.com
 * Date: Jul 05, 2019
 *
 * <AUTHOR>
 */
public class AttendanceAdminAssignmentWrapper implements BeanWrapper<AttendanceAdminAssignmentBean> {

    private AttendanceAdminAssignmentBean wrapperObject;

    private APIException apiException;

    /**
     * Constructor.
     *
     * @param attendanceAdminAssignmentBean instance of {@link AttendanceAdminAssignmentBean}
     */
    public AttendanceAdminAssignmentWrapper(AttendanceAdminAssignmentBean attendanceAdminAssignmentBean) {
        this.wrapperObject = attendanceAdminAssignmentBean;
    }

    @Override
    public APIException getApiException() {
        return apiException;
    }

    @Override
    public void setApiException(APIException apiException) {
        this.apiException = apiException;
    }

    @Override
    public AttendanceAdminAssignmentBean getBean() {
        return wrapperObject;
    }

    @Override
    public void setBean(AttendanceAdminAssignmentBean wrapperObject) {
        this.wrapperObject = wrapperObject;
    }
}
