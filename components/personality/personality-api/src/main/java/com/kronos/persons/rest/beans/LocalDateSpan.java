package com.kronos.persons.rest.beans;

import java.time.LocalDate;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.platform.utility.framework.datetime.KDateSpan;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.localdatespan.apimodel.description", title = "localDateSpan")
public class LocalDateSpan {
    /**
     * Takes space separated date fields in YYYY-MM-DD format or specify startDate & endDate
     * Eg. 2015-01-01 3000-12-31
     */
    @Schema(description = "@v1.0.localdatespan.apimodelproperty.daterange.description")
    private String dateRange;

    @Schema(description = "@v1.0.localdatespan.apimodelproperty.startdate.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String startDate;

    @Schema(description = "@v1.0.localdatespan.apimodelproperty.enddate.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String endDate;
    
    @Schema(description = "@v1.0.localdatespan.apimodelproperty.symbolicperiod.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private ObjectRef symbolicPeriod;
    
    private KDateSpan dateSpan;
	
	public LocalDateSpan(){
		
	}
	
	public LocalDateSpan(String dateRange) throws Exception {
		this.dateRange = dateRange;
	}


	public LocalDateSpan(String startDate, String endDate) {
		this.startDate = startDate;
		this.endDate = endDate;
	}
	
	/**
	 * @return the dateRange
	 */
	public String getDateRange() {
		return dateRange;
	}


	/**
	 * @param dateRange the dateRange to set
	 * @throws Exception 
	 */
	public void setDateRange(String dateRange) throws Exception {
		this.dateRange = dateRange;
	}


	/**
	 * @return the startDate
	 */
	public String getStartDate() {
		return startDate;
	}


	/**
	 * @param startDate the startDate to set
	 */
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}


	/**
	 * @return the endDate
	 */
	public String getEndDate() {
		return endDate;
	}


	/**
	 * @param endDate the endDate to set
	 */
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

    /**
     * @return the dateSpan
     */
    @JsonIgnore
    public KDateSpan getDateSpan() {
        if (dateSpan == null) {
            if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
                LocalDate startDte = LocalDate.parse(startDate);
                LocalDate endDte = LocalDate.parse(endDate);
                dateSpan = new KDateSpan(KDate.createDate(startDte.getYear(), startDte.getMonthValue(), startDte.getDayOfMonth()),
                        KDate.createDate(endDte.getYear(), endDte.getMonthValue(), endDte.getDayOfMonth()));
            } else {
                KDate today = KDate.createDate();
                dateSpan = new KDateSpan(today, today);
            }
        }
        return dateSpan;
    }

    /**
     * @param dateSpan
     *            the dateSpan to set
     */
    public void setDateSpan(KDateSpan dateSpan) {
        this.dateSpan = dateSpan;
    }

    /**
     * @return the symbolicPeriod
     */
    public ObjectRef getSymbolicPeriod() {
        return symbolicPeriod;
    }

    /**
     * @param symbolicPeriod
     *            the symbolicPeriod to set
     */
    public void setSymbolicPeriod(ObjectRef symbolicPeriod) {
        this.symbolicPeriod = symbolicPeriod;
    }

    @Override
	public String toString() {
		if (this.dateRange != null)
			return dateRange;
		if (this.startDate != null && this.endDate != null)
			return this.startDate.toString() + " " + this.endDate.toString();
		
		return "";
	}
	
}