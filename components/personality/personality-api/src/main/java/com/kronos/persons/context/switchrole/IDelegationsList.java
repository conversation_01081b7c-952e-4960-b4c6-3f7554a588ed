/***********************************************************************
 * IRoleDelegateContext Copyright 2018, Kronos Incorporated. All rights
 * reserved.
 **********************************************************************/
package com.kronos.persons.context.switchrole;

/**
 * Contract to provide all details of a User
 * <AUTHOR>
 *
 */
public interface IDelegationsList {
	
	/**
	 * getter to get Id.
	 * @return
	 */
   public String getId();
   
   /**
    * getter for get Delegator Id
    * @return
    */
   public String getDelegatorId();

   /**
    * getter to get the delegator
    * @return
    */
   public String getDelegator();

   /**
    * getter method to get the role
    * @return
    */
   public String getRole();

   /**
    * getter method to get StartDate
    * @return
    */
   public String getStartDate();

   /**
    * getter method to get the end Date
    * @return
    */
   public String getEndDate();

   /**
    * setter method to setid
    * @param id
    */
   public void setId(String id);

   /**
    * setter method to set the delegator id
    * @param delegatorId
    */
   public void setDelegatorId(String delegatorId);
   
   /**
    * setter method to set the delegator
    * @param delegator
    */
   public void setDelegator(String delegator);

   /**
    * setter method to set role
    * @param role
    */
   public void setRole(String role);

   /**
    * setter method to set the startDate
    * @param startDate
    */
   public void setStartDate(String startDate);

   /**
    * setter method to set the end date
    * @param endDate
    */
   public void setEndDate(String endDate);
}
