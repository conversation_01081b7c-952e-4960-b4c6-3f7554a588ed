package com.kronos.persons.rest.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO to hold effective date and expiration date.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * <p>
 * Date: March 6, 2020
 *
 * <AUTHOR>
 */
@Schema(name = "EffectiveExpirationDateRange",
        description = "@v1.0.commons-persons-wage_work_rules.daterange.apimodel.description")
public class DateRangeDTO {

    @Schema(description = "@v1.0.commons-persons-wage_work_rules.daterange.apimodelproperty.effectivedate.description")
    private String effectiveDate;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.daterange.apimodelproperty.expirationdate.description")
    private String expirationDate;

    /**
     * @return effective date
     */
    public String getEffectiveDate() {
        return effectiveDate;
    }

    /**
     * Sets effective date.
     *
     * @param effectiveDate effective date, to be set
     */
    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    /**
     * @return expiration date
     */
    public String getExpirationDate() {
        return expirationDate;
    }

    /**
     * Sets expiration date.
     *
     * @param expirationDate expiration date, to be set
     */
    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }
}
