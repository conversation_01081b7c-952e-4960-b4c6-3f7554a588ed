package com.kronos.persons.rest.model;

import com.kronos.people.help.api.dto.NewReleaseFeaturePreferenceDTO;

public class UserFeatures {

	private boolean isFeedbackEnabled;
	private boolean isCommunitySearchEnabled;
	private boolean isNewHomeExperienceEnabled;
	private NewReleaseFeaturePreferenceDTO newReleaseFeaturePreference;

	public boolean isFeedbackEnabled() {
		return this.isFeedbackEnabled;
	}

	public void setFeedbackEnabled(boolean isFeedbackEnabled) {
		this.isFeedbackEnabled = isFeedbackEnabled;
	}

	public boolean isCommunitySearchEnabled() {
		return this.isCommunitySearchEnabled;
	}

	public void setCommunitySearchEnabled(boolean isCommunitySearchEnabled) {
		this.isCommunitySearchEnabled = isCommunitySearchEnabled;
	}

	public boolean isNewHomeExperienceEnabled() { return this.isNewHomeExperienceEnabled; }


	public void setNewHomeExperienceEnabled(boolean isNewHomeExperienceEnabled) {
		this.isNewHomeExperienceEnabled = isNewHomeExperienceEnabled;
	}

	public NewReleaseFeaturePreferenceDTO getNewReleaseFeaturePreference() {
		return newReleaseFeaturePreference;
	}

	public void setNewReleaseFeaturePreference(NewReleaseFeaturePreferenceDTO newReleaseFeaturePreference) {
		this.newReleaseFeaturePreference = newReleaseFeaturePreference;
	}

	@Override
	public String toString() {
		return "UserFeatures [isFeedbackEnabled=" + isFeedbackEnabled + ", isCommunitySearchEnabled="
				+ isCommunitySearchEnabled + ", newHomeExperienceEnabled=" + isNewHomeExperienceEnabled
				+ ", newReleaseFeaturePreference=" + newReleaseFeaturePreference + "]";
	}
}
