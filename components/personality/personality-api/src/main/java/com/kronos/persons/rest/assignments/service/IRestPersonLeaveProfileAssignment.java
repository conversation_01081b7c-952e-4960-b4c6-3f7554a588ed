package com.kronos.persons.rest.assignments.service;

import java.util.List;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.kronos.persons.rest.assignments.model.AssignmentProfileRequestBean;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;

/**
 * 
 * <AUTHOR>
 *
 */

@OpenAPIDefinition(tags = {
		@Tag(description = "@v1.0.commons-persons-leave_profile.description", extensions = @Extension(properties = {
				@ExtensionProperty(name = "parent", value = "@root.personassignments.default") }), name = "@v1.0.commons-persons-leave_profile.name") })
@Tag(name = "@v1.0.commons-persons-leave_profile.name")
@Publishable(type = RestAPITypes.PUBLIC, value = true, productType = ProductTypes.WFP)
@Path("/v1/commons/persons/leave_profile")
public interface IRestPersonLeaveProfileAssignment {

	@GET
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "@v1.0.commons-persons-leave_profile.get.{personid}.value", description = "@v1.0.commons-persons-leave_profile.get.{personid}.notes", tags = "@v1.0.commons-persons-leave_profile.get.{personid}.nickname")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-leave_profile.get.{personid}.response.200.message", content = @Content(schema = @Schema(implementation = AssignmentProfileRequestBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-leave_profile.get.{personid}.response.400.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-leave_profile.get.{personid}.response.500.message")})
	@Path(value = "/{personId:[0-9]+}")
	public AssignmentProfileRequestBean retrievebyPersonId(@Parameter(description = "@v1.0.commons-persons-leave_profile.get.{personid}.pathparam.personid.value") @PathParam(value = "personId") Long personId);
	
	
	@GET
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "@v1.0.commons-persons-leave_profile.get.currentpath.value", description = "@v1.0.commons-persons-leave_profile.get.currentpath.notes", tags = "@v1.0.commons-persons-leave_profile.get.currentpath.nickname")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-leave_profile.get.currentpath.response.200.message", content = @Content(schema = @Schema(implementation = AssignmentProfileRequestBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-leave_profile.get.currentpath.response.400.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-leave_profile.get.currentpath.response.500.message")})
	@Path(value = "/")
	public AssignmentProfileRequestBean retrieve(@Parameter(description = "@v1.0.commons-persons-leave_profile.get.currentpath.queryparam.person_number.value") @QueryParam(value = "person_number") String personNumber);
	

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "@v1.0.commons-persons-leave_profile.post.multi_read.name", description = "@v1.0.commons-persons-leave_profile.post.multi_read.notes", tags = "@v1.0.commons-persons-leave_profile.post.multi_read.nickname")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-leave_profile.post.multi_read.response.200.message",content = @Content(schema = @Schema(type="list", implementation = AssignmentProfileRequestBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-leave_profile.post.multi_read.response.400.message"),
			@ApiResponse(responseCode = "207", description = "@v1.0.commons-persons-leave_profile.post.multi_read.response.207.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-leave_profile.post.multi_read.response.500.message")})
	@Path("/multi_read")
	public List<AssignmentProfileRequestBean> retrieveList(ExtensionSearchCriteria searchCriteria);
	
	@PUT
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@ResponseStatus(code=HttpStatus.OK)
	@Operation(summary = "@v1.0.commons-persons-leave_profile.put.currentpath.summary", description = "@v1.0.commons-persons-leave_profile.put.currentpath.notes", tags = "@v1.0.commons-persons-leave_profile.put.currentpath.nickname")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.put.currentpath.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = AssignmentProfileRequestBean.class))),
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.put.currentpath.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.put.currentpath.response.500.message", responseCode = "500") })
	@Path("/")
	public AssignmentProfileRequestBean update(
			AssignmentProfileRequestBean requestBean);

	@DELETE
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@ResponseStatus(code=HttpStatus.NO_CONTENT)
	@Operation(summary = "@v1.0.commons-persons-leave_profile.delete.currentpath.summary", description = "@v1.0.commons-persons-leave_profile.delete.currentpath.notes", tags = "@v1.0.commons-persons-leave_profile.delete.currentpath.nickname")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.delete.currentpath.response.204.message", responseCode = "204"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.delete.currentpath.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.delete.currentpath.response.500.message", responseCode = "500") })
	@Path("/")
	public void delete(
			AssignmentProfileRequestBean requestBean);

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@ResponseStatus(code=HttpStatus.OK)
	@Operation(summary = "@v1.0.commons-persons-leave_profile.post.multi_update.summary", description = "@v1.0.commons-persons-leave_profile.post.multi_update.notes", tags = "@v1.0.commons-persons-leave_profile.post.multi_update.nickname")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.post.multi_update.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = AssignmentProfileRequestBean.class))),
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.post.multi_update.response.207.message", responseCode = "207"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.post.multi_update.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.post.multi_update.response.500.message", responseCode = "500") })
	@Path("/multi_update")
	public List<AssignmentProfileRequestBean> multiUpdate(
			List<AssignmentProfileRequestBean> requestBean);

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@ResponseStatus(code=HttpStatus.NO_CONTENT)
	@Operation(summary = "@v1.0.commons-persons-leave_profile.post.multi_delete.summary", description = "@v1.0.commons-persons-leave_profile.post.multi_delete.notes", tags = "@v1.0.commons-persons-leave_profile.post.multi_delete.nickname")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.post.multi_delete.response.204.message", responseCode = "204"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.post.multi_delete.response.207.message", responseCode = "207"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.post.multi_delete.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_profile.post.multi_delete.response.500.message", responseCode = "500") })
	@Path("/multi_delete")
	public void multiDelete(
			List<AssignmentProfileRequestBean> requestBean);
}
