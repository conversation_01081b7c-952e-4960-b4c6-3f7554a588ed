package com.kronos.persons.rest.assignments.model.brazilcompliance;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.kronos.persons.rest.model.EmployeeCriteria;

import java.io.Serializable;

public class BrazilWhereCriteria implements Serializable {
    @JsonProperty(value = "companyDetails")
    Boolean companyDetails;
    @JsonProperty(value = "employees", required = true)
    private EmployeeCriteria employees;

    public Boolean getCompanyDetails() {
        return companyDetails;
    }

    public void setCompanyDetails(Boolean companyDetails) {
        this.companyDetails = companyDetails;
    }

    public EmployeeCriteria getEmployees() {
        return employees;
    }

    public void setEmployees(EmployeeCriteria employees) {
        this.employees = employees;
    }
}