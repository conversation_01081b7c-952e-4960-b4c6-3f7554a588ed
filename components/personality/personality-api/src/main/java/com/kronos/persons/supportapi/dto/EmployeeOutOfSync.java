/*
 * *****************************************************************************
 * Copyright (c) 2020 Kronos, Inc. All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * Kronos, Inc. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with Kronos.
 *
 * KRONOS MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE
 * SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
 * PURPOSE, OR NON-INFRINGEMENT. KRONOS SHALL NOT BE LIABLE FOR ANY DAMAGES
 * SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING
 * THIS SOFTWARE OR ITS DERIVATIVES.
 ****************************************************************************
 *
 **/
package com.kronos.persons.supportapi.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * This class contains employee data in cache which is out of sync data compared to data present in database
 * <AUTHOR>
 *
 */
public class EmployeeOutOfSync {

	private String personNumber;
	private List<OutOfSyncAttribute> outOfSyncAttributes = new ArrayList<>();
	
	public String getPersonNumber() {
		return personNumber;
	}
	public void setPersonNumber(String personNumber) {
		this.personNumber = personNumber;
	}
	public List<OutOfSyncAttribute> getOutOfSyncAttributes() {
		return outOfSyncAttributes;
	}
	public void setOutOfSyncAttributes(List<OutOfSyncAttribute> outOfSyncAttributes) {
		this.outOfSyncAttributes = outOfSyncAttributes;
	}
	@Override
	public String toString() {
		return "EmployeeOutOfSync [personNumber=" + personNumber + ", outOfSyncAttributes=" + outOfSyncAttributes + "]";
	}
	
	
}
