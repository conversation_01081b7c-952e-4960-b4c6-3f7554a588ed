package com.kronos.persons.supportapi.dto;

import java.util.List;

public class PersonalityCacheInsertResponse {
    private String message;
    private Integer primeCount;
    private String tenantShortName;
    private List<Long> personIds;

    public Integer getPrimeCount() {
        return primeCount;
    }

    public void setPrimeCount(Integer primeCount) {
        this.primeCount = primeCount;
    }

    public String getTenantShortName() {
        return tenantShortName;
    }

    public void setTenantShortName(String tenantShortName) {
        this.tenantShortName = tenantShortName;
    }

    public List<Long> getPersonIds() {
        return personIds;
    }
    public void setPersonIds(List<Long> personIds) {
        this.personIds = personIds;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "PersonalityCacheInsertResponse [message=" + message +
                ", primeCount=" + primeCount +
                ", tenantShortName=" + tenantShortName +
                ", personIds=" + personIds + "]";
    }
}
