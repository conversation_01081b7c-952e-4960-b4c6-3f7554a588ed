package com.kronos.persons.rest.beans;

import java.util.ArrayList;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.lightpersoninfobean.apimodel.description", title = "lightPersonInfo")
public class LightPersonInfoBean {

	@Schema(description = "@v1.0.lightpersoninfobean.apimodelproperty.totalElements.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String totalElements;

	@Schema(description = "@v1.0.lightpersoninfobean.apimodelproperty.records.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private List<RecordInfo> records = new ArrayList<>();

	public String getTotalElements() {
		return totalElements;
	}

	public void setTotalElements(String totalElements) {
		this.totalElements = totalElements;
	}

	public List<RecordInfo> getRecords() {
		return records;
	}

	public void setRecords(List<RecordInfo> records) {
		this.records = records;
	}

}
