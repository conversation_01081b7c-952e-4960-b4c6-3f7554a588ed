package com.kronos.persons.rest.assignments.model;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(title = "positionsJobPreference", description = "@v1.0.positionsJobPreferencesBean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class PositionJobPreferenceBean {

   @Schema(description = "@v1.0.positionJobPreferencesBean.apimodelproperty.position.description")
   private ObjectRef position;

   @Schema(description = "@v1.0.positionJobPreferencesBean.apimodelproperty.jobPreferences.description")
   private List<JobPreferenceBean> jobPreferences;

   public ObjectRef getPosition() {
      return position;
   }

   public void setPosition(ObjectRef position) {
      this.position = position;
   }

   public List<JobPreferenceBean> getJobPreferences() {
      return jobPreferences;
   }

   public void setJobPreferences(List<JobPreferenceBean> jobPreferences) {
      this.jobPreferences = jobPreferences;
   }
}
