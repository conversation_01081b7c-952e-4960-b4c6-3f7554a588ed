package com.kronos.persons.rest.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.batchresponsebean.apimodel.description", name = "batchResponse")
public class BatchResponseBean {
    @Schema(description = "@v1.0.batchresponsebean.apimodelproperty.code.description")
    private String errorCode;

    @Schema(description = "@v1.0.batchresponsebean.apimodelproperty.message.description")
    private String message;

    @Schema(description = "@v1.0.batchresponsebean.apimodelproperty.details.description")
	private BatchResponseDetailsBean details;

	/**
	 * @return the code
	 */
	public String getErrorCode() {
		return errorCode;
	}


	/**
	 * @param code the code to set
	 */
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}


	/**
	 * @return the message
	 */
	public String getMessage() {
		return message;
	}


	/**
	 * @param message the message to set
	 */
	public void setMessage(String message) {
		this.message = message;
	}


	/**
	 * @return the details
	 */
	public BatchResponseDetailsBean getDetails() {
		return details;
	}


	/**
	 * @param details the details to set
	 */
	public void setDetails(BatchResponseDetailsBean details) {
		this.details = details;
	}


}
