package com.kronos.persons.rest.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.responsebean.apimodel.description", name = "response")
public class ResponseBean {
    @Schema(description = "@v1.0.responsebean.apimodelproperty.personid.description")
    private Long personId;

    @Schema(description = "@v1.0.responsebean.apimodelproperty.personnumber.description")
    private String personNumber;
    
    private Object input;


	@Schema(description = "@v1.0.responsebean.apimodelproperty.error.description")
	private RestErrorBean error;

	/**
	 * @return the personId
	 */
	public Long getPersonId() {
		return personId;
	}

	/**
	 * @param personId the personId to set
	 */
	public void setPersonId(Long personId) {
		this.personId = personId;
	}

	/**
	 * @return the error
	 */
	public RestErrorBean getError() {
		return error;
	}

	/**
	 * @param error the error to set
	 */
	public void setError(RestErrorBean error) {
		this.error = error;
	}

	/**
	 * @return the personNumber
	 */
	public String getPersonNumber() {
		return personNumber;
	}

	/**
	 * @param personNumber the personNumber to set
	 */
	public void setPersonNumber(String personNumber) {
		this.personNumber = personNumber;
	}

	/**
	 * @return the input
	 */
	public Object getInput() {
		return input;
	}

	/**
	 * @param input the input to set
	 */
	public void setInput(Object input) {
		this.input = input;
	}
	
	
}
