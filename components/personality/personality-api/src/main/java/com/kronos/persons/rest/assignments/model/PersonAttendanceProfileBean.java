package com.kronos.persons.rest.assignments.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.persons.rest.beans.PeopleAssignmentBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "attendanceProfile", description = "@v1.0.personattendanceprofilebean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class PersonAttendanceProfileBean extends PeopleAssignmentBean implements BaseAssignmentBean{

	@Schema(description = "@v1.0.personattendanceprofilebean.apimodelproperty.attendanceProfileAssignments.description")
	private List<PersonAttendanceProfileAssignmentBean> attendanceProfileAssignments;


	public List<PersonAttendanceProfileAssignmentBean> getAttendanceProfileAssignments() {
		return attendanceProfileAssignments;
	}

	public void setAttendanceProfileAssignments(List<PersonAttendanceProfileAssignmentBean> attendanceProfileAssignments) {
		this.attendanceProfileAssignments = attendanceProfileAssignments;
	}


}
