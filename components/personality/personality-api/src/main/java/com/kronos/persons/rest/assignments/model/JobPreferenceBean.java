package com.kronos.persons.rest.assignments.model;

import java.time.LocalDate;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.wfc.platform.persistence.framework.ObjectId;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "jobPreference", description = "@v1.0.jobPreferenceBean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class JobPreferenceBean {
	
	@Schema(description = "@v1.0.jobPreferenceBean.apimodelproperty.job.description")
	private String job;
	
	@Schema(description = "@v1.0.jobPreferenceBean.apimodelproperty.preference.description")
	private String preference;
	
	@JsonIgnore
	private ObjectId workRuleId;
	
	@JsonIgnore
	private ObjectIdLong orgJobId;
	
	@Schema(description = "@v1.0.jobPreferenceBean.apimodelproperty.seniorityDate.description")
	private LocalDate seniorityDate;
	@JsonIgnore
	private String workRule;

	@Schema(description = "@v1.0.jobPreferenceBean.apimodelproperty.schedulingContextSet.description")
	private List<ObjectRef> schedulingContextSet;

    @JsonIgnore
	private List<Long> schedulingContextIds;

	@JsonIgnore
	private boolean fromEmployeeRequest;

	public String getJob() {
		return job;
	}

	public void setJob(String job) {
		this.job = job;
	}


	public String getPreference() {
		return preference;
	}

	public void setPreference(String preference) {
		this.preference = preference;
	}

	public ObjectId getWorkRuleId() {
		return workRuleId;
	}

	public void setWorkRuleId(ObjectId workRuleId) {
		this.workRuleId = workRuleId;
	}

	public ObjectIdLong getOrgJobId() {
		return orgJobId;
	}

	public void setOrgJobId(ObjectIdLong orgJobId) {
		this.orgJobId = orgJobId;
	}

	public LocalDate getSeniorityDate() {
		return seniorityDate;
	}

	public void setSeniorityDate(LocalDate seniorityDate) {
		this.seniorityDate = seniorityDate;
	}

	public String getWorkRule() {
		return workRule;
	}

	public void setWorkRule(String workRule) {
		this.workRule = workRule;
	}

	public List<ObjectRef> getSchedulingContextSet() {
		return schedulingContextSet;
	}

	public void setSchedulingContextSet(List<ObjectRef> schedulingContextSet) {
		this.schedulingContextSet = schedulingContextSet;
	}

	public List<Long> getSchedulingContextIds() {
        return schedulingContextIds;
    }

    public void setSchedulingContextIds(List<Long> schedulingContextIds) {
        this.schedulingContextIds = schedulingContextIds;
    }

	public boolean isFromEmployeeRequest() {
		return fromEmployeeRequest;
	}

	public void setFromEmployeeRequest(boolean fromEmployeeRequest) {
		this.fromEmployeeRequest = fromEmployeeRequest;
	}
}
