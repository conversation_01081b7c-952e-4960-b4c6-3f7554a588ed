package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.telephonenumberbean.apimodel.description", name = "telephoneNumber")
@JsonPropertyOrder(alphabetic = true)
public class TelephoneNumberBean {
	@Schema(description = "@v1.0.telephonenumberbean.apimodelproperty.phonenumber.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String phoneNumber;

	@Schema(description = "@v1.0.telephonenumberbean.apimodelproperty.contacttypename.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String contactTypeName;

	@Schema(description = "@v1.0.telephonenumberbean.apimodelproperty.issmsswitch.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Boolean isSMSSwitch;

	@JsonIgnore
	private ObjectIdLong telephoneNumberTypeId = null;

	/**
	 * @return the phoneNumber
	 */
	public String getPhoneNumber() {
		return phoneNumber;
	}

	/**
	 * @param phoneNumber
	 *            the phoneNumber to set
	 */
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	/**
	 * @return the contactTypeName
	 */
	

	/**
	 * @param contactTypeName
	 *            the contactTypeName to set
	 */
	

	/**
	 * @return the isSMSSwitch
	 */
	public Boolean getIsSMSSwitch() {
		return isSMSSwitch;
	}

	public String getContactTypeName() {
		return contactTypeName;
	}

	public void setContactTypeName(String contactTypeName) {
		this.contactTypeName = contactTypeName;
	}

	/**
	 * @param isSMSSwitch
	 *            the isSMSSwitch to set
	 */
	public void setIsSMSSwitch(Boolean isSMSSwitch) {
		this.isSMSSwitch = isSMSSwitch;
	}

	/**
	 * @return the telephoneNumberTypeId
	 */
	public ObjectIdLong getTelephoneNumberTypeId() {
		return telephoneNumberTypeId;
	}

	/**
	 * @param telephoneNumberTypeId
	 *            the telephoneNumberTypeId to set
	 */
	public void setTelephoneNumberTypeId(ObjectIdLong telephoneNumberTypeId) {
		this.telephoneNumberTypeId = telephoneNumberTypeId;
	}

}
