/***********************************************************************
 * EmployeeRef.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.persons.rest.model;

import jakarta.inject.Named;

import com.kronos.commonbusiness.converter.api.IDTOReference;


/**
 * Ref class for Employee.
 * 
 * <AUTHOR>
 *
 */
@Named
public class EmployeeRef implements IDTOReference {

	/**
	 * Long containing the internal id.
	 */
	private Long internalId;

	/**
	 * String containing the personId.
	 */
	private String personId;

	/**
	 * Default constructor.
	 */
	public EmployeeRef() {

	}

	/**
	 * Constructor with internalId initialization.
	 * @param id Long
	 */
	public EmployeeRef(Long id) {
		internalId = id;
	}

	/**
	 * Constructor with personId initialization.
	 * @param personNumber String 
	 */
	public EmployeeRef(String personNumber) {
		personId = personNumber;
	}

	/**
	 * Constructor with internalId, personId initialization.
	 * @param internalId
	 * @param personId
	 */
	public EmployeeRef(Long internalId, String personId) {

		this.internalId = internalId;
		this.personId = personId;
	}

	/**
	 * @return Long containing the internalId
	 */
	public Long getInternalId() {
		return internalId;
	}

	/**
	 * @param internalId Long containing the internalId to set
	 */
	public void setInternalId(Long internalId) {
		this.internalId = internalId;
	}

	/**
	 * @return String containing the personId
	 */
	public String getPersonId() {
		return personId;
	}

	/**
	 * @param personId String containing the personId
	 */
	public void setPersonId(String personId) {
		this.personId = personId;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((internalId == null) ? 0 : internalId.hashCode());
		result = prime * result + ((personId == null) ? 0 : personId.hashCode());
		return result;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		EmployeeRef other = (EmployeeRef) obj;
		return checkInternalIdEquality(other) && checkPersonIdEquality(other);
	}

	protected boolean checkPersonIdEquality(EmployeeRef other) {
		if (personId == null && other.personId != null) {
			return false;
		} else if (personId != null && !personId.equals(other.personId)) {
			return false;
		}
		return true;
	}

	protected boolean checkInternalIdEquality(EmployeeRef other) {
		if (internalId == null && other.internalId != null) {
			return false;
		} else if (internalId != null && !internalId.equals(other.internalId)) {
			return false;
		}
		return true;
	}

}
