package com.kronos.persons.rest.assignments.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.persons.rest.beans.PeopleAssignmentBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(title = "processProfileAssignmentRequest", description = "@v1.0.processProfileassignmentrequestbean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class ProcessProfileAssignmentRequestBean extends PeopleAssignmentBean implements BaseAssignmentBean{

	@Schema(description = "@v1.0.processProfileassignmentrequestbean.apimodelproperty.managerProcessProfileName.description")
	@JsonInclude(Include.NON_NULL)
	String managerProcessProfileName;
	
	@Schema(description = "@v1.0.processProfileassignmentrequestbean.apimodelproperty.employeeProcessProfileName.description")
	String employeeProcessProfileName;

	public String getManagerProcessProfileName() {
		return managerProcessProfileName;
	}

	public void setManagerProcessProfileName(String managerProcessProfileName) {
		this.managerProcessProfileName = managerProcessProfileName;
	}

	public String getEmployeeProcessProfileName() {
		return employeeProcessProfileName;
	}

	public void setEmployeeProcessProfileName(String employeeProcessProfileName) {
		this.employeeProcessProfileName = employeeProcessProfileName;
	}

}
