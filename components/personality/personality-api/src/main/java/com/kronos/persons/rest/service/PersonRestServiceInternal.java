/***********************************************************************
 * PersonRestServiceInternal.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/

package com.kronos.persons.rest.service;

import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;

import java.util.List;



/**
 * This is the REST respresentation for Person related REST calls.
 * <AUTHOR>
 *
 */

@Path(value = "/v1/commons/persons/internal")
public interface PersonRestServiceInternal {
    /**
     * Validates whether is photo enabled.
     * @return boolean value
     */
    @GET
    @Path("/isPhotosEnabled")
    @Produces("application/json")
    public Boolean isPhotosEnabledNew();

    /**
     * Fetches UserPhotoId.
     * @return String containing the photoId
     */
    @GET
    @Path("/getUserPhotoId")
    @Produces("application/json")
    public String getUserPhotoIdNew();

    /**
     * This method returns the SystemConfiguration information
     * @return ISystemConfiguration instance
     */
    @GET
    @Path("/getSystemConfiguration")
    @Produces("application/json")
    public ISystemConfiguration getSystemConfigurationNew();

    /**
     * This rest call is for handling delete person from people editor.
     * @param personIds List of Long ids
     */
    @DELETE
    @Path ("/deletePerson")
    @Produces(MediaType.APPLICATION_JSON)
    public void deletePersonsNew(@QueryParam("personIds") List<Long> personIds);


    /**
     * This rest call is used to validate person delete is allowed on not
     * @return boolean value
     */
    @GET
    @Path ("/isDeleteAllowed")
    @Produces(MediaType.APPLICATION_JSON)
    public Boolean isDeleteAllowedNew();

    /**
     * This rest call is used to get offline timeout
     * @return Integer value
     */
    @GET
    @Path ("/offlineTimeout")
    @Produces(MediaType.APPLICATION_JSON)
    public Integer getOfflineTimeoutNew();
}
