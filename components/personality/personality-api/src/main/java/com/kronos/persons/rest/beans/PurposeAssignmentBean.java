package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.purposeassignmentbean.apimodel.description", name = "purposeAssignment")
@JsonPropertyOrder(alphabetic = true)
public class PurposeAssignmentBean {
    @Schema(description = "@v1.0.purposeassignmentbean.apimodelproperty.purpose.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String purpose;

    @Schema(description = "@v1.0.purposeassignmentbean.apimodelproperty.requestreviewerlist.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String requestReviewerList;

	/**
	 * @return the purpose
	 */
	public String getPurpose() {
		return purpose;
	}

	/**
	 * @param purpose
	 *            the purpose to set
	 */
	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}

	/**
	 * @return the requestReviewerList
	 */
	public String getRequestReviewerList() {
		return requestReviewerList;
	}

	/**
	 * @param requestReviewerList
	 *            the requestReviewerList to set
	 */
	public void setRequestReviewerList(String requestReviewerList) {
		this.requestReviewerList = requestReviewerList;
	}

}
