package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.commonapp.types.business.EmploymentStatusType;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.employmentstatusbean.apimodel.description", name = "employmentStatusDetails")
@JsonPropertyOrder(alphabetic = true)
public class EmploymentStatusBean {
	@Schema(description = "@v1.0.employmentstatusbean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String effectiveDate;

	@Schema(description = "@v1.0.employmentstatusbean.apimodelproperty.employmentstatusname.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String employmentStatusName;

	@Schema(description = "@v1.0.employmentstatusbean.apimodelproperty.expirationdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String expirationDate;

	@JsonIgnore
	private EmploymentStatusType employmentStatus;

	public String getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public String getEmploymentStatusName() {
		return employmentStatusName;
	}

	public void setEmploymentStatusName(String employmentStatusName) {
		this.employmentStatusName = employmentStatusName;
	}

	public String getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}

	public EmploymentStatusType getEmploymentStatus() {
		return employmentStatus;
	}

	public void setEmploymentStatus(EmploymentStatusType employmentStatus) {
		this.employmentStatus = employmentStatus;
	}

	@Override
	public String toString() {
		return "EmploymentStatusBean [effectiveDate=" + effectiveDate
				+ ", expirationDate=" + expirationDate
				+ ", employmentStatusName=" + employmentStatusName
				+ ", employmentStatus=" + employmentStatus + "]";
	}
}
