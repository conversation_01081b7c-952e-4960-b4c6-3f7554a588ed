package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.predictiveschedulingeligibilitybean.apimodel.description", name = "predictiveSchedulingEligibilityDetails")
@JsonPropertyOrder(alphabetic = true)
public class PredictiveSchedulingEligibilityBean {

   @Schema(description = "@v1.0.predictiveschedulingeligibilitybean.apimodelproperty.iseligible.description")
   private Boolean isEligible;

   @Schema(description = "@v1.0.predictiveschedulingeligibilitybean.apimodelproperty.effectivedate.description")
   private String effectiveDate;

   @Schema(description = "@v1.0.predictiveschedulingeligibilitybean.apimodelproperty.expirationdate.description")
   private String expirationDate;

   public Boolean getIsEligible() {
      return isEligible;
   }

   public void setIsEligible(Boolean isEligible) {
      this.isEligible = isEligible;
   }

   public String getEffectiveDate() {
      return effectiveDate;
   }

   public void setEffectiveDate(String effectiveDate) {
      this.effectiveDate = effectiveDate;
   }

   public String getExpirationDate() {
      return expirationDate;
   }

   public void setExpirationDate(String expirationDate) {
      this.expirationDate = expirationDate;
   }
}
