package com.kronos.persons.rest.assignments.model;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.persons.rest.beans.HyperFindFilterBean;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(title = "personAttestationProfileAssignment", description = "@v1.0.personattestationprofileassignment.apimodel.description")
public class PersonAttestationProfileAssignment {
    @Schema(description = "@v1.0.personattestationprofileassignment.apimodelproperty.employee.description")
    private ObjectRef employee;

    @Schema(
            type = "Object",
            description = "@v1.0.personattestationprofileassignment.apimodelproperty.hyperFindFilter.description")
    private HyperFindFilterBean hyperFindFilter;

    @Schema(description = "@v1.0.personattestationprofileassignment.apimodelproperty.attestationProfileAssignments.description")
    private List<AttestationProfileAssignment> attestationProfileAssignments;

    @Schema(description = "@v1.0.personattestationprofileassignment.apimodelproperty.managerRoleAttestationProfileAssignments.description")
    private List<AttestationProfileAssignment> managerRoleAttestationProfileAssignments;

    public ObjectRef getEmployee() {
        return employee;
    }

    public void setEmployee(ObjectRef employee) {
        this.employee = employee;
    }

    public HyperFindFilterBean getHyperFindFilter() {
        return hyperFindFilter;
    }

    public void setHyperFindFilter(HyperFindFilterBean hyperFindFilter) {
        this.hyperFindFilter = hyperFindFilter;
    }

    public List<AttestationProfileAssignment> getAttestationProfileAssignments() {
        return attestationProfileAssignments;
    }

    public void setAttestationProfileAssignments(List<AttestationProfileAssignment> attestationProfileAssignments) {
        this.attestationProfileAssignments = attestationProfileAssignments;
    }

    public List<AttestationProfileAssignment> getManagerRoleAttestationProfileAssignments() {
        return managerRoleAttestationProfileAssignments;
    }

    public void setManagerRoleAttestationProfileAssignments(List<AttestationProfileAssignment> managerRoleAttestationProfileAssignments) {
        this.managerRoleAttestationProfileAssignments = managerRoleAttestationProfileAssignments;
    }
}