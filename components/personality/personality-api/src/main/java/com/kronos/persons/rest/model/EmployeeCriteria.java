package com.kronos.persons.rest.model;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
@Schema(description = "@v1.0.employeecriteria.apimodel.description", title = "employeeCriteria")
public class EmployeeCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "@v1.0.employeecriteria.apimodelproperty.key.description", allowableValues = "personid,personnumber,jobassignmentid,username,useraccountid,badgenumber,useremailaddress", requiredMode = Schema.RequiredMode.REQUIRED)
    private String key;

    @Schema(description = "@v1.0.employeecriteria.apimodelproperty.values.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> values;
    
    @Schema(description = "@v1.0.employeecriteria.apimodelproperty.multiKey.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("multi_key")
    private List<String> multiKey;
    
    @Schema(description = "@v1.0.employeecriteria.apimodelproperty.multiKeyValues.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("multi_key_values")
    private List<List<String>> multiKeyValues;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<String> getValues() {
        return values;
    }

    public void setValues(List<String> values) {
        this.values = values;
    }

    public List<String> getMultiKey() {
        return multiKey;
    }

    public void setMultiKey(List<String> multiKey) {
        this.multiKey = multiKey;
    }

    public List<List<String>> getMultiKeyValues() {
        return multiKeyValues;
    }

    public void setMultiKeyValues(List<List<String>> multiKeyValues) {
        this.multiKeyValues = multiKeyValues;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("{");
        builder.append("key:" + getKey() + ", ");
        builder.append("values:" + getValues() + "");
        builder.append("multi_key:" + getMultiKey() + ", ");
        builder.append("multi_key_values:" + getMultiKeyValues() + "");
        builder.append("}");
        return builder.toString();
    }
}
