package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.personlicensetypebean.apimodel.description", name = "personLicense")
@JsonPropertyOrder(alphabetic = true)
public class PersonLicenseTypeBean {
	@Schema(description = "@v1.0.personlicensetypebean.apimodelproperty.activeflag.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private Boolean activeFlag;

	@Schema(description = "@v1.0.personlicensetypebean.apimodelproperty.licensetypename.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String licenseTypeName;

	/**
	 * The object ID of the license type.
	 */
	@JsonIgnore
	protected ObjectIdLong licenseTypeId;

	/**
	 * @return the activeFlag
	 */
	public Boolean getActiveFlag() {
		return activeFlag;
	}

	/**
	 * @param activeFlag
	 *            the activeFlag to set
	 */
	public void setActiveFlag(Boolean activeFlag) {
		this.activeFlag = activeFlag;
	}

	/**
	 * @return the licenseTypeName
	 */
	public String getLicenseTypeName() {
		return licenseTypeName;
	}

	/**
	 * @param licenseTypeName
	 *            the licenseTypeName to set
	 */
	public void setLicenseTypeName(String licenseTypeName) {
		this.licenseTypeName = licenseTypeName;
	}

	/**
	 * @return the licenseTypeId
	 */
	public ObjectIdLong getLicenseTypeId() {
		return licenseTypeId;
	}

	/**
	 * @param licenseTypeId
	 *            the licenseTypeId to set
	 */
	public void setLicenseTypeId(ObjectIdLong licenseTypeId) {
		this.licenseTypeId = licenseTypeId;
	}

}
