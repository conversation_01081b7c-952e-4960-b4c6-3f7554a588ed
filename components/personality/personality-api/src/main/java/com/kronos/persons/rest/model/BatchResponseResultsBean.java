package com.kronos.persons.rest.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.batchresponseresultsbean.apimodel.description", name = "batchResponseResult")
public class BatchResponseResultsBean {
    @Schema(description = "@v1.0.batchresponseresultsbean.apimodelproperty.success.description")
    private ResponseBean success;

    @Schema(description = "@v1.0.batchresponseresultsbean.apimodelproperty.error.description")
	private RestErrorBean error;
	/**
	 * @return the success
	 */
	public ResponseBean getSuccess() {
		return success;
	}
	/**
	 * @param success the success to set
	 */
	public void setSuccess(ResponseBean success) {
		this.success = success;
	}
	/**
	 * @return the error
	 */
	public RestErrorBean getError() {
		return error;
	}
	/**
	 * @param error the error to set
	 */
	public void setError(RestErrorBean error) {
		this.error = error;
	}

}
