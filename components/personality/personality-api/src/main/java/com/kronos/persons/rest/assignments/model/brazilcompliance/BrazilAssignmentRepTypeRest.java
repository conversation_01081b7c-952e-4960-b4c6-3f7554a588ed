package com.kronos.persons.rest.assignments.model.brazilcompliance;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;


@Schema(description = "@v1.0.brazilassignmentreptyperest.apimodel.description", name = "brazilAssignmentRepTypeRest")
public class BrazilAssignmentRepTypeRest {

    @Schema(description = "@v1.0.brazilassignmentreptyperest.apimodelproperty.assignmentDetails.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<BrazilAssignmentRepTypeDetailRest> assignmentDetails = new ArrayList<>();

    @Schema(description = "@v1.0.brazilassignmentreptyperest.apimodelproperty.unAssignExisting.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean unAssignExisting;

    public Boolean getUnAssignExisting() {
        return unAssignExisting;
    }

    public void setUnAssignExisting(Boolean unAssignExisting) {
        this.unAssignExisting = unAssignExisting;
    }

    public List<BrazilAssignmentRepTypeDetailRest> getAssignmentDetails() {
        return assignmentDetails;
    }

    public void setAssignmentDetails(List<BrazilAssignmentRepTypeDetailRest> assignmentDetails) {
        this.assignmentDetails = assignmentDetails;
    }
}
