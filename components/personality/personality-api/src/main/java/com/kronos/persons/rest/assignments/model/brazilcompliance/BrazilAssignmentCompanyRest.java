package com.kronos.persons.rest.assignments.model.brazilcompliance;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "@v1.0.brazilassignmentcompanyrest.apimodel.description", name = "brazilAssignmentCompanyRest")
public class BrazilAssignmentCompanyRest {

    @Schema(description = "@v1.0.brazilassignmentcompanyrest.apimodelproperty.assignmentDetails.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<BrazilAssignmentCompanyDetailRest> assignmentDetails;

    @Schema(description = "@v1.0.brazilassignmentcompanyrest.apimodelproperty.unAssignExisting.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean unAssignExisting;

    public Boolean getUnAssignExisting() {
        return unAssignExisting;
    }

    public void setUnAssignExisting(Boolean unAssignExisting) {
        this.unAssignExisting = unAssignExisting;
    }

    public List<BrazilAssignmentCompanyDetailRest> getAssignmentDetails() {
        return assignmentDetails;
    }

    public void setAssignmentDetails(List<BrazilAssignmentCompanyDetailRest> assignmentDetails) {
        this.assignmentDetails = assignmentDetails;
    }
}