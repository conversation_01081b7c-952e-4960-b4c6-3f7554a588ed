/**
 * 
 */
package com.kronos.persons.rest.model;

import java.io.Serializable;
import java.time.LocalDate;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.personwherecriteria.apimodel.description", title = "personWhereCriteria")
public class PersonWhereCriteria implements Serializable {

	private static final long serialVersionUID = 1L;

	@Schema(description = "@v1.0.personwherecriteria.apimodelproperty.daterange.description",requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private DateRange dateRange;

	@Schema(description = "@v1.0.personwherecriteria.apimodelproperty.missingExternalIdType.description",allowableValues="@v1.0.personwherecriteria.apimodelproperty.missingExternalIdType.allowablevalues", requiredMode = Schema.RequiredMode.REQUIRED)
	private String missingExternalIdType;
	
	@Schema(description = "@v1.0.personwherecriteria.apimodelproperty.returnPersonIdOnly.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private boolean returnPersonIdOnly = Boolean.FALSE;
	
	@Schema(description = "@v1.0.personwherecriteria.apimodelproperty.ondate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private LocalDate snapshotDate = null;
	
	@Schema(description = "@v1.0.personwherecriteria.apimodelproperty.employmentStatus.description",allowableValues="@v1.0.personwherecriteria.apimodelproperty.employmentStatus.allowablevalues", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String employmentStatus = null;
	
	@Schema(description = "@v1.0.personwherecriteria.apimodelproperty.userAccountStatus.description",allowableValues="@v1.0.personwherecriteria.apimodelproperty.userAccountStatus.allowablevalues", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String userAccountStatus = null;

	public DateRange getDateRange() {
		return dateRange;
	}

	public void setDateRange(DateRange dateRange) {
		this.dateRange = dateRange;
	}

	public String getMissingExternalIdType() {
		return missingExternalIdType;
	}

	public void setMissingExternalIdType(String missingExternalIdType) {
		this.missingExternalIdType = missingExternalIdType;
	}

	public String getEmploymentStatus() {
		return employmentStatus;
	}

	public void setEmploymentStatus(String employmentStatus) {
		this.employmentStatus = employmentStatus;
	}

	public String getUserAccountStatus() {
		return userAccountStatus;
	}

	public void setUserAccountStatus(String userAccountStatus) {
		this.userAccountStatus = userAccountStatus;
	}
	
	public boolean getReturnPersonIdOnly() {
		return returnPersonIdOnly;
	}
	
	public void setReturnPersonIdOnly(boolean returnPersonIdOnly) {
		this.returnPersonIdOnly = returnPersonIdOnly;
	}

	public LocalDate getSnapshotDate() {
		return snapshotDate;
	}

	public void setSnapshotDate(LocalDate snapshotDate) {
		this.snapshotDate = snapshotDate;
	} 
	
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("{");
		builder.append("dateRange:" + getDateRange() + ", ");
		builder.append("}");
		return builder.toString();
	}

	
	
}
