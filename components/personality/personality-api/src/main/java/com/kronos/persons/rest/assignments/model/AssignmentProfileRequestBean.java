package com.kronos.persons.rest.assignments.model;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.persons.rest.beans.PeopleAssignmentBean;
import io.swagger.v3.oas.annotations.media.Schema;
import com.kronos.cache.api.annotations.CachedEntity;

@CachedEntity
@Schema(title = "assignmentProfileRequest", description = "@v1.0.assignmentprofilerequestbean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class AssignmentProfileRequestBean extends PeopleAssignmentBean implements BaseAssignmentBean, Serializable {

	private static final long serialVersionUID = 123456787567653456L;

	@Schema(description = "@v1.0.assignmentprofilerequestbean.apimodelproperty.assignmentprofile.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	String assignmentProfile;
	
	@JsonIgnoreProperties
	private Long profileId;

	/**
	 * @return the assignmentProfile
	 */
	public String getAssignmentProfile() {
		return assignmentProfile;
	}

	/**
	 * @param assignmentProfile
	 *            the assignmentProfile to set
	 */
	public void setAssignmentProfile(String assignmentProfile) {
		this.assignmentProfile = assignmentProfile;
	}

	public Long getProfileId() {
		return profileId;
	}

	public void setProfileId(Long profileId) {
		this.profileId = profileId;
	}
	


}
