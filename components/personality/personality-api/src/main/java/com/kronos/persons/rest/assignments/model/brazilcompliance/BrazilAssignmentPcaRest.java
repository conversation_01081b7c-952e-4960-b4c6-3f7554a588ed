package com.kronos.persons.rest.assignments.model.brazilcompliance;



import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "@v1.0.brazilassignmentpcarest.apimodel.description", name = "brazilAssignmentPcaRest")
public class BrazilAssignmentPcaRest {

    @Schema(description = "@v1.0.brazilassignmentpcarest.apimodelproperty.assignmentDetails.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<BrazilAssignmentPcaDetailRest> assignmentDetails;

    @Schema(description = "@v1.0.brazilassignmentpcarest.apimodelproperty.unAssignExisting.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean unAssignExisting;

    public Boolean getUnAssignExisting() {
        return unAssignExisting;
    }

    public void setUnAssignExisting(Boolean unAssignExisting) {
        this.unAssignExisting = unAssignExisting;
    }

    public List<BrazilAssignmentPcaDetailRest> getAssignmentDetails() {
        return assignmentDetails;
    }

    public void setAssignmentDetails(List<BrazilAssignmentPcaDetailRest> assignmentDetails) {
        this.assignmentDetails = assignmentDetails;
    }
}