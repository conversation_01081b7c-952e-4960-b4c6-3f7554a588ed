
package com.kronos.persons.rest.assignments.model.adjustmentrule;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.model.BeanWrapper;
/*
 * wrapper bean of AdjustmentRuleMultipleAssignmentBean
 * with Bean Object and APIException
 */
public class AdjustmentRuleMultipleAssignmentWrapperBean  implements
BeanWrapper<AdjustmentRuleMultipleAssignmentBean>{

    private AdjustmentRuleMultipleAssignmentBean wrapperObject;

    private APIException apiException;

    public AdjustmentRuleMultipleAssignmentWrapperBean(AdjustmentRuleMultipleAssignmentBean
                                                           employeeAdjustmentRuleAssignmentBean) {
        this.wrapperObject = employeeAdjustmentRuleAssignmentBean;
    }

   
    public APIException getApiException() {
        return apiException;
    }

   
    public void setApiException(APIException apiException) {
        this.apiException = apiException;
    }

    
    public AdjustmentRuleMultipleAssignmentBean getBean() {
        return wrapperObject;
    }

    
    public void setBean(AdjustmentRuleMultipleAssignmentBean wrapperObject) {
        this.wrapperObject = wrapperObject;
    }
}
