package com.kronos.persons.rest.model;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Schema(description = "@v1.0.lightpersoninformationsearchcriteria.apimodel.description", title = "lightPersonInformationSearchCriteria")
public class LightPersonInformationSearchCriteria implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @Schema(description = "@v1.0.lightpersoninformationsearchcriteria.apimodelproperty.index.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long index;
    
    @Schema(description = "@v1.0.lightpersoninformationsearchcriteria.apimodelproperty.count.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long count;

    @Schema(description = "@v1.0.lightpersoninformationsearchcriteria.apimodelproperty.where.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private PersonWhereCriteria where;
   
    public PersonWhereCriteria getWhere() {
        return where;
    }

    public void setWhere(PersonWhereCriteria where) {
        this.where = where;
    }
    
    public Long getIndex() {
		return index;
	}

	public void setIndex(Long index) {
		this.index = index;
	}

	public Long getCount() {
		return count;
	}

	public void setCount(Long count) {
		this.count = count;
	}

	@Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("{");
        builder.append("where:" + getWhere());
        builder.append("}");
        return builder.toString();
    }

}
