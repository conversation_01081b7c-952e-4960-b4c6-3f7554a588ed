package com.kronos.persons.rest.service;

import com.kronos.persons.rest.model.EmployeeWageWorkRulesDTO;
import com.kronos.persons.rest.model.WageWorkRuleMultiReadSearchCriteria;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import java.util.List;

/**
 * Interface intended for Employee Wage Work Rule Service.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * <p>
 * Date: March 9, 2020
 *
 * <AUTHOR> Kuksinskaya
 */
@Path(value = "/v1/commons/persons/wage_work_rules")
@Consumes(value = MediaType.APPLICATION_JSON)
@Produces(value = MediaType.APPLICATION_JSON)
@Publishable(productType = ProductTypes.WFP, type = RestAPITypes.PUBLIC, value = true)
@Tag(name = "@v1.0.commons-persons-wage_work_rules.name")
@OpenAPIDefinition(tags = {
        @Tag(description = "@v1.0.commons-persons-wage_work_rules.description",
                extensions = @Extension(properties = {
                        @ExtensionProperty(name = "parent",
                                value = "@v1.0.commons-persons-wage_work_rules.parent")}),
                name = "@v1.0.commons-persons-wage_work_rules.name")})
public interface IEmployeeWageWorkRuleService {

    /**
     * Method used to retrieve list of locations and jobs along with wage and work rules
     * for the list of specified persons, start and end date.
     *
     * @param wageWorkRuleMultiReadSearchCriteria request information
     * @return list of locations and jobs with corresponding wage and work rules for
     */
    @POST
    @Path("/multi_read")
    @Operation(summary = "@v1.0.commons-persons-wage_work_rules.post.multi_read.value",
            tags = "@v1.0.commons-persons-wage_work_rules.post.multi_read.nickname",
            description = "@v1.0.commons-persons-wage_work_rules.post.multi_read.notes")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "@v1.0.commons-persons-wage_work_rules.post.multi_read.response.200.message",
                    content = @Content(schema = @Schema(type="list", implementation = EmployeeWageWorkRulesDTO.class))),
            @ApiResponse(responseCode = "207",
                    description = "@v1.0.commons-persons-wage_work_rules.post.multi_read.response.207.message"),
            @ApiResponse(responseCode = "400",
                    description = "@v1.0.commons-persons-wage_work_rules.post.multi_read.response.400.message"),
            @ApiResponse(responseCode = "500",
                    description = "@v1.0.commons-persons-wage_work_rules.post.multi_read.response.500.message")
    })
    List<EmployeeWageWorkRulesDTO> getEmployeeWageWorkRules(@Parameter(description = "@v1.0.commons-persons-wage_work_rules.post.multi_read.payload.wageworkrulemultireadsearchcriteria.value",
            required = true) WageWorkRuleMultiReadSearchCriteria wageWorkRuleMultiReadSearchCriteria);

    /**
     * Method used to save list of position wage and work rules.
     *
     * @param employeeWageWorkRules list of position wage work rules
     * @return list of affected position wage and work rules
     */
    @POST
    @Path("/multi_upsert")
    @Operation(summary = "@v1.0.commons-persons-wage_work_rules.post.multi_upsert.value",
            tags = "@v1.0.commons-persons-wage_work_rules.post.multi_upsert.nickname",
            description = "@v1.0.commons-persons-wage_work_rules.post.multi_upsert.notes")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "@v1.0.commons-persons-wage_work_rules.post.multi_upsert.response.200.message",
                    content = @Content(schema = @Schema(type="list", implementation = EmployeeWageWorkRulesDTO.class))),
            @ApiResponse(responseCode = "207",
                    description = "@v1.0.commons-persons-wage_work_rules.post.multi_upsert.response.207.message"),
            @ApiResponse(responseCode = "400",
                    description = "@v1.0.commons-persons-wage_work_rules.post.multi_upsert.response.400.message"),
            @ApiResponse(responseCode = "409",
                    description = "@v1.0.commons-persons-wage_work_rules.post.multi_upsert.response.409.message"),
            @ApiResponse(responseCode = "500",
                    description = "@v1.0.commons-persons-wage_work_rules.post.multi_upsert.response.500.message")
    })
    List<EmployeeWageWorkRulesDTO> saveEmployeeWageWorkRules(@Parameter(description = "@v1.0.commons-persons-wage_work_rules.post.multi_upsert.payload.employeewageworkrules.value",
            required = true) List<EmployeeWageWorkRulesDTO> employeeWageWorkRules);
}
