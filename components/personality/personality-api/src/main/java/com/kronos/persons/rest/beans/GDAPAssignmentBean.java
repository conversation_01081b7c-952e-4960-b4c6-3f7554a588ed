package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.gdapassignmentbean.apimodel.description", name = "gdapAssignment")
public class GDAPAssignmentBean {
    @Schema(description = "@v1.0.gdapassignmentbean.apimodelproperty.defaultswitch.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean defaultSwitch;

    @Schema(description = "@v1.0.gdapassignmentbean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String effectiveDate;

    @Schema(description = "@v1.0.gdapassignmentbean.apimodelproperty.expirationdate.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String expirationDate;

    @Schema(description = "@v1.0.gdapassignmentbean.apimodelproperty.personidentity.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private PersonIdentityBean personIdentity;

    @Schema(description = "@v1.0.gdapassignmentbean.apimodelproperty.originaleffectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String originalEffectiveDate;

    @Schema(description = "@v1.0.gdapassignmentbean.apimodelproperty.originalexpirationdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String originalExpirationDate;

    @Schema(description = "@v1.0.gdapassignmentbean.apimodelproperty.role.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String role;

    @Schema(description = "@v1.0.gdapassignmentbean.apimodelproperty.gdapname.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String gdapName;
	
	
	/**
     * Value of role
     */
    @JsonIgnore
    protected ObjectIdLong roleId = null;
	
	/**
	 * @return the defaultSwitch
	 */
	public Boolean getDefaultSwitch() {
		return defaultSwitch;
	}
	/**
	 * @param defaultSwitch the defaultSwitch to set
	 */
	public void setDefaultSwitch(Boolean defaultSwitch) {
		this.defaultSwitch = defaultSwitch;
	}
	/**
	 * @return the effectiveDate
	 */
	public String getEffectiveDate() {
		return effectiveDate;
	}
	/**
	 * @param effectiveDate the effectiveDate to set
	 */
	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}
	/**
	 * @return the expirationDate
	 */
	public String getExpirationDate() {
		return expirationDate;
	}
	/**
	 * @param expirationDate the expirationDate to set
	 */
	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}
	/**
	 * @return the personIdentity
	 */
	public PersonIdentityBean getPersonIdentity() {
		return personIdentity;
	}
	/**
	 * @param personIdentity the personIdentity to set
	 */
	public void setPersonIdentity(PersonIdentityBean personIdentity) {
		this.personIdentity = personIdentity;
	}
	/**
	 * @return the originalEffectiveDate
	 */
	public String getOriginalEffectiveDate() {
		return originalEffectiveDate;
	}
	/**
	 * @param originalEffectiveDate the originalEffectiveDate to set
	 */
	public void setOriginalEffectiveDate(String originalEffectiveDate) {
		this.originalEffectiveDate = originalEffectiveDate;
	}
	/**
	 * @return the originalExpirationDate
	 */
	public String getOriginalExpirationDate() {
		return originalExpirationDate;
	}
	/**
	 * @param originalExpirationDate the originalExpirationDate to set
	 */
	public void setOriginalExpirationDate(String originalExpirationDate) {
		this.originalExpirationDate = originalExpirationDate;
	}
	/**
	 * @return the role
	 */
	public String getRole() {
		return role;
	}
	/**
	 * @param role the role to set
	 */
	public void setRole(String role) {
		this.role = role;
	}
	/**
	 * @return the gdapName
	 */
	public String getGdapName() {
		return gdapName;
	}
	/**
	 * @param gdapName the gdapName to set
	 */
	public void setGdapName(String gdapName) {
		this.gdapName = gdapName;
	}
	/**
	 * @return the roleId
	 */
	public ObjectIdLong getRoleId() {
		return roleId;
	}
	/**
	 * @param roleId the roleId to set
	 */
	public void setRoleId(ObjectIdLong roleId) {
		this.roleId = roleId;
	}
	
}
