package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.primarylaboraccountbean.apimodel.description", name = "primaryLaborAccount")
@JsonPropertyOrder(alphabetic = true)
public class PrimaryLaborAccountBean {
    @Schema(description = "@v1.0.primarylaboraccountbean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String effectiveDate;

    @Schema(description = "@v1.0.primarylaboraccountbean.apimodelproperty.expirationdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String expirationDate;

    @Schema(description = "@v1.0.primarylaboraccountbean.apimodelproperty.organizationpath.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String organizationPath;

    @Schema(description = "@v1.0.primarylaboraccountbean.apimodelproperty.laborcategoryname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String laborCategoryName;

    @JsonIgnore
    private ObjectIdLong organizationNodeId;

    @JsonIgnore
    private ObjectIdLong laborAccountId = new ObjectIdLong((-2));
    
    @Schema(description = "@v1.0.primarylaboraccountbean.apimodelproperty.orgpathbycriteria.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private OrgPathByCriteriaBean orgPathByCriteria;

	/**
	 * @return the effectiveDate
	 */
	public String getEffectiveDate() {
		return effectiveDate;
	}

	/**
	 * @param effectiveDate
	 *            the effectiveDate to set
	 */
	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	/**
	 * @return the expirationDate
	 */
	public String getExpirationDate() {
		return expirationDate;
	}

	/**
	 * @param expirationDate
	 *            the expirationDate to set
	 */
	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}

	/**
	 * @return the organizationPath
	 */
	public String getOrganizationPath() {
		return organizationPath;
	}

	/**
	 * @param organizationPath
	 *            the organizationPath to set
	 */
	public void setOrganizationPath(String organizationPath) {
		this.organizationPath = organizationPath;
	}

	/**
	 * @return the laborCategoryName
	 */
	public String getLaborCategoryName() {
		return laborCategoryName;
	}

	/**
	 * @param laborCategoryName
	 *            the laborCategoryName to set
	 */
	public void setLaborCategoryName(String laborCategoryName) {
		this.laborCategoryName = laborCategoryName;
	}

	/**
	 * @return the organizationNodeId
	 */
	public ObjectIdLong getOrganizationNodeId() {
		return organizationNodeId;
	}

	/**
	 * @param organizationNodeId
	 *            the organizationNodeId to set
	 */
	public void setOrganizationNodeId(ObjectIdLong organizationNodeId) {
		this.organizationNodeId = organizationNodeId;
	}

	/**
	 * @return the laborAccountId
	 */
	public ObjectIdLong getLaborAccountId() {
		return laborAccountId;
	}

	/**
	 * @param laborAccountId
	 *            the laborAccountId to set
	 */
	public void setLaborAccountId(ObjectIdLong laborAccountId) {
		this.laborAccountId = laborAccountId;
	}

	/**
	 * @return the orgPathByCriteria
	 */
	public OrgPathByCriteriaBean getOrgPathByCriteria() {
		return orgPathByCriteria;
	}

	/**
	 * @param orgPathByCriteria the orgPathByCriteria to set
	 */
	public void setOrgPathByCriteria(OrgPathByCriteriaBean orgPathByCriteria) {
		this.orgPathByCriteria = orgPathByCriteria;
	}

}
