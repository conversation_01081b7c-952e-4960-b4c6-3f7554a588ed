package com.kronos.persons.rest.assignments.model.percentageallocationrule;

import java.util.List;

import com.kronos.persons.rest.beans.PeopleAssignmentBean;

public class PercentageAllocationRuleWrapperBean extends PeopleAssignmentBean {

	List<PercentageAllocationRuleAssignmentBean> assignments;

	public List<PercentageAllocationRuleAssignmentBean> getAssignments() {
		return assignments;
	}

	public void setAssignments(List<PercentageAllocationRuleAssignmentBean> assignments) {
		this.assignments = assignments;
	}

}
