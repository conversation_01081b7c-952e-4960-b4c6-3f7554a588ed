package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.telephonenumbermissingpersonbean.apimodel.description", title = "TelephoneNumberMissingPerson")
@JsonPropertyOrder(alphabetic = true)
public class TelephoneNumberMissingPersonBean {
	@Schema(description = "@v1.0.telephonenumbermissingpersonbean.apimodelproperty.contactdata.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String contactData;

	@Schema(description = "@v1.0.telephonenumbermissingpersonbean.apimodelproperty.contacttype.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String contactType;

	public String getContactData() {
		return contactData;
	}

	public void setContactData(String contactData) {
		this.contactData = contactData;
	}

	public String getContactType() {
		return contactType;
	}

	public void setContactType(String contactType) {
		this.contactType = contactType;
	}

	
}
