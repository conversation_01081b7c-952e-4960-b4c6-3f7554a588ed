/***********************************************************************
 * IApplicationContextService.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.persons.context.service;

import com.kronos.persons.rest.beans.LightPersonInformation;
import com.kronos.persons.rest.model.LightPersonInformationSearchCriteria;
/**
 * 
 * <AUTHOR>
 *
 */
public interface IPersonAoidService {

	/**
	 * This method is used to fetch Employees without AOID
	 * @return LightPersonInformation
	 */
	public LightPersonInformation getPersonWithoutAoid(LightPersonInformationSearchCriteria criteria);

}
