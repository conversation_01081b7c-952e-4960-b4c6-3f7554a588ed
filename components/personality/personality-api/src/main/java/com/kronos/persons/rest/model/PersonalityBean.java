package com.kronos.persons.rest.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kronos.persons.rest.beans.GDAPAssignmentBean;
import com.kronos.persons.rest.beans.JobAssignmentBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.PersonInformationBean;
import com.kronos.persons.rest.beans.UserBean;
import com.kronos.persons.rest.beans.positions.PositionBean;
import com.kronos.persons.rest.supportapi.enums.PersonActionProcessEnum;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.restpersonalitybean.apimodel.description", name = "persons")
public class PersonalityBean {
	
    private PersonIdentityBean personIdentity;
    
    @Schema(description = "@v1.0.restpersonalitybean.apimodelproperty.gdapassignments.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<GDAPAssignmentBean> gdapAssignments;

    @Schema(description = "@v1.0.restpersonalitybean.apimodelproperty.personinformation.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private PersonInformationBean personInformation;

    @Schema(description = "@v1.0.restpersonalitybean.apimodelproperty.jobassignment.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private JobAssignmentBean jobAssignment;

	@Schema(description = "@v1.0.restpersonalitybean.apimodelproperty.positions.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private List<PositionBean> positions;

    @Schema(description = "@v1.0.restpersonalitybean.apimodelproperty.user.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private UserBean user;

	@JsonIgnore
	private Personality personality = null;

	@JsonIgnore
	private PersonActionProcessEnum personActionProcess;
	
	/**
	 * @return the personIdentity
	 */
	public PersonIdentityBean getPersonIdentity() {
		return personIdentity;
	}

	/**
	 * @param personIdentity the personIdentity to set
	 */
	public void setPersonIdentity(PersonIdentityBean personIdentity) {
		this.personIdentity = personIdentity;
	}

	
	/**
	 * @return the gdapAssignments
	 */
	public List<GDAPAssignmentBean> getGdapAssignments() {
		return gdapAssignments;
	}

	/**
	 * @param gdapAssignments the gdapAssignments to set
	 */
	public void setGdapAssignments(List<GDAPAssignmentBean> gdapAssignments) {
		this.gdapAssignments = gdapAssignments;
	}

	/**
	 * @return the personInformation
	 */
	public PersonInformationBean getPersonInformation() {
		return personInformation;
	}

	/**
	 * @param personInformation the personInformation to set
	 */
	public void setPersonInformation(PersonInformationBean personInformation) {
		this.personInformation = personInformation;
	}

	/**
	 * @return the jobAssignment
	 */
	public JobAssignmentBean getJobAssignment() {
		return jobAssignment;
	}

	/**
	 * @param jobAssignment the jobAssignment to set
	 */
	public void setJobAssignment(JobAssignmentBean jobAssignment) {
		this.jobAssignment = jobAssignment;
	}

	/**
	 * @return the user
	 */
	public UserBean getUser() {
		return user;
	}

	/**
	 * @param user the user to set
	 */
	public void setUser(UserBean user) {
		this.user = user;
	}

	/**
	 * @return the personality
	 */
	public Personality getPersonality() {
		return personality;
	}

	/**
	 * @param personality the personality to set
	 */
	public void setPersonality(Personality personality) {
		this.personality = personality;
	}

	/**
	 * @return the positions
	 */
	public List<PositionBean> getPositions() {
		return positions;
	}

	/**
	 * @param positions the positions to set
	 */
	public void setPositions(List<PositionBean> positions) {
		this.positions = positions;
	}

	public PersonActionProcessEnum getPersonActionProcess() {
		return personActionProcess;
	}

	public void setPersonActionProcess(PersonActionProcessEnum personActionProcess) {
		this.personActionProcess = personActionProcess;
	}

}
