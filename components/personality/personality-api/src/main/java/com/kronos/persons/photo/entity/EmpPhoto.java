package com.kronos.persons.photo.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import org.hibernate.annotations.Type;

@Entity
@Table(name = "empphoto")
public class EmpPhoto implements Serializable{
	
	private static final long serialVersionUID = 101L;

	@Id
	@Column(name = "personid")
	private Long personid;
	

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updatedtm")
    private LocalDateTime updatedtm;
	
	@Column(name = "capturedtm")
	@Temporal(TemporalType.TIMESTAMP)
    private LocalDateTime capturedtm;
	
	@Column(name = "datasourceid")
	private Long dataSourceId;
	
	@Basic(fetch = FetchType.LAZY, optional = true)
	@Column(name = "photocontentobjtxt")
	private byte[] imageContent;
	
	@Column(name = "deletedsw", nullable = false, precision = 12, scale = 0)
	private boolean deletedsw;

	public Long getPersonid() {
		return personid;
	}

	public void setPersonid(Long personid) {
		this.personid = personid;
	}

	public LocalDateTime getUpdatedtm() {
		return updatedtm;
	}

	public void setUpdatedtm(LocalDateTime updatedtm) {
		this.updatedtm = updatedtm;
	}

	public LocalDateTime getCapturedtm() {
		return capturedtm;
	}

	public void setCapturedtm(LocalDateTime capturedtm) {
		this.capturedtm = capturedtm;
	}

	public Long getDataSourceId() {
		return dataSourceId;
	}

	public void setDataSourceId(Long dataSourceId) {
		this.dataSourceId = dataSourceId;
	}

	public byte[] getImageContent() {
		return imageContent;
	}

	public void setImageContent(byte[] imageContent) {
		this.imageContent = imageContent;
	}

	public boolean isDeletedsw() {
		return deletedsw;
	}

	public void setDeletedsw(boolean deletedsw) {
		this.deletedsw = deletedsw;
	}

}
