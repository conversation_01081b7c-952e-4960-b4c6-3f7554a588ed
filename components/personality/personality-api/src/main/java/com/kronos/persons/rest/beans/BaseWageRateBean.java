package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Optional;
@Schema(description = "@v1.0.basewageratebean.apimodel.description", name = "baseWageRate")
@JsonPropertyOrder(alphabetic = true)
public class BaseWageRateBean {
    @Schema(description = "@v1.0.basewageratebean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String effectiveDate;

    @Schema(description = "@v1.0.basewageratebean.apimodelproperty.expirationdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String expirationDate;

    @Schema(description = "@v1.0.basewageratebean.apimodelproperty.hourlyrate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Double hourlyRate;

	public String getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public String getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}

	public Double getHourlyRate() {
		return hourlyRate;
	}

	public void setHourlyRate(Double hourlyRate) {
		this.hourlyRate = Optional.ofNullable(hourlyRate)
				.filter(rate -> !rate.isNaN())
				.filter(rate -> !rate.isInfinite())
				.orElse(0.0) ;
	}

	@Override
	public String toString() {
		return "BaseWageRateBean [effectiveDate=" + effectiveDate
				+ ", expirationDate=" + expirationDate + ", hourlyRate="
				+ hourlyRate + "]";
	}
}
