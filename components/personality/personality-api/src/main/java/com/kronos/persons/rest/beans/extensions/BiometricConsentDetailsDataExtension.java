package com.kronos.persons.rest.beans.extensions;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "@v1.0.biometricconsentdetails.apimodel.description", name = "biometricConsentDetails")
public class BiometricConsentDetailsDataExtension implements Serializable {

    public static final long serialVersionUID = 1L;

    @Schema(description = "@v1.0.biometricconsentdetails.apimodelproperty.consentstatus.description")
    private String consentStatus;
    @Schema(description = "@v1.0.biometricconsentdetails.apimodelproperty.consentdatetime.description")
    private LocalDateTime consentDateTime;
    @Schema(description = "@v1.0.biometricconsentdetails.apimodelproperty.consentlocation.description")
    private String consentLocation;
    @Schema(description = "@v1.0.biometricconsentdetails.apimodelproperty.consentform.description")
    private String consentForm;
    @Schema(description = "@v1.0.biometricconsentdetails.apimodelproperty.consenttext.description")
    private String consentText;

    public String getConsentStatus() {
        return consentStatus;
    }

    public void setConsentStatus(String consentStatus) {
        this.consentStatus = consentStatus;
    }

    public LocalDateTime getConsentDateTime() {
        return consentDateTime;
    }

    public void setConsentDateTime(LocalDateTime consentDateTime) {
        this.consentDateTime = consentDateTime;
    }

    public String getConsentLocation() {
        return consentLocation;
    }

    public void setConsentLocation(String consentLocation) {
        this.consentLocation = consentLocation;
    }

    public String getConsentForm() {
        return consentForm;
    }

    public void setConsentForm(String consentForm) {
        this.consentForm = consentForm;
    }

    public String getConsentText() {
        return consentText;
    }

    public void setConsentText(String consentText) {
        this.consentText = consentText;
    }
}
