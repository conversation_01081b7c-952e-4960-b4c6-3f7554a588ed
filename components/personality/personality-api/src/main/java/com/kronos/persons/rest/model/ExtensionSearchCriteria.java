package com.kronos.persons.rest.model;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
@Schema(description = "@v1.0.extensionsearchcriteria.apimodel.description", title = "extensionSearchCriteria")
public class ExtensionSearchCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "@v1.0.extensionsearchcriteria.apimodelproperty.where.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private ExtensionWhereCriteria where;

    @Schema(description = "@v1.0.extensionwherecriteria.apimodelproperty.returnUnassignedEmployees.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean returnUnassignedEmployees = false;
    
    @Schema(description = "@v1.0.extensionwherecriteria.apimodelproperty.failOnNoAssignment.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean failOnNoAssignment = true;

    public ExtensionSearchCriteria() {
        this.where = new ExtensionWhereCriteria();
    }

    public ExtensionWhereCriteria getWhere() {
        return where;
    }

    public void setWhere(ExtensionWhereCriteria where) {
        this.where = where;
    }

    public Boolean getReturnUnassignedEmployees() {
        return returnUnassignedEmployees;
    }

    public void setReturnUnassignedEmployees(Boolean returnUnassignedEmployees) {
        this.returnUnassignedEmployees = returnUnassignedEmployees;
    }

    public Boolean getFailOnNoAssignment() {
		return failOnNoAssignment;
	}

	public void setFailOnNoAssignment(Boolean failOnNoAssignment) {
		this.failOnNoAssignment = failOnNoAssignment;
	}

	@Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("{");
        builder.append("where:" + getWhere() + ", ");
        builder.append("returnUnassignedEmployees:" + getReturnUnassignedEmployees() + " ");
        builder.append("}");
        return builder.toString();
    }

}
