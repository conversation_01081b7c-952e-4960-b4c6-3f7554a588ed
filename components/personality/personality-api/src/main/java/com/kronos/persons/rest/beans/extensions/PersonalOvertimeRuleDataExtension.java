package com.kronos.persons.rest.beans.extensions;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Collection;


@Schema(description = "@v1.0.personalovertimeruledataextension.apimodel.description", name = "personalOvertimeRule")
public class PersonalOvertimeRuleDataExtension implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "@v1.0.personalovertimeruledataextension.apimodelproperty.personalovertimelimits.description")
    private Collection<PersonalOvertimeLimitDataExtension> personalOvertimeLimits;

    @Schema(description = "@v1.0.personalovertimeruledataextension.apimodelproperty.personalovertimeruledisplayname.description")
    private String personalOvertimeRuleDisplayName;

    @Schema(description = "@v1.0.personalovertimeruledataextension.apimodelproperty.usescheduleflag.description")
	private Boolean useScheduleFlag;

	/**
	 * @return the personalOvertimeLimits
	 */
	public Collection<PersonalOvertimeLimitDataExtension> getPersonalOvertimeLimits() {
		return personalOvertimeLimits;
	}

	/**
	 * @param personalOvertimeLimits
	 *            the personalOvertimeLimits to set
	 */
	public void setPersonalOvertimeLimits(Collection<PersonalOvertimeLimitDataExtension> personalOvertimeLimits) {
		this.personalOvertimeLimits = personalOvertimeLimits;
	}

	/**
	 * @return the personalOvertimeRuleDisplayName
	 */
	public String getPersonalOvertimeRuleDisplayName() {
		return personalOvertimeRuleDisplayName;
	}

	/**
	 * @param personalOvertimeRuleDisplayName
	 *            the personalOvertimeRuleDisplayName to set
	 */
	public void setPersonalOvertimeRuleDisplayName(String personalOvertimeRuleDisplayName) {
		this.personalOvertimeRuleDisplayName = personalOvertimeRuleDisplayName;
	}

	/**
	 * @return the useScheduleFlag
	 */
	public Boolean getUseScheduleFlag() {
		return useScheduleFlag;
	}

	/**
	 * @param useScheduleFlag
	 *            the useScheduleFlag to set
	 */
	public void setUseScheduleFlag(Boolean useScheduleFlag) {
		this.useScheduleFlag = useScheduleFlag;
	}

}
