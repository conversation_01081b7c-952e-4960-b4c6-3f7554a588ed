package com.kronos.persons.rest.beans;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.io.Serializable;

@Schema(description = "@v1.0.assignmentspanbean.apimodel.description", name = "assignmentSpan")
public class AssignmentSpanBean implements Serializable {

    @Schema(description = "@v1.0.assignmentspanbean.apimodelproperty.start.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String start;

    @Schema(description = "@v1.0.assignmentspanbean.apimodelproperty.end.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String end;

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        AssignmentSpanBean that = (AssignmentSpanBean) o;

        return new EqualsBuilder().append(start, that.start).append(end, that.end).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(start).append(end).toHashCode();
    }

    @Override
    public String toString() {
        return "AssignmentSpanBean {" +
                "start='" + start + '\'' +
                ", end='" + end + '\'' +
                '}';
    }
}
