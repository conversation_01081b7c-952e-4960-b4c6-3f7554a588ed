package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;

public interface IPersonGroupBean {

    /**
     * @return the name
     */
    String getName();

    /**
     * @param name the name to set
     */
    void setName(String name);

    /**
     * @return the startDate
     */
    String getStartDate();

    /**
     * @param startDate the startDate to set
     */
    void setStartDate(String startDate);

    /**
     * @return the endDate
     */
    String getEndDate();

    /**
     * @param endDate the endDate to set
     */
    void setEndDate(String endDate);

    /**
     * @return the originalStartDate
     */
    String getOriginalStartDate();

    /**
     * @param originalStartDate the originalStartDate to set
     */
    void setOriginalStartDate(String originalStartDate);

    /**
     * @return the actionType
     */
    ActionType getActionType();

    /**
     * @param actionType the actionType to set
     */
    void setActionType(ActionType actionType);

    @JsonIgnore
    default Boolean getRemoveFromOtherGroups() {
        return false;
    }

    default void setRemoveFromOtherGroups(Boolean removeFromOtherGroups){

    }

    @JsonIgnore
    default Boolean isAssignmentSpanCutLogicExecuted() {
        return false;
    }

    default void setAssignmentSpanCutLogicExecuted(Boolean assignmentSpanCutLogicExecuted) {

    }
}
