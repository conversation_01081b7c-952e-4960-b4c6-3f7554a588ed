package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.fulltimeequivalencybean.apimodel.description", name = "fullTimeEquivalencyDetail")
@JsonPropertyOrder(alphabetic = true)
public class FullTimeEquivalencyBean {
    @Schema(description = "@v1.0.fulltimeequivalencybean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String effectiveDate;

    @Schema(description = "@v1.0.fulltimeequivalencybean.apimodelproperty.employeestandardhours.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Double employeeStandardHours;

    @Schema(description = "@v1.0.fulltimeequivalencybean.apimodelproperty.fulltimepercentage.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Double fullTimePercentage;

    @Schema(description = "@v1.0.fulltimeequivalencybean.apimodelproperty.fulltimestandardhours.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Double fullTimeStandardHours;

    @Schema(description = "@v1.0.fulltimeequivalencybean.apimodelproperty.expirationdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String expirationDate;

	/**
	 * @return the effectiveDate
	 */
	public String getEffectiveDate() {
		return effectiveDate;
	}

	/**
	 * @param effectiveDate
	 *            the effectiveDate to set
	 */
	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	/**
	 * @return the employeeStandardHours
	 */
	public Double getEmployeeStandardHours() {
		return employeeStandardHours;
	}

	/**
	 * @param employeeStandardHours
	 *            the employeeStandardHours to set
	 */
	public void setEmployeeStandardHours(Double employeeStandardHours) {
		this.employeeStandardHours = employeeStandardHours;
	}

	/**
	 * @return the fullTimePercentage
	 */
	public Double getFullTimePercentage() {
		return fullTimePercentage;
	}

	/**
	 * @param fullTimePercentage
	 *            the fullTimePercentage to set
	 */
	public void setFullTimePercentage(Double fullTimePercentage) {
		this.fullTimePercentage = fullTimePercentage;
	}

	/**
	 * @return the fullTimeStandardHours
	 */
	public Double getFullTimeStandardHours() {
		return fullTimeStandardHours;
	}

	/**
	 * @param fullTimeStandardHours
	 *            the fullTimeStandardHours to set
	 */
	public void setFullTimeStandardHours(Double fullTimeStandardHours) {
		this.fullTimeStandardHours = fullTimeStandardHours;
	}

	/**
	 * @return the expirationDate
	 */
	public String getExpirationDate() {
		return expirationDate;
	}

	/**
	 * @param expirationDate the expirationDate to set
	 */
	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}
	
}
