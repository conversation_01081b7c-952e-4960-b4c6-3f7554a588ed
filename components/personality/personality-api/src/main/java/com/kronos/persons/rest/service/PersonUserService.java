/***********************************************************************
 * PersonUserService.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.*
 * **********************************************************************/
package com.kronos.persons.rest.service;

import com.kronos.persons.context.service.ICurrentUser;
import com.kronos.persons.rest.model.UserFeatures;

/**
 * This is the interface for person user service.
 * <AUTHOR>
 *
 */
public interface PersonUserService {
	
	 /**
     * Return the details of current user
     * @return currentUser instance
     */
    public ICurrentUser getUserDetails();
    
    /**
     * Returns whether the photo of user is enabled
     * @return boolean value
     */
    public Boolean isPhotosEnabled();
    
    
    /**
     * Returns the user PhotoId.
     * @return String value
     */
    public String getUserPhotoId();

    

	/**
	 * This method returns the SystemConfiguration information.
	 * @return ISystemConfiguration instance
	 */
	public ISystemConfiguration getSystemConfiguration();
	
	/**
	 * This method returns the UserFeatures information.
	 * @return UserFeatures instance
	 */
	public UserFeatures getUserFeatures();

	/**
	 * This method returns the Offline timeout.
	 * @return Integer value
	 */
	public Integer getOfflineTimeout();

}
