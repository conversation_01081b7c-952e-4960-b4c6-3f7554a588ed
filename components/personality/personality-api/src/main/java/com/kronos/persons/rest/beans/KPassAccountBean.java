/**
 * 
 */
package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.kpassaccountbean.apimodel.description", name = "kPassAccount")
@JsonPropertyOrder(alphabetic = true)
public class KPassAccountBean {
    @Schema(description = "@v1.0.kpassaccountbean.apimodelproperty.isrestricteduser.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean isRestrictedUser;

    @Schema(description = "@v1.0.kpassaccountbean.apimodelproperty.learningpath.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String learningPath;

	/**
	 * @return the isRestrictedUser
	 */
	public Boolean getIsRestrictedUser() {
		return isRestrictedUser;
	}

	/**
	 * @param isRestrictedUser
	 *            the isRestrictedUser to set
	 */
	public void setIsRestrictedUser(Boolean isRestrictedUser) {
		this.isRestrictedUser = isRestrictedUser;
	}

	/**
	 * @return the learningPath
	 */
	public String getLearningPath() {
		return learningPath;
	}

	/**
	 * @param learningPath
	 *            the learningPath to set
	 */
	public void setLearningPath(String learningPath) {
		this.learningPath = learningPath;
	}

}
