package com.kronos.persons.supportapi.dto;

import java.util.ArrayList;
import java.util.List;

public class OnDemandPrimingRequest {
    private List<String> tenantShortNames = new ArrayList<>();

    private String trxID;

    public String getTrxID() {
        return trxID;
    }

    public void setTrxID(String trxID) {
        this.trxID = trxID;
    }

    public List<String> getTenantShortNames() {
        return tenantShortNames;
    }

    public void setTenantShortNames(List<String> tenantShortNames) {
        this.tenantShortNames = tenantShortNames;
    }

    @Override
    public String toString() {
        return "OnDemandPrimingRequest [tenantShortNames=" + tenantShortNames + ", trxID=" + trxID + "]";
    }
}
