/*
 * *****************************************************************************
 * Copyright (c) 2020 Kronos, Inc. All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * Kronos, Inc. ("Confidential Information").  You shall not
 * Copyright (c) 2020 Kronos Inc. All Rights Reserved.
 *
 * KRONOS MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE
 * SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
 * PURPOSE, OR NON-INFRINGEMENT. KRONOS SHALL NOT BE LIABLE FOR ANY DAMAGES
 * SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING
 * THIS SOFTWARE OR ITS DERIVATIVES.
 ****************************************************************************
 *
 **/
package com.kronos.persons.supportapi.dto;

/**
 * This class is used to store status of a person for caching related operation
 * <AUTHOR>
 */
public class PersonDetail {
	
	String personNum;
	
	String personID;
	
	String status;

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getPersonNum() {
		return personNum;
	}

	public void setPersonNum(String personNum) {
		this.personNum = personNum;
	}

	public String getPersonID() {
		return personID;
	}

	public void setPersonID(String personID) {
		this.personID = personID;
	}
	
	@Override
	public String toString() {
		return "PersonDetail[personNum=" + personNum + ", personID="
				+ personID + ", status="+ status +"]";
	}
	
	

}
