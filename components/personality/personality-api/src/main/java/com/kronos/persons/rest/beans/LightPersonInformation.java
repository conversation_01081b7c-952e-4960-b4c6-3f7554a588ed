package com.kronos.persons.rest.beans;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.lightpersoninformationbean.apimodel.description", title = "lightPersonInformationBean")

public class LightPersonInformation{
	
    @Schema(description = "@v1.0.lightpersoninformationbean.apimodelproperty.totalElements.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String totalElements;
    
    @Schema(description = "@v1.0.lightpersoninformationbean.apimodelproperty.records.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Record> records= null;

	public String getTotalElements() {
		return totalElements;
	}

	public void setTotalElements(String totalElements) {
		this.totalElements = totalElements;
	}

	public List<Record> getRecords() {
		return records;
	}

	public void setRecords(List<Record> records) {
		this.records = records;
	}
	

	
    
    
    
}
