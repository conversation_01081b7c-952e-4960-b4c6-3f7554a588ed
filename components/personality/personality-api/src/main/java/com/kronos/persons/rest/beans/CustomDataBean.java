package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.customdatabean.apimodel.description", name = "customData")
@JsonPropertyOrder(alphabetic = true)
public class CustomDataBean {
    @Schema(description = "@v1.0.customdatabean.apimodelproperty.customdatatypename.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customDataTypeName;

    @Schema(description = "@v1.0.customdatabean.apimodelproperty.text.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String text;

	/**
	 * The object ID of the custom data type.
	 */
    @JsonIgnore
	protected ObjectIdLong customDataTypeId = null;

	public String getCustomDataTypeName() {
		return customDataTypeName;
	}

	public void setCustomDataTypeName(String customDataTypeName) {
		this.customDataTypeName = customDataTypeName;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public ObjectIdLong getCustomDataTypeId() {
		return customDataTypeId;
	}

	public void setCustomDataTypeId(ObjectIdLong customDataTypeId) {
		this.customDataTypeId = customDataTypeId;
	}
}
