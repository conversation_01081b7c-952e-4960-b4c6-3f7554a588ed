package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.usercurrencyassignmentbean.apimodel.description", name = "UserCurrencyAssignment")
@JsonPropertyOrder(alphabetic = true)
@JsonIgnoreProperties(ignoreUnknown = true, 
value = {"currencyLocale"})
public class UserCurrencyAssignmentBean {
    @Schema(description = "@v1.0.usercurrencyassignmentbean.apimodelproperty.currencycode.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String currencyCode;

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	@Override
	public String toString() {
		return "UserCurrencyAssignmentBean [currencyCode=" + currencyCode + "]";
	}
}
