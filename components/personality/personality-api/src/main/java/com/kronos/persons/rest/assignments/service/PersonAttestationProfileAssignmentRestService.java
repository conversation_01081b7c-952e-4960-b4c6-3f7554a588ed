package com.kronos.persons.rest.assignments.service;

import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignmentDTO;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Tag(name = "@v1.0.commons-persons-attestation_profile_assignments.name")
@OpenAPIDefinition(tags = {
    @Tag(
        description = "@v1.0.commons-persons-attestation_profile_assignments.description",
        extensions = @Extension(properties = {
            @ExtensionProperty(name = "parent", value = "@root.personassignments.default")
        }),
        name = "@v1.0.commons-persons-attestation_profile_assignments.name"
    )
})
@Publishable(type = RestAPITypes.PUBLIC, value = true, productType = ProductTypes.WTK)
@Path("/v1/commons/persons")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface PersonAttestationProfileAssignmentRestService {

    @Operation(
        tags = "@v1.0.commons-persons-attestation_profile_assignments.get.currentpath.nickname",
        description = "@v1.0.commons-persons-attestation_profile_assignments.get.currentpath.notes",
        summary = "@v1.0.commons-persons-attestation_profile_assignments.get.currentpath.summary"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-attestation_profile_assignments.get.currentpath.response.200.message", content = @Content(schema = @Schema(type="list", implementation = PersonAttestationProfileAssignmentDTO.class))),
        @ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-attestation_profile_assignments.get.currentpath.response.400.message"),
        @ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-attestation_profile_assignments.get.currentpath.response.500.message")
    })
    @GET
    @Path("/{person_id}/attestation_profile_assignments")
    Response retrieveByPersonId(
        @Parameter(description = "@v1.0.commons-persons-attestation_profile_assignments.get.currentpath.pathparam.personid.value", required = true)
        @PathParam("person_id") Long personId,
        @Parameter(description = "@v1.0.commons-persons-attestation_profile_assignments.get.currentpath.queryparam.assignToManagerRole.value")
        @QueryParam("assignToManagerRole") boolean isAssignToManagerRole);

    @Operation(
        tags = "@v1.0.commons-persons-attestation_profile_assignments.post.currentpath.nickname",
        description = "@v1.0.commons-persons-attestation_profile_assignments.post.currentpath.notes",
        summary = "@v1.0.commons-persons-attestation_profile_assignments.post.currentpath.summary"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "@v1.0.commons-persons-attestation_profile_assignments.post.currentpath.response.201.message", content = @Content(schema = @Schema(implementation = PersonAttestationProfileAssignmentDTO.class))),
        @ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-attestation_profile_assignments.post.currentpath.response.400.message"),
        @ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-attestation_profile_assignments.post.currentpath.response.500.message")})
    @POST
    @Path("/{person_id}/attestation_profile_assignments")
    Response create(
        @Parameter(description = "@v1.0.commons-persons-attestation_profile_assignments.post.currentpath.pathparam.personid.value", required = true)
        @PathParam("person_id") Long personId,
        @Parameter(description = "@v1.0.commons-persons-attestation_profile_assignments.post.currentpath.dto.value", required = true) PersonAttestationProfileAssignmentDTO dto);
}
