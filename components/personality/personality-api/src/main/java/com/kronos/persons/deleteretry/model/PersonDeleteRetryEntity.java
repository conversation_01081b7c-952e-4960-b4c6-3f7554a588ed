package com.kronos.persons.deleteretry.model;

import java.math.BigInteger;
import java.sql.Timestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;

@Entity
@Table(name = "person_delete_retry")
public class PersonDeleteRetryEntity {

	@Id
	@NotNull
	@Column(name = "personid")
	private BigInteger personId;

	@Column(name = "retryCount")
	private Integer retryCount;
	
	@Column(name = "lastUpdatedtm")
	private Timestamp lastUpdatedtm;
	
	@Column(name = "isPersonDeleted")
	private Integer isPersonDeleted;
	
	@Column(name = "deletedBy")
	private String deletedBy;
	
	public String getDeletedBy() {
		return deletedBy;
	}

	public void setDeletedBy(String deletedBy) {
		this.deletedBy = deletedBy;
	}

	public Integer getIsPersonDeleted() {
		return isPersonDeleted;
	}

	public void setIsPersonDeleted(Integer isPersonDeleted) {
		this.isPersonDeleted = isPersonDeleted;
	}

	public Timestamp getLastUpdatedtm() {
		return lastUpdatedtm;
	}

	public void setLastUpdatedtm(Timestamp lastUpdatedtm) {
		this.lastUpdatedtm = lastUpdatedtm;
	}
	
	public BigInteger getPersonId() {
		return personId;
	}

	public void setPersonId(BigInteger personId) {
		this.personId = personId;
	}

	public Integer getRetryCount() {
		return retryCount;
	}

	public void setRetryCount(Integer retryCount) {
		this.retryCount = retryCount;
	}

}
