/***********************************************************************
 * SimpleTimeZone.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.persons.context.model;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * This {@code SimpleTimeZone} defines the timezone properties.
 * <AUTHOR>
 *
 */
public class SimpleTimeZone {
	private String timeZoneId;
	private String shortName;
	private String displayName;
	private int gmtOffset;
	private String dateFormat;
	
	/**
	 * Default constructor
	 */
	public SimpleTimeZone() {
	}
	/**
	 * @return the timeZoneId
	 */
	public String getTimeZoneId() {
		return timeZoneId;
	}
	/**
	 * @param timeZoneId the timeZoneId to set
	 */
	public void setTimeZoneId(String timeZoneId) {
		this.timeZoneId = timeZoneId;
	}
	/**
	 * @return the shortName
	 */
	public String getShortName() {
		return shortName;
	}
	/**
	 * @param shortName the shortName to set
	 */
	public void setShortName(String shortName) {
		this.shortName = shortName;
	}
	/**
	 * @return the displayName
	 */
	public String getDisplayName() {
		return displayName;
	}
	/**
	 * @param displayName the displayName to set
	 */
	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}
	/**
	 * @return the gmtOffset
	 */
	public int getGmtOffset() {
		return gmtOffset;
	}
	/**
	 * @param gmtOffset the gmtOffset to set
	 */
	public void setGmtOffset(int gmtOffset) {
		this.gmtOffset = gmtOffset;
	}
	/**
	 * @return the dateFormat
	 */
	public String getDateFormat() {
		return dateFormat;
	}
	/**
	 * @param dateFormat the dateFormat to set
	 */
	public void setDateFormat(String dateFormat) {
		this.dateFormat = dateFormat;
	}

	@Override
	public boolean equals(Object object) {
		if(this == object) return true;
		if(!(object instanceof SimpleTimeZone)) return false;

		SimpleTimeZone objectItem = (SimpleTimeZone) object;

		return new EqualsBuilder()
				.append(getTimeZoneId(), objectItem.getTimeZoneId())
				.append(getDateFormat(), objectItem.getDateFormat())
				.append(getDisplayName(), objectItem.getDisplayName())
				.append(getGmtOffset(), objectItem.getGmtOffset())
				.append(getShortName(), objectItem.getShortName())
				.append(hashCode(), objectItem.hashCode())
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder()
				.append(getTimeZoneId())
				.append(getDateFormat())
				.append(getDisplayName())
				.append(getGmtOffset())
				.append(getShortName())
				.hashCode();
	}

}
