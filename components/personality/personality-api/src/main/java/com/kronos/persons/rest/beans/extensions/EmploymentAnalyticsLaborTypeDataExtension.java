/***********************************************************************
 * EmploymentAnalyticsLaborTypeEntry.java
 *
 * Copyright 2019, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.persons.rest.beans.extensions;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;


/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.analyticslabortypedataextension.apimodel.description", name = "analyticslabortype", allOf = ExtensionEffectiveDatedEntry.class)
@JsonPropertyOrder(alphabetic = true)
public class EmploymentAnalyticsLaborTypeDataExtension extends ExtensionEffectiveDatedEntry {
    public static final long serialVersionUID = 1L;

    @Schema(description = "@v1.0.analyticslabortypedataextension.apimodelproperty.analyticslabortype.description")
    private String analyticsLaborType;

    @Schema(description = "@v1.0.analyticslabortypedataextension.apimodelproperty.analyticslabortypeid.description")
	private Long analyticsLaborTypeId;

    
	public String getAnalyticsLaborType() {
		return analyticsLaborType;
	}

	public void setAnalyticsLaborType(String analyticslaborType) {
		this.analyticsLaborType = analyticslaborType;
	}

	public Long getAnalyticsLaborTypeId() {
		return analyticsLaborTypeId;
	}

	public void setAnalyticsLaborTypeId(Long analyticsLaborTypeId) {
		this.analyticsLaborTypeId = analyticsLaborTypeId;
	}
	
	/**
	 * {@inheritDoc}
	 */
	@Override
	public int hashCode() {
		return new HashCodeBuilder().append(getEffectiveDate()).append(getExpirationDate()).append(getAnalyticsLaborTypeId()).toHashCode();
	}
	
	/**
	 * {@inheritDoc}
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null || getClass() != obj.getClass())
			return false;
		EmploymentAnalyticsLaborTypeDataExtension otherAnalyticLaborTypeExtension = (EmploymentAnalyticsLaborTypeDataExtension) obj;
		return new EqualsBuilder().append(getAnalyticsLaborTypeId(), otherAnalyticLaborTypeExtension.getAnalyticsLaborTypeId())
				                  .append(getEffectiveDate(), otherAnalyticLaborTypeExtension.getEffectiveDate())
				                  .append(getExpirationDate(), otherAnalyticLaborTypeExtension.getExpirationDate()).isEquals();
	}
}