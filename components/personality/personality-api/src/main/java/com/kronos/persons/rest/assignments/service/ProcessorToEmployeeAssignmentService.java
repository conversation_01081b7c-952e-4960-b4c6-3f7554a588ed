package com.kronos.persons.rest.assignments.service;

import com.kronos.persons.rest.assignments.model.ProcessorToEmployeeAssignmentDTO;
import com.kronos.persons.rest.assignments.model.ProcessorToEmployeeCriteriaDTO;
import com.kronos.wfc.commonapp.people.business.personality.Personality;

import java.util.List;

public interface ProcessorToEmployeeAssignmentService {

    Personality createProcessorToEmployeeAssignment(ProcessorToEmployeeAssignmentDTO assignmentBean);

    Personality updateProcessorToEmployeeAssignment(ProcessorToEmployeeAssignmentDTO assignmentBean);

    List<ProcessorToEmployeeAssignmentDTO> getProcessorToEmployeeAssignmentsByParams(Long personId, String personNumber, String processor, String effectivedate);

    Personality deleteProcessorToEmployeeAssignment(ProcessorToEmployeeCriteriaDTO criteriaBean);

}