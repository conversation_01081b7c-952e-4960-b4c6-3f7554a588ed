package com.kronos.persons.rest.exception;

import com.kronos.container.api.exception.APIException;

/**
 * 
 * <AUTHOR>
 *
 */
public class PrsnPersistenceException {
	
  private static final String PROPERTY_NAME = "propertyName";
  private static final String PROPERTY_VALUE = "propertyValue";
  private static final String BEAN_NAME = "beanName";
  private static final String BEAN_KEY = "beanKey";

  public static APIException personNotFound(String propertyName, String propertyValue) {
	  APIException apiEx = new APIException(ExceptionConstants.PERSON_NOT_FOUND);
	  apiEx.addUserParameter(PROPERTY_NAME, propertyName);
	  apiEx.addUserParameter(PROPERTY_VALUE, propertyValue);
	   return apiEx;
	}

	public static APIException personNotFoundWith404(String propertyName, String propertyValue) {
		APIException apiEx = new APIException(ExceptionConstants.PERSON_NOT_FOUND_HTTP_404);
		apiEx.addUserParameter(PROPERTY_NAME, propertyName);
		apiEx.addUserParameter(PROPERTY_VALUE, propertyValue);
		return apiEx;
	}

	public static APIException personNotManager(String beanKey) {
	   APIException apiEx = new APIException(ExceptionConstants.PERSON_NOT_MANAGER);
	  apiEx.addUserParameter(BEAN_KEY, beanKey);
	   return apiEx;
	 }  
  
  public static APIException personNotVisible(String managerName, String beanKey) {
	   APIException apiEx = new APIException(ExceptionConstants.PERSON_NOT_VISIBLE);
	   apiEx.addUserParameter(BEAN_NAME, managerName);
	   apiEx.addUserParameter(BEAN_KEY, beanKey);
	   return apiEx;
	 }  
	
  public static APIException noAccessToPerson(String propertyName, String propertyValue) {
	   APIException apiEx = new APIException(ExceptionConstants.NO_ACCESS_TO_PERSON);
	   apiEx.addUserParameter(PROPERTY_NAME, propertyName);
	   apiEx.addUserParameter(PROPERTY_VALUE, propertyValue);
	   return apiEx;
	 }
  
  public static APIException personNotFoundForEmpPhoto(String propertyName, String propertyValue) {
	  APIException apiEx = new APIException(ExceptionConstants.PERSON_NOT_FOUND_FOR_PHOTO);
	  apiEx.addUserParameter(PROPERTY_NAME, propertyName);
	  apiEx.addUserParameter(PROPERTY_VALUE, propertyValue);
	   return apiEx;
	}
}
