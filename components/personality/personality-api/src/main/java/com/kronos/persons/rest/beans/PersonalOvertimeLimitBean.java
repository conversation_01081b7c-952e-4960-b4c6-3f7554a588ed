package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.personalovertimelimitbean.apimodel.description", name =  "personalOvertimeLimitDetails")
@JsonPropertyOrder(alphabetic = true)
public class PersonalOvertimeLimitBean {
    @Schema(description =  "@v1.0.personalovertimelimitbean.apimodelproperty.personalovertimeamounttypename.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String personalOvertimeAmountTypeName;

    @Schema(description =  "@v1.0.personalovertimelimitbean.apimodelproperty.amount.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String amount;

    // amount in secs
    @Schema(description =  "@v1.0.personalovertimelimitbean.apimodelproperty.minimumamount.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String minimumAmount; // amount in secs

    @JsonIgnore
	private ObjectIdLong personalOvertimeAmountTypeId = ObjectIdLong.NULL;

	/**
	 * @return the personalOvertimeAmountTypeName
	 */
	public String getPersonalOvertimeAmountTypeName() {
		return personalOvertimeAmountTypeName;
	}

	/**
	 * @param personalOvertimeAmountTypeName
	 *            the personalOvertimeAmountTypeName to set
	 */
	public void setPersonalOvertimeAmountTypeName(
			String personalOvertimeAmountTypeName) {
		this.personalOvertimeAmountTypeName = personalOvertimeAmountTypeName;
	}

	/**
	 * @return the amount
	 */
	public String getAmount() {
		return amount;
	}

	/**
	 * @param amount
	 *            the amount to set
	 */
	public void setAmount(String amount) {
		this.amount = amount;
	}

	/**
	 * @return the minimumAmount
	 */
	public String getMinimumAmount() {
		return minimumAmount;
	}

	/**
	 * @param minimumAmount
	 *            the minimumAmount to set
	 */
	public void setMinimumAmount(String minimumAmount) {
		this.minimumAmount = minimumAmount;
	}

	/**
	 * @return the personalOvertimeAmountTypeId
	 */
	public ObjectIdLong getPersonalOvertimeAmountTypeId() {
		return personalOvertimeAmountTypeId;
	}

	/**
	 * @param personalOvertimeAmountTypeId
	 *            the personalOvertimeAmountTypeId to set
	 */
	public void setPersonalOvertimeAmountTypeId(
			ObjectIdLong personalOvertimeAmountTypeId) {
		this.personalOvertimeAmountTypeId = personalOvertimeAmountTypeId;
	}

}
