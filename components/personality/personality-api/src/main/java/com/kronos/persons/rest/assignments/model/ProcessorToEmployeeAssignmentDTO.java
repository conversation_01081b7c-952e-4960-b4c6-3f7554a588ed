package com.kronos.persons.rest.assignments.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.totalizing.business.extensibility.Processor;
import com.kronos.wfc.totalizing.business.extensibility.ProcessorToEmployee;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Optional;
import java.util.function.Supplier;

@Schema(title = "processorToEmployeeAssignment", description = "@v1.0.processortoemployeeassignment.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class ProcessorToEmployeeAssignmentDTO extends RuleAssignmentBean implements BaseAssignmentBean, Serializable {
    private static final long serialVersionUID = 5903421394830079379L;

    @Schema(description = "@v1.0.processortoemployeeassignment.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String effectiveDate;

    @Schema(description = "@v1.0.processortoemployeeassignment.apimodelproperty.expirationdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String expirationDate;

    @Schema(description = "@v1.0.processortoemployeeassignment.apimodelproperty.originaleffectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String originalEffectiveDate;

    @Schema(description = "@v1.0.processortoemployeeassignment.apimodelproperty.personidentity.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private PersonIdentityBean personIdentity;

    @Schema(description = "@v1.0.processortoemployeeassignment.apimodelproperty.processor.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private ObjectRef processor;

    @JsonIgnore
    private Long processorId;

    public ProcessorToEmployeeAssignmentDTO() {
        super();
    }

    public ProcessorToEmployeeAssignmentDTO(ProcessorToEmployee processorToEmployee) {
        super(processorToEmployee);
    }

    @Override
    public String getEffectiveDate() {
        return effectiveDate;
    }

    @Override
    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    @Override
    public String getExpirationDate() {
        return expirationDate;
    }

    @Override
    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    @Override
    public PersonIdentityBean getPersonIdentity() {
        return personIdentity;
    }

    @Override
    public void setPersonIdentity(PersonIdentityBean personIdentity) {
        this.personIdentity = personIdentity;
    }

    public ObjectRef getProcessor() {
        return processor;
    }

    public void setProcessor(ObjectRef processor) {
        this.processor = processor;
    }

    @Override
    public String getOriginalEffectiveDate() {
        return originalEffectiveDate;
    }

    @Override
    public void setOriginalEffectiveDate(String originalEffectiveDate) {
        this.originalEffectiveDate = originalEffectiveDate;
    }

    @Override
    @JsonIgnore
    public String getProcessorIdentifier() {
        return Optional.ofNullable(processor)
                .filter(p -> !p.isNullReference())
                .map(p -> Optional.ofNullable(p.getQualifier())
                        .orElseGet(getProcessorNameById(p.getId()))
                ).orElse(null);
    }

    private Supplier<? extends String> getProcessorNameById(Long id) {
        return () -> id != null ?
                Optional.ofNullable(Processor.getById(new ObjectIdLong(id)))
                        .map(Processor::getName).orElse(null) :
                null;
    }

    @Override
    public void setProcessorIdentifier(String processorIdentifier) {
        processor = new ObjectRef(null, processorIdentifier);
    }

    @Override
    public Long getProcessorId() {
        return processorId;
    }

    @Override
    public void setProcessorId(Long processorId) {
        this.processorId = processorId;
    }
}
