package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.employeecurrencyassignmentbean.apimodel.description", name = "employeeCurrencyAssignment")
@JsonPropertyOrder(alphabetic = true)
@JsonIgnoreProperties(ignoreUnknown = true, 
value = {"currencyLocale"})
public class EmployeeCurrencyAssignmentBean {
    @Schema(description = "@v1.0.employeecurrencyassignmentbean.apimodelproperty.currencycode.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String currencyCode;

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	@Override
	public String toString() {
		return "EmployeeCurrencyAssignmentBean [currencyCode=" + currencyCode + "]";
	}
}
