package com.kronos.persons.rest.assignments.service;

import java.util.List;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.kronos.persons.rest.assignments.model.percentageallocationrule.PercentageAllocationRuleAssignmentBean;
import com.kronos.persons.rest.assignments.model.percentageallocationrule.PercentageAllocationRuleCriteriaBean;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;

@Deprecated // use IRestPercentageAllocationRuleAssignmentV2 instead
@OpenAPIDefinition(tags = {
        @Tag(description = "@v1.0.commons-persons-percentage_allocation_rule.description", extensions = @Extension(properties = {
                @ExtensionProperty(name = "parent", value = "@root.personassignments.default") }), name = "@v1.0.commons-persons-percentage_allocation_rule.name") })
@Tag(name = "@v1.0.commons-persons-percentage_allocation_rule.name")
@Publishable(type = RestAPITypes.PUBLIC, value = true, productType = ProductTypes.WFP)
@Path("/v1/commons/persons/percentage_allocation_rules")
public interface IRestPercentageAllocationRuleAssignment
{
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseStatus(code = HttpStatus.OK)
    @Operation(tags = "@v1.0.commons-persons-percentage_allocation_rule.post.currentpath.nickname", description = "@v1.0.commons-persons-percentage_allocation_rule.post.currentpath.notes", summary = "@v1.0.commons-persons-percentage_allocation_rule.post.currentpath.summary")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.currentpath.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = PercentageAllocationRuleAssignmentBean.class))),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.currentpath.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.currentpath.response.500.message", responseCode = "500") })
    @Path("/")
    PercentageAllocationRuleAssignmentBean assignPercentageAllocationRule(
            PercentageAllocationRuleAssignmentBean requestBean);

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseStatus(code = HttpStatus.OK)
    @Operation(tags = "@v1.0.commons-persons-percentage_allocation_rule.put.currentpath.nickname", description = "@v1.0.commons-persons-percentage_allocation_rule.put.currentpath.notes", summary = "@v1.0.commons-persons-percentage_allocation_rule.put.currentpath.summary")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.put.currentpath.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = PercentageAllocationRuleAssignmentBean.class))),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.put.currentpath.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.put.currentpath.response.500.message", responseCode = "500") })
    @Path("/")
    PercentageAllocationRuleAssignmentBean updatePercentageAllocationRuleAssignment(
            PercentageAllocationRuleAssignmentBean requestBean);

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(tags = "@v1.0.commons-persons-percentage_allocation_rule.get.{personid}.nickname", description = "@v1.0.commons-persons-percentage_allocation_rule.get.{personid}.notes", summary = "@v1.0.commons-persons-percentage_allocation_rule.get.{personid}.summary")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.get.{personid}.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = PercentageAllocationRuleAssignmentBean.class))),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.get.{personid}.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.get.{personid}.response.500.message", responseCode = "500") })
    @Path("/{personId:[0-9]+}")
    List<PercentageAllocationRuleAssignmentBean> getPercentageAllocationRuleAssignment(
            @Parameter(description = "@v1.0.commons-persons-percentage_allocation_rule.get.{personid}.pathparam.personid.value", required = false) @PathParam(value = "personId" ) Long personId,
            @Parameter(description = "@v1.0.commons-persons-percentage_allocation_rule.get.{personId}.queryparam.processor.value", required = false) @QueryParam(value = "processor") String processor,
            @Parameter(description = "@v1.0.commons-persons-percentage_allocation_rule.get.{personId}.queryparam.effective_date.value", required = false) @QueryParam(value = "effective_date") String effectivedate
    );

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(tags = "@v1.0.commons-persons-percentage_allocation_rule.get.currentpath.nickname", description = "@v1.0.commons-persons-percentage_allocation_rule.get.currentpath.notes", summary = "@v1.0.commons-persons-percentage_allocation_rule.get.currentpath.summary")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.get.currentpath.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = PercentageAllocationRuleAssignmentBean.class))),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.get.currentpath.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.get.currentpath.response.500.message", responseCode = "500") })
    @Path("/")
    List<PercentageAllocationRuleAssignmentBean> getPercentageAllocationRuleAssignmentByPersonNumber(
            @Parameter(description = "@v1.0.commons-persons-percentage_allocation_rule.get.currentpath.queryparam.person_number.value", required = false) @QueryParam(value = "person_number") String personNumber,
            @Parameter(description = "@v1.0.commons-persons-percentage_allocation_rule.get.currentpath.queryparam.processor.value", required = false) @QueryParam(value = "processor") String processor,
            @Parameter(description = "@v1.0.commons-persons-percentage_allocation_rule.get.currentpath.queryparam.effective_date.value", required = false) @QueryParam(value = "effective_date") String effectivedate
    );

    @GET
    @Deprecated
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(tags = "@v1.0.commons-persons-percentage_allocation_rule.get.multi_read.nickname", description = "@v1.0.commons-persons-percentage_allocation_rule.get.multi_read.notes", summary = "@v1.0.commons-persons-percentage_allocation_rule.get.multi_read.summary")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.get.multi_read.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = PercentageAllocationRuleAssignmentBean.class))),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.get.multi_read.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.get.multi_read.response.500.message", responseCode = "500") })
    @Path("/multi_read")
    List<PercentageAllocationRuleAssignmentBean> getAllPercentageAllocationRuleAssignments();

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(tags = "@v1.0.commons-persons-percentage_allocation_rule.get.multi_read_by_persons.nickname", description = "@v1.0.commons-persons-percentage_allocation_rule.get.multi_read_by_persons.notes", summary = "@v1.0.commons-persons-percentage_allocation_rule.get.multi_read_by_persons.summary")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.get.multi_read.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = PercentageAllocationRuleAssignmentBean.class))),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.get.multi_read.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.get.multi_read.response.500.message", responseCode = "500") })
    @Path("/multi_read")
    List<PercentageAllocationRuleAssignmentBean> getPercentageAllocationRulesByPersonNumbers(ExtensionSearchCriteria requestDataList);

    @DELETE
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseStatus(code = HttpStatus.NO_CONTENT)
    @Operation(tags = "@v1.0.commons-persons-percentage_allocation_rule.delete.currentpath.nickname", description = "@v1.0.commons-persons-percentage_allocation_rule.delete.currentpath.notes", summary = "@v1.0.commons-persons-percentage_allocation_rule.delete.currentpath.summary")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.delete.currentpath.response.200.message", responseCode = "204"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.delete.currentpath.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.delete.currentpath.response.500.message", responseCode = "500") })
    @Path("/")
    void deletePercentageAllocationRuleAssignment(PercentageAllocationRuleCriteriaBean criteriaBean);

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseStatus(code = HttpStatus.NO_CONTENT)
    @Operation(tags = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_delete.nickname", description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_delete.notes", summary = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_delete.summary")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_delete.response.204.message", responseCode = "204"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_delete.response.207.message", responseCode = "207"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_delete.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_delete.response.500.message", responseCode = "500") })
    @Path("/multi_delete")
    void multiDelete(List<PercentageAllocationRuleCriteriaBean> criteriaBeanList);

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseStatus(code = HttpStatus.OK)
    @Operation(tags = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_create.nickname", description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_create.notes", summary = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_create.summary")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_create.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = PercentageAllocationRuleAssignmentBean.class))),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_create.response.207.message", responseCode = "207"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_create.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_create.response.500.message", responseCode = "500") })
    @Path("/multi_create")
    List<PercentageAllocationRuleAssignmentBean> multiCreate(
            List<PercentageAllocationRuleAssignmentBean> requestBean);

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseStatus(code = HttpStatus.OK)
    @Operation(tags = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_update.nickname", description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_update.notes", summary = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_update.summary")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_update.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = PercentageAllocationRuleAssignmentBean.class))),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_update.response.207.message", responseCode = "207"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_update.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-percentage_allocation_rule.post.multi_update.response.500.message", responseCode = "500") })
    @Path("/multi_update")
    List<PercentageAllocationRuleAssignmentBean> multiUpdate(
            List<PercentageAllocationRuleAssignmentBean> requestBean);
}
