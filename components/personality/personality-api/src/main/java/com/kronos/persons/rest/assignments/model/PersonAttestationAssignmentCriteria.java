package com.kronos.persons.rest.assignments.model;

public class PersonAttestationAssignmentCriteria {

    private boolean includeEmployeeRole;
    private boolean includeManagerRole;

    public boolean isIncludeEmployeeRole() {
        return includeEmployeeRole;
    }

    public void setIncludeEmployeeRole(boolean includeEmployeeRole) {
        this.includeEmployeeRole = includeEmployeeRole;
    }

    public boolean isIncludeManagerRole() {
        return includeManagerRole;
    }

    public void setIncludeManagerRole(boolean includeManagerRole) {
        this.includeManagerRole = includeManagerRole;
    }

}
