package com.kronos.persons.rest.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Search criteria for multi read of wage and work rules.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * <p>
 * Date: April 1, 2020
 *
 * <AUTHOR>
 */
@Schema(name= "WageWorkRuleMultiReadSearchCriteria",
        description = "@v1.0.commons-persons-wage_work_rules.wageworkrulemultireadsearchcriteria.apimodel.description")
public class WageWorkRuleMultiReadSearchCriteria {

    @Schema(description = "@v1.0.commons-persons-wage_work_rules.wageworkrulemultireadsearchcriteria.apimodelproperty.where.description")
    private WageWorkRuleMultiReadWhere where;

    /**
     * @return request information for multi read of wage and work rules
     */
    public WageWorkRuleMultiReadWhere getWhere() {
        return where;
    }

    /**
     * Sets request information for multi read of wage and work rules.
     *
     * @param where request information for multi read of wage and work rules, to be set
     */
    public void setWhere(WageWorkRuleMultiReadWhere where) {
        this.where = where;
    }
}
