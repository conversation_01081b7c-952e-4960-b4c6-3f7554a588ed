package com.kronos.persons.rest.model;

/**
 * DTO to hold information about Work Rule.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * <p>
 * Date: April 8, 2020
 *
 * <AUTHOR>
 */
public class WorkRuleDTO {

    private Long id;
    private String name;

    /**
     * Constructs Work Rule dto with id and name.
     *
     * @param id id, to be set
     * @param name name of the work rule, to be set
     */
    public WorkRuleDTO(Long id, String name) {
        this.id = id;
        this.name = name;
    }

    /**
     * @return id of the work rule
     */
    public Long getId() {
        return id;
    }

    /**
     * Sets id to the work rule.
     *
     * @param id id, to be set
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return name of the work rule
     */
    public String getName() {
        return name;
    }

    /**
     * Sets name to the work rule.
     *
     * @param name name, to be set
     */
    public void setName(String name) {
        this.name = name;
    }
}
