package com.kronos.persons.rest.beans.extensions;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;


@Schema(description = "@v1.0.predictiveschedulingeligibilitydataextension.apimodel.description", name = "predictiveSchedulingEligibilityDetails", allOf = ExtensionEffectiveDatedEntry.class)
@JsonPropertyOrder(alphabetic = true)
public class PredictiveSchedulingEligibilityDataExtension extends ExtensionEffectiveDatedEntry {

   @Schema(description = "@v1.0.predictiveschedulingeligibilitydataextension.apimodelproperty.iseligible.description")
   private Boolean isEligible;

   public Boolean getIsEligible() {
      return isEligible;
   }

   public void setIsEligible(Boolean isEligible) {
      this.isEligible = isEligible;
   }
}
