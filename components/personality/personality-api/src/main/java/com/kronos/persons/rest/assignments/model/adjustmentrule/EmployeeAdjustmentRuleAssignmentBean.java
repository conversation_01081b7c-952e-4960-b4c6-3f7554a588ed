package com.kronos.persons.rest.assignments.model.adjustmentrule;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.persons.rest.assignments.model.BaseAssignmentBean;
import com.kronos.persons.rest.assignments.model.RuleAssignmentBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.wfc.totalizing.business.extensibility.ProcessorToEmployee;
import io.swagger.v3.oas.annotations.media.Schema;
import com.kronos.cache.api.annotations.CachedEntity;
@CachedEntity
@Schema(title = "employeeAdjustmentRuleAssignment", description = "@v1.0.employeeadjustmentruleassignmentbean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class EmployeeAdjustmentRuleAssignmentBean extends RuleAssignmentBean implements BaseAssignmentBean, Serializable
{
  
	private static final long serialVersionUID = -1809604646808285009L;

	@Schema(description = "@v1.0.employeeadjustmentruleassignmentbean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    String effectiveDate;

    @Schema(description = "@v1.0.employeeadjustmentruleassignmentbean.apimodelproperty.expirationdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    String expirationDate;

    @Schema(description = "@v1.0.employeeadjustmentruleassignmentbean.apimodelproperty.originaleffectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    String originalEffectiveDate;

    @Schema(description = "@v1.0.employeeadjustmentruleassignmentbean.apimodelproperty.personidentity.description", requiredMode = Schema.RequiredMode.REQUIRED)
    PersonIdentityBean personIdentity;

    @Schema(description = "@v1.0.employeeadjustmentruleassignmentbean.apimodelproperty.processor.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String processor;
    
    @JsonIgnore
    private Long processorId;

    @Schema(description = "@v1.0.employeeadjustmentruleassignmentbean.apimodelproperty.adjustmentRule.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private ObjectRef adjustmentRule;

    public EmployeeAdjustmentRuleAssignmentBean()
    {
        super();
    }

    public EmployeeAdjustmentRuleAssignmentBean(
            ProcessorToEmployee processorToEmployee)
    {
        super(processorToEmployee);
    }

    public String getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getOriginalEffectiveDate() {
        return originalEffectiveDate;
    }

    public void setOriginalEffectiveDate(String originalEffectiveDate) {
        this.originalEffectiveDate = originalEffectiveDate;
    }

    public PersonIdentityBean getPersonIdentity() {
        return personIdentity;
    }

    public void setPersonIdentity(PersonIdentityBean personIdentity) {
        this.personIdentity = personIdentity;
    }

    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }

    public ObjectRef getAdjustmentRule() {
        return adjustmentRule;
    }

    public void setAdjustmentRule(ObjectRef adjustmentRule) {
        this.adjustmentRule = adjustmentRule;
    }

    @Override
    @JsonIgnore
    public String getProcessorIdentifier()
    {
        if (this.processor != null) {
            return this.processor;
        } else {
            return this.adjustmentRule != null ? this.adjustmentRule.getQualifier() : null;
        }
    }

    @Override
    public void setProcessorIdentifier(String processorIdentifier)
    {
        this.processor = processorIdentifier;
    }
    
    @Override
    public Long getProcessorId() {
		return processorId;
	}

    @Override
    public void setProcessorId(Long processorId) {
		this.processorId = processorId;
	}

}
