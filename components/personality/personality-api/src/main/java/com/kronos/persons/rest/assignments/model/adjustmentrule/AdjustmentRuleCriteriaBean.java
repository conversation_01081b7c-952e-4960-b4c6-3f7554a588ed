package com.kronos.persons.rest.assignments.model.adjustmentrule;

import com.kronos.persons.rest.assignments.model.RuleCriteriaBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "adjustmentRuleCriteria", description = "@v1.0.adjustmentrulecriteriabean.apimodel.description")
public class AdjustmentRuleCriteriaBean implements RuleCriteriaBean
{

    @Schema(description = "@v1.0.adjustmentrulecriteriabean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    String effectiveDate;

    @Schema(description = "@v1.0.adjustmentrulecriteriabean.apimodelproperty.personidentity.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    PersonIdentityBean personIdentity;

    @Schema(description = "@v1.0.adjustmentrulecriteriabean.apimodelproperty.processor.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    String processor;

    public String getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public PersonIdentityBean getPersonIdentity() {
        return personIdentity;
    }

    public void setPersonIdentity(PersonIdentityBean personIdentity) {
        this.personIdentity = personIdentity;
    }

    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }

}
