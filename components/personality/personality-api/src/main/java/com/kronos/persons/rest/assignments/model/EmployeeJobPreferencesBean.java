package com.kronos.persons.rest.assignments.model;

import java.time.LocalDate;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "employeeJobPreferences", description = "@v1.0.employeeJobPreferencesBean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class EmployeeJobPreferencesBean implements BaseAssignmentBean {

   @Schema(description = "@v1.0.employeeJobPreferencesBean.apimodelproperty.personNumber.description")
   private String personNumber;

   @Schema(description = "@v1.0.employeeJobPreferencesBean.apimodelproperty.employeeJobPreferences.description")
   private List<JobPreferenceBean> employeeJobPreferences;

   @Schema(description = "@v1.0.employeeJobPreferencesBean.apimodelproperty.positionsEmployeeJobPreference.description")
   private List<PositionJobPreferenceBean> employeePositionJobPreferences;

   @Schema(description = "@v1.0.employeeJobPreferencesBean.apimodelproperty.effectiveDate.description")
   private LocalDate effectiveDate;

   @JsonIgnore
   private Boolean isUpdatePartial;

   @JsonIgnore
   private Boolean update;

   @JsonIgnore
   private ObjectRef employeeRef;

   /**
    * @return the personNumber
    */
   public String getPersonNumber() {
      return personNumber;
   }

   /**
    * @param personNumber the personNumber to set
    */
   public void setPersonNumber(String personNumber) {
      this.personNumber = personNumber;
   }

   public List<JobPreferenceBean> getEmployeeJobPreferences() {
      return employeeJobPreferences;
   }

   public void setEmployeeJobPreferences(List<JobPreferenceBean> employeeJobPreferences) {
      this.employeeJobPreferences = employeeJobPreferences;
   }

   public LocalDate getEffectiveDate() {
      return effectiveDate;
   }

   public void setEffectiveDate(LocalDate effectiveDate) {
      this.effectiveDate = effectiveDate;
   }

   public Boolean isUpdatePartial() {
      return isUpdatePartial;
   }

   public void setUpdatePartial(Boolean isUpdatePartial) {
      this.isUpdatePartial = isUpdatePartial;
   }

   public Boolean isUpdate() {
      return update;
   }

   public void setUpdate(Boolean update) {
      this.update = update;
   }

   public List<PositionJobPreferenceBean> getEmployeePositionJobPreferences() {
      return employeePositionJobPreferences;
   }

   public void setEmployeePositionJobPreferences(List<PositionJobPreferenceBean> employeePositionJobPreferences) {
      this.employeePositionJobPreferences = employeePositionJobPreferences;
   }

   public ObjectRef getEmployeeRef() {
      return employeeRef;
   }

   public void setEmployeeRef(ObjectRef employeeRef) {
      this.employeeRef = employeeRef;
   }
}
