package com.kronos.persons.rest.assignments.model.brazilcompliance;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.brazilassignmentcompanydetailrest.apimodel.description", name = "brazilAssignmentCompanyDetailRest")
@JsonPropertyOrder({"company", "effectiveDate"})
public class BrazilAssignmentCompanyDetailRest {

    @Schema(description = "@v1.0.brazilassignmentcompanydetailrest.apimodelproperty.company.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private CompanyAttributesRest company;

    @Schema(description = "@v1.0.brazilassignmentcompanydetailrest.apimodelproperty.effectiveDate.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate effectiveDate;

    public CompanyAttributesRest getCompany() {
        return company;
    }

    public void setCompany(CompanyAttributesRest company) {
        this.company = company;
    }

    public LocalDate getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDate effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

}