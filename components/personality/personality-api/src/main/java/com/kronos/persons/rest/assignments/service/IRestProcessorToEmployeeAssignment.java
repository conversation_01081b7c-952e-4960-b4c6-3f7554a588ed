package com.kronos.persons.rest.assignments.service;

import com.kronos.persons.rest.assignments.model.ProcessorToEmployeeAssignmentDTO;
import com.kronos.persons.rest.assignments.model.ProcessorToEmployeeCriteriaDTO;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;
import java.util.List;

@Tag(name= "@v1.0.commons-persons-processor_to_employee_assignment.name")
@Path("/v1/commons/persons/extension_processor")
@Publishable(type = RestAPITypes.PRIVATE, value = true, productType = ProductTypes.WFP)
@OpenAPIDefinition(tags = {
        @Tag(description = "@v1.0.commons-persons-processor_to_employee_assignment.description", extensions = @Extension(properties = {
                @ExtensionProperty(name = "parent", value = "@v1.0.commons-persons-processor_to_employee_assignment.default")}), name = "@v1.0.commons-persons-processor_to_employee_assignment.name")})
public interface IRestProcessorToEmployeeAssignment {

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseStatus(code = HttpStatus.OK)
    @Operation(tags = "@v1.0.commons-persons-processor_to_employee_assignment.post.nickname", description = "@v1.0.commons-persons-processor_to_employee_assignment.post.notes",
            summary = "@v1.0.commons-persons-processor_to_employee_assignment.post.summary", operationId = "@v1.0.commons-persons-processor_to_employee_assignment.name")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = ProcessorToEmployeeAssignmentDTO.class))),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.response.403.message", responseCode = "403"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.response.404.message", responseCode = "404")})
    @Path("/")
    ProcessorToEmployeeAssignmentDTO createProcessorToEmployeeAssignment(
            ProcessorToEmployeeAssignmentDTO requestBean);

    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseStatus(code = HttpStatus.OK)
    @Operation(tags = "@v1.0.commons-persons-processor_to_employee_assignment.put.nickname", description = "@v1.0.commons-persons-processor_to_employee_assignment.put.notes",
            summary = "@v1.0.commons-persons-processor_to_employee_assignment.put.summary", operationId = "@v1.0.commons-persons-processor_to_employee_assignment.name")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.put.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = ProcessorToEmployeeAssignmentDTO.class))),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.put.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.put.response.403.message", responseCode = "403"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.put.response.404.message", responseCode = "404")})
    @Path("/")
    ProcessorToEmployeeAssignmentDTO updateProcessorToEmployeeAssignment(
            ProcessorToEmployeeAssignmentDTO requestBean);

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(tags = "@v1.0.commons-persons-processor_to_employee_assignment.get.{personid}.nickname", description = "@v1.0.commons-persons-processor_to_employee_assignment.get.{personid}.notes",
            summary = "@v1.0.commons-persons-processor_to_employee_assignment.get.{personid}.summary", operationId = "@v1.0.commons-persons-processor_to_employee_assignment.name")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.get.{personid}.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = ProcessorToEmployeeAssignmentDTO.class))),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.get.{personid}.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.get.{personid}.response.403.message", responseCode = "403"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.get.{personid}.response.404.message", responseCode = "404")})
    @Path("/{personId:[0-9]+}")
    List<ProcessorToEmployeeAssignmentDTO> getProcessorToEmployeeAssignmentByPersonId(
            @Parameter(description = "@v1.0.commons-persons-processor_to_employee_assignment.get.{personid}.pathparam.personid.value", required = false) @PathParam(value = "personId") Long personId
    );

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(tags = "@v1.0.commons-persons-processor_to_employee_assignment.get.nickname", description = "@v1.0.commons-persons-processor_to_employee_assignment.get.notes",
            summary = "@v1.0.commons-persons-processor_to_employee_assignment.get.summary", operationId = "@v1.0.commons-persons-processor_to_employee_assignment.name")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.get.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = ProcessorToEmployeeAssignmentDTO.class))),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.get.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.get.response.403.message", responseCode = "403"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.get.response.404.message", responseCode = "404")})
    @Path("/")
    List<ProcessorToEmployeeAssignmentDTO> getProcessorToEmployeeAssignmentsByParams(
            @Parameter(description = "@v1.0.commons-persons-processor_to_employee_assignment.get.queryparam.person_number.value", required = false) @QueryParam(value = "person_number") String personNumber,
            @Parameter(description = "@v1.0.commons-persons-processor_to_employee_assignment.get.queryparam.processor.value", required = false) @QueryParam(value = "processor") String processor,
            @Parameter(description = "@v1.0.commons-persons-processor_to_employee_assignment.get.queryparam.effective_date.value", required = false) @QueryParam(value = "effective_date") String effectivedate
    );

    @DELETE
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseStatus(code = HttpStatus.NO_CONTENT)
    @Operation(tags = "@v1.0.commons-persons-processor_to_employee_assignment.delete.nickname", description = "@v1.0.commons-persons-processor_to_employee_assignment.delete.notes",
            summary = "@v1.0.commons-persons-processor_to_employee_assignment.delete.summary", operationId = "@v1.0.commons-persons-processor_to_employee_assignment.name")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.delete.response.200.message", responseCode = "204"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.delete.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.delete.response.403.message", responseCode = "403"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.delete.response.404.message", responseCode = "404")})
    @Path("/")
    void deleteProcessorToEmployeeAssignment(ProcessorToEmployeeCriteriaDTO criteriaBean);

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseStatus(code = HttpStatus.NO_CONTENT)
    @Operation(tags = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_delete.nickname", description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_delete.notes",
            summary = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_delete.summary", operationId = "@v1.0.commons-persons-processor_to_employee_assignment.name")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_delete.response.204.message", responseCode = "204"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_delete.response.207.message", responseCode = "207"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_delete.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_delete.response.403.message", responseCode = "403")})
    @Path("/multi_delete")
    void multiDeleteProcessorToEmployeeAssignments(List<ProcessorToEmployeeCriteriaDTO> criteriaBeanList);

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseStatus(code = HttpStatus.OK)
    @Operation(tags = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_create.nickname", description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_create.notes",
            summary = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_create.summary", operationId = "@v1.0.commons-persons-processor_to_employee_assignment.name")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_create.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = ProcessorToEmployeeAssignmentDTO.class))),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_create.response.207.message", responseCode = "207"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_create.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_create.response.403.message", responseCode = "403")})
    @Path("/multi_create")
    List<ProcessorToEmployeeAssignmentDTO> multiCreateProcessorToEmployeeAssignments(
            List<ProcessorToEmployeeAssignmentDTO> requestBean);

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @ResponseStatus(code = HttpStatus.OK)
    @Operation(tags = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_update.nickname", description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_update.notes",
            summary = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_update.summary", operationId = "@v1.0.commons-persons-processor_to_employee_assignment.name")
    @ApiResponses(value = {
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_update.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = ProcessorToEmployeeAssignmentDTO.class))),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_update.response.207.message", responseCode = "207"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_update.response.400.message", responseCode = "400"),
            @ApiResponse(description = "@v1.0.commons-persons-processor_to_employee_assignment.post.multi_update.response.403.message", responseCode = "403")})
    @Path("/multi_update")
    List<ProcessorToEmployeeAssignmentDTO> multiUpdateProcessorToEmployeeAssignments(
            List<ProcessorToEmployeeAssignmentDTO> requestBean);

}