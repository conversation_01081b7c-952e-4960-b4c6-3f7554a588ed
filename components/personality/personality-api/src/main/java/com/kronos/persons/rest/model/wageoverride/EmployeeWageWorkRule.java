package com.kronos.persons.rest.model.wageoverride;

import java.util.List;

/**
 * This class is used as an intermediate object for storing data
 * from WageWorkRuleOverride and JobTransferEntry while working
 * EmployeeWageWorkRuleConverter.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * Date: Apr 04, 2020
 *
 * <AUTHOR>
 */
public class EmployeeWageWorkRule {
    private Long personId;
    private Long positionId;
    private List<WageWorkRule> wageWorkRules;

    /**
     * Constructor.
     */
    public EmployeeWageWorkRule() {
    }

    /**
     * Constructor.
     *
     * @param personId the id of employee
     */
    public EmployeeWageWorkRule(Long personId) {
        this.personId = personId;
    }

    public EmployeeWageWorkRule(Long personId, Long positionId) {
        this.personId = personId;
        this.positionId = positionId;
    }

    /**
     * Constructor.
     *
     * @param personId      the id of employee
     * @param wageWorkRules list of wage work rules
     */
    public EmployeeWageWorkRule(Long personId, List<WageWorkRule> wageWorkRules) {
        this.personId = personId;
        this.wageWorkRules = wageWorkRules;
    }

    public EmployeeWageWorkRule(Long personId, Long positionId, List<WageWorkRule> wageWorkRules) {
        this.personId = personId;
        this.positionId = positionId;
        this.wageWorkRules = wageWorkRules;
    }
    /**
     * Gets person id.
     *
     * @return person id
     */
    public Long getPersonId() {
        return personId;
    }

    /**
     * Set person id.
     *
     * @param personId person id
     */
    public void setPersonId(Long personId) {
        this.personId = personId;
    }

    public Long getPositionId() {
        return positionId;
    }

    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    /**
     * Gets wage work rules.
     *
     * @return list of wage work rules
     */
    public List<WageWorkRule> getWageWorkRules() {
        return wageWorkRules;
    }

    /**
     * Set wage work rules.
     *
     * @param wageWorkRules list of wage work rules
     */
    public void setWageWorkRules(List<WageWorkRule> wageWorkRules) {
        this.wageWorkRules = wageWorkRules;
    }
}
