package com.kronos.persons.rest.assignments.model;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "JobPreferenceAssignment", description = "@v1.0.jobPreferenceBean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class JobPreferenceAssignmentBean implements Serializable {

	private static final long serialVersionUID = 123456787567653456L;
	@Schema(description = "@v1.0.jobpreferencebean.apimodelproperty.job.description")
	private String job;

	@Schema(description = "@v1.0.jobPreferenceBean.apimodelproperty.preference.description")
	private String preference;
	
	@Schema(description = "@v1.0.jobPreferenceBean.apimodelproperty.seniorityDate.description")
	private LocalDate seniorityDate;
	

	@Schema(description = "@v1.0.jobPreferenceBean.apimodelproperty.schedulingContextSet.description")
	private List<ObjectRef> schedulingContextSet;

	public String getJob() {
		return job;
	}

	public void setJob(String job) {
		this.job = job;
	}

	public String getPreference() {
		return preference;
	}

	public void setPreference(String preference) {
		this.preference = preference;
	}

	public List<ObjectRef> getSchedulingContextSet() {
		return schedulingContextSet;
	}

	public void setSchedulingContextSet(List<ObjectRef> schedulingContextSet) {
		this.schedulingContextSet = schedulingContextSet;
	}

	public LocalDate getSeniorityDate() {
		return seniorityDate;
	}

	public void setSeniorityDate(LocalDate seniorityDate) {
		this.seniorityDate = seniorityDate;
	}
	
}
