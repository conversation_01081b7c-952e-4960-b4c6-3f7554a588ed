package com.kronos.persons;

import java.time.LocalDate;

public final class EmployeeAssignmentsConstants {
    public static final String EMPLOYEE_ASSIGNMENTS_EDIT = "EMPLOYEE_ASSIGNMENTS_EDIT";
    public static final String EMPLOYEE_ASSIGNMENTS_READ = "EMPLOYEE_ASSIGNMENTS_READ";
    public static final String ALLOWED = "ALLOWED";
    public static final LocalDate FOREVER_DATE = LocalDate.of(3000, 1, 1);
    public static final LocalDate BEGINNING_DATE = LocalDate.of(1900, 1, 1);

    private EmployeeAssignmentsConstants() {
    }
}
