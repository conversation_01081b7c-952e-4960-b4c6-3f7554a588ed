package com.kronos.persons.rest.assignments.model.brazilcompliance;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import io.swagger.v3.oas.annotations.media.Schema;


@Schema(description = "@v1.0.brazilemployeeassignmentsrest.apimodel.description", name = "brazilEmployeeAssignmentsRest")
@JsonPropertyOrder({"personIdentity", "pis", "esocial", "cpf", "companyAssignments", "pcaAssignments", "repTypeAssignments"})
public class BrazilEmployeeAssignmentsRest {

    @Schema(description = "@v1.0.brazilemployeeassignmentsrest.apimodelproperty.personIdentity.description", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "personIdentity", required = true)
    private PersonIdentityBean personIdentity;

    @Schema(description = "@v1.0.brazilemployeeassignmentsrest.apimodelproperty.pis.description")
    private String pis;

    @Schema(description = "@v1.0.brazilemployeeassignmentsrest.apimodelproperty.eSocial.description")
    private String esocial;

    @Schema(description = "@v1.0.brazilemployeeassignmentsrest.apimodelproperty.cpf.description")
    private String cpf;

    @Schema(description = "@v1.0.brazilemployeeassignmentsrest.apimodelproperty.companyAssignments.description")
    private BrazilAssignmentCompanyRest companyAssignments;

    @Schema(description = "@v1.0.brazilemployeeassignmentsrest.apimodelproperty.pcaAssignments.description")
    private BrazilAssignmentPcaRest pcaAssignments;

    @Schema(description = "@v1.0.brazilemployeeassignmentsrest.apimodelproperty.repTypeAssignments.description")
    private BrazilAssignmentRepTypeRest repTypeAssignments;

    public PersonIdentityBean getPersonIdentity() {
        return personIdentity;
    }

    public void setPersonIdentity(PersonIdentityBean personIdentity) {
        this.personIdentity = personIdentity;
    }

    public String getPis() {
        return pis;
    }

    public void setPis(String pis) {
        this.pis = pis;
    }

    public String getEsocial() {
        return esocial;
    }

    public void setEsocial(String eSocial) {
        this.esocial = eSocial;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public BrazilAssignmentCompanyRest getCompanyAssignments() {
        return companyAssignments;
    }

    public void setCompanyAssignments(BrazilAssignmentCompanyRest companyAssignments) {
        this.companyAssignments = companyAssignments;
    }

    public BrazilAssignmentPcaRest getPcaAssignments() {
        return pcaAssignments;
    }

    public void setPcaAssignments(BrazilAssignmentPcaRest pcaAssignments) {
        this.pcaAssignments = pcaAssignments;
    }

    public BrazilAssignmentRepTypeRest getRepTypeAssignments() {
        return repTypeAssignments;
    }

    public void setRepTypeAssignments(BrazilAssignmentRepTypeRest repTypeAssignments) {
        this.repTypeAssignments = repTypeAssignments;
    }

}