/***********************************************************************
 * EmployeeTransferRestServiceConstants.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/

package com.kronos.persons.rest.service;

/**
 * This interface contains all constants being used in Employee component.
 * <AUTHOR>
 *
 */
public interface PersonRestServiceConstants {

	public static final String ERROR_CREATING_EMPLOYEE_SURROGATE = "WCO-101601"; 
	public static final String ERROR_VALIDATING_EMPLOYEESURROGATE = "WCO-101602";
	public static final String ERROR_VALIDATING_EMPLOYEEDTO = "WCO-101603";
	public static final String ERROR_SETTING_PARAMETERS_FOR_EMPLOYEE = "WCO-101604";
	public static final String ERROR_FETCHING_TRANSFER_EMPLOYEE = "WCO-101605";
	public static final String ERROR_WHILE_CLONING = "WCO-101606";
	public static final String ERROR_GETTING_USER_DETAILS = "WCO-101607";
	public static final String ERROR_GETTING_PHOTO_ENABLED = "WCO-101608";
	public static final String ERROR_GETTING_USER_PHOTO_ID = "WCO-101609";
	public static final String ERROR_GETTING_SYSTEM_CONFIGURATION = "WCO-101610";
	public static final String ERROR_GETTING_USER_DELEGATION_ID = "WCO-101612";
	
	public static final String ERROR_VALIDATING_EMPLOYEEDTO_MESSAGE = "Error validating instance information required for Employee";
	public static final String ERROR_VALIDATING_EMPLOYEESURROGATE_MESSAGE = "Error validating instance information required for EmployeeSurrogate";
	public static final String ERROR_CREATING_EMPLOYEE_SURROGATE_MESSAGE = "Exception occurred while creating EmployeeSurrogate";
	public static final String ERROR_CLONING_EFFECTIVEPRIMARYJOB_MESSAGE = "Exception occurred while cloning EffectivePrimaryJob instance";
	public static final String CLONENOTSUPPORTEDEXCEPTION_OCCURRED_WHILE_CLONING_EMPLOYEE_MESSAGE = "CloneNotSupportedException caught while cloning Employee instance";
	public static final String ERROR_CLONING_EMPLOYEESURROGATE_MESSAGE = "Exception occurred while cloning EmployeeSurrogate instance.";
	public static final String ERROR_FETCHING_ID_FROM_LEGACY_FOR_SETTING_IN_EMPLOYEE_MESSAGE = "Exception occurred while fetching id from legacy instance for setting in Employee instance.";
	public static final String ERROR_SETTING_SNAPSHOT_IN_EMPLOYEE_MESSAGE = "Exception occurred while setting snapshotDate in Employee instance";
	public static final String ERROR_SETTING_PRIMARYORGJOBNODE_IN_EMPLOYEE_MESSAGE = "Exception occurred while setting primaryOrgJobNode from legacy instance in Employee.";
	public static final String ERROR_FETCHING_EMPLOYEE_TRANSFER_DETAILS_FROM_EMPLOYEEID_MESSAGE = "Exception occurred while fetching transfer employee detail from employeeId.";
	public static final String NUMBER_FORMAT_EXCEPTION_PARSING_SURROGATE_PERSONID = "Exception occured while parsing personId for surrogateEmployee. Hence setting legacyId to null and continuing!";
	public static final String ERROR_GETTING_USER_PHOTO_ID_MESSAGE = "Error occurred fetching User photoId! ";
	public static final String ERROR_GETTING_USER_PHOTO_ID_FROM_CACHE_MESSAGE = "Error occurred fetching User photoId from EmployeePhotoId cache!";
    public static final String ERROR_GETTING_USER_DELEGATION = "Error occurred fetching User Details in userDelegation! ";
	public static final String ERROR_GETTING_SYSTEM_CONFIGURATION_MESSAGE = "Error occurred fetching System Configuration! ";
	public static final String ERROR_CREATING_SYSTEM_CONFIGURATION_MESSAGE = "Error occurred creating new System Configuration!";
	public static final String ERROR_GETTING_USER_DETAILS_MESSAGE = "Error getting user details!";
	public static final String ERROR_GETTING_PHOTO_ENABLED_MESSAGE = "Error getting is photo enabled value! ";
	public static final String SESSION_TIMEOUT_KEY = "global.webserver.session.timeout";
	public static final String DEFAULT_SESSION_TIMEOUT = "1800";
	public static final String ERROR_PARSING_SESSION_TIMEOUT ="Error occurred parsing session timeout to integer!";
	public static final String ERROR_PARSING_GRACEPERIOD ="Error occurred parsing grace-period to integer!";
	public static final String GRACEPERIOD_KEY = "site.webserver.session.timeout.graceperiod.minutes";
	public static final String WFC_VERSION_PROP = "NAVIGATOR.Register.Version";
	public static final String WFC_BUILD_PROP = "NAVIGATOR.Register.Build";
	public static final String ERROR_DELETING_PERSON_IDS = "Error occurred deleting multiple personIds! ";
	public static final String GROUPEDIT_ENTITY_TYPE = "PERSON";
	public static final String OFFLINE_TIMEOUT_KEY = "global.offline.disconnection.delay";
	public static final String DEFAULT_OFFLINE_TIMEOUT = "5000";
	public static final String ERROR_GETTING_OFFLINE_TIMEOUT = "Error occurred fetching offline timeout! Default value will be returned.";
	
}
