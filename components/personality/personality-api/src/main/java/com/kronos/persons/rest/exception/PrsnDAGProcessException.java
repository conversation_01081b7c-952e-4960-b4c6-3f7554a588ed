package com.kronos.persons.rest.exception;

import com.kronos.container.api.exception.APIException;

/**
 * 
 * <AUTHOR>
 *
 */
public class PrsnDAGProcessException {
	
	public static String GDAPName = "GDAPname";
	
	public static APIException assignToNonManager(String name){
		APIException apiExp = new APIException(ExceptionConstants.CANNOT_ASSIGN_GDAP_TO_NONMANAGER);
		apiExp.addUserParameter(GDAPName, name);
		return apiExp;
	}

	
	public static APIException noForeverStartDate(String gdapName){
		APIException apiExp = new APIException(ExceptionConstants.DATE_PRIOR_TO_SIGNOFF);
		apiExp.addUserParameter(GDAPName, gdapName);
		return apiExp;
	}
	
	public static APIException gdapAssignmentEffectiveDateInPast(){
		APIException apiExp = new APIException(ExceptionConstants.PERSON_GDAP_EFF_DATE_IN_PAST);
		return apiExp;
	}

	
	public static APIException changingActiveGdapAssignmentDate(){
		APIException apiExp = new APIException(ExceptionConstants.PERSON_GDAP_MODIFY_ACTIVE_EFF_DATE);
		return apiExp;
	}

	public static APIException badGDAPItemName(String gdapName){
		APIException apiExp = new APIException(ExceptionConstants.NON_EXISTING_DAG_NAME);
		apiExp.addUserParameter(GDAPName, gdapName);
		return apiExp;
	}
	
	public static APIException existingDAGAttemptedAccess(){
		return new APIException(ExceptionConstants.EXISTING_DAGITEM_ATTEMPTED_ACCESS);
	}
	
}
