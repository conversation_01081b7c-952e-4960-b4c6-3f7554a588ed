package com.kronos.persons.rest.model;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.positionref.apimodel.description", name = "positionRef")
public class PositionRef {
    @Schema(description = "@v1.0.positionref.apimodelproperty.id.description")
    private Long id;
    @Schema(description = "@v1.0.positionref.apimodelproperty.name.description")
    private String name;
    @Schema(description = "@v1.0.positionref.apimodelproperty.externalid.description")
    private String externalId;

    public PositionRef() {

    }

    public PositionRef(Long id, String externalId, String name) {
        this.id = id;
        this.externalId = externalId;
        this.name = name;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
