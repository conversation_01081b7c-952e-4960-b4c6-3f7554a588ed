package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.approverbean.apimodel.description", name = "approverDetails")
@JsonPropertyOrder(alphabetic = true)
public class ApproverBean {
    @Schema(description = "@v1.0.approverbean.apimodelproperty.approvelevel.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String approveLevel;

    @Schema(description = "@v1.0.approverbean.apimodelproperty.duedateamount.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String dueDateAmount;

    @Schema(description = "@v1.0.approverbean.apimodelproperty.fullname.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fullName;

    @Schema(description = "@v1.0.approverbean.apimodelproperty.personnumber.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String personNumber;

	/**
	 * The object ID of the order number of the approver.
	 */
    @JsonIgnore
	protected Integer orderNum = null;

	public String getApproveLevel() {
		return approveLevel;
	}

	public void setApproveLevel(String approveLevel) {
		this.approveLevel = approveLevel;
	}

	public String getDueDateAmount() {
		return dueDateAmount;
	}

	public void setDueDateAmount(String dueDateAmount) {
		this.dueDateAmount = dueDateAmount;
	}

	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	public String getPersonNumber() {
		return personNumber;
	}

	public void setPersonNumber(String personNumber) {
		this.personNumber = personNumber;
	}

	public Integer getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(Integer orderNum) {
		this.orderNum = orderNum;
	}

	@Override
	public String toString() {
		return "ApproverBean [approveLevel=" + approveLevel
				+ ", dueDateAmount=" + dueDateAmount + ", fullName=" + fullName
				+ ", personNumber=" + personNumber + ", orderNum=" + orderNum
				+ "]";
	}
}
