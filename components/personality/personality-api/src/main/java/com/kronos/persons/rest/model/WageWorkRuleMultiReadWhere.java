package com.kronos.persons.rest.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO to hold request information for multi read of wage and work rules.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * <p>
 * Date: April 1, 2020
 *
 * <AUTHOR>
 */
@Schema(name = "WageWorkRuleMultiReadWhere",
        description = "@v1.0.commons-persons-wage_work_rules.wageworkrulemultireadwhere.apimodel.description")
public class WageWorkRuleMultiReadWhere {

    @Schema(description = "@v1.0.commons-persons-wage_work_rules.wageworkrulemultireadwhere.apimodelproperty.employees.description")
    private EmployeeCriteria employees;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.wageworkrulemultireadwhere.apimodelproperty.startdate.description")
    private String startDate;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.wageworkrulemultireadwhere.apimodelproperty.enddate.description")
    private String endDate;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.wageworkrulemultireadwhere.apimodelproperty.rawwage.description")
    private boolean rawWage;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.wageworkrulemultireadwhere.apimodelproperty.overridesonly.description")
    private boolean overridesOnly = false;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.wageworkrulemultireadwhere.apimodelproperty.index.description")
    private Integer index;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.wageworkrulemultireadwhere.apimodelproperty.count.description")
    private Integer count;

    /**
     * @return criteria for the employees
     */
    public EmployeeCriteria getEmployees() {
        return employees;
    }

    /**
     * Sets criteria for the employees.
     *
     * @param employees employees criteria, to be set
     */
    public void setEmployees(EmployeeCriteria employees) {
        this.employees = employees;
    }

    /**
     * @return start date
     */
    public String getStartDate() {
        return startDate;
    }

    /**
     * Sets start date.
     *
     * @param startDate start date, to be set
     */
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    /**
     * @return end date
     */
    public String getEndDate() {
        return endDate;
    }

    /**
     * Sets end date.
     *
     * @param endDate end date, to be set
     */
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    /**
     * @return flag of raw wage.
     */
    public boolean isRawWage() {
        return rawWage;
    }

    /**
     * Sets raw wage.
     * 
     * @param rawWage
     */
    public void setRawWage(boolean rawWage) {
        this.rawWage = rawWage;
    }

    /**
     * Returns {@code true} in case if we are loading only objects with overrides, otherwise {@code false}.
     *
     * @return flag indicating whether we are loading only objects with overrides
     */
    public boolean isOverridesOnly() {
        return overridesOnly;
    }

    /**
     * Sets flag indicating whether we are loading only objects with overrides.
     *
     * @param overridesOnly condition of flag, to be set
     */
    public void setOverridesOnly(boolean overridesOnly) {
        this.overridesOnly = overridesOnly;
    }

    /**
     * @return from which index to load data
     */
    public Integer getIndex() {
        return index;
    }

    /**
     * Sets from which index to load data.
     *
     * @param index index, from which to load data, to be set
     */
    public void setIndex(Integer index) {
        this.index = index;
    }

    /**
     * @return count of rows to be loaded.
     */
    public Integer getCount() {
        return count;
    }

    /**
     * Sets count of rows to be loaded.
     *
     * @param count count of rows, to be set
     */
    public void setCount(Integer count) {
        this.count = count;
    }
}
