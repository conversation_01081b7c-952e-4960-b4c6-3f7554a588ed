/*
 * *****************************************************************************
 * Copyright (c) 2020 Kronos, Inc. All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * Kronos, Inc. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with Kronos.
 *
 * KRONOS MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE
 * SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
 * PURPOSE, OR NON-INFRINGEMENT. KRONOS SHALL NOT BE LIABLE FOR ANY DAMAGES
 * SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING
 * THIS SOFTWARE OR ITS DERIVATIVES.
 ****************************************************************************
 *
 **/
package com.kronos.persons.supportapi.dto;

import java.util.List;

/**
 * This contains validateCache data for 1 tenant
 * <AUTHOR>
 *
 */
public class PersonalityResponseData {

	private String tenantShortName;
	private List<String> employeesInSync;
	private List<EmployeeOutOfSync> employeeOutOfSync;
	private List<String> listOfInvalidEmployees;
	
	public String getTenantShortName() {
		return tenantShortName;
	}
	public void setTenantShortName(String tenantShortName) {
		this.tenantShortName = tenantShortName;
	}
	public List<String> getEmployeesInSync() {
		return employeesInSync;
	}
	public void setEmployeesInSync(List<String> employeesInSync) {
		this.employeesInSync = employeesInSync;
	}
	public List<EmployeeOutOfSync> getEmployeeOutOfSync() {
		return employeeOutOfSync;
	}
	public void setEmployeeOutOfSync(List<EmployeeOutOfSync> employeeOutOfSync) {
		this.employeeOutOfSync = employeeOutOfSync;
	}
	public List<String> getListOfInvalidEmployees() {
		return listOfInvalidEmployees;
	}
	public void setListOfInvalidEmployees(List<String> listOfInvalidEmployees) {
		this.listOfInvalidEmployees = listOfInvalidEmployees;
	}
	@Override
	public String toString() {
		return "PersonalityResponseData [tenantShortName=" + tenantShortName + ", employeesInSync="
				+ employeesInSync + ", employeeOutOfSync=" + employeeOutOfSync + ", listOfInvalidEmployees="
				+ listOfInvalidEmployees + "]";
	}
	
	
}
