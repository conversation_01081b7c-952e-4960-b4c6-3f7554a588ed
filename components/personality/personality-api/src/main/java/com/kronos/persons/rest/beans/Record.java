package com.kronos.persons.rest.beans;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.record.apimodel.description", title = "record")
public class Record {

	@Schema(description = "@v1.0.record.apimodelproperty.LightPersonInformationBean.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private LightPersonInformationBean lightPersonInformation;

	public LightPersonInformationBean getLightPersonInformation() {
		return lightPersonInformation;
	}

	public void setLightPersonInformation(LightPersonInformationBean lightPersonInformation) {
		this.lightPersonInformation = lightPersonInformation;
	}

	
	
}
