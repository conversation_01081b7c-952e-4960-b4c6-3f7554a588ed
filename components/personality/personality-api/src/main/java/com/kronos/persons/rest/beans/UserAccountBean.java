package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.useraccountbean.apimodel.description", name = "userAccount")
@JsonPropertyOrder(alphabetic = true)
public class UserAccountBean {
    @Schema(description = "@v1.0.useraccountbean.apimodelproperty.consecutivebadlogons.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long consecutiveBadLogons;

	@Schema(description = "@v1.0.useraccountbean.apimodelproperty.identityid.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String identityId;

	@Schema(description = "@v1.0.useraccountbean.apimodelproperty.lockoutresetdatetime.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String lockoutResetDateTime;

    @Schema(description = "@v1.0.useraccountbean.apimodelproperty.logonprofilename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String logonProfileName;

    @Schema(description = "@v1.0.useraccountbean.apimodelproperty.passwordupdateddatetime.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String passwordUpdatedDateTime;

    @Schema(description = "@v1.0.useraccountbean.apimodelproperty.passwordupdateflag.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean passwordUpdateFlag;

    @Schema(description = "@v1.0.useraccountbean.apimodelproperty.username.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userName;

	@Schema(description = "@v1.0.useraccountbean.apimodelproperty.ssousername.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String ssoUsername;

    @Schema(description = "@v1.0.useraccountbean.apimodelproperty.userpassword.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userPassword;

    @Schema(description = "@v1.0.useraccountbean.apimodelproperty.mfarequired.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Boolean mfaRequired;
    
    @Schema(description = "@v1.0.useraccountbean.apimodelproperty.clockonlyuser.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Boolean clockOnlyUser;

	/**
	 *@deprecated This attribute will no longer be for use.
	 */
	@Deprecated
	@Schema(description = "@v1.0.useraccountbean.apimodelproperty.accountlocked.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Boolean accountLocked;

	/*
     * Added field to update if autheType has changed
     */
    @JsonIgnore
    private boolean isDeltaAuthType;
    
    @JsonIgnore
    private Long authTypeId;

	public Long getConsecutiveBadLogons() {
		return consecutiveBadLogons;
	}

	public void setConsecutiveBadLogons(Long consecutiveBadLogons) {
		this.consecutiveBadLogons = consecutiveBadLogons;
	}

	public String getIdentityId() {
		return identityId;
	}

	public void setIdentityId(String identityId) {
		this.identityId = identityId;
	}

	public String getLockoutResetDateTime() {
		return lockoutResetDateTime;
	}

	public void setLockoutResetDateTime(String lockoutResetDateTime) {
		this.lockoutResetDateTime = lockoutResetDateTime;
	}

	public String getLogonProfileName() {
		return logonProfileName;
	}

	public void setLogonProfileName(String logonProfileName) {
		this.logonProfileName = logonProfileName;
	}

	public String getPasswordUpdatedDateTime() {
		return passwordUpdatedDateTime;
	}

	public void setPasswordUpdatedDateTime(String passwordUpdatedDateTime) {
		this.passwordUpdatedDateTime = passwordUpdatedDateTime;
	}

	public Boolean getPasswordUpdateFlag() {
		return passwordUpdateFlag;
	}

	public void setPasswordUpdateFlag(Boolean passwordUpdateFlag) {
		this.passwordUpdateFlag = passwordUpdateFlag;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getSsoUsername() {
		return ssoUsername;
	}

	public void setSsoUsername(String ssoUsername) {
		this.ssoUsername = ssoUsername;
	}

	public String getUserPassword() {
		return userPassword;
	}

	public void setUserPassword(String userPassword) {
		this.userPassword = userPassword;
	}

	public Boolean getMfaRequired() {
		return mfaRequired;
	}

	public void setMfaRequired(Boolean mfaRequired) {
		this.mfaRequired = mfaRequired;
	}
	
	public Boolean getClockOnlyUser() {
		return clockOnlyUser;
	}

	public void setClockOnlyUser(Boolean clockOnlyUser) {
		this.clockOnlyUser = clockOnlyUser;
	}
	
	@JsonIgnore
	public boolean isDeltaAuthType() {
		return isDeltaAuthType;
	}

	public void setDeltaAuthType(boolean isDeltaAuthType) {
		this.isDeltaAuthType = isDeltaAuthType;
	}
	
	@JsonIgnore
	public Long getAuthTypeId() {
		return authTypeId;
	}

	public void setAuthTypeId(Long authTypeId) {
		this.authTypeId = authTypeId;
	}

	public Boolean getAccountLocked() {return accountLocked;}

	public void setAccountLocked(Boolean accountLocked) {this.accountLocked = accountLocked;}

	@Override
	public String toString() {
		return "UserAccountBean [consecutiveBadLogons=" + consecutiveBadLogons
				+ ", identityId=" + identityId
				+ ", lockoutResetDateTime=" + lockoutResetDateTime
				+ ", logonProfileName=" + logonProfileName
				+ ", passwordUpdatedDateTime=" + passwordUpdatedDateTime
				+ ", passwordUpdateFlag=" + passwordUpdateFlag
				+ ", userName=" + userName + ", userPassword=" + userPassword
				+ ", accountLocked=" + accountLocked
				+ ", mfaRequired=" + mfaRequired +", clockOnlyUser=" + clockOnlyUser
				+ ", ssoUsername=" + ssoUsername +"]";
	}
}