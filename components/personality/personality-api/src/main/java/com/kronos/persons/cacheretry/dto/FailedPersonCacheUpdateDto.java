package com.kronos.persons.cacheretry.dto;

import java.io.Serializable;
import java.util.Objects;

public class FailedPersonCacheUpdateDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long personId;

    private String eventType;

    public FailedPersonCacheUpdateDto() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPersonId() {
        return personId;
    }

    public void setPersonId(Long personId) {
        this.personId = personId;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public static FailedPersonCacheUpdateDto newInstance(Long personId, String eventType) {

        Objects.requireNonNull(personId);
        Objects.requireNonNull(eventType);

        FailedPersonCacheUpdateDto dto = new FailedPersonCacheUpdateDto();
        dto.setPersonId(personId);
        dto.setEventType(eventType);
        return dto;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        FailedPersonCacheUpdateDto that = (FailedPersonCacheUpdateDto) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (!personId.equals(that.personId)) return false;
        return eventType.equals(that.eventType);
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + personId.hashCode();
        result = 31 * result + eventType.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return String.format("FailedPersonCacheUpdateDto {personId=%d, eventType=%s}", personId, eventType);
    }
}
