package com.kronos.persons.rest.assignments.model.oneview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;

@ApiModel(description = "@v1.0.oneviewpaygrouprest.apimodel.description", value = "oneViewPayGroupRest")
public class OneViewPayGroupRest {

    @ApiModelProperty(value = "@v1.0.oneviewpaygrouprest.apimodelproperty.id.description", required = false)
    private Long id;

    @ApiModelProperty(value = "@v1.0.oneviewpaygrouprest.apimodelproperty.name.description", required = true)
    private String name;

    @ApiModelProperty(value = "@v1.0.oneviewpaygrouprest.apimodelproperty.effectiveDate.description", required = true)
    private LocalDate effectiveDate;

    @ApiModelProperty(value = "@v1.0.oneviewpaygrouprest.apimodelproperty.expirationDate.description", required = true)
    private LocalDate expirationDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDate getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDate effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public LocalDate getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDate expirationDate) {
        this.expirationDate = expirationDate;
    }
}
