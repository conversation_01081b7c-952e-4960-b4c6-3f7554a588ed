package com.kronos.persons.rest.beans;

import com.kronos.commonapp.orgmap.setup.model.OrgObjectRef;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.orgnodesbean.apimodel.description", name = "orgNodes")
public class OrgNodesBean {
	@Schema(description = "@v1.0.orgnodesbean.apimodelproperty.locationtype.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private OrgObjectRef locationType;
	
	@Schema(description = "@v1.0.orgnodesbean.apimodelproperty.attribute.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String attribute;
	
	@Schema(description = "@v1.0.orgnodesbean.apimodelproperty.value.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String value;

	/**
	 * @return the locationType
	 */
	public OrgObjectRef getLocationType() {
		return locationType;
	}

	/**
	 * @param locationType the locationType to set
	 */
	public void setLocationType(OrgObjectRef locationType) {
		this.locationType = locationType;
	}

	/**
	 * @return the attribute
	 */
	public String getAttribute() {
		return attribute;
	}

	/**
	 * @param attribute the attribute to set
	 */
	public void setAttribute(String attribute) {
		this.attribute = attribute;
	}

	/**
	 * @return the value
	 */
	public String getValue() {
		return value;
	}

	/**
	 * @param value the value to set
	 */
	public void setValue(String value) {
		this.value = value;
	}
}
