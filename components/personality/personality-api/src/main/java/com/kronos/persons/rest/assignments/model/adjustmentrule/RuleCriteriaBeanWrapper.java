package com.kronos.persons.rest.assignments.model.adjustmentrule;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.RuleCriteriaBean;
import com.kronos.persons.rest.model.BeanWrapper;

/**
 * Implementation of {@link BeanWrapper} for {@link RuleCriteriaBean}
 * Contain bean and exception wat was thrown in time of validation if bean not valid.
 * Copyright (C) 2019 Kronos.com
 * Date: Nov 13, 2019
 *
 * <AUTHOR>
 */
public class RuleCriteriaBeanWrapper implements BeanWrapper<RuleCriteriaBean> {

    private RuleCriteriaBean wrapperObject;

    private APIException apiException;

    /**
     * Constructor.
     *
     * @param bean instance of {@link RuleCriteriaBean}
     */
    public RuleCriteriaBeanWrapper(RuleCriteriaBean bean) {
        this.wrapperObject = bean;
    }

    @Override
    public APIException getApiException() {
        return apiException;
    }

    @Override
    public void setApiException(APIException apiException) {
        this.apiException = apiException;
    }

    @Override
    public RuleCriteriaBean getBean() {
        return wrapperObject;
    }

    @Override
    public void setBean(RuleCriteriaBean wrapperObject) {
        this.wrapperObject = wrapperObject;
    }
}
