package com.kronos.persons.rest.assignments.service;

import java.util.List;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

import com.kronos.persons.rest.assignments.model.EmployeeJobPreferencesBean;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;
import com.kronos.authz.api.annotations.IsPermitted;



@OpenAPIDefinition(tags = {
		@Tag(description = "@v1.0.commons-persons-job_preferences.description", extensions = @Extension(properties = {
				@ExtensionProperty(name = "parent", value = "@root.personassignments.default") }), name = "@v1.0.commons-persons-job_preferences.name") })
@Tag(name =  "@v1.0.commons-persons-job_preferences.name")
@Publishable(type = RestAPITypes.PUBLIC, value = true, productType = ProductTypes.WAT)
@Path("/v1/commons/persons/job_preferences")
public interface IRestEmployeeJobPreferences {
	
	public static final String JOB_PREFERENCES_AND_SCHEDULING_CONTEXT = "JOB_PREFERENCES_AND_SCHEDULING_CONTEXT";
	 
	public static final String EDIT = "EDIT";

	public static final String VIEW = "VIEW";
	
	@PUT
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(tags = "@v1.0.commons-persons-job_preferences.put.currentpath.nickname", description = "@v1.0.commons-persons-job_preferences.put.currentpath.notes", summary = "@v1.0.commons-persons-job_preferences.put.currentpath.summary")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.put.currentpath.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = EmployeeJobPreferencesBean.class))),
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.put.currentpath.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.put.currentpath.response.500.message", responseCode = "500") })
	@Path("/")
	@IsPermitted(accessControlPoint =JOB_PREFERENCES_AND_SCHEDULING_CONTEXT, action =EDIT)
	public EmployeeJobPreferencesBean update(EmployeeJobPreferencesBean requestBean,@Parameter(description = "@v1.0.commons-persons-job_preferences.put.currentpath.queryparam.updatePartial.value")
			@QueryParam(value = "updatePartial") Boolean updatePartial);

	@GET
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(tags = "@v1.0.commons-persons-job_preferences.get.{personid}.nickname", description = "@v1.0.commons-persons-job_preferences.get.{personid}.notes",summary = "@v1.0.commons-persons-job_preferences.get.{personid}.summary")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.get.{personid}.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = EmployeeJobPreferencesBean.class))),
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.get.{personid}.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.get.{personid}.response.500.message", responseCode = "500")})
	@Path(value = "/{personId:[0-9]+}")
	@IsPermitted(accessControlPoint =JOB_PREFERENCES_AND_SCHEDULING_CONTEXT, action =VIEW)
	public EmployeeJobPreferencesBean retrievebyPersonId(@Parameter(description = "@v1.0.commons-persons-job_preferences.get.{personid}.pathparam.personid.value") @PathParam(value = "personId") Long personId);

	@GET
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(tags = "@v1.0.commons-persons-job_preferences.get.currentpath.nickname", description = "@v1.0.commons-persons-job_preferences.get.currentpath.notes",summary = "@v1.0.commons-persons-job_preferences.get.currentpath.summary")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.get.currentpath.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = EmployeeJobPreferencesBean.class))),
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.get.currentpath.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.get.currentpath.response.500.message", responseCode = "500")})
	@Path(value = "/")
	@IsPermitted(accessControlPoint =JOB_PREFERENCES_AND_SCHEDULING_CONTEXT, action =VIEW)
	public EmployeeJobPreferencesBean retrieve(@Parameter(required = true, description = "@v1.0.commons-persons-job_preferences.get.currentpath.queryparam.person_number.value") @QueryParam(value = "person_number") String personNumber,
											   @Parameter(required = false, description = "@v1.0.commons-persons-job_preferences.get.currentpath.queryparam.full_job_path.value") @QueryParam(value = "full_job_path") Boolean fullJobPath);

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(tags = "@v1.0.commons-persons-job_preferences.post.multi_read.nickname",  description = "@v1.0.commons-persons-job_preferences.post.multi_read.notes", summary = "@v1.0.commons-persons-job_preferences.post.multi_read.summary")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.post.multi_read.response.200.message",responseCode = "200",  content = @Content(schema = @Schema(implementation = EmployeeJobPreferencesBean.class))),
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.post.multi_read.response.207.message", responseCode = "207"),
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.post.multi_read.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.post.multi_read.response.500.message", responseCode = "500")})
	@Path("/multi_read")
	@IsPermitted(accessControlPoint =JOB_PREFERENCES_AND_SCHEDULING_CONTEXT, action =VIEW)
	public List<EmployeeJobPreferencesBean> multiRead(ExtensionSearchCriteria searchCriteria);

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(tags = "@v1.0.commons-persons-job_preferences.post.multi_update.nickname", description = "@v1.0.commons-persons-job_preferences.get.multi_update.notes", summary = "@v1.0.commons-persons-job_preferences.post.multi_update.summary")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.post.multi_update.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = EmployeeJobPreferencesBean.class))),
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.post.multi_update.response.207.message", responseCode = "207"),
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.post.multi_update.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-job_preferences.post.multi_update.response.500.message", responseCode = "500")})
	@Path("/multi_update")
	@IsPermitted(accessControlPoint =JOB_PREFERENCES_AND_SCHEDULING_CONTEXT, action =EDIT)
	public List<EmployeeJobPreferencesBean> multiUpdate(List<EmployeeJobPreferencesBean> requestBean,
			@Parameter(description = "@v1.0.commons-persons-job_preferences.post.multi_update.queryparam.updatePartial.value") @QueryParam(value = "updatePartial") Boolean updatePartial);

}
