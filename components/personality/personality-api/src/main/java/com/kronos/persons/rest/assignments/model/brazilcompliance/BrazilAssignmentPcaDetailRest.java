package com.kronos.persons.rest.assignments.model.brazilcompliance;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.brazilassignmentpcadetailrest.apimodel.description", name = "brazilAssignmentPcaDetailRest")
@JsonPropertyOrder({"payCodeAttribute", "effectiveDate"})
public class BrazilAssignmentPcaDetailRest  {

    @Schema(description = "@v1.0.brazilassignmentpcadetailrest.apimodelproperty.payCodeAttribute.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private ObjectRef payCodeAttribute;

    @Schema(description = "@v1.0.brazilassignmentpcadetailrest.apimodelproperty.effectiveDate.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate effectiveDate;

    public ObjectRef getPayCodeAttribute() {
        return payCodeAttribute;
    }

    public void setPayCodeAttribute(ObjectRef payCodeAttribute) {
        this.payCodeAttribute = payCodeAttribute;
    }

    public LocalDate getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDate effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

}

