/*
 * *****************************************************************************
 * Copyright (c) 2020 Kronos, Inc. All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * Kronos, Inc. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with Kronos.
 *
 * KRONOS MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE
 * SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
 * PURPOSE, OR NON-INFRINGEMENT. KRONOS SHALL NOT BE LIABLE FOR ANY DAMAGES
 * SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING
 * THIS SOFTWARE OR ITS DERIVATIVES.
 ****************************************************************************
 *
 **/
package com.kronos.persons.supportapi.dto;

/**
 * This class contains out of sync details for and employee
 * 
 * <AUTHOR>
 */
public class OutOfSyncAttribute {

	private String attributeName;
	private String valueInDb;
	private String valueInRedisOrOnHeap;
	
	
	public OutOfSyncAttribute(String attributeName, String valueInDb, String valueInRedisOrOnHeap) {
		super();
		this.attributeName = attributeName;
		this.valueInDb = valueInDb;
		this.valueInRedisOrOnHeap = valueInRedisOrOnHeap;
	}
	
	public OutOfSyncAttribute() {
		
	}
	public String getAttributeName() {
		return attributeName;
	}
	public void setAttributeName(String attributeName) {
		this.attributeName = attributeName;
	}
	public String getValueInDb() {
		return valueInDb;
	}
	public void setValueInDb(String valueInDb) {
		this.valueInDb = valueInDb;
	}
	public String getValueInRedisOrOnHeap() {
		return valueInRedisOrOnHeap;
	}
	public void setValueInRedisOrOnHeap(String valueInRedisOrOnHeap) {
		this.valueInRedisOrOnHeap = valueInRedisOrOnHeap;
	}
	@Override
	public String toString() {
		return "OutOfSyncAttribute [attributeName=" + attributeName + ", valueInDb=" + valueInDb
				+ ", valueInRedisOrOnHeap=" + valueInRedisOrOnHeap + "]";
	}
	
	
}
