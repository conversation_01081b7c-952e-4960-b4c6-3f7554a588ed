package com.kronos.persons.rest.model.wageoverride;

import com.kronos.wfc.commonapp.people.business.person.wageoverride.WageWorkRuleOverride;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Holder for store WageWorkRuleOverride for saving and
 * WageWorkRuleOverrideDetails for deleting after calculating overrides for the employees.
 * <p>
 * WageWorkRuleOverridePersistHolder.
 * Copyright (C) 2020 Kronos.com
 * Date: May 12, 2020
 *
 * <AUTHOR>
 */
public class WageWorkRuleOverridePersistHolder {
    private final List<WageWorkRuleOverride> overridesToSave = new ArrayList<>();
    private final List<WageWorkRuleOverride> overridesToDelete = new ArrayList<>();

    /**
     * Add Collection of WageWorkRuleOverrides for delete.
     *
     * @param details collections of {@link WageWorkRuleOverride}
     */
    public void addOverridesToDelete(Collection<WageWorkRuleOverride> details) {
        overridesToDelete.addAll(details);
    }

    /**
     * Add WageWorkRuleOverride for save or update.
     *
     * @param wageWorkRuleOverride the wageWorkRuleOverride
     */
    public void addOverrideToSave(WageWorkRuleOverride wageWorkRuleOverride) {
        overridesToSave.add(wageWorkRuleOverride);
    }

    /**
     * @return List of WageWorkRuleOverride
     */
    public List<WageWorkRuleOverride> getOverridesToSave() {
        return overridesToSave;
    }

    /**
     * @return List of WageWorkRuleOverrideDetails
     */
    public List<WageWorkRuleOverride> getOverridesToDelete() {
        return overridesToDelete;
    }

    /**
     * Is holder contains overrides to save or delete.
     *
     * @return true if collections "overrides to save" or "overrides to delete" are not empty.
     */
    public boolean notEmpty() {
        return !overridesToSave.isEmpty() || !overridesToDelete.isEmpty();
    }
}
