package com.kronos.persons.rest.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * DTO to hold effective dated Wage and Work Rules.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * <p>
 * Date: April 1, 2020
 *
 * <AUTHOR>
 */
@Schema(name = "EffectiveDatedWageWorkRules",
        description = "@v1.0.commons-persons-wage_work_rules.effectivedatedwageworkrules.apimodel.description")
public class EffectiveDatedWageWorkRulesDTO {

    @Schema(description = "@v1.0.commons-persons-wage_work_rules.effectivedatedwageworkrules.apimodelproperty.effectivedate.description")
    private String effectiveDate;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.effectivedatedwageworkrules.apimodelproperty.expirationdate.description")
    private String expirationDate;

    @Schema(description = "@v1.0.commons-persons-wage_work_rules.effectivedatedwageworkrules.apimodelproperty.totalelements.description")
    @JsonProperty(access = Access.READ_ONLY)
    @JsonInclude(Include.NON_NULL)
    private Integer totalElements;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.effectivedatedwageworkrules.apimodelproperty.basewage.description")
    @JsonInclude(Include.NON_NULL)
    private String baseWage;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.effectivedatedwageworkrules.apimodelproperty.baseworkrule.description")
    @JsonInclude(Include.NON_NULL)
    private ObjectRef baseWorkRule;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.effectivedatedwageworkrules.apimodelproperty.deletebasewage.description")
    @JsonInclude(Include.NON_NULL)
    private Boolean deleteBaseWage;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.effectivedatedwageworkrules.apimodelproperty.deletebaseworkrule.description")
    @JsonInclude(Include.NON_NULL)
    private Boolean deleteBaseWorkRule;
    @Schema(description = "@v1.0.commons-persons-wage_work_rules.effectivedatedwageworkrules.apimodelproperty.wageworkrules.description")
    private List<PositionWageWorkRuleDTO> wageWorkRules;

    /**
     * @return effective date
     */
    public String getEffectiveDate() {
        return effectiveDate;
    }

    /**
     * Sets effective date.
     *
     * @param effectiveDate effective date, to be set
     */
    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    /**
     * @return expiration date
     */
    public String getExpirationDate() {
        return expirationDate;
    }

    /**
     * Sets expiration date.
     *
     * @param expirationDate expiration date, to be set
     */
    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    /**
     * @return list of {@link PositionWageWorkRuleDTO}
     */
    public List<PositionWageWorkRuleDTO> getWageWorkRules() {
        return wageWorkRules;
    }

    /**
     * Sets list of {@link PositionWageWorkRuleDTO}.
     *
     * @param wageWorkRules list of {@link PositionWageWorkRuleDTO}, to be set
     */
    public void setWageWorkRules(List<PositionWageWorkRuleDTO> wageWorkRules) {
        this.wageWorkRules = wageWorkRules;
    }

    /**
     * @return total number of existing elements
     */
    public Integer getTotalElements() {
        return totalElements;
    }

    /**
     * Sets total number of existing elements.
     *
     * @param totalElements total number of existing elements, to be set
     */
    public void setTotalElements(Integer totalElements) {
        this.totalElements = totalElements;
    }

    /**
     * @return base wage
     */
    public String getBaseWage() {
        return baseWage;
    }

    /**
     * Sets base wage.
     *
     * @param baseWage base wage
     */
    public void setBaseWage(String baseWage) {
        this.baseWage = baseWage;
    }

    /**
     * @return base work rule
     */
    public ObjectRef getBaseWorkRule() {
        return baseWorkRule;
    }

    /**
     * Sets base work rule.
     *
     * @param baseWorkRule base work rule
     */
    public void setBaseWorkRule(ObjectRef baseWorkRule) {
        this.baseWorkRule = baseWorkRule;
    }

    /**
     * @return delete base wage flag
     */
    public Boolean getDeleteBaseWage() { return deleteBaseWage; }

    /**
     * Sets delete base wage flag.
     *
     * @param deleteBaseWage delete base wage flag
     */
    public void setDeleteBaseWage(Boolean deleteBaseWage) { this.deleteBaseWage = deleteBaseWage; }

    /**
     * @return delete base work rule flag
     */
    public Boolean getDeleteBaseWorkRule() { return deleteBaseWorkRule; }

    /**
     * Sets delete base work rule flag.
     *
     * @param deleteBaseWorkRule delete base work rule flag
     */
    public void setDeleteBaseWorkRule(Boolean deleteBaseWorkRule) { this.deleteBaseWorkRule = deleteBaseWorkRule; }
}
