package com.kronos.persons.rest.beans.extensions;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedEntry;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.personalovertimeassignmentdataextension.apimodel.description", name = "personalOvertimeAssignment", allOf = EffectiveDatedEntry.class)
@JsonPropertyOrder(alphabetic = true)
public class PersonalOvertimeAssignmentDataExtension extends EffectiveDatedEntry {
    private static final long serialVersionUID = 1L;

    @Schema(description = "@v1.0.personalovertimeassignmentdataextension.apimodelproperty.overtimelevel.description")
    private Long overtimeLevel;

    @Schema(description = "@v1.0.personalovertimeassignmentdataextension.apimodelproperty.overtimetype.description")
    private String overtimeType;

    @Schema(description = "@v1.0.personalovertimeassignmentdataextension.apimodelproperty.overtimetypeid.description")
    private Long overtimeTypeId;

    @Schema(description = "@v1.0.personalovertimeassignmentdataextension.apimodelproperty.personalovertimerule.description")
	private PersonalOvertimeRuleDataExtension personalOvertimeRule;

	private Boolean stopOvertimeFlag;

	public Long getOvertimeLevel() {
		return overtimeLevel;
	}

	public void setOvertimeLevel(Long overtimeLevel) {
		this.overtimeLevel = overtimeLevel;
	}

	public String getOvertimeType() {
		return overtimeType;
	}

	public void setOvertimeType(String overtimeType) {
		this.overtimeType = overtimeType;
	}

	public Long getOvertimeTypeId() {
		return overtimeTypeId;
	}

	public void setOvertimeTypeId(Long overtimeTypeId) {
		this.overtimeTypeId = overtimeTypeId;
	}

	public PersonalOvertimeRuleDataExtension getPersonalOvertimeRule() {
		return personalOvertimeRule;
	}

	public void setPersonalOvertimeRule(PersonalOvertimeRuleDataExtension personalOvertimeRule) {
		this.personalOvertimeRule = personalOvertimeRule;
	}

	public Boolean getStopOvertimeFlag() {
		return stopOvertimeFlag;
	}

	public void setStopOvertimeFlag(Boolean stopOvertimeFlag) {
		this.stopOvertimeFlag = stopOvertimeFlag;
	}

}
