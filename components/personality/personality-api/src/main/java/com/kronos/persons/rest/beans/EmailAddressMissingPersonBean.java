package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.emailaddressmissingpersonbean.apimodel.description", title = "EmailAddressMissingPerson")
@JsonPropertyOrder(alphabetic = true)
public class EmailAddressMissingPersonBean {
    @Schema(description = "@v1.0.emailaddressmissingpersonbean.apimodelproperty.address.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String address;

    @Schema(description = "@v1.0.emailaddressmissingpersonbean.apimodelproperty.contacttype.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String contactType;

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getContactType() {
		return contactType;
	}

	public void setContactType(String contactType) {
		this.contactType = contactType;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((address == null) ? 0 : address.hashCode());
		result = prime * result + ((contactType == null) ? 0 : contactType.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		EmailAddressMissingPersonBean other = (EmailAddressMissingPersonBean) obj;
		return checkEquality(other);
	}

	private boolean checkEquality(EmailAddressMissingPersonBean other) {
		if (address == null) {
			if (other.address != null)
				return false;
		} else if (!address.equals(other.address))
			return false;
		return checkEqualityEctended(other);
		
	}

	private boolean checkEqualityEctended(EmailAddressMissingPersonBean other) {
		if (contactType == null) {
			if (other.contactType != null)
				return false;
		} else if (!contactType.equals(other.contactType))
			return false;
		return true;
	}

    

}
