package com.kronos.persons.rest.model;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.employeerefbean.apimodel.description", name = "employeeReference")
@JsonPropertyOrder(alphabetic = true)
public class EmployeeRefBean {
    @Schema(description = "@v1.0.employeerefbean.apimodelproperty.id.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    Long id;

    @Schema(description = "@v1.0.employeerefbean.apimodelproperty.qualifier.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	String qualifier;
	
	/**
	 * @return the id
	 */
	public Long getId() {
		return id;
	}
	/**
	 * @param id the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}
	/**
	 * @return the qualifier
	 */
	public String getQualifier() {
		return qualifier;
	}
	/**
	 * @param qualifier the qualifier to set
	 */
	public void setQualifier(String qualifier) {
		this.qualifier = qualifier;
	}
	
}
