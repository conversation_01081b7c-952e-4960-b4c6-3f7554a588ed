package com.kronos.persons.rest.model;

import com.kronos.persons.context.model.SimpleTimeZone;
import com.kronos.persons.rest.beans.extensions.ContactDataExtension;
import com.kronos.persons.rest.beans.extensions.PostalAddressDataExtension;
import java.util.Collection;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * <AUTHOR>
 */
@Schema(description = "@v1.0.userinfobean.apimodel.description", name = "currentUserInfoModel")
public class UserInfoBean {

    @Schema(description = "@v1.0.userinfobean.apimodelproperty.employeeid.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private long employeeId;
    @Schema(description = "@v1.0.userinfobean.apimodelproperty.firstname.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String firstName;
    @Schema(description = "@v1.0.userinfobean.apimodelproperty.lastname.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String lastName;
    @Schema(description = "@v1.0.userinfobean.apimodelproperty.middleinitial.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String middleInitial;
    @Schema(description = "@v1.0.userinfobean.apimodelproperty.username.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userName;
    @Schema(description = "@v1.0.userinfobean.apimodelproperty.userlocale.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userLocale;
    @Schema(description = "@v1.0.userinfobean.apimodelproperty.timezone.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private SimpleTimeZone timeZone;
    @Schema(description = "@v1.0.userinfobean.apimodelproperty.personnumber.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String personNumber;
   @Schema(description = "@v1.0.userinfobean.apimodelproperty.postaladdresses.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
   private Collection<PostalAddressDataExtension> postalAddresses;
   @Schema(description = "@v1.0.userinfobean.apimodelproperty.emailcontacts.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
   private Collection<ContactDataExtension> emailContacts;
   @Schema(description = "@v1.0.userinfobean.apimodelproperty.telephonecontacts.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
   private Collection<ContactDataExtension> telephoneContacts;

   public Collection<PostalAddressDataExtension> getPostalAddresses() {
      return postalAddresses;
   }

   public void setPostalAddresses(Collection<PostalAddressDataExtension> postalAddresses) {
      this.postalAddresses = postalAddresses;
   }

   public Collection<ContactDataExtension> getEmailContacts() {
      return emailContacts;
   }

   public void setEmailContacts(Collection<ContactDataExtension> emailContacts) {
      this.emailContacts = emailContacts;
   }

   public Collection<ContactDataExtension> getTelephoneContacts() {
      return telephoneContacts;
   }

   public void setTelephoneContacts(Collection<ContactDataExtension> telephoneContacts) {
      this.telephoneContacts = telephoneContacts;
   }

	public long getEmployeeId() {
        return this.employeeId;
    }

    public void setEmployeeId(long employeeId) {
        this.employeeId = employeeId;
    }

    public String getFirstName() {
        return this.firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return this.lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getMiddleInitial() {
        return this.middleInitial;
    }

    public void setMiddleInitial(String middleInitial) {
        this.middleInitial = middleInitial;
    }

    public String getUserName() {
        return this.userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserLocale() {
        return this.userLocale;
    }

    public void setUserLocale(String userLocale) {
        this.userLocale = userLocale;
    }

    public SimpleTimeZone getTimeZone() {
        return this.timeZone;
    }

    public void setTimeZone(SimpleTimeZone timeZone) {
        this.timeZone = timeZone;
    }

    public String getPersonNumber() {
        return this.personNumber;
    }

    public void setPersonNumber(String personNumber) {
        this.personNumber = personNumber;
    }

    @Override
    public boolean equals(Object object) {
        if(this == object) return true;
        if(!(object instanceof UserInfoBean)) return false;

        UserInfoBean objectItem = (UserInfoBean) object;

        return new EqualsBuilder()
                .append(getEmployeeId(), objectItem.getEmployeeId())
                .append(getFirstName(), objectItem.getFirstName())
                .append(getLastName(), objectItem.getLastName())
                .append(getMiddleInitial(), objectItem.getMiddleInitial())
                .append(getUserName(), objectItem.getUserName())
                .append(getUserLocale(), objectItem.getUserLocale())
                .append(getTimeZone(), objectItem.getTimeZone())
                .append(hashCode(), objectItem.hashCode())
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder()
                .append(getEmployeeId())
                .append(getFirstName())
                .append(getLastName())
                .append(getMiddleInitial())
                .append(getUserName())
                .append(getUserLocale())
                .append(getTimeZone())
                .hashCode();
    }

}