package com.kronos.persons.rest.model;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CriteriaBean implements Serializable {

    private static final long serialVersionUID = 1L;

    private String extensionType;

    private String snapshotDate;

    private Boolean onlyActivePerson = false;

    private String searchBy;

    private List<SearchValues> searchValue;

    /**
     * @return the extensionType
     */
    public String getExtensionType() {
        return extensionType;
    }

    /**
     * @param extensionType
     *            the extensionType to set
     */
    public void setExtensionType(String extensionType) {
        this.extensionType = extensionType;
    }

    /**
     * @return the snapshotDate
     */
    public String getSnapshotDate() {
        return snapshotDate;
    }

    /**
     * @param snapshotDate
     *            the snapshotDate to set
     */
    public void setSnapshotDate(String snapshotDate) {
        this.snapshotDate = snapshotDate;
    }

    /**
     * @return the onlyActivePerson
     */
    public Boolean getOnlyActivePerson() {
        return onlyActivePerson;
    }

    /**
     * @param onlyActivePerson
     *            the onlyActivePerson to set
     */
    public void setOnlyActivePerson(Boolean onlyActivePerson) {
        this.onlyActivePerson = onlyActivePerson;
    }

    /**
     * @return the searchBy
     */
    public String getSearchBy() {
        return searchBy;
    }

    /**
     * @param searchBy
     *            the searchBy to set
     */
    public void setSearchBy(String searchBy) {
        this.searchBy = searchBy;
    }

    /**
     * @return the searchValue
     */
    public List<SearchValues> getSearchValue() {
        return searchValue;
    }

    /**
     * @param searchValue
     *            the searchValue to set
     */
    public void setSearchValue(List<SearchValues> searchValue) {
        this.searchValue = searchValue;
    }

}
