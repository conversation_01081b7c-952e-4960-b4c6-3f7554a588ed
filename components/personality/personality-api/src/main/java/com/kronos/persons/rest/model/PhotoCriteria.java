package com.kronos.persons.rest.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;



public class PhotoCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    private String searchBy;
    private List<String> searchValue;

    private List<String> multiKey;
    private List<SearchValues> multiKeyValues;
    
    private LocalDateTime modifiedSinceDateTime;
    
    public PhotoCriteria() {
    	
    }
    
    public PhotoCriteria(String searchBy,List<String> searchValue,List<String> multiKey,List<SearchValues> multiKeyValues) {
    	this.searchBy=searchBy;
    	this.searchValue=searchValue;
    	this.multiKey=multiKey;
    	this.multiKeyValues=multiKeyValues;
    }

	public String getSearchBy() {
		return searchBy;
	}

	public void setSearchBy(String searchBy) {
		this.searchBy = searchBy;
	}

	public List<String> getSearchValue() {
		return searchValue;
	}

	public void setSearchValue(List<String> searchValue) {
		this.searchValue = searchValue;
	}

	public List<String> getMultiKey() {
		return multiKey;
	}

	public void setMultiKey(List<String> multiKey) {
		this.multiKey = multiKey;
	}

	public List<SearchValues> getMultiKeyValues() {
		return multiKeyValues;
	}

	public void setMultiKeyValues(List<SearchValues> multiKeyValues) {
		this.multiKeyValues = multiKeyValues;
	}

	public LocalDateTime getModifiedSinceDateTime() {
		return modifiedSinceDateTime;
	}

	public void setModifiedSinceDateTime(LocalDateTime modifiedSinceDateTime) {
		this.modifiedSinceDateTime = modifiedSinceDateTime;
	}
	
	@Override
	public int hashCode() {
		return Objects.hash(modifiedSinceDateTime, multiKey, multiKeyValues, searchBy, searchValue);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PhotoCriteria other = (PhotoCriteria) obj;
		return Objects.equals(modifiedSinceDateTime, other.modifiedSinceDateTime)
				&& Objects.equals(multiKey, other.multiKey) && Objects.equals(multiKeyValues, other.multiKeyValues)
				&& Objects.equals(searchBy, other.searchBy) && Objects.equals(searchValue, other.searchValue);
	}
}
