/***********************************************************************
 * ICurrentUser.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.persons.context.service;

import com.kronos.persons.context.model.SimpleTimeZone;

/**
 * The {@code ICurrentUser} is the interface which defines current user
 * information with different APIs.
 * 
 * <AUTHOR>
 *
 */

public interface ICurrentUser {
 
	/**
	 * This API gets the userId.
	 * 
	 * @return the {@link Long} userId.
	 */
   public long getUserId();

	/**
	 * This API gets the first name of the user.
	 * 
	 * @return the {@link String} firstName.
	 */
   public String getFirstName();
   
	/**
	 * This API gets the last name of the user.
	 * 
	 * @return the {@link String} lastName.
	 */
   public String getLastName();
   
	/**
	 * This API gets the middle initials of the user.
	 * 
	 * @return the {@link String} middleInitial.
	 */
   public String getMiddleInitial();
   
	/**
	 * This API gets whether the user is in manager role.
	 * 
	 * @return the {@link Boolean} isInManagerRole.
	 */
   public boolean isInManagerRole();
   
	/**
	 * This API gets currency code mapped with user.
	 * 
	 * @return the {@link String} currencyCode.
	 */
   public String getCurrencyCode();
   
	/**
	 * This API gets the locale mapped with user.
	 * 
	 * @return the {@link String} user locale
	 */
   public String getUserLocale();
   
	/**
	 * This API gets the timezone mapped with user.
	 * 
	 * @return the {@link SimpleTimeZone} timeZone
	 */
   public SimpleTimeZone getTimeZone();
   
	/**
	 * This API gets the employee Id.
	 * 
	 * @return the {@link Long} employeeId
	 */
   public long getEmployeeId();
   
	/**
	 * This API gets the locale profile Id.
	 * 
	 * @return the {@link Long} locale profileId
	 */
   public long getLocaleProfileId();

	/**
	 * This API check whether employee has WTK license or not.
	 * 
	 * @return the {@link Boolean} Employee WTK License Flag
	 */
   public boolean getHasWTKLicense();
   
    /**
	 * This API gets the tenant external name .
	 * 
	 * @return the {@link String} tenant externalName
	 */
    public String getTenantExternalName();
   
     /**
      * This API returns vendor name .
      *  
      * @return the {@link String } Vendor Name
      */
      public String getVendorName();

	/**
	 * This API returns isAuth0Login flag.
	 *
	 * @return the {@link boolean } isAuth0Login flag
	 */
	public boolean isAuth0Login();

}
