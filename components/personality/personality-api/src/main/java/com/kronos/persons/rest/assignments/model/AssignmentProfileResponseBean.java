package com.kronos.persons.rest.assignments.model;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.model.RestErrorBean;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "assignmentProfileResponse", description = "@v1.0.personcascadeprofileassignmentresponsebean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class AssignmentProfileResponseBean {

	@Schema(description = "@v1.0.personcascadeprofileassignmentresponsebean.apimodelproperty.personnumber.description")
	private PersonIdentityBean personIdentity;

	@Schema(description = "@v1.0.personcascadeprofileassignmentresponsebean.apimodelproperty.error.description")
	private RestErrorBean error;

	@Schema(description = "@v1.0.personcascadeprofileassignmentresponsebean.apimodelproperty.assignmentprofile.description")
	String assignmentProfile;

	/**
	 * @return the personIdentity
	 */
	public PersonIdentityBean getPersonIdentity() {
		return personIdentity;
	}

	/**
	 * @param personIdentity
	 *            the personIdentity to set
	 */
	public void setPersonIdentity(PersonIdentityBean personIdentity) {
		this.personIdentity = personIdentity;
	}

	/**
	 * @return the error
	 */
	public RestErrorBean getError() {
		return error;
	}

	/**
	 * @param error
	 *            the error to set
	 */
	public void setError(RestErrorBean error) {
		this.error = error;
	}

	/**
	 * @return the assignmentProfile
	 */
	public String getAssignmentProfile() {
		return assignmentProfile;
	}

	/**
	 * @param assignmentProfile
	 *            the assignmentProfile to set
	 */
	public void setAssignmentProfile(String assignmentProfile) {
		this.assignmentProfile = assignmentProfile;
	}

}
