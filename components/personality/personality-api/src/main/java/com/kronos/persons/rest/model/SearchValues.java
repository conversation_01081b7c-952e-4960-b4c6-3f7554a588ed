package com.kronos.persons.rest.model;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.searchvalues.apimodel.description", name = "searchValues")
public class SearchValues {
	
	
	@Schema(description = "@v1.0.searchvalues.apimodelproperty.aoid.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String aoid;

	@Schema(description = "@v1.0.searchvalues.apimodelproperty.coid.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String coid;

	/**
	 * @return the aoid
	 */
	public String getAoid() {
		return aoid;
	}

	/**
	 * @param aoid the aoid to set
	 */
	public void setAoid(String aoid) {
		this.aoid = aoid;
	}

	/**
	 * @return the coid
	 */
	public String getCoid() {
		return coid;
	}

	/**
	 * @param coid the coid to set
	 */
	public void setCoid(String coid) {
		this.coid = coid;
	}
	

}
