package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * author <PERSON><PERSON><PERSON> <PERSON>
 */

@Schema(description = "@v1.0.PositionCodeBean.apimodel.description", name = "PositionCode", allOf = HrPositionCodeObjectRef.class)
@JsonPropertyOrder(alphabetic = true)
public class PositionCodeBean extends HrPositionCodeObjectRef {

    public PositionCodeBean(Long id, String name, String startDate, String endDate) {
        this.id = id;
        this.name = name;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public PositionCodeBean() {
    }
}
