package com.kronos.persons.rest.assignments.model;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.kronos.persons.rest.beans.PeopleAssignmentBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

@Schema(title = "supervisorAssignmentRequest", description = "@v1.0.supervisorassignmentrequestbean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class SupervisorAssignmentRequestBean extends PeopleAssignmentBean implements BaseAssignmentBean{


	@Schema(description = "@v1.0.supervisorassignmentbean.apimodelproperty.supervisorName.description")
	private String supervisorName;
	@Schema(description = "@v1.0.supervisorassignmentbean.apimodelproperty.supervisorPersonNumber.description")
	private String supervisorPersonNumber;

	public void setSupervisorName(String supervisorName) {
		this.supervisorName = supervisorName;
	}

	public String getSupervisorPersonNumber() {
		return supervisorPersonNumber;
	}

	public void setSupervisorPersonNumber(String supervisorPersonNumber) {
		this.supervisorPersonNumber = supervisorPersonNumber;
	}

	public String getSupervisorName() {
		return supervisorName;
	}


	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		SupervisorAssignmentRequestBean that = (SupervisorAssignmentRequestBean) o;
		return new EqualsBuilder()
				.append(supervisorName, that.supervisorName)
				.append(supervisorPersonNumber, that.supervisorPersonNumber)
				.appendSuper(super.equals(o))
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder()
				.append(supervisorName)
				.append(supervisorPersonNumber)
				.appendSuper(super.hashCode())
				.toHashCode();
	}

	@JsonGetter("personidentity")
	public PersonIdentityBean getPersonIdentity() {
		return super.getPersonIdentity();
	}

	@JsonSetter("personidentity")
	public void setPersonIdentity(PersonIdentityBean personIdentity) {
		super.setPersonIdentity(personIdentity);
	}
}
