package com.kronos.persons.rest.model;

import java.time.LocalDateTime;


import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "dateTimeRange", title = "@v1.0.daterange.apimodel.description")
public class DateRange {

	@Schema(description = "@v1.0.daterange.apimodelproperty.startDateTime.description")
	private LocalDateTime startDateTime;

	@Schema(description = "@v1.0.daterange.apimodelproperty.endDateTime.description")
	private LocalDateTime endDateTime;

	public LocalDateTime getStartDateTime() {
		return startDateTime;
	}

	public void setStartDateTime(LocalDateTime startDateTime) {
		this.startDateTime = startDateTime;
	}

	public LocalDateTime getEndDateTime() {
		return endDateTime;
	}

	public void setEndDateTime(LocalDateTime endDateTime) {
		this.endDateTime = endDateTime;
	}

}
