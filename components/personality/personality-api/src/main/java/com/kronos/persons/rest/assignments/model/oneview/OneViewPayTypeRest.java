package com.kronos.persons.rest.assignments.model.oneview;

import com.kronos.persons.EmployeeAssignmentsConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.util.Objects;

@ApiModel(description = "@v1.0.oneviewpaytyperest.apimodel.description", value = "oneViewPayTypeRest")
public class OneViewPayTypeRest {

    @ApiModelProperty(value = "@v1.0.oneviewpaytyperest.apimodelproperty.id.description", required = false)
    private Long id;

    @ApiModelProperty(value = "@v1.0.oneviewpaytyperest.apimodelproperty.name.description", required = true)
    private String name;

    @ApiModelProperty(value = "@v1.0.oneviewpaytyperest.apimodelproperty.effectiveDate.description", required = true)
    private LocalDate effectiveDate;

    @ApiModelProperty(value = "@v1.0.oneviewpaytyperest.apimodelproperty.expirationDate.description", required = true)
    private LocalDate expirationDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDate getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDate effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public LocalDate getExpirationDate() {
        if(Objects.isNull(expirationDate)){
            expirationDate = EmployeeAssignmentsConstants.FOREVER_DATE;
        }
        return expirationDate;
    }

    public void setExpirationDate(LocalDate expirationDate) {
        this.expirationDate = expirationDate;
    }
}
