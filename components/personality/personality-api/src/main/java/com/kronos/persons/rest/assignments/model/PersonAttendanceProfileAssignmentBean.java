package com.kronos.persons.rest.assignments.model;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "attendanceProfileAssignment", description = "@v1.0.personattendanceprofileassignmentbean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class PersonAttendanceProfileAssignmentBean {
	@Schema(description = "@v1.0.personattendanceprofileassignmentbean.apimodelproperty.profileName.description")
	private String profileName;
	@Schema(description = "@v1.0.personattendanceprofileassignmentbean.apimodelproperty.effectiveDate.description")
	private LocalDate effectiveDate;

	public String getProfileName() {
		return profileName;
	}

	public void setProfileName(String profileName) {
		this.profileName = profileName;
	}

	public LocalDate getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(LocalDate effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

}
