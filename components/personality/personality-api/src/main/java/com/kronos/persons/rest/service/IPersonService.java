package com.kronos.persons.rest.service;

import java.util.List;

import com.kronos.persons.rest.beans.LightWeightPersonBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.extensions.ExtensionResponseBean;
import com.kronos.persons.rest.model.EmployeeReferences;
import com.kronos.persons.rest.model.ExtensionCriteria;
import com.kronos.persons.rest.model.PersonalityBean;
import com.kronos.wfc.commonapp.people.business.personality.Personality;

/**
 * <AUTHOR>
 */
public interface IPersonService {

    /**
	 * Method to retrieve the specific extension information based on person
	 * number and extension type.
	 * <ul>
	 * <li>Allowed values for extension type:</li>
	 * <ul>
	 * <li>accrual</li>
	 * <li>device</li>
	 * <li>employee</li>
	 * <li>scheduling</li>
	 * <li>timekeeping</li>
	 * </ul>
	 * </ul>
	 * 
	 * @param personNumber
	 *            The {@link String} object
	 * @param extensionType
	 *            The {@link String} object
	 * @param includeAccrualPolicyDetails
	 *            whether to include accrual policies in response or not
	 * @return The {@link ExtensionResponseBean} object
	 */
	public ExtensionResponseBean getExtensionByPersonNumber(String personNumber, String extensionType, boolean includeAccrualPolicyDetails, String includeBaseWages);

	/**
	 * Method to retrieve all extensions information based on person number.
	 * 
	 * @param personNumber
	 *            The {@link String} object
	 * @param includeAccrualPolicyDetails
	 *            whether to include accrual policies in response or not
	 * @return The {@link ExtensionResponseBean} object
	 */
	public ExtensionResponseBean getAllExtensionByPersonNumber(String personNumber, boolean includeAccrualPolicyDetails, String includeBaseWages);

    /**
     * Method to retrieve the extension information in key-value format, based
     * on input criteria.
     * 
     * @param extensionCriteria
     *            The {@link ExtensionCriteria} object
     * @return The List<{@link ExtensionResponseBean}> object
     */
    public List<ExtensionResponseBean> getExtensionsByCriteria(ExtensionCriteria extensionCriteria);

    /**
     * Method to retrieve the base person information including Person Number,
     * Person Id, Full Name and Display Name, based on input criteria.
     * 
     * @param extensionCriteria
     *            The {@link ExtensionCriteria} object
     * @return The List<{@link LightWeightPersonBean}> object
     */
    public List<LightWeightPersonBean> getBasePersonData(ExtensionCriteria extensionCriteria);

    /**
     * Method to retrieve the employee references based on input criteria.
     * 
     * @param extensionCriteria
     *            The {@link ExtensionCriteria} object
     * @return The {@link EmployeeReferences} object
     */
    public EmployeeReferences getEmployeeRefs(ExtensionCriteria extensionCriteria);

    /**
     * Method to retrieve the person data in bean format based on personId.
     * 
     * @param personId
     *            The {@link Long} object
     * @return The {@link PersonalityBean} object
     */
    public PersonalityBean getPersonDataByPersonId(Long personId);

    /**
     * Method to create the personality.
     * 
     * @param personalityBean
     *            The {@link PersonalityBean} object
     * @return The {@link Personality} object
     */
    public Personality create(PersonalityBean personalityBean);

    /**
     * Method to update the personality based on personId.
     * 
     * @param personId
     *            The {@link Long} object
     * @param personalityBean
     *            The {@link PersonalityBean} object
     * @return The {@link Personality} object
     */
    public Personality updateByPersonId(Long personId, PersonalityBean personalityBean);

    /**
     * Method to update the personality based on personIdentity
     * {@link PersonIdentityBean} provided in {@link PersonalityBean}.
     * 
     * @param personalityBean
     *            The {@link PersonalityBean} object
     * @return The {@link Personality} object
     */
    public Personality updateByPersonIdentity(PersonalityBean personalityBean);

    /**
     * Method to delete the personality based on personId.
     * 
     * @param personId
     *            The {@link Long} object
     */
    public void deleteByPersonId(Long personId);

    /**
     * Method to delete the personality based on the passed multiple personIds.
     * 
     * @param personIds
     *            The List of {@link Long} object
     */
    public void deleteByMultiplePersonId(List<Long> personIds);
    
    /**
     * Method to delete the personality based on personIdentity
     * {@link PersonIdentityBean} provided in {@link PersonalityBean}.
     * 
     * @param personalityBean
     *            The {@link PersonalityBean} object
     */
    public void deleteByPersonIdentity(PersonalityBean personalityBean);
    
    
    
	/**
	 * Method to retrieve person number in case aoid/coid is provided. If person
	 * number is passed the method returns the person number as it is.
	 * 
	 * 
	 * @param aoid
	 *            -The Associate Object ID (AOID) of a person in the system
	 * @param coid
	 *            The Client Object ID (COID) of a person in the system.
	 * @param personNumber
	 *            A unique identifier for a person. This is not an employee ID.
	 * @return personNumber A unique identifier for a person. This is not an
	 *         employee ID.
	 */
	public default String retrievePersonNumberByAOID(String aoid, String coid, String personNumber) {
		return null;
	}
    
}
