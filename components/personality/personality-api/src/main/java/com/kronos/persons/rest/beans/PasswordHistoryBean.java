/**
 * 
 */
package com.kronos.persons.rest.beans;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.passwordhistorybean.apimodel.description", name = "passwordHistory")
@JsonPropertyOrder(alphabetic = true)
public class PasswordHistoryBean {
    @Schema(description = "@v1.0.passwordhistorybean.apimodelproperty.effectivedatetime.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime effectiveDateTime;

    @Schema(description = "@v1.0.passwordhistorybean.apimodelproperty.password.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String password;

	/**
	 * @return the effectiveDateTime
	 */
	public LocalDateTime getEffectiveDateTime() {
		return effectiveDateTime;
	}

	/**
	 * @param effectiveDateTime
	 *            the effectiveDateTime to set
	 */
	public void setEffectiveDateTime(LocalDateTime effectiveDateTime) {
		this.effectiveDateTime = effectiveDateTime;
	}

	/**
	 * @return the password
	 */
	public String getPassword() {
		return password;
	}

	/**
	 * @param password
	 *            the password to set
	 */
	public void setPassword(String password) {
		this.password = password;
	}

}
