package com.kronos.persons.rest.beans;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.personinformationbean.apimodel.description", name = "personInformation")
@JsonPropertyOrder(alphabetic = true)
public class PersonInformationBean {
    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.accessassignment.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private AccessAssignmentBean accessAssignment;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.badgeassignments.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<BadgeAssignmentBean> badgeAssignments;

	@JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.employeecurrencyassignment.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private EmployeeCurrencyAssignmentBean employeeCurrencyAssignment;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.employmentstatuslist.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<EmploymentStatusBean> employmentStatusList;
    
    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.analyticslabortypelist.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<EmploymentAnalyticsLaborTypeBean> analyticsLaborTypeList;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.expectedhourslist.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<ExpectedHoursBean> expectedHoursList;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.personaccessassignments.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<PersonAccessAssignmentBean> personAccessAssignments;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.personauthenticationtypes.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<PersonAuthenticationTypeBean> personAuthenticationTypes;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.person.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private PersonBean person;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.personlicensetypes.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<PersonLicenseTypeBean> personLicenseTypes;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.postaladdresses.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<PostalAddressBean> postalAddresses;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.telephonenumbers.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<TelephoneNumberBean> telephoneNumbers;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.useraccountstatuslist.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<UserAccountStatusBean> userAccountStatusList;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.emailaddresses.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<EmailAddressBean> emailAddresses;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.approverlist.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<ApproverBean> approverList;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.customdatalist.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<CustomDataBean> customDataList;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.customdatelist.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<CustomDateBean> customDateList;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.kpassaccount.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private KPassAccountBean kpassAccount;

    @Schema(description = "@v1.0.personinformationbean.apimodelproperty.purposeassignments.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private List<PurposeAssignmentBean> purposeAssignments;

	@Schema(description = "@v1.0.personinformationbean.apimodelproperty.workemployee.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private WorkEmployeeBean workEmployee;


	@Schema(description = "@v1.0.personinformationbean.apimodelproperty.hrPositionCodes.description")
	private List<PositionCodeBean> hrPositionCodes;


	/**
	 * @return the accessAssignment
	 */
	public AccessAssignmentBean getAccessAssignment() {
		return accessAssignment;
	}

	/**
	 * @param accessAssignment
	 *            the accessAssignment to set
	 */
	public void setAccessAssignment(AccessAssignmentBean accessAssignment) {
		this.accessAssignment = accessAssignment;
	}

	/**
	 * @return the badgeAssignments
	 */
	public List<BadgeAssignmentBean> getBadgeAssignments() {
		return badgeAssignments;
	}

	/**
	 * @param badgeAssignments
	 *            the badgeAssignments to set
	 */
	public void setBadgeAssignments(List<BadgeAssignmentBean> badgeAssignments) {
		this.badgeAssignments = badgeAssignments;
	}

	/**
	 * @return the employeeCurrencyAssignment
	 */
	public EmployeeCurrencyAssignmentBean getEmployeeCurrencyAssignment() {
		return employeeCurrencyAssignment;
	}

	/**
	 * @param employeeCurrencyAssignment
	 *            the employeeCurrencyAssignment to set
	 */
	public void setEmployeeCurrencyAssignment(EmployeeCurrencyAssignmentBean employeeCurrencyAssignment) {
		this.employeeCurrencyAssignment = employeeCurrencyAssignment;
	}

	/**
	 * @return the employmentStatusList
	 */
	public List<EmploymentStatusBean> getEmploymentStatusList() {
		return employmentStatusList;
	}

	/**
	 * @param employmentStatusList
	 *            the employmentStatusList to set
	 */
	public void setEmploymentStatusList(List<EmploymentStatusBean> employmentStatusList) {
		this.employmentStatusList = employmentStatusList;
	}

	/**
	 * @return the expectedHoursList
	 */
	public List<ExpectedHoursBean> getExpectedHoursList() {
		return expectedHoursList;
	}

	/**
	 * @param expectedHoursList
	 *            the expectedHoursList to set
	 */
	public void setExpectedHoursList(List<ExpectedHoursBean> expectedHoursList) {
		this.expectedHoursList = expectedHoursList;
	}

	/**
	 * @return the personAccessAssignments
	 */
	public List<PersonAccessAssignmentBean> getPersonAccessAssignments() {
		return personAccessAssignments;
	}

	/**
	 * @param personAccessAssignments
	 *            the personAccessAssignments to set
	 */
	public void setPersonAccessAssignments(List<PersonAccessAssignmentBean> personAccessAssignments) {
		this.personAccessAssignments = personAccessAssignments;
	}

	/**
	 * @return the personAuthenticationTypes
	 */
	public List<PersonAuthenticationTypeBean> getPersonAuthenticationTypes() {
		return personAuthenticationTypes;
	}

	/**
	 * @param personAuthenticationTypes
	 *            the personAuthenticationTypes to set
	 */
	public void setPersonAuthenticationTypes(List<PersonAuthenticationTypeBean> personAuthenticationTypes) {
		this.personAuthenticationTypes = personAuthenticationTypes;
	}

	/**
	 * @return the person
	 */
	public PersonBean getPerson() {
		return person;
	}

	/**
	 * @param person
	 *            the person to set
	 */
	public void setPerson(PersonBean person) {
		this.person = person;
	}

	/**
	 * @return the personLicenseTypes
	 */
	public List<PersonLicenseTypeBean> getPersonLicenseTypes() {
		return personLicenseTypes;
	}

	/**
	 * @param personLicenseTypes
	 *            the personLicenseTypes to set
	 */
	public void setPersonLicenseTypes(List<PersonLicenseTypeBean> personLicenseTypes) {
		this.personLicenseTypes = personLicenseTypes;
	}

	/**
	 * @return the postalAddresses
	 */
	public List<PostalAddressBean> getPostalAddresses() {
		return postalAddresses;
	}

	/**
	 * @param postalAddresses
	 *            the postalAddresses to set
	 */
	public void setPostalAddresses(List<PostalAddressBean> postalAddresses) {
		this.postalAddresses = postalAddresses;
	}

	/**
	 * @return the telephoneNumbers
	 */
	public List<TelephoneNumberBean> getTelephoneNumbers() {
		return telephoneNumbers;
	}

	/**
	 * @param telephoneNumbers
	 *            the telephoneNumbers to set
	 */
	public void setTelephoneNumbers(List<TelephoneNumberBean> telephoneNumbers) {
		this.telephoneNumbers = telephoneNumbers;
	}

	/**
	 * @return the userAccountStatusList
	 */
	public List<UserAccountStatusBean> getUserAccountStatusList() {
		return userAccountStatusList;
	}

	/**
	 * @param userAccountStatusList
	 *            the userAccountStatusList to set
	 */
	public void setUserAccountStatusList(List<UserAccountStatusBean> userAccountStatusList) {
		this.userAccountStatusList = userAccountStatusList;
	}

	/**
	 * @return the emailAddresses
	 */
	public List<EmailAddressBean> getEmailAddresses() {
		return emailAddresses;
	}

	/**
	 * @param emailAddresses
	 *            the emailAddresses to set
	 */
	public void setEmailAddresses(List<EmailAddressBean> emailAddresses) {
		this.emailAddresses = emailAddresses;
	}

	/**
	 * @return the approverList
	 */
	public List<ApproverBean> getApproverList() {
		return approverList;
	}

	/**
	 * @param approverList
	 *            the approverList to set
	 */
	public void setApproverList(List<ApproverBean> approverList) {
		this.approverList = approverList;
	}

	/**
	 * @return the customDataList
	 */
	public List<CustomDataBean> getCustomDataList() {
		return customDataList;
	}

	/**
	 * @param customDataList
	 *            the customDataList to set
	 */
	public void setCustomDataList(List<CustomDataBean> customDataList) {
		this.customDataList = customDataList;
	}

	/**
	 * @return the customDateList
	 */
	public List<CustomDateBean> getCustomDateList() {
		return customDateList;
	}

	/**
	 * @param customDateList
	 *            the customDateList to set
	 */
	public void setCustomDateList(List<CustomDateBean> customDateList) {
		this.customDateList = customDateList;
	}

	/**
	 * @return the kpassAccount
	 */
	public KPassAccountBean getKpassAccount() {
		return kpassAccount;
	}

	/**
	 * @param kpassAccount
	 *            the kpassAccount to set
	 */
	public void setKpassAccount(KPassAccountBean kpassAccount) {
		this.kpassAccount = kpassAccount;
	}

	/**
	 * @return the purposeAssignments
	 */
	public List<PurposeAssignmentBean> getPurposeAssignments() {
		return purposeAssignments;
	}

	/**
	 * @param purposeAssignments
	 *            the purposeAssignments to set
	 */
	public void setPurposeAssignments(List<PurposeAssignmentBean> purposeAssignments) {
		this.purposeAssignments = purposeAssignments;
	}
	
	public List<EmploymentAnalyticsLaborTypeBean> getAnalyticsLaborTypeList() {
		return analyticsLaborTypeList;
	}

	public void setAnalyticsLaborTypeList(List<EmploymentAnalyticsLaborTypeBean> analyticsLaborTypeList) {
		this.analyticsLaborTypeList = analyticsLaborTypeList;
	}

	public WorkEmployeeBean getWorkEmployee() {
		return workEmployee;
	}

	public void setWorkEmployee(WorkEmployeeBean workEmployee) {
		this.workEmployee = workEmployee;
	}

	/**
	 *
	 * @return position codes
	 */
	public List<PositionCodeBean> getHrPositionCodes() {
		return hrPositionCodes;
	}

	/**
	 *
	 * @param hrPositionCodes sets position code
	 */
	public void setHrPositionCodes(List<PositionCodeBean> hrPositionCodes) {
		this.hrPositionCodes = hrPositionCodes;
	}
}
