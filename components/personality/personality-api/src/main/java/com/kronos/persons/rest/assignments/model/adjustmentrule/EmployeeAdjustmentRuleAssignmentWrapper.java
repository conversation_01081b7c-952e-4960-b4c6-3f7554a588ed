package com.kronos.persons.rest.assignments.model.adjustmentrule;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.model.BeanWrapper;

/**
 * EmployeeAdjustmentRuleAssignmentWrapper.
 *
 * Implementation of {@link BeanWrapper} for bean
 * instance of {@link EmployeeAdjustmentRuleAssignmentBean}
 *
 * Copyright (C) 2019 Kronos.com
 * Date: Jun 17, 2019
 *
 * <AUTHOR>
 */
public class EmployeeAdjustmentRuleAssignmentWrapper implements
        BeanWrapper<EmployeeAdjustmentRuleAssignmentBean> {

    private EmployeeAdjustmentRuleAssignmentBean wrapperObject;

    private APIException apiException;

    /**
     * Constructor.
     *
     * @param employeeAdjustmentRuleAssignmentBean instance of {@link EmployeeAdjustmentRuleAssignmentBean}
     */
    public EmployeeAdjustmentRuleAssignmentWrapper(EmployeeAdjustmentRuleAssignmentBean
                                                           employeeAdjustmentRuleAssignmentBean) {
        this.wrapperObject = employeeAdjustmentRuleAssignmentBean;
    }

    @Override
    public APIException getApiException() {
        return apiException;
    }

    @Override
    public void setApiException(APIException apiException) {
        this.apiException = apiException;
    }

    @Override
    public EmployeeAdjustmentRuleAssignmentBean getBean() {
        return wrapperObject;
    }

    @Override
    public void setBean(EmployeeAdjustmentRuleAssignmentBean wrapperObject) {
        this.wrapperObject = wrapperObject;
    }
}
