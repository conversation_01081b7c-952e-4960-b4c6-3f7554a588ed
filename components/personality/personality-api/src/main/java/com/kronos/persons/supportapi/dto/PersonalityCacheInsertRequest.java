package com.kronos.persons.supportapi.dto;

import com.kronos.wfc.platform.properties.framework.KronosProperties;

import java.util.ArrayList;
import java.util.List;

public class PersonalityCacheInsertRequest {
    private List<PersonalityInsertRequestData> personalityInsertList = new ArrayList<>();

    public List<PersonalityInsertRequestData> getTenantPersonalityRequestData() {
        return personalityInsertList;
    }

    public void setTenantPersonalityRequestData(List<PersonalityInsertRequestData> tenantPersonalityRequestData) {
        this.personalityInsertList = tenantPersonalityRequestData;
    }

    @Override
    public String toString() {
        return "PersonalityCacheEvictRequest [tenantPersonalityRequestData=" + personalityInsertList + "]";
    }
}
