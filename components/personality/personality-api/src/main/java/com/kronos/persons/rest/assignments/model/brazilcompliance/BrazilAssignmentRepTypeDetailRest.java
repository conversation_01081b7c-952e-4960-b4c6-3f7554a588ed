package com.kronos.persons.rest.assignments.model.brazilcompliance;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;


@Schema(description = "@v1.0.brazilassignmentreptypedetailrest.apimodel.description", name = "brazilAssignmentRepTypeDetailRest")
@JsonPropertyOrder({"repType", "unionAgreementNumber", "effectiveDate"})
public class BrazilAssignmentRepTypeDetailRest {

    @Schema(description = "@v1.0.brazilassignmentreptypedetailrest.apimodelproperty.repType.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private ObjectRef repType;

    @Schema(description = "@v1.0.brazilassignmentreptypedetailrest.apimodelproperty.unionAgreementNumber.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String unionAgreementNumber;

    @Schema(description = "@v1.0.brazilassignmentreptypedetailrest.apimodelproperty.effectiveDate.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate effectiveDate;

    public ObjectRef getRepType() {
        return repType;
    }

    public void setRepType(ObjectRef repType) {
        this.repType = repType;
    }

    public String getUnionAgreementNumber() {
        return unionAgreementNumber;
    }

    public void setUnionAgreementNumber(String unionAgreementNumber) {
        this.unionAgreementNumber = unionAgreementNumber;
    }

    public LocalDate getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDate effectiveDate) {
        this.effectiveDate = effectiveDate;
    }
}
