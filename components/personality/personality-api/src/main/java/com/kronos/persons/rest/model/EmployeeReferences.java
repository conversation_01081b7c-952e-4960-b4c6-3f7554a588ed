package com.kronos.persons.rest.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.employeereferences.apimodel.description", name = "employeeReferences")
@JsonPropertyOrder(alphabetic = true)
public class EmployeeReferences {
    @Schema(description = "@v1.0.employeereferences.apimodelproperty.ids.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    List<Long> ids;

    @Schema(description = "@v1.0.employeereferences.apimodelproperty.qualifiers.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    List<String> qualifiers;

    @Schema(description = "@v1.0.employeereferences.apimodelproperty.refs.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	List<EmployeeRefBean> refs;
	
	/**
	 * @return the ids
	 */
	public List<Long> getIds() {
		return ids;
	}
	/**
	 * @param ids the ids to set
	 */
	public void setIds(List<Long> ids) {
		this.ids = ids;
	}
	/**
	 * @return the qualifiers
	 */
	public List<String> getQualifiers() {
		return qualifiers;
	}
	/**
	 * @param qualifiers the qualifiers to set
	 */
	public void setQualifiers(List<String> qualifiers) {
		this.qualifiers = qualifiers;
	}
	/**
	 * @return the refs
	 */
	public List<EmployeeRefBean> getRefs() {
		return refs;
	}
	/**
	 * @param refs the refs to set
	 */
	public void setRefs(List<EmployeeRefBean> refs) {
		this.refs = refs;
	}
	
}
