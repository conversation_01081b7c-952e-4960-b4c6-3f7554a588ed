/*
 * *****************************************************************************
 * Copyright (c) 2020 Kronos, Inc. All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * Kronos, Inc. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with Kronos.
 *
 * KRONOS MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE
 * SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
 * PURPOSE, OR NON-INFRINGEMENT. KRONOS SHALL NOT BE LIABLE FOR ANY DAMAGES
 * SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING
 * THIS SOFTWARE OR ITS DERIVATIVES.
 ****************************************************************************
 *
 **/
package com.kronos.persons.rest.supportapi.extensions;

import com.kronos.people.personality.model.extension.AllExtension;

/**
 * This extension builder contains functions for getting extension for caching support APIs
 * 
 * <AUTHOR>
 */
public interface ExtensionBuilderService {

	/**
	 * This method is used to get AllExtension from Database
	 * @param personId
	 * @return
	 */
	public AllExtension buildAllExtensionFromDb(Long personId);
	
	/**
	 * This method is used to get AllExtension from OnHeap/Redis cache
	 * @param personId
	 * @return
	 */
	public AllExtension getAllPersonalityExtensionFromCache(Long personId);
	
}
