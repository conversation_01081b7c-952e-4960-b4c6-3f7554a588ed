package com.kronos.persons.rest.beans;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.recordinfo.apimodel.description", title = "recordInfo")
public class RecordInfo {

	@Schema(description = "@v1.0.recordinfo.apimodelproperty.personid.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long personId;

	@Schema(description = "@v1.0.recordinfo.apimodelproperty.personNumber.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String personNumber;

	@Schema(description = "@v1.0.recordinfo.apimodelproperty.firstName.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String firstName;

	@Schema(description = "@v1.0.recordinfo.apimodelproperty.lastName.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String lastName;

	@Schema(description = "@v1.0.recordinfo.apimodelproperty.employmentStatus.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String employmentStatus = null;

	@Schema(description = "@v1.0.recordinfo.apimodelproperty.userAccountStatus.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String userAccountStatus = null;

	public Long getPersonId() {
		return personId;
	}

	public void setPersonId(Long personId) {
		this.personId = personId;
	}

	public String getPersonNumber() {
		return personNumber;
	}

	public void setPersonNumber(String personNumber) {
		this.personNumber = personNumber;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getEmploymentStatus() {
		return employmentStatus;
	}

	public void setEmploymentStatus(String employmentStatus) {
		this.employmentStatus = employmentStatus;
	}

	public String getUserAccountStatus() {
		return userAccountStatus;
	}

	public void setUserAccountStatus(String userAccountStatus) {
		this.userAccountStatus = userAccountStatus;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}
