package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.emailaddressbean.apimodel.description", name = "emailAddress")
@JsonPropertyOrder(alphabetic = true)
public class EmailAddressBean {
    @Schema(description = "@v1.0.emailaddressbean.apimodelproperty.address.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String address;

    @Schema(description = "@v1.0.emailaddressbean.apimodelproperty.contacttypename.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String contactTypeName;

    @Schema(description = "@v1.0.emailaddressbean.apimodelproperty.hasemailnotificationdelivery.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private Boolean hasEmailNotificationDelivery;

	/**
	 * The object ID of the e-mail address type.
	 */
    @JsonIgnore
	protected ObjectIdLong emailAddressTypeId = null;

	/**
	 * @return the address
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * @param address
	 *            the address to set
	 */
	public void setAddress(String address) {
		this.address = address;
	}

	/**
	 * @return the contactTypeName
	 */
	

	/**
	 * @param contactTypeName
	 *            the contactTypeName to set
	 */
	
	

	/**
	 * @return the hasEmailNotificationDelivery
	 */
	public Boolean getHasEmailNotificationDelivery() {
		return hasEmailNotificationDelivery;
	}

	

	public String getContactTypeName() {
		return contactTypeName;
	}

	public void setContactTypeName(String contactTypeName) {
		this.contactTypeName = contactTypeName;
	}

	/**
	 * @param hasEmailNotificationDelivery
	 *            the hasEmailNotificationDelivery to set
	 */
	public void setHasEmailNotificationDelivery(
			Boolean hasEmailNotificationDelivery) {
		this.hasEmailNotificationDelivery = hasEmailNotificationDelivery;
	}

	/**
	 * @return the emailAddressTypeId
	 */
	public ObjectIdLong getEmailAddressTypeId() {
		return emailAddressTypeId;
	}

	/**
	 * @param emailAddressTypeId
	 *            the emailAddressTypeId to set
	 */
	public void setEmailAddressTypeId(ObjectIdLong emailAddressTypeId) {
		this.emailAddressTypeId = emailAddressTypeId;
	}

}
