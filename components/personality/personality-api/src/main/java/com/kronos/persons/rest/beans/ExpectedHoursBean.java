/**
 * 
 */
package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 *
 */

@Schema(description = "@v1.0.expectedhoursbean.apimodel.description", name = "expectedHoursDetails")
@JsonPropertyOrder(alphabetic = true)
public class ExpectedHoursBean {
    @Schema(description = "@v1.0.expectedhoursbean.apimodelproperty.quantity.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String quantity;

    @Schema(description = "@v1.0.expectedhoursbean.apimodelproperty.timeperiodtypename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String timePeriodTypeName;

	/**
	 * The object ID of the expected hours type.
	 */
	@JsonIgnore
	protected ObjectIdLong expectedHoursTypeId;

	/**
	 * @return the quantity
	 */
	public String getQuantity() {
		return quantity;
	}

	/**
	 * @param quantity
	 *            the quantity to set
	 */
	public void setQuantity(String quantity) {
		this.quantity = quantity;
	}

	/**
	 * @return the timePeriodTypeName
	 */
	public String getTimePeriodTypeName() {
		return timePeriodTypeName;
	}

	/**
	 * @param timePeriodTypeName
	 *            the timePeriodTypeName to set
	 */
	public void setTimePeriodTypeName(String timePeriodTypeName) {
		this.timePeriodTypeName = timePeriodTypeName;
	}

	/**
	 * @return the expectedHoursTypeId
	 */
	public ObjectIdLong getExpectedHoursTypeId() {
		return expectedHoursTypeId;
	}

	/**
	 * @param expectedHoursTypeId
	 *            the expectedHoursTypeId to set
	 */
	public void setExpectedHoursTypeId(ObjectIdLong expectedHoursTypeId) {
		this.expectedHoursTypeId = expectedHoursTypeId;
	}

}
