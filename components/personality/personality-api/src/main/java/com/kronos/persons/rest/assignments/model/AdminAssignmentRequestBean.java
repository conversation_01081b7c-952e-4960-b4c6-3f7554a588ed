package com.kronos.persons.rest.assignments.model;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.kronos.persons.rest.beans.PeopleAssignmentBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "adminAssignmentRequest", description = "@v1.0.adminassignmentrequestbean.apimodel.description")
@JsonPropertyOrder(alphabetic = true)
public class AdminAssignmentRequestBean extends PeopleAssignmentBean implements BaseAssignmentBean{

	
	@Schema(description = "@v1.0.adminassignmentrequestbean.apimodelproperty.administrator.description")
	AdminProfileBean administrator;
   
	/**
	 * @return the administrator
	 */
	public AdminProfileBean getAdministrator() {
		return administrator;
	}

	/**
	 * @param administrator
	 *            the administrator to set
	 */
	public void setAdministrator(AdminProfileBean administrator) {
		this.administrator = administrator;
	}
	
    @JsonGetter("personidentity")
    public PersonIdentityBean getPersonIdentity() {
        return super.getPersonIdentity();
    }

    @JsonSetter("personidentity")
    public void setPersonIdentity(PersonIdentityBean personIdentity) {
        super.setPersonIdentity(personIdentity);
    }

}
