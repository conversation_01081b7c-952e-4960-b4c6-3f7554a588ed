package com.kronos.persons.rest.beans;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
@Schema(description = "@v1.0.hyperfindfilterbean.apimodel.description", title = "hyperfindfilterbean")
public class HyperFindFilterBean {

    @Schema(description = "@v1.0.hyperfindfilterbean.apimodelproperty.hyperfind.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private ObjectRef hyperfind;

    @Schema(description = "@v1.0.hyperfindfilterbean.apimodelproperty.visibilitycode.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String visibilityCode;

    @Schema(description = "@v1.0.hyperfindfilterbean.apimodelproperty.daterange.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateSpan dateRange;

    public ObjectRef getHyperfind() {
        return hyperfind;
    }

    public void setHyperfind(ObjectRef hyperfind) {
        this.hyperfind = hyperfind;
    }

    public String getVisibilityCode() {
        return visibilityCode;
    }

    public void setVisibilityCode(String visibilityCode) {
        this.visibilityCode = visibilityCode;
    }

    public LocalDateSpan getDateRange() {
        return dateRange;
    }

    public void setDateRange(LocalDateSpan dateRange) {
        this.dateRange = dateRange;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("{");
        builder.append("hyperfind:" + getHyperfind() + ", ");
        builder.append("visibilityCode:" + getVisibilityCode() + ", ");
        builder.append("dateRange:" + getDateRange() + " ");
        builder.append("}");
        return builder.toString();
    }

}
