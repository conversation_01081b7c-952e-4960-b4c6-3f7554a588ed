package com.kronos.persons.rest.assignments.service;

import java.util.List;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.kronos.persons.rest.assignments.model.AdminAssignmentRequestBean;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.rest.annotation.Publishable;
import com.kronos.rest.util.ProductTypes;
import com.kronos.rest.util.RestAPITypes;

@OpenAPIDefinition(tags = {
		@Tag(description = "@v1.0.commons-persons-leave_admin.description", extensions = @Extension(properties = {
				@ExtensionProperty(name = "parent", value = "@root.personassignments.default") }), name = "@v1.0.commons-persons-leave_admin.name") })
@Tag(name = "@v1.0.commons-persons-leave_admin.name")
@Publishable(type = RestAPITypes.PUBLIC, value = true, productType = ProductTypes.WFP)
@Path("/v1/commons/persons/leave_admin")
public interface IRestPersonLeaveAdminAssignment {
	
	@GET
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "@v1.0.commons-persons-leave_admin.get.{personid}.value", description = "@v1.0.commons-persons-leave_admin.get.{personid}.notes", tags = "@v1.0.commons-persons-leave_admin.get.{personid}.nickname")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-leave_admin.get.{personid}.response.200.message", content = @Content(schema = @Schema(implementation = AdminAssignmentRequestBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-leave_admin.get.{personid}.response.400.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-leave_admin.get.{personid}.response.500.message")})
	@Path("/{personId:[0-9]+}")
	public AdminAssignmentRequestBean retrieve(
			@Parameter(description = "@v1.0.commons-persons_assignments-leave_admin.get.{personid}.pathparam.personid.value", required = false) @PathParam(value = "personId" ) Long personId
	);
	
	@GET
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "@v1.0.commons-persons-leave_admin.get.currentpath.value", description = "@v1.0.commons-persons-leave_admin.get.currentpath.notes", tags = "@v1.0.commons-persons-leave_admin.get.currentpath.nickname")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-leave_admin.get.currentpath.response.200.message", content = @Content(schema = @Schema(implementation = AdminAssignmentRequestBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-leave_admin.get.currentpath.response.400.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-leave_admin.get.currentpath.response.500.message")})
	@Path("/")
	public AdminAssignmentRequestBean retrieveByPersonNumber(
			@Parameter(description = "@v1.0.commons-persons_assignments-leave_admin.get.currentpath.queryparam.person_number.value", required = false) @QueryParam(value = "person_number") String personNumber
	);


	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Operation(summary = "@v1.0.commons-persons-leave_admin.post.multi_read.name", description = "@v1.0.commons-persons-leave_admin.post.multi_read.notes", tags = "@v1.0.commons-persons-leave_admin.post.multi_read.nickname")
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "@v1.0.commons-persons-leave_admin.post.multi_read.response.200.message",content = @Content(schema = @Schema(type="list", implementation = AdminAssignmentRequestBean.class))),
			@ApiResponse(responseCode = "400", description = "@v1.0.commons-persons-leave_admin.post.multi_read.response.400.message"),
			@ApiResponse(responseCode = "207", description = "@v1.0.commons-persons-leave_admin.post.multi_read.response.207.message"),
			@ApiResponse(responseCode = "500", description = "@v1.0.commons-persons-leave_admin.post.multi_read.response.500.message")})
	@Path("/multi_read")
	public List<AdminAssignmentRequestBean> retrieveList(ExtensionSearchCriteria extensionCriteria);

	@PUT
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@ResponseStatus(code = HttpStatus.OK)
	@Operation(summary = "@v1.0.commons-persons-leave_admin.put.currentpath.summary", description = "@v1.0.commons-persons-leave_admin.put.currentpath.notes", tags = "@v1.0.commons-persons-leave_admin.put.currentpath.nickname")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.put.currentpath.response.200.message", responseCode = "200", content = @Content(schema = @Schema(implementation = AdminAssignmentRequestBean.class))),
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.put.currentpath.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.put.currentpath.response.500.message", responseCode = "500") })
	@Path("/")
	public AdminAssignmentRequestBean update(AdminAssignmentRequestBean requestBean);

	@DELETE
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@ResponseStatus(code = HttpStatus.NO_CONTENT)
	@Operation(summary = "@v1.0.commons-persons-leave_admin.delete.currentpath.summary", description = "@v1.0.commons-persons-leave_admin.delete.currentpath.notes", tags = "@v1.0.commons-persons-leave_admin.delete.currentpath.nickname")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.delete.currentpath.response.204.message", responseCode = "204"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.delete.currentpath.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.delete.currentpath.response.500.message", responseCode = "500") })
	@Path("/")
	public void delete(AdminAssignmentRequestBean requestBean);

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@ResponseStatus(code = HttpStatus.OK)
	@Operation(summary = "@v1.0.commons-persons-leave_admin.post.multi_update.summary", description = "@v1.0.commons-persons-leave_admin.post.multi_update.notes", tags = "@v1.0.commons-persons-leave_admin.post.multi_update.nickname")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.post.multi_update.response.200.message", responseCode = "200", content = @Content(schema = @Schema(type="list", implementation = AdminAssignmentRequestBean.class))),
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.post.multi_update.response.207.message", responseCode = "207"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.post.multi_update.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.post.multi_update.response.500.message", responseCode = "500") })
	@Path("/multi_update")
	public List<AdminAssignmentRequestBean> multiUpdate(List<AdminAssignmentRequestBean> requestBean);

	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@ResponseStatus(code = HttpStatus.NO_CONTENT)
	@Operation(summary = "@v1.0.commons-persons-leave_admin.post.multi_delete.summary", description = "@v1.0.commons-persons-leave_admin.post.multi_delete.notes", tags = "@v1.0.commons-persons-leave_admin.post.multi_delete.nickname")
	@ApiResponses(value = {
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.post.multi_delete.response.204.message", responseCode = "204"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.post.multi_delete.response.207.message", responseCode = "207"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.post.multi_delete.response.400.message", responseCode = "400"),
			@ApiResponse(description = "@v1.0.commons-persons-leave_admin.post.multi_delete.response.500.message", responseCode = "500") })
	@Path("/multi_delete")
	public void multiDelete(List<AdminAssignmentRequestBean> requestBean);

}
