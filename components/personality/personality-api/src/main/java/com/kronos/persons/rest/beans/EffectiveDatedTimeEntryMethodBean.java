/**
 * 
 */
package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 *
 */

@Schema(description = "@v1.0.effectivedatedtimeentrymethodbean.apimodel.description", name = "effectiveDatedTimeEntryMethod")
@JsonPropertyOrder(alphabetic = true)
public class EffectiveDatedTimeEntryMethodBean {
    @Schema(description = "@v1.0.effectivedatedtimeentrymethodbean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String effectiveDate;

    @Schema(description = "@v1.0.effectivedatedtimeentrymethodbean.apimodelproperty.timeentrytypename.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String timeEntryTypeName;

	@Schema(description = "@v1.0.effectivedatedtimeentrymethodbean.apimodelproperty.timeEntryActivityTrackingStatus.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String timeEntryActivityTrackingStatus;

	/**
	 * @return the effectiveDate
	 */
	public String getEffectiveDate() {
		return effectiveDate;
	}

	/**
	 * @param effectiveDate
	 *            the effectiveDate to set
	 */
	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	/**
	 * @return the timeEntryTypeName
	 */
	public String getTimeEntryTypeName() {
		return timeEntryTypeName;
	}

	/**
	 * @param timeEntryTypeName
	 *            the timeEntryTypeName to set
	 */
	public void setTimeEntryTypeName(String timeEntryTypeName) {
		this.timeEntryTypeName = timeEntryTypeName;
	}


	public String getTimeEntryActivityTrackingStatus() {
		return timeEntryActivityTrackingStatus;
	}

	public void setTimeEntryActivityTrackingStatus(String timeEntryActivityTrackingStatus) {
		this.timeEntryActivityTrackingStatus = timeEntryActivityTrackingStatus;
	}
}
