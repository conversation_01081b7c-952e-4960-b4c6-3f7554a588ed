package com.kronos.persons.rest.model;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.photosearchcriteria.apimodel.description", title = "photoSearchCriteria")
public class PhotoSearchCriteria implements Serializable {

    private static final long serialVersionUID = 1L;
    
	@Schema(description = "@v1.0.photosearchcriteria.apimodelproperty.where.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private PhotoSearchWhereCriteria where;

	@Schema(description = "@v1.0.photosearchcriteria.apimodelproperty.multiReadOptions.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private PhotoSearchOption multiReadOptions;

	public PhotoSearchWhereCriteria getWhere() {
		return where;
	}

	public void setWhere(PhotoSearchWhereCriteria where) {
		this.where = where;
	}

	public PhotoSearchOption getMultiReadOptions() {
		return multiReadOptions;
	}

	public void setMultiReadOptions(PhotoSearchOption multiReadOptions) {
		this.multiReadOptions = multiReadOptions;
	}

	@Override
	public String toString() {
		return "PhotoSearchCriteria [where=" + where + ", multiReadOptions=" + multiReadOptions + "]";
	}
    
}
