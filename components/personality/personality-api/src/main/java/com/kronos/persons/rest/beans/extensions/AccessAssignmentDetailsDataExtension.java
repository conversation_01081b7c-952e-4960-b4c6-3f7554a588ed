/**
 * 
 */
package com.kronos.persons.rest.beans.extensions;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;


import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodel.description", name = "CommonBusinessAccessAssignmentDetailsDataExtension")
@JsonPropertyOrder(alphabetic = true)
public class AccessAssignmentDetailsDataExtension implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.canapproveovertime.description")
    private Boolean canApproveOvertime;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.cantransfer.description")
    private Boolean canTransfer;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.laborcategoryprofile.description")
    private String laborCategoryProfile;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.laborcategoryprofileid.description")
    private Long laborCategoryProfileId;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.laborleveltransferset.description")
    private String laborLevelTransferset;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.laborleveltransfersetid.description")
    private Long laborLevelTransfersetId;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.paycodeprofile.description")
    private String payCodeProfile;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.paycodeprofileid.description")
    private Long payCodeProfileId;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.paycodeviewprofile.description")
    private String payCodeViewProfile;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.paycodeviewprofileid.description")
    private Long payCodeViewProfileId;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.reportname.description")
    private String reportName;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.shiftcode.description")
    private String shiftCode;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.sselaborcategoryprofile.description")
    private String sseLaborCategoryProfile;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.sselaborcategoryprofileid.description")
    private Long sseLaborCategoryProfileId;

	@Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.ssemgrlaborcategoryprofile.description")
	private String sseMgrLaborCategoryProfile;

	@Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.ssemgrlaborcategoryprofileid.description")
	private Long sseMgrLaborCategoryProfileId;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.sselaborleveltransferset.description")
    private String sseLaborLevelTransferset;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.sselaborleveltransfersetid.description")
    private Long sseLaborLevelTransfersetId;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.ssepaycode.description")
    private String ssePayCode;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.ssepaycodeid.description")
    private Long ssePayCodeId;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.sseworkruleprofile.description")
    private String sseWorkRuleProfile;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.sseworkruleprofileid.description")
    private Long sseWorkRuleProfileId;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.timeentrytype.description")
    private String timeEntryType;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.workruleprofile.description")
    private String workRuleProfile;

    @Schema(description = "@v1.0.accessassignmentdetailsdataextension.apimodelproperty.workruleprofileid.description")
	private Long workRuleProfileId;

	/**
	 * @return the canApproveOvertime
	 */
	public Boolean isCanApproveOvertime() {
		return canApproveOvertime;
	}

	/**
	 * @param canApproveOvertime
	 *            the canApproveOvertime to set
	 */
	public void setCanApproveOvertime(Boolean canApproveOvertime) {
		this.canApproveOvertime = canApproveOvertime;
	}

	/**
	 * @return the canTransfer
	 */
	public Boolean isCanTransfer() {
		return canTransfer;
	}

	/**
	 * @param canTransfer
	 *            the canTransfer to set
	 */
	public void setCanTransfer(Boolean canTransfer) {
		this.canTransfer = canTransfer;
	}

	/**
	 * @return the laborCategoryProfile
	 */
	public String getLaborCategoryProfile() {
		return laborCategoryProfile;
	}

	/**
	 * @param laborCategoryProfile
	 *            the laborCategoryProfile to set
	 */
	public void setLaborCategoryProfile(String laborCategoryProfile) {
		this.laborCategoryProfile = laborCategoryProfile;
	}

	/**
	 * @return the laborCategoryProfileId
	 */
	public Long getLaborCategoryProfileId() {
		return laborCategoryProfileId;
	}

	/**
	 * @param laborCategoryProfileId
	 *            the laborCategoryProfileId to set
	 */
	public void setLaborCategoryProfileId(Long laborCategoryProfileId) {
		this.laborCategoryProfileId = laborCategoryProfileId;
	}

	/**
	 * @return the laborLevelTransferset
	 */
	public String getLaborLevelTransferset() {
		return laborLevelTransferset;
	}

	/**
	 * @param laborLevelTransferset
	 *            the laborLevelTransferset to set
	 */
	public void setLaborLevelTransferset(String laborLevelTransferset) {
		this.laborLevelTransferset = laborLevelTransferset;
	}

	/**
	 * @return the laborLevelTransfersetId
	 */
	public Long getLaborLevelTransfersetId() {
		return laborLevelTransfersetId;
	}

	/**
	 * @param laborLevelTransfersetId
	 *            the laborLevelTransfersetId to set
	 */
	public void setLaborLevelTransfersetId(Long laborLevelTransfersetId) {
		this.laborLevelTransfersetId = laborLevelTransfersetId;
	}

	/**
	 * @return the payCodeProfile
	 */
	public String getPayCodeProfile() {
		return payCodeProfile;
	}

	/**
	 * @param payCodeProfile
	 *            the payCodeProfile to set
	 */
	public void setPayCodeProfile(String payCodeProfile) {
		this.payCodeProfile = payCodeProfile;
	}

	/**
	 * @return the payCodeProfileId
	 */
	public Long getPayCodeProfileId() {
		return payCodeProfileId;
	}

	/**
	 * @param payCodeProfileId
	 *            the payCodeProfileId to set
	 */
	public void setPayCodeProfileId(Long payCodeProfileId) {
		this.payCodeProfileId = payCodeProfileId;
	}

	/**
	 * @return the payCodeViewProfile
	 */
	public String getPayCodeViewProfile() {
		return payCodeViewProfile;
	}

	/**
	 * @param payCodeViewProfile
	 *            the payCodeViewProfile to set
	 */
	public void setPayCodeViewProfile(String payCodeViewProfile) {
		this.payCodeViewProfile = payCodeViewProfile;
	}

	/**
	 * @return the payCodeViewProfileId
	 */
	public Long getPayCodeViewProfileId() {
		return payCodeViewProfileId;
	}

	/**
	 * @param payCodeViewProfileId
	 *            the payCodeViewProfileId to set
	 */
	public void setPayCodeViewProfileId(Long payCodeViewProfileId) {
		this.payCodeViewProfileId = payCodeViewProfileId;
	}

	/**
	 * @return the reportName
	 */
	public String getReportName() {
		return reportName;
	}

	/**
	 * @param reportName
	 *            the reportName to set
	 */
	public void setReportName(String reportName) {
		this.reportName = reportName;
	}

	/**
	 * @return the shiftCode
	 */
	public String getShiftCode() {
		return shiftCode;
	}

	/**
	 * @param shiftCode
	 *            the shiftCode to set
	 */
	public void setShiftCode(String shiftCode) {
		this.shiftCode = shiftCode;
	}

	/**
	 * @return the sseLaborCategoryProfile
	 */
	public String getSseLaborCategoryProfile() {
		return sseLaborCategoryProfile;
	}

	/**
	 * @param sseLaborCategoryProfile
	 *            the sseLaborCategoryProfile to set
	 */
	public void setSseLaborCategoryProfile(String sseLaborCategoryProfile) {
		this.sseLaborCategoryProfile = sseLaborCategoryProfile;
	}

	/**
	 * @return the sseLaborCategoryProfileId
	 */
	public Long getSseLaborCategoryProfileId() {
		return sseLaborCategoryProfileId;
	}

	/**
	 * @param sseLaborCategoryProfileId
	 *            the sseLaborCategoryProfileId to set
	 */
	public void setSseLaborCategoryProfileId(Long sseLaborCategoryProfileId) {
		this.sseLaborCategoryProfileId = sseLaborCategoryProfileId;
	}

	/**
	 * @return the sseLaborLevelTransferset
	 */
	public String getSseLaborLevelTransferset() {
		return sseLaborLevelTransferset;
	}

	/**
	 * @param sseLaborLevelTransferset
	 *            the sseLaborLevelTransferset to set
	 */
	public void setSseLaborLevelTransferset(String sseLaborLevelTransferset) {
		this.sseLaborLevelTransferset = sseLaborLevelTransferset;
	}

	/**
	 * @return the sseLaborLevelTransfersetId
	 */
	public Long getSseLaborLevelTransfersetId() {
		return sseLaborLevelTransfersetId;
	}

	/**
	 * @param sseLaborLevelTransfersetId
	 *            the sseLaborLevelTransfersetId to set
	 */
	public void setSseLaborLevelTransfersetId(Long sseLaborLevelTransfersetId) {
		this.sseLaborLevelTransfersetId = sseLaborLevelTransfersetId;
	}

	/**
	 * @return the ssePayCode
	 */
	public String getSsePayCode() {
		return ssePayCode;
	}

	/**
	 * @param ssePayCode
	 *            the ssePayCode to set
	 */
	public void setSsePayCode(String ssePayCode) {
		this.ssePayCode = ssePayCode;
	}

	/**
	 * @return the ssePayCodeId
	 */
	public Long getSsePayCodeId() {
		return ssePayCodeId;
	}

	/**
	 * @param ssePayCodeId
	 *            the ssePayCodeId to set
	 */
	public void setSsePayCodeId(Long ssePayCodeId) {
		this.ssePayCodeId = ssePayCodeId;
	}

	/**
	 * @return the sseWorkRuleProfile
	 */
	public String getSseWorkRuleProfile() {
		return sseWorkRuleProfile;
	}

	/**
	 * @param sseWorkRuleProfile
	 *            the sseWorkRuleProfile to set
	 */
	public void setSseWorkRuleProfile(String sseWorkRuleProfile) {
		this.sseWorkRuleProfile = sseWorkRuleProfile;
	}

	/**
	 * @return the sseWorkRuleProfileId
	 */
	public Long getSseWorkRuleProfileId() {
		return sseWorkRuleProfileId;
	}

	/**
	 * @param sseWorkRuleProfileId
	 *            the sseWorkRuleProfileId to set
	 */
	public void setSseWorkRuleProfileId(Long sseWorkRuleProfileId) {
		this.sseWorkRuleProfileId = sseWorkRuleProfileId;
	}

	/**
	 * @return the timeEntryType
	 */
	public String getTimeEntryType() {
		return timeEntryType;
	}

	/**
	 * @param timeEntryType
	 *            the timeEntryType to set
	 */
	public void setTimeEntryType(String timeEntryType) {
		this.timeEntryType = timeEntryType;
	}

	/**
	 * @return the workRuleProfile
	 */
	public String getWorkRuleProfile() {
		return workRuleProfile;
	}

	/**
	 * @param workRuleProfile
	 *            the workRuleProfile to set
	 */
	public void setWorkRuleProfile(String workRuleProfile) {
		this.workRuleProfile = workRuleProfile;
	}

	/**
	 * @return the workRuleProfileId
	 */
	public Long getWorkRuleProfileId() {
		return workRuleProfileId;
	}

	/**
	 * @param workRuleProfileId
	 *            the workRuleProfileId to set
	 */
	public void setWorkRuleProfileId(Long workRuleProfileId) {
		this.workRuleProfileId = workRuleProfileId;
	}

	public String getSseMgrLaborCategoryProfile() {
		return sseMgrLaborCategoryProfile;
	}

	public void setSseMgrLaborCategoryProfile(String sseMgrLaborCategoryProfile) {
		this.sseMgrLaborCategoryProfile = sseMgrLaborCategoryProfile;
	}

	public Long getSseMgrLaborCategoryProfileId() {
		return sseMgrLaborCategoryProfileId;
	}

	public void setSseMgrLaborCategoryProfileId(Long sseMgrLaborCategoryProfileId) {
		this.sseMgrLaborCategoryProfileId = sseMgrLaborCategoryProfileId;
	}
}
