package com.kronos.persons.rest.assignments.model;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;
@Schema(title = "attestationprofileassignment", description = "@v1.0.attestationprofileassignment.apimodel.description")
public class AttestationProfileAssignment {
    @Schema(description = "@v1.0.attestationprofileassignment.apimodelproperty.profile.description")
    private ObjectRef profile;
    @Schema(description = "@v1.0.attestationprofileassignment.apimodelproperty.effectiveDate.description")
    private String effectiveDate;
    @Schema(description = "@v1.0.attestationprofileassignment.apimodelproperty.expirationDate.description")
    private String expirationDate;

    public ObjectRef getProfile() {
        return profile;
    }

    public void setProfile(ObjectRef profile) {
        this.profile = profile;
    }

    public String getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }
}