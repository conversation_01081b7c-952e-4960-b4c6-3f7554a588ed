package com.kronos.persons.rest.assignments.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.extensions.ExceptionBean;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.personcommonidrequest.apimodel.description", title = "personCommonIdenitifier")
public class PersonCommonIdRequest implements ExceptionBean{

	@Schema(description = "@v1.0.personcommonidrequest.apimodelproperty.personidentity.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private PersonIdentityBean personIdentity;

	@Schema(description = "@v1.0.personcommonidrequest.apimodelproperty.coid.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String coid;

	@Schema(description = "@v1.0.personcommonidrequest.apimodelproperty.aoid.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String aoid;

	@JsonIgnore
	private Long personId;

	@Schema(description = "@v1.0.personcommonidrequest.apimodelproperty.error.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private APIException error;

	/**
	 * @return the personIdentity
	 */
	public PersonIdentityBean getPersonIdentity() {
		return personIdentity;
	}

	/**
	 * @param personIdentity the personIdentity to set
	 */
	public void setPersonIdentity(PersonIdentityBean personIdentity) {
		this.personIdentity = personIdentity;
	}

	/**
	 * @return the coid
	 */
	public String getCoid() {
		return coid;
	}

	/**
	 * @param coid the coid to set
	 */
	public void setCoid(String coid) {
		this.coid = coid;
	}

	/**
	 * @return the aoid
	 */
	public String getAoid() {
		return aoid;
	}

	/**
	 * @param aoid the aoid to set
	 */
	public void setAoid(String aoid) {
		this.aoid = aoid;
	}

	public Long getPersonId() {
		return personId;
	}

	public void setPersonId(Long personId) {
		this.personId = personId;
	}


	@Override
	public APIException getError() {
		return error;
	}

	public void setError(APIException error) {
		this.error = error;
	}

}
