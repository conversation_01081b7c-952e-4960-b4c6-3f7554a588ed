/**
 * 
 */
package com.kronos.persons.rest.model;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class PersonQualifierBean {

    List<PersonQualifierDetailBean> personDetails;

    List<String> missingQualifiers;

    public List<PersonQualifierDetailBean> getPersonDetails() {
        return personDetails;
    }

    public void setPersonDetails(List<PersonQualifierDetailBean> personDetails) {
        this.personDetails = personDetails;
    }

    public List<String> getMissingQualifiers() {
        return missingQualifiers;
    }

    public void setMissingQualifiers(List<String> missingQualifiers) {
        this.missingQualifiers = missingQualifiers;
    }

    public void addPersonDetails(PersonQualifierDetailBean bean) {
        if (personDetails == null) {
            personDetails = new ArrayList<>();
        }
        personDetails.add(bean);
    }

    public void addMissingQualifiers(String qualifier) {
        if (missingQualifiers == null) {
            missingQualifiers = new ArrayList<>();
        }
        missingQualifiers.add(qualifier);
    }

    public List<Long> getPersonIdList() {
        if (personDetails != null && !personDetails.isEmpty()) {
            return personDetails.stream().map(details -> details.getPersonId()).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public List<String> getPersonNumberList() {
        if (personDetails != null && !personDetails.isEmpty()) {
            return personDetails.stream().map(details -> details.getPersonNumber()).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public Map<Long, String> getPersonIdQualifierMap() {
        Map<Long, String> personIdQualifierMap = new HashMap<>();
        if (personDetails != null) {
            personDetails.stream().forEach(detailBean -> {
                personIdQualifierMap.put(detailBean.getPersonId(), detailBean.getQualifier());
            });
        }
        return personIdQualifierMap;
    }
    
    public Map<String, Long> getPersonQualifierIdMap() {
        Map<String, Long> personQualifierIdMap = new HashMap<>();
        if (personDetails != null) {
            personDetails.stream().forEach(detailBean -> {
            	personQualifierIdMap.put(detailBean.getQualifier(), detailBean.getPersonId());
            });
        }
        return personQualifierIdMap;
    }
}
