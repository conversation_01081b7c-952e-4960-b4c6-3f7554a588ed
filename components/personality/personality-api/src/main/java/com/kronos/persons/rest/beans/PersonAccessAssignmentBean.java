package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "@v1.0.personaccessassignmentbean.apimodel.description", name = "personAccessAssignment")
@JsonPropertyOrder(alphabetic = true)
public class PersonAccessAssignmentBean {

	@Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.manageraccessorganizationsetname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED, hidden = true)
	private String managerAccessOrganizationSetName;

    @Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.effectivedate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String effectiveDate;

    @Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.expirationdate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String expirationDate;

	@Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.professionaltransferorganizationsetname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String professionalTransferOrganizationSetName;

    @Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.manageraccessemployeegroupname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String managerEmployeeGroupName;

	@Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.managertransferorganizationsetname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String managerTransferOrganizationSetName;

	@Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.homehyperfindqueryname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String homeHyperFindQueryName;

	@Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.empmgrtransferorganizationsetname.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String empMgrTransferOrganizationSetName;

	@Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.managerEmployeeGroupExpirationDate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String managerEmployeeGroupExpirationDate;

	@Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.managerEmployeeGroupEffectiveDate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String managerEmployeeGroupEffectiveDate;
    
	@Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.managerTransferOrganizationSetExpirationDate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String managerTransferOrganizationSetExpirationDate;

    @Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.managerTransferOrganizationSetEffectiveDate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String managerTransferOrganizationSetEffectiveDate;
    
    @Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.professionalTransferOrganizationSetExpirationDate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String professionalTransferOrganizationSetExpirationDate;

    @Schema(description = "@v1.0.personaccessassignmentbean.apimodelproperty.professionalTransferOrganizationSetEffectiveDate.description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String professionalTransferOrganizationSetEffectiveDate;
    
	public void setManagerTransferOrganizationSetName(
			String managerTransferOrganizationSetName) {
		this.managerTransferOrganizationSetName = managerTransferOrganizationSetName;
	}

	public String getManagerTransferOrganizationSetName() {
		return managerTransferOrganizationSetName;
	}

	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public String getEffectiveDate() {
		return effectiveDate;
	}

	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}

	public String getExpirationDate() {
		return expirationDate;
	}

	public void setManagerAccessOrganizationSetName(
			String managerAccessOrganizationSetName) {
		this.managerAccessOrganizationSetName = managerAccessOrganizationSetName;
	}

	public String getManagerAccessOrganizationSetName() {
		return managerAccessOrganizationSetName;
	}

	public void setManagerEmployeeGroupName(
			String managerEmployeeGroupName) {
		this.managerEmployeeGroupName = managerEmployeeGroupName;
	}

	public String getManagerEmployeeGroupName() {
		return managerEmployeeGroupName;
	}

	public void setHomeHyperFindQueryName(String homeHyperFindQueryName) {
		this.homeHyperFindQueryName = homeHyperFindQueryName;
	}

	public String getHomeHyperFindQueryName() {
		return homeHyperFindQueryName;
	}

	public void setProfessionalTransferOrganizationSetName(
			String professionalTransferOrganizationSetName) {
		this.professionalTransferOrganizationSetName = professionalTransferOrganizationSetName;
	}

	public String getProfessionalTransferOrganizationSetName() {
		return professionalTransferOrganizationSetName;
	}

	public void setEmpMgrTransferOrganizationSetName(String empMgrTransferOrganizationSetName) {
		this.empMgrTransferOrganizationSetName = empMgrTransferOrganizationSetName;
	}

	public String getEmpMgrTransferOrganizationSetName() {
		return empMgrTransferOrganizationSetName;
	}

	/**
	 * @param managerEmployeeGroupExpirationDate the managerEmployeeGroupExpirationDate to set
	 */
	public void setManagerEmployeeGroupExpirationDate(String managerEmployeeGroupExpirationDate) {
		this.managerEmployeeGroupExpirationDate = managerEmployeeGroupExpirationDate;
	}

	/**
	 * @return the managerEmployeeGroupExpirationDate
	 */
	public String getManagerEmployeeGroupExpirationDate() {
		return managerEmployeeGroupExpirationDate;
	}

	/**
	 * @param managerTransferOrganizationSetEffectiveDate the managerTransferOrganizationSetEffectiveDate to set
	 */
	public void setManagerTransferOrganizationSetEffectiveDate(String managerTransferOrganizationSetEffectiveDate) {
		this.managerTransferOrganizationSetEffectiveDate = managerTransferOrganizationSetEffectiveDate;
	}

	/**
	 * @return the managerTransferOrganizationSetEffectiveDate
	 */
	public String getManagerTransferOrganizationSetEffectiveDate() {
		return managerTransferOrganizationSetEffectiveDate;
	}

	/**
	 * @param managerTransferOrganizationSetExpirationDate the managerTransferOrganizationSetExpirationDate to set
	 */
	public void setManagerTransferOrganizationSetExpirationDate(String managerTransferOrganizationSetExpirationDate) {
		this.managerTransferOrganizationSetExpirationDate = managerTransferOrganizationSetExpirationDate;
	}

	/**
	 * @return the managerTransferOrganizationSetExpirationDate
	 */
	public String getManagerTransferOrganizationSetExpirationDate() {
		return managerTransferOrganizationSetExpirationDate;
	}

	/**
	 * @param managerEmployeeGroupEffectiveDate the managerEmployeeGroupEffectiveDate to set
	 */
	public void setManagerEmployeeGroupEffectiveDate(String managerEmployeeGroupEffectiveDate) {
		this.managerEmployeeGroupEffectiveDate = managerEmployeeGroupEffectiveDate;
	}

	/**
	 * @return the managerEmployeeGroupEffectiveDate
	 */
	public String getManagerEmployeeGroupEffectiveDate() {
		return managerEmployeeGroupEffectiveDate;
	}

	/**
	 * @param professionalTransferOrganizationSetEffectiveDate the professionalTransferOrganizationSetEffectiveDate to set
	 */
	public void setProfessionalTransferOrganizationSetEffectiveDate(
			String professionalTransferOrganizationSetEffectiveDate) {
		this.professionalTransferOrganizationSetEffectiveDate = professionalTransferOrganizationSetEffectiveDate;
	}

	/**
	 * @return the professionalTransferOrganizationSetEffectiveDate
	 */
	public String getProfessionalTransferOrganizationSetEffectiveDate() {
		return professionalTransferOrganizationSetEffectiveDate;
	}

	/**
	 * @param professionalTransferOrganizationSetExpirationDate the professionalTransferOrganizationSetExpirationDate to set
	 */
	public void setProfessionalTransferOrganizationSetExpirationDate(
			String professionalTransferOrganizationSetExpirationDate) {
		this.professionalTransferOrganizationSetExpirationDate = professionalTransferOrganizationSetExpirationDate;
	}

	/**
	 * @return the professionalTransferOrganizationSetExpirationDate
	 */
	public String getProfessionalTransferOrganizationSetExpirationDate() {
		return professionalTransferOrganizationSetExpirationDate;
	}

	@Override
	public String toString() {
		return "PersonAccessAssignmentBean [managerAccessOrganizationSetName="
				+ managerAccessOrganizationSetName
		        + ", managerEmployeeGroupName="
				+ managerEmployeeGroupName
				+ ", homeHyperFindQueryName="
				+ homeHyperFindQueryName
				+ ", managerTransferOrganizationSetName="
				+ managerTransferOrganizationSetName
				+ ", professionalTransferOrganizationSetName="
				+ professionalTransferOrganizationSetName + ", effectiveDate="
				+ effectiveDate + ", expirationDate=" + expirationDate + "]";
	}
}
