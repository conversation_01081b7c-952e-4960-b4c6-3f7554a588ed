package com.kronos.persons.rest.model;

import com.kronos.people.proxy.api.dto.GenericObjectRef;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;

@Schema(description = "@v1.0.delegationroleassignmentdto.apimodel.description", name = "delegationRoleAssignmentModel")
public class DelegationRoleAssignmentDTO {

  @Schema(description = "@v1.0.delegationroleassignmentdto.apimodelproperty.id.description", requiredMode = Schema.RequiredMode.REQUIRED)
  private String id;
  
  @Schema(description = "@v1.0.delegationroleassignmentdto.apimodelproperty.delegator.description", requiredMode = Schema.RequiredMode.REQUIRED)
  private EmployeeRefBean delegator;
  
  @Schema(description = "@v1.0.delegationroleassignmentdto.apimodelproperty.delegatorfullname.description", requiredMode = Schema.RequiredMode.REQUIRED)
  private String delegatorFullName;
  
  @Schema(description = "@v1.0.delegationroleassignmentdto.apimodelproperty.role.description", requiredMode = Schema.RequiredMode.REQUIRED)
  private String role;
  
  @Schema(description = "@v1.0.delegationroleassignmentdto.apimodelproperty.startdate.description", requiredMode = Schema.RequiredMode.REQUIRED)
  private LocalDate startDate;
  
  @Schema(description = "@v1.0.delegationroleassignmentdto.apimodelproperty.enddate.description", requiredMode = Schema.RequiredMode.REQUIRED)
  private LocalDate endDate;
  
  public String getId() {
    return id;
  }
  
  public void setId(String id) {
    this.id = id;
  }
  
  public EmployeeRefBean getDelegator() {
    return delegator;
  }
  
  public void setDelegator(EmployeeRefBean delegator) {
    this.delegator = delegator;
  }
  
  public String getDelegatorFullName() {
    return delegatorFullName;
  }
  
  public void setDelegatorFullName(String delegatorFullName) {
    this.delegatorFullName = delegatorFullName;
  }
  
  public String getRole() {
    return role;
  }
  
  public void setRole(String role) {
    this.role = role;
  }
  
  public LocalDate getStartDate() {
    return startDate;
  }
  
  public void setStartDate(LocalDate startDate) {
    this.startDate = startDate;
  }
  
  public LocalDate getEndDate() {
    return endDate;
  }

  public void setEndDate(LocalDate endDate) {
    this.endDate = endDate;
  }
}
