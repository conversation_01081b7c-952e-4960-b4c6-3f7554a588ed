package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.personauthenticationtypebean.apimodel.description", name = "personAuthenticationType")
@JsonPropertyOrder(alphabetic = true)
public class PersonAuthenticationTypeBean {
    @Schema(description = "@v1.0.personauthenticationtypebean.apimodelproperty.activeflag.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean activeFlag;

    @Schema(description = "@v1.0.personauthenticationtypebean.apimodelproperty.authenticationtypename.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String authenticationTypeName;

	/**
	 * The object ID of the authentication type.
	 */
	@JsonIgnore
	protected ObjectIdLong authenticationTypeId;

	/**
	 * @return the activeFlag
	 */
	public Boolean getActiveFlag() {
		return activeFlag;
	}

	/**
	 * @param activeFlag
	 *            the activeFlag to set
	 */
	public void setActiveFlag(Boolean activeFlag) {
		this.activeFlag = activeFlag;
	}

	/**
	 * @return the authenticationTypeName
	 */
	public String getAuthenticationTypeName() {
		return authenticationTypeName;
	}

	/**
	 * @param authenticationTypeName
	 *            the authenticationTypeName to set
	 */
	public void setAuthenticationTypeName(String authenticationTypeName) {
		this.authenticationTypeName = authenticationTypeName;
	}

	/**
	 * @return the authenticationTypeId
	 */
	public ObjectIdLong getAuthenticationTypeId() {
		return authenticationTypeId;
	}

	/**
	 * @param authenticationTypeId
	 *            the authenticationTypeId to set
	 */
	public void setAuthenticationTypeId(ObjectIdLong authenticationTypeId) {
		this.authenticationTypeId = authenticationTypeId;
	}

}
