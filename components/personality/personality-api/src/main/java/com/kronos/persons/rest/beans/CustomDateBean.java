package com.kronos.persons.rest.beans;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 
 * <AUTHOR>
 *
 */
@Schema(description = "@v1.0.customdatebean.apimodel.description", name = "customDate")
@JsonPropertyOrder(alphabetic = true)
public class CustomDateBean {
    @Schema(description = "@v1.0.customdatebean.apimodelproperty.customdatetypename.description", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customDateTypeName;

    @Schema(description = "@v1.0.customdatebean.apimodelproperty.date.description", requiredMode = Schema.RequiredMode.REQUIRED)
	private String date;

	/**
	 * The object ID of the custom data type.
	 */
    @JsonIgnore
	protected ObjectIdLong customDateTypeId;

	public String getCustomDateTypeName() {
		return customDateTypeName;
	}

	public void setCustomDateTypeName(String customDateTypeName) {
		this.customDateTypeName = customDateTypeName;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public ObjectIdLong getCustomDateTypeId() {
		return customDateTypeId;
	}

	public void setCustomDateTypeId(ObjectIdLong customDateTypeId) {
		this.customDateTypeId = customDateTypeId;
	}

}
