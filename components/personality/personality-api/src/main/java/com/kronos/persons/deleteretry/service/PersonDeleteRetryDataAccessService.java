package com.kronos.persons.deleteretry.service;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;

import com.kronos.persons.deleteretry.model.PersonDeleteRetryEntity;

public interface PersonDeleteRetryDataAccessService {

	Integer updatePersonRetryCount(BigInteger personId, Integer retryCount, Timestamp currentUTCTime);

	List<PersonDeleteRetryEntity> getPersonIdsToDelete(Integer threshold, Timestamp timeDiff);

	void save(PersonDeleteRetryEntity pdre);

	void delete(PersonDeleteRetryEntity pdre);
	
	void removeDeletedPerson();
	
	void removeOldRecords(Timestamp time);

}