package com.kronos.persons.rest.model;

/**
 * PayCodeTag
 *
 *  Interface to get fields from PayCodeTag
 *
 * Copyright (C) 2021 UKG.com
 * Date: December 9, 2021
 *
 * <AUTHOR>
 */
public interface PayCodeTag {

    /**
     * Get id from payCode tag
     * @return id of payCode tag
     */
    Long getId();

    /**
     * Get name from payCode tag
     * @return name of payCode tag
     */
    String getName();
}
