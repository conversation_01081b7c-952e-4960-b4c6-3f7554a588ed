//If deployToBackend is set to true, then this will be added into swimlane-runtime automatically if it isn't there already.
ext.deployToBackend=true

dependencies {

	api project(':personality-api')
	api project(':people-impl')
	api project(':positions-dataaccess-api')
	api project(':assignmentcache-core-api')    
	api project(':external-identifier-assignment-api')

    // Swimlane Container
    api (depManifest.setVersion(group: 'com.kronos.container', name: 'master-container-api')) {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.tenant-provider', name: 'tenant-provider-api')){ transitive = false}
   	api (depManifest.setVersion(group: 'com.kronos.commonapp',name: 'event-manager-api')){transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.legacy', name: 'wfp')){transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.legacy', name: 'wtk')){transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.legacy', name: 'wfl')){transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.legacy', name: 'wat')){transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.licensing', name: 'licensing-framework-api')) {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'currency-policy-api')) {transitive=false}
	api (depManifest.setVersion(group: 'jakarta.transaction',name:'jakarta.transaction-api')) {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.scheduling', name: 'scheduling-commons-core')) {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.scheduling', name: 'scheduling-setup-predsched-rules-api')) { transitive = false }
    api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'commonbusiness-employee-preference-api')) { transitive = false }

	api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'orgmap-proxy-api')){transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'orgmap-security-api')) {transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'orgmap-selector-api')) {transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'orgmap-setup-api')) {transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'orgmap-traversal-api')) {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'orgmap-genericlocation-api')){transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'employee-group-setup-api')) {transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.tenant-provider', name: 'tenant-provider-api')) {transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.timekeeping', name: 'udm-service-api')) {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'helper')){transitive=false}
	api(depManifest.setVersion(group:  'com.kronos.commonapp',name:'authz-framework-impl')){transitive=false}
	api(depManifest.setVersion(group:  'com.kronos.commonbusiness',name:'timezone-setup-api')){transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'localepolicy-model-api')){transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'localepolicy-formatters-api')) {transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'timezone-symbolicperiods-api'))  {transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'laborcategory-common-api'))  {transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'laborcategory-setup-api'))  {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.commonapp', name: 'groupedit-api')) {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.api', name: 'async-common-component-api')) {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.timekeeping.common.library', name: 'timekeeping-core-services-api')) {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.timekeeping', name: 'timekeeping-brazil-compliance-setup-api')) {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.authz', name: 'authz-extn-api')) {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'laborcategory-laboraccount-api')) {transitive=false}
    api (depManifest.setVersion(group: 'org.apache.cxf', name: 'cxf-rt-frontend-jaxrs'))
	api (depManifest.setVersion(group: 'com.kronos.common.auth', name: 'common-authentication-api')) {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'people-hcm-bridge-impl')) {transitive=false}
	api (depManifest.setVersion(group: 'com.kronos.hcm', name: 'hcm-wfd-provisioning-dataaccess-api')) {transitive=false}
	api (depManifest.setVersion(group: 'io.github.weblegacy',name:'struts-core'))

<<<<<<< HEAD
	testImplementation (depManifest.setVersion(group: 'com.kronos.totalizing', name: 'totalizer-shared-api')) {transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.totalizing', name: 'totalizer-engine')) {transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.authz', name: 'authz-extn-api')){transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.common.encryption', name: 'wfm-encryption-util-api')) {transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'orgmap-common-impl')) {transitive=false}
	//testImplementation (depManifest.setVersion(group: 'com.kronos.commonapp', name: 'orgmap-common-impl')){transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.healthcheck-framework', name: 'healthcheck-wfmDB-api')){transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.dataaccess', name: 'dataaccess-framework-impl')) {transitive=false}
    testImplementation (depManifest.setVersion(group: 'com.kronos.commonapp', name: 'kronosproperties-api')) {transitive=false}
    testImplementation (depManifest.setVersion(group: 'org.apache.commons', name: 'commons-lang3'))
    testImplementation (depManifest.setVersion(group: 'org.slf4j', name: 'slf4j-api'))
	testImplementation (depManifest.setVersion(group: 'jakarta.servlet.jsp', name: 'jakarta.servlet.jsp-api'))
	testImplementation (depManifest.setVersion(group: 'org.springframework', name: 'spring-test'))
	testImplementation (depManifest.setVersion(group: 'org.springframework', name: 'spring-context'))
	testImplementation (depManifest.setVersion(group: 'org.javassist', name: 'javassist')) {transitive=false}
    testImplementation (depManifest.setVersion(group:  'com.kronos.commonbusiness', name: 'timezone-legacyutilities-api')){transitive=false}
    testImplementation (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'timezone-setup-api' )) {transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'userpreferences-api')) {transitive=false} 
	testImplementation (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'localepolicy-model-api')){transitive=false}
    testImplementation (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'localepolicy-formatters-api')) {transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'release-toggle-api')) {transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'timezone-setup-api')) {transitive=false}
	testRuntimeOnly(depManifest.setVersion(group: 'com.kronos.scheduling', name: 'scheduling-setup-shifttemplate-api')) {transitive=false}
    testImplementation (depManifest.setVersion(group: 'com.kronos.licensing', name: 'licensing-framework-api')) {transitive=false}
    testImplementation (depManifest.setVersion(group: 'com.kronos.scheduling', name: 'scheduling-setup-shifttemplate-api'))  {transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.scheduling', name: 'scheduling-proxies-setup-orgmap-api')) { transitive = false }
	testImplementation (depManifest.setVersion(group: 'com.kronos.commonapp', name: 'groupedit-api')) {transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'release-toggle-api')) {transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.commonapp', name: 'hyperfind-execution-api')) {transitive=false}
	testRuntimeOnly(depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'release-toggle-api')) {transitive=false}
	testRuntimeOnly(depManifest.setVersion(group: 'com.kronos.healthcheck-framework', name: 'healthcheck-wfmDB-api')) {transitive=false}
	testRuntimeOnly(depManifest.setVersion(group: 'com.kronos.dataaccess', name: 'dataaccess-framework-impl')) {transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'orgmap-transaction-api')) {transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'timezone-utilities-api')){transitive=false}
	testImplementation (depManifest.setVersion(group:'com.kronos.systemaccounts',name:'system-accounts-api')) {transitive=false}
	testImplementation (depManifest.setVersion(group: 'com.kronos.legacy', name: 'wtk')){transitive=false}

	testImplementation (depManifest.setVersion(group: 'org.junit.jupiter', name: 'junit-jupiter-api'))
	testImplementation (depManifest.setVersion(group: 'org.mockito', name: 'mockito-core'))
	testImplementation (depManifest.setVersion(group: 'org.mockito', name: 'mockito-junit-jupiter'))
	testImplementation (depManifest.setVersion(group: 'org.hamcrest', name: 'hamcrest-all'))
	testImplementation (depManifest.setVersion(group: 'com.kronos.clientcredentials',name: 'client-credentials-api')){transitive=false}
=======
	testCompile 'org.powermock:powermock-module-junit4:1.5.1'
	testCompile 'org.powermock:powermock-api-mockito:1.5.1'
	testCompile (depManifest.setVersion(group: 'com.kronos.healthcheck-framework', name: 'healthcheck-wfmDB-api')){transitive=false}
	testCompile (depManifest.setVersion(group: 'com.kronos.dataaccess', name: 'dataaccess-framework-impl')) {transitive=false}
    testCompile (depManifest.setVersion(group: 'com.kronos.commonapp', name: 'kronosproperties-api')) {transitive=false}
    testCompile (depManifest.setVersion(group: 'org.apache.commons', name: 'commons-lang3')) {transitive=false}
	testCompile (depManifest.setVersion(group: 'org.apache.commons', name: 'commons-collections4')) {transitive=false}
	testCompile (depManifest.setVersion(group: 'org.slf4j', name: 'slf4j-api'))
	testCompile (depManifest.setVersion(group: 'javax.servlet.jsp', name: 'jsp-api'))
	testCompile (depManifest.setVersion(group: 'org.springframework', name: 'spring-test'))
	testCompile (depManifest.setVersion(group: 'org.springframework', name: 'spring-context'))
    testCompile (depManifest.setVersion(group: 'junit', name: 'junit')) {transitive=false}
	testCompile (depManifest.setVersion(group: 'org.mockito', name:'mockito-all')) {transitive=false}
	testCompile (depManifest.setVersion(group: 'org.javassist', name: 'javassist')) {transitive=false}
    testCompile (depManifest.setVersion(group: 'org.powermock', name:'powermock-mockito-release-full'))
	testCompile (depManifest.setVersion(group:'org.mockito', name:'mockito-all'))
    testCompile (depManifest.setVersion(group:  'com.kronos.commonbusiness', name: 'timezone-legacyutilities-api')){transitive=false} 
    testCompile (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'timezone-setup-api' )) {transitive=false}
	testCompile (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'userpreferences-api')) {transitive=false} 
	testCompile (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'localepolicy-model-api')){transitive=false}
    testCompile (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'localepolicy-formatters-api')) {transitive=false}
	testCompile (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'release-toggle-api')) {transitive=false}
	testCompile (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'timezone-setup-api')) {transitive=false}
	testRuntime (depManifest.setVersion(group: 'com.kronos.scheduling', name: 'scheduling-setup-shifttemplate-api')) {transitive=false}
    testCompile(depManifest.setVersion(group: 'com.kronos.licensing', name: 'licensing-framework-api')) {transitive=false}
    testCompile (depManifest.setVersion(group: 'com.kronos.scheduling', name: 'scheduling-setup-shifttemplate-api'))  {transitive=false}
	testCompile (depManifest.setVersion(group: 'com.kronos.scheduling', name: 'scheduling-proxies-setup-orgmap-api')) { transitive = false }
	testCompile (depManifest.setVersion(group: 'com.kronos.commonapp', name: 'groupedit-api')) {transitive=false}
	testCompile (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'release-toggle-api')) {transitive=false}
	testCompile (depManifest.setVersion(group: 'com.kronos.commonapp', name: 'hyperfind-execution-api')) {transitive=false}
	testRuntime (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'release-toggle-api')) {transitive=false}
	testRuntime (depManifest.setVersion(group: 'com.kronos.healthcheck-framework', name: 'healthcheck-wfmDB-api')) {transitive=false}
	testRuntime (depManifest.setVersion(group: 'com.kronos.dataaccess', name: 'dataaccess-framework-impl')) {transitive=false}
	testRuntime (depManifest.setVersion(group: 'org.apache.commons', name: 'commons-lang3')) {transitive=false}

	testCompile (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'orgmap-transaction-api')) {transitive=false}
	testCompile (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'timezone-utilities-api')){transitive=false}
	testCompile (depManifest.setVersion(group:'com.kronos.systemaccounts',name:'system-accounts-api')) {transitive=false}
	testCompile (depManifest.setVersion(group: 'org.apache.struts', name: 'struts-kronos-custom'))
	testCompile (depManifest.setVersion(group: 'junit', name: 'junit')) {
		exclude group: 'org.hamcrest'
	}
	testCompile (depManifest.setVersion(group: 'org.hamcrest', name: 'hamcrest-all'))
	testCompile (depManifest.setVersion(group: 'com.kronos.clientcredentials',name: 'client-credentials-api')){transitive=false}
	testCompile (depManifest.setVersion(group: 'commons-beanutils', name: 'commons-beanutils'))
>>>>>>> r9int-build-1974

	testRuntimeOnly(depManifest.setVersion(group: 'com.google.guava', name: 'guava'))
	testRuntimeOnly(depManifest.setVersion(group: 'org.junit.jupiter', name: 'junit-jupiter-engine'))
	testRuntimeOnly(depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'localepolicy-common-datatypes')) {transitive=false}
    testRuntimeOnly(depManifest.setVersion(group: 'org.apache.commons', name: 'commons-math3'))
    testRuntimeOnly(depManifest.setVersion(group: 'commons-io', name: 'commons-io'))
    testRuntimeOnly(depManifest.setVersion(group: 'org.apache.poi', name: 'poi'))
    testRuntimeOnly(depManifest.setVersion(group: 'org.glassfish.jersey.core',name:'jersey-common'))
    testRuntimeOnly(depManifest.setVersion(group: 'org.springframework.amqp', name: 'spring-amqp'))
	testRuntimeOnly(depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'textlabel-framework-api')){transitive=false}
}