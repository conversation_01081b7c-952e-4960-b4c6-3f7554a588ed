package com.kronos.persons.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.*;


import com.kronos.people.personality.model.Criteria;
import com.kronos.people.personality.model.IdentifierType;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.service.PersonPhotoServiceConstants;
import com.kronos.wfc.datacollection.empphoto.business.EmpPhotoBusinessValidationException;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.util.OptionalPersonId;
import com.kronos.persons.photo.entity.EmpPhoto;
import com.kronos.persons.photo.service.IPersonPhotoDataAccessService;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.PhotoBean;
import com.kronos.persons.rest.beans.PhotoInfoBean;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.persons.rest.model.PhotoCriteria;
import com.kronos.persons.rest.model.SearchValues;
import com.kronos.wfc.commonapp.people.business.commonobjectid.CommonObjectId;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class PersonPhotoUtilsTest {
	
	@Mock
	IPersonPhotoDataAccessService empPhotoDataAcessService;

	private MockedStatic<PersonPhotoUtilsTest> mockedPersonPhotoUtils;
	private MockedStatic<KronosProperties> mockedKronosProperties;
	private MockedStatic<PrsnValidationException> mockedPrsnValidationException;

	@Mock
	Properties properties;

	@BeforeEach
	public void setUp() {
		mockedPersonPhotoUtils = Mockito.mockStatic(PersonPhotoUtilsTest.class);
		mockedKronosProperties = Mockito.mockStatic(KronosProperties.class);
		mockedPrsnValidationException = Mockito.mockStatic(PrsnValidationException.class);
	}

	@AfterEach
	public void tearDown() {
		mockedPersonPhotoUtils.close();
		mockedKronosProperties.close();
		mockedPrsnValidationException.close();
	}

	@Test
	public void createAoidCoidList() {
		List<SearchValues> multiKeyValues = new ArrayList<>();
		SearchValues value=new SearchValues();
		value.setAoid("90109");
		value.setCoid("90109");
		multiKeyValues.add(value);
		List<String> listAoidReq=new ArrayList<>();
		Map<String, String> aoidCoidMap =new HashMap<>();
		List<List<String>> ls= List.of(Arrays.asList("90109", "90109"));
		PhotoCriteria photoCriteria=new PhotoCriteria(null,null,Arrays.asList("aoid","coid"),multiKeyValues);
		assertEquals(ls,PersonPhotoUtils.createAoidCoidList(photoCriteria, listAoidReq,aoidCoidMap));
		
	}

	@Test
	public void populateErrorMapTest() {
		PhotoBean bean = new PhotoBean();
		List<String> listAoidReq = Arrays.asList("90109");
		Map<String, String> aoidCoidMap = new HashMap<>();
		aoidCoidMap.put("90109", "90109");
		boolean coidRequired = false;
		Map<Object, PhotoBean> aoidErrorMap = new LinkedHashMap<>();
		Map<Object, PhotoBean> responseaoidErrorMap = new LinkedHashMap<>();

		APIException ex = mock(APIException.class);
		//when(ex.getInputDetail()).thenReturn((Object) "Input detail");
		bean.setError(ex);
		aoidErrorMap.put("90109", bean);

		mockedKronosProperties.when(() -> KronosProperties.getProperties()).thenReturn(properties);
//		when(properties.getProperty(anyString(),isNull())).thenReturn("12");
		mockedPrsnValidationException.when(()-> PrsnValidationException.invalidPropertyValue(anyString(),any())).thenReturn(ex);

		PhotoCriteria photoCriteria = mock(PhotoCriteria.class);
		PersonPhotoUtils.populateErrorMap(listAoidReq, aoidCoidMap, responseaoidErrorMap, photoCriteria);

		assertNotNull(responseaoidErrorMap.get("90109").getError());
		verify(ex).setInputDetail(any());
	}


	@Test
	public void getResponseList() {
		Map<Object, PhotoBean> responseMap=new HashMap<>();
		PhotoBean bean=new PhotoBean();
		List<PhotoBean> repsonseList=new ArrayList<>();
		List<PhotoBean> expectedResponseList= new ArrayList<>();
		try {
		bean.setError(PrsnValidationException.invalidPropertyValue(ExtensionConstant.AOID_SEARCH_KEY, "90109"));
		responseMap.put("90109", bean);
		expectedResponseList.add(bean);
		repsonseList=PersonPhotoUtils.getResponseList(responseMap);
		assertTrue(CollectionUtils.isEqualCollection(repsonseList, expectedResponseList));
		}catch(Exception e) {
			fail("Unexpected Exception Occured");
		}
	}
	
	@Test
	public void getResponseListNulltest() {
		Map<Object, PhotoBean> responseMap=null;
		assertThrows(NullPointerException.class, () -> PersonPhotoUtils.getResponseList(responseMap));
	}


	@Test
	public void populateSingleKeyListResponse() {
		List<Long> personIds = List.of(5L);
		List<EmpPhoto> photoBeanObj = new ArrayList<>();
		try (MockedStatic<PersonPhotoUtils> mockedPersonPhotoUtils = Mockito.mockStatic(PersonPhotoUtils.class)) {
			EmpPhoto emp = new EmpPhoto();
			emp.setPersonid(5L);
			emp.setDeletedsw(false);
			photoBeanObj.add(emp);
			mockedPersonPhotoUtils.when(() -> PersonPhotoUtils.populateSingleKeyListResponse(personIds, photoBeanObj))
					.thenCallRealMethod();
			PersonPhotoUtils.populateSingleKeyListResponse(personIds, photoBeanObj);
			mockedPersonPhotoUtils.verify(() -> PersonPhotoUtils.populateSingleKeyListResponse(personIds, photoBeanObj), Mockito.times(1));
		} catch (Exception e) {
			// fail();
		}
	}

	@Test
	public void populateMultiKeyListResponseTest() {
		List<CommonObjectId> commonIdReqList = new ArrayList<>();
		List<EmpPhoto> photoBeanObj = new ArrayList<>();
		try (MockedStatic<PersonPhotoUtils> mockedPersonPhotoUtils = Mockito.mockStatic(PersonPhotoUtils.class)) {
			EmpPhoto emp = new EmpPhoto();
			emp.setPersonid(5L);
			emp.setDeletedsw(false);
			photoBeanObj.add(emp);
			mockedPersonPhotoUtils.when(() -> PersonPhotoUtils.populateMultiKeyListResponse(commonIdReqList, photoBeanObj))
					.thenCallRealMethod();
			PersonPhotoUtils.populateMultiKeyListResponse(commonIdReqList, photoBeanObj);
			mockedPersonPhotoUtils.verify(() -> PersonPhotoUtils.populateMultiKeyListResponse(commonIdReqList, photoBeanObj), Mockito.times(1));
		} catch (Exception e) {
			// fail();
		}
	}
	
	@Test
	public void getSingleKeyResponseWithError() {
		OptionalPersonId person = new OptionalPersonId(null, new Exception());
		Map<Object, OptionalPersonId> personIdCollection = new HashMap<>();
		personIdCollection.put("123", person);
		List<PhotoBean> response = PersonPhotoUtils.getSingleKeyResponseData(new HashMap<>(), "personNumber", personIdCollection);
		assertNotNull(response.get(0).getError());
	}
	
	@Test
	public void getSingleKeyResponse() {
		OptionalPersonId person = new OptionalPersonId(null, new Exception());
		PhotoBean bean=new PhotoBean();
		bean.setPersonIdentity(mock(PersonIdentityBean.class));
		bean.setPhoto(mock(PhotoInfoBean.class));
		person.setPersonId(123L);
		Map<Object, OptionalPersonId> personIdCollection = new HashMap<>();
		personIdCollection.put("123", person);
		Map<Object, PhotoBean> photoBeanResponse=new HashMap<>();
		photoBeanResponse.put(person.getPersonId(),bean );
		List<PhotoBean> response = PersonPhotoUtils.getSingleKeyResponseData(photoBeanResponse, "personNumber", personIdCollection);
		assertNotNull(response.get(0).getPersonIdentity());
		assertNotNull(response.get(0).getPhoto());
		
	}
	
	@Test
	public void getMultiKeyResponse() {
		OptionalPersonId person = new OptionalPersonId(null, new Exception());
		PhotoCriteria photoCriteria=mock(PhotoCriteria.class);
		PhotoBean bean=new PhotoBean();
		bean.setPersonIdentity(mock(PersonIdentityBean.class));
		bean.setPhoto(mock(PhotoInfoBean.class));
		person.setPersonId(123L);
		List<SearchValues> multiKeyValues = new ArrayList<>();
		SearchValues value=new SearchValues();
		value.setAoid("90109");
		value.setCoid("90109");
		multiKeyValues.add(value);
		Map<String, Long> aoidPersonNumMap=new HashMap<>();
		Map<Object, PhotoBean> photoBeanResponse=new HashMap<>();
		photoBeanResponse.put(person.getPersonId(),bean );
		Map<Object, PhotoBean> aoidErrorMap=new HashMap<>();
		aoidPersonNumMap.put(multiKeyValues.get(0).getAoid(),123L);
		aoidErrorMap.put(person.getPersonId(),new PhotoBean());
		when(photoCriteria.getMultiKeyValues()).thenReturn(multiKeyValues);
		List<PhotoBean> response= PersonPhotoUtils.getMultiKeyResponseData(photoCriteria, aoidPersonNumMap, photoBeanResponse, aoidErrorMap);
		assertNotNull(response.get(0).getPersonIdentity());
		assertNotNull(response.get(0).getPhoto());
	}

	@Test
	public void testGetEncodedImageWhenByteArrayIsNull() {
		assertNull(PersonPhotoUtils.getEncodedImage(null));
	}

	@Test
	public void testGetEncodedImageWhenByteArrayIsNotNull() {
		String test = "test";
		String encodedImage = PersonPhotoUtils.getEncodedImage(test.getBytes());
		assertEquals(test, new String(Base64.getDecoder().decode(encodedImage)));
	}

	@Test
	public void testGetEncodedImageWhenByteArrayIsBlank() {
		EmpPhotoBusinessValidationException exception = assertThrows(EmpPhotoBusinessValidationException.class, ()->
			PersonPhotoUtils.getEncodedImage(new byte[]{})
		);
		assertEquals(EmpPhotoBusinessValidationException.INVALID_IMAGE, exception.getErrorCode());
	}

	@Test
	public void testGetEncodedImageWhenByteArrayIsIncorrect() {
		byte[] byteArray = "test".getBytes();
		Base64.Encoder encoder = Mockito.mock(Base64.Encoder.class);

		try(MockedStatic<Base64> mockedBase64 = Mockito.mockStatic(Base64.class)) {
			mockedBase64.when(Base64::getEncoder).thenReturn(encoder);
			mockedBase64.when(() -> encoder.encodeToString(byteArray)).thenThrow(new RuntimeException("Test Exception"));
			EmpPhotoBusinessValidationException exception = assertThrows(EmpPhotoBusinessValidationException.class, () ->
					PersonPhotoUtils.getEncodedImage(byteArray)
			);

			assertEquals(EmpPhotoBusinessValidationException.INVALID_IMAGE, exception.getErrorCode());
		}
	}

	@Test
	public void testPopulateErrorMapWhenCoidRequiredIsTrue() {
		List<String> listAoidReq = Collections.singletonList("90109");
		Map<String, String> aoidCoidMap=new HashMap<>();
		aoidCoidMap.put("90109","90109");
		Map<Object, PhotoBean> responseaoidErrorMap=new LinkedHashMap<>();
		PhotoCriteria photoCriteria=mock(PhotoCriteria.class);
		List<String> multiKeyValues = new ArrayList<>();
		multiKeyValues.add(ExtensionConstant.AOID_SEARCH_KEY);
		when(photoCriteria.getMultiKey()).thenReturn(multiKeyValues);

		APIException ex = new APIException(ExceptionConstants.INVALID_VALUE_COMBINATION);
		mockedKronosProperties.when(() -> KronosProperties.getPropertyAsBoolean(CommonObjectId.COID_REQUIRED_KEY, false)).thenReturn(true);
		mockedPrsnValidationException.when(()-> PrsnValidationException.invalidPropertyValueCombination(anyString(),anyString(),anyString(),anyString())).thenReturn(ex);
		PersonPhotoUtils.populateErrorMap(listAoidReq, aoidCoidMap, responseaoidErrorMap, photoCriteria);

		assertEquals("WCO-101236", responseaoidErrorMap.get("90109").getError().getErrorCode());
		assertEquals(1, responseaoidErrorMap.get("90109").getError().getInputDetail().size());
		assertNull(responseaoidErrorMap.get("90109").getPhoto());
		assertNull(responseaoidErrorMap.get("90109").getPersonIdentity());
	}

	@Test
	public void testGetMultiKeyResponseDataWhenMultiKeyHasAoid() {
		OptionalPersonId person = new OptionalPersonId(123L, new Exception());
		PhotoCriteria photoCriteria=new PhotoCriteria();
		PhotoBean bean=new PhotoBean();
		bean.setPersonIdentity(new PersonIdentityBean());
		bean.setPhoto(new PhotoInfoBean());
		bean.setError(new APIException("test"));
		List<SearchValues> multiKeyValues = new ArrayList<>();
		SearchValues value=new SearchValues();
		value.setAoid("90109");
		value.setCoid("90109");
		multiKeyValues.add(value);
		Map<String, Long> aoidPersonNumMap=new HashMap<>();
		Map<Object, PhotoBean> photoBeanResponse=new HashMap<>();
		photoBeanResponse.put(person.getPersonId(),bean );
		Map<Object, PhotoBean> aoidErrorMap=new HashMap<>();
		aoidPersonNumMap.put(multiKeyValues.get(0).getAoid(),123L);
		aoidErrorMap.put(person.getPersonId(),new PhotoBean());
		photoCriteria.setMultiKeyValues(multiKeyValues);
		List<String> multiKey = new ArrayList<>();
		multiKey.add("aoid");
		photoCriteria.setMultiKey(multiKey);
		List<PhotoBean> response= PersonPhotoUtils.getMultiKeyResponseData(photoCriteria, aoidPersonNumMap, photoBeanResponse, aoidErrorMap);
		assertNotNull(response.get(0).getPersonIdentity());
		assertNotNull(response.get(0).getPhoto());
		assertEquals("WCO-101729", response.get(0).getError().getErrorCode());
		assertEquals(1, response.get(0).getError().getInputDetail().size());
		assertNull(response.get(0).getPhoto().getImage());
		assertNull(response.get(0).getPersonIdentity().getPersonNumber());
	}

	@Test
	public void testGetMultiKeyResponseDataWhenMultiKeyHasCoid() {
		OptionalPersonId person = new OptionalPersonId(123L, new Exception());
		PhotoCriteria photoCriteria=new PhotoCriteria();
		PhotoBean bean=new PhotoBean();
		bean.setPersonIdentity(new PersonIdentityBean());
		bean.setPhoto(new PhotoInfoBean());
		bean.setError(new APIException("test"));
		List<SearchValues> multiKeyValues = new ArrayList<>();
		SearchValues value=new SearchValues();
		value.setAoid("90109");
		value.setCoid("90109");
		multiKeyValues.add(value);
		Map<String, Long> aoidPersonNumMap=new HashMap<>();
		Map<Object, PhotoBean> photoBeanResponse=new HashMap<>();
		photoBeanResponse.put(person.getPersonId(),bean );
		Map<Object, PhotoBean> aoidErrorMap=new HashMap<>();
		aoidPersonNumMap.put(multiKeyValues.get(0).getAoid(),123L);
		aoidErrorMap.put(person.getPersonId(),new PhotoBean());
		photoCriteria.setMultiKeyValues(multiKeyValues);
		List<String> multiKey = new ArrayList<>();
		multiKey.add("coid");
		photoCriteria.setMultiKey(multiKey);
		List<PhotoBean> response= PersonPhotoUtils.getMultiKeyResponseData(photoCriteria, aoidPersonNumMap, photoBeanResponse, aoidErrorMap);
		assertNotNull(response.get(0).getPersonIdentity());
		assertNotNull(response.get(0).getPhoto());
		assertEquals("WCO-101729", response.get(0).getError().getErrorCode());
		assertEquals(1, response.get(0).getError().getInputDetail().size());
		assertNull(response.get(0).getPhoto().getImage());
		assertNull(response.get(0).getPersonIdentity().getPersonNumber());
	}

	@Test
	public void testGetMultiKeyResponseDataWhenAoidPersonNumMapIsBlank() {
		PhotoCriteria photoCriteria=new PhotoCriteria();
		List<SearchValues> multiKeyValues = new ArrayList<>();
		SearchValues value=new SearchValues();
		value.setAoid("90109");
		value.setCoid("90109");
		multiKeyValues.add(value);
		Map<String, Long> aoidPersonNumMap=new HashMap<>();
		Map<Object, PhotoBean> aoidErrorMap=new HashMap<>();
		PhotoBean photoBean = new PhotoBean();
		aoidErrorMap.put(multiKeyValues.get(0).getAoid(),photoBean);
		photoCriteria.setMultiKeyValues(multiKeyValues);
		List<PhotoBean> response= PersonPhotoUtils.getMultiKeyResponseData(photoCriteria, aoidPersonNumMap, null, aoidErrorMap);
		assertEquals(photoBean, response.get(0));
	}

	@Test
	public void testCreateCriteria() {
		List<Long> personIds = Arrays.asList(1L, 2L);
		Criteria criteria = PersonPhotoUtils.createCriteria(personIds);
		assertEquals(IdentifierType.PERSONID, criteria.getIdsType());
		assertEquals(1L, criteria.getIds()[0]);
		assertEquals(2L, criteria.getIds()[1]);
	}

	@Test
	public void testCreateCriteriaWhenPersonIdsBlank() {
		Criteria criteria = PersonPhotoUtils.createCriteria(Collections.EMPTY_LIST);
		assertEquals(IdentifierType.PERSONID, criteria.getIdsType());
		assertEquals(0, criteria.getIds().length);
	}
	@Test
	public void testCreateCriteriaWhenPersonIdsNull() {
		assertThrows(NullPointerException.class,
				() -> PersonPhotoUtils.createCriteria(null));
	}

	@Test
	public void testGetPersonCommonIdMapByAoidCoidWhenIdsAreBlank() {
		assertEquals(0, PersonPhotoUtils.getPersonCommonIdMapByAoidCoid(new ArrayList<>()).size());
	}

	@Test
	public void testPopulateMultiKeyListResponseWhenDataIsValid() {
		Long personId = 5L;
		String personNum = "123";
		String associateIdText = "associateIdText";
		String commonIdText = "commonIdText";

		mockedKronosProperties.when(KronosProperties::getProperties).thenReturn(properties);
		when(properties.getProperty(anyString(),isNull())).thenReturn("12");

		List<CommonObjectId> commonIdReqList = new ArrayList<>();
		CommonObjectId commonObjectId = new CommonObjectId();
		commonObjectId.setPersonId(new ObjectIdLong(personId));
		commonObjectId.setPersonNumber(personNum);
		commonObjectId.setAssociateIdText(associateIdText);
		commonObjectId.setCommonIdText(commonIdText);
		commonIdReqList.add(commonObjectId);
		List<EmpPhoto> photoBeanObj = new ArrayList<>();
		EmpPhoto empPhoto = new EmpPhoto();
		empPhoto.setPersonid(personId);
		empPhoto.setDeletedsw(false);
		empPhoto.setImageContent("test".getBytes());
		photoBeanObj.add(empPhoto);
		Map<Object, PhotoBean> populateMultiKeyListResponse = PersonPhotoUtils.populateMultiKeyListResponse(commonIdReqList, photoBeanObj);
		assertEquals(personNum, populateMultiKeyListResponse.get(personId).getPersonIdentity().getPersonNumber());
		assertEquals(associateIdText, populateMultiKeyListResponse.get(personId).getPersonIdentity().getAoid());
		assertEquals(commonIdText, populateMultiKeyListResponse.get(personId).getPersonIdentity().getCoid());
		assertEquals(PersonPhotoServiceConstants.PHOTO_IMAGE_FORMAT_TYPE, populateMultiKeyListResponse.get(personId).getPhoto().getImageType());
	}


}
