package com.kronos.persons.rest.assignments.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import jakarta.ws.rs.core.Response;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignmentDTO;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonAttestationProfileAssignmentRestServiceImplTest {
    private static final Long DEFAULT_PERSON_ID = 4L;

    @Mock
    private PersonAttestationProfileAssignmentService personAttestationProfileAssignmentService;

    @InjectMocks
    private PersonAttestationProfileAssignmentRestServiceImpl personAttestationProfileAssignmentRestServiceImpl;

    @Test
    public void shouldReturnPersonAttestationProfileAssignmentsOnRetrieveByPersonId() {
        List<PersonAttestationProfileAssignmentDTO> assignments = new ArrayList<>();
        PersonAttestationProfileAssignmentDTO assignment = new PersonAttestationProfileAssignmentDTO();
        assignment.setProfile(new ObjectRef(1L, "some name"));
        assignments.add(assignment);
        Response expected = Response.ok(Collections.singletonList(assignment)).build();

        when(personAttestationProfileAssignmentService.getAttestationProfileAssignments(anyLong(), anyBoolean()))
            .thenAnswer(i -> assignments);

        Response actual = personAttestationProfileAssignmentRestServiceImpl.retrieveByPersonId(DEFAULT_PERSON_ID, false);
        verify(personAttestationProfileAssignmentService, times(1)).getAttestationProfileAssignments(DEFAULT_PERSON_ID, false);
        assertEquals(expected.getStatus(), actual.getStatus());
        assertEquals(expected.getEntity(), actual.getEntity());
    }

    @Test
    public void shouldReturnPersonAttestationProfileAssignmentOnCreateRequest() {
        PersonAttestationProfileAssignmentDTO assignment = new PersonAttestationProfileAssignmentDTO();
        assignment.setProfile(new ObjectRef(1L, "some name"));
        Response expected = Response.status(Response.Status.CREATED).entity(assignment).build();
        when(personAttestationProfileAssignmentService.create(anyLong(),
            any(PersonAttestationProfileAssignmentDTO.class))).thenReturn(assignment);


        Response actual = personAttestationProfileAssignmentRestServiceImpl.create(DEFAULT_PERSON_ID, assignment);
        verify(personAttestationProfileAssignmentService, times(1)).create(DEFAULT_PERSON_ID, assignment);
        assertEquals(expected.getStatus(), actual.getStatus());
        assertEquals(expected.getEntity(), actual.getEntity());
    }
}
