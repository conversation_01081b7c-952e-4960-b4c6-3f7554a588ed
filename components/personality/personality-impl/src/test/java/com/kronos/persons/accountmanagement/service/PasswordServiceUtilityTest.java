/*******************************************************************************
 * PasswordServiceUtilityTest.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.accountmanagement.service;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;


/**
 * The class to unit test methods of PasswordServiceUtility.
 */
@ExtendWith(MockitoExtension.class)
public class PasswordServiceUtilityTest {

    @Test
    public void testGenerateRandomPassword() {
        String password = PasswordServiceUtility.generateRandomPassword();
        assertEquals(20, password.length());
        assertTrue(password.matches(".*[a-z].*"));
        assertTrue(password.matches(".*[A-Z].*"));
        assertTrue(password.matches(".*\\d.*"));
        assertTrue(password.matches(".*[!@#$%^&*_=+-/].*"));
    }
}
