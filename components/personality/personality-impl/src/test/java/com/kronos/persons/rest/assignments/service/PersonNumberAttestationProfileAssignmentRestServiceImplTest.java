package com.kronos.persons.rest.assignments.service;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.commonbusiness.datatypes.ref.ObjectRefList;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.AttestationProfileAssignRoleSelect;
import com.kronos.persons.rest.assignments.model.EmployeeRefs;
import com.kronos.persons.rest.assignments.model.PersonAttestationAssignmentCriteria;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignment;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignmentDTO;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignmentRequest;
import com.kronos.persons.rest.assignments.validation.BulkPersonAttestationProfileAssignmentValidator;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.utils.BulkProcessingHelper;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonNotification;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import jakarta.ws.rs.core.Response;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonNumberAttestationProfileAssignmentRestServiceImplTest {
    private static final String DEFAULT_PERSON_NUMBER = "444";
    private static final Long DEFAULT_PERSON_ID = 1L;

    @Mock
    private PersonAttestationProfileAssignmentService personAttestationProfileAssignmentService;
    @Mock
    private BulkProcessingHelper bulkProcessingHelper;
    @Mock
    private BulkPersonAttestationProfileAssignmentValidator bulkPersonAttestationProfileAssignmentValidator;

    @InjectMocks
    private PersonNumberAttestationProfileAssignmentRestServiceImpl personAttestationProfileAssignmentRestServiceImpl;

    private MockedStatic<KronosProperties> kronosPropertiesMockedStatic;
    private MockedStatic<PrsnException> prsnExceptionMockedStatic;
    private MockedStatic<PersonNotification> personNotificationMockedStatic;

    @BeforeEach
    public void setUp() {
        kronosPropertiesMockedStatic = Mockito.mockStatic(KronosProperties.class);
        prsnExceptionMockedStatic = Mockito.mockStatic(PrsnException.class);
        personNotificationMockedStatic = Mockito.mockStatic(PersonNotification.class);
    }

    @AfterEach
    public void tearDown() {
        kronosPropertiesMockedStatic.close();
        prsnExceptionMockedStatic.close();
        personNotificationMockedStatic.close();
    }

    @Test
    public void shouldReturnPersonAttestationProfileAssignmentsOnRetrieveByPersonId() {
        List<PersonAttestationProfileAssignmentDTO> assignments = new ArrayList<>();
        PersonAttestationProfileAssignmentDTO assignment = new PersonAttestationProfileAssignmentDTO();
        assignment.setProfile(new ObjectRef(1L, "some name"));
        assignment.setPerson(new ObjectRef(null, DEFAULT_PERSON_NUMBER));
        assignments.add(assignment);
        Response expected = Response.ok(Collections.singletonList(assignment)).build();

        when(personAttestationProfileAssignmentService.getAttestationProfileAssignments(anyString(), eq(false)))
            .thenAnswer(i -> assignments);

        Response actual = personAttestationProfileAssignmentRestServiceImpl.retrieveByPersonNumber(
            DEFAULT_PERSON_NUMBER,false);
        verify(personAttestationProfileAssignmentService, times(1)).getAttestationProfileAssignments(
            DEFAULT_PERSON_NUMBER, false);
        assertEquals(expected.getStatus(), actual.getStatus());
        assertEquals(expected.getEntity(), actual.getEntity());
    }

    @Test
    public void shouldReturnPersonAttestationProfileAssignmentOnCreateRequest() {
        PersonAttestationProfileAssignmentDTO assignment = new PersonAttestationProfileAssignmentDTO();
        assignment.setProfile(new ObjectRef(1L, "some name"));
        assignment.setPerson(new ObjectRef(null, DEFAULT_PERSON_NUMBER));
        Response expected = Response.status(Response.Status.CREATED).entity(assignment).build();
        when(personAttestationProfileAssignmentService.create(any(PersonAttestationProfileAssignmentDTO.class))).thenReturn(assignment);


        Response actual = personAttestationProfileAssignmentRestServiceImpl.create(assignment);
        verify(personAttestationProfileAssignmentService, times(1)).create(assignment);
        assertEquals(expected.getStatus(), actual.getStatus());
        assertEquals(expected.getEntity(), actual.getEntity());
    }

    @Test
    public void multiUpdate() throws Exception {
        List<PersonAttestationProfileAssignment> request = new ArrayList<>();
        PersonAttestationProfileAssignment personAttestationProfileAssignment = new PersonAttestationProfileAssignment();
        ObjectRef employee = new ObjectRef(DEFAULT_PERSON_ID, DEFAULT_PERSON_NUMBER);
        personAttestationProfileAssignment.setEmployee(employee);
        request.add(personAttestationProfileAssignment);

        Map<Integer, PersonAttestationProfileAssignment> assignmentMap = new HashMap<>();
        assignmentMap.put(1, personAttestationProfileAssignment);
        when(bulkProcessingHelper.numberInput(request)).thenReturn(assignmentMap);
        when(bulkProcessingHelper.createMultiUpsertResponse(any(), any(), any())).thenReturn(request);
        when(personAttestationProfileAssignmentService.multiUpsert(any(), any(), any())).thenReturn(assignmentMap);


        final Response response = personAttestationProfileAssignmentRestServiceImpl.multiUpdate(request,false);

        verify(bulkPersonAttestationProfileAssignmentValidator).checkFAPPermissions();
        verify(bulkPersonAttestationProfileAssignmentValidator).checkServiceLimitForMultiUpdateOperation(request);
        verify(bulkPersonAttestationProfileAssignmentValidator).validateRequestBody(request);
        verify(bulkProcessingHelper).resolveAndValidateEmployees(any(), any(), any());
        verify(bulkPersonAttestationProfileAssignmentValidator).checkDuplicates(any(), any());
        verify(personAttestationProfileAssignmentService).multiUpsert(any(), any(),any());
        assertEquals(response.getEntity(), request);
    }

    @Test
    public void multiUpdateWithoutEmployee() throws Exception {
        List<PersonAttestationProfileAssignment> request = new ArrayList<>();
        PersonAttestationProfileAssignment personAttestationProfileAssignment = new PersonAttestationProfileAssignment();
        request.add(personAttestationProfileAssignment);
        Map<Integer, PersonAttestationProfileAssignment> assignmentMap = new HashMap<>();
        assignmentMap.put(1, personAttestationProfileAssignment);
        when(bulkProcessingHelper.numberInput(request)).thenReturn(assignmentMap);
        when(bulkProcessingHelper.createMultiUpsertResponse(any(), any(), any())).thenReturn(request);

        final Response response = personAttestationProfileAssignmentRestServiceImpl.multiUpdate(request,false);

        verify(bulkPersonAttestationProfileAssignmentValidator).checkFAPPermissions();
        verify(bulkPersonAttestationProfileAssignmentValidator).checkServiceLimitForMultiUpdateOperation(request);
        verify(bulkPersonAttestationProfileAssignmentValidator).validateRequestBody(request);
        verify(bulkProcessingHelper).resolveAndValidateEmployees(any(), any(), any());
        verify(bulkPersonAttestationProfileAssignmentValidator).checkDuplicates(any(), any());
        verify(personAttestationProfileAssignmentService).multiUpsert(any(), any(),any());

        assertEquals(response.getEntity(), request);
    }

    @Test
    public void shouldSkipProcessingIfAllElementsFailed() throws Exception {
        List<PersonAttestationProfileAssignment> request = new ArrayList<>();
        Map<Integer, PersonAttestationProfileAssignment> assignmentMap = new HashMap<>();
        when(bulkProcessingHelper.numberInput(request)).thenReturn(assignmentMap);

        personAttestationProfileAssignmentRestServiceImpl.multiUpdate(request,false);
        Mockito.verifyNoInteractions(personAttestationProfileAssignmentService);
    }


    @Test
    public void testPersonAttestationAssignmentCriteriaOfMultiRead(){
        PersonAttestationAssignmentCriteria except = new PersonAttestationAssignmentCriteria();
        except.setIncludeEmployeeRole(true);
        except.setIncludeManagerRole(true);
        testPersonAttestationAssignmentCriteria(AttestationProfileAssignRoleSelect.ALL, except);

        except.setIncludeEmployeeRole(true);
        except.setIncludeManagerRole(false);
        testPersonAttestationAssignmentCriteria(null, except);

        except.setIncludeEmployeeRole(false);
        except.setIncludeManagerRole(true);
        testPersonAttestationAssignmentCriteria(AttestationProfileAssignRoleSelect.MANAGER_ROLE, except);

    }

    @Test
    public void multiUpdateShouldThrowExceptionIfRequestIsNull() {
        try {
            personAttestationProfileAssignmentRestServiceImpl.multiUpdate(null,false);
        } catch (APIException ex) {
            assertEquals("WCO-101307", ex.getErrorCode());
        }
    }

    
    @Test
    public void multiReadShouldThrowExceptionIfRequestIsNull() {
        try {
            personAttestationProfileAssignmentRestServiceImpl.multiRead(null);
        } catch (APIException ex) {
            assertEquals("WCO-101308", ex.getErrorCode());
        }
    }

    private void testPersonAttestationAssignmentCriteria(AttestationProfileAssignRoleSelect select, PersonAttestationAssignmentCriteria except) {
        personAttestationProfileAssignmentRestServiceImpl.multiRead(mockRequest(select));
        ArgumentCaptor<PersonAttestationAssignmentCriteria> captor = ArgumentCaptor.forClass(PersonAttestationAssignmentCriteria.class);
        verify(personAttestationProfileAssignmentService).getPersonAttestationProfileAssignment(any(), captor.capture());
        PersonAttestationAssignmentCriteria actual = captor.getValue();
        assertEquals(except.isIncludeEmployeeRole(), actual.isIncludeEmployeeRole());
        assertEquals(except.isIncludeManagerRole(), actual.isIncludeManagerRole());
        reset(personAttestationProfileAssignmentService);
    }

    private static PersonAttestationProfileAssignmentRequest mockRequest(AttestationProfileAssignRoleSelect select) {
        PersonAttestationProfileAssignmentRequest request = new PersonAttestationProfileAssignmentRequest();
        request.setSelect(select);
        EmployeeRefs employeeRefs = new EmployeeRefs();
        ObjectRefList objectRefList = new ObjectRefList();
        objectRefList.getIdsList().add(1l);
        employeeRefs.setEmployees(objectRefList);
        request.setWhere(employeeRefs);
        return request;
    }


}
