package com.kronos.persons.rest.assignments.service;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.PersonAttendanceProfileBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.model.BatchResponseBean;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.PersistenceException;
import com.kronos.wfc.platform.properties.framework.KronosProperties;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class RestPersonAttendanceProfileTest {

	@InjectMocks
	RestPersonAttendanceProfile restEmployeeProfile;
	@Mock
	private PersonAttendanceProfileService employeeProfileService;

	private PersonAttendanceProfileBean requestBean;

	private PersonIdentityBean employee;
	@Spy 
	private ValidatorUtils validatorUtils = new ValidatorUtils();
	@Mock
	private Personality personality;

	@Mock
    private PersonAssignmentHelper<PersonAttendanceProfileBean> assignmentHelper;

	private MockedStatic<KronosProperties> mockedKronosProperties;
	private MockedStatic<ResponseHandler> mockedResponseHandler;
	private MockedStatic<PrsnException> mockedPrsnException;

	
	@BeforeEach
	public void setUp() throws Exception {
		mockedKronosProperties = Mockito.mockStatic(KronosProperties.class);
		mockedResponseHandler = Mockito.mockStatic(ResponseHandler.class);
		mockedPrsnException = Mockito.mockStatic(PrsnException.class);
		requestBean = new PersonAttendanceProfileBean();
		employee = new PersonIdentityBean();
	}

	@AfterEach
	public void tearDown() throws Exception {
		requestBean = null;
		employee = null;
		mockedKronosProperties.close();
		mockedResponseHandler.close();
		mockedPrsnException.close();
	}

	@Test
	public void testUpdateShouldPassSuccessFully() {
		employee.setPersonNumber("1234");
		employee.setPersonKey(10L);
		requestBean.setPersonIdentity(employee);
		Mockito.when(employeeProfileService.updateRequest(Mockito.any())).thenReturn(personality);
		PersonAttendanceProfileBean responseBean = restEmployeeProfile.update(requestBean);
		assertNull(responseBean);
	}

	@SuppressWarnings("unchecked")
	@Test
	public void testUpdateShouldThrowExceptionForUpdateRequest() {
		assertThrows(APIException.class, () -> {
			Mockito.when(employeeProfileService.updateRequest(requestBean)).thenThrow(APIException.class);
			PersonAttendanceProfileBean responseBean = restEmployeeProfile.update(requestBean);
			assertNotNull(responseBean);
		});

	}

	@Test
	public void testMultiUpdateShouldPassSuccessfully() {
		mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("10");
		List<PersonAttendanceProfileBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);
		Personality personality = Mockito.mock(Personality.class);
		Mockito.when(employeeProfileService.updateRequest(Mockito.any())).thenReturn(personality);
		List<PersonAttendanceProfileBean> responseList = restEmployeeProfile.multiUpdate(requestDataList);
		assertNotNull(responseList);
		assertEquals(1, responseList.size());
	}

	@Test
	public void testRetrieveShouldPassSuccessfully() {
		Mockito.when(assignmentHelper.getPersonAssignmentByPersonId(Mockito.anyLong(), Mockito.any())).thenReturn(requestBean);
		assertEquals(requestBean, restEmployeeProfile.retrieveByPersonId(10L));
	}

	@SuppressWarnings("unchecked")
	@Test
	public void testRetrieveshouldThrowException() {
		assertThrows(APIException.class, () -> {
			Mockito.when(assignmentHelper.getPersonAssignmentByPersonId(Mockito.anyLong(), Mockito.any())).thenThrow(APIException.class);
			restEmployeeProfile.retrieveByPersonId(10L);
		});

	}

	@Test
	public void testRetrieveListshouldPassSuccussfully() {
	    ExtensionSearchCriteria criteria = new ExtensionSearchCriteria();
		mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("10");
		List<PersonAttendanceProfileBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);
		Function<PersonIdentityBean, PersonAttendanceProfileBean> getDataListFunction = employeeProfileService::retrieve;
		List<PersonAttendanceProfileBean> responseList = restEmployeeProfile.retrieveList(criteria);
		assertEquals(0, responseList.size());
	}
	@SuppressWarnings("unchecked")
	@Test
	public void testRetrieveListshouldThrowExceptionForMultiRetrive() {
		assertThrows(APIException.class, () -> {
			ExtensionSearchCriteria criteria = new ExtensionSearchCriteria();
			mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("10");
			List<PersonAttendanceProfileBean> requestDataList = new ArrayList<>();
			requestDataList.add(requestBean);
			Mockito.when(assignmentHelper.getPersonAssignmentList(Mockito.any(), Mockito.any())).thenThrow(APIException.class);
			restEmployeeProfile.retrieveList(criteria);
		});
	}
	
	
	@Test
	public void testMultiUpdateShouldThrowExceptionForMultiUpdate() {
		assertThrows(APIException.class, () -> {
			mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("10");
			List<PersonAttendanceProfileBean> requestDataList = new ArrayList<>();
			requestDataList.add(requestBean);
			Personality personality = Mockito.mock(Personality.class);
			Mockito.when(employeeProfileService.updateRequest(Mockito.any())).thenThrow(new APIException());
			BatchResponseBean responseBean1 = new BatchResponseBean();
			responseBean1.setErrorCode(ExceptionConstants.EXCEPTION_101205);
			restEmployeeProfile.multiUpdate(requestDataList);
		});
	}

	
	@Test
	public void testUpdateShouldThrowGenericException() {
		Mockito.doThrow(new PersistenceException()).when(employeeProfileService).updateRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		try {
			restEmployeeProfile.update(requestBean);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testUpdateShouldThrowException() {
		Mockito.doThrow(new RuntimeException()).when(employeeProfileService).updateRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restEmployeeProfile.update(requestBean);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	
	@Test
	public void testMultiUpdateShouldGenericException() {
		Mockito.doThrow(new PersistenceException()).when(employeeProfileService).updateRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		List<PersonAttendanceProfileBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);
		try {
			restEmployeeProfile.multiUpdate(requestDataList);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testMultiUpdateShouldException() {
		Mockito.doThrow(new RuntimeException()).when(employeeProfileService).updateRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		List<PersonAttendanceProfileBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);
		try {
			restEmployeeProfile.multiUpdate(requestDataList);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	@Test
	public void testRetrieveByPersonNumbershouldPassSuccessfully() {
		PersonIdentityBean personIdentity = new PersonIdentityBean();
		String personNumber="10";
		personIdentity.setPersonNumber(personNumber);
		requestBean.setPersonIdentity(personIdentity );
		Mockito.when(assignmentHelper.getPersonAssignmentByPersonNumber(Mockito.any(),Mockito.any())).thenReturn(requestBean);
		PersonAttendanceProfileBean response = restEmployeeProfile.retrieveByPersonNumber(personNumber);
		assertEquals(requestBean, response);
	}
}
