/*******************************************************************************
 * ApplicationContextManagerImplTest.java
 * Copyright © 2024 UKG Inc. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.context.service;

import com.kronos.commonapp.authz.api.fap.permissions.IPermission;
import com.kronos.commonapp.authz.impl.fap.actions.Action;
import com.kronos.commonapp.authz.impl.fap.controlpoints.AccessControlPoint;
import com.kronos.commonapp.authz.impl.fap.scopes.Scope;
import com.kronos.commonapp.userpreferences.api.IKPreferences;
import com.kronos.persons.context.utils.CurrentUserUtil;
import com.kronos.wfc.commonapp.people.business.person.AccessAssignment;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.user.CurrentUserAccountManager;
import com.kronos.wfc.platform.audit.business.types.AuditType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.business.authorization.profiles.AccessProfile;
import com.kronos.wfc.platform.security.business.state.State;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;

/**
 * Class with unit tests for ApplicationContextManagementImpl.java
 */
@ExtendWith(MockitoExtension.class)
public class ApplicationContextManagerImplTest {

    private static final String ACCESS_CONTROL_POINT_KPI_DEFINITION = "KPI DEFINITION";
    private static final String ACTION_ADD = "ADD";
    private static final String SCOPE_ALL = "ALL";
    public static final String PERMISSION_CONTEXT = "PERMISSION_CONTEXT";

    @InjectMocks
    private ApplicationContextManagerImpl applicationContextManagerImpl;

    @Mock
    private IKPreferences ikPreferences;

    @Mock
    private CurrentUserUtil currentUserUtil;

    private MockedStatic<AccessProfile> mockedAccessProfile;
    private MockedStatic<AuditType> mockedAuditType;
    private MockedStatic<CurrentUserAccountManager> mockedCurrentUserAccountManager;
    private MockedStatic<AccessControlPoint> mockedAccessControlPoint;
    private MockedStatic<Action> mockedAction;
    private MockedStatic<Scope> mockedScope;
    private MockedStatic<State> mockedState;

    @BeforeEach
    public void setUp() {
        mockedAuditType = Mockito.mockStatic(AuditType.class);
        mockedAccessProfile = Mockito.mockStatic(AccessProfile.class);
        mockedCurrentUserAccountManager = Mockito.mockStatic(CurrentUserAccountManager.class);
        mockedAccessControlPoint = Mockito.mockStatic(AccessControlPoint.class);
        mockedAction = Mockito.mockStatic(Action.class);
        mockedScope = Mockito.mockStatic(Scope.class);
        mockedState = Mockito.mockStatic(State.class);
    }

    @AfterEach
    public void tearDownStaticMocks() {
        mockedAccessProfile.close();
        mockedAuditType.close();
        mockedCurrentUserAccountManager.close();
        mockedAccessControlPoint.close();
        mockedAction.close();
        mockedScope.close();
        mockedState.close();
    }

    @Test
    public void testGetPermissionsShouldReturnPermissionsWhenManagerContextTrue() {
        Personality personality = Mockito.mock(Personality.class);
        ObjectIdLong personId = new ObjectIdLong(1L);
        Mockito.when(personality.getPersonId()).thenReturn(personId);
        Mockito.when(CurrentUserAccountManager.getPersonality()).thenReturn(personality);
        AccessAssignment accessAssignmentMock = Mockito.mock(AccessAssignment.class);
        AccessProfile accessProfileMock = Mockito.mock(AccessProfile.class);
        Mockito.when(personality.getAccessAssignment()).thenReturn(accessAssignmentMock);
        Mockito.when(accessAssignmentMock.getAccessProfile()).thenReturn(accessProfileMock);
        Hashtable<String, com.kronos.wfc.platform.security.business.authorization.permissions.Permission> permissionsTable = new Hashtable<>();
        String permissionKey = ACCESS_CONTROL_POINT_KPI_DEFINITION + "." + ACTION_ADD;
        com.kronos.wfc.platform.security.business.authorization.permissions.Permission wfcPermissionMock =
                Mockito.mock(com.kronos.wfc.platform.security.business.authorization.permissions.Permission.class);
        com.kronos.wfc.platform.security.business.authorization.controlpoints.AccessControlPoint wfcAccessControlPointMock =
                Mockito.mock(com.kronos.wfc.platform.security.business.authorization.controlpoints.AccessControlPoint.class);
        Mockito.when(wfcAccessControlPointMock.getName()).thenReturn(ACCESS_CONTROL_POINT_KPI_DEFINITION);
        com.kronos.wfc.platform.security.business.authorization.actions.Action wfcActionMock =
                Mockito.mock(com.kronos.wfc.platform.security.business.authorization.actions.Action.class);
        Mockito.when(wfcActionMock.getName()).thenReturn(ACTION_ADD);
        com.kronos.wfc.platform.security.business.authorization.scopes.Scope wfcScopeMock =
                Mockito.mock(com.kronos.wfc.platform.security.business.authorization.scopes.Scope.class);
        Mockito.when(wfcScopeMock.getName()).thenReturn(SCOPE_ALL);
        Mockito.when(wfcPermissionMock.getAccessControlPoint()).thenReturn(wfcAccessControlPointMock);
        Mockito.when(wfcPermissionMock.getAction()).thenReturn(wfcActionMock);
        Mockito.when(wfcPermissionMock.getScope()).thenReturn(wfcScopeMock);
        permissionsTable.put(permissionKey, wfcPermissionMock);
        Mockito.when(accessProfileMock.getProfilePermissions()).thenReturn(permissionsTable);
        Mockito.when(AccessProfile.isPermitted(Mockito.any(), Mockito.eq(ACCESS_CONTROL_POINT_KPI_DEFINITION),
                Mockito.eq(ACTION_ADD), Mockito.any())).thenReturn(true);
        AccessControlPoint fapAccessControlPointMock = Mockito.mock(AccessControlPoint.class);
        Mockito.when(AccessControlPoint.getAccessControlPoint(ACCESS_CONTROL_POINT_KPI_DEFINITION))
                .thenReturn(fapAccessControlPointMock);
        Action fapActionMock = Mockito.mock(Action.class);
        Mockito.when(Action.getAction(ACTION_ADD)).thenReturn(fapActionMock);
        Scope fapScopeMock = Mockito.mock(Scope.class);
        Mockito.when(Scope.getScope(SCOPE_ALL)).thenReturn(fapScopeMock);

        List<IPermission> result = applicationContextManagerImpl.getPermissions(true);
        IPermission resultPermission = result.get(0);

        assertEquals(1, result.size());
        assertSame(fapAccessControlPointMock, resultPermission.getAccessControlPoint());
        assertSame(fapActionMock, resultPermission.getAction());
        assertSame(fapScopeMock, resultPermission.getScope());
    }

    @Test
    public void testGetApplicationContextShouldReturnApplicationContextWhenSessionStateFindsContext() {
        IApplicationContext expectedApplicationContext = new ApplicationContextImpl();
        Mockito.when(State.getFromSessionState(PERMISSION_CONTEXT)).thenReturn(expectedApplicationContext);

        IApplicationContext result = applicationContextManagerImpl.getApplicationContext();

        assertSame(expectedApplicationContext, result);
    }

    @Test
    public void testGetApplicationContextShouldCreateApplicationContextWhenSessionStateDoesNotFindContext() {
        Mockito.when(State.getFromSessionState(PERMISSION_CONTEXT)).thenReturn(null);
        ApplicationContextManagerImpl applicationContextManagerSpy = Mockito.spy(applicationContextManagerImpl);
        CurrentUser expectedCurrentUser = new CurrentUser();
        Mockito.when(currentUserUtil.getCurrentLoggedInUser()).thenReturn(expectedCurrentUser);
        List<IPermission> expectedPermissions = new ArrayList<>();
        List<IPermission> expectedManagerPermissions = new ArrayList<>();
        Mockito.doReturn(expectedPermissions).when(applicationContextManagerSpy).getPermissions(false);
        Mockito.doReturn(expectedManagerPermissions).when(applicationContextManagerSpy).getPermissions(true);

        IApplicationContext result = applicationContextManagerSpy.getApplicationContext();

        assertEquals(expectedCurrentUser, result.getCurrentUser());
        assertSame(ikPreferences, result.getPreference());
        assertSame(expectedPermissions, result.getPermissions());
        assertSame(expectedManagerPermissions, result.getManagerPermissions());
    }

}
