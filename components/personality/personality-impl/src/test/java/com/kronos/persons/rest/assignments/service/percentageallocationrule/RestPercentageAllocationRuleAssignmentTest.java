/**
 * 
 */
package com.kronos.persons.rest.assignments.service.percentageallocationrule;

import com.google.common.collect.Lists;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.adjustmentrule.RuleCriteriaBeanWrapper;
import com.kronos.persons.rest.assignments.model.percentageallocationrule.PercentageAllocationRuleAssignmentBean;
import com.kronos.persons.rest.assignments.model.percentageallocationrule.PercentageAllocationRuleCriteriaBean;
import com.kronos.persons.rest.assignments.model.percentageallocationrule.PercentageAllocationRuleWrapperBean;
import com.kronos.persons.rest.assignments.service.PersonAssignmentHelper;
import com.kronos.persons.rest.assignments.service.adjustmentrule.bean.AssignmentBeanHolder;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.model.EmployeeCriteria;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.rest.model.ExtensionWhereCriteria;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.mock.web.MockHttpServletResponse;

import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RestPercentageAllocationRuleAssignmentTest
{

    @InjectMocks
    RestPercentageAllocationRuleAssignment restPercentageAllocationRuleAssignment;

    @Mock
    PercentageAllocationRuleAssignmentService assignmentService;
    
    @Mock
    HttpServletResponse httpResponse;

    @Mock
    PersonAssignmentHelper<PercentageAllocationRuleAssignmentBean> personAssignmentHelper;

    private MockedStatic<KronosProperties> kronosPropertiesMockedStatic;

    @BeforeEach
    public void setup() {
        kronosPropertiesMockedStatic = Mockito.mockStatic(KronosProperties.class);
    }

    @AfterEach
    public void tearDown() {
        kronosPropertiesMockedStatic.close();
    }

    @Test
    public void assignPercentageAllocationRule() {
        PercentageAllocationRuleAssignmentBean requestBean = new PercentageAllocationRuleAssignmentBean();
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(assignmentService.assignPercentageAllocationRule(requestBean)).thenReturn(personality);
        PercentageAllocationRuleAssignmentBean responseBean = restPercentageAllocationRuleAssignment.assignPercentageAllocationRule(requestBean);
        assertNotNull(responseBean);
        assertEquals(responseBean,requestBean);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void assignPercentageAllocationRuleException() {
        assertThrows(APIException.class, () -> {
            PercentageAllocationRuleAssignmentBean requestBean = new PercentageAllocationRuleAssignmentBean();
            Mockito.when(assignmentService.assignPercentageAllocationRule(any())).thenThrow(RuntimeException.class);
            restPercentageAllocationRuleAssignment.assignPercentageAllocationRule(requestBean);
        });
    }

    @Test
    public void updatePercentageAllocationRuleAssignment() {
        PercentageAllocationRuleAssignmentBean requestBean = new PercentageAllocationRuleAssignmentBean();
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(assignmentService.updatePercentageAllocationRuleAssignment(requestBean)).thenReturn(personality);
        PercentageAllocationRuleAssignmentBean responseBean = restPercentageAllocationRuleAssignment.updatePercentageAllocationRuleAssignment(requestBean);
        assertNotNull(responseBean);
        assertEquals(responseBean,requestBean);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void updatePercentageAllocationRuleAssignmentException() {
        assertThrows(APIException.class, () -> {
            Mockito.when(assignmentService.updatePercentageAllocationRuleAssignment(any())).thenThrow(RuntimeException.class);
            PercentageAllocationRuleAssignmentBean requestBean = Mockito.mock(PercentageAllocationRuleAssignmentBean.class);
            restPercentageAllocationRuleAssignment.updatePercentageAllocationRuleAssignment(requestBean);
        });
    }

    @Test
    public void multiCreate() {
        kronosPropertiesMockedStatic.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        PercentageAllocationRuleAssignmentBean requestBean = Mockito.mock(PercentageAllocationRuleAssignmentBean.class);
        List<PercentageAllocationRuleAssignmentBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(assignmentService.assignPercentageAllocationRule(any())).thenReturn(personality);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        List<PercentageAllocationRuleAssignmentBean> responseBean = restPercentageAllocationRuleAssignment.multiCreate(requestDataList);
        assertNotNull(responseBean);
        assertEquals(1, responseBean.size());
    }

    @Test
    public void multiUpdate() {
        kronosPropertiesMockedStatic.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        PercentageAllocationRuleAssignmentBean requestBean = Mockito.mock(PercentageAllocationRuleAssignmentBean.class);
        List<PercentageAllocationRuleAssignmentBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(assignmentService.updatePercentageAllocationRuleAssignment(any())).thenReturn(personality);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        List<PercentageAllocationRuleAssignmentBean> responseBean = restPercentageAllocationRuleAssignment.multiUpdate(requestDataList);
        assertNotNull(responseBean);
        assertEquals(1, responseBean.size());
    }

    @Test
    public void multiDelete() {
        kronosPropertiesMockedStatic.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        PercentageAllocationRuleCriteriaBean requestBean = new PercentageAllocationRuleCriteriaBean();
        List<PercentageAllocationRuleCriteriaBean> requestDataList = new ArrayList<>();
        PercentageAllocationRuleAssignmentBean employeeAdjustmentRuleAssignmentBean
                = Mockito.mock(PercentageAllocationRuleAssignmentBean.class);
        requestDataList.add(requestBean);
        AssignmentBeanHolder<PercentageAllocationRuleAssignmentBean> beanHolder =
                new AssignmentBeanHolder<>(employeeAdjustmentRuleAssignmentBean);
        Mockito.when(assignmentService.validateAndReturnHolderForDelete(any()))
                .thenReturn(beanHolder);
        doNothing().when(assignmentService).deletePercentageAllocationRuleAssignments(Lists.newArrayList(beanHolder));
        restPercentageAllocationRuleAssignment.multiDelete(requestDataList);
    }

    @Test
    public void multiDeleteWhenInvalidBean() {
        kronosPropertiesMockedStatic.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        PercentageAllocationRuleCriteriaBean requestBean = new PercentageAllocationRuleCriteriaBean();
        List<PercentageAllocationRuleCriteriaBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        doAnswer(invocation -> {
            RuleCriteriaBeanWrapper wrapper = (RuleCriteriaBeanWrapper) invocation.getArguments()[0];
            wrapper.setApiException(new APIException());
            return new AssignmentBeanHolder<>();
        }).when(assignmentService).validateAndReturnHolderForDelete(any(RuleCriteriaBeanWrapper.class));
        try {
            restPercentageAllocationRuleAssignment.multiDelete(requestDataList);
        } catch (APIException apiException) {
            verify(assignmentService, times(0)).deletePercentageAllocationRuleAssignments(anyList());
        }
    }

    @Test
    public void deletePercentageAllocationRuleAssignmentException() {
        PercentageAllocationRuleCriteriaBean requestBean = new PercentageAllocationRuleCriteriaBean();
        restPercentageAllocationRuleAssignment.deletePercentageAllocationRuleAssignment(requestBean);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void deletePercentageAllocationRuleAssignment() {
        assertThrows(APIException.class, () -> {
        HttpServletResponse httpResponse = new MockHttpServletResponse();
        PercentageAllocationRuleCriteriaBean requestBean = new PercentageAllocationRuleCriteriaBean();
        Mockito.when(assignmentService.deletePercentageAllocationRuleAssignment(requestBean)).thenThrow(RuntimeException.class);
        restPercentageAllocationRuleAssignment.deletePercentageAllocationRuleAssignment(requestBean);
        });
    }

    @Test
    public void getAllPercentageAllocationRuleAssignment() throws Exception {
        Mockito.when(assignmentService.getAllPercentageAllocationRules()).thenReturn(getRuleList());
        List<PercentageAllocationRuleAssignmentBean> list = restPercentageAllocationRuleAssignment.getAllPercentageAllocationRuleAssignments();
        assertNotNull(list);
        assertEquals(2, list.size());
    }

    @Disabled
    @Test
    public void getPercentageAllocationRuleAssignment() throws Exception {
        PercentageAllocationRuleCriteriaBean requestBean = new PercentageAllocationRuleCriteriaBean();
        PersonIdentityBean identityBean = new PersonIdentityBean();
        List<PercentageAllocationRuleAssignmentBean> lst = getRuleList();
        Mockito.when(assignmentService.getPercentageAllocationRules(Mockito.anyLong(), anyString(), anyString(), anyString())).thenReturn(lst);
        List<PercentageAllocationRuleAssignmentBean> list = restPercentageAllocationRuleAssignment
                .getPercentageAllocationRuleAssignment(identityBean.getPersonKey(),requestBean.getProcessor(),requestBean.getEffectiveDate());
        assertNotNull(list);
        assertEquals(2, list.size());
    }

    private List<PercentageAllocationRuleAssignmentBean> getRuleList() {
        List<PercentageAllocationRuleAssignmentBean> list = new ArrayList<>();
        PercentageAllocationRuleAssignmentBean bean1 = new PercentageAllocationRuleAssignmentBean();
        bean1.setEffectiveDate("1111");
        PercentageAllocationRuleAssignmentBean bean2 = new PercentageAllocationRuleAssignmentBean();
        bean2.setEffectiveDate("2222");
        list.add(bean1);
        list.add(bean2);
        return list;
    }

    @Test
    public void getPercentageAllocationRulesByPersonNumbers() throws Exception{
        ExtensionSearchCriteria searchCriteria = new ExtensionSearchCriteria();
        ExtensionWhereCriteria where = new ExtensionWhereCriteria();
        EmployeeCriteria employees = new EmployeeCriteria();
        List<String> values = new ArrayList<>();
        searchCriteria.setReturnUnassignedEmployees(false);
        employees.setKey("personNumber");
        values.add("20338");
        employees.setValues(values);
        where.setEmployees(employees);
        searchCriteria.setWhere(where);
        Mockito.when(personAssignmentHelper.getBulkPersonAssignmentList(Mockito.any(),Mockito.any(),Mockito.anyBoolean())).thenReturn(getRuleList());
        List<PercentageAllocationRuleAssignmentBean> list = restPercentageAllocationRuleAssignment.getPercentageAllocationRulesByPersonNumbers(searchCriteria);
        assertEquals(2, list.size());
    }
    
    @Test
    public void getPARulesByPersonNumWithFailOnNoAssignmentFalse() throws Exception{
        ExtensionSearchCriteria searchCriteria = new ExtensionSearchCriteria();
        ExtensionWhereCriteria where = new ExtensionWhereCriteria();
        EmployeeCriteria employees = new EmployeeCriteria();
        List<String> values = new ArrayList<>();
        searchCriteria.setFailOnNoAssignment(false);
        employees.setKey("personNumber");
        values.add("20338");
        employees.setValues(values);
        where.setEmployees(employees);
        searchCriteria.setWhere(where);
        Mockito.when(personAssignmentHelper.getBulkPersonAssignmentList(Mockito.any(),Mockito.any(),Mockito.anyBoolean())).thenReturn(new ArrayList<>());
        List<PercentageAllocationRuleAssignmentBean> list = restPercentageAllocationRuleAssignment.getPercentageAllocationRulesByPersonNumbers(searchCriteria);
        assertEquals(0, list.size());
    }
    
    @Test
    public void getPARulesByPersonNumWithFailOnNoAssignmentFalseAndReturnUnassignFlagTrue() throws Exception{
        ExtensionSearchCriteria searchCriteria = new ExtensionSearchCriteria();
        ExtensionWhereCriteria where = new ExtensionWhereCriteria();
        EmployeeCriteria employees = new EmployeeCriteria();
        List<String> values = new ArrayList<>();
        searchCriteria.setFailOnNoAssignment(false);
        searchCriteria.setReturnUnassignedEmployees(true);
        employees.setKey("personNumber");
        values.add("20338");
        employees.setValues(values);
        where.setEmployees(employees);
        searchCriteria.setWhere(where);
        Mockito.when(personAssignmentHelper.getBulkPersonAssignmentList(Mockito.any(),Mockito.any(),Mockito.anyBoolean())).thenReturn(new ArrayList<>());
        List<PercentageAllocationRuleAssignmentBean> list = restPercentageAllocationRuleAssignment.getPercentageAllocationRulesByPersonNumbers(searchCriteria);
        assertEquals(0, list.size());
    }
    
    @Test
    public void getPARulesByPersonNumWithReturnUnassignFlagTrue() throws Exception {
        ExtensionSearchCriteria searchCriteria = new ExtensionSearchCriteria();
        ExtensionWhereCriteria where = new ExtensionWhereCriteria();
        EmployeeCriteria employees = new EmployeeCriteria();
        List<String> values = new ArrayList<>();
        searchCriteria.setReturnUnassignedEmployees(true);
        employees.setKey("personNumber");
        values.add("20338");
        employees.setValues(values);
        where.setEmployees(employees);
        searchCriteria.setWhere(where);
        Mockito.when(personAssignmentHelper.getBulkPersonAssignmentList(Mockito.any(),Mockito.any(),Mockito.anyBoolean())).thenReturn(getRuleList());
        List<PercentageAllocationRuleAssignmentBean> list = restPercentageAllocationRuleAssignment.getPercentageAllocationRulesByPersonNumbers(searchCriteria);
        assertEquals(2, list.size());
    } 
    
    @Test
    public void testmultiUpsert() {
        kronosPropertiesMockedStatic.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        PercentageAllocationRuleWrapperBean requestBean = Mockito.mock(PercentageAllocationRuleWrapperBean.class);
        List<PercentageAllocationRuleWrapperBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(assignmentService.updatePercentageAllocationRuleAssignment(Mockito.any())).thenReturn(personality);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        List<PercentageAllocationRuleWrapperBean> responseBean = restPercentageAllocationRuleAssignment.multiUpsert(requestDataList);
        assertNotNull(responseBean);
        assertEquals(1, responseBean.size());
    }

}
