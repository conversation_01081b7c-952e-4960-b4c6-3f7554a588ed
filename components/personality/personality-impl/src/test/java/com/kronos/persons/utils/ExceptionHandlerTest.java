/*******************************************************************************
 * ExceptionHandlerTest.java
 * Copyright © 2024 UKG Inc. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.utils;

import java.lang.reflect.Constructor;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;

import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.persons.rest.model.RestErrorBean;
import com.kronos.wfc.platform.persistence.framework.PersistenceException;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.wfc.platform.xml.api.bean.APIProcessingException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class ExceptionHandlerTest {

    private static final String ASSIGNMENT_PROFILE_BEAN_NAME = "assignmentProfile";
    private static final String UPDATE_ACTION = "update";
    private static final Long ACTION_FAILURE_CODE = 19L;

    @Test
    public void handleException() {
        Map<String, String> userParamMap = new HashMap<>();
        userParamMap.put("test", "value");
        APIException apie = ExceptionHandler.getAPIExceptionWithUserParameters("1", userParamMap);
        Map<String, String> exceptionUserParamMap = apie.getUserParameters();
        assertEquals("value", exceptionUserParamMap.get("test"));
    }

    @Test
    public void getExceptionGeneric() {
        APIException exception = ExceptionHandler.getException(APIProcessingException.actionDataAccessViolation("Person", "Create"));
        assertEquals(ExceptionConstants.EXCEPTION_101205, exception.getErrorCode());
    }

    @Test
    public void getException() {
        APIException exception = ExceptionHandler.getException(new Exception());
        assertEquals(ExceptionConstants.EXCEPTION_101206, exception.getErrorCode());
    }

    @Test
    public void getAPIException() {
        APIException exception = ExceptionHandler
            .getAPIException(ExceptionConstants.EXCEPTION_101206);
        assertEquals(ExceptionConstants.EXCEPTION_101206, exception.getErrorCode());
    }

    @Test
    public void testConstructorIsPrivate() {
        try {
            Constructor<ExceptionHandler> constructor = ExceptionHandler.class.getDeclaredConstructor();
            assertTrue(Modifier.isPrivate(constructor.getModifiers()));
            constructor.setAccessible(true);
            constructor.newInstance();
        } catch (Exception e) {
            fail();
        }
    }

    @Test
    public void testGivenAPIExceptionAsInput_WhenHandleException_ThenReturnRestErrorBean(){
        APIException input = PrsnValidationException.invalidPropertyValue("RequestData", null);

        RestErrorBean result = ExceptionHandler.handleException(input, ASSIGNMENT_PROFILE_BEAN_NAME, UPDATE_ACTION);

        assertEquals(ACTION_FAILURE_CODE, result.getErrorCode());
        assertEquals(ASSIGNMENT_PROFILE_BEAN_NAME, result.getBeanName());
        assertTrue(result.getErrorData().contains("actionName=update"));
    }

    @Test
    public void testGivenGenericExceptionAsInput_WhenHandleException_ThenReturnRestErrorBean(){
        Long invalidArgumentCode = 6L;
        PersistenceException input = new PersistenceException(invalidArgumentCode.intValue());

        RestErrorBean result = ExceptionHandler.handleException(input, ASSIGNMENT_PROFILE_BEAN_NAME, UPDATE_ACTION);

        assertEquals(invalidArgumentCode, result.getErrorCode());
        assertTrue(result.getErrorData().contains("StatementKey=Unknown"));
    }

}
