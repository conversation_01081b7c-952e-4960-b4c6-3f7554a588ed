package com.kronos.persons.rest.assignments.service;

import com.google.common.collect.Lists;
import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.dataaccess.entity.WtkEmployee;
import com.kronos.people.personality.dataaccess.repository.WTKPeopleRepository;
import com.kronos.people.personality.service.PersonalityService;
import com.kronos.persons.rest.assignments.model.SupervisorAssignmentRequestBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.audit.business.types.AuditType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.business.authorization.profiles.AccessProfile;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;


@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonSupervisorAssignmentServiceTest {

    @InjectMocks
    private PersonSupervisorAssignmentService personSupervisorAssignmentService;
    @Mock
    private PersonIdentityBeanValidator personIdentityBeanValidator;
    @Mock
    private WTKPeopleRepository wtkPeopleRepository;
    @Mock
    private ValidatorUtils validatorUtils;
    @Mock
    private Person person;

    @Mock
    PersonalityService personalityService;

    private static final String ACP_PE_REPORTS_TO_FIELD = "PE_REPORTS_TO_FIELD";
    private static final String SUPERVISOR_PERSON_NUMBER = "supervisor_person_number";

    private MockedStatic<AuditType> mockedAuditType;
    private MockedStatic<AccessProfile> mockedAccessProfile;
    private MockedStatic<Person> mockedPerson;


    @BeforeEach
    public void setUp() {
        mockedAuditType = Mockito.mockStatic(AuditType.class);
        mockedAccessProfile = Mockito.mockStatic(AccessProfile.class);
        mockedPerson = Mockito.mockStatic(Person.class);
    }

    @AfterEach
    public void tearDown() {
        mockedAuditType.close();
        mockedAccessProfile.close();
        mockedPerson.close();
    }

    @Test
    public void testRetrieve() {
        PersonIdentityBean identityBean = new PersonIdentityBean();
        Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, identityBean, null);
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(personIdentityBeanValidator.getPersonality(identityBean)).thenReturn(personality);
        mockedAccessProfile.when(() -> AccessProfile.isPermitted(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        WtkEmployee wtkEmployee = new WtkEmployee();
        wtkEmployee.setEmployeeId(1L);
        wtkEmployee.setPersonId(1L);
        wtkEmployee.setWtkEmployeeId(2L);
        List<WtkEmployee> wtkEmployees = Lists.newArrayList();
        wtkEmployees.add(wtkEmployee);
        Optional<List<WtkEmployee>> optWtkEmployeeList = Optional.of(wtkEmployees);
        Mockito.when(wtkPeopleRepository.findByPersonId(Mockito.anyLong())).thenReturn(optWtkEmployeeList);
        Mockito.when(personIdentityBeanValidator.createForPersonNumber(Mockito.anyString())).thenReturn(identityBean);
        Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);

        Mockito.when(validatorUtils.notNull(Mockito.any())).thenReturn(false);
        personSupervisorAssignmentService.retrieve(identityBean);

        Mockito.when(validatorUtils.notNull(Mockito.any())).thenReturn(true);
        personSupervisorAssignmentService.retrieve(identityBean);

        Mockito.when(validatorUtils.notNull(Mockito.any())).thenReturn(true);
        wtkEmployee.setSupervisorId(2L);
        wtkEmployees = Lists.newArrayList();
        wtkEmployees.add(wtkEmployee);
        optWtkEmployeeList = Optional.of(wtkEmployees);
        Mockito.when(wtkPeopleRepository.findByPersonId(Mockito.anyLong())).thenReturn(optWtkEmployeeList);
        Mockito.when(personIdentityBeanValidator.createForPersonNumber(Mockito.anyString())).thenReturn(identityBean);
        personSupervisorAssignmentService.retrieve(identityBean);
        Person.getByPersonId(Mockito.any());
    }

    @Test
    public void testRetrieveThrowExceptionWhenOptWtkEmployeeListValuseIsNull() {
        assertThrows(APIException.class, () -> {
            PersonIdentityBean identityBean = new PersonIdentityBean();
            Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, identityBean, null);
            Personality personality = Mockito.mock(Personality.class);
            Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
            Mockito.when(personIdentityBeanValidator.getPersonality(identityBean)).thenReturn(personality);
            Mockito.when(AccessProfile.isPermitted(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
            List<WtkEmployee> wtkEmployees = Lists.newArrayList();
            Optional<List<WtkEmployee>> optWtkEmployeeList = Optional.of(wtkEmployees);
            Mockito.when(wtkPeopleRepository.findByPersonId(Mockito.anyLong())).thenReturn(optWtkEmployeeList);

            personSupervisorAssignmentService.retrieve(identityBean);
        });
    }

    @Test
    public void testRetrieveThrowExceptionWhenOptWtkEmployeeListIsEmpty() {
        assertThrows(APIException.class, () -> {
            PersonIdentityBean identityBean = new PersonIdentityBean();
            Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, identityBean, null);
            Personality personality = Mockito.mock(Personality.class);
            Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
            Mockito.when(personIdentityBeanValidator.getPersonality(identityBean)).thenReturn(personality);
            mockedAccessProfile.when(() -> AccessProfile.isPermitted(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
            Optional<List<WtkEmployee>> optWtkEmployeeList = Optional.empty();
            Mockito.when(wtkPeopleRepository.findByPersonId(Mockito.anyLong())).thenReturn(optWtkEmployeeList);

            personSupervisorAssignmentService.retrieve(identityBean);
        });
    }

    @Test
    public void testRetrieveThrowExceptionWhenNOPermission() {
        assertThrows(APIException.class, () -> {
            PersonIdentityBean identityBean = new PersonIdentityBean();
            Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, identityBean, null);
            Personality personality = Mockito.mock(Personality.class);
            Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
            Mockito.when(personIdentityBeanValidator.getPersonality(identityBean)).thenReturn(personality);
            mockedAccessProfile.when(() -> AccessProfile.isPermitted(Mockito.anyString(), Mockito.anyString())).thenReturn(false);
            personSupervisorAssignmentService.retrieve(identityBean);
        });
    }

    @Test
    public void testUpdateRequest() throws Exception {
        SupervisorAssignmentRequestBean supervisorAssigmentRequestBean = new SupervisorAssignmentRequestBean();
        supervisorAssigmentRequestBean.setSupervisorName("supervisor_name");
        supervisorAssigmentRequestBean.setSupervisorPersonNumber(SUPERVISOR_PERSON_NUMBER);
        supervisorAssigmentRequestBean.setUnAssignExisting(true);
        PersonIdentityBean personIdentity = new PersonIdentityBean();
        personIdentity.setPersonNumber("1");
        supervisorAssigmentRequestBean.setPersonIdentity(personIdentity);


        PersonIdentityBean personIdentitySupervisorBean = new PersonIdentityBean();
        personIdentitySupervisorBean.setPersonNumber(SUPERVISOR_PERSON_NUMBER);

        Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, supervisorAssigmentRequestBean.getPersonIdentity(), null);
        Personality personality = Mockito.mock(Personality.class);
        Personality supervisorPersonality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(supervisorPersonality.getPersonId()).thenReturn(new ObjectIdLong(2L));

        Mockito.when(personIdentityBeanValidator.getPersonality(supervisorAssigmentRequestBean.getPersonIdentity())).thenReturn(personality);
        Mockito.when(personIdentityBeanValidator.getPersonality(personIdentitySupervisorBean)).thenReturn(supervisorPersonality);

        mockedAccessProfile.when(() -> AccessProfile.isPermitted(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Mockito.when(personalityService.isManager(Mockito.any())).thenReturn(true);

        Optional<List<WtkEmployee>> optWtkEmployeeList  = prepareWtkEmployeeList(1L);

        Mockito.when(wtkPeopleRepository.findByPersonId(Mockito.anyLong())).thenReturn(optWtkEmployeeList);
        Mockito.when(personIdentityBeanValidator.createForPersonNumber(Mockito.anyString())).thenReturn(supervisorAssigmentRequestBean.getPersonIdentity());

        Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
        Mockito.when(validatorUtils.notNull(Mockito.any())).thenReturn(true);
        Mockito.when(wtkPeopleRepository.save(Mockito.any())).thenReturn(optWtkEmployeeList.get().get(0));

        Personality personsality = personSupervisorAssignmentService.updateRequest(supervisorAssigmentRequestBean);

        verify(wtkPeopleRepository, atLeastOnce()).save(Mockito.any());
        assertEquals(new ObjectIdLong(1L), personsality.getPersonId());
        assertEquals(Long.valueOf(2), optWtkEmployeeList.get().get(0).getSupervisorId());
    }

    @Test
    public void testUpdateRequestWhenNOPermission() {
        assertThrows(APIException.class, () -> {
            SupervisorAssignmentRequestBean supervisorAssigmentRequestBean = new SupervisorAssignmentRequestBean();
            supervisorAssigmentRequestBean.setSupervisorName("supervisor_name");
            supervisorAssigmentRequestBean.setSupervisorPersonNumber(SUPERVISOR_PERSON_NUMBER);
            supervisorAssigmentRequestBean.setUnAssignExisting(true);

            Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, supervisorAssigmentRequestBean.getPersonIdentity(), null);
            Personality personality = Mockito.mock(Personality.class);
            Personality supervisorPersonality = Mockito.mock(Personality.class);
            Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
            Mockito.when(supervisorPersonality.getPersonId()).thenReturn(new ObjectIdLong(2L));


            Mockito.when(personIdentityBeanValidator.getPersonality(supervisorAssigmentRequestBean.getPersonIdentity())).thenReturn(personality);

            Mockito.when(AccessProfile.isPermitted(Mockito.anyString(), Mockito.anyString())).thenReturn(false);

            Optional<List<WtkEmployee>> optWtkEmployeeList = prepareWtkEmployeeList(1L);

            Mockito.when(wtkPeopleRepository.findByPersonId(Mockito.anyLong())).thenReturn(optWtkEmployeeList);
            Mockito.when(personIdentityBeanValidator.createForPersonNumber(Mockito.anyString())).thenReturn(supervisorAssigmentRequestBean.getPersonIdentity());

            Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
            Mockito.when(validatorUtils.notNull(Mockito.any())).thenReturn(true);
            Mockito.when(wtkPeopleRepository.save(Mockito.any())).thenReturn(optWtkEmployeeList.get().get(0));
            personSupervisorAssignmentService.updateRequest(supervisorAssigmentRequestBean);
        });
    }


    @Test
    public void testUpdateRequestWhenInvalidSupervisorThenThrowException() {
        assertThrows(APIException.class, () -> {
            SupervisorAssignmentRequestBean supervisorAssigmentRequestBean = new SupervisorAssignmentRequestBean();
            supervisorAssigmentRequestBean.setSupervisorName("supervisor_name");
            supervisorAssigmentRequestBean.setSupervisorPersonNumber(SUPERVISOR_PERSON_NUMBER);
            supervisorAssigmentRequestBean.setUnAssignExisting(true);

            Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, supervisorAssigmentRequestBean.getPersonIdentity(), null);
            Personality personality = Mockito.mock(Personality.class);
            Personality supervisorPersonality = Mockito.mock(Personality.class);
            Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
            Mockito.when(supervisorPersonality.getPersonId()).thenReturn(new ObjectIdLong(2L));

            Mockito.when(personIdentityBeanValidator.getPersonality(supervisorAssigmentRequestBean.getPersonIdentity())).thenThrow(APIException.class);
            Mockito.when(AccessProfile.isPermitted(Mockito.anyString(), Mockito.anyString())).thenReturn(false);

            Optional<List<WtkEmployee>> optWtkEmployeeList = prepareWtkEmployeeList(1L);

            Mockito.when(wtkPeopleRepository.findByPersonId(Mockito.anyLong())).thenReturn(optWtkEmployeeList);
            Mockito.when(personIdentityBeanValidator.createForPersonNumber(Mockito.anyString())).thenReturn(supervisorAssigmentRequestBean.getPersonIdentity());

            Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
            Mockito.when(validatorUtils.notNull(Mockito.any())).thenReturn(true);
            Mockito.when(wtkPeopleRepository.save(Mockito.any())).thenReturn(optWtkEmployeeList.get().get(0));
            personSupervisorAssignmentService.updateRequest(supervisorAssigmentRequestBean);
        });
    }

    @Test
    public void testUpdateRequestWhenWtkEmployeeListIsEmpty() {
        assertThrows(APIException.class, () -> {
            SupervisorAssignmentRequestBean supervisorAssigmentRequestBean = new SupervisorAssignmentRequestBean();
            supervisorAssigmentRequestBean.setSupervisorName("supervisor_name");
            supervisorAssigmentRequestBean.setSupervisorPersonNumber(SUPERVISOR_PERSON_NUMBER);
            supervisorAssigmentRequestBean.setUnAssignExisting(true);

            Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, supervisorAssigmentRequestBean.getPersonIdentity(), null);
            Personality personality = Mockito.mock(Personality.class);
            Personality supervisorPersonality = Mockito.mock(Personality.class);
            Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
            Mockito.when(supervisorPersonality.getPersonId()).thenReturn(new ObjectIdLong(2L));


            Mockito.when(personIdentityBeanValidator.getPersonality(supervisorAssigmentRequestBean.getPersonIdentity())).thenReturn(personality);

            Mockito.when(AccessProfile.isPermitted(Mockito.anyString(), Mockito.anyString())).thenReturn(false);

            Optional<List<WtkEmployee>> optWtkEmployeeList = Optional.empty();

            Mockito.when(wtkPeopleRepository.findByPersonId(Mockito.anyLong())).thenReturn(optWtkEmployeeList);
            Mockito.when(personIdentityBeanValidator.createForPersonNumber(Mockito.anyString())).thenReturn(supervisorAssigmentRequestBean.getPersonIdentity());

            Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
            Mockito.when(validatorUtils.notNull(Mockito.any())).thenReturn(true);
            personSupervisorAssignmentService.updateRequest(supervisorAssigmentRequestBean);
        });
    }

    @Test
    public void testUpdateRequestValidateUnAssignExistingIsFalse() {
        SupervisorAssignmentRequestBean supervisorAssigmentRequestBean = new SupervisorAssignmentRequestBean();
        supervisorAssigmentRequestBean.setSupervisorName("supervisor_name");
        supervisorAssigmentRequestBean.setSupervisorPersonNumber(SUPERVISOR_PERSON_NUMBER);
        supervisorAssigmentRequestBean.setUnAssignExisting(false);

        Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, supervisorAssigmentRequestBean.getPersonIdentity(), null);
        Personality personality = Mockito.mock(Personality.class);
        Personality supervisorPersonality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(supervisorPersonality.getPersonId()).thenReturn(new ObjectIdLong(2L));


        Mockito.when(personIdentityBeanValidator.getPersonality(supervisorAssigmentRequestBean.getPersonIdentity())).thenReturn(personality);

        Mockito.when(AccessProfile.isPermitted(Mockito.anyString(), Mockito.anyString())).thenReturn(true);

        Optional<List<WtkEmployee>> optWtkEmployeeList  = prepareWtkEmployeeList(1L);

        Mockito.when(wtkPeopleRepository.findByPersonId(Mockito.anyLong())).thenReturn(optWtkEmployeeList);
        Mockito.when(personIdentityBeanValidator.createForPersonNumber(Mockito.anyString())).thenReturn(supervisorAssigmentRequestBean.getPersonIdentity());

        Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
        Mockito.when(validatorUtils.notNull(Mockito.any())).thenReturn(true);
        Mockito.when(wtkPeopleRepository.save(Mockito.any())).thenReturn(optWtkEmployeeList.get().get(0));

        Personality personsality = personSupervisorAssignmentService.updateRequest(supervisorAssigmentRequestBean);

        verify(wtkPeopleRepository, never()).save(Mockito.any());
        assertNull(personsality);
    }

    @Test
    public void testUpdateRequestValidateUnAssignExistingIsNull() {
        SupervisorAssignmentRequestBean supervisorAssigmentRequestBean = new SupervisorAssignmentRequestBean();
        supervisorAssigmentRequestBean.setSupervisorName("supervisor_name");
        supervisorAssigmentRequestBean.setSupervisorPersonNumber(SUPERVISOR_PERSON_NUMBER);

        Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, supervisorAssigmentRequestBean.getPersonIdentity(), null);
        Personality personality = Mockito.mock(Personality.class);
        Personality supervisorPersonality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(supervisorPersonality.getPersonId()).thenReturn(new ObjectIdLong(2L));


        Mockito.when(personIdentityBeanValidator.getPersonality(supervisorAssigmentRequestBean.getPersonIdentity())).thenReturn(personality);

        Mockito.when(AccessProfile.isPermitted(Mockito.anyString(), Mockito.anyString())).thenReturn(true);

        Optional<List<WtkEmployee>> optWtkEmployeeList  = prepareWtkEmployeeList(1L);

        Mockito.when(wtkPeopleRepository.findByPersonId(Mockito.anyLong())).thenReturn(optWtkEmployeeList);
        Mockito.when(personIdentityBeanValidator.createForPersonNumber(Mockito.anyString())).thenReturn(supervisorAssigmentRequestBean.getPersonIdentity());

        Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
        Mockito.when(validatorUtils.notNull(Mockito.any())).thenReturn(true);
        Mockito.when(wtkPeopleRepository.save(Mockito.any())).thenReturn(optWtkEmployeeList.get().get(0));

        Personality personsality = personSupervisorAssignmentService.updateRequest(supervisorAssigmentRequestBean);

        verify(wtkPeopleRepository, never()).save(Mockito.any());
        assertNull(personsality);
    }

    @Test
    public void testUpdateRequestUnAssignSupervisorWhenSupervisorNumberIsNull() {
        SupervisorAssignmentRequestBean supervisorAssigmentRequestBean = new SupervisorAssignmentRequestBean();
        supervisorAssigmentRequestBean.setSupervisorName("null");
        supervisorAssigmentRequestBean.setSupervisorPersonNumber("null");
        supervisorAssigmentRequestBean.setUnAssignExisting(true);
        PersonIdentityBean personIdentity = new PersonIdentityBean();
        supervisorAssigmentRequestBean.setPersonIdentity(personIdentity);

        Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, supervisorAssigmentRequestBean.getPersonIdentity(), null);
        Personality personality = Mockito.mock(Personality.class);
        Personality supervisorPersonality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(supervisorPersonality.getPersonId()).thenReturn(new ObjectIdLong(2L));


        Mockito.when(personIdentityBeanValidator.getPersonality(supervisorAssigmentRequestBean.getPersonIdentity())).thenReturn(personality);

        Mockito.when(AccessProfile.isPermitted(Mockito.anyString(), Mockito.anyString())).thenReturn(true);

        Optional<List<WtkEmployee>> optWtkEmployeeList  = prepareWtkEmployeeList(1L);

        Mockito.when(wtkPeopleRepository.findByPersonId(Mockito.anyLong())).thenReturn(optWtkEmployeeList);
        Mockito.when(personIdentityBeanValidator.createForPersonNumber(Mockito.anyString())).thenReturn(supervisorAssigmentRequestBean.getPersonIdentity());
        Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
        Mockito.when(validatorUtils.notNull(Mockito.any())).thenReturn(true);
        Mockito.when(wtkPeopleRepository.save(Mockito.any())).thenReturn(optWtkEmployeeList.get().get(0));

        personSupervisorAssignmentService.updateRequest(supervisorAssigmentRequestBean);

        verify(wtkPeopleRepository, atLeastOnce()).save(Mockito.any());
        assertEquals(null, optWtkEmployeeList.get().get(0).getSupervisorId());
    }

    @Test
    public void testUpdateRequestUnAssignSupervisorWhenSupervisorNumberIsEmptyString() {
        SupervisorAssignmentRequestBean supervisorAssigmentRequestBean = new SupervisorAssignmentRequestBean();
        supervisorAssigmentRequestBean.setSupervisorName("");
        supervisorAssigmentRequestBean.setSupervisorPersonNumber("");
        supervisorAssigmentRequestBean.setUnAssignExisting(true);
        PersonIdentityBean personIdentity = new PersonIdentityBean();
        supervisorAssigmentRequestBean.setPersonIdentity(personIdentity);

        Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, supervisorAssigmentRequestBean.getPersonIdentity(), null);
        Personality personality = Mockito.mock(Personality.class);
        Personality supervisorPersonality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(supervisorPersonality.getPersonId()).thenReturn(new ObjectIdLong(2L));


        Mockito.when(personIdentityBeanValidator.getPersonality(supervisorAssigmentRequestBean.getPersonIdentity())).thenReturn(personality);

        Mockito.when(AccessProfile.isPermitted(Mockito.anyString(), Mockito.anyString())).thenReturn(true);

        Optional<List<WtkEmployee>> optWtkEmployeeList  = prepareWtkEmployeeList(1L);

        Mockito.when(wtkPeopleRepository.findByPersonId(Mockito.anyLong())).thenReturn(optWtkEmployeeList);
        Mockito.when(personIdentityBeanValidator.createForPersonNumber(Mockito.anyString())).thenReturn(supervisorAssigmentRequestBean.getPersonIdentity());

        Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
        Mockito.when(validatorUtils.notNull(Mockito.any())).thenReturn(true);
        Mockito.when(wtkPeopleRepository.save(Mockito.any())).thenReturn(optWtkEmployeeList.get().get(0));

        personSupervisorAssignmentService.updateRequest(supervisorAssigmentRequestBean);

        verify(wtkPeopleRepository, atLeastOnce()).save(Mockito.any());
        assertEquals(null, optWtkEmployeeList.get().get(0).getSupervisorId());
    }

    private Optional<List<WtkEmployee>> prepareWtkEmployeeList(long personId) {
        WtkEmployee wtkEmployee = new WtkEmployee();
        wtkEmployee.setEmployeeId(personId);
        wtkEmployee.setPersonId(personId);
        wtkEmployee.setWtkEmployeeId(2L);
        List<WtkEmployee> wtkEmployees = Lists.newArrayList();
        wtkEmployees.add(wtkEmployee);
        Optional<List<WtkEmployee>> testUpdateRequest = Optional.of(wtkEmployees);
        return testUpdateRequest;
    }

}