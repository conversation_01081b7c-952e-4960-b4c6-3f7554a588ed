package com.kronos.persons.rest.assignments.service.adjustmentrule;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.adjustmentrule.AdjustmentRuleCriteriaBean;
import com.kronos.persons.rest.assignments.model.adjustmentrule.AdjustmentRuleMultipleAssignmentBean;
import com.kronos.persons.rest.assignments.model.adjustmentrule.EmployeeAdjustmentRuleAssignmentBean;
import com.kronos.persons.rest.assignments.model.adjustmentrule.EmployeeAdjustmentRuleAssignmentWrapper;
import com.kronos.persons.rest.assignments.model.adjustmentrule.RuleCriteriaBeanWrapper;
import com.kronos.persons.rest.assignments.service.PersonAssignmentHelper;
import com.kronos.persons.rest.assignments.service.adjustmentrule.bean.AssignmentBeanHolder;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RestEmployeeAdjustmentRuleAssignmentTest {

    @InjectMocks
    private RestEmployeeAdjustmentRuleAssignment restEmployeeAdjustmentRuleAssignment;

    @Mock
    private PersonAssignmentHelper<EmployeeAdjustmentRuleAssignmentBean> personAssignmentHelpMock;


    private EmployeeAdjustmentRuleAssignmentBean employeeAdjustmentRuleAssignmentBean;

    private List<EmployeeAdjustmentRuleAssignmentBean> employeeAdjustmentRuleAssignmentBeanList;

    private PersonIdentityBean personIdentityBean;

    private List<AdjustmentRuleMultipleAssignmentBean> AdjustmentRuleMultipleAssignmentBeanList;

    private AdjustmentRuleMultipleAssignmentBean AdjustmentRuleMultipleAssignmentBean;

    @Mock
    private EmployeeAdjustmentRuleAssignmentService assignmentService;

    private MockedStatic<KronosProperties> kronosPropertiesMock;

    @BeforeEach
    public void init() {
        employeeAdjustmentRuleAssignmentBean = new EmployeeAdjustmentRuleAssignmentBean();
        personIdentityBean = new PersonIdentityBean();
        personIdentityBean.setPersonNumber("123");
        employeeAdjustmentRuleAssignmentBean.setPersonIdentity(personIdentityBean);
        employeeAdjustmentRuleAssignmentBean.setProcessor("au");
        employeeAdjustmentRuleAssignmentBean.setProcessorId(1L);
        employeeAdjustmentRuleAssignmentBean.setExpirationDate("2024-02-01");
        employeeAdjustmentRuleAssignmentBean.setOriginalEffectiveDate("2024-02-01");
        employeeAdjustmentRuleAssignmentBean.setId("1");
        employeeAdjustmentRuleAssignmentBeanList = new ArrayList<>();
        employeeAdjustmentRuleAssignmentBeanList.add(employeeAdjustmentRuleAssignmentBean);
        AdjustmentRuleMultipleAssignmentBean = new AdjustmentRuleMultipleAssignmentBean();
        AdjustmentRuleMultipleAssignmentBean.setUnAssignExisting(true);
        AdjustmentRuleMultipleAssignmentBean.setPersonIdentity(personIdentityBean);
        AdjustmentRuleMultipleAssignmentBean.setAssignments(employeeAdjustmentRuleAssignmentBeanList);
        AdjustmentRuleMultipleAssignmentBeanList = new ArrayList<>();
        AdjustmentRuleMultipleAssignmentBeanList.add(AdjustmentRuleMultipleAssignmentBean);
        kronosPropertiesMock = Mockito.mockStatic(KronosProperties.class);
        kronosPropertiesMock.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
    }

    @AfterEach
    public void tearDown() {
        kronosPropertiesMock.close();
        Mockito.clearAllCaches();
        Mockito.framework().clearInlineMocks();
    }

    @Test
    public void testAssignEmployeeAdjustmentRule() {
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(assignmentService.assignAdjustmentRule(any())).thenReturn(personality);
        EmployeeAdjustmentRuleAssignmentBean requestBean = Mockito.mock(EmployeeAdjustmentRuleAssignmentBean.class);
        EmployeeAdjustmentRuleAssignmentBean responseBean = restEmployeeAdjustmentRuleAssignment
                .assignEmployeeAdjustmentRule(requestBean);
        assertNotNull(responseBean);
        assertEquals(responseBean, requestBean);
    }

    @Test()
    public void whenGivenExtensionSearchCriteria_thenExpectPersonAssignmentList() throws Exception {
        String data = "{  \"where\": {    \"employees\": {      \"key\": \"personnumber\",      \"values\": [        \"10046\"      ]    },    \"extensionType\": \"employee\",    \"snapshotDate\": \"2017-08-10\",    \"onlyActivePerson\": true  }}";
        ObjectMapper mapper = new ObjectMapper();
        ExtensionSearchCriteria criteria = mapper.readValue(data, ExtensionSearchCriteria.class);
        criteria.setFailOnNoAssignment(true);
        criteria.setReturnUnassignedEmployees(true);
        Mockito.when(personAssignmentHelpMock.getBulkPersonAssignmentList(any(), any(),Mockito.anyBoolean())).thenReturn(getRuleList());
        List<EmployeeAdjustmentRuleAssignmentBean> resList = restEmployeeAdjustmentRuleAssignment.getEmployeeAdjustmentRuleAssignmentsByPersonNumbers(criteria);
        assertEquals(resList.get(0).getEffectiveDate(), getRuleList().get(0).getEffectiveDate());
        assertEquals(resList.get(1).getEffectiveDate(), getRuleList().get(1).getEffectiveDate());
    }

    @Test
    public void whenGivenPersonNumberProcessorEffectiveDate_thenTestEmployeeAdjustmentRuleAssignmentByPersonNumber_expectApiException() throws Exception {
        Mockito.doThrow(APIException.class)
                .when(assignmentService).getEmployeeAdjustmentRules(Mockito.isNull(), anyString(), anyString(), anyString());

        assertThrows(APIException.class, () -> {
            restEmployeeAdjustmentRuleAssignment.getEmployeeAdjustmentRuleAssignmentByPersonNumber("123", "pro", "2024-04-02");
        });
    }

    @Test()
    public void whenGivenAdjustmentRuleMultipleAssignmentBeanList_thenTestMultiUpsertAction_expectApiException() throws Exception {
        doAnswer(invocation -> {
            EmployeeAdjustmentRuleAssignmentWrapper wrapper = (EmployeeAdjustmentRuleAssignmentWrapper)
                    invocation.getArguments()[0];
            wrapper.setApiException(new APIException());
            return invocation;
        }).when(assignmentService).validate(any(EmployeeAdjustmentRuleAssignmentWrapper.class));
        assertThrows(APIException.class, () -> {
            restEmployeeAdjustmentRuleAssignment.multiUpsert(AdjustmentRuleMultipleAssignmentBeanList);
        });
        verify(assignmentService, times(0)).updateAdjustmentHoldRules(anyList());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testAssignEmployeeAdjustmentRuleException() {
        assertThrows(APIException.class, () -> {
            Mockito.when(assignmentService.assignAdjustmentRule(any())).thenThrow(APIException.class);
            EmployeeAdjustmentRuleAssignmentBean requestBean = Mockito.mock(EmployeeAdjustmentRuleAssignmentBean.class);
            restEmployeeAdjustmentRuleAssignment.assignEmployeeAdjustmentRule(requestBean);
        });
    }

    @Test
    public void testUpdateEmployeeAdjustmentRuleAssignment() {
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(assignmentService.updateAdjustmentRuleAssignment(any())).thenReturn(personality);
        EmployeeAdjustmentRuleAssignmentBean requestBean = Mockito.mock(EmployeeAdjustmentRuleAssignmentBean.class);
        EmployeeAdjustmentRuleAssignmentBean responseBean = restEmployeeAdjustmentRuleAssignment
                .updateEmployeeAdjustmentRuleAssignment(requestBean);
        assertNotNull(responseBean);
        assertEquals(responseBean, requestBean);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testUpdateEmployeeAdjustmentRuleAssignmentException() {
        assertThrows(APIException.class, () -> {
            Mockito.when(assignmentService.updateAdjustmentRuleAssignment(any()))
                    .thenThrow(APIException.class);
            EmployeeAdjustmentRuleAssignmentBean requestBean = Mockito.mock(EmployeeAdjustmentRuleAssignmentBean.class);
            restEmployeeAdjustmentRuleAssignment.updateEmployeeAdjustmentRuleAssignment(requestBean);
        });
    }

    @Test
    public void testMultiCreate() {
        kronosPropertiesMock.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        EmployeeAdjustmentRuleAssignmentBean requestBean = Mockito.mock(EmployeeAdjustmentRuleAssignmentBean.class);
        List<EmployeeAdjustmentRuleAssignmentBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        List<EmployeeAdjustmentRuleAssignmentBean> responseBean = restEmployeeAdjustmentRuleAssignment
                .multiCreate(requestDataList);
        assertNotNull(responseBean);
        assertEquals(1, responseBean.size());
    }

    @Test
    public void testMultiCreateInvalidBean() {
        kronosPropertiesMock.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        EmployeeAdjustmentRuleAssignmentBean requestBean = Mockito.mock(EmployeeAdjustmentRuleAssignmentBean.class);
        List<EmployeeAdjustmentRuleAssignmentBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        doAnswer(invocation -> {
            EmployeeAdjustmentRuleAssignmentWrapper wrapper = (EmployeeAdjustmentRuleAssignmentWrapper)
                    invocation.getArguments()[0];
            wrapper.setApiException(new APIException());
            return invocation;
        }).when(assignmentService).validate(any(EmployeeAdjustmentRuleAssignmentWrapper.class));
        try {
            restEmployeeAdjustmentRuleAssignment.multiCreate(requestDataList);
        } catch (APIException apiException) {
            verify(assignmentService, times(0)).assignAdjustmentRules(anyList());
        }
    }

    @Test
    public void testMultiUpdate() {
        kronosPropertiesMock.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        EmployeeAdjustmentRuleAssignmentBean requestBean = Mockito.mock(EmployeeAdjustmentRuleAssignmentBean.class);
        List<EmployeeAdjustmentRuleAssignmentBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(assignmentService.updateAdjustmentRuleAssignment(Mockito.any())).thenReturn(personality);
        List<EmployeeAdjustmentRuleAssignmentBean> responseBean = restEmployeeAdjustmentRuleAssignment
                .multiUpdate(requestDataList);
        assertNotNull(responseBean);
        assertEquals(1, responseBean.size());
    }

    @Test
    public void testMultiUpdateInvalidBean() {
        kronosPropertiesMock.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        EmployeeAdjustmentRuleAssignmentBean requestBean = Mockito.mock(EmployeeAdjustmentRuleAssignmentBean.class);
        List<EmployeeAdjustmentRuleAssignmentBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        doAnswer(invocation -> {
            EmployeeAdjustmentRuleAssignmentWrapper wrapper = (EmployeeAdjustmentRuleAssignmentWrapper)
                    invocation.getArguments()[0];
            wrapper.setApiException(new APIException());
            return invocation;
        }).when(assignmentService).validate(any(EmployeeAdjustmentRuleAssignmentWrapper.class));
        try {
            restEmployeeAdjustmentRuleAssignment.multiUpdate(requestDataList);
        } catch (APIException apiException) {
            verify(assignmentService, times(0)).updateAdjustmentHoldRules(anyList());
        }
    }

    @Test
    public void testMultiDelete() {
        kronosPropertiesMock.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        AdjustmentRuleCriteriaBean requestBean = new AdjustmentRuleCriteriaBean();
        List<AdjustmentRuleCriteriaBean> requestDataList = new ArrayList<>();
        EmployeeAdjustmentRuleAssignmentBean employeeAdjustmentRuleAssignmentBean
                = Mockito.mock(EmployeeAdjustmentRuleAssignmentBean.class);
        requestDataList.add(requestBean);
        AssignmentBeanHolder<EmployeeAdjustmentRuleAssignmentBean> beanHolder =
                new AssignmentBeanHolder<>(employeeAdjustmentRuleAssignmentBean);
        Mockito.when(assignmentService.validateAndReturnHolderForDelete(Mockito.any()))
                .thenReturn(beanHolder);
        doNothing().when(assignmentService).deleteAdjustmentHoldRules(Lists.newArrayList(beanHolder));
        restEmployeeAdjustmentRuleAssignment.multiDelete(requestDataList);
    }

    @Test
    public void testMultiDeleteInvalidBean() {
        kronosPropertiesMock.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        AdjustmentRuleCriteriaBean requestBean = new AdjustmentRuleCriteriaBean();
        List<AdjustmentRuleCriteriaBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        doAnswer(invocation -> {
            RuleCriteriaBeanWrapper wrapper = (RuleCriteriaBeanWrapper) invocation.getArguments()[0];
            wrapper.setApiException(new APIException());
            return new AssignmentBeanHolder<>();
        }).when(assignmentService).validateAndReturnHolderForDelete(any(RuleCriteriaBeanWrapper.class));
        try {
            restEmployeeAdjustmentRuleAssignment.multiDelete(requestDataList);
        } catch (APIException apiException) {
            verify(assignmentService, times(0)).deleteAdjustmentHoldRules(anyList());
        }
    }

    @Test
    public void testDeleteEmployeeAdjustmentRuleAssignmentException() {
        AdjustmentRuleCriteriaBean requestBean = new AdjustmentRuleCriteriaBean();
        restEmployeeAdjustmentRuleAssignment.deleteEmployeeAdjustmentRuleAssignment(requestBean);
        verify(assignmentService, Mockito.only()).deleteAdjustmentRuleAssignment(requestBean);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testDeleteEmployeeAdjustmentRuleAssignment() {
        assertThrows(APIException.class, () -> {
            AdjustmentRuleCriteriaBean requestBean = new AdjustmentRuleCriteriaBean();
            Mockito.when(assignmentService.deleteAdjustmentRuleAssignment(requestBean)).thenThrow(APIException.class);
            restEmployeeAdjustmentRuleAssignment.deleteEmployeeAdjustmentRuleAssignment(requestBean);
        });
    }

    @Test
    public void testGetAllAdjustmentRuleAssignment() {
        Mockito.when(assignmentService.getAllEmployeeAdjustmentRules()).thenReturn(getRuleList());
        List<EmployeeAdjustmentRuleAssignmentBean> list = restEmployeeAdjustmentRuleAssignment
                .getAllAdjustmentRuleAssignment();
        assertNotNull(list);
        assertEquals(2, list.size());
    }

    @Test
    public void testGetEmployeeAdjustmentRuleAssignment() {
        AdjustmentRuleCriteriaBean requestBean = new AdjustmentRuleCriteriaBean();
        PersonIdentityBean identityBean = new PersonIdentityBean();
        Mockito.when(assignmentService.getEmployeeAdjustmentRules(anyLong(), anyString(), anyString(), anyString()))
                .thenReturn(getRuleList());
        List<EmployeeAdjustmentRuleAssignmentBean> list = restEmployeeAdjustmentRuleAssignment
                .getEmployeeAdjustmentRuleAssignment(identityBean.getPersonKey(), requestBean.getProcessor(),
                        requestBean.getEffectiveDate());
        assertNotNull(list);
        assertEquals(0, list.size());
    }

    private List<EmployeeAdjustmentRuleAssignmentBean> getRuleList() {
        List<EmployeeAdjustmentRuleAssignmentBean> list = new ArrayList<>();
        EmployeeAdjustmentRuleAssignmentBean bean1 = new EmployeeAdjustmentRuleAssignmentBean();
        bean1.setEffectiveDate("1111");
        EmployeeAdjustmentRuleAssignmentBean bean2 = new EmployeeAdjustmentRuleAssignmentBean();
        bean2.setEffectiveDate("2222");
        list.add(bean1);
        list.add(bean2);
        return list;
    }
}
