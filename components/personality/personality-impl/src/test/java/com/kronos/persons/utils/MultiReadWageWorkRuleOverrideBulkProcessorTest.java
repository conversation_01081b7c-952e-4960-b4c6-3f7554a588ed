package com.kronos.persons.utils;

import com.google.common.collect.Lists;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.container.api.exception.APIException;
import com.kronos.container.api.util.APIExceptionDetailResult;
import com.kronos.persons.rest.model.EmployeeCriteria;
import com.kronos.persons.rest.model.EmployeeWageWorkRulesDTO;
import com.kronos.persons.rest.model.WageWorkRuleMultiReadWhere;
import com.kronos.persons.rest.model.wageoverride.EmployeeWageWorkRulesWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import static com.kronos.persons.rest.exception.ExceptionConstants.ALL_RECORDS_FAILED;
import static com.kronos.persons.rest.exception.ExceptionConstants.PARTIAL_SUCCESS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Verification for {@link MultiReadWageWorkRuleOverrideBulkProcessor}.
 * Copyright (C) 2020 Kronos.com
 * Date: May 29, 2020
 *
 * <AUTHOR> Kuchynski
 */
@ExtendWith(MockitoExtension.class)
public class MultiReadWageWorkRuleOverrideBulkProcessorTest {
    private static final Long PERSON_ID_1 = 101L;
    private static final Long PERSON_ID_2 = 102L;
    private static final String PERSON_NUMBER_1 = "PERSON_NUMBER_1";
    private static final String PERSON_NUMBER_2 = "PERSON_NUMBER_2";
    private static final String ERROR_CODE = "WCO-XXXXXXX";
    private static final APIException API_EXCEPTION = new APIException(ERROR_CODE);
    private static final APIException API_EXCEPTION2 = new APIException(ERROR_CODE);

    private WageWorkRuleMultiReadWhere where;

    @BeforeEach
    public void setUp() {
        where = new WageWorkRuleMultiReadWhere();
        where.setEmployees(new EmployeeCriteria());
        where.getEmployees().setValues(Lists.newArrayList(PERSON_NUMBER_1, PERSON_NUMBER_2));
    }

    @Test
    public void testProcessSuccess() {
        EmployeeWageWorkRulesDTO dto1 = new EmployeeWageWorkRulesDTO();
        dto1.setEmployee(new ObjectRef(PERSON_ID_1, PERSON_NUMBER_1));
        EmployeeWageWorkRulesDTO dto2 = new EmployeeWageWorkRulesDTO();
        dto2.setEmployee(new ObjectRef(PERSON_ID_2, PERSON_NUMBER_2));
        EmployeeWageWorkRulesWrapper wrapper1 = new EmployeeWageWorkRulesWrapper();
        wrapper1.setDTO(dto1);
        EmployeeWageWorkRulesWrapper wrapper2 = new EmployeeWageWorkRulesWrapper();
        wrapper2.setDTO(dto2);
        Function<WageWorkRuleMultiReadWhere, List<EmployeeWageWorkRulesWrapper>> processor =
                (wageWorkRuleMultiReadWhere -> Lists.newArrayList(wrapper1, wrapper2));
        MultiReadWageWorkRuleOverrideBulkProcessor bulkProcessor =
                new MultiReadWageWorkRuleOverrideBulkProcessor(where, processor);

        List<EmployeeWageWorkRulesDTO> result = bulkProcessor.process();
        assertEquals(2, result.size());
        assertEquals(new ObjectRef(PERSON_ID_1, PERSON_NUMBER_1), result.get(0).getEmployee());
        assertEquals(new ObjectRef(PERSON_ID_2, PERSON_NUMBER_2), result.get(1).getEmployee());
    }

    @Test
    public void testProcessPartialSuccess() {
        testMultiReadPartialSuccess(false);
    }

    @Test
    public void testProcessPartialSuccessMultiKey() {
        testMultiReadPartialSuccess(true);
    }

    private void testMultiReadPartialSuccess(boolean isMultiKey) {
        EmployeeWageWorkRulesDTO dto1 = new EmployeeWageWorkRulesDTO();
        dto1.setEmployee(new ObjectRef(PERSON_ID_1, PERSON_NUMBER_1));
        EmployeeWageWorkRulesWrapper wrapper1 = new EmployeeWageWorkRulesWrapper();
        wrapper1.setDTO(dto1);
        EmployeeWageWorkRulesWrapper wrapper2 = new EmployeeWageWorkRulesWrapper();
        wrapper2.setApiException(API_EXCEPTION);
        Object errorInput = null;
        if (isMultiKey) {
            wrapper2.setMultiKey(true);
            errorInput = Arrays.asList("102L", "-243");
            wrapper2.setErrorInput(errorInput);
        } else {
            wrapper2.setMultiKey(false);
            errorInput = "102L";
            wrapper2.setErrorInput(errorInput);
        }
        Function<WageWorkRuleMultiReadWhere, List<EmployeeWageWorkRulesWrapper>> processor =
                (wageWorkRuleMultiReadWhere -> Lists.newArrayList(wrapper1, wrapper2));
        MultiReadWageWorkRuleOverrideBulkProcessor bulkProcessor =
                new MultiReadWageWorkRuleOverrideBulkProcessor(where, processor);
        List<EmployeeWageWorkRulesDTO> result = null;
        try {
            result = bulkProcessor.process();
        } catch (APIException apiException) {
            assertEquals(PARTIAL_SUCCESS, apiException.getErrorCode());
            assertFalse(apiException.getResults().isEmpty());
            List<APIExceptionDetailResult> results = (List<APIExceptionDetailResult>)apiException.getResults().get("results");
            assertEquals(2, results.size());
            APIExceptionDetailResult<EmployeeWageWorkRulesDTO> success = results.get(0);
            assertNull(success.getError());
            EmployeeWageWorkRulesDTO dto = success.getSuccess();
            assertNotNull(dto);
            assertEquals(PERSON_ID_1, dto.getEmployee().getId());
            assertEquals(PERSON_NUMBER_1, dto.getEmployee().getQualifier());
            APIExceptionDetailResult<EmployeeWageWorkRulesDTO> error = results.get(1);
            assertNotNull(error);
            assertNull(error.getSuccess());
            APIException exp = error.getError();
            WageWorkRuleMultiReadWhere inputDetail = (WageWorkRuleMultiReadWhere) exp.getInputDetail().get("input");
            if (isMultiKey) {
                assertEquals(errorInput, inputDetail.getEmployees().getMultiKeyValues().get(0));
            } else {
                assertEquals(errorInput, inputDetail.getEmployees().getValues().get(0));
            }
        }
        assertNull(result);
    }

    @Test
    public void testProcessAllError() {
        EmployeeWageWorkRulesWrapper wrapper1 = new EmployeeWageWorkRulesWrapper();
        wrapper1.setApiException(API_EXCEPTION);
        wrapper1.setMultiKey(true);
        Object errorInput1 = Arrays.asList("243L", "-243");
        wrapper1.setErrorInput(errorInput1);
        EmployeeWageWorkRulesWrapper wrapper2 = new EmployeeWageWorkRulesWrapper();
        wrapper2.setMultiKey(true);
        wrapper2.setApiException(API_EXCEPTION2);
        Object errorInput2 = Arrays.asList("253", "-253L");
        wrapper2.setErrorInput(errorInput2);
        Function<WageWorkRuleMultiReadWhere, List<EmployeeWageWorkRulesWrapper>> processor =
                (wageWorkRuleMultiReadWhere -> Lists.newArrayList(wrapper1, wrapper2));
        MultiReadWageWorkRuleOverrideBulkProcessor bulkProcessor =
                new MultiReadWageWorkRuleOverrideBulkProcessor(where, processor);
        List<EmployeeWageWorkRulesDTO> result = null;
        try {
            result = bulkProcessor.process();
        } catch (APIException apiException) {
            assertEquals(ALL_RECORDS_FAILED, apiException.getErrorCode());
            assertFalse(apiException.getResults().isEmpty());
            List<APIExceptionDetailResult> results = (List<APIExceptionDetailResult>)apiException.getResults().get("results");
            assertEquals(2, results.size());
            APIExceptionDetailResult<EmployeeWageWorkRulesDTO> error1 = results.get(0);
            assertNotNull(error1);
            assertNull(error1.getSuccess());
            assertNotNull(error1.getError());
            APIException exp = error1.getError();
            WageWorkRuleMultiReadWhere inputDetail = (WageWorkRuleMultiReadWhere) exp.getInputDetail().get("input");
            assertEquals(errorInput1, inputDetail.getEmployees().getMultiKeyValues().get(0));

            APIExceptionDetailResult<EmployeeWageWorkRulesDTO> error2 = results.get(1);
            assertNotNull(error2);
            assertNull(error2.getSuccess());
            assertNotNull(error2.getError());
            exp = error2.getError();
            inputDetail = (WageWorkRuleMultiReadWhere) exp.getInputDetail().get("input");
            assertEquals(errorInput2, inputDetail.getEmployees().getMultiKeyValues().get(0));
        }
        assertNull(result);
    }

    @Test
    public void testGetResponseSizeNew() {
        MultiReadWageWorkRuleOverrideBulkProcessor bulkProcessor = new MultiReadWageWorkRuleOverrideBulkProcessor(where, null);
        List<EmployeeWageWorkRulesWrapper> responseList = new ArrayList<>();
        EmployeeWageWorkRulesWrapper wrapper = new EmployeeWageWorkRulesWrapper();
        responseList.add(wrapper);
        wrapper = new EmployeeWageWorkRulesWrapper();
        responseList.add(wrapper);
        assertEquals(2,bulkProcessor.getResponseSize(responseList));
        responseList.remove(1);
        assertEquals(1, bulkProcessor.getResponseSize(responseList));
    }
}
