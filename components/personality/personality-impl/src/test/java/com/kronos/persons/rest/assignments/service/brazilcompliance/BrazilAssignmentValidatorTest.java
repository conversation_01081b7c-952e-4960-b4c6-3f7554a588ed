package com.kronos.persons.rest.assignments.service.brazilcompliance;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentCompanyDetailRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentCompanyRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentPcaDetailRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentPcaRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentRepTypeDetailRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentRepTypeRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilEmployeeAssignmentsRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.CompanyAttributesRest;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.timekeeping.brazil.compliance.setup.api.model.CompanyDTO;
import com.kronos.timekeeping.brazil.compliance.setup.api.model.PaycodeAttributeDefinitionDTO;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BrazilRepTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentUpdateHelper.ALL_COMPANIES_ID_MAP;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentUpdateHelper.ALL_COMPANIES_QUAL_MAP;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentUpdateHelper.ALL_PAYCODE_ATTRIBUTES_ID_MAP;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentUpdateHelper.ALL_PAYCODE_ATTRIBUTES_QUAL_MAP;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentUpdateHelper.ALL_REP_TYPE_ATTRIBUTES_QUALIFIER_MAP;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentUpdateHelper.ALL_REP_TYPE_ID_MAP;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentValidator.BATCH_SIZE_MULTI_READ_PROP;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentValidator.BATCH_SIZE_MULTI_UPSERT_PROP;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentValidator.DEFAULT_RECORD_BATCH_SIZE_MULTI_READ;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentValidator.DEFAULT_RECORD_BATCH_SIZE_MULTI_UPSERT;
import static com.kronos.persons.rest.exception.ExceptionConstants.EMPTY_MULTI_UPDATE_REQUEST;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class BrazilAssignmentValidatorTest {

    private static final Long PERSON_ID = 243L;
    private static final String PERSON_NUMBER = "20335";
    private static final Long COMPANY_ID = 33333L;
    private static final String COMPANY_QUALIFIER = "COMPANY-33333";
    private static final String LOCATION = "LOCATION";
    private static final String CEI = "CEI";
    private static final String CAEPF = "CAEPF";
    private static final String CNO = "CNO";
    private static final String DEVICE_GROUP_1 = "DEVICE_GROUP_1";
    private static final String PIS = "PIS";
    private static final String ESOCIAL = "eSocial";
    private static final String CPF = "cpf";
    private static final Long PCA_ID = 44444L;
    private static final String PCA_QUALIFIER = "PCA-44444";
    private static final Long REP_TYPE_ID = BrazilRepTypeEnum.REP_TYPE_A.getId();
    private static final String REP_TYPE_QUALIFIER = BrazilRepTypeEnum.REP_TYPE_A.getQualifier();
    private final static String UNION_AGREEMENT_NUMBER = "UAG";
    private final LocalDateTime EFFECTIVE_DATE = LocalDateTime.now().minusYears(10);

    @InjectMocks
    BrazilAssignmentValidator brazilAssignmentValidator;
    @Mock
    private IKProperties ikProperties;

    @Mock
    PersonIdentityBeanValidator personIdentityBeanValidator;

    @Test
    public void testValidateRequestIsNotNull() {
        APIException thrown = assertThrows(
                APIException.class,
                () -> brazilAssignmentValidator.validateRequestIsNotNull(null), "Expected to throw APIException");
        assertEquals(EMPTY_MULTI_UPDATE_REQUEST, thrown.getErrorCode());

        thrown = assertThrows(
                APIException.class,
                () -> brazilAssignmentValidator.validateRequestIsNotNull(new ArrayList<>()), "Expected to throw APIException");
        assertEquals(EMPTY_MULTI_UPDATE_REQUEST, thrown.getErrorCode());

    }

    @Test
    public void testValidateSingleEmployeeUpdate() {
        /** Null request */
        APIException exception = brazilAssignmentValidator.validate(null, null, null, null, new HashMap<>(), new HashMap<>());
        assertEquals(ExceptionConstants.PROPERTY_IS_NULL, exception.getErrorCode());

        /** Request with null PersonIdentity */
        BrazilEmployeeAssignmentsRest request = createBrazilEmployeeAssignmentsRest();
        request.setPersonIdentity(null);
        exception = brazilAssignmentValidator.validate(request, null, null, null, new HashMap<>(), new HashMap<>());
        assertEquals(ExceptionConstants.PROPERTY_IS_NULL, exception.getErrorCode());
    }

    @Test
    public void testValidatePersonIdentity(){
        /** Null identity */
        APIException exception = brazilAssignmentValidator.validatePersonIdentity(null, null,  null, new HashMap<>(), new HashMap<>());
        assertEquals(ExceptionConstants.PROPERTY_IS_NULL, exception.getErrorCode());

        /**   Person id  is not valid */
        PersonIdentityBean identity = new PersonIdentityBean();
        identity.setPersonKey(PERSON_ID);
        exception = brazilAssignmentValidator.validatePersonIdentity(identity, new HashMap<>(), new HashMap<>(), new HashMap<>(), new HashMap<>());
        assertEquals(ExceptionConstants.PERSON_NOT_FOUND_HTTP_404, exception.getErrorCode());

        /** Person number  is not valid */
        identity = new PersonIdentityBean();
        identity.setPersonNumber(PERSON_NUMBER);
        exception = brazilAssignmentValidator.validatePersonIdentity(identity, new HashMap<>(), new HashMap<>(), new HashMap<>(), new HashMap<>());
        assertEquals(ExceptionConstants.PERSON_NOT_FOUND_HTTP_404, exception.getErrorCode());


        /** Person id is valid but not accessible */

        Mockito.when(personIdentityBeanValidator.validatePersonId(any(), any(), any())).thenReturn(new APIException(ExceptionConstants.NO_ACCESS_TO_PERSON));
        identity = new PersonIdentityBean();
        identity.setPersonKey(PERSON_ID);
        Map<Long, EmployeeExtension> personIdMap = new HashMap<>();
        EmployeeExtension ext = new EmployeeExtension();
        ext.setPersonId(PERSON_ID);
        ext.setPersonNumber(PERSON_NUMBER);
        personIdMap.put(PERSON_ID, ext);
        exception = brazilAssignmentValidator.validatePersonIdentity(identity, personIdMap, new HashMap<>(), new HashMap<>(), new HashMap<>());
        assertEquals(ExceptionConstants.NO_ACCESS_TO_PERSON, exception.getErrorCode());


        /** Person number is valid but not accessible */
        Mockito.when(personIdentityBeanValidator.validatePersonNumber(any(), any(), any())).thenReturn(new APIException(ExceptionConstants.NO_ACCESS_TO_PERSON));
        identity = new PersonIdentityBean();
        identity.setPersonNumber(PERSON_NUMBER);
        Map<String, EmployeeExtension> personNumberMap = new HashMap<>();
        ext = new EmployeeExtension();
        ext.setPersonId(PERSON_ID);
        ext.setPersonNumber(PERSON_NUMBER);
        personNumberMap.put(PERSON_NUMBER, ext);

        exception = brazilAssignmentValidator.validatePersonIdentity(identity, new HashMap<>(), personNumberMap, new HashMap<>(), new HashMap<>());
        assertEquals(ExceptionConstants.NO_ACCESS_TO_PERSON, exception.getErrorCode());
    }

    @Test
    public void testValidateCompanyAssignments(){
        /** No Company assignment - No exception*/
        Collection<CompanyDTO> allCompanies = Arrays.asList(getCompanyDTO());
        Map<Object, Object> companyMapWithIdAsKey = allCompanies.stream().collect(Collectors.toMap(CompanyDTO::getCompanyId, CompanyDTO::getCompanyName));
        Map<Object, Object> companyMapWithQualifierAsKey = allCompanies.stream().collect(Collectors.toMap(CompanyDTO::getCompanyName, CompanyDTO::getCompanyId));
        Map<String, Map<Object, Object>> attributeMap = new HashMap<>();
        attributeMap.put(ALL_COMPANIES_ID_MAP, companyMapWithIdAsKey);
        attributeMap.put(ALL_COMPANIES_QUAL_MAP, companyMapWithQualifierAsKey);

        BrazilEmployeeAssignmentsRest request = createBrazilEmployeeAssignmentsRest();
        CompanyAttributesRest companyAttributesRest = request.getCompanyAssignments().getAssignmentDetails().get(0).getCompany();
        /** Valid Company id assignment - No Exception */
        APIException exception = brazilAssignmentValidator.validateCompanyAssignments(request, attributeMap);
        assertNull(exception);

        /** Valid Company assignment with unAssignExisting true - Exception */
        request.getCompanyAssignments().setUnAssignExisting(true);
        exception = brazilAssignmentValidator.validateCompanyAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.INVALID_PROPERTY_COMBINATION, exception.getErrorCode());
        request.getCompanyAssignments().setUnAssignExisting(false);

        /** Non-existent company id in the request - Exception */
        companyAttributesRest.setId(1111L);
        exception = brazilAssignmentValidator.validateCompanyAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());
        companyAttributesRest.setId(COMPANY_ID);

        /** Non existent company qualifier in the request - Exception */
        companyAttributesRest.setId(null);
        companyAttributesRest.setQualifier("NON-EXISTENT");
        exception = brazilAssignmentValidator.validateCompanyAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());
        companyAttributesRest.setId(COMPANY_ID);
        companyAttributesRest.setQualifier(COMPANY_QUALIFIER);

        /** No effective date - Exception */
        request.getCompanyAssignments().getAssignmentDetails().get(0).setEffectiveDate(null);
        exception = brazilAssignmentValidator.validateCompanyAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.PROPERTY_IS_NULL, exception.getErrorCode());
        companyAttributesRest.setId(COMPANY_ID);
        request.getCompanyAssignments().getAssignmentDetails().get(0).setEffectiveDate(EFFECTIVE_DATE.toLocalDate());

        /** No company id / qualifier - Exception */
        companyAttributesRest.setId(null);
        companyAttributesRest.setQualifier(null);
        exception = brazilAssignmentValidator.validateCompanyAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.PROPERTY_IS_NULL, exception.getErrorCode());
        companyAttributesRest.setId(COMPANY_ID);
        companyAttributesRest.setQualifier(COMPANY_QUALIFIER);
    }

    @Test
    public void testValidatePayCodeAttributeAssignments(){
        Collection<PaycodeAttributeDefinitionDTO> allPca = Collections.singletonList(getPcaDTO());
        Map<Object, Object> pcaMapWithIdAsKey = allPca.stream().collect(Collectors.toMap(PaycodeAttributeDefinitionDTO::getPaycodeAttributeDefId, PaycodeAttributeDefinitionDTO::getPaycodeAttributeDefName));
        Map<Object, Object> pcaMapWithQualifierAsKey = allPca.stream().collect(Collectors.toMap(PaycodeAttributeDefinitionDTO::getPaycodeAttributeDefName, PaycodeAttributeDefinitionDTO::getPaycodeAttributeDefId));
        Map<String, Map<Object, Object>> attributeMap = new HashMap<>();
        attributeMap.put(ALL_PAYCODE_ATTRIBUTES_ID_MAP, pcaMapWithIdAsKey);
        attributeMap.put(ALL_PAYCODE_ATTRIBUTES_QUAL_MAP, pcaMapWithQualifierAsKey);
        BrazilEmployeeAssignmentsRest request = createBrazilEmployeeAssignmentsRest();
        BrazilAssignmentPcaDetailRest brazilAssignmentPcaDetailRest = request.getPcaAssignments().getAssignmentDetails().get(0);

        // Valid id assignment - No Exception
        APIException exception = brazilAssignmentValidator.validatePayCodeAttributeAssignments(request, attributeMap);
        assertNull(exception);

        // Valid Pca assignment with unAssignExisting true - Exception
        request.getPcaAssignments().setUnAssignExisting(true);
        exception = brazilAssignmentValidator.validatePayCodeAttributeAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.INVALID_PROPERTY_COMBINATION, exception.getErrorCode());
        request.getPcaAssignments().setUnAssignExisting(false);

        // Non-existent id in the request - Exception
        brazilAssignmentPcaDetailRest.setPayCodeAttribute(new ObjectRef(99999L));
        exception = brazilAssignmentValidator.validatePayCodeAttributeAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());

        // Non-existent qualifier in the request - Exception
        brazilAssignmentPcaDetailRest.setPayCodeAttribute(new ObjectRef("NON_EXISTENT"));
        exception = brazilAssignmentValidator.validatePayCodeAttributeAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());

        // No effective date - Exception
        brazilAssignmentPcaDetailRest.setPayCodeAttribute(new ObjectRef(PCA_ID));
        request.getPcaAssignments().getAssignmentDetails().get(0).setEffectiveDate(null);
        exception = brazilAssignmentValidator.validatePayCodeAttributeAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.PROPERTY_IS_NULL, exception.getErrorCode());
    }

    @Test
    public void testValidateRepTypeAssignments(){
        BrazilEmployeeAssignmentsRest request = createBrazilEmployeeAssignmentsRest();
        BrazilAssignmentRepTypeDetailRest brazilAssignmentRepTypeDetailRest = request.getRepTypeAssignments().getAssignmentDetails().get(0);
        Map<String, Map<Object, Object>> attributeMap = new HashMap<>();
        attributeMap.put(ALL_REP_TYPE_ID_MAP, Arrays.stream(BrazilRepTypeEnum.values()).collect(Collectors.toMap(BrazilRepTypeEnum::getId, BrazilRepTypeEnum::getQualifier)));
        attributeMap.put(ALL_REP_TYPE_ATTRIBUTES_QUALIFIER_MAP, Arrays.stream(BrazilRepTypeEnum.values()).collect(Collectors.toMap(BrazilRepTypeEnum::getQualifier, BrazilRepTypeEnum::getId)));

        // Valid id assignment - No Exception
        APIException exception = brazilAssignmentValidator.validateRepTypeAssignments(request, attributeMap);
        assertNull(exception);

        // Valid Pca assignment with unAssignExisting true - Exception
        request.getRepTypeAssignments().setUnAssignExisting(true);
        exception = brazilAssignmentValidator.validateRepTypeAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.INVALID_PROPERTY_COMBINATION, exception.getErrorCode());

        // Non-existent id in the request - Exception
        brazilAssignmentRepTypeDetailRest.setRepType(new ObjectRef(99999L));
        request.getRepTypeAssignments().setUnAssignExisting(false);
        exception = brazilAssignmentValidator.validateRepTypeAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());

        // Non-existent qualifier in the request - Exception
        brazilAssignmentRepTypeDetailRest.setRepType(new ObjectRef("NON_EXISTENT"));
        exception = brazilAssignmentValidator.validateRepTypeAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());

        // Non-existent qualifier in the request - Exception
        brazilAssignmentRepTypeDetailRest.setRepType(new ObjectRef("NON_EXISTENT"));
        exception = brazilAssignmentValidator.validateRepTypeAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());

        // No union agreement number for REP-A id - Exception
        brazilAssignmentRepTypeDetailRest.setRepType(new ObjectRef(REP_TYPE_ID));
        request.getRepTypeAssignments().getAssignmentDetails().get(0).setUnionAgreementNumber(null);
        exception = brazilAssignmentValidator.validateRepTypeAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.PROPERTY_IS_NULL, exception.getErrorCode());

        // No union agreement number for REP-A qualifier - Exception
        brazilAssignmentRepTypeDetailRest.setRepType(new ObjectRef(REP_TYPE_QUALIFIER));
        exception = brazilAssignmentValidator.validateRepTypeAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.PROPERTY_IS_NULL, exception.getErrorCode());

        // Empty union agreement number for REP-A - Exception
        brazilAssignmentRepTypeDetailRest.setRepType(new ObjectRef(REP_TYPE_ID, REP_TYPE_QUALIFIER));
        request.getRepTypeAssignments().getAssignmentDetails().get(0).setUnionAgreementNumber("");
        exception = brazilAssignmentValidator.validateRepTypeAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.PROPERTY_IS_NULL, exception.getErrorCode());

        // Union agreement number present for non REP-A - Exception
        brazilAssignmentRepTypeDetailRest.setRepType(new ObjectRef(BrazilRepTypeEnum.REP_TYPE_C.getQualifier()));
        request.getRepTypeAssignments().getAssignmentDetails().get(0).setUnionAgreementNumber(UNION_AGREEMENT_NUMBER);
        exception = brazilAssignmentValidator.validateRepTypeAssignments(request, attributeMap);
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());

        // No union agreement number for non REP-A - No exception
        brazilAssignmentRepTypeDetailRest.setRepType(new ObjectRef(BrazilRepTypeEnum.REP_TYPE_P.getQualifier()));
        request.getRepTypeAssignments().getAssignmentDetails().get(0).setUnionAgreementNumber(null);
        exception = brazilAssignmentValidator.validateRepTypeAssignments(request, attributeMap);
        assertNull(exception);

        // Empty union agreement number for non REP-A - No exception
        brazilAssignmentRepTypeDetailRest.setRepType(new ObjectRef(BrazilRepTypeEnum.REP_TYPE_P.getId()));
        request.getRepTypeAssignments().getAssignmentDetails().get(0).setUnionAgreementNumber("");
        exception = brazilAssignmentValidator.validateRepTypeAssignments(request, attributeMap);
        assertNull(exception);
    }

    @Test
    public void testServiceLimitMultiRead() {
        Mockito.when(ikProperties.getProperty(BATCH_SIZE_MULTI_READ_PROP, DEFAULT_RECORD_BATCH_SIZE_MULTI_READ)).thenReturn("2");

        // Number of employees more than the service limit
        APIException thrown = assertThrows(
                APIException.class,
                () -> brazilAssignmentValidator.checkServiceLimitForMultiRead(Arrays.asList("Employee1", "Employee2", "Employee3")), "Expected to throw APIException");
        assertEquals("WCO-101207", thrown.getErrorCode());
        verify(ikProperties, times(1)).getProperty(BATCH_SIZE_MULTI_READ_PROP, DEFAULT_RECORD_BATCH_SIZE_MULTI_READ);

        // Number of employees same as the service limit
        brazilAssignmentValidator.checkServiceLimitForMultiRead(Arrays.asList("Employee1", "Employee2"));
        verify(ikProperties, times(2)).getProperty(BATCH_SIZE_MULTI_READ_PROP, DEFAULT_RECORD_BATCH_SIZE_MULTI_READ);

        // Number of employees less than the service limit
        brazilAssignmentValidator.checkServiceLimitForMultiRead(Arrays.asList("Employee1"));
        verify(ikProperties, times(3)).getProperty(BATCH_SIZE_MULTI_READ_PROP, DEFAULT_RECORD_BATCH_SIZE_MULTI_READ);
    }


    @Test
    public void testServiceLimitMultiUpsert() {
        Mockito.when(ikProperties.getProperty(BATCH_SIZE_MULTI_UPSERT_PROP, DEFAULT_RECORD_BATCH_SIZE_MULTI_UPSERT)).thenReturn("2");

        // Number of employees more than the service limit
        APIException thrown = assertThrows(
                APIException.class,
                () -> brazilAssignmentValidator.checkServiceLimitForMultiUpsert(Arrays.asList(new PersonIdentityBean(), new PersonIdentityBean(), new PersonIdentityBean())), "Expected to throw APIException");
        assertEquals("WCO-101207", thrown.getErrorCode());
        verify(ikProperties, times(1)).getProperty(BATCH_SIZE_MULTI_UPSERT_PROP, DEFAULT_RECORD_BATCH_SIZE_MULTI_UPSERT);

        // Number of employees same as the service limit
        brazilAssignmentValidator.checkServiceLimitForMultiUpsert(Arrays.asList(new PersonIdentityBean(), new PersonIdentityBean()));
        verify(ikProperties, times(2)).getProperty(BATCH_SIZE_MULTI_UPSERT_PROP, DEFAULT_RECORD_BATCH_SIZE_MULTI_UPSERT);

        // Number of employees less than the service limit
        brazilAssignmentValidator.checkServiceLimitForMultiUpsert(Arrays.asList(new PersonIdentityBean()));
        verify(ikProperties, times(3)).getProperty(BATCH_SIZE_MULTI_UPSERT_PROP, DEFAULT_RECORD_BATCH_SIZE_MULTI_UPSERT);
    }

    private CompanyDTO getCompanyDTO() {
        CompanyDTO company = new CompanyDTO();
        company.setCompanyId(COMPANY_ID);
        company.setCompanyName(COMPANY_QUALIFIER);
        company.setCaepf(CAEPF);
        company.setCno(CNO);
        company.setCei(CEI);
        company.setDeviceGroup(new ObjectRef(1L, DEVICE_GROUP_1));
        company.setLocation(LOCATION);
        company.setEmployerIdentifier(COMPANY_QUALIFIER);
        return company;
    }

    private PaycodeAttributeDefinitionDTO getPcaDTO() {
        PaycodeAttributeDefinitionDTO pca = new PaycodeAttributeDefinitionDTO();
        pca.setPaycodeAttributeDefId(PCA_ID);
        pca.setPaycodeAttributeDefName(PCA_QUALIFIER);
        return pca;
    }

    /**
     * Create Mock BO
     */
    BrazilEmployeeAssignmentsRest createBrazilEmployeeAssignmentsRest(){
        PersonIdentityBean identity = new PersonIdentityBean();
        identity.setPersonNumber(PERSON_NUMBER);
        identity.setPersonKey(PERSON_ID);
        BrazilEmployeeAssignmentsRest bo = new BrazilEmployeeAssignmentsRest();
        bo.setPersonIdentity(identity);
        bo.setCpf(CPF);
        bo.setEsocial(ESOCIAL);
        bo.setPis(PIS);

        // Populate Company Data
        List<BrazilAssignmentCompanyDetailRest> companyAssignmentDetails = new ArrayList<>();
        BrazilAssignmentCompanyDetailRest company = new BrazilAssignmentCompanyDetailRest();
        CompanyAttributesRest companyAttributes = new CompanyAttributesRest();
        companyAttributes.setId(COMPANY_ID);
        companyAttributes.setQualifier(COMPANY_QUALIFIER);
        company.setCompany(companyAttributes);
        company.setEffectiveDate(EFFECTIVE_DATE.toLocalDate());
        companyAssignmentDetails.add(company);
        BrazilAssignmentCompanyRest companyAssignments= new BrazilAssignmentCompanyRest();
        companyAssignments.setAssignmentDetails(companyAssignmentDetails);
        bo.setCompanyAssignments(companyAssignments);

        // Populate Pca
        List<BrazilAssignmentPcaDetailRest> assignmentDetails = new ArrayList<>();
        BrazilAssignmentPcaDetailRest pca = new BrazilAssignmentPcaDetailRest();
        pca.setPayCodeAttribute(new ObjectRef(PCA_ID, PCA_QUALIFIER));
        pca.setEffectiveDate(EFFECTIVE_DATE.toLocalDate());
        assignmentDetails.add(pca);
        BrazilAssignmentPcaRest pcaAssignments = new BrazilAssignmentPcaRest();
        pcaAssignments.setAssignmentDetails(assignmentDetails);
        bo.setPcaAssignments(pcaAssignments);

        // Populate RepType
        List<BrazilAssignmentRepTypeDetailRest> repTypeAssignmentDetails = new ArrayList<>();
        BrazilAssignmentRepTypeDetailRest repType = new BrazilAssignmentRepTypeDetailRest();
        repType.setRepType(new ObjectRef(REP_TYPE_ID, REP_TYPE_QUALIFIER));
        repType.setEffectiveDate(EFFECTIVE_DATE.toLocalDate());
        repType.setUnionAgreementNumber(UNION_AGREEMENT_NUMBER);
        repTypeAssignmentDetails.add(repType);
        BrazilAssignmentRepTypeRest repTypeAssignments = new BrazilAssignmentRepTypeRest();
        repTypeAssignments.setAssignmentDetails(repTypeAssignmentDetails);
        bo.setRepTypeAssignments(repTypeAssignments);
        return bo;
    }
}
