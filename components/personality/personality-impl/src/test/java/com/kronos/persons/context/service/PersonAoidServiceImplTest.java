/*******************************************************************************
 * PersonAoidServiceImplTest.java
 * Copyright © 2024 UKG Inc. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.context.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.dataaccess.entity.EmployeeDTO;
import com.kronos.people.personality.dataaccess.service.IPersonReadService;
import com.kronos.persons.context.utils.PeopleConverter;
import com.kronos.persons.rest.beans.LightPersonInformation;
import com.kronos.persons.rest.beans.LightPersonInformationBean;
import com.kronos.persons.rest.beans.Record;
import com.kronos.persons.rest.model.DateRange;
import com.kronos.persons.rest.model.LightPersonInformationSearchCriteria;
import com.kronos.persons.rest.model.PersonWhereCriteria;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * Class with unit tests for {@link PersonAoidServiceImpl}
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonAoidServiceImplTest {

	private static final String DEFAULT_COUNT_SIZE = "20";
	private static final String DEFAULT_TIME_SPAN = "14";
	private static final String GENERIC_EXCEPTION_ERROR_CODE = "WCO-101336";
	private static final String MAX_COUNT_SIZE_KEY = "com.kronos.person.rest.service.default.max.count.size";
	private static final String MAX_TIME_SPAN_KEY = "com.kronos.person.rest.service.without.aoid.max.time.span";

	@InjectMocks
	private PersonAoidServiceImpl service;

	@Mock
	private IPersonReadService readService;

	@Mock
	private IKProperties properties;

	@Mock
	private PeopleConverter converter;

	@Mock
	private LightPersonInformationSearchCriteria lightPersonInformationSearchCriteria;

	@Mock
	private PersonWhereCriteria personWhereCriteria;

	@Test
	public void testGetPersonWithoutAoid() {
		LightPersonInformationSearchCriteria searchCriteria = new LightPersonInformationSearchCriteria();
		searchCriteria.setCount(0L);
		searchCriteria.setIndex(0L);
		PersonWhereCriteria where = new PersonWhereCriteria();
		DateRange dateRange = new DateRange();
		LocalDateTime rightNow = LocalDateTime.now();

		dateRange.setEndDateTime(rightNow);
		dateRange.setStartDateTime(rightNow.minusDays(14));
		where.setDateRange(dateRange);
		where.setMissingExternalIdType("aoid");
		searchCriteria.setWhere(where);
		Mockito.when(properties.getProperty(Mockito.any(), Mockito.any())).thenReturn("14");
		Mockito.when(converter.convertRestToEntityBeans(Mockito.any())).thenReturn(getBeanDetails());

		Mockito.when(readService.findWithoutaoid(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(getEmployeeDTOList());
		service.getPersonWithoutAoid(searchCriteria);
	}
	
	@Test
	public void testGetPersonWithoutAoidWithMoreCount() {
		assertThrows(APIException.class, () -> {
			LightPersonInformationSearchCriteria searchCriteria = new LightPersonInformationSearchCriteria();
			searchCriteria.setCount(21L);
			searchCriteria.setIndex(0L);
			PersonWhereCriteria where = new PersonWhereCriteria();
			DateRange dateRange = new DateRange();
			LocalDateTime rightNow = LocalDateTime.now();

			dateRange.setEndDateTime(rightNow);
			dateRange.setStartDateTime(rightNow.minusDays(14));
			where.setDateRange(dateRange);
			where.setMissingExternalIdType("aoid");
			searchCriteria.setWhere(where);
			Mockito.when(properties.getProperty(Mockito.any(), Mockito.any())).thenReturn("14");
			Mockito.when(converter.convertRestToEntityBeans(Mockito.any())).thenReturn(getBeanDetails());

			Mockito.when(readService.findWithoutaoid(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
					.thenReturn(getEmployeeDTOList());
			service.getPersonWithoutAoid(searchCriteria);
		});
	}
	
	private LightPersonInformation getBeanDetails() {
		LightPersonInformation bean = new LightPersonInformation();
		bean.setTotalElements("10");
		Record record = new Record();
		LightPersonInformationBean lightPersonInformationBean = new LightPersonInformationBean();
		lightPersonInformationBean.setBirthDate("12-02-2019");
		lightPersonInformationBean.setEmployeeID("109998");
		lightPersonInformationBean.setFirstName("Test First Name");
		lightPersonInformationBean.setLastName("Test Last Name");
		lightPersonInformationBean.setUpdatedTime("fsv");
		record.setLightPersonInformation(lightPersonInformationBean);
		List<Record> records = new ArrayList<>();
		records.add(record);
		bean.setRecords(records);
		return bean;
	}

	private List<EmployeeDTO> getEmployeeDTOList() {
		List<EmployeeDTO> list = new ArrayList<>();
		EmployeeDTO dto = new EmployeeDTO();
		dto.setPersonid(111L);
		dto.setBirthDate("12-02-2019");
		dto.setFirstName("Test Name");
		dto.setLastName("TestLastName");
		list.add(dto);
		return list;
	}

	@Test
	public void testGetPersonWithoutAoidPageSizeGreaterThanOne() {
		LightPersonInformationSearchCriteria searchCriteria = new LightPersonInformationSearchCriteria();
		searchCriteria.setCount(10L);
		searchCriteria.setIndex(2L);
		PersonWhereCriteria where = new PersonWhereCriteria();
		DateRange dateRange = new DateRange();
		where.setDateRange(dateRange);
		where.setMissingExternalIdType("aoid");
		searchCriteria.setWhere(where);
		Mockito.when(properties.getProperty(Mockito.any(), Mockito.any())).thenReturn("14");
		Mockito.when(converter.convertRestToEntityBeans(Mockito.any())).thenReturn(getBeanDetails());
		Mockito.when(readService.findWithoutaoid(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
				.thenReturn(getEmployeeDTOList());
		service.getPersonWithoutAoid(searchCriteria);
	}

	@Test
	public void testGetPersonWithoutAoidStartDateNull() {
		assertThrows(APIException.class, () -> {
			LightPersonInformationSearchCriteria searchCriteria = new LightPersonInformationSearchCriteria();
			searchCriteria.setCount(10L);
			searchCriteria.setIndex(0L);
			PersonWhereCriteria where = new PersonWhereCriteria();
			DateRange dateRange = new DateRange();
			LocalDateTime rightNow = LocalDateTime.now();
			dateRange.setStartDateTime(null);
			dateRange.setEndDateTime(rightNow.minusDays(14));
			where.setDateRange(dateRange);
			where.setMissingExternalIdType("aoid");
			searchCriteria.setWhere(where);
			Mockito.when(properties.getProperty(Mockito.any(), Mockito.any())).thenReturn("14");
			Mockito.when(converter.convertRestToEntityBeans(Mockito.any())).thenReturn(getBeanDetails());
			Mockito.when(readService.findWithoutaoid(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
					.thenReturn(getEmployeeDTOList());
			service.getPersonWithoutAoid(searchCriteria);
		});
	}

	@Test
	public void testGetPersonWithoutAoidEndDateNull() {
		assertThrows(APIException.class, () -> {
			LightPersonInformationSearchCriteria searchCriteria = new LightPersonInformationSearchCriteria();
			searchCriteria.setCount(10L);
			searchCriteria.setIndex(0L);
			PersonWhereCriteria where = new PersonWhereCriteria();
			DateRange dateRange = new DateRange();
			LocalDateTime rightNow = LocalDateTime.now();
			dateRange.setStartDateTime(rightNow);
			dateRange.setEndDateTime(null);
			where.setDateRange(dateRange);
			where.setMissingExternalIdType("aoid");
			searchCriteria.setWhere(where);
			Mockito.when(properties.getProperty(Mockito.any(), Mockito.any())).thenReturn("14");
			Mockito.when(converter.convertRestToEntityBeans(Mockito.any())).thenReturn(getBeanDetails());
			Mockito.when(readService.findWithoutaoid(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
					.thenReturn(getEmployeeDTOList());
			service.getPersonWithoutAoid(searchCriteria);
		});
	}

	@Test
	public void testGetPersonWithoutAoidStartDateAfterEndDate() {
		assertThrows(APIException.class, () -> {
			LightPersonInformationSearchCriteria searchCriteria = new LightPersonInformationSearchCriteria();
			searchCriteria.setCount(10L);
			searchCriteria.setIndex(0L);
			PersonWhereCriteria where = new PersonWhereCriteria();
			DateRange dateRange = new DateRange();
			LocalDateTime rightNow = LocalDateTime.now();
			dateRange.setStartDateTime(rightNow);
			dateRange.setEndDateTime(rightNow.minusDays(14));
			where.setDateRange(dateRange);
			where.setMissingExternalIdType("aoid");
			searchCriteria.setWhere(where);
			Mockito.when(properties.getProperty(Mockito.any(), Mockito.any())).thenReturn("14");
			Mockito.when(converter.convertRestToEntityBeans(Mockito.any())).thenReturn(getBeanDetails());
			APIException ex = new APIException();
			Mockito.when(readService.findWithoutaoid(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
					.thenThrow(ex);
			service.getPersonWithoutAoid(searchCriteria);
			assertEquals("WCO-101332",ex.getErrorCode());
		});
	}

	@Test
	public void testGivenRangeBetweenDatesExceedsMaxTimeSpan_WhenGetPersonWithoutAoid_ThenThrowsAPIException() {
		DateRange dateRange = new DateRange();
		LocalDateTime localDateTime = LocalDateTime.of(2023, 1, 1, 12, 0);
		dateRange.setStartDateTime(localDateTime);
		dateRange.setEndDateTime(localDateTime.plusDays(30));
		Mockito.when(lightPersonInformationSearchCriteria.getWhere()).thenReturn(personWhereCriteria);
		Mockito.when(personWhereCriteria.getDateRange()).thenReturn(dateRange);
		setupIKPropertiesForDefaults();

		APIException result = assertThrows(APIException.class,
				() -> service.getPersonWithoutAoid(lightPersonInformationSearchCriteria));
		assertEquals("WCO-101333", result.getErrorCode());
		assertEquals(DEFAULT_TIME_SPAN, result.getUserParameters().get("dayRange"));
	}

	@Test
	public void testGivenIllegalArgumentExceptionIsThrownWhilePersonReadServiceCreatesPageRequest_WhenGetPersonWithoutAoid_ThenCatchAndThrowAPIException(){
		setupIKPropertiesForDefaults();
		Mockito.when(lightPersonInformationSearchCriteria.getWhere()).thenReturn(personWhereCriteria);
		Mockito.when(readService.findWithoutaoid(Mockito.anyLong(), Mockito.anyLong(), Mockito.any(), Mockito.any()))
				.thenThrow(new IllegalArgumentException());

		APIException result = assertThrows(APIException.class,
				() -> service.getPersonWithoutAoid(lightPersonInformationSearchCriteria));
		assertEquals(GENERIC_EXCEPTION_ERROR_CODE, result.getErrorCode());
	}

	@Test
	public void testGivenPersonReadServiceThrowsAPIExceptionBecauseOfNoEmployeeFound_WhenGetPersonWithoutAoid_ThenCatchAndThrowAPIException(){
		String expectedErrorCode = "NO_DATA_NOT_FOUND_FOR_PAYLOAD";
		setupIKPropertiesForDefaults();
		Mockito.when(lightPersonInformationSearchCriteria.getWhere()).thenReturn(personWhereCriteria);
		Mockito.when(readService.findWithoutaoid(Mockito.anyLong(), Mockito.anyLong(), Mockito.any(), Mockito.any()))
				.thenThrow(new APIException(expectedErrorCode));

		APIException result = assertThrows(APIException.class,
				() -> service.getPersonWithoutAoid(lightPersonInformationSearchCriteria));
		assertEquals(expectedErrorCode, result.getErrorCode());
	}

	@Test
	public void testGivenPersonReadServiceThrowsAPIExceptionWhileSearchingEmployees_WhenGetPersonWithoutAoid_ThenCatchAndThrowAPIException(){
		String dataNotFoundErrorCode = "WCO-101336";
		setupIKPropertiesForDefaults();
		Mockito.when(lightPersonInformationSearchCriteria.getWhere()).thenReturn(personWhereCriteria);
		Mockito.when(readService.findWithoutaoid(Mockito.anyLong(), Mockito.anyLong(), Mockito.any(), Mockito.any()))
				.thenThrow(new APIException(dataNotFoundErrorCode));

		APIException result = assertThrows(APIException.class,
				() -> service.getPersonWithoutAoid(lightPersonInformationSearchCriteria));
		assertEquals(GENERIC_EXCEPTION_ERROR_CODE, result.getErrorCode());
	}

	private void setupIKPropertiesForDefaults(){
		Mockito.when(properties.getProperty(MAX_COUNT_SIZE_KEY, DEFAULT_COUNT_SIZE)).thenReturn(DEFAULT_COUNT_SIZE);
		Mockito.when(properties.getProperty(MAX_TIME_SPAN_KEY, DEFAULT_TIME_SPAN)).thenReturn(DEFAULT_TIME_SPAN);
	}

}
