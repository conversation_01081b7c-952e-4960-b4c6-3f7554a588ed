package com.kronos.persons.utils;

import com.google.common.collect.Lists;
import com.kronos.container.api.exception.APIException;
import com.kronos.container.api.util.APIExceptionDetailResult;
import com.kronos.persons.rest.assignments.model.RuleAssignmentBean;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.model.BeanWrapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * BulkProcessor.
 * Copyright (C) 2019 Kronos.com
 * Date: Jun 17, 2019
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class BulkProcessorTest {
    private static final String EXCEPTION_RESULT_KEY = "results";
    private BulkProcessor<BeanWrapper, RuleAssignmentBean> processor;

    private Function<List<BeanWrapper>, List<RuleAssignmentBean>> requestProcessor = (beanWrappers ->
            beanWrappers.stream()
                    .filter(beanWrapper -> Objects.isNull(beanWrapper.getApiException()))
                    .map(beanWrapper -> (RuleAssignmentBean) beanWrapper.getBean())
                    .collect(Collectors.toList()));

    @Test
    public void testProcessWithAllValidBeans() {
        RuleAssignmentBean bean1 = mock(RuleAssignmentBean.class);
        RuleAssignmentBean bean2 = mock(RuleAssignmentBean.class);
        BeanWrapper beanWrapper1 = prepareBeanWrapper(bean1, null);
        BeanWrapper beanWrapper2 = prepareBeanWrapper(bean2, null);
        List<BeanWrapper> requestDataList = Lists.newArrayList(beanWrapper1, beanWrapper2);

        processor = new BulkProcessor<>(requestDataList, requestProcessor);
        List<RuleAssignmentBean> resultList = processor.process();

        assertEquals(2, resultList.size());
        assertEquals(bean1, resultList.get(0));
        assertEquals(bean2, resultList.get(1));
    }

    @Test
    public void testProcessWithPartValidBeans() {
        APIException exception = new APIException("Test error code");
        RuleAssignmentBean bean2 = mock(RuleAssignmentBean.class);
        BeanWrapper beanWrapper1 = prepareBeanWrapper1(mock(RuleAssignmentBean.class), exception);
        BeanWrapper beanWrapper2 = prepareBeanWrapper1(bean2, null);
        List<BeanWrapper> requestDataList = Lists.newArrayList(beanWrapper1, beanWrapper2);

        processor = new BulkProcessor<>(requestDataList, requestProcessor);
        try {
            processor.process();
        } catch (APIException expectedException) {
            APIException resultException = ((APIExceptionDetailResult) ((List) expectedException.getResults()
                    .get(EXCEPTION_RESULT_KEY)).get(0)).getError();
            RuleAssignmentBean resultBean = (RuleAssignmentBean) ((APIExceptionDetailResult)
                    ((List) expectedException.getResults().get(EXCEPTION_RESULT_KEY)).get(1)).getSuccess();
            assertEquals(ExceptionConstants.PARTIAL_SUCCESS, expectedException.getErrorCode());
            assertEquals(2, ((List) expectedException.getResults().get(EXCEPTION_RESULT_KEY)).size());
            assertEquals(exception.getErrorCode(), resultException.getErrorCode());
        }
    }

    @Test
    public void testProcessWithAllNotValidBeans() {
        APIException exception1 = new APIException("Test error code_1");
        APIException exception2 = new APIException("Test error code_2");
        BeanWrapper beanWrapper1 = prepareBeanWrapper1(mock(RuleAssignmentBean.class), exception1);
        BeanWrapper beanWrapper2 = prepareBeanWrapper1(mock(RuleAssignmentBean.class), exception2);
        List<BeanWrapper> requestDataList = Lists.newArrayList(beanWrapper1, beanWrapper2);

        processor = new BulkProcessor<>(requestDataList, requestProcessor);
        try {
            processor.process();
        } catch (APIException expectedException) {
            APIException resultException1 = ((APIExceptionDetailResult) ((List) expectedException.getResults()
                    .get(EXCEPTION_RESULT_KEY)).get(0)).getError();
            APIException resultException2 = ((APIExceptionDetailResult) ((List) expectedException.getResults()
                    .get(EXCEPTION_RESULT_KEY)).get(1)).getError();
            assertEquals(ExceptionConstants.ALL_RECORDS_FAILED, expectedException.getErrorCode());
            assertEquals(2, ((List) expectedException.getResults().get(EXCEPTION_RESULT_KEY)).size());
            assertEquals(exception1.getErrorCode(), resultException1.getErrorCode());
            assertEquals(exception2.getErrorCode(), resultException2.getErrorCode());
        }
    }

    private BeanWrapper prepareBeanWrapper(RuleAssignmentBean bean, APIException exception) {
        BeanWrapper beanWrapper = mock(BeanWrapper.class);
        when(beanWrapper.getBean()).thenReturn(bean);
        when(beanWrapper.getApiException()).thenReturn(exception);
        return beanWrapper;
    }

    private BeanWrapper prepareBeanWrapper1(RuleAssignmentBean bean, APIException exception) {
        BeanWrapper beanWrapper = mock(BeanWrapper.class);
        when(beanWrapper.getApiException()).thenReturn(exception);
        return beanWrapper;
    }
}
