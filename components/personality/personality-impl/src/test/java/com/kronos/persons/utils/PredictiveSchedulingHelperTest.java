package com.kronos.persons.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doReturn;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PredictiveSchedulingHelperTest {
	private static final String GLOBAL_PREDICTIVE_SCHEDULING_ENABLED = "global.PredictiveScheduling.enabled";

	@Mock
	private IKProperties properties;

	@InjectMocks
	private PredictiveSchedulingHelper underTest;
	
	@Test
	public void isPredictiveSchedulingEnabled_whenFalse_thenFalse() {
		boolean expected = false;
		configure(expected);
		
		boolean actual = underTest.isPredictiveSchedulingEnabled();
		
		assertEquals(expected, actual);
	}
	
	@Test
	public void isPredictiveSchedulingEnabled_whenTrue_thenTrue() {
		boolean expected = true;
		configure(expected);
		
		boolean actual = underTest.isPredictiveSchedulingEnabled();
		
		assertEquals(expected, actual);
	}
	
	private void configure(boolean enabled) {
		doReturn(enabled).when(properties).getPropertyAsBoolean(GLOBAL_PREDICTIVE_SCHEDULING_ENABLED, false);
	}
}
