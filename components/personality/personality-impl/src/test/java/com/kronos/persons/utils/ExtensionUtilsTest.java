/*******************************************************************************
 * ExtensionUtilsTest.java
 * Copyright © 2024 UKG Inc. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.utils;


import java.io.IOException;
import java.lang.reflect.Constructor;
import java.lang.reflect.Modifier;
import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;

import com.kronos.people.personality.model.extension.BaseExtension;
import com.kronos.people.personality.model.extension.ExtensionIdentifier;
import com.kronos.persons.rest.beans.extensions.AbstractDataExtension;
import com.kronos.persons.rest.model.ExtensionCriteria;
import com.kronos.persons.rest.model.SearchValues;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kronos.people.personality.model.IdentifierType;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedEntry;
import com.kronos.wfc.commonapp.laborlevel.business.set.LaborAccountSet;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
public class ExtensionUtilsTest {
	private static final String RESPONSE = "Response";

	private static final String MESSAGE = "Message";

	private static final String ACTION = "Action";

	private static final String STATUS = "Status";

	private static final Object FAILURE = "Failure";

	@Mock
	private LaborAccountSet accountSet;


	@Test
	public void validateLongPredicate() {
		Predicate<Long> predicate = ExtensionUtils.getLongNullCheckPredicate();
		assertTrue(predicate.test(1L));
		assertFalse(predicate.test(0L));
		assertFalse(predicate.test(null));
		assertTrue(predicate.test(-1L));

	}

	@SuppressWarnings("rawtypes")
	@Test
	public void validateCollectionPredicate() {
		Predicate<Collection<String>> predicate = ExtensionUtils.getNullorEmptyCheckPredicate();
		List<String> list;
		assertFalse(predicate.test(null));
		list = new ArrayList<>();
		assertFalse(predicate.test(list));
		list.add("Test");
		assertTrue(predicate.test(list));
	}

	@Test
	public void setDates() {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		LocalDate effectiveDate = LocalDate.parse("2015-03-15", formatter);
		LocalDate expirationDate = LocalDate.parse("2016-03-15", formatter);
		EffectiveDatedEntry source = new EffectiveDatedEntry(effectiveDate, expirationDate);
		EffectiveDatedEntry target = new EffectiveDatedEntry();
		ExtensionUtils.setDates(source, target);
		assertEquals(effectiveDate, target.getEffectiveDate());
		assertEquals(expirationDate, target.getExpirationDate());
	}

	@Test
	public void testConstructorIsPrivate() {
		try {
			Constructor<ExtensionUtils> constructor;
			constructor = ExtensionUtils.class.getDeclaredConstructor();
			assertTrue(Modifier.isPrivate(constructor.getModifiers()));
			constructor.setAccessible(true);
			constructor.newInstance();
		} catch (Exception e) {
			fail();
		}
	}

	@Test
	public void longToObjectIdLong() {
		ObjectIdLong id = ExtensionUtils.longToObjectIdLong(1L);
		assertEquals(1L, id.longValue());
	}

	@Test
	public void getPrivateField() {
		TestClass t = new TestClass("Test");
		String value = (String) ExtensionUtils.getPrivateFieldValue(t, "value");
		assertEquals("Test", value);
	}

	@Test
	public void getPrivateFieldException() {
		TestClass t = new TestClass("Test");
		String value = (String) ExtensionUtils.getPrivateFieldValue(t, "value1");
		assertNull(value);
	}

	@SuppressWarnings("deprecation")
	@Test
	public void getLocalizedValue() {
		Mockito.when(accountSet.getStringPropertyKey()).thenReturn("property");
		Mockito.when(accountSet.getName()).thenReturn("value1");
		String value = ExtensionUtils.getLocalizedValue(accountSet);
		assertEquals("value1", value);
	}

	@Test
	public void getIdentifierType() {
		IdentifierType type = ExtensionUtils.getIdentifierType("personnumber");
		assertEquals(String.class, type.getDataType());
	}

	@Test
	public void getErrorResponseMessage() throws IOException {
		JsonNode node = new ObjectMapper().readTree(ExtensionUtils.getErrorResponseMessage(ACTION, "Test"));
		JsonNode responseNode = node.get(RESPONSE);
		assertNotNull(responseNode);

		JsonNode actionNode = responseNode.get(ACTION);
		assertNotNull(actionNode);
		assertEquals(ACTION, actionNode.asText());

		JsonNode statusNode = responseNode.get(STATUS);
		assertNotNull(statusNode);
		assertEquals(FAILURE, statusNode.asText());

		JsonNode errorNode = responseNode.get("Error");
		assertNotNull(errorNode);
		JsonNode messageNode = errorNode.get(MESSAGE);
		assertNotNull(messageNode);
		assertEquals("Test", messageNode.asText());
	}

	@Test
	public void convertStringToLocalDate() {
		LocalDate date = DateUtils.convertStringToLocalDate("2017-01-01", "yyyy-MM-dd");
		assertEquals(Month.JANUARY, date.getMonth());
		assertEquals(2017, date.getYear());
		assertEquals(1, date.getDayOfMonth());

	}

	@Test
	public void runConsumerIfPredicatePass() {
		TestClass obj = new TestClass("1");
		ExtensionUtils.runConsumerIfPredicatePass(obj, t -> !Optional.ofNullable(t.attribute).isPresent(), (d) -> d.setAttribute("Test"));
		assertEquals("Test", obj.attribute);
	}
	
	@Test
    public void formatDateString(){
	    assertNotNull(ExtensionUtils.formatDateStringWithPattern("2017-01-01", "yyyy-MM-dd"));
	}


	@Test
	public void formatDateString_whenValidInput_thenFormatIsApplied(){
		String formatResult = ExtensionUtils.formatDateString("2017-01-01");
		assertNotNull(formatResult);
		assertEquals("2017-01-01", formatResult);
	}

	@Test
	public void formatDateString_whenEmptyInput_thenReturnNull(){
		String formatResult = ExtensionUtils.formatDateString("");
		assertNull(formatResult);
	}


	@Test
	public void populateCommonExtensionAttribute_whenEmptyInput_thenReturnNull(){
		BaseExtension extension = new BaseExtension() {
			@Override
			public ExtensionIdentifier getIdentifier() {
				return new ExtensionIdentifier("123");
			}
		};

		extension.setActive(true);
		extension.setPersonId(1L);
		extension.setPersonNumber("10001");

		AbstractDataExtension dataExtension = new AbstractDataExtension() {
			@Override
			public ExtensionIdentifier getIdentifier() {
				return new ExtensionIdentifier("123");
			}
		};


		ExtensionUtils.populateCommonExtensionAttribute(extension, dataExtension);
		assertEquals(extension.getPersonId(), dataExtension.getPersonId());
		assertTrue(dataExtension.isActive());
		assertEquals(extension.getPersonNumber(), dataExtension.getPersonNumber());

	}

	@Test
	public void getEndDate_whenItIsCalled_thenReturnAValidResult(){
		LocalDate dateResult = ExtensionUtils.getEndDate();
		assertNotNull(dateResult);
		assertEquals("3000-01-01", dateResult.toString());
	}

	@Test
	public void withinDateRange_between_thenReturnTrue(){
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		LocalDate startDate = LocalDate.parse("2015-03-15", formatter);
		LocalDate endDate = LocalDate.parse("2016-03-15", formatter);
		LocalDate validateDate = LocalDate.parse("2016-01-15", formatter);

		boolean inRange = ExtensionUtils.withinDateRange(startDate, endDate, validateDate);
		assertTrue(inRange);
	}

	@Test
	public void withinDateRange_notBetween_thenReturnFalse(){
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		LocalDate startDate = LocalDate.parse("2015-03-15", formatter);
		LocalDate endDate = LocalDate.parse("2016-03-15", formatter);
		LocalDate validateDate = LocalDate.parse("2015-02-15", formatter);

		boolean inRange = ExtensionUtils.withinDateRange(startDate, endDate, validateDate);
		assertFalse(inRange);
	}

	@Test
	public void isNull_notNullInput_thenReturnFalse(){
		boolean isNull = ExtensionUtils.isNull(new Object());
		assertFalse(isNull);
	}

	@Test
	public void isNull_nullInput_thenReturnTrue(){
		boolean isNull = ExtensionUtils.isNull(null);
		assertTrue(isNull);
	}

	@Test
	public void createAoidCoidList_notBetween_thenReturnFalse(){
		List<SearchValues> multiKeyValues = new ArrayList<>();
		SearchValues value=new SearchValues();
		value.setAoid("90109");
		value.setCoid("90109");
		multiKeyValues.add(value);

		ExtensionCriteria criteriaBean = new ExtensionCriteria();
		criteriaBean.setMultiKeyValues(multiKeyValues);

		List<String> listAoidReq = new ArrayList<>();
		Map<String, String> aoidCoidMap = new HashMap<>();
		List<List<String>>  aoidCoidList = ExtensionUtils.createAoidCoidList(criteriaBean, listAoidReq, aoidCoidMap);
		assertFalse(aoidCoidList.isEmpty());
		assertEquals(Arrays.asList("90109", "90109"), aoidCoidList.get(0));
	}

	private static class TestClass {
		public TestClass(String value) {
			this.value = value;
		}

		String attribute;

		public void setAttribute(String attribute) {
			this.attribute = attribute;

		}

		@SuppressWarnings("unused")
		private final String value;

	}

}
