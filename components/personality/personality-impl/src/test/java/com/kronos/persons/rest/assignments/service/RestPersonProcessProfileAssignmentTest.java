package com.kronos.persons.rest.assignments.service;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonNotification;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.ProcessProfileAssignmentRequestBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.PersistenceException;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RestPersonProcessProfileAssignmentTest {

    @InjectMocks
    RestPersonProcessProfileAssignment restService;

    @Mock
    PersonProcessProfileAssignmentService personProcessProfileAssignmentService;

    @Mock
    PersonAssignmentHelper<ProcessProfileAssignmentRequestBean> assignmentHeplper;
    
    @Mock
    Personality persoanlity;

    @Mock
    PersonIdentityBeanValidator personIdentityBeanValidator;

    private MockedStatic<KronosProperties> mockedKronosProperties;
    private MockedStatic<PersonNotification> mockedPersonNotification;
    private MockedStatic<PrsnException> mockedPrsnException;

    @BeforeEach
    public void setUp() {
        mockedKronosProperties = Mockito.mockStatic(KronosProperties.class);
        mockedPersonNotification = Mockito.mockStatic(PersonNotification.class);
        mockedPrsnException = Mockito.mockStatic(PrsnException.class);
    }

    @AfterEach
    public void tearDown() {
        mockedKronosProperties.close();
        mockedPersonNotification.close();
        mockedPrsnException.close();
    }

    @Test
    public void update()throws Exception {
        ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
        Mockito.when(personProcessProfileAssignmentService.update(requestBean)).thenReturn(persoanlity);
        //PowerMockito.doNothing().when(PersonNotification.class, "sendUpdate", new ObjectIdLong(1L));  //Todo
        Mockito.when(persoanlity.getPersonNumber()).thenReturn("111");
        Mockito.when(persoanlity.getPersonId()).thenReturn(new ObjectIdLong(1L));
        PersonIdentityBean employee = new PersonIdentityBean();
		requestBean.setPersonIdentity(employee);
        requestBean.setEmployeeProcessProfileName("testProfile");
        requestBean.setManagerProcessProfileName("testProfile");
        ProcessProfileAssignmentRequestBean responseBean = restService.update(requestBean);
        assertNotNull(responseBean);
    }

    @Test
    public void multiUpdate() throws Exception {
        mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("5");
        ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
        List<ProcessProfileAssignmentRequestBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        Mockito.when(personProcessProfileAssignmentService.update(requestBean,false)).thenReturn(personality);
        Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any())).thenReturn(personality);
        //PowerMockito.doNothing().when(PersonNotification.class, "sendBulkUpdate", new ArrayList<>(), true);   //Todo
        List<ProcessProfileAssignmentRequestBean> responseList = restService.multiUpdate(requestDataList);
        assertEquals(1, responseList.size());
    }

    @Test
    public void testMultiUpdate_whenGetPersonalityThrowException() {
        mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("5");
        ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
        List<ProcessProfileAssignmentRequestBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(personProcessProfileAssignmentService.update(requestBean,false)).thenThrow(new APIException());
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any())).thenReturn(personality);
        try {
            //PowerMockito.doNothing().when(PersonNotification.class, "sendBulkUpdate", new ArrayList<>(), true);   //Todo
            Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any())).thenThrow(new APIException());
            restService.multiUpdate(requestDataList);
            fail();
        } catch (Exception e) {
            if (!(e instanceof APIException)) {
                fail();
            }
        }
    }
    
    @SuppressWarnings("unchecked")
    @Test
    public void testMultiUpdateShouldThrowGenericException() {
        ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
        List<ProcessProfileAssignmentRequestBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);

        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(personProcessProfileAssignmentService.update(requestBean, false)).thenThrow(PersistenceException.class);
        mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
        Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any())).thenReturn(personality);
        mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("5");

        APIException exception = assertThrows(APIException.class, () -> restService.multiUpdate(requestDataList));
        assertNotNull(exception);
    }
      
      @SuppressWarnings("unchecked")
      @Test
      public void testMultiUpdateShouldThrowException() {
          ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
          List<ProcessProfileAssignmentRequestBean> requestDataList = new ArrayList<>();
          requestDataList.add(requestBean);

          Mockito.when(personProcessProfileAssignmentService.update(requestBean, false)).thenThrow(RuntimeException.class);
          mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
          Personality personality = Mockito.mock(Personality.class);
          Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any())).thenReturn(personality);
          mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("5");

          APIException exception = assertThrows(APIException.class, () -> restService.multiUpdate(requestDataList));
          assertNotNull(exception);
      }
      
      @SuppressWarnings("unchecked")
      @Test
      public void testMultiUpdateShouldThrowAPIException() {
          ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
          List<ProcessProfileAssignmentRequestBean> requestDataList = new ArrayList<>();
          requestDataList.add(requestBean);

          Personality personality = Mockito.mock(Personality.class);
          Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any())).thenReturn(personality);
          Mockito.when(personProcessProfileAssignmentService.update(requestBean, false)).thenThrow(APIException.class);
          mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(APIException.class))).thenReturn(new APIException());
          mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("5");

          APIException exception = assertThrows(APIException.class, () -> restService.multiUpdate(requestDataList));
          assertNotNull(exception);
      }
      
    @Test
    public void retrieveByPersonIdTest() {
    	ProcessProfileAssignmentRequestBean bean = new ProcessProfileAssignmentRequestBean();
        Long personId=123L;
        Mockito.when(personProcessProfileAssignmentService.load(Mockito.any())).thenReturn(bean);
		assertEquals(bean, restService.retrieveByPersonId(personId));
    }
    
    @Test
    public void retrieveByPersonNumberTest() {
    	ProcessProfileAssignmentRequestBean bean = new ProcessProfileAssignmentRequestBean();
        String personNumber="123";
        Mockito.when(personProcessProfileAssignmentService.load(Mockito.any())).thenReturn(bean);
		assertEquals(bean, restService.retrieveByPersonNumber(personNumber));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void retrieveAPIExceptionTest() {
        Mockito.when(personProcessProfileAssignmentService.load(Mockito.any())).thenThrow(APIException.class);
        try {
            restService.retrieveByPersonId(10L);
        } catch (APIException e) {
            assertTrue(true);
        }
    }

    @SuppressWarnings("unchecked")
	@Test
    public void retrieveGenericExceptionTest() {
        Mockito.when(personProcessProfileAssignmentService.load(Mockito.any())).thenThrow(PersistenceException.class);
        mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
        try {
            restService.retrieveByPersonId(10L);
        } catch (Exception e) {
            assertTrue(true);
        }
    }
    
    @SuppressWarnings("unchecked")
    @Test
    public void retrieveExceptionTest() {
        Mockito.when(personProcessProfileAssignmentService.load(Mockito.any())).thenThrow(RuntimeException.class);
        mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
        try {
            restService.retrieveByPersonId(10L);
        } catch (APIException e) {
            assertTrue(true);
        }
    }
    
    @Test
    public void retrieveListTest() {
        Function<PersonIdentityBean, ProcessProfileAssignmentRequestBean> getDataListFunction = personProcessProfileAssignmentService::load;
        ExtensionSearchCriteria criteria = new ExtensionSearchCriteria();
        ProcessProfileAssignmentRequestBean requestBean1 = new ProcessProfileAssignmentRequestBean();
        List<ProcessProfileAssignmentRequestBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean1);
        Mockito.when(assignmentHeplper.getPersonAssignmentList(criteria, getDataListFunction)).thenReturn(requestDataList);
        List<ProcessProfileAssignmentRequestBean> responseList = restService.retrieveList(criteria);
        assertEquals(0, responseList.size());
    }

}
