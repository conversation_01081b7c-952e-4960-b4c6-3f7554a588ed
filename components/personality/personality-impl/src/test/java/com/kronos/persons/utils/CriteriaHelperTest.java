/*******************************************************************************
 * CriteriaHelperTest.java
 * Copyright © 2024 UKG Inc. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.utils;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.kronos.container.api.access.SpringContext;
import com.kronos.people.personality.model.Criteria;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.service.PersonalityService;
import com.kronos.persons.rest.beans.HyperFindFilterBean;
import com.kronos.persons.rest.extensions.service.CriteriaValidator;
import com.kronos.persons.rest.model.EmployeeCriteria;
import com.kronos.persons.rest.model.ExtensionCriteria;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.rest.model.ExtensionWhereCriteria;
import com.kronos.persons.rest.model.PersonQualifierBean;
import com.kronos.persons.rest.model.PersonQualifierDetailBean;
import com.kronos.persons.rest.model.SearchValues;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.exception.ExceptionConstants;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;


/**
 * Class with unit tests for CriteriaHelper.java
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
public class CriteriaHelperTest {

	private static final String AOID = "aoid";
	private static final String COID = "coid";
	private static final String ID_100 = "100";
	private static final String ID_200 = "200";

	private MockedStatic<SpringContext> mockedSpringContext;

	CriteriaHelper criteriaHelper = new CriteriaHelper();

	@BeforeEach
	public void setup(){
		mockedSpringContext = mockStatic(SpringContext.class);
	}

	@AfterEach
	public void tearDown(){
		mockedSpringContext.close();
	}

	@Test
	public void testValidateCriteriaForSingleKey() {
		try {
			criteriaHelper.validateCriteriaForMultiKeyNotAllowed(getSearchCriteriaSingleKey());
		} catch (APIException ex) {
			fail("Must not reach here");
		}
	}

	@Test
	public void testValidateCriteriaForMultiKey() {
		try {
			criteriaHelper.validateCriteriaForMultiKeyNotAllowed(getSearchCriteriaMultiKey());
			fail("Must not reach here");
		} catch (APIException ex) {
			assertEquals(ExceptionConstants.EXCEPTION_101221,
					ex.getErrorCode());
		}
	}

	@Test
	public void testGetExtensionCriteriaShouldThrowAPIExceptionWhenEmployeeCriteriaLacksKeyAndMultiKey(){
		ExtensionSearchCriteria extensionSearchCriteria = new ExtensionSearchCriteria();
		APIException result = assertThrows(APIException.class,
				() -> criteriaHelper.getExtensionCriteria(extensionSearchCriteria));

		assertEquals("WCO-101203", result.getErrorCode());
	}

	@Test
	public void testGetExtensionCriteriaShouldThrowAPIExceptionWhenEmployeeCriteriaIsNull(){
		ExtensionSearchCriteria extensionSearchCriteria = new ExtensionSearchCriteria();
		ExtensionWhereCriteria extensionWhereCriteria = new ExtensionWhereCriteria();
		extensionWhereCriteria.setEmployees(null);
		extensionSearchCriteria.setWhere(extensionWhereCriteria);

		APIException result = assertThrows(APIException.class,
				() -> criteriaHelper.getExtensionCriteria(extensionSearchCriteria));
		assertEquals("WCO-101231", result.getErrorCode());
		assertEquals("employees", result.getUserParameters().get("missingProperty"));
	}

	@Test
	public void testGetExtensionCriteriaShouldThrowAPIExceptionEmployeeCriteriaHasBothKeyAndMultiKey() throws NoSuchMethodException {
		CriteriaValidator criteriaValidatorMock = Mockito.mock(CriteriaValidator.class);
		ExtensionSearchCriteria extensionSearchCriteria = new ExtensionSearchCriteria();
		ExtensionWhereCriteria extensionWhereCriteria = new ExtensionWhereCriteria();
		EmployeeCriteria employeeCriteria = new EmployeeCriteria();
		criteriaHelper.criteriaValidator = criteriaValidatorMock;
		when(SpringContext.getBean(CriteriaValidator.class)).thenReturn(criteriaValidatorMock);

		List<String> singlenessValues = new ArrayList<>();
		singlenessValues.add("1001");
		employeeCriteria.setValues(singlenessValues);
		employeeCriteria.setKey("personnumber");

		List<String> keys = Arrays.asList("AOID","COID");
		employeeCriteria.setMultiKey(keys);
		List<String> multiKeylist = Arrays.asList(ID_100, ID_200);
		employeeCriteria.setValues(multiKeylist);
		extensionWhereCriteria.setEmployees(employeeCriteria);
		extensionSearchCriteria.setWhere(extensionWhereCriteria);
		
		when(criteriaHelper.criteriaValidator.getSearchValueConstructor(any())).thenReturn((Constructor) String.class.getConstructor(String.class));
		APIException result = assertThrows(APIException.class,
				() -> criteriaHelper.getExtensionCriteria(extensionSearchCriteria));
		assertEquals("WCO-101314", result.getErrorCode());
	}

	@Test
	public void testGetExtensionCriteriaShouldFillMultiKeyValuesWhenAOIDKeyIsFirstElement(){
		ExtensionSearchCriteria extensionSearchCriteria = new ExtensionSearchCriteria();
		ExtensionWhereCriteria dummyExtensionWhereCriteria = buildDummyExtensionWhereCriteria(AOID, COID);
		extensionSearchCriteria.setWhere(dummyExtensionWhereCriteria);

		ExtensionCriteria result = criteriaHelper.getExtensionCriteria(extensionSearchCriteria);
		SearchValues resultSearchValues = result.getMultiKeyValues().get(0);

		assertFields(dummyExtensionWhereCriteria, result);
		assertEquals(ID_100, resultSearchValues.getAoid());
		assertEquals(ID_200, resultSearchValues.getCoid());
	}

	@Test
	public void testGetExtensionCriteriaShouldFillMultiKeyValuesWhenCOIDKeyIsFirstElement(){
		ExtensionSearchCriteria extensionSearchCriteria = new ExtensionSearchCriteria();
		ExtensionWhereCriteria dummyExtensionWhereCriteria = buildDummyExtensionWhereCriteria(COID, AOID);
		extensionSearchCriteria.setWhere(dummyExtensionWhereCriteria);

		ExtensionCriteria result = criteriaHelper.getExtensionCriteria(extensionSearchCriteria);
		SearchValues resultSearchValues = result.getMultiKeyValues().get(0);

		assertFields(dummyExtensionWhereCriteria, result);
		assertEquals(ID_200, resultSearchValues.getAoid());
		assertEquals(ID_100, resultSearchValues.getCoid());
	}

	@Test
	public void testGetPersonQualifiersShouldFillPersonQualifierBeanWhenExtensionCriteriaIsValid(){
//		PowerMockito.mockStatic(SpringContext.class);
		CriteriaValidator criteriaValidatorMock = Mockito.mock(CriteriaValidator.class);
		PersonalityService personalityServiceMock = Mockito.mock(PersonalityService.class);
		Map<Object, PersonalityResponse<EmployeeExtension>> personalityResponseMap = new HashMap<>();
		PersonalityResponse<EmployeeExtension> personalityResponse = new PersonalityResponse<>();
		EmployeeExtension employeeExtension = new EmployeeExtension();
		Long expectedPersonId = 1L;
		String expectedPersonNumber = "123456";
		String expectedQualifier = "qualifier";
		employeeExtension.setPersonId(expectedPersonId);
		employeeExtension.setPersonNumber(expectedPersonNumber);
		personalityResponse.setextension(employeeExtension);
		personalityResponseMap.put(expectedQualifier, personalityResponse);
		when(personalityServiceMock.findEmployeeExtensionsByCriteria(any())).thenReturn(personalityResponseMap);
		when(criteriaValidatorMock.validateAndGetCriteria(any())).thenReturn(new Criteria());
//		PowerMockito.when(SpringContext.getBean(CriteriaValidator.class)).thenReturn(criteriaValidatorMock);
		mockedSpringContext.when(() -> SpringContext.getBean(CriteriaValidator.class)).thenReturn(criteriaValidatorMock);
//		PowerMockito.when(SpringContext.getBean(PersonalityService.class)).thenReturn(personalityServiceMock);
		mockedSpringContext.when(() -> SpringContext.getBean(PersonalityService.class)).thenReturn(personalityServiceMock);

		PersonQualifierBean result = criteriaHelper.getPersonQualifiers(new ExtensionCriteria());
		PersonQualifierDetailBean resultPersonQualifierDetailBean = result.getPersonDetails().get(0);

		assertEquals(expectedPersonId, resultPersonQualifierDetailBean.getPersonId());
		assertEquals(expectedPersonNumber, resultPersonQualifierDetailBean.getPersonNumber());
		assertEquals(expectedQualifier, resultPersonQualifierDetailBean.getQualifier());
	}

	private ExtensionSearchCriteria getSearchCriteriaSingleKey() {
		ExtensionSearchCriteria criteria = new ExtensionSearchCriteria();
		ExtensionWhereCriteria where = new ExtensionWhereCriteria();
		EmployeeCriteria employeeCriteria = new EmployeeCriteria();
		employeeCriteria.setKey("personnumber");
		List<String> list = new ArrayList<>();
		list.add("1234");
		list.add("1222");
		employeeCriteria.setValues(list);
		where.setEmployees(employeeCriteria);
		criteria.setWhere(where);
		return criteria;
	}

	private ExtensionSearchCriteria getSearchCriteriaMultiKey() {
		ExtensionSearchCriteria criteria = new ExtensionSearchCriteria();
		ExtensionWhereCriteria where = new ExtensionWhereCriteria();
		EmployeeCriteria employeeCriteria = new EmployeeCriteria();
		employeeCriteria.setMultiKey(Arrays.asList("aoid","coid"));
		where.setEmployees(employeeCriteria);
		criteria.setWhere(where);
		return criteria;
	}

	private ExtensionWhereCriteria buildDummyExtensionWhereCriteria(String firstKey, String secondKey){
		ExtensionWhereCriteria extensionWhereCriteria = new ExtensionWhereCriteria();
		extensionWhereCriteria.setExtensionType("accrual");
		extensionWhereCriteria.setOnlyActivePerson(true);
		extensionWhereCriteria.setIncludeBaseWages("10.00");
		extensionWhereCriteria.setHyperFindFilter(new HyperFindFilterBean());
		extensionWhereCriteria.setSnapshotDate("2024-10-20");
		extensionWhereCriteria.setSnapshotDateTime("2024-10-21T09:00:00");
		extensionWhereCriteria.setIsEmployeeAllowed(true);
		extensionWhereCriteria.setIncludeAccrualPolicyDetails(true);

		EmployeeCriteria employeeCriteria = new EmployeeCriteria();
		List<String> keys = Arrays.asList(firstKey, secondKey);
		employeeCriteria.setMultiKey(keys);
		List<List<String>> multiKeylist = new ArrayList<>();
		List<String> multiKeyValuesList = Arrays.asList(ID_100, ID_200);
		multiKeylist.add(multiKeyValuesList);
		employeeCriteria.setMultiKeyValues(multiKeylist);
		extensionWhereCriteria.setEmployees(employeeCriteria);

		return extensionWhereCriteria;
	}

	private void assertFields(ExtensionWhereCriteria whereCriteria, ExtensionCriteria result){
		assertEquals(whereCriteria.getExtensionType(), result.getExtensionType());
		assertEquals(whereCriteria.getOnlyActivePerson(), result.getOnlyActivePerson());
		assertEquals(whereCriteria.getIncludeBaseWages(), result.getIncludeBaseWages());
		assertSame(whereCriteria.getHyperFindFilter(), result.getHyperFindFilter());
		assertEquals(whereCriteria.getSnapshotDate(), result.getSnapshotDate());
		assertEquals(whereCriteria.getSnapshotDateTime(), result.getSnapshotDateTime());
		assertEquals(whereCriteria.getIsEmployeeAllowed(), result.getIsEmployeeAllowed());
		assertEquals(whereCriteria.getIncludeAccrualPolicyDetails(), result.getIncludeAccrualPolicyDetails());
		EmployeeCriteria employeeCriteria = whereCriteria.getEmployees();
		assertEquals(employeeCriteria.getMultiKey(), result.getMultiKey());
	}

	@Test
	public void testPopulateEmployeeCriteriaForSingleKeyWithNullValues() throws NoSuchMethodException {
		CriteriaValidator criteriaValidatorMock = Mockito.mock(CriteriaValidator.class);
		ExtensionSearchCriteria criteria = new ExtensionSearchCriteria();
		ExtensionWhereCriteria where = new ExtensionWhereCriteria();
		CriteriaHelper criteriaHelper = new CriteriaHelper();
		criteriaHelper.criteriaValidator = criteriaValidatorMock;
		EmployeeCriteria employeeCriteria = new EmployeeCriteria();
		employeeCriteria.setKey("personnumber");
		List<String> values = new ArrayList<>();
		values.add(null);
		employeeCriteria.setValues(values);
		where.setEmployees(employeeCriteria);
		criteria.setWhere(where);
		when(criteriaHelper.criteriaValidator.getSearchValueConstructor(any())).thenReturn((Constructor) String.class.getConstructor(String.class));
		ExtensionCriteria result = criteriaHelper.getExtensionCriteria(criteria);
		assertNotNull(result);
		assertEquals(where.getEmployees().getKey().toLowerCase(),result.getSearchBy());
	}

	@Test
	public void testPopulateEmployeeCriteriaForSingleKeyWithNonNullValues() throws NoSuchMethodException {
		CriteriaValidator criteriaValidatorMock = Mockito.mock(CriteriaValidator.class);
		ExtensionSearchCriteria criteria = new ExtensionSearchCriteria();
		ExtensionWhereCriteria where = new ExtensionWhereCriteria();
		CriteriaHelper criteriaHelper = new CriteriaHelper();
		criteriaHelper.criteriaValidator = criteriaValidatorMock;
		EmployeeCriteria employeeCriteria = new EmployeeCriteria();
		employeeCriteria.setKey("personnumber");
		List<String> values = new ArrayList<>();
		values.add("123456");
		values.add("666888");
		employeeCriteria.setValues(values);
		where.setEmployees(employeeCriteria);
		criteria.setWhere(where);
		when(criteriaHelper.criteriaValidator.getSearchValueConstructor(any())).thenReturn((Constructor) String.class.getConstructor(String.class));
		ExtensionCriteria result = criteriaHelper.getExtensionCriteria(criteria);
		assertNotNull(result);
		assertEquals(where.getEmployees().getKey().toLowerCase(),result.getSearchBy());
	}

}