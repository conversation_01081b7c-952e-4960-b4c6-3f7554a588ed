/*******************************************************************************
 * BulkAccountManagementServiceHelperTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.accountmanagement.service;

import com.kronos.accountmanagement.api.AccountManagement;
import com.kronos.accountmanagement.domain.AccountManagementResponse;
import com.kronos.accountmanagement.domain.AccountStatus;
import com.kronos.accountmanagement.domain.AuthenticationMethod;
import com.kronos.accountmanagement.domain.TransactionDTO;
import com.kronos.accountmanagement.domain.UserDTO;
import com.kronos.accountmanagement.domain.UserFailureDTO;
import com.kronos.accountmanagement.domain.UserFailureListDTO;
import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.exception.APIException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;


import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.spy;

/**
 * Test class for {@link BulkAccountManagementServiceHelper}
 */
@ExtendWith(MockitoExtension.class)
public class BulkAccountManagementServiceHelperTest {
    private static final String AMS_ERROR = "ams error";
    private BulkAccountManagementServiceHelper bulkAccountManagementServiceHelper;
    @Mock
    private AccountManagement accountManagement;
    @Mock
    private IKProperties properties;

    private static final String TRANSACTION_ID = "123456";
    private static final String TENANT_ID = "tenant";

    @BeforeEach
    public void setup() {
        bulkAccountManagementServiceHelper = spy(new BulkAccountManagementServiceHelper(accountManagement, TENANT_ID, properties));
    }

    @Test
    public void testBulkOperationForCreateWhenAmsResponseIsSuccessThenNoError() {
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_POLLING_INTERVAL, "500")).thenReturn("10");
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_TIMEOUT, "60000")).thenReturn("1");
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        mockCreateUsersAmsResponseForSuccess(userDtoList);
        mockTransactionStatusAmsResponseForSuccess();

        AccountManagementResponse<UserFailureListDTO> amsFailure = bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.CREATE);
        assertNull(amsFailure);
        Mockito.verify(accountManagement, Mockito.times(1)).createUsers(TENANT_ID, userDtoList);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);
    }

    @Test
    public void testBulkOperationForCreateWhenAmsTransactionHasError() {
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_POLLING_INTERVAL, "500")).thenReturn("10");
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_TIMEOUT, "60000")).thenReturn("1");
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        mockCreateUsersAmsResponseForSuccess(userDtoList);
        mockTransactionStatusAmsResponseForError();

        APIException apiException = assertThrows(APIException.class, () -> bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.CREATE));

        assertEquals(AMS_ERROR, apiException.getUserParameters().get("errorMessage"));
        assertEquals("WCO-101561", apiException.getErrorCode());
        Mockito.verify(accountManagement, Mockito.times(1)).createUsers(TENANT_ID, userDtoList);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);
    }

    @Test
    public void testBulkOperationForCreateWhenAmsTransactionHasNoResponse() {
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_POLLING_INTERVAL, "500")).thenReturn("10");
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_TIMEOUT, "60000")).thenReturn("1");
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        mockCreateUsersAmsResponseForSuccess(userDtoList);
        mockTransactionStatusAmsResponseForBlank();

        APIException apiException = assertThrows(APIException.class, () -> bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.CREATE));

        assertEquals("Unknown error from AMS during transaction status call.", apiException.getUserParameters().get("errorMessage"));
        assertEquals("WCO-101563", apiException.getErrorCode());
        Mockito.verify(accountManagement, Mockito.times(1)).createUsers(TENANT_ID, userDtoList);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);
    }

    @Test
    public void testBulkOperationForCreateWhenAmsTransactionHasException() {
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_POLLING_INTERVAL, "500")).thenReturn("10");
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_TIMEOUT, "60000")).thenReturn("1");
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        mockCreateUsersAmsResponseForSuccess(userDtoList);
        Mockito.when(accountManagement.getTransaction(TENANT_ID, TRANSACTION_ID)).thenThrow(new RuntimeException("test"));

        APIException apiException = assertThrows(APIException.class, () -> bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.CREATE));
        assertEquals("Unknown exception from AMS during transaction status call.", apiException.getUserParameters().get("errorMessage"));
        assertEquals("WCO-101563", apiException.getErrorCode());
        Mockito.verify(accountManagement, Mockito.times(1)).createUsers(TENANT_ID, userDtoList);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);
    }

    @Test
    public void testBulkOperationForCreateWhenAmsErrorTransactionApiHasNoResponse() {
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_POLLING_INTERVAL, "500")).thenReturn("10");
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_TIMEOUT, "60000")).thenReturn("1");
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        mockCreateUsersAmsResponseForSuccess(userDtoList);
        mockTransactionStatusAmsResponseForFailure();
        mockErrorTransactionApiResponseForSuccess();

        AccountManagementResponse<UserFailureListDTO> amsFailure = bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.CREATE);
        assertNotNull(amsFailure);
        assertEquals(1, amsFailure.getResponse().getUserFailures().size());
        Mockito.verify(accountManagement, Mockito.times(1)).createUsers(TENANT_ID, userDtoList);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);
    }


    @Test
    public void testBulkOperationForCreateWhenAmsErrorTransactionApiHasError() {
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_POLLING_INTERVAL, "500")).thenReturn("10");
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_TIMEOUT, "60000")).thenReturn("1");
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        mockCreateUsersAmsResponseForSuccess(userDtoList);
        mockTransactionStatusAmsResponseForFailure();
        mockErrorTransactionApiResponseForError();

        APIException apiException = assertThrows(APIException.class, () -> bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.CREATE));

        assertEquals(AMS_ERROR, apiException.getUserParameters().get("errorMessage"));
        assertEquals("WCO-101561", apiException.getErrorCode());
        Mockito.verify(accountManagement, Mockito.times(1)).createUsers(TENANT_ID, userDtoList);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);
    }

    @Test
    public void testBulkOperationForCreateWhenAmsErrorTransactionApiHasErrorNoResponse() {
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_POLLING_INTERVAL, "500")).thenReturn("10");
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_TIMEOUT, "60000")).thenReturn("1");
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        mockCreateUsersAmsResponseForSuccess(userDtoList);
        mockTransactionStatusAmsResponseForFailure();
        mockErrorTransactionApiResponseForBlankResponse();

        APIException apiException = assertThrows(APIException.class, () -> bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.CREATE));

        assertEquals("Unknown error from AMS during transaction error call.", apiException.getUserParameters().get("errorMessage"));
        assertEquals("WCO-101563", apiException.getErrorCode());
        Mockito.verify(accountManagement, Mockito.times(1)).createUsers(TENANT_ID, userDtoList);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);
    }

    @Test
    public void testBulkOperationForCreateWhenAmsErrorTransactionApiHasException() {
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_POLLING_INTERVAL, "500")).thenReturn("10");
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_TIMEOUT, "60000")).thenReturn("1");
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        mockCreateUsersAmsResponseForSuccess(userDtoList);
        mockTransactionStatusAmsResponseForCompletedWithError();
        Mockito.when(accountManagement.getTransactionErrors(TENANT_ID, TRANSACTION_ID)).thenThrow(new RuntimeException("test"));

        APIException apiException = assertThrows(APIException.class, () -> bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.CREATE));
        assertEquals("Unknown exception from AMS during transaction error call.", apiException.getUserParameters().get("errorMessage"));
        assertEquals("WCO-101563", apiException.getErrorCode());
        Mockito.verify(accountManagement, Mockito.times(1)).createUsers(TENANT_ID, userDtoList);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);

    }


    @Test
    public void testBulkOperationForCreateWhenAmsResponseHasException() {
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        Mockito.when(accountManagement.createUsers(TENANT_ID, userDtoList)).thenThrow(new RuntimeException());

        APIException apiException = assertThrows(APIException.class, () -> bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.CREATE));
        assertEquals("Unknown error from AMS during create action", apiException.getUserParameters().get("errorMessage"));
        assertEquals("WCO-101563", apiException.getErrorCode());
        Mockito.verify(accountManagement, Mockito.times(1)).createUsers(TENANT_ID, userDtoList);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);

    }

    @Test
    public void testBulkOperationForCreateWhenAmsResponseHasErrorStatus() {
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        mockCreateUsersAmsResponseForError(userDtoList);

        APIException apiException = assertThrows(APIException.class, () -> bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.CREATE));

        assertEquals(AMS_ERROR, apiException.getUserParameters().get("errorMessage"));
        assertEquals("WCO-101561", apiException.getErrorCode());
        Mockito.verify(accountManagement, Mockito.times(1)).createUsers(TENANT_ID, userDtoList);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);
    }

    @Test
    public void testBulkOperationForCreateWhenAmsResponseHasNoResponse() {
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        mockCreateUsersAmsResponseBlankResponse(userDtoList);

        APIException apiException = assertThrows(APIException.class, () -> bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.CREATE));

        assertEquals("Unknown error from AMS.", apiException.getUserParameters().get("errorMessage"));
        assertEquals("WCO-101563", apiException.getErrorCode());
        Mockito.verify(accountManagement, Mockito.times(1)).createUsers(TENANT_ID, userDtoList);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);
    }

    @Test
    public void testBulkOperationForCreateWhenInterruptedException() throws Exception {
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        mockCreateUsersAmsResponseForSuccess(userDtoList);

        doThrow(new InterruptedException()).when(bulkAccountManagementServiceHelper).amsTransactionStatusPolling(TRANSACTION_ID);

        APIException apiException = assertThrows(APIException.class, () -> bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.CREATE));

        assertEquals("Unknown Interruption error from AMS while polling status.", apiException.getUserParameters().get("errorMessage"));
        assertEquals("WCO-101563", apiException.getErrorCode());
        Mockito.verify(accountManagement, Mockito.times(1)).createUsers(TENANT_ID, userDtoList);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);
    }

    @Test
    public void testBulkOperationForUpdateWhenAmsResponseIsSuccessThenNoError() {
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_POLLING_INTERVAL, "500")).thenReturn("10");
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_TIMEOUT, "60000")).thenReturn("1");
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        mockUpdateUsersAmsResponseForSuccess(userDtoList);
        mockTransactionStatusAmsResponseForSuccess();

        AccountManagementResponse<UserFailureListDTO> amsFailure = bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.UPDATE);
        assertNull(amsFailure);

        Mockito.verify(accountManagement, Mockito.times(1)).updateUsers(TENANT_ID, userDtoList);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);
    }

    @Test
    public void testBulkOperationForDeleteWhenAmsResponseIsSuccessThenNoError() {
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());
        List<String> userIdList = userDtoList.stream().map(UserDTO::getUserId).collect(Collectors.toList());

        // Mock the delete users AMS response
        mockDeleteUsersAmsResponseForSuccess(userIdList);

        // Mock the transaction status AMS response with a valid transaction ID
        mockTransactionStatusAmsResponseForSuccess();

        // Ensure properties return valid values for polling interval and timeout
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_POLLING_INTERVAL, "500")).thenReturn("500");
        Mockito.when(properties.getProperty(BulkAccountManagementServiceHelper.AMS_BULKAPI_TIMEOUT, "60000")).thenReturn("60000");

        AccountManagementResponse<UserFailureListDTO> amsFailure = bulkAccountManagementServiceHelper.bulkOperation(userDtoList, BulkAccountManagementServiceHelper.DELETE);

        assertNull(amsFailure);
        Mockito.verify(accountManagement, Mockito.times(1)).deleteUsers(TENANT_ID, userIdList);
        Mockito.verify(accountManagement, Mockito.times(1)).getTransaction(TENANT_ID, TRANSACTION_ID);
        Mockito.verify(accountManagement, Mockito.times(0)).getTransactionErrors(TENANT_ID, TRANSACTION_ID);
    }

    @Test
    public void testBulkOperationForUnknownOperation() {
        List<UserDTO> userDtoList = new ArrayList<>();
        userDtoList.add(getUserDto());
        userDtoList.add(getUserDto());

        assertThrows(APIException.class, () -> bulkAccountManagementServiceHelper.bulkOperation(userDtoList, "test"));
    }

    private void mockDeleteUsersAmsResponseForSuccess(List<String> useIdList) {
        TransactionDTO transactionDTO = new TransactionDTO(TRANSACTION_ID, TENANT_ID, "DELETED_AT", BulkAccountManagementServiceHelper.JOB_SUCCESS);
        AccountManagementResponse<TransactionDTO> accountManagementResponse = new AccountManagementResponse<>(transactionDTO, TENANT_ID, null);
        accountManagementResponse.setHttpStatusCode(200);
        Mockito.when(accountManagement.deleteUsers(TENANT_ID, useIdList)).thenReturn(accountManagementResponse);
    }

    private void mockCreateUsersAmsResponseForSuccess(List<UserDTO> userDtoList) {
        TransactionDTO transactionDTO = new TransactionDTO(TRANSACTION_ID, TENANT_ID, "CREATED_AT", BulkAccountManagementServiceHelper.JOB_SUCCESS);
        AccountManagementResponse<TransactionDTO> accountManagementResponse = new AccountManagementResponse<>(transactionDTO, TENANT_ID, null);
        accountManagementResponse.setHttpStatusCode(200);
        Mockito.when(accountManagement.createUsers(TENANT_ID, userDtoList)).thenReturn(accountManagementResponse);
    }

    private void mockCreateUsersAmsResponseForError(List<UserDTO> userDtoList) {
        AccountManagementResponse<TransactionDTO> accountManagementResponse = new AccountManagementResponse<>(null, TENANT_ID, AMS_ERROR);
        accountManagementResponse.setHttpStatusCode(401);
        Mockito.when(accountManagement.createUsers(TENANT_ID, userDtoList)).thenReturn(accountManagementResponse);
    }

    private void mockCreateUsersAmsResponseBlankResponse(List<UserDTO> userDtoList) {
        TransactionDTO transactionDTO = new TransactionDTO();
        AccountManagementResponse<TransactionDTO> accountManagementResponse = new AccountManagementResponse<>(transactionDTO, TENANT_ID, null);
        accountManagementResponse.setHttpStatusCode(401);
        Mockito.when(accountManagement.createUsers(TENANT_ID, userDtoList)).thenReturn(accountManagementResponse);
    }

    private void mockUpdateUsersAmsResponseForSuccess(List<UserDTO> userDtoList) {
        TransactionDTO transactionDTO = new TransactionDTO(TRANSACTION_ID, TENANT_ID, "UPDATED_AT", BulkAccountManagementServiceHelper.JOB_SUCCESS);
        AccountManagementResponse<TransactionDTO> accountManagementResponse = new AccountManagementResponse<>(transactionDTO, TENANT_ID, null);
        accountManagementResponse.setHttpStatusCode(200);
        Mockito.when(accountManagement.updateUsers(TENANT_ID, userDtoList)).thenReturn(accountManagementResponse);
    }

    private void mockTransactionStatusAmsResponseForSuccess() {
        TransactionDTO transactionDTO = new TransactionDTO(TRANSACTION_ID, TENANT_ID, "CREATED_AT", BulkAccountManagementServiceHelper.JOB_SUCCESS);
        AccountManagementResponse<TransactionDTO> accountManagementResponse = new AccountManagementResponse<>(transactionDTO, TENANT_ID, null);
        accountManagementResponse.setHttpStatusCode(200);
        Mockito.when(accountManagement.getTransaction(TENANT_ID, TRANSACTION_ID)).thenReturn(accountManagementResponse);
    }

    private void mockTransactionStatusAmsResponseForFailure() {
        TransactionDTO transactionDTO = new TransactionDTO(TRANSACTION_ID, TENANT_ID, "CREATED_AT", BulkAccountManagementServiceHelper.JOB_FAILURE);
        AccountManagementResponse<TransactionDTO> accountManagementResponse = new AccountManagementResponse<>(transactionDTO, TENANT_ID, null);
        accountManagementResponse.setHttpStatusCode(200);
        Mockito.when(accountManagement.getTransaction(TENANT_ID, TRANSACTION_ID)).thenReturn(accountManagementResponse);
    }

    private void mockTransactionStatusAmsResponseForCompletedWithError() {
        TransactionDTO transactionDTO = new TransactionDTO(TRANSACTION_ID, TENANT_ID, "CREATED_AT", BulkAccountManagementServiceHelper.JOB_COMPLETED_WITH_ERRORS);
        AccountManagementResponse<TransactionDTO> accountManagementResponse = new AccountManagementResponse<>(transactionDTO, TENANT_ID, null);
        accountManagementResponse.setHttpStatusCode(200);
        Mockito.when(accountManagement.getTransaction(TENANT_ID, TRANSACTION_ID)).thenReturn(accountManagementResponse);
    }

    private void mockErrorTransactionApiResponseForSuccess() {
        UserFailureListDTO userFailureListDTO = new UserFailureListDTO();
        userFailureListDTO.getUserFailures().add(new UserFailureDTO());
        AccountManagementResponse<UserFailureListDTO> accountManagementResponse = new AccountManagementResponse<>(userFailureListDTO, TENANT_ID, null);
        accountManagementResponse.setHttpStatusCode(200);
        Mockito.when(accountManagement.getTransactionErrors(TENANT_ID, TRANSACTION_ID)).thenReturn(accountManagementResponse);
    }

    private void mockErrorTransactionApiResponseForError() {
        AccountManagementResponse<UserFailureListDTO> accountManagementResponse = new AccountManagementResponse<>(null, TENANT_ID, AMS_ERROR);
        accountManagementResponse.setHttpStatusCode(401);
        Mockito.when(accountManagement.getTransactionErrors(TENANT_ID, TRANSACTION_ID)).thenReturn(accountManagementResponse);
    }

    private void mockErrorTransactionApiResponseForBlankResponse() {
        AccountManagementResponse<UserFailureListDTO> accountManagementResponse = new AccountManagementResponse<>(null, TENANT_ID, null);
        accountManagementResponse.setHttpStatusCode(401);
        Mockito.when(accountManagement.getTransactionErrors(TENANT_ID, TRANSACTION_ID)).thenReturn(accountManagementResponse);
    }

    private void mockTransactionStatusAmsResponseForError() {
        AccountManagementResponse<TransactionDTO> accountManagementResponse = new AccountManagementResponse<>(null, TENANT_ID, AMS_ERROR);
        accountManagementResponse.setHttpStatusCode(401);
        Mockito.when(accountManagement.getTransaction(TENANT_ID, TRANSACTION_ID)).thenReturn(accountManagementResponse);
    }

    private void mockTransactionStatusAmsResponseForBlank() {
        TransactionDTO transactionDTO = new TransactionDTO();
        AccountManagementResponse<TransactionDTO> accountManagementResponse = new AccountManagementResponse<>(transactionDTO, TENANT_ID, null);
        accountManagementResponse.setHttpStatusCode(401);
        Mockito.when(accountManagement.getTransaction(TENANT_ID, TRANSACTION_ID)).thenReturn(accountManagementResponse);
    }

    private UserDTO getUserDto() {
        UserDTO.Builder userDTO = new UserDTO.Builder();

        userDTO.userId(UUID.randomUUID().toString()).build();
        userDTO.userName("testUserName").build();
        userDTO.userPrincipalName("testUserName").build();
        userDTO.givenName("testGivenName").build();
        userDTO.email("<EMAIL>").build();
        userDTO.phoneNumber("+19999999999999999999").build();
        userDTO.password("testPassword").build();
        userDTO.accountStatus(AccountStatus.ACTIVE).build();
        userDTO.mfaPolicyEnabled(true).build();
        userDTO.authenticationMethod(AuthenticationMethod.DIRECT_LOGIN).build();
        userDTO.emailVerified(true).build();
        userDTO.locale("en").build();
        return userDTO.build();
    }
}
