package com.kronos.persons.context.service;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.dataaccess.entity.EmployeeDTO;
import com.kronos.people.personality.dataaccess.service.IPersonReadService;
import com.kronos.persons.context.utils.PeopleConverter;
import com.kronos.persons.rest.beans.LightPersonInfoBean;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.model.LightPersonInformationSearchCriteria;
import com.kronos.persons.rest.model.PersonWhereCriteria;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class LightPersonServiceImplTest {

	@InjectMocks
	private LightPersonServiceImpl service = new LightPersonServiceImpl();

	@Mock
	private IPersonReadService personReadService;

	@Mock
	private PeopleConverter peopleConverter;

	@Mock
	private IKProperties kronosProperties;

	private final String DEFAULT_MAX_COUNT_SIZE = "global.person.lightweight.records.page.size.max";
	private final String DEFAULT_MAXCOUNTSIZE = "10000";
//	private MockedStatic<AccessProfile> mockedAccessProfile;

//	@BeforeEach
//	public void setup() {
//		mockedAccessProfile = Mockito.mockStatic(AccessProfile.class);
//	}

//	@AfterEach
//	public void tearDown() {
//		mockedAccessProfile.close();
//	}

	@Test
	public void testfindLightPersonRecords_pageSizeExceed_throwPageSizeException() {
		try {
			LightPersonInformationSearchCriteria searchCriteria = Mockito
					.mock(LightPersonInformationSearchCriteria.class);
			Mockito.when(searchCriteria.getCount()).thenReturn(-1l);
			Mockito.when(kronosProperties.getProperty(DEFAULT_MAX_COUNT_SIZE, DEFAULT_MAXCOUNTSIZE))
			.thenReturn(DEFAULT_MAXCOUNTSIZE);
			service.findLightPersonRecords(searchCriteria);
			fail("This should not called.");
		} catch (APIException e) {
			assertEquals(ExceptionConstants.INVALID_COUNT, e.getErrorCode());
		}
	}
	@Test
	public void testfindLightPersonRecords_throwPageSizeException() {
		try {
			LightPersonInformationSearchCriteria searchCriteria = Mockito
					.mock(LightPersonInformationSearchCriteria.class);
			Mockito.when(searchCriteria.getCount()).thenReturn(10001L);
			Mockito.when(kronosProperties.getProperty(DEFAULT_MAX_COUNT_SIZE, DEFAULT_MAXCOUNTSIZE))
					.thenReturn(DEFAULT_MAXCOUNTSIZE);
			service.findLightPersonRecords(searchCriteria);
			fail("This should not called.");
		} catch (APIException e) {
			assertEquals("WCO-101338", e.getErrorCode());
		}
	}

	@Test
	public void testfindLightPersonRecords_exception() {
		LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
		PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
		Mockito.when(searchCriteria.getCount()).thenReturn(null);
		Mockito.when(searchCriteria.getIndex()).thenReturn(2l);
		Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
		EmployeeDTO e = Mockito.mock(EmployeeDTO.class);
		List<EmployeeDTO> employeeDTOs = new ArrayList<>();
		employeeDTOs.add(e);
		Mockito.when(personReadService.findLightPersonRecords(Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any())).thenReturn(employeeDTOs);
		Mockito.when(kronosProperties.getProperty(DEFAULT_MAX_COUNT_SIZE, DEFAULT_MAXCOUNTSIZE))
				.thenReturn(DEFAULT_MAXCOUNTSIZE);
		Mockito.when(peopleConverter.convertRestToEntityBeansForfind(employeeDTOs))
				.thenThrow(new NullPointerException());
		try {
			service.findLightPersonRecords(searchCriteria);
			fail();
		} catch (Exception e2) {
			assertTrue(true);
		}
	}

	@Test
	public void testfindLightPersonRecords_apiexception() {
		LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
		PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
		Mockito.when(searchCriteria.getCount()).thenReturn(null);
		Mockito.when(searchCriteria.getIndex()).thenReturn(2l);
		Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
		EmployeeDTO e = Mockito.mock(EmployeeDTO.class);
		List<EmployeeDTO> employeeDTOs = new ArrayList<>();
		employeeDTOs.add(e);
		Mockito.when(personReadService.findLightPersonRecords(Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any())).thenReturn(employeeDTOs);
		Mockito.when(kronosProperties.getProperty(DEFAULT_MAX_COUNT_SIZE, DEFAULT_MAXCOUNTSIZE))
				.thenReturn(DEFAULT_MAXCOUNTSIZE);
		Mockito.when(peopleConverter.convertRestToEntityBeansForfind(employeeDTOs)).thenThrow(new APIException());
		try {
			service.findLightPersonRecords(searchCriteria);
			fail();
		} catch (APIException e2) {
			assertTrue(true);
		}
	}

	@Test
	public void testfindLightPersonRecords_FailedForCount() {
		LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
		PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
		Mockito.when(searchCriteria.getCount()).thenReturn(null);
		Mockito.when(searchCriteria.getIndex()).thenReturn(2l);
		Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
		EmployeeDTO e = Mockito.mock(EmployeeDTO.class);
		List<EmployeeDTO> employeeDTOs = new ArrayList<>();
		employeeDTOs.add(e);
		Mockito.when(personReadService.findLightPersonRecords(Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any())).thenReturn(employeeDTOs);
		Mockito.when(kronosProperties.getProperty(DEFAULT_MAX_COUNT_SIZE, DEFAULT_MAXCOUNTSIZE))
				.thenReturn(DEFAULT_MAXCOUNTSIZE);
		LightPersonInfoBean employeeDT = Mockito.mock(LightPersonInfoBean.class);
		Mockito.when(peopleConverter.convertRestToEntityBeansForfind(employeeDTOs)).thenReturn(employeeDT);
		try {
			employeeDT = service.findLightPersonRecords(searchCriteria);
		} catch (APIException e2) {
			assertTrue(true);
		}
	}

	@Test
	public void testfindLightPersonRecords_FailedForIndex() {
		LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
		PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
		Mockito.when(searchCriteria.getCount()).thenReturn(1L);
		Mockito.when(searchCriteria.getIndex()).thenReturn(-1L);
//		Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);

		EmployeeDTO e = Mockito.mock(EmployeeDTO.class);
		List<EmployeeDTO> employeeDTOs = new ArrayList<>();
		employeeDTOs.add(e);

//		Mockito.when(personReadService.findLightPersonRecords(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
//				.thenReturn(employeeDTOs);
		Mockito.when(kronosProperties.getProperty(DEFAULT_MAX_COUNT_SIZE, DEFAULT_MAXCOUNTSIZE))
				.thenReturn(DEFAULT_MAXCOUNTSIZE);

		LightPersonInfoBean employeeDT = Mockito.mock(LightPersonInfoBean.class);
//		Mockito.when(peopleConverter.convertRestToEntityBeansForfind(employeeDTOs)).thenReturn(employeeDT);

		try {
			employeeDT = service.findLightPersonRecords(searchCriteria);
			fail();
		} catch (APIException e2) {
			assertTrue(true);
		}
	}
	
	@Test
	public void testfindLightPersonRecords() {
		LightPersonInformationSearchCriteria searchCriteria = Mockito.mock(LightPersonInformationSearchCriteria.class);
		PersonWhereCriteria personWhereCriteria = Mockito.mock(PersonWhereCriteria.class);
		Mockito.when(searchCriteria.getCount()).thenReturn(1L);
		Mockito.when(searchCriteria.getIndex()).thenReturn(1L);
		Mockito.when(searchCriteria.getWhere()).thenReturn(personWhereCriteria);
		EmployeeDTO e = Mockito.mock(EmployeeDTO.class);
		List<EmployeeDTO> employeeDTOs = new ArrayList<>();
		employeeDTOs.add(e);
		Mockito.when(personReadService.findLightPersonRecords(Mockito.any(), Mockito.any(), Mockito.any(),
				Mockito.any(), Mockito.any())).thenReturn(employeeDTOs);
		Mockito.when(kronosProperties.getProperty(DEFAULT_MAX_COUNT_SIZE, DEFAULT_MAXCOUNTSIZE))
				.thenReturn(DEFAULT_MAXCOUNTSIZE);
		LightPersonInfoBean employeeDT = Mockito.mock(LightPersonInfoBean.class);
		Mockito.when(peopleConverter.convertRestToEntityBeansForfind(employeeDTOs)).thenReturn(employeeDT);
		employeeDT = service.findLightPersonRecords(searchCriteria);
		assertNotNull(employeeDT);
	}
}
