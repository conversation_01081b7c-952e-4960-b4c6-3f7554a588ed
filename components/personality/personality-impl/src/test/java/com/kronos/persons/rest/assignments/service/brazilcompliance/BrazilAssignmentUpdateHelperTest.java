package com.kronos.persons.rest.assignments.service.brazilcompliance;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.util.PersonalityHelper;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentCompanyDetailRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentCompanyRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentPcaDetailRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentPcaRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentRepTypeDetailRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentRepTypeRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilEmployeeAssignmentsRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.CompanyAttributesRest;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.timekeeping.brazil.compliance.setup.api.model.CompanyDTO;
import com.kronos.timekeeping.brazil.compliance.setup.api.model.PaycodeAttributeDefinitionDTO;
import com.kronos.timekeeping.brazil.compliance.setup.api.service.ICompanyService;
import com.kronos.timekeeping.brazil.compliance.setup.api.service.IPaycodeAttributeDefService;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BRCAssignment;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BRCAssignmentSet;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BRCConfigType;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BRCEmployee;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.company.BRCAssignmentCompany;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.pca.BRCAssignmentPca;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.reptype.BRCAssignmentRepType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentUpdateHelper.ALL_COMPANIES_ID_MAP;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentUpdateHelper.ALL_COMPANIES_QUAL_MAP;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentUpdateHelper.ALL_PAYCODE_ATTRIBUTES_ID_MAP;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentUpdateHelper.ALL_PAYCODE_ATTRIBUTES_QUAL_MAP;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentUpdateHelper.ALL_REP_TYPE_ATTRIBUTES_QUALIFIER_MAP;
import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentUpdateHelper.ALL_REP_TYPE_ID_MAP;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class BrazilAssignmentUpdateHelperTest {

    private final static String CPF = "cpf";
    private final static String ESOCIAL = "eSocial";
    private final static String PIS = "pis";
    private final static Long PERSON_ID = 243L;
    private static final Long OBJECT_ID = 1L;
    private final static Long ATTR_ID_1 = 11L;
    private final static Long ATTR_ID_2 = 22L;
    private final static Long ATTR_ID_3 = 33L;
    private final static String ATTR_NAME_1 = "COMPANY-11";
    private final static String UNION_AGREEMENT_NUMBER = "UAG";
    private final static String PERSON_NUMBER = "20335";
    private static final LocalDateTime EFFECTIVE_DATE = LocalDateTime.now().minusYears(5);

    @InjectMocks
    BrazilAssignmentUpdateHelper brazilAssignmentUpdateHelper;
    @Mock
    BrazilAssignmentBOConverter brazilAssignmentBOConverter;
    @Mock
    ICompanyService companyService;
    @Mock
    private IPaycodeAttributeDefService paycodeAttributeDefService;
    @Mock
    private PersonalityHelper personalityHelper;

    @Test
    public void testUpdateIdentity() {
        /** Update Identity when it has only personId */
        EmployeeExtension ext = new EmployeeExtension();
        ext.setPersonNumber(PERSON_NUMBER);
        ext.setPersonId(PERSON_ID);
        Map<Long, EmployeeExtension> personIdMap = new HashMap<>();
        personIdMap.put(PERSON_ID, ext);
        PersonIdentityBean identity = new PersonIdentityBean();
        identity.setPersonKey(PERSON_ID);
        brazilAssignmentUpdateHelper.enrichPersonIdentities(Arrays.asList(identity), personIdMap, null);
        assertEquals(PERSON_ID, identity.getPersonKey());
        assertEquals(PERSON_NUMBER, identity.getPersonNumber());

        /** Update Identity when it has only person number */
        Map<String, EmployeeExtension> personNumberMap = new HashMap<>();
        personNumberMap.put(PERSON_NUMBER, ext);
        identity = new PersonIdentityBean();
        identity.setPersonNumber(PERSON_NUMBER);
        brazilAssignmentUpdateHelper.enrichPersonIdentities(Arrays.asList(identity), null, personNumberMap);
        assertEquals(PERSON_ID, identity.getPersonKey());
        assertEquals(PERSON_NUMBER, identity.getPersonNumber());
    }

    @Test
    public void testUpdateEmpWithTheDataToBePersisted() {
        KDate effectiveKDate = KDate.createDate();
        LocalDate effectiveDate = LocalDate.now();
        BrazilEmployeeAssignmentsRest employeeRest = createBrazilEmployeeAssignmentsRest(false);
        employeeRest.getCompanyAssignments().getAssignmentDetails().get(0).setEffectiveDate(effectiveDate);
        BRCAssignmentCompany companyMock = new BRCAssignmentCompany(null, new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_1), effectiveKDate, null);
        when(brazilAssignmentBOConverter.convertCompanyBOToPersistenceObj(any(), any())).thenReturn(companyMock);
        employeeRest.getPcaAssignments().getAssignmentDetails().get(0).setEffectiveDate(effectiveDate);
        BRCAssignmentPca pcaMock = new BRCAssignmentPca(null, new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_1), effectiveKDate, null);
        when(brazilAssignmentBOConverter.convertPcaBOToPersistenceObj(any(), any())).thenReturn(pcaMock);
        employeeRest.getRepTypeAssignments().getAssignmentDetails().get(0).setEffectiveDate(effectiveDate);
        BRCAssignmentRepType repTypeMock = new BRCAssignmentRepType(null, new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_1), UNION_AGREEMENT_NUMBER, effectiveKDate, null);
        when(brazilAssignmentBOConverter.convertRepTypeBOToPersistenceObj(any(), any())).thenReturn(repTypeMock);

        BRCEmployee employee = new BRCEmployee(PERSON_ID);
        brazilAssignmentUpdateHelper.updateEmpWithTheDataToBePersisted(employeeRest, employee);
        assertEquals(employeeRest.getCpf(), employee.getCpf());
        assertEquals(employeeRest.getPis(), employee.getPis());
        assertEquals(employeeRest.getEsocial(), employee.getEsocial());
        assertEquals(employeeRest.getCompanyAssignments().getAssignmentDetails().size(), employee.getCompanySet().getAllMembers().size());
        assertEquals(employeeRest.getPcaAssignments().getAssignmentDetails().size(), employee.getPcaSet().getAllMembers().size());
        assertEquals(employeeRest.getRepTypeAssignments().getAssignmentDetails().size(), employee.getPcaSet().getAllMembers().size());
    }

    @Test
    public void testRemovePreExistingAssignmentWithSameEffectiveDate() {
        KDate effectiveDate1 = new KDate().plusMonths(-12);
        KDate effectiveDate2 = new KDate();
        KDate effectiveDate3 = new KDate().plusMonths(12);

        /** no new assignments */
        BRCAssignmentSet assignmentSet = createCompanyAssignmentSet();
        brazilAssignmentUpdateHelper.removePreExistingAssignmentWithSameEffectiveDate(new ArrayList<>(), assignmentSet);
        assertEquals(0, assignmentSet.getDeletedList().size());

        /** one new assignment. One pre-existing assignment. Same effective date */
        BRCAssignment newAssignment = new BRCAssignmentCompany(null, new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_1), effectiveDate1, null);

        List<BRCAssignment> assignmentList = new ArrayList<>();
        assignmentList.add(new BRCAssignmentCompany(new ObjectIdLong(1L), new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_2), effectiveDate1, null));
        BRCAssignmentSet companySet = new BRCAssignmentSet(BRCConfigType.BRC_COMPANY, assignmentList, new ObjectIdLong(PERSON_ID));
        brazilAssignmentUpdateHelper.removePreExistingAssignmentWithSameEffectiveDate(Arrays.asList(newAssignment), companySet);

        assertEquals(1, companySet.getDeletedList().size());
        assertEquals(0, companySet.getAllMembers().size());
        BRCAssignmentCompany deletedAssignment = (BRCAssignmentCompany) companySet.getDeletedList().get(0);
        assertEquals(ATTR_ID_2, deletedAssignment.getCompanyId().get());

        /** Two new assignment. Three preexisting assignments. Two have same effective date */
        List<BRCAssignment> newAssignmentList = new ArrayList<>();
        newAssignmentList.add(new BRCAssignmentCompany(null, new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_1), effectiveDate1, null));
        newAssignmentList.add(new BRCAssignmentCompany(null, new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_2), effectiveDate2, null));

        assignmentList = new ArrayList<>();
        assignmentList.add(new BRCAssignmentCompany(new ObjectIdLong(1L), new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_1), effectiveDate1, null));
        assignmentList.add(new BRCAssignmentCompany(new ObjectIdLong(2L), new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_2), effectiveDate2, null));
        assignmentList.add(new BRCAssignmentCompany(new ObjectIdLong(3L), new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_3), effectiveDate3, null));

        companySet = new BRCAssignmentSet(BRCConfigType.BRC_COMPANY, assignmentList, new ObjectIdLong(PERSON_ID));
        brazilAssignmentUpdateHelper.removePreExistingAssignmentWithSameEffectiveDate(newAssignmentList, companySet);
        assertEquals(2, companySet.getDeletedList().size());
        assertEquals(1, companySet.getAllMembers().size());
        assertEquals(ATTR_ID_3, ((BRCAssignmentCompany) companySet.getAllMembers().get(0)).getCompanyId().get());

        /** One new assignment with an effective date older than the two preexisting assignments.*/
        newAssignmentList = new ArrayList<>();
        newAssignmentList.add(new BRCAssignmentCompany(null, new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_1), effectiveDate1, null));

        assignmentList = new ArrayList<>();
        assignmentList.add(new BRCAssignmentCompany(new ObjectIdLong(2L), new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_2), effectiveDate2, null));
        assignmentList.add(new BRCAssignmentCompany(new ObjectIdLong(3L), new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_3), effectiveDate3, null));

        companySet = new BRCAssignmentSet(BRCConfigType.BRC_COMPANY, assignmentList, new ObjectIdLong(PERSON_ID));
        brazilAssignmentUpdateHelper.removePreExistingAssignmentWithSameEffectiveDate(newAssignmentList, companySet);
        assertEquals(0, companySet.getDeletedList().size());
        assertEquals(2, companySet.getAllMembers().size());
    }

    @Test
    public void testUpdateEmpWithTheDataToBePersistedUnAssignExisting() {
        BRCEmployee employee = new BRCEmployee(PERSON_ID);
        employee.setCompanySet(createCompanyAssignmentSet());
        BrazilEmployeeAssignmentsRest brazilEmployeeAssignmentsRest = new BrazilEmployeeAssignmentsRest();
        brazilEmployeeAssignmentsRest.setCompanyAssignments(new BrazilAssignmentCompanyRest());
        brazilEmployeeAssignmentsRest.getCompanyAssignments().setUnAssignExisting(true);
        brazilAssignmentUpdateHelper.updateEmpWithTheDataToBePersisted(brazilEmployeeAssignmentsRest, employee);
        assertEquals(1L, employee.getCompanySet().getDeletedList().size());
    }

    @Test
    public void testUpdatePiiAttributes() {
        BRCEmployee brcEmployeeMock = Mockito.mock(BRCEmployee.class);
        Mockito.doNothing().when(brcEmployeeMock).setPis(any());
        Mockito.doNothing().when(brcEmployeeMock).setEsocial(any());
        Mockito.doNothing().when(brcEmployeeMock).setCpf(any());
        BrazilEmployeeAssignmentsRest employeeDataRest = createBrazilEmployeeAssignmentsRest(true);
        brazilAssignmentUpdateHelper.updatePiiAttributes(brcEmployeeMock, employeeDataRest);

        verify(brcEmployeeMock, atLeast(1)).setCpf(any());
        verify(brcEmployeeMock, atLeast(1)).setPis(any());
        verify(brcEmployeeMock, atLeast(1)).setEsocial(any());
    }

    @Test
    public void testGetAttributeMap() {
        Collection<CompanyDTO> allCompanies = mockCompanyDTOList();
        Collection<PaycodeAttributeDefinitionDTO> allpaycodeEdits = mockPcaTOList();

        when(companyService.findAllCompanies()).thenReturn(allCompanies);
        when(paycodeAttributeDefService.findAllPaycodeAttributeDefinitions()).thenReturn(allpaycodeEdits);

        Map<String, Map<Object, Object>> attributeMAp = brazilAssignmentUpdateHelper.getAttributeMap();

        Map<Object, Object> companyMapWithIdAsKey = attributeMAp.get(ALL_COMPANIES_ID_MAP);
        assertNotNull(companyMapWithIdAsKey);
        assertEquals(1, companyMapWithIdAsKey.size());
        assertNotNull(companyMapWithIdAsKey.get(ATTR_ID_1));

        Map<Object, Object> companyMapWithQualifierAsKey = attributeMAp.get(ALL_COMPANIES_QUAL_MAP);
        assertNotNull(companyMapWithQualifierAsKey);
        assertEquals(1, companyMapWithQualifierAsKey.size());
        assertNotNull(companyMapWithQualifierAsKey.get(ATTR_NAME_1));

        Map<Object, Object> pcaMapWithIdAsKey = attributeMAp.get(ALL_PAYCODE_ATTRIBUTES_ID_MAP);
        assertNotNull(pcaMapWithIdAsKey);
        assertEquals(1, pcaMapWithIdAsKey.size());
        assertNotNull(pcaMapWithIdAsKey.get(ATTR_ID_1));

        Map<Object, Object> pcaMapWithQualifierAsKey = attributeMAp.get(ALL_PAYCODE_ATTRIBUTES_QUAL_MAP);
        assertNotNull(pcaMapWithQualifierAsKey);
        assertEquals(1, pcaMapWithQualifierAsKey.size());
        assertNotNull(pcaMapWithQualifierAsKey.get(ATTR_NAME_1));

        Map<Object, Object> repTypeMapWithIdAsKey = attributeMAp.get(ALL_REP_TYPE_ID_MAP);
        assertEquals("REP-A", repTypeMapWithIdAsKey.get(1L));
        assertEquals("REP-C", repTypeMapWithIdAsKey.get(2L));
        assertEquals("REP-P", repTypeMapWithIdAsKey.get(3L));

        Map<Object, Object> repTypeMapWithQualifierAsKey = attributeMAp.get(ALL_REP_TYPE_ATTRIBUTES_QUALIFIER_MAP);
        assertEquals(1L, repTypeMapWithQualifierAsKey.get("REP-A"));
        assertEquals(2L, repTypeMapWithQualifierAsKey.get("REP-C"));
        assertEquals(3L, repTypeMapWithQualifierAsKey.get("REP-P"));
    }

    @Test
    public void testGetPersonIdAndEmployeeExtensionMap() {
        PersonIdentityBean identity = new PersonIdentityBean();
        identity.setPersonKey(PERSON_ID);
        List<PersonIdentityBean> personIdentityList = Arrays.asList(identity);

        EmployeeExtension extMock = mock(EmployeeExtension.class);
        when(extMock.getPersonId()).thenReturn(PERSON_ID);
        when(extMock.getPersonNumber()).thenReturn(PERSON_NUMBER);
        List<EmployeeExtension> extList = Arrays.asList(extMock);

        when(personalityHelper.getEmployeeExtensionByBulk(any())).thenReturn(extList);

        Map<Long, EmployeeExtension> identityMap = brazilAssignmentUpdateHelper.getPersonIdAndEmployeeExtensionMap(personIdentityList);
        assertEquals(1, identityMap.size());
        assertEquals(PERSON_NUMBER, identityMap.get(PERSON_ID).getPersonNumber());
    }

    @Test
    public void testGetPersonNumberAndEmployeeExtensionMap() {
        PersonIdentityBean identity = new PersonIdentityBean();
        identity.setPersonNumber(PERSON_NUMBER);
        List<PersonIdentityBean> personIdentityList = Arrays.asList(identity);

        EmployeeExtension extMock = mock(EmployeeExtension.class);
        when(extMock.getPersonId()).thenReturn(PERSON_ID);
        when(extMock.getPersonNumber()).thenReturn(PERSON_NUMBER);
        List<EmployeeExtension> extList = Arrays.asList(extMock);

        when(personalityHelper.getEmployeeExtensionByBulk(any())).thenReturn(extList);

        Map<String, EmployeeExtension> identityMap = brazilAssignmentUpdateHelper.getPersonNumberAndEmployeeExtensionMap(personIdentityList);
        assertEquals(1, identityMap.size());
        assertEquals(PERSON_ID, identityMap.get(PERSON_NUMBER).getPersonId());
    }

    private Collection<CompanyDTO> mockCompanyDTOList() {
        Collection<CompanyDTO> allCompanies = new ArrayList<>();
        CompanyDTO dto = new CompanyDTO();
        dto.setCompanyId(ATTR_ID_1);
        dto.setCompanyName(ATTR_NAME_1);
        allCompanies.add(dto);
        return allCompanies;
    }

    private Collection<PaycodeAttributeDefinitionDTO> mockPcaTOList() {
        Collection<PaycodeAttributeDefinitionDTO> pcaList = new ArrayList<>();
        PaycodeAttributeDefinitionDTO dto = new PaycodeAttributeDefinitionDTO();
        dto.setPaycodeAttributeDefId(ATTR_ID_1);
        dto.setPaycodeAttributeDefName(ATTR_NAME_1);
        pcaList.add(dto);
        return pcaList;
    }

    private BRCAssignmentSet createCompanyAssignmentSet() {
        BRCAssignment assignment = new BRCAssignmentCompany(new ObjectIdLong(OBJECT_ID), new ObjectIdLong(PERSON_ID), new ObjectIdLong(ATTR_ID_1), new KDate(), null);
        List<BRCAssignment> assignmentList = new ArrayList<>();
        assignmentList.add(assignment);
        return new BRCAssignmentSet(BRCConfigType.BRC_COMPANY, assignmentList, new ObjectIdLong(PERSON_ID));
    }

    private BrazilEmployeeAssignmentsRest createBrazilEmployeeAssignmentsRest(boolean piiAttr) {
        PersonIdentityBean identity = new PersonIdentityBean();
        identity.setPersonKey(PERSON_ID);
        BrazilEmployeeAssignmentsRest bo = new BrazilEmployeeAssignmentsRest();
        bo.setPersonIdentity(identity);
        if(piiAttr) {
            bo.setCpf(CPF);
            bo.setEsocial(ESOCIAL);
            bo.setPis(PIS);
        }
        /** Populate Company Data */
        List<BrazilAssignmentCompanyDetailRest> companyAssignmentDetails = new ArrayList<>();
        BrazilAssignmentCompanyDetailRest company = new BrazilAssignmentCompanyDetailRest();
        CompanyAttributesRest companyAttributes = new CompanyAttributesRest();
        companyAttributes.setId(ATTR_ID_1);
        companyAttributes.setQualifier(ATTR_NAME_1);
        company.setCompany(companyAttributes);
        company.setEffectiveDate(EFFECTIVE_DATE.toLocalDate());
        companyAssignmentDetails.add(company);
        BrazilAssignmentCompanyRest companyAssignments = new BrazilAssignmentCompanyRest();
        companyAssignments.setAssignmentDetails(companyAssignmentDetails);
        bo.setCompanyAssignments(companyAssignments);

        // Populate Pca
        List<BrazilAssignmentPcaDetailRest> assignmentDetails = new ArrayList<>();
        BrazilAssignmentPcaDetailRest pca = new BrazilAssignmentPcaDetailRest();
        pca.setPayCodeAttribute(new ObjectRef(ATTR_ID_1));
        pca.setEffectiveDate(EFFECTIVE_DATE.toLocalDate());
        assignmentDetails.add(pca);
        BrazilAssignmentPcaRest pcaAssignments = new BrazilAssignmentPcaRest();
        pcaAssignments.setAssignmentDetails(assignmentDetails);
        bo.setPcaAssignments(pcaAssignments);

        // Populate RepType
        List<BrazilAssignmentRepTypeDetailRest> repTypeAssignmentDetails = new ArrayList<>();
        BrazilAssignmentRepTypeDetailRest repType = new BrazilAssignmentRepTypeDetailRest();
        repType.setRepType(new ObjectRef(ATTR_ID_1));
        repType.setEffectiveDate(EFFECTIVE_DATE.toLocalDate());
        repTypeAssignmentDetails.add(repType);
        BrazilAssignmentRepTypeRest repTypeAssignments = new BrazilAssignmentRepTypeRest();
        repTypeAssignments.setAssignmentDetails(repTypeAssignmentDetails);
        bo.setRepTypeAssignments(repTypeAssignments);
        return bo;
    }
}
