/*******************************************************************************
 * PersonPhotoCriteriaHelperTest.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.utils;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.model.EmployeeCriteria;
import com.kronos.persons.rest.model.PhotoCriteria;
import com.kronos.persons.rest.model.PhotoSearchCriteria;
import com.kronos.persons.rest.model.PhotoSearchWhereCriteria;
import com.kronos.persons.rest.model.SearchValues;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * The class to unit test methods of PersonPhotoCriteriaHelper.
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class PersonPhotoCriteriaHelperTest {

    @InjectMocks
    private PersonPhotoCriteriaHelper personPhotoCriteriaHelperMock;
    private final PhotoCriteria responseSingleKey = new PhotoCriteria("personnumber", Arrays.asList("1234", "1222"), null, null);
    private final PhotoCriteria responseMultiKey = new PhotoCriteria(null, null, Arrays.asList("aoid", "coid"), null);
    private final PhotoCriteria responseMultiKeyWithKeyValues = new PhotoCriteria(null, null, Arrays.asList("aoid", "coid"), Arrays.asList(
            createSearchValues("abc", "def"),
            createSearchValues("wxy", "xyz")
    ));

    @Test
    public void givenSearchCriteriaSingleKey_ThenExpectSingleKeyResponse() {
        try {
            PhotoCriteria response = personPhotoCriteriaHelperMock.getPersonPhotoCriteria(getSearchCriteriaSingleKey());
            assertEquals(responseSingleKey, response);
        } catch (Exception e) {
            fail("Unexpected Exception occurred");
        }
    }

    @Test
    public void givenPhotoCriteriaMultiKey_ThenExpectMultiKeyResponse() {
        try {
            PhotoCriteria response = personPhotoCriteriaHelperMock.getPersonPhotoCriteria(getSearchCriteriaMultiKey());
            assertEquals(responseMultiKey, response);
        } catch (Exception e) {
            fail("Unexpected Exception Occured");
        }

    }

    @Test
    public void givenPhotoSearchCriteria_whenSearchValueFromMultiValueList_thenExpectMultiValueListNotNull() {
        PhotoCriteria response = personPhotoCriteriaHelperMock.getPersonPhotoCriteria(getSearchCriteriaMultiKeyAndValues());
        assertEquals(responseMultiKeyWithKeyValues.getMultiKey(), response.getMultiKey());
        assertEquals(responseMultiKeyWithKeyValues.getMultiKeyValues().get(0).getAoid(), response.getMultiKeyValues().get(0).getAoid());
        assertEquals(responseMultiKeyWithKeyValues.getMultiKeyValues().get(0).getCoid(), response.getMultiKeyValues().get(0).getCoid());
        assertEquals(responseMultiKeyWithKeyValues.getMultiKeyValues().get(1).getAoid(), response.getMultiKeyValues().get(1).getAoid());
        assertEquals(responseMultiKeyWithKeyValues.getMultiKeyValues().get(1).getCoid(), response.getMultiKeyValues().get(1).getCoid());
    }

    @Test
    public void givenPhotoSearchCriteriaWithCoidAoidKeys_whenSearchValueFromMultiValueList_thenExpectMultiValueListNotNull() {
        PhotoCriteria response = personPhotoCriteriaHelperMock.getPersonPhotoCriteria(getSearchCriteriaMultiKeyAndValuesPair());
        assertEquals(responseMultiKeyWithKeyValues.getMultiKeyValues().get(0).getAoid(), response.getMultiKeyValues().get(0).getAoid());
        assertEquals(responseMultiKeyWithKeyValues.getMultiKeyValues().get(0).getCoid(), response.getMultiKeyValues().get(0).getCoid());
        assertEquals(responseMultiKeyWithKeyValues.getMultiKeyValues().get(1).getAoid(), response.getMultiKeyValues().get(1).getAoid());
        assertEquals(responseMultiKeyWithKeyValues.getMultiKeyValues().get(1).getCoid(), response.getMultiKeyValues().get(1).getCoid());
    }

//    @Test(expected = APIException.class)
    @Test
    public void givenPhotoSearchCriteriaMultiKeyWithNullMultiValueList_ThenExpectException() {
//        personPhotoCriteriaHelperMock.getPersonPhotoCriteria(getSearchCriteriaBothKey());
        assertThrows(APIException.class, () -> personPhotoCriteriaHelperMock.getPersonPhotoCriteria(getSearchCriteriaBothKey()));
    }

    @Test
    public void givenPhotoSearchCriteriaWithNullMultiKey_ThenExpectException() {
//        personPhotoCriteriaHelperMock.getPersonPhotoCriteria(getSearchCriteriaNoKey());
        assertThrows(APIException.class, () -> personPhotoCriteriaHelperMock.getPersonPhotoCriteria(getSearchCriteriaNoKey()));
    }

    private PhotoSearchCriteria getSearchCriteriaSingleKey() {
        PhotoSearchCriteria criteria = new PhotoSearchCriteria();
        PhotoSearchWhereCriteria where = new PhotoSearchWhereCriteria();
        EmployeeCriteria employeeCriteria = new EmployeeCriteria();
        employeeCriteria.setKey("personnumber");
        List<String> list = new ArrayList<>();
        list.add("1234");
        list.add("1222");
        employeeCriteria.setValues(list);
        where.setEmployees(employeeCriteria);
        criteria.setWhere(where);
        return criteria;
    }

    private PhotoSearchCriteria getSearchCriteriaMultiKey() {
        PhotoSearchCriteria criteria = new PhotoSearchCriteria();
        PhotoSearchWhereCriteria where = new PhotoSearchWhereCriteria();
        EmployeeCriteria employeeCriteria = new EmployeeCriteria();
        employeeCriteria.setMultiKey(Arrays.asList("aoid", "coid"));
        where.setEmployees(employeeCriteria);
        criteria.setWhere(where);
        return criteria;
    }

    private PhotoSearchCriteria getSearchCriteriaMultiKeyAndValues() {
        PhotoSearchCriteria criteria = new PhotoSearchCriteria();
        PhotoSearchWhereCriteria where = new PhotoSearchWhereCriteria();
        EmployeeCriteria employeeCriteria = new EmployeeCriteria();
        List<String> multiKey = new ArrayList<>();
        List<List<String>> multiKeyValues = new ArrayList<>();
        List<String> mkv = new ArrayList<>();
        List<String> mkv1 = new ArrayList<>();
        multiKey.add("aoid");
        multiKey.add("coid");
        mkv.add("abc");
        mkv.add("def");
        multiKeyValues.add(mkv);
        mkv1.add("wxy");
        mkv1.add("xyz");
        multiKeyValues.add(mkv1);
        employeeCriteria.setMultiKey(multiKey);
        employeeCriteria.setMultiKeyValues(multiKeyValues);
        where.setEmployees(employeeCriteria);
        criteria.setWhere(where);
        return criteria;
    }

    private PhotoSearchCriteria getSearchCriteriaMultiKeyAndValuesPair() {
        PhotoSearchCriteria criteria = new PhotoSearchCriteria();
        PhotoSearchWhereCriteria where = new PhotoSearchWhereCriteria();
        EmployeeCriteria employeeCriteria = new EmployeeCriteria();
        List<String> multiKey = new ArrayList<>();
        List<List<String>> multiKeyValues = new ArrayList<>();
        List<String> mkv = new ArrayList<>();
        List<String> mkv1 = new ArrayList<>();
        multiKey.add("coid");
        multiKey.add("aoid");
        mkv.add("def");
        mkv.add("abc");
        multiKeyValues.add(mkv);
        mkv1.add("xyz");
        mkv1.add("wxy");
        multiKeyValues.add(mkv1);
        employeeCriteria.setMultiKey(multiKey);
        employeeCriteria.setMultiKeyValues(multiKeyValues);
        where.setEmployees(employeeCriteria);
        criteria.setWhere(where);
        return criteria;
    }

    private SearchValues createSearchValues(String aoid, String coid) {
        SearchValues searchValues = new SearchValues();
        searchValues.setAoid(aoid);
        searchValues.setCoid(coid);
        return searchValues;
    }

    private PhotoSearchCriteria getSearchCriteriaBothKey() {
        PhotoSearchCriteria criteria = new PhotoSearchCriteria();
        PhotoSearchWhereCriteria where = new PhotoSearchWhereCriteria();
        EmployeeCriteria employeeCriteria = new EmployeeCriteria();
        employeeCriteria.setMultiKey(Arrays.asList("aoid", "coid"));
        employeeCriteria.setKey("personnumber");
        List<String> list = new ArrayList<>();
        list.add("1234");
        list.add("1222");
        employeeCriteria.setValues(list);
        where.setEmployees(employeeCriteria);
        criteria.setWhere(where);
        return criteria;
    }

    private PhotoSearchCriteria getSearchCriteriaNoKey() {
        PhotoSearchCriteria criteria = new PhotoSearchCriteria();
        PhotoSearchWhereCriteria where = new PhotoSearchWhereCriteria();
        EmployeeCriteria employeeCriteria = new EmployeeCriteria();
        where.setEmployees(employeeCriteria);
        criteria.setWhere(where);
        return criteria;
    }

}
