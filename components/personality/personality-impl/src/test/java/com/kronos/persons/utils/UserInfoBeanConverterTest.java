package com.kronos.persons.utils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import com.kronos.container.api.access.SpringContext;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.entry.ContactEntry;
import com.kronos.people.personality.model.extension.entry.PostalAddressEntry;
import com.kronos.people.personality.service.PersonalityService;
import com.kronos.persons.context.service.CurrentUser;
import com.kronos.persons.rest.beans.extensions.ContactDataExtension;
import com.kronos.persons.rest.beans.extensions.PostalAddressDataExtension;
import com.kronos.persons.rest.model.UserInfoBean;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.types.business.EMailAddressType;
import com.kronos.wfc.commonapp.types.business.PostalAddressType;
import com.kronos.wfc.commonapp.types.business.TelephoneNumberType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class UserInfoBeanConverterTest {

	@InjectMocks
	private UserInfoBeanConverter userInfoBeanConverter;

	@Mock
	private Personality personality;

	private MockedStatic<SpringContext> mockedSpringContext;
	private MockedStatic<EMailAddressType> mockedEMailAddressType;
	private MockedStatic<TelephoneNumberType> mockedTelephoneNumberType;
	private MockedStatic<PostalAddressType> mockedPostalAddressType;

	@Mock
	private CurrentUser currentUser;

	@Mock
	private CurrentUserContactDetailsPopulator currentUserContactDetailsPopulator;

	@Mock
	private PersonalityService personalityService;

	@Mock
	private EMailAddressType eMailAddressType;

	@Mock
	private TelephoneNumberType telephoneNumberType;

	@Mock
	private PostalAddressType postalAddressType;

	@BeforeEach
	public void setUp() {
		mockedSpringContext = Mockito.mockStatic(SpringContext.class);
		mockedEMailAddressType = Mockito.mockStatic(EMailAddressType.class);
		mockedTelephoneNumberType = Mockito.mockStatic(TelephoneNumberType.class);
		mockedPostalAddressType = Mockito.mockStatic(PostalAddressType.class);
	}

	@AfterEach
	public void tearDown() {
		mockedSpringContext.close();
		mockedEMailAddressType.close();
		mockedTelephoneNumberType.close();
		mockedPostalAddressType.close();
	}

	@Test
	public void createCurrentUserDetailsBeanTrueTest() {
		mockedSpringContext.when(() -> SpringContext.getBean(PersonalityService.class)).thenReturn(personalityService);
		Mockito.doReturn(getPersonalityResponse()).when(personalityService).findEmployeeExtension(0L);
		mockedSpringContext.when(() -> SpringContext.getBean(CurrentUserContactDetailsPopulator.class))
				.thenReturn(new CurrentUserContactDetailsPopulator());

		mockedEMailAddressType.when(() -> EMailAddressType.getEMailAddressType(new ObjectIdLong(0))).thenReturn(eMailAddressType);

		mockedEMailAddressType.when(() -> EMailAddressType.getEMailAddressType(new ObjectIdLong(0))).thenReturn(eMailAddressType);

		mockedTelephoneNumberType.when(() -> TelephoneNumberType.getTelephoneNumberType(new ObjectIdLong(0)))
				.thenReturn(telephoneNumberType);

		mockedPostalAddressType.when(() -> PostalAddressType.getPostalAddressType(new ObjectIdLong(0))).thenReturn(postalAddressType);
		UserInfoBean actual = userInfoBeanConverter.createCurrentUserDetailsBean(personality, currentUser, true);
		UserInfoBean expected = getExpectedCurrentUserInfoBean();
		assertEquals(expected, actual);
		ArrayList<PostalAddressDataExtension> actualPostalAddress = (ArrayList<PostalAddressDataExtension>) actual
				.getPostalAddresses();
		ArrayList<PostalAddressDataExtension> expectedPostalAddress = (ArrayList<PostalAddressDataExtension>) expected
				.getPostalAddresses();
		assertEquals(expectedPostalAddress.get(0).getCity(), actualPostalAddress.get(0).getCity());
		assertEquals(expectedPostalAddress.get(0).getZipCode(), actualPostalAddress.get(0).getZipCode());
		assertNotNull(actual.getPostalAddresses());
		assertNotNull(actual.getTelephoneContacts());
		assertNotNull(actual.getEmailContacts());

	}

	@Test
	public void createCurrentUserDetailsBeanFalseTest() {
		mockedSpringContext.when(() -> SpringContext.getBean(PersonalityService.class)).thenReturn(personalityService);
		mockedSpringContext.when(() -> SpringContext.getBean(CurrentUserContactDetailsPopulator.class)).thenReturn(currentUserContactDetailsPopulator);
		UserInfoBean bean = new UserInfoBean();
		bean.setEmployeeId(0L);
		UserInfoBean actual = userInfoBeanConverter.createCurrentUserDetailsBean(personality, currentUser, false);
		assertNull(actual.getEmailContacts());

	}

	private UserInfoBean getExpectedCurrentUserInfoBean() {
		Collection<ContactDataExtension> emailContacts = getEmailContactDataExtension();
		Collection<ContactDataExtension> telephoneContacts = getTelephoneContactDataExtension();
		Collection<PostalAddressDataExtension> postalAddresses = getPostalAddressDataExtension();
		UserInfoBean bean = new UserInfoBean();
		bean.setEmployeeId(0L);
		bean.setEmailContacts(emailContacts);
		bean.setTelephoneContacts(telephoneContacts);
		bean.setPostalAddresses(postalAddresses);
		return bean;
	}

	private Collection<ContactDataExtension> getEmailContactDataExtension() {
		List<ContactDataExtension> emailContactDataExtensions = new ArrayList<>();
		ContactDataExtension emailContactDataExtension = new ContactDataExtension();
		emailContactDataExtension.setContactData("abc@test");
		emailContactDataExtension.setContactType("email");
		emailContactDataExtension.setContactTypeId(0L);
		emailContactDataExtension.setHasEmailNotificationDelivery(true);
		emailContactDataExtension.setSMSSwitch(false);
		emailContactDataExtensions.add(emailContactDataExtension);
		return emailContactDataExtensions;
	}

	private Collection<ContactDataExtension> getTelephoneContactDataExtension() {
		List<ContactDataExtension> telephoneContactDataExtensions = new ArrayList<>();
		ContactDataExtension telephoneContactDataExtension = new ContactDataExtension();
		telephoneContactDataExtension.setContactData("976543210");
		telephoneContactDataExtension.setContactType("Phone 1");
		telephoneContactDataExtension.setContactTypeId(0L);
		telephoneContactDataExtension.setHasEmailNotificationDelivery(false);
		telephoneContactDataExtension.setSMSSwitch(true);
		telephoneContactDataExtensions.add(telephoneContactDataExtension);
		return telephoneContactDataExtensions;
	}

	private Collection<PostalAddressDataExtension> getPostalAddressDataExtension() {
		List<PostalAddressDataExtension> postalAddressDataExtensions = new ArrayList<>();
		PostalAddressDataExtension postalAddressDataExtension = new PostalAddressDataExtension();
		postalAddressDataExtension.setCity("New Delhi");
		postalAddressDataExtension.setState("Delhi");
		postalAddressDataExtension.setContactTypeId(0L);
		postalAddressDataExtension.setCountry("India");
		postalAddressDataExtension.setStreet("ABC Street");
		postalAddressDataExtension.setZipCode("12345");
		postalAddressDataExtensions.add(postalAddressDataExtension);
		return postalAddressDataExtensions;
	}

	private PersonalityResponse<EmployeeExtension> getPersonalityResponse() {
		PersonalityResponse<EmployeeExtension> personalityResponse = new PersonalityResponse<>();
		EmployeeExtension employeeExtension = new EmployeeExtension();
		Collection<ContactEntry> contactEntries = new ArrayList<>();
		Collection<PostalAddressEntry> postalAddressEntries = new ArrayList<>();
		PostalAddressEntry postalAddressEntry = new PostalAddressEntry();
		postalAddressEntry.setCity("New Delhi");
		postalAddressEntry.setState("Delhi");
		postalAddressEntry.setContactTypeId(0L);
		postalAddressEntry.setCountry("India");
		postalAddressEntry.setStreet("ABC Street");
		postalAddressEntry.setZipCode("12345");
		postalAddressEntries.add(postalAddressEntry);
		contactEntries.add(new ContactEntry(0L, "abc@test", 0L, true));
		employeeExtension.setEmailContactEntries(contactEntries);
		employeeExtension.setTelContactEntries(contactEntries);
		employeeExtension.setPostalAddressEntries(postalAddressEntries);
		personalityResponse.setextension(employeeExtension);
		return personalityResponse;
	}
}
