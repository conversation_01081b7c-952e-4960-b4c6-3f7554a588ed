/**
 * 
 */
package com.kronos.persons.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import static org.junit.jupiter.api.Assertions.assertEquals;


/**
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
public class DateUtilsTest {

	@Test
	public void test_getStartingOfTheDay() {
		assertEquals(LocalDateTime.of(LocalDate.now(), LocalTime.MIN), DateUtils.getStartingOfTheDay(LocalDate.now()));
	}

	@Test
	public void test_getMidNight() {
		assertEquals(LocalDateTime.of(LocalDate.now(), LocalTime.MAX), DateUtils.getMidNight(LocalDate.now()));
	}

}
