package com.kronos.persons.rest.assignments.service.percentageallocationrule;

import com.kronos.container.api.access.SpringContext;
import com.kronos.people.personality.service.PersonalityService;
import com.kronos.persons.rest.assignments.model.percentageallocationrule.PercentageAllocationRuleAssignmentBean;
import com.kronos.persons.rest.assignments.service.adjustmentrule.ExtensibilityUtil;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignment;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.totalizing.business.extensibility.ProcessorToEmployee;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class PercentageAllocationRuleAssignmentServiceTest {
   @InjectMocks
   PercentageAllocationRuleAssignmentService percentageAllocationRuleAssignmentService;

   @Mock
   protected ValidatorUtils validatorUtils;

   @Mock
   protected PersonIdentityBeanValidator personIdentityBeanValidator;

   @Mock
   protected PersonalityService personalityService;

   private MockedStatic<Personality> mockedPersonality;
   private MockedStatic<ProcessorToEmployee> mockedProcessorToEmployee;
   private MockedStatic<SpringContext> mockedSpringContext;
   private MockedStatic<ExtensibilityUtil> mockedExtensibilityUtil;

   @BeforeEach
   public void setup(){
      mockedPersonality = Mockito.mockStatic(Personality.class);
      mockedProcessorToEmployee = Mockito.mockStatic(ProcessorToEmployee.class);
      mockedSpringContext = Mockito.mockStatic(SpringContext.class);
      Mockito.when(SpringContext.getBean(PersonIdentityBeanValidator.class)).thenReturn(null);
      mockedExtensibilityUtil = Mockito.mockStatic(ExtensibilityUtil.class);
      JobAssignment ja = Mockito.mock(JobAssignment.class);
      Personality personality = Mockito.mock(Personality.class);
      Mockito.when(Personality.getByPersonId(Mockito.any(ObjectIdLong.class))).thenReturn(personality);
      Mockito.when(personality.getJobAssignment()).thenReturn(ja);
   }

   @AfterEach
    public void tearDown(){
        mockedPersonality.close();
        mockedProcessorToEmployee.close();
        mockedSpringContext.close();
        mockedExtensibilityUtil.close();
    }

   @Test
   public void testGetPercentageAllocationRulesByPersonNum(){
      Mockito.when(ProcessorToEmployee.getByJobAssignmentByPassCache(Mockito.any(JobAssignment.class))).thenReturn(null);
      PersonIdentityBean identityBean = new PersonIdentityBean();
      identityBean.setPersonKey(1L);
      List<PercentageAllocationRuleAssignmentBean> list = percentageAllocationRuleAssignmentService.getPercentageAllocationRulesByPersonNum(identityBean, true);
      assertEquals(1, list.size());
      assertEquals(identityBean, list.get(0).getPersonIdentity());
      list = percentageAllocationRuleAssignmentService.getPercentageAllocationRulesByPersonNum(identityBean, false);
      assertEquals(0, list.size());
   }
}
