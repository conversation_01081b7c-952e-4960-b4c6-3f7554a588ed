package com.kronos.persons.rest.assignments.service.adjustmentrule;

import com.kronos.container.api.access.SpringContext;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.totalizing.business.extensibility.TotalizerExtensibilityException;
import com.kronos.wfc.totalizing.business.extensibility.entity.EffectiveDated;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.lang.reflect.Constructor;
import java.lang.reflect.Modifier;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class ExtensibilityUtilTest {

    @Mock
    PersonIdentityBeanValidator personIdentityBeanValidator;

    ObjectIdLong personId = new ObjectIdLong(1L);

    private MockedStatic<SpringContext> springContextMockedStatic;
    private MockedStatic<Personality> personalityMockedStatic;

    @BeforeEach
    public void setup() {
        springContextMockedStatic = Mockito.mockStatic(SpringContext.class);
        springContextMockedStatic.when(() -> SpringContext.getBean(PersonIdentityBeanValidator.class)).thenReturn(personIdentityBeanValidator);
        personalityMockedStatic = Mockito.mockStatic(Personality.class);
        Personality personality = Mockito.mock(Personality.class);
        personalityMockedStatic.when(() -> Personality.getByPersonId(personId)).thenReturn(personality);
        Mockito.when(personality.getPersonId()).thenReturn(personId);
        Mockito.when(personality.getUserAccountId()).thenReturn(new ObjectIdLong(2L));
        Mockito.when(personality.getCurrentBadgeNumber()).thenReturn("111");
        Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any())).thenReturn(personality);
    }

    @AfterEach
    public void tearDown() {
        springContextMockedStatic.close();
        personalityMockedStatic.close();
    }

    @Disabled
    @Test
    public void testGetPersonId_givenPersonIdentityBean_expectId() {
        PersonIdentityBean personIdentity = new PersonIdentityBean();
        ObjectIdLong id = ExtensibilityUtil.getPersonId(personIdentity);
        assertEquals(personId, id);
    }

    @Test
    public void createPersonIdentityBean() {
        PersonIdentityBean bean = ExtensibilityUtil.createPersonIdentityBean(personId);
        assertEquals(String.valueOf(personId.longValue()), bean.getPersonKey().toString());
    }

    @Test
    public void createPersonIdentityBeanWithBadgeNumber() {
        PersonIdentityBean bean = ExtensibilityUtil.createPersonIdentityBean(personId);
        assertEquals(String.valueOf(personId.longValue()), bean.getPersonKey().toString());
        assertEquals("111", bean.getBadgeNumber());
        assertEquals("2", bean.getUserKey().toString());
    }

    @Test
    public void validateEffectiveDateException() {
        assertThrows(TotalizerExtensibilityException.class, () -> {
            EffectiveDatedEntry entity = new EffectiveDatedEntry();
            LocalDate date = LocalDate.now().minusDays(10);
            entity.setExpiraytionDate(KDate.createDate(date.getYear(), date.getMonthValue(), date.getDayOfMonth()));
            ExtensibilityUtil.validateEffectiveDate(entity);
        });
    }

    @Test
    public void validateEffectiveDate() {
        EffectiveDated entity = new EffectiveDatedEntry();
        ExtensibilityUtil.validateEffectiveDate(entity);
    }

    @Test
    public void testConstructorIsPrivate() {
        try {
            Constructor<ExtensibilityUtil> constructor;
            constructor = ExtensibilityUtil.class.getDeclaredConstructor();
            assertTrue(Modifier.isPrivate(constructor.getModifiers()));
            constructor.setAccessible(true);
            constructor.newInstance();
        } catch (Exception e) {
            fail();
        }
    }

    @Disabled
    @Test
    public void testInitializePersonIdentityBean_withNonNullPersonality_expectPersonalityObject() {
        PersonIdentityBean personIdentity = ExtensibilityUtil.createPersonIdentityBean(personId);

        Personality result = ExtensibilityUtil.initializePersonIdentityBean(personIdentity);
        Assertions.assertEquals(personId, result.getPersonId());
        Assertions.assertEquals("111", result.getCurrentBadgeNumber());
    }

    private static class EffectiveDatedEntry implements EffectiveDated {
        KDate expirationDate = KDate.getEotDate();

        public void setExpiraytionDate(KDate extDate) {
            expirationDate = extDate;
        }
        public KDate getExpirationDate() {
            return expirationDate;
        }

        @Override
        public KDate getEffectiveDate() {
            return KDate.createDate();
        }
    }
}
