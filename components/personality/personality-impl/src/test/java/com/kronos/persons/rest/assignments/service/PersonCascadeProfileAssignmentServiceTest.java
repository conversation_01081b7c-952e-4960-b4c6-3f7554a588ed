/*******************************************************************************
 * LicenseValidatorTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.rest.assignments.service;

import com.google.common.collect.Lists;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.AssignmentProfileRequestBean;
import com.kronos.persons.rest.assignments.model.AssignmentProfileRequestBeanWrapper;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.persons.rest.model.BeanWrapper;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonNotification;
import com.kronos.wfc.commonapp.types.business.NotificationJMSTypeCache;
import com.kronos.wfc.commonapp.types.business.NotificationTypeCache;
import com.kronos.wfc.platform.caching.framework.notifiers.CacheNotificationService;
import com.kronos.wfc.platform.entity.business.AbstractEntity;
import com.kronos.wfc.platform.notification.framework.NotificationService;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.timekeeping.cascade.business.config.CascadeProfile;
import com.kronos.wfc.timekeeping.cascade.business.people.PersonCascadeProfileAssignment;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Verifies {@link PersonCascadeProfileAssignmentService}.
 * Copyright (C) 2019 Kronos.com
 * Date: Jul 04, 2019
 * <AUTHOR> Kuchynski
 *
 * <AUTHOR> Castiblanco
 * Date: Mar 04, 2024
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonCascadeProfileAssignmentServiceTest {
    private static final String VALID_PROFILE_NAME = "Valid Cascade Profile";
    private static final String FAIL_PROFILE_NAME = "Fail Cascade Profile";
    private static final String LEADING_TRAILING_SPACES_IN_PROFILE_NAME = " Valid Cascade Profile ";
    private static final String TEST_ERROR_CODE = "TEST_CODE_00000";
    private static final ObjectIdLong PERSON_ID = new ObjectIdLong(1L);
    private static final ObjectIdLong PROFILE_ID = new ObjectIdLong(1L);
    private static final String PROFILE_NAME = "PROFILE_NAME";

    @Mock
    private PersonIdentityBeanValidator personIdentityBeanValidator;

    @Mock
    private PersonIdentityBean personIdentity;

    @Mock
    private Personality personality;

    @Mock
    private PersonCascadeProfileAssignment cascadeProfileAssignment;

    @Mock
    private AbstractEntity wrappedObject; // Renamed to match the field in the class under test

    @Mock
    private ValidatorUtils validatorUtils;

    @InjectMocks
    private PersonCascadeProfileAssignmentService service;

    private MockedStatic<ResponseHandler> mockedResponseHandler;
    private MockedStatic<PrsnValidationException> mockedPrsnValidationException;
    private MockedStatic<PersonCascadeProfileAssignment> mockedPersonCascadeProfileAssignment;
    private MockedStatic<CascadeProfile> mockedCascadeProfile;
    private MockedStatic<PersonCascadeProfileBulkUpdateTransactionService> mockedPersonCascadeProfileBulkUpdateTransactionService;
    private MockedStatic<Personality> mockedPersonality;
    private MockedStatic<AbstractEntity> mockedAbstractEntity;
    private MockedStatic<PersonNotification> mockedPersonNotification;

    @BeforeEach
    public void setUp() {
        mockedResponseHandler = Mockito.mockStatic(ResponseHandler.class);
        mockedPrsnValidationException = mockStatic(PrsnValidationException.class);
        mockedPersonCascadeProfileAssignment = mockStatic(PersonCascadeProfileAssignment.class);
        mockedCascadeProfile = mockStatic(CascadeProfile.class);
        mockedPersonCascadeProfileBulkUpdateTransactionService = mockStatic(PersonCascadeProfileBulkUpdateTransactionService.class);
        mockedPersonality = mockStatic(Personality.class);
        mockedAbstractEntity = mockStatic(AbstractEntity.class);
        mockedPersonNotification = mockStatic(PersonNotification.class);
    }

    @AfterEach
    public void tearDown() {
        mockedResponseHandler.close();
        mockedPrsnValidationException.close();
        mockedPersonCascadeProfileAssignment.close();
        mockedCascadeProfile.close();
        mockedPersonCascadeProfileBulkUpdateTransactionService.close();
        mockedPersonality.close();
        mockedAbstractEntity.close();
        mockedPersonNotification.close();
    }

    @Test
    public void testValidateValidBean() {
        AssignmentProfileRequestBean bean = prepareBean();
        BeanWrapper<AssignmentProfileRequestBean> beanWrapper = new AssignmentProfileRequestBeanWrapper(bean);
        doNothing().when(personIdentityBeanValidator).newvalidate(null, personIdentity, null);
        when(personIdentityBeanValidator.getPersonality(personIdentity)).thenReturn(personality);
        Mockito.when(ResponseHandler.apiSuccessResponse(bean, personality)).thenReturn(bean);
        service.validate(beanWrapper);
        verify(personIdentityBeanValidator, Mockito.times(NumberUtils.INTEGER_ONE))
                .newvalidate(null, personIdentity, null);
        assertNull(beanWrapper.getApiException());
    }

    @Test
    public void testValidateBeanWithInvalidPersonIdentity() {
        APIException expectedException = new APIException(TEST_ERROR_CODE);
        AssignmentProfileRequestBean bean = prepareBean();
        BeanWrapper<AssignmentProfileRequestBean> beanWrapper = new AssignmentProfileRequestBeanWrapper(bean);
        doThrow(expectedException).when(personIdentityBeanValidator).newvalidate(null, personIdentity, null);
        service.validate(beanWrapper);
        assertEquals(expectedException, beanWrapper.getApiException());
    }

    @Test
    public void testValidateBeanWithEmptyProfileName() {
        APIException expectedException = new APIException(TEST_ERROR_CODE);
        AssignmentProfileRequestBean bean = new AssignmentProfileRequestBean();
        bean.setPersonIdentity(personIdentity);
        BeanWrapper<AssignmentProfileRequestBean> beanWrapper = new AssignmentProfileRequestBeanWrapper(bean);
        Mockito.when(PrsnValidationException.missingProperty(Mockito.anyString()))
                .thenReturn(expectedException);
        service.validate(beanWrapper);
        assertEquals(expectedException, beanWrapper.getApiException());
    }

    @Test
    public void testValidateBeanWithLeadingOrTrailingSpacesInProfileName() {
        AssignmentProfileRequestBean bean = new AssignmentProfileRequestBean();
        bean.setPersonIdentity(personIdentity);
        bean.setAssignmentProfile(LEADING_TRAILING_SPACES_IN_PROFILE_NAME);
        BeanWrapper<AssignmentProfileRequestBean> beanWrapper = new AssignmentProfileRequestBeanWrapper(bean);
        doNothing().when(personIdentityBeanValidator).newvalidate(null, personIdentity, null);
        when(personIdentityBeanValidator.getPersonality(personIdentity)).thenReturn(personality);
        Mockito.when(ResponseHandler.apiSuccessResponse(bean, personality)).thenReturn(bean);
        service.validate(beanWrapper);
        verify(personIdentityBeanValidator, Mockito.times(NumberUtils.INTEGER_ONE))
                .newvalidate(null, personIdentity, null);
        assertNull(beanWrapper.getApiException());
    }

    @Test
    public void testMultiUpdateSuccess() throws Exception {
        PersonCascadeProfileBulkUpdateTransactionService transactionService =
                mock(PersonCascadeProfileBulkUpdateTransactionService.class);
        CascadeProfile cascadeProfile = mock(CascadeProfile.class);
        AssignmentProfileRequestBean bean = prepareBean();
        AssignmentProfileRequestBeanWrapper beanWrapper = new AssignmentProfileRequestBeanWrapper(bean);
        List<AssignmentProfileRequestBeanWrapper> beanWrappers = Lists.newArrayList(beanWrapper);
        Mockito.mockStatic(NotificationTypeCache.class);
        Mockito.mockStatic(NotificationJMSTypeCache.class);
        Mockito.mockStatic(NotificationService.class);
        Mockito.mockStatic(CacheNotificationService.class);
        when(personIdentityBeanValidator.getPersonality(personIdentity)).thenReturn(personality);
        when(personality.getPersonId()).thenReturn(PERSON_ID);
        Mockito.when(CascadeProfile.retrieveByName(VALID_PROFILE_NAME)).thenReturn(cascadeProfile);
        Mockito.when(PersonCascadeProfileAssignment.retrieveCascadeProfileByPersonId(PERSON_ID))
                .thenReturn(cascadeProfileAssignment);
        Mockito.when(PersonCascadeProfileBulkUpdateTransactionService
                .getTransactionService(cascadeProfile, Lists.newArrayList(cascadeProfileAssignment)))
                .thenReturn(transactionService);
        service.multiUpdate(beanWrappers);
    }

    @Test
    public void testMultiUpdatePartiallySuccessful () throws Exception {
        APIException expectedException = new APIException(TEST_ERROR_CODE);
        PersonCascadeProfileBulkUpdateTransactionService transactionService =
                mock(PersonCascadeProfileBulkUpdateTransactionService.class);
        Mockito.when(PrsnValidationException.invalidPropertyValue(anyString(),anyString()))
                .thenReturn(expectedException);
        CascadeProfile cascadeProfile = mock(CascadeProfile.class);
        AssignmentProfileRequestBean successBean = prepareBean();
        AssignmentProfileRequestBean failBean = prepareFailBean();
        AssignmentProfileRequestBeanWrapper successWrapper = new AssignmentProfileRequestBeanWrapper(successBean);
        AssignmentProfileRequestBeanWrapper failWrapper = new AssignmentProfileRequestBeanWrapper(failBean);
        List<AssignmentProfileRequestBeanWrapper> beanWrappers = Lists.newArrayList(
                successWrapper, failWrapper);

        Mockito.when(CascadeProfile.retrieveByName(VALID_PROFILE_NAME)).thenReturn(cascadeProfile);
        Mockito.when(CascadeProfile.retrieveByName(FAIL_PROFILE_NAME)).thenReturn(null);
        when(personIdentityBeanValidator.getPersonality(personIdentity)).thenReturn(personality);
        when(personality.getPersonId()).thenReturn(PERSON_ID);
        Mockito.when(PersonCascadeProfileAssignment.retrieveCascadeProfileByPersonId(PERSON_ID))
                .thenReturn(cascadeProfileAssignment);
        Mockito.when(PersonCascadeProfileBulkUpdateTransactionService
                .getTransactionService(cascadeProfile, Lists.newArrayList(cascadeProfileAssignment),false))
                .thenReturn(transactionService);
        doNothing().when(transactionService).run();


        service.multiUpdate(beanWrappers);
        assertEquals(expectedException.getErrorCode(), failWrapper.getApiException().getErrorCode());
        assertNull(successWrapper.getApiException());
        verify(personIdentityBeanValidator, times(1)).getPersonality(personIdentity);
        verify(transactionService, times(1)).run();
    }

    @Test
    public void givenAssignmentProfileRequestBeanAndPersonalityIsNull_whenUpdateRequest_thenReturnPersonality() {
        AssignmentProfileRequestBean requestBean = prepareBean();
        requestBean.setPersonIdentity(null);
        APIException expectedException = new APIException(ExceptionConstants.MISSING_PROPERTY);
        Mockito.when(PrsnValidationException.missingProperty(anyString()))
                .thenThrow(expectedException);
        APIException thrown = assertThrows(APIException.class, () -> {
            service.updateRequest(requestBean);
        });

        assertEquals(ExceptionConstants.MISSING_PROPERTY, thrown.getErrorCode());
    }

    @Test
    public void givenAssignmentProfileRequestBeanAndPersonalityIsNull_whenDeleteRequest_thenReturnPersonality() {
        AssignmentProfileRequestBean requestBean = prepareBean();
        requestBean.setPersonIdentity(null);
        APIException expectedException = new APIException(ExceptionConstants.MISSING_PROPERTY);
        Mockito.when(PrsnValidationException.missingProperty(anyString()))
                .thenThrow(expectedException);
        APIException thrown = assertThrows(APIException.class, () -> {
            service.deleteRequest(requestBean);
        });
        assertEquals(ExceptionConstants.MISSING_PROPERTY, thrown.getErrorCode());
    }

    @Test
    public void givenAssignmentProfileRequestBean_whenDeleteRequest_thenReturnPersonality() {
        CascadeProfile cascadeProfile = mock(CascadeProfile.class);
        AssignmentProfileRequestBean requestBean = prepareBean();
        doNothing().when(personIdentityBeanValidator)
                .newvalidate(null, personIdentity, null);
        when(personIdentityBeanValidator
                .getPersonality(personIdentity)).thenReturn(personality);
        Mockito.when(CascadeProfile.retrieveByName(VALID_PROFILE_NAME)).thenReturn(cascadeProfile);

        when(CascadeProfile.retrieveById(PROFILE_ID)).thenReturn(cascadeProfile);
        Mockito.when(PersonCascadeProfileAssignment.retrieveCascadeProfileByPersonId(Mockito.any()))
                .thenReturn(cascadeProfileAssignment);

        Personality result = service.deleteRequest(requestBean);
        assertNotNull(result);
    }

    @Test
    public void givenAssignmentProfileRequestBean_whenUpdateRequest_thenReturnPersonality() {
        CascadeProfile cascadeProfile = mock(CascadeProfile.class);
        AssignmentProfileRequestBean requestBean = prepareBean();
        doNothing().when(personIdentityBeanValidator)
                .newvalidate(null, personIdentity, null);
        when(personIdentityBeanValidator.getPersonality(personIdentity))
                .thenReturn(personality);
        Mockito.when(CascadeProfile.retrieveByName(VALID_PROFILE_NAME)).thenReturn(cascadeProfile);

        when(CascadeProfile.retrieveById(PROFILE_ID)).thenReturn(cascadeProfile);
        Mockito.when(PersonCascadeProfileAssignment.retrieveCascadeProfileByPersonId(Mockito.any()))
                .thenReturn(cascadeProfileAssignment);

        Personality result = service.updateRequest(requestBean);
        assertNotNull(result);
    }

    @Test
    public void givenPersonIdentityBean_whenRetrieve_thenReturnAssignmentProfileRequestBean() {
        CascadeProfile cascadeProfile = mock(CascadeProfile.class);
        AssignmentProfileRequestBean bean = prepareBean();
        doNothing().when(personIdentityBeanValidator).newvalidate(null, personIdentity, null);
        when(personIdentityBeanValidator.getPersonality(personIdentity)).thenReturn(personality);

        when(cascadeProfile.getName()).thenReturn(PROFILE_NAME);
        when(CascadeProfile.retrieveById(PROFILE_ID)).thenReturn(cascadeProfile);
        Mockito.when(PersonCascadeProfileAssignment.retrieveCascadeProfileByPersonId(Mockito.any()))
                .thenReturn(cascadeProfileAssignment);
        when(cascadeProfileAssignment.getProfileId()).thenReturn(PROFILE_ID);
        Mockito.when(ResponseHandler.apiSuccessResponse(bean, personality)).thenReturn(bean);

        AssignmentProfileRequestBean result = service.retrieve(personIdentity);
        verify(personIdentityBeanValidator)
                .newvalidate(null, personIdentity, null);
        verify(personIdentityBeanValidator)
                .getPersonality(personIdentity);
        assertNotNull(result);
    }

    @Test
    public void  givenPersonIdentityBeanIsNull_whenRetrieve_thenThrowAnException() throws Exception {
        APIException expectedException = new APIException(ExceptionConstants.MISSING_PROPERTY);
        Mockito.when(PrsnValidationException.missingProperty(anyString()))
                .thenThrow(expectedException);

        APIException thrown = assertThrows(APIException.class, () -> {
            service.retrieve(null);
        });
        assertEquals(ExceptionConstants.MISSING_PROPERTY, thrown.getErrorCode());

    }

    @Test
    public void givenInvalidPersonNumber_whenRetrieveByPersonNumber_thenThrowAnException() throws Exception {
        APIException expectedException = new APIException(ExceptionConstants.INVALID_PROPERTY_VALUE);
        Mockito.when(PrsnValidationException.invalidPropertyValue(anyString(), anyString()))
                .thenThrow(expectedException);
        PersonIdentityBean personIdentityBean = mock(PersonIdentityBean.class);
        when(personIdentityBeanValidator.getPersonByPersonNumber(any())).thenReturn(null);

        APIException thrown = assertThrows(APIException.class, () -> {
            service.retrieveByPersonNumber("invalidPersonNumber");
        });
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, thrown.getErrorCode());
    }

    @Test
    public void givenPersonNumber_whenRetrieveByPersonNumber_thenAssigmentProfileRequestBeanIsNotNull() throws Exception {
        CascadeProfile cascadeProfile = mock(CascadeProfile.class);
        PersonIdentityBean personIdentityBean = mock(PersonIdentityBean.class);

        when(cascadeProfile.getName()).thenReturn(PROFILE_NAME);
        when(personality.getPersonNumber()).thenReturn(PERSON_ID.toString());
        when(personIdentityBeanValidator.getPersonByPersonNumber(any())).thenReturn(personality);
        Mockito.when(PersonCascadeProfileAssignment.retrieveCascadeProfileByPersonId(Mockito.any()))
                .thenReturn(cascadeProfileAssignment);
        when(cascadeProfileAssignment.getProfileId()).thenReturn(PROFILE_ID);
        when(CascadeProfile.retrieveById(PROFILE_ID)).thenReturn(cascadeProfile);

        AssignmentProfileRequestBean result = service.retrieveByPersonNumber(PERSON_ID.toString());
        assertNotNull(result);
        assertNotNull(result.getAssignmentProfile());
    }

    @Test
    public void givenPersonId_whenRetrieveByPersonNumber_thenAssigmentProfileRequestBeanIsNotNull() {
        CascadeProfile cascadeProfile = mock(CascadeProfile.class);
        PersonIdentityBean personIdentityBean = mock(PersonIdentityBean.class);
        when(personIdentityBean.getPersonNumber()).thenReturn(PERSON_ID.toString());
        when(personality.getPersonNumber()).thenReturn(PERSON_ID.toString());
        when(personIdentityBeanValidator.getPersonByPersonId(PERSON_ID.longValue())).thenReturn(personality);
        when(personIdentityBeanValidator.createForPersonNumber(any())).thenReturn(personIdentityBean);
        when(personIdentityBeanValidator.getPersonByPersonNumber(personIdentityBean)).thenReturn(personality);
        when(PersonCascadeProfileAssignment.retrieveCascadeProfileByPersonId(Mockito.any()))
                .thenReturn(cascadeProfileAssignment);
        when(cascadeProfileAssignment.getProfileId()).thenReturn(PROFILE_ID);
        when(CascadeProfile.retrieveById(PROFILE_ID)).thenReturn(cascadeProfile);

        AssignmentProfileRequestBean result = service.retrieveByPersonId(PERSON_ID.longValue());
        assertNotNull(result, "AssignmentProfileRequestBean should not be null");
        assertNotNull(result.getPersonIdentity(), "PersonIdentity should not be null");
    }

    @Test
    public void  givenPersonId_whenRetrieveByPersonId_thenAssigmentProfileRequestBeanIsNotNull () {
        CascadeProfile cascadeProfile = mock(CascadeProfile.class);
        when(cascadeProfile.getName()).thenReturn(PROFILE_NAME);
        when(personality.getPersonNumber()).thenReturn(PERSON_ID.toString());
        when(personIdentityBeanValidator.createForPersonNumber(any())).thenReturn(mock(PersonIdentityBean.class));
        when(personIdentityBeanValidator.getPersonByPersonId(PERSON_ID.longValue())).thenReturn(personality);
        Mockito.when(PersonCascadeProfileAssignment.retrieveCascadeProfileByPersonId(Mockito.any()))
                .thenReturn(cascadeProfileAssignment);
        when(cascadeProfileAssignment.getProfileId()).thenReturn(PROFILE_ID);
        when(CascadeProfile.retrieveById(PROFILE_ID)).thenReturn(cascadeProfile);

        AssignmentProfileRequestBean result = service.retrieveByPersonId(PERSON_ID.longValue());
        assertNotNull(result.getAssignmentProfile());
    }

    @Test
    public void  givenCascadeProfileIdIsNull_whenRetrieveByPersonIdFromPersonality_thenAssigmentProfileIsNull () {
        Mockito.when(Personality.getByPersonId(PERSON_ID)).thenReturn(personality);
        Mockito.when(PersonCascadeProfileAssignment.retrieveCascadeProfileByPersonId(Mockito.any()))
                .thenReturn(cascadeProfileAssignment);
//        when(validatorUtils.notNull(PROFILE_NAME)).thenReturn(false);
        when(cascadeProfileAssignment.getProfileId()).thenReturn(PROFILE_ID);
        AssignmentProfileRequestBean result = service.retrieveByPersonIdFromPersonality(1L);
        assertNull(result.getAssignmentProfile());
    }

    @Test
    public void  givenRetriveCascadeProfileByIdIsNull_whenRetrieveByPersonIdFromPersonality_thenAssigmentProfileIsNull () {
        Mockito.when(Personality.getByPersonId(PERSON_ID)).thenReturn(personality);
        Mockito.when(PersonCascadeProfileAssignment.retrieveCascadeProfileByPersonId(Mockito.any()))
                .thenReturn(cascadeProfileAssignment);
//        when(validatorUtils.notNull(PROFILE_NAME)).thenReturn(false);
        when(cascadeProfileAssignment.getProfileId()).thenReturn(PROFILE_ID);
        when(CascadeProfile.retrieveById(PROFILE_ID)).thenReturn(null);
        AssignmentProfileRequestBean result = service.retrieveByPersonIdFromPersonality(1L);
//        assertNull(result.getAssignmentProfile());
    }

    @Test
    public void givenRetriveCascadeProfileByIdIsPresent_whenRetrieveByPersonIdFromPersonality_thenAssigmentProfileIsAssigned() {
        CascadeProfile cascadeProfile = mock(CascadeProfile.class);
        when(cascadeProfile.getName()).thenReturn(PROFILE_NAME);
        Mockito.when(Personality.getByPersonId(PERSON_ID)).thenReturn(personality);
        Mockito.when(PersonCascadeProfileAssignment.retrieveCascadeProfileByPersonId(Mockito.any()))
                .thenReturn(cascadeProfileAssignment);
        when(cascadeProfileAssignment.getProfileId()).thenReturn(PROFILE_ID); // Ensure this returns a non-null value
        when(CascadeProfile.retrieveById(PROFILE_ID)).thenReturn(cascadeProfile);

        AssignmentProfileRequestBean result = service.retrieveByPersonIdFromPersonality(1L);
        assertNotNull(result.getAssignmentProfile());
        assertEquals(PROFILE_NAME, result.getAssignmentProfile());
        assertEquals(PROFILE_ID.longValue(), result.getProfileId().longValue());
    }

    @Test
    public void testRetrieveByPersonIdFromPersonality() {
        Mockito.when(Personality.getByPersonId(PERSON_ID)).thenReturn(personality);
        Mockito.when(PersonCascadeProfileAssignment.retrieveCascadeProfileByPersonId(Mockito.any()))
                .thenReturn(cascadeProfileAssignment);
        when(cascadeProfileAssignment.getProfileId()).thenReturn(PERSON_ID);
        AssignmentProfileRequestBean result = service.retrieveByPersonIdFromPersonality(1L);
        assertNotNull(result);
    }

    @Test
    public void testRetrieveByPersonIdFromPersonalityNull() {
        APIException expectedException = new APIException(TEST_ERROR_CODE);
        Mockito.when(Personality.getByPersonId(PERSON_ID)).thenReturn(null);
        Mockito.when(PrsnValidationException.invalidPropertyValue(anyString(), any(Object.class)))
                .thenThrow(expectedException);

        APIException thrown = assertThrows(APIException.class, () -> {
            service.retrieveByPersonIdFromPersonality(1L);
        });

        assertEquals(TEST_ERROR_CODE, thrown.getErrorCode());
    }

    private AssignmentProfileRequestBean prepareBean() {
        AssignmentProfileRequestBean bean = new AssignmentProfileRequestBean();
        bean.setAssignmentProfile(VALID_PROFILE_NAME);
        bean.setPersonIdentity(personIdentity);
        return bean;
    }

    private AssignmentProfileRequestBean prepareFailBean() {
        AssignmentProfileRequestBean bean = new AssignmentProfileRequestBean();
        bean.setAssignmentProfile(FAIL_PROFILE_NAME);
        bean.setPersonIdentity(personIdentity);
        return bean;
    }
}
