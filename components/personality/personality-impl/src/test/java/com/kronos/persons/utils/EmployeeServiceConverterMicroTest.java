package com.kronos.persons.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.fail;

@ExtendWith(MockitoExtension.class)
public class EmployeeServiceConverterMicroTest {

	@Test
	public void testLoadRepository(){
		EmployeeServiceConverter converter = new EmployeeServiceConverter();
		try{
			converter.loadRepository();
		} catch(Exception e){
			fail();
		}
	}
}
