package com.kronos.persons.rest.assignments.service;

import static com.kronos.persons.rest.assignments.service.PersonAttestationAssignmentCriteriaHelper.buildCriteria;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.kronos.commonapp.authz.impl.fap.profiles.AccessProfile;
import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.commonbusiness.datatypes.ref.ObjectRefList;
import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.dataaccess.entity.AttestationProfileAssignment;
import com.kronos.people.personality.dataaccess.repository.AttestationProfileAssignmentRepository;
import com.kronos.people.personality.model.PersonAttestationProfileAssignmentsDTO;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignment;
import com.kronos.persons.rest.beans.HyperFindFilterBean;
import com.kronos.persons.utils.PersonAttestationProfileAssignmentConverter;
import com.kronos.persons.rest.assignments.model.EmployeeRefs;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.timekeeping.service.attestation.api.service.AttestationProfileSetupService;
import com.kronos.wfc.platform.audit.business.types.AuditType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.people.personality.service.PersonAssignmentService;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignmentDTO;
import com.kronos.persons.rest.assignments.validation.PersonAttestationProfileAssignmentValidator;
import com.kronos.persons.rest.assignments.service.converter.AttestationProfileAssignmentConverter;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileAssignmentDTO;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileDTO;
import com.kronos.timekeeping.service.attestation.api.service.AttestationProfileAssignmentService;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonAttestationProfileAssignmentServiceTest {
    private static final Long DEFAULT_PERSON_ID = 4L;

    private static final String DEFAULT_PERSON_NUMBER = "444";
    private static final Integer SEQUENCE_1 = 1;
    private static final String DEFAULT_RECORD_BATCH_SIZE = "400";
    private static final String RECORD_BATCH_SIZE_MULTI_UPDATE = "com.kronos.persons.rest.assignments.service.multiupdate.request.size";
    private static final String BATCH_SIZE = "1";
    private static final ObjectRef PERSON = new ObjectRef();
    private static final LocalDate EFFECTIVE_DATE = LocalDate.now();
    private static final LocalDate EXPIRATION_DATE = EFFECTIVE_DATE.plusDays(5);
    private static final ObjectRef PROFILE = new ObjectRef();

    @Mock
    private PersonAssignmentService personAssignmentService;

    @Mock
    private AttestationProfileAssignmentService attestationProfileAssignmentService;

    @Mock
    private PersonAttestationProfileAssignmentValidator validator;

    @Mock
    private AttestationProfileAssignmentConverter converter;
    @Mock
    private PersonAttestationProfileAssignmentConverter personAttestationProfileAssignmentConverter;

    @Mock
    private IKProperties ikProperties;

    @Mock
    private AttestationProfileAssignmentRepository attestationProfileAssignmentRepository;

    @Mock
    private AttestationProfileSetupService attestationProfileSetupService;

    @Mock
    private HyperFindFilterBean hyperFindFilterBean;

    @InjectMocks
    private PersonAttestationProfileAssignmentService personAttestationProfileAssignmentService;

    private MockedStatic<AuditType> auditTypeMockedStatic;
    private MockedStatic<AccessProfile> accessProfileMockedStatic;

    @BeforeEach
    public void setUp() {
        auditTypeMockedStatic = Mockito.mockStatic(AuditType.class);
        when(converter.toPersonAttestationProfileAssignmentDTO(any(AttestationProfileAssignmentDTO.class),anyLong()))
            .thenAnswer(
                i -> toPersonAttestationProfileAssignmentDTO((AttestationProfileAssignmentDTO) i.getArguments()[0], (Long) i.getArguments()[1]));

        when(converter.toAttestationProfileAssignmentDTO(any(PersonAttestationProfileAssignmentDTO.class)))
            .thenAnswer(
                i -> toAttestationProfileAssignmentDTO((PersonAttestationProfileAssignmentDTO) i.getArguments()[0]));
        accessProfileMockedStatic = Mockito.mockStatic(AccessProfile.class);
    }

    @AfterEach
    public void tearDown() {
        auditTypeMockedStatic.close();
        accessProfileMockedStatic.close();
    }

    @Test
    public void shouldReturnProfileAssignmentsOnGetAttestationProfileAssignments() {
        List<PersonAttestationProfileAssignmentDTO> expected = new ArrayList<>();
        PersonAttestationProfileAssignmentDTO assignment = buildValidDTO();
        expected.add(assignment);

        doNothing().when(validator).validateReadAccess(anyLong());
        when(attestationProfileAssignmentService.getAttestationProfileAssignments(anyLong(), eq(false))).thenReturn(
            expected.stream()
                .map(this::toAttestationProfileAssignmentDTO)
                .collect(Collectors.toList()));

        List<PersonAttestationProfileAssignmentDTO> actual = personAttestationProfileAssignmentService.getAttestationProfileAssignments(
            DEFAULT_PERSON_ID, false);

        verify(attestationProfileAssignmentService, times(1)).getAttestationProfileAssignments(DEFAULT_PERSON_ID, false);
        assertEquals(expected.size(), actual.size());
        PersonAttestationProfileAssignmentDTO expectedAssignment = expected.get(0);
        PersonAttestationProfileAssignmentDTO actualAssignment = actual.get(0);
        assertEquals(expectedAssignment.getEffectiveDate(), actualAssignment.getEffectiveDate());
        assertEquals(expectedAssignment.getExpirationDate(), actualAssignment.getExpirationDate());
        assertEquals(expectedAssignment.getProfile().getId(), actualAssignment.getProfile().getId());
        assertEquals(expectedAssignment.getProfile().getQualifier(), actualAssignment.getProfile().getQualifier());
    }

    @Test
    public void testGetAttestationProfileAssignmentsWithPersonId() {
        List<PersonAttestationProfileAssignmentDTO> expected = new ArrayList<>();
        PersonAttestationProfileAssignmentDTO assignment = buildValidDTO();
        expected.add(assignment);

        when(attestationProfileAssignmentService.getAttestationProfileAssignments(anyLong(), eq(false))).thenReturn(
                expected.stream()
                        .map(this::toAttestationProfileAssignmentDTO)
                        .collect(Collectors.toList()));

        doNothing().when(validator).validateReadAccess(anyLong());
        List<PersonAttestationProfileAssignmentDTO> actual = personAttestationProfileAssignmentService.getAttestationProfileAssignments(
                DEFAULT_PERSON_ID, false);
        List<PersonAttestationProfileAssignmentDTO> except = personAttestationProfileAssignmentService.getAttestationProfileAssignments(
                DEFAULT_PERSON_ID);
        assertEquals(actual.size(), except.size());
        assertEquals(actual.get(0).getPerson(), except.get(0).getPerson());
        assertEquals(actual.get(0).getEffectiveDate(), except.get(0).getEffectiveDate());
    }

    @Test
    public void testGetAttestationProfileAssignmentsWithPersonNumber() {
        List<PersonAttestationProfileAssignmentDTO> expected = new ArrayList<>();
        PersonAttestationProfileAssignmentDTO assignment = buildValidDTO();
        expected.add(assignment);

        when(attestationProfileAssignmentService.getAttestationProfileAssignments(anyLong(), eq(false))).thenReturn(
                expected.stream()
                        .map(this::toAttestationProfileAssignmentDTO)
                        .collect(Collectors.toList()));

        when(validator.validateReadAccessAndGetPersonId(DEFAULT_PERSON_NUMBER)).thenReturn(DEFAULT_PERSON_ID);
        List<PersonAttestationProfileAssignmentDTO> actual = personAttestationProfileAssignmentService.getAttestationProfileAssignments(
                DEFAULT_PERSON_NUMBER, false);
        List<PersonAttestationProfileAssignmentDTO> except = personAttestationProfileAssignmentService.getAttestationProfileAssignments(
                DEFAULT_PERSON_NUMBER);
        assertEquals(actual.size(), except.size());
        assertEquals(actual.get(0).getPerson(), except.get(0).getPerson());
        assertEquals(actual.get(0).getEffectiveDate(), except.get(0).getEffectiveDate());
    }


    @Test
    public void shouldReturnProfileAssignmentsOnGetAttestationProfileAssignmentsByPersonNumber() {
        List<PersonAttestationProfileAssignmentDTO> expected = new ArrayList<>();
        PersonAttestationProfileAssignmentDTO assignment = buildValidDTO();
        expected.add(assignment);

        when(validator.validateReadAccessAndGetPersonId(DEFAULT_PERSON_NUMBER)).thenReturn(DEFAULT_PERSON_ID);
        when(attestationProfileAssignmentService.getAttestationProfileAssignments(anyLong(), eq(false))).thenReturn(
            expected.stream()
                .map(this::toAttestationProfileAssignmentDTO)
                .collect(Collectors.toList()));

        List<PersonAttestationProfileAssignmentDTO> actual = personAttestationProfileAssignmentService.getAttestationProfileAssignments(
            DEFAULT_PERSON_NUMBER, false);

        verify(attestationProfileAssignmentService, times(1)).getAttestationProfileAssignments(DEFAULT_PERSON_ID, false);
        assertEquals(expected.size(), actual.size());
        PersonAttestationProfileAssignmentDTO expectedAssignment = expected.get(0);
        PersonAttestationProfileAssignmentDTO actualAssignment = actual.get(0);
        assertEquals(expectedAssignment.getEffectiveDate(), actualAssignment.getEffectiveDate());
        assertEquals(expectedAssignment.getExpirationDate(), actualAssignment.getExpirationDate());
        assertEquals(expectedAssignment.getProfile().getId(), actualAssignment.getProfile().getId());
        assertEquals(expectedAssignment.getProfile().getQualifier(), actualAssignment.getProfile().getQualifier());
    }

    @Test
    public void shouldReturnProfileAssignmentOnCreate() {
        PersonAttestationProfileAssignmentDTO expected = buildValidDTO();

        doNothing().when(validator).validateCreateAccess(anyLong(), any(PersonAttestationProfileAssignmentDTO.class));
        when(personAssignmentService.addProfileAssignment(anyLong(),
            any(AttestationProfileAssignmentDTO.class))).thenReturn(
            toAttestationProfileAssignmentDTO(expected));

        PersonAttestationProfileAssignmentDTO actual = personAttestationProfileAssignmentService.create(
            DEFAULT_PERSON_ID, expected);

        verify(personAssignmentService, times(1)).addProfileAssignment(DEFAULT_PERSON_ID,
            toAttestationProfileAssignmentDTO(expected));

        assertEquals(expected.getEffectiveDate(), actual.getEffectiveDate());
        assertEquals(expected.getExpirationDate(), actual.getExpirationDate());
        assertEquals(expected.getProfile().getId(), actual.getProfile().getId());
        assertEquals(expected.getProfile().getQualifier(), actual.getProfile().getQualifier());
    }

    @Test
    public void shouldReturnProfileAssignmentOnCreateWithPersonRef() {
        PersonAttestationProfileAssignmentDTO expected = buildValidDTO();
        PersonAttestationProfileAssignmentDTO dto = buildValidDTO();
        dto.setPerson(new ObjectRef(null, DEFAULT_PERSON_NUMBER));

        when(validator.validateCreateAccessAndGetPersonId(dto)).thenReturn(DEFAULT_PERSON_ID);
        when(personAssignmentService.addProfileAssignment(eq(DEFAULT_PERSON_ID),
            any(AttestationProfileAssignmentDTO.class))).thenReturn(
            toAttestationProfileAssignmentDTO(expected));

        PersonAttestationProfileAssignmentDTO actual = personAttestationProfileAssignmentService.create(dto);

        verify(personAssignmentService, times(1)).addProfileAssignment(eq(DEFAULT_PERSON_ID),
            any(AttestationProfileAssignmentDTO.class));

        assertEquals(expected.getEffectiveDate(), actual.getEffectiveDate());
        assertEquals(expected.getExpirationDate(), actual.getExpirationDate());
        assertEquals(expected.getProfile().getId(), actual.getProfile().getId());
        assertEquals(expected.getProfile().getQualifier(), actual.getProfile().getQualifier());
    }

    @Test
    public void getPersonAttestationProfileAssignment_exclusive() {
        assertThrows(APIException.class, () -> {
            personAttestationProfileAssignmentService.getPersonAttestationProfileAssignment(new EmployeeRefs(new ObjectRefList()), buildCriteria());
        });

    }

    @Test
    public void getPersonAttestationProfileAssignment_success() {
        Mockito.when(AccessProfile.isPermitted("PERSON")).thenReturn(true);
        when(ikProperties.getProperty(anyString(), anyString())).thenReturn("3");
        List<Long> personIds = Collections.singletonList(1L);
        List<AttestationProfileAssignment> attestProfAss = Collections.singletonList(new AttestationProfileAssignment());
        attestProfAss.get(0).setProfileId(1L);
        AttestationProfileDTO attestProf = new AttestationProfileDTO();
        PersonAttestationProfileAssignment persAttProfAss = new PersonAttestationProfileAssignment();
        persAttProfAss.setEmployee(new ObjectRef(1L));
        when(attestationProfileSetupService.getAttestationProfile(1L)).thenReturn(attestProf);
        when(attestationProfileAssignmentRepository.findByPersonIds(personIds)).thenReturn(attestProfAss);
        when(converter.toPersonAttestationProfileAssignment(anyList(), anyMap(), any())).thenReturn(persAttProfAss);
        EmployeeRefs employees =  new EmployeeRefs(new ObjectRefList(personIds, null, null));
        List<PersonAttestationProfileAssignment> actual =  personAttestationProfileAssignmentService.getPersonAttestationProfileAssignment(employees, buildCriteria());
        assertFalse(actual.isEmpty());
        assertEquals((long) actual.get(0).getEmployee().getId(), 1L);
    }

    @Test
    public void getPersonAttestationProfileAssignment_partial() {
        Mockito.when(AccessProfile.isPermitted("PERSON")).thenReturn(true);
        when(ikProperties.getProperty(anyString(), anyString())).thenReturn("3");
        List<Long> personIds = Arrays.asList(1L, 2L);
        List<AttestationProfileAssignment> attestProfAss = Collections.singletonList(new AttestationProfileAssignment());
        attestProfAss.get(0).setProfileId(1L);
        AttestationProfileDTO attestProf = new AttestationProfileDTO();
        PersonAttestationProfileAssignment persAttProfAss = new PersonAttestationProfileAssignment();
        persAttProfAss.setEmployee(new ObjectRef(1L));
        when(attestationProfileSetupService.getAttestationProfile(1L)).thenReturn(attestProf);
        when(attestationProfileAssignmentRepository.findByPersonIds(personIds)).thenReturn(attestProfAss);
        when(converter.toPersonAttestationProfileAssignment(anyList(), anyMap(), any())).thenReturn(persAttProfAss);
        Map<Long, Object> validationResults = new HashMap<>();
        validationResults.put(1L, new ObjectRef(1L));
        validationResults.put(2L, new APIException());
        when(validator.validateReadAccessAndGetObjectRefsByPersonIds(personIds, hyperFindFilterBean)).thenReturn(validationResults);
        EmployeeRefs employees =  new EmployeeRefs(new ObjectRefList(personIds, null, null));
        employees.setHyperFindFilter(hyperFindFilterBean);
        try {
            personAttestationProfileAssignmentService.getPersonAttestationProfileAssignment(employees, buildCriteria());
            fail();
        } catch (APIException actual) {
            assertEquals(actual.getErrorCode(), ExceptionConstants.PARTIAL_SUCCESS);
            assertFalse(actual.getResults().isEmpty());
            assertTrue(actual.getResults().containsKey("results"));
            assertTrue(actual.getResults().get("results") instanceof List);
            assertFalse(((List) actual.getResults().get("results")).isEmpty());
            assertEquals(((List) actual.getResults().get("results")).size(), 2);
        }
    }

    @Test
    public void multiUpsert() throws Exception {
        java.util.Map<Integer, APIException> excptionHolder = new HashMap<>();
        Map<Integer, PersonAttestationProfileAssignment> assignments = new HashMap<>();
        PersonAttestationProfileAssignment personAttestationProfileAssignment = new PersonAttestationProfileAssignment();
        assignments.put(SEQUENCE_1, personAttestationProfileAssignment);
        Map<Integer, PersonAttestationProfileAssignmentsDTO> assignmentDTOMap = new HashMap<>();
        PersonAttestationProfileAssignmentsDTO personAttestationProfileAssignmentsDTO = new PersonAttestationProfileAssignmentsDTO();
        personAttestationProfileAssignmentsDTO.setPerson(PERSON);
        com.kronos.people.personality.model.AttestationProfileAssignmentDTO attestationProfileAssignmentDTO = new com.kronos.people.personality.model.AttestationProfileAssignmentDTO();
        attestationProfileAssignmentDTO.setEffectiveDate(EFFECTIVE_DATE);
        attestationProfileAssignmentDTO.setExpirationDate(EXPIRATION_DATE);
        attestationProfileAssignmentDTO.setProfile(PROFILE);
        List<com.kronos.people.personality.model.AttestationProfileAssignmentDTO> attestationProfileAssignmentDTOs = new ArrayList<>();
        attestationProfileAssignmentDTOs.add(attestationProfileAssignmentDTO);
        personAttestationProfileAssignmentsDTO.setAttestationProfileAssignments(attestationProfileAssignmentDTOs);
        assignmentDTOMap.put(SEQUENCE_1, personAttestationProfileAssignmentsDTO);
        when(personAttestationProfileAssignmentConverter.convertToDTO(assignments)).thenReturn(assignmentDTOMap);
        Map<Integer, PersonAttestationProfileAssignment> assignmentMap = new HashMap<>();

        when(personAttestationProfileAssignmentConverter.convertToAPI(assignmentDTOMap)).thenReturn(assignmentMap);

        final Map<Integer, PersonAttestationProfileAssignment> result = personAttestationProfileAssignmentService.multiUpsert(excptionHolder, assignments,false);

        assertEquals(assignmentMap, result);
    }

    @Test
    public void shouldRemoveAssignmentOfPersonWithNoManagerLicense() {
        Mockito.when(AccessProfile.isPermitted("PERSON")).thenReturn(true);
        List<Long> personIds = Collections.singletonList(1L);
        List<AttestationProfileAssignment> attestProfAss = Collections.singletonList(new AttestationProfileAssignment());
        attestProfAss.get(0).setProfileId(1L);
        AttestationProfileDTO attestProf = new AttestationProfileDTO();
        PersonAttestationProfileAssignment persAttProfAss = new PersonAttestationProfileAssignment();
        persAttProfAss.setEmployee(new ObjectRef(1L));
        persAttProfAss.setManagerRoleAttestationProfileAssignments(createManagerRoleAttestationAssignment());
        when(attestationProfileSetupService.getAttestationProfile(1L)).thenReturn(attestProf);
        when(attestationProfileAssignmentRepository.findByPersonIdsAndManagerRoles(any(), any())).thenReturn(attestProfAss);
        when(converter.toPersonAttestationProfileAssignment(anyList(), anyMap(), any())).thenReturn(persAttProfAss);
        when(validator.hasManagerLicense(any())).thenReturn(false);
        EmployeeRefs employees = new EmployeeRefs(new ObjectRefList(personIds, null, null));
        List<PersonAttestationProfileAssignment> actual =  personAttestationProfileAssignmentService.getPersonAttestationProfileAssignment(employees, buildCriteria());
        assertFalse(actual.isEmpty());
        assertTrue(actual.get(0).getManagerRoleAttestationProfileAssignments().isEmpty());
    }

    @Test
    public void shouldReturnEmptyIfNoManagerLicense() {
        doNothing().when(validator).validateReadAccess(anyLong());
        doNothing().when(validator).validateManagerRoleReadFAP(any());
        when(validator.hasManagerLicense(any())).thenReturn(false);
        List<PersonAttestationProfileAssignmentDTO> list = personAttestationProfileAssignmentService.getAttestationProfileAssignments(1l, true);
        assertEquals(0, list.size());
    }

    private List<com.kronos.persons.rest.assignments.model.AttestationProfileAssignment> createManagerRoleAttestationAssignment() {
        List<com.kronos.persons.rest.assignments.model.AttestationProfileAssignment> attestationProfileAssignments = new ArrayList<>();
        attestationProfileAssignments.add(new com.kronos.persons.rest.assignments.model.AttestationProfileAssignment());
        return attestationProfileAssignments;
    }

    private PersonAttestationProfileAssignmentDTO buildValidDTO() {
        PersonAttestationProfileAssignmentDTO dto = new PersonAttestationProfileAssignmentDTO();
        dto.setProfile(new ObjectRef(1L, "some name"));
        dto.setEffectiveDate(LocalDate.now());
        dto.setExpirationDate(LocalDate.now().plusDays(4));

        return dto;
    }

    private PersonAttestationProfileAssignmentDTO toPersonAttestationProfileAssignmentDTO(
        AttestationProfileAssignmentDTO dto, Long personId) {
        PersonAttestationProfileAssignmentDTO convertedDTO = new PersonAttestationProfileAssignmentDTO();
        convertedDTO.setEffectiveDate(dto.getEffectiveDate().toLocalDate());
        convertedDTO.setExpirationDate(dto.getExpirationDate().toLocalDate());
        AttestationProfileDTO profileDTO = dto.getAttestationProfile();
        convertedDTO.setProfile(new ObjectRef(profileDTO.getId(), profileDTO.getName()));
        convertedDTO.setPerson(new ObjectRef((personId != null? personId: 0L), "TestUser"));
        return convertedDTO;
    }

    private AttestationProfileAssignmentDTO toAttestationProfileAssignmentDTO(
        PersonAttestationProfileAssignmentDTO dto) {
        AttestationProfileAssignmentDTO convertedDTO = new AttestationProfileAssignmentDTO();
        AttestationProfileDTO convertedProfileDTO = new AttestationProfileDTO();
        ObjectRef profileDTO = dto.getProfile();
        convertedProfileDTO.setId(profileDTO.getId());
        convertedProfileDTO.setName(profileDTO.getQualifier());

        convertedDTO.setAttestationProfile(convertedProfileDTO);
        convertedDTO.setEffectiveDate(dto.getEffectiveDate().atStartOfDay());
        convertedDTO.setExpirationDate(dto.getExpirationDate().atStartOfDay());

        return convertedDTO;
    }
}
