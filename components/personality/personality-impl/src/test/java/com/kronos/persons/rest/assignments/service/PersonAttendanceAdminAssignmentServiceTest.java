package com.kronos.persons.rest.assignments.service;

import com.google.common.collect.Lists;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.AdminProfileBean;
import com.kronos.persons.rest.assignments.model.AttendanceAdminAssignmentBean;
import com.kronos.persons.rest.assignments.model.AttendanceAdminAssignmentWrapper;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.absencemgmt.service.business.people.PersonAttendanceAdministratorAssignment;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.commonapp.people.business.person.PersonLicenseType;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.licensing.framework.LicenseType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.shared.SecurityConstants;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonAttendanceAdminAssignmentServiceTest {

	@InjectMocks
	PersonAttendanceAdminAssignmentService service;

	@Mock
	PersonIdentityBeanValidator personIdentityBeanValidator;

	@Mock
	LicenseValidator licenseValidator;

	@Spy
	ValidatorUtils validatorUtils = new ValidatorUtils();

	@Mock
	LicenseType licenseType;

	@Mock
	PersonLicenseType plt;

	@Mock
	PersonAttendanceAdministratorAssignment assignment;

	@Mock
	private Person person;

	@Mock
	private PersonIdentityBean personIdentity;

	@Mock
	private AdminProfileBean adminIdentity;

	@Mock
	private Personality personality;

	private MockedStatic<ResponseHandler> responseHandlerMockedStatic;
	private MockedStatic<PersonAttendanceAdministratorAssignment> personAttendanceAdministratorAssignmentMockedStatic;
	private MockedStatic<PersonAttendanceAdminAssignmentBulkTransactionService> personAttendanceAdminAssignmentBulkTransactionServiceMockedStatic;
	private MockedStatic<LicenseType> licenseTypeMockedStatic;
	private MockedStatic<Personality> personalityMockedStatic;
	private MockedStatic<Person> personMockedStatic;

	@BeforeEach
	public void setUp() {
		responseHandlerMockedStatic = Mockito.mockStatic(ResponseHandler.class);
		personAttendanceAdministratorAssignmentMockedStatic = Mockito.mockStatic(PersonAttendanceAdministratorAssignment.class);
		personAttendanceAdminAssignmentBulkTransactionServiceMockedStatic = Mockito.mockStatic(PersonAttendanceAdminAssignmentBulkTransactionService.class);
		licenseTypeMockedStatic = Mockito.mockStatic(LicenseType.class);
		personalityMockedStatic = Mockito.mockStatic(Personality.class);
		personMockedStatic = Mockito.mockStatic(Person.class);
	}

	@AfterEach
	public void tearDown() {
		responseHandlerMockedStatic.close();
		personAttendanceAdministratorAssignmentMockedStatic.close();
		personAttendanceAdminAssignmentBulkTransactionServiceMockedStatic.close();
		licenseTypeMockedStatic.close();
		personalityMockedStatic.close();
		personMockedStatic.close();
		Mockito.clearAllCaches();
		Mockito.framework().clearInlineMocks();
	}

	@SuppressWarnings("unchecked")
	@Test
	public void retrieveException() {
		assertThrows(APIException.class, () -> {
			PersonIdentityBean requestBean = new PersonIdentityBean();
			Mockito.when(personIdentityBeanValidator.getPersonality(requestBean)).thenThrow(APIException.class);
			service.retrieve(requestBean);
		});
	}

	@Test
	public void retrieve() {
		ObjectIdLong id = new ObjectIdLong(1L);
		Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
		AdminProfileBean employee = new AdminProfileBean();
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		requestBean.setPersonIdentity(employee);
		Personality personality = Mockito.mock(Personality.class);
		Personality adminPersonality = Mockito.mock(Personality.class);
		Mockito.when(Personality.getByPersonId(id)).thenReturn(adminPersonality);
		Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");
		Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
		Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
		Mockito.when(PersonAttendanceAdministratorAssignment.retrieveAttendanceAdminByPersonId(Mockito.any()))
				.thenReturn(assignment);
		Mockito.when(assignment.getAttendanceAdminId()).thenReturn(id);
		Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
		Mockito.when(personality.getNameData()).thenReturn(person);
		Mockito.when(plt.getActive()).thenReturn(true);
		AttendanceAdminAssignmentBean responseBean = service.retrieve(employee);
		assertNotNull(responseBean);
	}

	@Test
	public void retrieveAssignmentNotFound() {
		assertThrows(APIException.class, () -> {
			ObjectIdLong id = new ObjectIdLong(1L);
			AdminProfileBean employee = new AdminProfileBean();
			AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
			requestBean.setPersonIdentity(employee);
			Personality personality = Mockito.mock(Personality.class);
			Personality adminPersonality = Mockito.mock(Personality.class);
			Mockito.when(Personality.getByPersonId(id)).thenReturn(adminPersonality);
			Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");
			Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
			Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
			Mockito.when(PersonAttendanceAdministratorAssignment.retrieveAttendanceAdminByPersonId(Mockito.any())).thenReturn(null);
			Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
			Mockito.when(plt.getActive()).thenReturn(true);
			service.retrieve(employee);
		});
	}

	@Test
	public void updateRequest() {
		ObjectIdLong id = new ObjectIdLong(1L);
		AdminProfileBean employee = new AdminProfileBean();
		AdminProfileBean administrator = new AdminProfileBean();
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		requestBean.setPersonIdentity(employee);
		requestBean.setAdministrator(administrator);
		Personality personality = Mockito.mock(Personality.class);
		Personality adminPersonality = Mockito.mock(Personality.class);
		Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");
		Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
		Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getAdministrator())).thenReturn(adminPersonality);
		Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
		Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
		Mockito.when(adminPersonality.getLicenseType(Mockito.any())).thenReturn(plt);
		Mockito.when(plt.getActive()).thenReturn(true);
		Mockito.when(PersonAttendanceAdministratorAssignment.retrieveAttendanceAdminByPersonId(Mockito.any()))
				.thenReturn(assignment);
		Mockito.when(assignment.getAttendanceAdminId()).thenReturn(id);
		Personality responseBean = service.updateRequest(requestBean);
		assertNotNull(responseBean);
	}

	@Test
	public void updateRequestAdminNotExist() {
		assertThrows(APIException.class, () -> {
			AdminProfileBean employee = new AdminProfileBean();
			AdminProfileBean administrator = new AdminProfileBean();
			AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
			requestBean.setPersonIdentity(employee);
			requestBean.setAdministrator(administrator);
			Personality personality = Mockito.mock(Personality.class);
			Personality adminPersonality = Mockito.mock(Personality.class);
			Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");
			Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
			Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getAdministrator())).thenReturn(adminPersonality);
			Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
			Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
			Mockito.when(adminPersonality.getLicenseType(Mockito.any())).thenReturn(plt);
			Mockito.when(plt.getActive()).thenReturn(true);
			Mockito.when(PersonAttendanceAdministratorAssignment.retrieveAttendanceAdminByPersonId(Mockito.any())).thenReturn(null);
			service.updateRequest(requestBean);
		});
	}

	@Test
	public void updateRequestException() {
		assertThrows(APIException.class, () -> {
			AdminProfileBean employee = new AdminProfileBean();
			AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
			requestBean.setPersonIdentity(employee);
			Personality personality = Mockito.mock(Personality.class);
			Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
			Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
			Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
			Mockito.when(plt.getActive()).thenReturn(true);
			service.updateRequest(requestBean);
		});
	}

	@Test
	public void deleteRequest() {
		ObjectIdLong id = new ObjectIdLong(1L);
		AdminProfileBean employee = new AdminProfileBean();
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		requestBean.setPersonIdentity(employee);
		Personality personality = Mockito.mock(Personality.class);
		Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
		Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
		Mockito.when(PersonAttendanceAdministratorAssignment.retrieveAttendanceAdminByPersonId(Mockito.any()))
				.thenReturn(assignment);
		Mockito.when(assignment.getAttendanceAdminId()).thenReturn(id);
		Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
		Mockito.when(plt.getActive()).thenReturn(true);
		Mockito.when(PersonAttendanceAdministratorAssignment.retrieveAttendanceAdminByPersonId(Mockito.any()))
				.thenReturn(assignment);
		Personality responseBean = service.deleteRequest(requestBean);
		assertNotNull(responseBean);
	}

	@Test
	public void deleteRequestException() {
		assertThrows(APIException.class, () -> {
			ObjectIdLong id = new ObjectIdLong(1L);
			AdminProfileBean employee = new AdminProfileBean();
			AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
			requestBean.setPersonIdentity(employee);
			Personality personality = Mockito.mock(Personality.class);
			Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
			Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
			Mockito.when(PersonAttendanceAdministratorAssignment.retrieveAttendanceAdminByPersonId(Mockito.any()))
					.thenReturn(assignment);
			Mockito.when(assignment.getAttendanceAdminId()).thenReturn(id);
			Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
			Mockito.when(plt.getActive()).thenReturn(true);
			Mockito.when(PersonAttendanceAdministratorAssignment.retrieveAttendanceAdminByPersonId(Mockito.any()))
					.thenReturn(null);
			service.deleteRequest(requestBean);
		});
	}

	@Test
	public void testValidateAdministratorWrapperValidBean() {
		AttendanceAdminAssignmentBean bean = prepareBean();
		AttendanceAdminAssignmentWrapper beanWrapper = new AttendanceAdminAssignmentWrapper(bean);
		Mockito.doNothing().when(personIdentityBeanValidator).validate(null, adminIdentity, null);
		service.validateAdministratorWrapper(beanWrapper);
		assertNull(beanWrapper.getApiException());
	}

	@Test
	public void testValidateAdministratorWrapperBeanWithInvalidPersonIdentity() {
		AttendanceAdminAssignmentBean bean = prepareBean();
		AttendanceAdminAssignmentWrapper beanWrapper = new AttendanceAdminAssignmentWrapper(bean);
		Mockito.doThrow(new APIException()).when(personIdentityBeanValidator).validate(null, adminIdentity, null);
		service.validateAdministratorWrapper(beanWrapper);
		assertNotNull(beanWrapper.getApiException());
	}

	@Test
	public void testValidateAdministratorWrapperBeanWithNullPersonIdentity() {
		AttendanceAdminAssignmentBean bean = new AttendanceAdminAssignmentBean();
		AttendanceAdminAssignmentWrapper beanWrapper = new AttendanceAdminAssignmentWrapper(bean);
		service.validateAdministratorWrapper(beanWrapper);
		assertNotNull(beanWrapper.getApiException());
	}

	@Test
	public void testBulkUpdateRequestSuccess() {
		PersonAttendanceAdminAssignmentBulkTransactionService transactionService =
				Mockito.mock(PersonAttendanceAdminAssignmentBulkTransactionService.class);
		AttendanceAdminAssignmentBean bean = prepareBean();
		AttendanceAdminAssignmentWrapper beanWrapper = new AttendanceAdminAssignmentWrapper(bean);
		List<AttendanceAdminAssignmentWrapper> beanWrappers = Lists.newArrayList(beanWrapper);
		Mockito.when(personIdentityBeanValidator.getPersonality(adminIdentity)).thenReturn(personality);
		Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, personIdentity, null);
		Mockito.when(personIdentityBeanValidator.getPersonality(personIdentity)).thenReturn(personality);
		Mockito.doNothing().when(licenseValidator).validateLicense(personality, SecurityConstants.WORKFORCE_ATTENDANCE);
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1));
		Mockito.when(ResponseHandler.apiSuccessResponse(bean, personality)).thenReturn(bean);
		Mockito.when(PersonAttendanceAdministratorAssignment.retrieveAttendanceAdminByPersonId(Mockito.any()))
				.thenReturn(assignment);
		Mockito.when(PersonAttendanceAdminAssignmentBulkTransactionService
				.getTransactionService(personality, Lists.newArrayList(assignment))).thenReturn(transactionService);
		Mockito.doNothing().when(transactionService).run();
		service.bulkUpdateRequest(beanWrappers);
		assertNull(beanWrapper.getApiException());
		Mockito.verify(transactionService, Mockito.times(1)).run();
	}

	@SuppressWarnings("unchecked")
	@Test
	public void testBulkUpdateRequestPartiallySuccessful() {
		PersonAttendanceAdminAssignmentBulkTransactionService transactionService =
				Mockito.mock(PersonAttendanceAdminAssignmentBulkTransactionService.class);
		AttendanceAdminAssignmentBean successBean = prepareBean();
		AttendanceAdminAssignmentBean failBean = prepareFailBean();
		AttendanceAdminAssignmentWrapper failBeanWrapper = new AttendanceAdminAssignmentWrapper(failBean);
		AttendanceAdminAssignmentWrapper successBeanWrapper = new AttendanceAdminAssignmentWrapper(successBean);
		List<AttendanceAdminAssignmentWrapper> beanWrappers = Lists.newArrayList(successBeanWrapper, failBeanWrapper);

		Mockito.when(personIdentityBeanValidator.getPersonality(adminIdentity)).thenReturn(personality);
		Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, personIdentity, null);
		Mockito.when(personIdentityBeanValidator.getPersonality(personIdentity)).thenReturn(personality);
		Mockito.doNothing().when(licenseValidator).validateLicense(personality, SecurityConstants.WORKFORCE_ATTENDANCE);
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1));
		Mockito.when(ResponseHandler.apiSuccessResponse(successBeanWrapper, personality))
				.thenReturn(successBeanWrapper);
		Mockito.when(PersonAttendanceAdministratorAssignment.retrieveAttendanceAdminByPersonId(Mockito.any()))
				.thenReturn(assignment);
		Mockito.when(PersonAttendanceAdministratorAssignment.retrieveAttendanceAdminByPersonId(null))
				.thenReturn(null);
		Personality personalityHavingNullId = Mockito.mock(Personality.class);
		Mockito.when(personIdentityBeanValidator.getPersonality(null)).thenReturn(personalityHavingNullId);
		Mockito.when(personalityHavingNullId.getPersonId()).thenReturn(null);

		Mockito.when(PersonAttendanceAdminAssignmentBulkTransactionService
				.getTransactionService(personality, Lists.newArrayList(assignment))).thenReturn(transactionService);
		Mockito.doNothing().when(transactionService).run();
		service.bulkUpdateRequest(beanWrappers);
		assertNull(successBeanWrapper.getApiException());
		assertNotNull(failBeanWrapper.getApiException());
		Mockito.verify(transactionService, Mockito.times(1)).run();
	}

	@Test
	public void testBulkUpdateRequestFail() {
		Mockito.mock(PersonAttendanceAdminAssignmentBulkTransactionService.class);
		AttendanceAdminAssignmentBean successBean = prepareBean();
		AttendanceAdminAssignmentBean failBean = prepareFailBean();
		AttendanceAdminAssignmentWrapper failBeanWrapper = new AttendanceAdminAssignmentWrapper(failBean);
		AttendanceAdminAssignmentWrapper successBeanWrapper = new AttendanceAdminAssignmentWrapper(successBean);
		List<AttendanceAdminAssignmentWrapper> beanWrappers = Lists.newArrayList(successBeanWrapper, failBeanWrapper);

		Mockito.when(personIdentityBeanValidator.getPersonality(adminIdentity)).thenReturn(personality);
		Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(null, personIdentity, null);
		Mockito.when(personIdentityBeanValidator.getPersonality(personIdentity)).thenReturn(personality);
		Mockito.doNothing().when(licenseValidator).validateLicense(personality, SecurityConstants.WORKFORCE_ATTENDANCE);
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1));
		Mockito.when(ResponseHandler.apiSuccessResponse(successBeanWrapper, personality))
				.thenReturn(successBeanWrapper);
		Mockito.when(PersonAttendanceAdministratorAssignment.retrieveAttendanceAdminByPersonId(Mockito.any()))
				.thenReturn(assignment);
		Mockito.when(PersonAttendanceAdminAssignmentBulkTransactionService
				.getTransactionService(personality, Lists.newArrayList(assignment))).thenThrow(new APIException());
		service.bulkUpdateRequest(beanWrappers);
		assertNotNull(successBeanWrapper.getApiException());
		assertNotNull(failBeanWrapper.getApiException());
	}

	private AttendanceAdminAssignmentBean prepareBean() {
		AttendanceAdminAssignmentBean bean = new AttendanceAdminAssignmentBean();
		bean.setAdministrator(adminIdentity);
		bean.setPersonIdentity(personIdentity);
		return bean;
	}

	private AttendanceAdminAssignmentBean prepareFailBean() {
		AttendanceAdminAssignmentBean bean = new AttendanceAdminAssignmentBean();
		bean.setAdministrator(adminIdentity);
		bean.setPersonIdentity(null);
		return bean;
	}
}
