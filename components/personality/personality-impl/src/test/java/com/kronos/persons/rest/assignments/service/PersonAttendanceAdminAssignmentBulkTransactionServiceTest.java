package com.kronos.persons.rest.assignments.service;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.google.common.collect.Lists;
import com.kronos.wfc.absencemgmt.service.business.people.PersonAttendanceAdministratorAssignment;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.SQLStatement;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.mockito.MockedStatic;

/**
 * Tests for {@link PersonAttendanceAdminAssignmentBulkTransactionService}.
 * Copyright (C) 2019 Kronos.com
 * Date: Jul 05, 2019
 *
 * author aliaksei.kakhanouski
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonAttendanceAdminAssignmentBulkTransactionServiceTest {

    private static final ObjectIdLong LEAVE_PROFILE_ID = new ObjectIdLong(1L);
    private static final ObjectIdLong PERSON_ID = new ObjectIdLong(2L);

    private PersonAttendanceAdminAssignmentBulkTransactionService transactionService;
    @Mock
    private SQLStatement sqlStatement;
    @Mock
    private Personality personality;
    @Mock
    private PersonAttendanceAdministratorAssignment assignment;

    private MockedStatic<SQLStatement> sqlStatementMockedStatic;
    private MockedStatic<PersonAttendanceAdminAssignmentBulkTransactionService> transactionServiceMockedStatic;

    @BeforeEach
    public void setup() throws Exception {
        sqlStatementMockedStatic = Mockito.mockStatic(SQLStatement.class);
        transactionServiceMockedStatic = Mockito.mockStatic(PersonAttendanceAdminAssignmentBulkTransactionService.class);
        transactionService = Mockito.mock(PersonAttendanceAdminAssignmentBulkTransactionService.class);
        transactionServiceMockedStatic.when(() -> PersonAttendanceAdminAssignmentBulkTransactionService
                        .getTransactionService(personality, Lists.newArrayList(assignment)))
                .thenReturn(transactionService);
    }

    @AfterEach
    public void tearDown() {
        sqlStatementMockedStatic.close();
        transactionServiceMockedStatic.close();
    }

    @Test
    public void testTransaction() {
        when(personality.getPersonId()).thenReturn(PERSON_ID);
        when(assignment.getObjectId()).thenReturn(LEAVE_PROFILE_ID);
        Mockito.doNothing().when(sqlStatement).execute();

        transactionService.transaction();

        verify(assignment, times(0)).getObjectId();
        verify(personality, times(0)).getPersonId();
        verify(sqlStatement, times(0)).execute();
    }
}