package com.kronos.persons.rest.assignments.service;

import com.google.common.collect.Lists;
import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.access.SpringContext;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.AdminAssignmentRequestBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;

import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.utils.NewBatchProcessor;
import com.kronos.wfc.commonapp.people.business.personality.Personality;

import com.kronos.wfc.platform.exceptions.framework.GenericException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;


import java.util.List;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * Verifies {@link RestPersonLeaveAdminAssignment}.
 * Copyright (C) 2019 Kronos.com
 * Date: Jun 25, 2019
 *
 * <AUTHOR> Sharma
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RestPersonLeaveAdminAssignmentTest {

    @InjectMocks
    RestPersonLeaveAdminAssignment restPersonLeaveAdminAssignment;

    @Mock
    private PersonLeaveAdminAssignmentService leaveAdminAssignmentService;

    @Mock
    PersonAssignmentHelper<AdminAssignmentRequestBean> personAssignmentHelper;

    @Mock
    private NewBatchProcessor<AdminAssignmentRequestBean, AdminAssignmentRequestBean> newBatchProcessor;

    private final Long personId = 1L;

    private final String personNumber = "personNumber";

    private MockedStatic<SpringContext> mockedSpringContext;

    @BeforeEach
    public void setUp() {
        mockedSpringContext = Mockito.mockStatic(SpringContext.class);
    }

    @AfterEach
    public void tearDown() {
        mockedSpringContext.close();
    }

    @Test
    public void testRetrieveWhenLeaveAdminAssignmentServiceRetrieve() {
        AdminAssignmentRequestBean adminAssignmentRequestBeanForPrepare = preparePersonAdminAssignmentRequestBean(personId);
        Mockito.when(leaveAdminAssignmentService.retrieve(any())).thenReturn(adminAssignmentRequestBeanForPrepare);
        AdminAssignmentRequestBean adminAssignmentRequestBean = restPersonLeaveAdminAssignment.retrieve(personId);
        assertEquals(adminAssignmentRequestBean.getPersonIdentity().getPersonKey(), adminAssignmentRequestBeanForPrepare.getPersonIdentity().getPersonKey());
    }

    @Test
    public void testRetrieveWhenLeaveAdminAssignmentServiceRetrieveThenThrowAPIException() {
        assertThrows(APIException.class, () -> {
            Mockito.when(leaveAdminAssignmentService.retrieve(any())).thenThrow(APIException.class);
            restPersonLeaveAdminAssignment.retrieve(personId);
        });
    }


    @Test
    public void testRetrieveWhenLeaveAdminAssignmentServiceRetrieveThenThrowException() {
        assertThrows(APIException.class, () -> {
            Mockito.when(leaveAdminAssignmentService.retrieve(any())).thenThrow(new RuntimeException("Error Occurred"));
            restPersonLeaveAdminAssignment.retrieve(personId);
        });
    }

    @Test
    public void testRetrieveWhenLeaveAdminAssignmentServiceRetrieveThenThrowGenericException(){
        assertThrows(APIException.class, () -> {
        IKProperties kProperties = Mockito.mock(IKProperties.class);
        mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(kProperties);
        GenericException genericException = Mockito.mock(GenericException.class);
        Mockito.when(leaveAdminAssignmentService.retrieve(any())).thenThrow(genericException);
        restPersonLeaveAdminAssignment.retrieve(personId);
        });
    }

    @Test
    public void testRetrieveByPersonNumberWhenLeaveAdminAssignmentServiceRetrieve() {
        AdminAssignmentRequestBean adminAssignmentRequestBeanForPrepare = preparePersonAdminAssignmentRequestBean(personNumber);
        Mockito.when(leaveAdminAssignmentService.retrieve(any())).thenReturn(adminAssignmentRequestBeanForPrepare);
        AdminAssignmentRequestBean adminAssignmentRequestBean = restPersonLeaveAdminAssignment.retrieveByPersonNumber(personNumber);
        assertEquals(adminAssignmentRequestBean.getPersonIdentity().getPersonNumber(), adminAssignmentRequestBeanForPrepare.getPersonIdentity().getPersonNumber());
    }

    @Test
    public void testRetrieveByPersonNumberWhenLeaveAdminAssignmentServiceRetrieveThenThrowAPIException() {
        assertThrows(APIException.class, () -> {
            Mockito.when(leaveAdminAssignmentService.retrieve(any())).thenThrow(APIException.class);
            restPersonLeaveAdminAssignment.retrieveByPersonNumber(personNumber);
        });
    }

    @Test
    public void testRetrieveByPersonNumberWhenLeaveAdminAssignmentServiceRetrieveThenThrowException() {
        assertThrows(APIException.class, () -> {
            Mockito.when(leaveAdminAssignmentService.retrieve(any())).thenThrow(new RuntimeException("Error Occurred"));
            restPersonLeaveAdminAssignment.retrieveByPersonNumber(personNumber);
        });
    }

    @Test
    public void testRetrieveByPersonNumberWhenLeaveAdminAssignmentServiceRetrieveThenThrowGenericException(){
        assertThrows(APIException.class, () -> {
        IKProperties kProperties = Mockito.mock(IKProperties.class);
        mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(kProperties);
        GenericException genericException = Mockito.mock(GenericException.class);
        Mockito.when(leaveAdminAssignmentService.retrieve(any())).thenThrow(genericException);
        restPersonLeaveAdminAssignment.retrieveByPersonNumber(personNumber);
        });
    }

    @Test
    public void testRetrieveListWhenLeavePersonAssignmentHelperGetPersonAssignmentList() {
        Function<PersonIdentityBean, AdminAssignmentRequestBean> getDataListFunction = (Function<PersonIdentityBean, AdminAssignmentRequestBean>) leaveAdminAssignmentService.retrieve(any());
        ExtensionSearchCriteria searchCriteria = new ExtensionSearchCriteria();
        List<AdminAssignmentRequestBean> adminAssignmentRequestBeans = Lists.newArrayList();
        //Mockito.when(personAssignmentHelper.getPersonAssignmentList(searchCriteria, getDataListFunction)).thenReturn(adminAssignmentRequestBeans);
        restPersonLeaveAdminAssignment.retrieveList(searchCriteria);
        Mockito.verify(personAssignmentHelper, times(1)).getPersonAssignmentList(any(), any());
    }

    @Test
    public void testUpdateWhenLeaveAdminAssignmentServiceUpdateRequest() {
        AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
        Personality personality = mock(Personality.class);
        Mockito.when(leaveAdminAssignmentService.updateRequest(any(AdminAssignmentRequestBean.class))).thenReturn(personality);
        AdminAssignmentRequestBean result = restPersonLeaveAdminAssignment.update(requestBean);
        assertEquals(requestBean, result);
    }

    @Test
    public void testUpdateWhenLeaveAdminAssignmentServiceUpdateRequestThenThrowAPIException() {
        assertThrows(APIException.class, () -> {
        AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
        Mockito.when(leaveAdminAssignmentService.updateRequest(any(AdminAssignmentRequestBean.class))).thenThrow(new APIException("Error Occured"));
        restPersonLeaveAdminAssignment.update(requestBean);
        });
    }

    @Test
    public void testUpdateWhenLeaveAdminAssignmentServiceUpdateRequestThenThrowGenericException() throws GenericException{
        assertThrows(APIException.class, () -> {
        AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
        IKProperties kProperties = Mockito.mock(IKProperties.class);
        mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(kProperties);
        GenericException genericException = Mockito.mock(GenericException.class);
        Mockito.when(leaveAdminAssignmentService.updateRequest(any(AdminAssignmentRequestBean.class))).thenThrow(genericException);
        restPersonLeaveAdminAssignment.update(requestBean);
        });
    }

    @Test
    public void testUpdateWhenLeaveAdminAssignmentServiceUpdateRequestThenThrowException() {
        assertThrows(APIException.class, () -> {
            AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
            IKProperties kProperties = Mockito.mock(IKProperties.class);
            mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(kProperties);
            Mockito.when(leaveAdminAssignmentService.updateRequest(any(AdminAssignmentRequestBean.class))).thenThrow(new RuntimeException("Error Occurred"));
            restPersonLeaveAdminAssignment.update(requestBean);
        });
    }

    @Test
    public void testDeleteWhenLeaveAdminAssignmentServiceDeleteRequest() {
        AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
        restPersonLeaveAdminAssignment.delete(requestBean);
        verify(leaveAdminAssignmentService).deleteRequest(requestBean);
    }

    @Test
    public void testDeleteWhenLeaveAdminAssignmentServiceDeleteRequestThenThrowAPIException() {
        assertThrows(APIException.class, () -> {
        AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
        Mockito.when(leaveAdminAssignmentService.deleteRequest(any(AdminAssignmentRequestBean.class))).thenThrow(new APIException("Error Occured"));
        restPersonLeaveAdminAssignment.delete(requestBean);
        });
    }

    @Test
    public void testDeleteWhenLeaveAdminAssignmentServiceDeleteRequestThenThrowGenericException() throws GenericException{
        assertThrows(APIException.class, () -> {
        AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
        IKProperties kProperties = Mockito.mock(IKProperties.class);
        mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(kProperties);
        GenericException genericException = Mockito.mock(GenericException.class);
        Mockito.when(leaveAdminAssignmentService.deleteRequest(any(AdminAssignmentRequestBean.class))).thenThrow(genericException);
        restPersonLeaveAdminAssignment.delete(requestBean);
        });
    }

    @Test
    public void testDeleteWhenLeaveAdminAssignmentServiceDeleteRequestThenThrowException() {
        assertThrows(APIException.class, () -> {
            AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
            IKProperties kProperties = Mockito.mock(IKProperties.class);
            mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(kProperties);
            Mockito.when(leaveAdminAssignmentService.deleteRequest(any(AdminAssignmentRequestBean.class))).thenThrow(new RuntimeException("Error Occurred"));
            restPersonLeaveAdminAssignment.delete(requestBean);
        });
    }

    @Test
    public void testMultiUpdateWhenLeaveAdminAssignmentServiceUpdateRequest() throws Exception {
        AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
        List<AdminAssignmentRequestBean> requestDataList = Lists.newArrayList(requestBean);
        //PowerMockito.whenNew(NewBatchProcessor.class).withAnyArguments().thenReturn(newBatchProcessor);   //Todo
        Mockito.when(newBatchProcessor.process()).thenReturn(requestDataList);
        Personality personality = mock(Personality.class);
        Mockito.when(leaveAdminAssignmentService.updateRequest(any(AdminAssignmentRequestBean.class))).thenReturn(personality);
        List<AdminAssignmentRequestBean> result = restPersonLeaveAdminAssignment.multiUpdate(requestDataList);
        assertEquals(requestDataList, result);
    }

    @Test
    public void testMultiUpdateWhenLeaveAdminAssignmentServiceUpdateRequestThenThrowAPIException() {
        assertThrows(APIException.class, () -> {
        AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
        List<AdminAssignmentRequestBean> requestDataList = Lists.newArrayList(requestBean);
        Mockito.when(leaveAdminAssignmentService.updateRequest(any(AdminAssignmentRequestBean.class))).thenThrow(new APIException("Error Occured"));
        restPersonLeaveAdminAssignment.multiUpdate(requestDataList);
        });
    }

    @Test
    public void testMultiUpdateWhenLeaveAdminAssignmentServiceUpdateRequestThenThrowGenericException() throws GenericException {
        assertThrows(APIException.class, () -> {
        AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
        List<AdminAssignmentRequestBean> requestDataList = Lists.newArrayList(requestBean);
        IKProperties kProperties = Mockito.mock(IKProperties.class);
        mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(kProperties);
        GenericException genericException = Mockito.mock(GenericException.class);
        Mockito.when(leaveAdminAssignmentService.updateRequest(any(AdminAssignmentRequestBean.class))).thenThrow(genericException);
        restPersonLeaveAdminAssignment.multiUpdate(requestDataList);
        });
    }

    @Test
    public void testMultiUpdateWhenLeaveAdminAssignmentServiceUpdateRequestThenThrowException() throws GenericException {
        assertThrows(APIException.class, () -> {
            AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
            List<AdminAssignmentRequestBean> requestDataList = Lists.newArrayList(requestBean);
            IKProperties kProperties = Mockito.mock(IKProperties.class);
            mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(kProperties);
            Mockito.when(leaveAdminAssignmentService.updateRequest(any(AdminAssignmentRequestBean.class))).thenThrow(new RuntimeException("Error Occurred"));
            restPersonLeaveAdminAssignment.multiUpdate(requestDataList);
        });
    }

    @Test
    public void testMultiUpdateWhenEmptyRequest() {
        assertThrows(APIException.class, () -> {
        List<AdminAssignmentRequestBean> requestDataList = Lists.newArrayList();
        restPersonLeaveAdminAssignment.multiUpdate(requestDataList);
        });
    }

    @Test
    public void testMultiDeleteWhenLeaveAdminAssignmentServiceDeleteRequest() {
        AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
        List<AdminAssignmentRequestBean> requestDataList = Lists.newArrayList(requestBean);
        Personality personality = mock(Personality.class);
        Mockito.when(leaveAdminAssignmentService.deleteRequest(any(AdminAssignmentRequestBean.class))).thenReturn(personality);
        restPersonLeaveAdminAssignment.multiDelete(requestDataList);
        verify(leaveAdminAssignmentService, times(requestDataList.size()))
                .deleteRequest(any(AdminAssignmentRequestBean.class));
    }

    @Test
    public void testMultiDeleteWhenLeaveAdminAssignmentServiceDeleteRequestThenThrowAPIException() {
        assertThrows(APIException.class, () -> {
        AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
        List<AdminAssignmentRequestBean> requestDataList = Lists.newArrayList(requestBean);
        Mockito.when(leaveAdminAssignmentService.deleteRequest(any(AdminAssignmentRequestBean.class))).thenThrow(new APIException("Error Occured"));
        restPersonLeaveAdminAssignment.multiDelete(requestDataList);
        });
    }

    @Test
    public void testMultiDeleteWhenEmptyRequest() {
        assertThrows(APIException.class, () -> {
        List<AdminAssignmentRequestBean> requestDataList = Lists.newArrayList();
        restPersonLeaveAdminAssignment.multiDelete(requestDataList);
        });
    }

    @Test
    public void testMultiDeleteWhenLeaveAdminAssignmentServiceDeleteRequestThenThrowGenericException() throws GenericException {
        assertThrows(APIException.class, () -> {
            AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
            List<AdminAssignmentRequestBean> requestDataList = Lists.newArrayList(requestBean);
            IKProperties kProperties = Mockito.mock(IKProperties.class);
            mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(kProperties);
            Mockito.when(leaveAdminAssignmentService.deleteRequest(any(AdminAssignmentRequestBean.class))).thenThrow(new RuntimeException("Error Occurred"));
            restPersonLeaveAdminAssignment.multiDelete(requestDataList);
        });
    }

    @Test
    public void testMultiDeleteWhenLeaveAdminAssignmentServiceDeleteRequestThenThrowException() throws GenericException {
        assertThrows(APIException.class, () -> {
        AdminAssignmentRequestBean requestBean = mock(AdminAssignmentRequestBean.class);
        List<AdminAssignmentRequestBean> requestDataList = Lists.newArrayList(requestBean);
        IKProperties kProperties = Mockito.mock(IKProperties.class);
        mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(kProperties);
        GenericException genericException = Mockito.mock(GenericException.class);
        Mockito.when(leaveAdminAssignmentService.deleteRequest(any(AdminAssignmentRequestBean.class))).thenThrow(genericException);
        restPersonLeaveAdminAssignment.multiDelete(requestDataList);
        });
    }

    private AdminAssignmentRequestBean preparePersonAdminAssignmentRequestBean(Long personId){
        AdminAssignmentRequestBean adminAssignmentRequestBean =  new AdminAssignmentRequestBean();
        PersonIdentityBean personIdentity = new PersonIdentityBean();
        personIdentity.setPersonKey(personId);
        adminAssignmentRequestBean.setPersonIdentity(personIdentity);
        return adminAssignmentRequestBean;
    }

    private AdminAssignmentRequestBean preparePersonAdminAssignmentRequestBean(String personNumber){
        AdminAssignmentRequestBean adminAssignmentRequestBean =  new AdminAssignmentRequestBean();
        PersonIdentityBean personIdentity = new PersonIdentityBean();
        personIdentity.setPersonNumber(personNumber);
        adminAssignmentRequestBean.setPersonIdentity(personIdentity);
        return adminAssignmentRequestBean;
    }

}
