package com.kronos.persons.rest.assignments.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.mock.web.MockHttpServletResponse;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.EmployeeJobPreferencesBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.model.BatchResponseBean;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.PersistenceException;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RestEmployeeJobPreferencesTest {
	@InjectMocks
	private RestEmployeeJobPreferences restEmployeeJobPreferences;
	@Mock
	private EmployeeJobPreferenceService empJobPrefService;
	private EmployeeJobPreferencesBean requestBean;
	@Mock
	private Personality personality;
	@Mock
	private ExtensionSearchCriteria searchCriteria;
	@Mock
	PersonIdentityBeanValidator personIdentityBeanValidator;

	private MockedStatic<KronosProperties> mockedKronosProperties;
	private MockedStatic<ResponseHandler> mockedResponseHandler;
	private MockedStatic<PrsnException> mockedPrsnException;

	@BeforeEach
	public void setUp() throws Exception {
		mockedKronosProperties = Mockito.mockStatic(KronosProperties.class);
		mockedResponseHandler = Mockito.mockStatic(ResponseHandler.class);
		mockedPrsnException = Mockito.mockStatic(PrsnException.class);
		requestBean = new EmployeeJobPreferencesBean();
	}

	@AfterEach
	public void tearDown() throws Exception {
		mockedKronosProperties.close();
		mockedResponseHandler.close();
		mockedPrsnException.close();
	}

	@Test
	public void testUpdateShouldPassSuccessFully() {
		String personNumber = "1234";
		requestBean.setPersonNumber(personNumber);
		Mockito.when(personality.getPersonNumber()).thenReturn("1234");
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(10L));
		MockHttpServletResponse httpResponse = new MockHttpServletResponse();
		Mockito.when(empJobPrefService.updateRequest(Mockito.any(EmployeeJobPreferencesBean.class),Mockito.anyBoolean())).thenReturn(personality);
		EmployeeJobPreferencesBean responseBean = restEmployeeJobPreferences.update(requestBean,false);
		assertNull(responseBean);
	}
	
	@SuppressWarnings("unchecked")
	@Test
	public void testUpdateShouldThrowExceptionForUpdateRequest() {
		Mockito.when(empJobPrefService.updateRequest(Mockito.any(EmployeeJobPreferencesBean.class),Mockito.anyBoolean())).thenThrow(APIException.class);
		EmployeeJobPreferencesBean responseBean =null;
		try {
			responseBean = restEmployeeJobPreferences.update(requestBean,false);
		} catch (Exception e) {
			assertNull(responseBean);
		}
	}
	
	@SuppressWarnings("unchecked")
	@Test
	public void testMultiUpdateShouldPassSuccessfully() {
		//Mockito.mockStatic(KronosProperties.class);
		Mockito.when(KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("10");
		List<EmployeeJobPreferencesBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);
		Personality personality = Mockito.mock(Personality.class);
		Mockito.when(empJobPrefService.updateRequest(Mockito.any(EmployeeJobPreferencesBean.class),Mockito.anyBoolean())).thenReturn(personality);
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(10L));
		Mockito.when(personality.getPersonNumber()).thenReturn("1024");
		List<EmployeeJobPreferencesBean> responseList = restEmployeeJobPreferences.multiUpdate(requestDataList,false);
		assertNotNull(responseList);
		assertEquals(1, responseList.size());
	}
	
	@Test
	public void testRetrieveShouldPassSuccessfully() {
		Mockito.when(empJobPrefService.retrieve(Mockito.any(), Mockito.anyBoolean())).thenReturn(requestBean);
		assertEquals(requestBean, restEmployeeJobPreferences.retrieve("12345", null));
		verify(empJobPrefService).retrieve(Mockito.any(), eq(false));
	}
	
	@Test
	public void testRetrieveListshouldPassSuccussfully() {
		mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("10");
		List<String> requestDataList = new ArrayList<>();
		requestDataList.add("12345");
		List<EmployeeJobPreferencesBean> responseListfromfunction = new ArrayList<>();
		EmployeeJobPreferencesBean bean = new EmployeeJobPreferencesBean();
		Mockito.when(empJobPrefService.multiRead(searchCriteria)).thenReturn(responseListfromfunction);
		List<EmployeeJobPreferencesBean> responseList = restEmployeeJobPreferences.multiRead(searchCriteria);
		responseListfromfunction.add(bean);
		assertEquals(1, responseList.size());
	}
	
	@SuppressWarnings("unchecked")
	@Test
	public void testRetrieveListshouldThrowExceptionForMultiRetrive() {
		mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("10");
		Mockito.when(empJobPrefService.multiRead(Mockito.any())).thenThrow(APIException.class);
		try {
			restEmployeeJobPreferences.multiRead(searchCriteria);
			fail();
		} catch (APIException apiEx){
		}
	}
	
	@Test
	public void testMultiUpdateShouldThrowExceptionForMultiUpdate() {
		mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("10");
		List<EmployeeJobPreferencesBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);
		Personality personality = Mockito.mock(Personality.class);
		Mockito.when(empJobPrefService.updateRequest(Mockito.any(EmployeeJobPreferencesBean.class),Mockito.anyBoolean())).thenReturn(personality);
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(10L));
		Mockito.when(personality.getPersonNumber()).thenReturn("1024");
		BatchResponseBean responseBean1 = new BatchResponseBean();
		responseBean1.setErrorCode(ExceptionConstants.EXCEPTION_101205);
		mockedResponseHandler.when(() -> ResponseHandler.apiSuccessResponse(Mockito.any(),Mockito.any())).thenReturn(responseBean1);
		assertThrows(Exception.class,() -> {restEmployeeJobPreferences.multiUpdate(requestDataList,null);});

	}
	
	@Test
	public void testRetrievebyPersonIdShouldPassSuccessfully() {
		Mockito.when(empJobPrefService.retrieve(Mockito.any())).thenReturn(requestBean);
		Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any())).thenReturn(personality);
		assertEquals(requestBean, restEmployeeJobPreferences.retrievebyPersonId(10L));
	}
	
	@Test
	public void testRetrievebyPersonIdShouldThrowException() {
		assertThrows(APIException.class,()->{
			Mockito.when(empJobPrefService.retrieve(Mockito.any())).thenReturn(requestBean);
			Mockito.doThrow(new APIException()).when(personIdentityBeanValidator).getPersonality(Mockito.any());
			restEmployeeJobPreferences.retrievebyPersonId(10L);
		});

	}
	
	@Test
	public void testUpdateShouldThrowException() {
		Mockito.doThrow(new RuntimeException()).when(empJobPrefService).updateRequest(Mockito.any(),Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restEmployeeJobPreferences.update(requestBean,true);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}

	@Test
	public void testUpdateShouldThrowGenericException() {
		Mockito.doThrow(new PersistenceException()).when(empJobPrefService).updateRequest(Mockito.any(),Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		try {
			restEmployeeJobPreferences.update(requestBean,true);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	
	@Test
	public void testMultiUpdateShouldThrowGenericException() {
		List<EmployeeJobPreferencesBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);
		Mockito.doThrow(new PersistenceException()).when(empJobPrefService).updateRequest(Mockito.any(),Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		try {
			restEmployeeJobPreferences.multiUpdate(requestDataList,true);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testMultiUpdateShouldThrowException() {
		List<EmployeeJobPreferencesBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);
		Mockito.doThrow(new RuntimeException()).when(empJobPrefService).updateRequest(Mockito.any(),Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restEmployeeJobPreferences.multiUpdate(requestDataList,true);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testMultiUpdateShouldThrowAPIException() {
		List<EmployeeJobPreferencesBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);
		Mockito.doThrow(new APIException()).when(empJobPrefService).updateRequest(Mockito.any(),Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(APIException.class))).thenReturn(new APIException());
		try {
			restEmployeeJobPreferences.multiUpdate(requestDataList,true);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}

	@Test
	public void testUpdateWhenUpdatePartialIsNull(){
		String personNumber = "1234";
		requestBean.setPersonNumber(personNumber);
		Mockito.when(personality.getPersonNumber()).thenReturn("1234");
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(10L));
		Mockito.when(empJobPrefService.updateRequest(Mockito.any(EmployeeJobPreferencesBean.class),Mockito.anyBoolean())).thenReturn(personality);
		EmployeeJobPreferencesBean responseBean = restEmployeeJobPreferences.update(requestBean,null);
		assertNull(responseBean);
	}

	@Test
	public void testRetrieveByPersonIdThrowAPIException() {
		Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any())).thenThrow(APIException.class);
		assertThrows(APIException.class,()->{restEmployeeJobPreferences.retrievebyPersonId(null);});
	}

	@Test
	public void testRetrieveForFullPath() {
		Mockito.when(empJobPrefService.retrieve(Mockito.any(), Mockito.anyBoolean())).thenReturn(requestBean);
		assertEquals(requestBean, restEmployeeJobPreferences.retrieve("12345", true));
		verify(empJobPrefService).retrieve(Mockito.any(), eq(true));
	}
}
