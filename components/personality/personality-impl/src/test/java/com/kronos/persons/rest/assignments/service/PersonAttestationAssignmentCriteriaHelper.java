package com.kronos.persons.rest.assignments.service;

import com.kronos.persons.rest.assignments.model.PersonAttestationAssignmentCriteria;

public class PersonAttestationAssignmentCriteriaHelper {

    public static PersonAttestationAssignmentCriteria buildCriteria(){
        return buildCriteria(true, true);
    }

    public static PersonAttestationAssignmentCriteria buildCriteria(boolean includeManagerRole, boolean includeEmployeeRole){
        PersonAttestationAssignmentCriteria personAttestationAssignmentCriteria = new PersonAttestationAssignmentCriteria();
        personAttestationAssignmentCriteria.setIncludeManagerRole(includeManagerRole);
        personAttestationAssignmentCriteria.setIncludeEmployeeRole(includeEmployeeRole);
        return personAttestationAssignmentCriteria;
    }
}
