package com.kronos.persons.rest.assignments.validation;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.time.LocalDate;
import java.util.*;

import com.kronos.persons.rest.exception.PrsnInvalidBeanException;
import com.kronos.wfc.commonapp.people.business.person.PersonLicenseTypeSet;
import com.kronos.wfc.platform.audit.business.types.AuditType;
import com.kronos.wfc.platform.security.business.authorization.profiles.AccessProfile;
import com.kronos.wfc.platform.security.shared.SecurityConstants;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.util.PersonalityHelper;
import com.kronos.persons.rest.assignments.model.AttestationProfileAssignment;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignment;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignmentDTO;
import com.kronos.persons.rest.beans.HyperFindFilterBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import static com.kronos.persons.rest.exception.ExceptionConstants.EMPTY_MULTI_UPDATE_REQUEST;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileDTO;
import com.kronos.timekeeping.service.attestation.api.service.AttestationProfileSetupService;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonAttestationProfileAssignmentValidatorTest {
    private static final Long DEFAULT_PERSON_ID = 4L;
    private static final Long DEFAULT_PROFILE_ID = 5L;
    private static final String DEFAULT_PERSON_NUMBER = "444";
    private static final String DEFAULT_PROFILE_NAME = "Some Profile Name";

    @Mock
    private PersonIdentityBeanValidator personIdentityBeanValidator;

    @Mock
    private AttestationProfileSetupService attestationProfileSetupService;

    @Mock
    private Personality personality;

    @Mock PersonalityHelper personalityHelper;

    @Mock EmployeeExtension employeeExtension;

    @Mock HyperFindFilterBean hyperFindFilterBean;

    @InjectMocks
    private PersonAttestationProfileAssignmentValidator validator;

    private MockedStatic<AuditType> auditTypeMock;
    private MockedStatic<AccessProfile> accessProfileMock;

    @BeforeEach
    public void setUp() {
        validator.setAttestationProfileSetupService(attestationProfileSetupService);

        ObjectIdLong personId = new ObjectIdLong(DEFAULT_PERSON_ID);
        when(personality.getPersonId()).thenReturn(personId);

        when(personIdentityBeanValidator.createForPersonNumber(anyString()))
                .thenAnswer(i -> {
                    String personNumber = (String) i.getArguments()[0];
                    PersonIdentityBean personIdentity = new PersonIdentityBean();
                    personIdentity.setPersonNumber(personNumber);
                    return personIdentity;
                });

        when(personIdentityBeanValidator.getPersonality(any(PersonIdentityBean.class))).thenReturn(personality);

        auditTypeMock = Mockito.mockStatic(AuditType.class);
        accessProfileMock = Mockito.mockStatic(AccessProfile.class);
    }

    @AfterEach
    public void tearDown() {
        auditTypeMock.close();
        accessProfileMock.close();
        Mockito.clearAllCaches();
        Mockito.framework().clearInlineMocks();
    }

    @Test
    public void shouldValidateReadAccess() {
        Long personId = 4L;
        PersonIdentityBean personIdentity = new PersonIdentityBean();
        personIdentity.setPersonKey(personId);

        when(personIdentityBeanValidator.buildPersonIdentity(personId)).thenReturn(personIdentity);
        when(personIdentityBeanValidator.getPersonality(personIdentity)).thenReturn(new Personality());

        validator.validateReadAccess(personId);

        verify(personIdentityBeanValidator).buildPersonIdentity(personId);
        verify(personIdentityBeanValidator).getPersonality(personIdentity);
    }

    @Test
    public void shouldValidateReadAccessAndGetPersonId() {
        validator.validateReadAccessAndGetPersonId(DEFAULT_PERSON_NUMBER);

        verify(personIdentityBeanValidator, times(1)).getPersonality(any(PersonIdentityBean.class));
    }

    @Test
    public void shouldNotThrowExceptionOnValidateCreateAccessWithValidDTOContainingProfileId() {
        PersonAttestationProfileAssignmentDTO dto = new PersonAttestationProfileAssignmentDTO();

        dto.setEffectiveDate(LocalDate.now());
        dto.setProfile(new ObjectRef(DEFAULT_PROFILE_ID, DEFAULT_PROFILE_NAME));

        AttestationProfileDTO profileDTO = new AttestationProfileDTO();
        profileDTO.setId(DEFAULT_PROFILE_ID);
        profileDTO.setName(DEFAULT_PROFILE_NAME);

        when(attestationProfileSetupService.getAttestationProfile(dto.getProfile().getId())).thenReturn(profileDTO);

        validator.validateCreateAccess(DEFAULT_PERSON_ID, dto);

        verify(personIdentityBeanValidator, times(1)).getPersonality(any(PersonIdentityBean.class));

        verify(attestationProfileSetupService, times(1)).getAttestationProfile(dto.getProfile().getId());
    }

    @Test
    public void shouldNotThrowExceptionOnValidateCreateAccessWithValidDTOContainingProfileName() {
        PersonAttestationProfileAssignmentDTO dto = new PersonAttestationProfileAssignmentDTO();

        dto.setEffectiveDate(LocalDate.now());
        dto.setProfile(new ObjectRef(null, DEFAULT_PROFILE_NAME));

        AttestationProfileDTO profileDTO = new AttestationProfileDTO();
        profileDTO.setId(DEFAULT_PROFILE_ID);
        profileDTO.setName(DEFAULT_PROFILE_NAME);

        doNothing().when(attestationProfileSetupService)
                .checkAttestationProfileExists(dto.getProfile().getQualifier());
        validator.validateCreateAccess(DEFAULT_PERSON_ID, dto);

        verify(personIdentityBeanValidator, times(1)).getPersonality(any(PersonIdentityBean.class));

        verify(attestationProfileSetupService, times(1)).checkAttestationProfileExists(dto.getProfile().getQualifier());
    }

    @Test
    public void shouldThrowExceptionOnValidateCreateAccessWithDTOContainingEmptyProfile() {
        PersonAttestationProfileAssignmentDTO dto = new PersonAttestationProfileAssignmentDTO();

        dto.setEffectiveDate(LocalDate.now());
        dto.setProfile(new ObjectRef(null, null));

        AttestationProfileDTO profileDTO = new AttestationProfileDTO();
        profileDTO.setId(DEFAULT_PROFILE_ID);
        profileDTO.setName(DEFAULT_PROFILE_NAME);

        APIException exception = assertThrows(APIException.class, () -> {
            validator.validateCreateAccess(DEFAULT_PERSON_ID, dto);
        });

        assertEquals(null, exception.getMessage());
    }

    @Test
    public void shouldThrowExceptionOnValidateCreateAccessWithDTOContainingInvalidProfileId() {
        PersonAttestationProfileAssignmentDTO dto = new PersonAttestationProfileAssignmentDTO();
        dto.setEffectiveDate(LocalDate.now());
        dto.setProfile(new ObjectRef(999L, null));

        when(attestationProfileSetupService.getAttestationProfile(dto.getProfile().getId()))
                .thenThrow(new APIException("Expected error message"));

        APIException exception = assertThrows(APIException.class, () -> {
            validator.validateCreateAccess(DEFAULT_PERSON_ID, dto);
        });

        assertEquals(null, exception.getMessage());
    }

    @Test
    public void shouldThrowExceptionOnValidateCreateAccessWithDTOContainingInvalidProfileName() {
        PersonAttestationProfileAssignmentDTO dto = new PersonAttestationProfileAssignmentDTO();

        dto.setEffectiveDate(LocalDate.now());
        dto.setProfile(new ObjectRef(null, "Invalid Name"));

        AttestationProfileDTO profileDTO = new AttestationProfileDTO();
        profileDTO.setId(DEFAULT_PROFILE_ID);
        profileDTO.setName(DEFAULT_PROFILE_NAME);

        doThrow(new APIException()).when(attestationProfileSetupService).checkAttestationProfileExists(dto.getProfile().getQualifier());

        APIException exception = assertThrows(APIException.class, () -> {
            validator.validateCreateAccess(DEFAULT_PERSON_ID, dto);
        });

        assertEquals(null, exception.getMessage());
    }

    @Test
    public void shouldThrowExceptionOnValidateCreateAccessWithDTOContainingMissingEffectiveDate() {
        PersonAttestationProfileAssignmentDTO dto = new PersonAttestationProfileAssignmentDTO();
        dto.setEffectiveDate(null);
        dto.setProfile(new ObjectRef(DEFAULT_PROFILE_ID, null));

        PrsnInvalidBeanException multiException = new PrsnInvalidBeanException();
        multiException.addApiExceptionToList(new APIException("Expected error message"));

        doThrow(multiException.getApiException()).when(personIdentityBeanValidator).validatePersonId(anyLong(), any(), any());

        APIException exception = assertThrows(APIException.class, () -> {
            validator.validateCreateAccess(DEFAULT_PERSON_ID, dto);
        });

        assertEquals(null, exception.getMessage());
    }

    @Test
    public void shouldNotThrowExceptionOnValidateCreateAccessAndGetPersonIdWithValidDTO() {
        PersonAttestationProfileAssignmentDTO dto = new PersonAttestationProfileAssignmentDTO();

        dto.setEffectiveDate(LocalDate.now());
        dto.setProfile(new ObjectRef(DEFAULT_PROFILE_ID, DEFAULT_PROFILE_NAME));

        dto.setPerson(new ObjectRef(null, DEFAULT_PERSON_NUMBER));

        AttestationProfileDTO profileDTO = new AttestationProfileDTO();
        profileDTO.setId(DEFAULT_PROFILE_ID);
        profileDTO.setName(DEFAULT_PROFILE_NAME);

        when(attestationProfileSetupService.getAttestationProfile(dto.getProfile().getId())).thenReturn(profileDTO);

        validator.validateCreateAccessAndGetPersonId(dto);

        verify(personIdentityBeanValidator, times(1)).getPersonality(any(PersonIdentityBean.class));

        verify(attestationProfileSetupService, times(1)).getAttestationProfile(dto.getProfile().getId());
    }

    @Test
    public void shouldThrowExceptionOnValidateCreateAccessAndGetPersonIdWithDTOContainingMissingPerson() {
        PersonAttestationProfileAssignmentDTO dto = new PersonAttestationProfileAssignmentDTO();

        dto.setEffectiveDate(null);
        dto.setProfile(new ObjectRef(DEFAULT_PROFILE_ID, null));

        APIException exception = assertThrows(APIException.class, () -> {
            validator.validateCreateAccessAndGetPersonId(dto);
        });

        assertEquals(null, exception.getMessage());
    }

    @Test
    public void shouldThrowExceptionOnValidateCreateAccessAndGetPersonIdWithDTOContainingEmptyPerson() {
        PersonAttestationProfileAssignmentDTO dto = new PersonAttestationProfileAssignmentDTO();

        dto.setEffectiveDate(null);
        dto.setProfile(new ObjectRef(null, null));

        AttestationProfileDTO profileDTO = new AttestationProfileDTO();
        profileDTO.setId(DEFAULT_PROFILE_ID);
        profileDTO.setName(DEFAULT_PROFILE_NAME);

        APIException exception = assertThrows(APIException.class, () -> {
            validator.validateCreateAccessAndGetPersonId(dto);
        });

        assertEquals(null, exception.getMessage());
    }

    @Test
    public void shouldThrowExceptionOnWhenUserCannotViewTimekeepManagerRole() {
        mockAccessProfiles(false);

        APIException exception = assertThrows(APIException.class, () -> {
            validator.validateManagerRoleReadFAP(Boolean.TRUE);
        });

        assertEquals(null, exception.getMessage());
    }

    @Test
    public void shouldThrowExceptionOnWhenUserCannotUpdateTimekeepManagerRole() {
        mockAccessProfiles(false);

        APIException exception = assertThrows(APIException.class, () -> {
            validator.validateManagerRoleWriteFAP();
        });

        assertEquals(null, exception.getMessage());
    }

    @Test
    public void shouldThrowExceptionOnWhenPersonNotHaveManagerRoleLicense() {
        mockAccessProfiles(true);
        mockManagerLicense(false);

        PersonAttestationProfileAssignmentDTO dto = new PersonAttestationProfileAssignmentDTO();
        dto.setEffectiveDate(LocalDate.now().minusDays(1));
        dto.setExpirationDate(LocalDate.now().plusDays(1));
        dto.setProfile(new ObjectRef(1L));
        dto.setPerson(new ObjectRef(DEFAULT_PERSON_ID));
        dto.setAssignToManagerRole(true);

        APIException exception = assertThrows(APIException.class, () -> {
            validator.validateCreateAccess(DEFAULT_PERSON_ID, dto);
        });

        assertEquals(null, exception.getMessage());
    }

    private void mockManagerLicense(boolean answer) {
        PersonLicenseTypeSet licenseTypeSet = mock(PersonLicenseTypeSet.class);
        when(licenseTypeSet.hasManagerLicense()).thenReturn(answer);
        when(personality.getLicenseTypes()).thenReturn(licenseTypeSet);
    }

    @Test
    public void testHasManagerLicense() {
        mockManagerLicense(true);
        assertFalse(validator.hasManagerLicense(DEFAULT_PERSON_ID));
    }

    private void mockAccessProfiles(boolean answer) {
        Mockito.when(AuditType.getAuditType(anyString())).thenReturn(null);
        Mockito.when(AccessProfile.isPermitted(eq(SecurityConstants.TIMEKEEPER_MANAGER_ROLE_VIEW), any())).thenReturn(answer);
    }

    @Test
    public void validateReadAccessAndGetObjectRefsByPersonIds_null() {
        Map<Long, Object> actual = validator.validateReadAccessAndGetObjectRefsByPersonIds(null, null);
        assertEquals(0, actual.size());
    }

    @Test
    public void validateReadAccessAndGetObjectRefsByPersonIds_emptyList() {
        Map<Long, Object> actual = validator.validateReadAccessAndGetObjectRefsByPersonIds(Collections.emptyList(), hyperFindFilterBean);
        assertEquals(0, actual.size());
    }

    @Test
    public void validateReadAccessAndGetObjectRefsByPersonIds() {
        List<Long> ids = Arrays.asList(1L, 2L);

        Map<Long, EmployeeExtension> existingPersons = new HashMap<>();
        existingPersons.put(1L, employeeExtension);

        when(personalityHelper.getPersonNumbersByEmployeeIds(ids)).thenReturn(existingPersons);
        when(personIdentityBeanValidator.validatePersonId(1L, employeeExtension, hyperFindFilterBean)).thenReturn(new ObjectRef());

        Map<Long, Object> actual = validator.validateReadAccessAndGetObjectRefsByPersonIds(ids, hyperFindFilterBean);
        assertEquals(2, actual.size());

        verify(personIdentityBeanValidator, times(1)).validatePersonId(2L, null, hyperFindFilterBean);
    }

    @Test
    public void validateReadAccessAndGetObjectRefsByPersonIdsMap_null() {
        Map<Long, Object> actual = validator.validateReadAccessAndGetObjectRefsByPersonIds(null);
        assertEquals(0, actual.size());
    }

    @Test
    public void validateReadAccessAndGetObjectRefsByPersonIdsMap_emptyList() {
        Map<Long, Object> actual = validator.validateReadAccessAndGetObjectRefsByPersonIds(Collections.emptyMap());
        assertEquals(0, actual.size());
    }

    @Test
    public void validateReadAccessAndGetObjectRefsByPersonIdsMap() {
        Map<Long, HyperFindFilterBean> personsWithFilters = new HashMap<>();
        personsWithFilters.put(1L, hyperFindFilterBean);
        personsWithFilters.put(2L, hyperFindFilterBean);

        Map<Long, EmployeeExtension> existingPersons = new HashMap<>();
        existingPersons.put(1L, employeeExtension);

        when(personalityHelper.getPersonNumbersByEmployeeIds(anyList())).thenReturn(existingPersons);
        when(personIdentityBeanValidator.validatePersonId(1L, employeeExtension, hyperFindFilterBean)).thenReturn(new ObjectRef());

        Map<Long, Object> actual = validator.validateReadAccessAndGetObjectRefsByPersonIds(personsWithFilters);
        assertEquals(2, actual.size());

        verify(personIdentityBeanValidator, times(1)).validatePerson(eq(employeeExtension), eq(hyperFindFilterBean), any(), any(Boolean.class));
    }

    @Test
    public void validateReadAccessAndGetObjectRefsByPersonNums_null() {
        Map<String, Object> actual = validator.validateReadAccessAndGetObjectRefsByPersonNums(null, null);
        assertEquals(0, actual.size());
    }

    @Test
    public void validateReadAccessAndGetObjectRefsByPersonNums_emptyList() {
        Map<String, Object> actual = validator.validateReadAccessAndGetObjectRefsByPersonNums(Collections.emptyList(), hyperFindFilterBean);
        assertEquals(0, actual.size());
    }

    @Test
    public void validateReadAccessAndGetObjectRefsByPersonNums() {
        List<String> nums = Arrays.asList("num1", "num2");

        Map<String, EmployeeExtension> existingPersons = new HashMap<>();
        existingPersons.put("num2", employeeExtension);

        when(personalityHelper.getPersonIdsByPersonNumbers(any())).thenReturn(existingPersons);
        when(personIdentityBeanValidator.validatePersonNumber("num2", employeeExtension, hyperFindFilterBean)).thenReturn(new ObjectRef());

        Map<String, Object> actual = validator.validateReadAccessAndGetObjectRefsByPersonNums(nums, hyperFindFilterBean);
        assertEquals(2, actual.size());

        verify(personIdentityBeanValidator, times(1)).validatePersonNumber("num1", null, hyperFindFilterBean);
    }

    @Test
    public void validateReadAccessAndGetObjectRefsByPersonNumsMap_null() {
        Map<String, Object> actual = validator.validateReadAccessAndGetObjectRefsByPersonNums(null);
        assertEquals(0, actual.size());
    }

    @Test
    public void validateReadAccessAndGetObjectRefsByPersonNumsMap_emptyList() {
        Map<String, Object> actual = validator.validateReadAccessAndGetObjectRefsByPersonNums(Collections.emptyMap());
        assertEquals(0, actual.size());
    }

    @Test
    public void validateReadAccessAndGetObjectRefsByPersonNumsMap() {
        Map<String, HyperFindFilterBean> nums = new HashMap<>();
        nums.put("num1", hyperFindFilterBean);
        nums.put("num2", hyperFindFilterBean);

        Map<String, EmployeeExtension> existingPersons = new HashMap<>();
        existingPersons.put("num2", employeeExtension);

        when(personalityHelper.getPersonIdsByPersonNumbers(any())).thenReturn(existingPersons);
        when(personIdentityBeanValidator.validatePersonNumber("num2", employeeExtension, hyperFindFilterBean)).thenReturn(new ObjectRef());

        Map<String, Object> actual = validator.validateReadAccessAndGetObjectRefsByPersonNums(nums);
        assertEquals(2, actual.size());

        verify(personIdentityBeanValidator, times(1)).validatePerson(eq(employeeExtension), eq(hyperFindFilterBean), any(), any(Boolean.class));
    }

    @Test
    public void validateReadAccessAndGetObjectRefsByPersonNumsMap_invokeAccessEveryOnlyOnce() {
        Map<String, HyperFindFilterBean> nums = new HashMap<>();
        nums.put("num1", hyperFindFilterBean);
        nums.put("num2", hyperFindFilterBean);

        Map<String, EmployeeExtension> existingPersons = new HashMap<>();
        existingPersons.put("num2", employeeExtension);

        when(personalityHelper.getPersonIdsByPersonNumbers(any())).thenReturn(existingPersons);
        when(personIdentityBeanValidator.validatePersonNumber("num2", employeeExtension, hyperFindFilterBean)).thenReturn(new ObjectRef());

        Map<String, Object> actual = validator.validateReadAccessAndGetObjectRefsByPersonNums(nums);
        assertEquals(2, actual.size());

        verify(personIdentityBeanValidator, times(1)).hasAccessToEveryone();
    }

    @Test
	public void validatePersonAttestationProfileAssignments() {
		Map<Integer, APIException> exceptionHolder = new HashMap<>();
		Map<Integer, PersonAttestationProfileAssignment> nums = new HashMap<>();
		
		PersonAttestationProfileAssignment person1 = createPersonAttestationProfileAssignmentWithEffectiveDate("2020-05-01",new ObjectRef(123L, "Test1"),new ObjectRef(1234L, "Profile1"));
		
		PersonAttestationProfileAssignment person2 = createPersonAttestationProfileAssignmentWithEffectiveDate(null,new ObjectRef(456L, "Test2"),new ObjectRef(4567L, "Profile2"));

		PersonAttestationProfileAssignment person3 = createPersonAttestationProfileAssignmentWithEffectiveDate("2020-05-30",new ObjectRef(789L, "Test3"),new ObjectRef(7890L, "Profile3"));
		// person1-> has effectivedate, person 2-> doesnt have effectivedate and
		// person3-> has effective date, so there should be 2 persons returned as 1 is
		// not having effectivedate and will fail validation
		nums.put(1, person1);
		nums.put(2, person2);
		nums.put(3, person3);
		validator.validatePersonAttestationProfileAssignments(exceptionHolder, nums);
		// 2 should be size of nums as person2 has failed validation
		assertEquals(2, nums.size());
	}

    @Test
    public void validateManagerPersonAttestationProfileAssignmentsNoManagerLicense() {
        Map<Integer, APIException> exceptionHolder = new HashMap<>();
        Map<Integer, PersonAttestationProfileAssignment> nums = new HashMap<>();

        PersonAttestationProfileAssignment person1 = createManagerPersonAttestationProfileAssignmentWithEffectiveDate("2020-05-01",
                new ObjectRef(123L, "Test1"),new ObjectRef(1234L, "Profile1"));
        nums.put(1, person1);
        mockAccessProfiles(true);
        validator.validatePersonAttestationProfileAssignments(exceptionHolder, nums);
        // 2 should be size of nums as person2 has failed validation
        assertEquals(0, nums.size());
    }

    @Test
    public void validateManagerPersonAttestationProfileAssignments() {
        Map<Integer, APIException> exceptionHolder = new HashMap<>();
        Map<Integer, PersonAttestationProfileAssignment> nums = new HashMap<>();

        PersonAttestationProfileAssignment person1 = createManagerPersonAttestationProfileAssignmentWithEffectiveDate("2020-05-01",
                new ObjectRef(123L, "Test1"), new ObjectRef(1234L, "Profile1"));
        nums.put(1, person1);

        mockAccessProfiles(true);
        mockManagerLicense(true);

        validator.validatePersonAttestationProfileAssignments(exceptionHolder, nums);

        assertEquals(0, nums.size());
    }


    @Test
    public void validatePersonAttestationProfileAssignmentsForeffectivedate() {
        Map<Integer, APIException> exceptionHolder = new HashMap<>();
        Map<Integer, PersonAttestationProfileAssignment> nums = new HashMap<>();

        PersonAttestationProfileAssignment person1 = createPersonAttestationProfileAssignmentWithEffectiveDate("3020-01-01",new ObjectRef(123L, "Test1"),new ObjectRef(1234L, "Profile1"));
        PersonAttestationProfileAssignment person2 = createPersonAttestationProfileAssignmentWithEffectiveDate("2020-05-01",new ObjectRef(456L, "Test2"),new ObjectRef(4567L, "Profile2"));
        PersonAttestationProfileAssignment person3 = createPersonAttestationProfileAssignmentWithEffectiveDate("2020-05-30",new ObjectRef(789L, "Test3"),new ObjectRef(7890L, "Profile3"));

        nums.put(1, person1);
        nums.put(2, person2);
        nums.put(3, person3);
        validator.validatePersonAttestationProfileAssignments(exceptionHolder, nums);
        // 2 should be size of nums as person2 has failed validation
        assertEquals(2, nums.size());
    }

    @Test
    public void validatePersonAttestationProfileAssignmentsWithNullProfile() {
        Map<Integer, APIException> exceptionHolder = new HashMap<>();
        Map<Integer, PersonAttestationProfileAssignment> nums = new HashMap<>();

        PersonAttestationProfileAssignment person = createPersonAttestationProfileAssignmentWithEffectiveDate("2020-01-01",new ObjectRef(123L, "Test1"),new ObjectRef(1234L, "Profile1"));
        List<AttestationProfileAssignment> listWithNull = new ArrayList<>();
        listWithNull.add(null);
        person.setAttestationProfileAssignments(listWithNull);

        nums.put(1, person);

        try {
            validator.validatePersonAttestationProfileAssignments(exceptionHolder, nums);
            // should not succeed should throw error
            fail("validatePersonAttestationProfileAssignmentsWithNullProfile failed");
        } catch (APIException e) {
            assertEquals(EMPTY_MULTI_UPDATE_REQUEST, e.getErrorCode());
        }
    }

	private PersonAttestationProfileAssignment createPersonAttestationProfileAssignmentWithEffectiveDate(String effectivaDate, ObjectRef employee, ObjectRef profile) {
		PersonAttestationProfileAssignment person = new PersonAttestationProfileAssignment();
		List<AttestationProfileAssignment> attsetationProfile = new ArrayList<AttestationProfileAssignment>();
		AttestationProfileAssignment apa = new AttestationProfileAssignment();
		apa.setEffectiveDate(effectivaDate);
		apa.setProfile(profile);
		attsetationProfile.add(apa);	
		person.setEmployee(employee);
		person.setAttestationProfileAssignments(attsetationProfile);
		return person;
	}

    private PersonAttestationProfileAssignment createManagerPersonAttestationProfileAssignmentWithEffectiveDate(String effectivaDate, ObjectRef employee, ObjectRef profile) {
        PersonAttestationProfileAssignment person = new PersonAttestationProfileAssignment();
        List<AttestationProfileAssignment> attsetationProfile = new ArrayList<>();
        AttestationProfileAssignment apa = new AttestationProfileAssignment();
        apa.setEffectiveDate(effectivaDate);
        apa.setProfile(profile);
        attsetationProfile.add(apa);
        person.setEmployee(employee);
        person.setManagerRoleAttestationProfileAssignments(attsetationProfile);
        return person;
    }
}
