package com.kronos.persons.rest.assignments.service;

import com.google.common.collect.Lists;
import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.access.SpringContext;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.SupervisorAssignmentRequestBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.utils.NewBatchProcessor;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.properties.framework.KronosPropertiesIfc;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.mockito.Mockito.times;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RestPersonSupervisorAssignmentTest {

    @InjectMocks
    private RestPersonSupervisorAssignment restPersonSupervisorAssignment;

    @Mock
    private PersonSupervisorAssignmentService personSupervisorAssignmentService;

    @Mock
    private PersonAssignmentHelper<SupervisorAssignmentRequestBean> personAssignmentHelper;

    @Mock
    private NewBatchProcessor<SupervisorAssignmentRequestBean, SupervisorAssignmentRequestBean> newBatchProcessor;

    private final Long personId = 1L;

    private MockedStatic<SpringContext> springContextMockedStatic;
    private MockedStatic<ResponseHandler> responseHandlerMockedStatic;

    @BeforeEach
    public void setUp() {
        springContextMockedStatic = Mockito.mockStatic(SpringContext.class);
        responseHandlerMockedStatic = Mockito.mockStatic(ResponseHandler.class);
    }

    @AfterEach
    public void tearDown() {
        springContextMockedStatic.close();
        responseHandlerMockedStatic.close();
    }

    @Test
    public void testRetrieve() {
        SupervisorAssignmentRequestBean supervisorAssignmentRequestBeanForPrepare = preparePersonSupervisorAssignmentRequestBean(personId);
        Mockito.when(personSupervisorAssignmentService.retrieve(any())).thenReturn(supervisorAssignmentRequestBeanForPrepare);
        SupervisorAssignmentRequestBean supervisorAssignmentRequestBean = restPersonSupervisorAssignment.retrieve(personId);
        assertEquals(supervisorAssignmentRequestBean.getPersonIdentity().getPersonKey(), supervisorAssignmentRequestBeanForPrepare.getPersonIdentity().getPersonKey());
    }

    @Test
    public void testRetrieveThrowAPIExceptionWhenCatchAPIException() {
        assertThrows(APIException.class, () -> {
            Mockito.when(personSupervisorAssignmentService.retrieve(any())).thenThrow(APIException.class);
            restPersonSupervisorAssignment.retrieve(personId);
        });
    }

    @Test
    public void testRetrieveThrowAPIExceptionWhenCatchException() {
        assertThrows(APIException.class, () -> {
            Mockito.when(personSupervisorAssignmentService.retrieve(any())).thenThrow(RuntimeException.class);
            restPersonSupervisorAssignment.retrieve(personId);
        });
    }

    @Test
    public void testRetrieveByGenericException() {
        assertThrows(APIException.class, () -> {
            IKProperties kProperties = Mockito.mock(IKProperties.class);
            springContextMockedStatic.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(kProperties);
            GenericException genericException = Mockito.mock(GenericException.class);
            Mockito.when(personSupervisorAssignmentService.retrieve(any())).thenThrow(genericException);
            restPersonSupervisorAssignment.retrieve(personId);
        });
    }

    @Test
    public void testRetrieveList() {
        Function<PersonIdentityBean, SupervisorAssignmentRequestBean> getDataListFunction = (Function<PersonIdentityBean, SupervisorAssignmentRequestBean>) personSupervisorAssignmentService.retrieve(any());
        ExtensionSearchCriteria searchCriteria = new ExtensionSearchCriteria();
        List<SupervisorAssignmentRequestBean> supervisorAssignmentRequestBeans = Lists.newArrayList();
        restPersonSupervisorAssignment.retrieveList(searchCriteria);
        Mockito.verify(personAssignmentHelper, times(1)).getPersonAssignmentList(any(), any());
    }

    @Test
    public void testMultiUpsertException() {
        assertThrows(APIException.class, () -> {
            SupervisorAssignmentRequestBean requestBean = mock(SupervisorAssignmentRequestBean.class);
            List<SupervisorAssignmentRequestBean> requestDataList = Lists.newArrayList(requestBean);

            IKProperties kProperties = Mockito.mock(IKProperties.class);
            springContextMockedStatic.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(kProperties);

            KronosPropertiesIfc kronosPropertiesIfc = mock(KronosPropertiesIfc.class);
            Mockito.when(personSupervisorAssignmentService.updateRequest(any(SupervisorAssignmentRequestBean.class)))
                    .thenThrow(new APIException("Error Occurred"));

            Mockito.doNothing().when(ResponseHandler.class);
            ResponseHandler.validateForNullRequest(requestDataList);

            Mockito.doNothing().when(ResponseHandler.class);
            ResponseHandler.validServiceLimit(requestDataList);

            restPersonSupervisorAssignment.multiUpsert(requestDataList);
        });
    }

    @Test
    public void testMultiUpsertGenericException() {
        SupervisorAssignmentRequestBean requestBean = mock(SupervisorAssignmentRequestBean.class);
        List<SupervisorAssignmentRequestBean> requestDataList = Lists.newArrayList(requestBean);

        // Mock ResponseHandler validations
        Mockito.doNothing().when(ResponseHandler.class);
        ResponseHandler.validateForNullRequest(requestDataList);

        Mockito.doNothing().when(ResponseHandler.class);
        ResponseHandler.validServiceLimit(requestDataList);

        // Mock service to throw APIException
        Mockito.when(personSupervisorAssignmentService.updateRequest(any(SupervisorAssignmentRequestBean.class)))
                .thenThrow(new APIException("Error Occurred"));

        assertThrows(APIException.class, () -> restPersonSupervisorAssignment.multiUpsert(requestDataList));
    }

    @Test
    public void testMultiUpsertException2() {
        SupervisorAssignmentRequestBean requestBean = mock(SupervisorAssignmentRequestBean.class);
        List<SupervisorAssignmentRequestBean> requestDataList = Lists.newArrayList(requestBean);

        // Mock ResponseHandler validations
        Mockito.doNothing().when(ResponseHandler.class);
        ResponseHandler.validateForNullRequest(requestDataList);

        Mockito.doNothing().when(ResponseHandler.class);
        ResponseHandler.validServiceLimit(requestDataList);

        Mockito.when(personSupervisorAssignmentService.updateRequest(any(SupervisorAssignmentRequestBean.class)))
                .thenThrow(new RuntimeException("Unexpected error"));

        assertThrows(APIException.class, () -> restPersonSupervisorAssignment.multiUpsert(requestDataList));
    }

    private SupervisorAssignmentRequestBean preparePersonSupervisorAssignmentRequestBean(Long personId) {
        SupervisorAssignmentRequestBean supervisorAssignmentRequestBean = new SupervisorAssignmentRequestBean();
        PersonIdentityBean personIdentity = new PersonIdentityBean();
        personIdentity.setPersonKey(personId);
        supervisorAssignmentRequestBean.setPersonIdentity(personIdentity);
        return supervisorAssignmentRequestBean;
    }
}