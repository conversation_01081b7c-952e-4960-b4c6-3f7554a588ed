package com.kronos.persons.context.switchrole;

import com.kronos.commonapp.authz.api.services.permission.PermissionsService;
import com.kronos.commonapp.authz.services.model.CombinedPermission;
import com.kronos.commonapp.authz.services.model.Permissions;
import com.kronos.persons.rest.model.DelegationRoleAssignmentDTO;
import com.kronos.releasetoggle.api.ReleaseToggleService;
import com.kronos.wfc.commonapp.people.business.person.delegateauthority.DelegationSummary;
import com.kronos.wfc.commonapp.people.business.person.delegateauthority.MultiManagerRoleAssignmentDTO;
import com.kronos.wfc.commonapp.people.business.person.delegation.SwitchRoleFacadeServiceHelper;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.user.CurrentUserAccountManager;
import com.kronos.wfc.commonapp.people.facade.person.delegateauthority.DelegateAuthorityFacade;
import com.kronos.wfc.platform.datetime.bridge.ITimeZoneAccessorService;
import com.kronos.wfc.platform.datetime.bridge.TimeZoneAccessorServiceFactory;
import com.kronos.wfc.platform.datetime.framework.KTimeZone;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.business.state.State;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RoleDelegateManagerTest {

    @InjectMocks
    private RoleDelegateManager roleDelegateManager;

    @Mock
    private PermissionsService permissionsService;

    @Mock
    private ReleaseToggleService releaseToggleService;

    @Mock
    private DelegateAuthorityFacade daFacade;

    @Mock
    private Personality mockPersonality;

    @Mock
    private SwitchRoleFacadeServiceHelper helper;

    @Mock
    private DelegationSummary delegationSummary;

    private MultiManagerRoleAssignmentDTO defaultDTO;
    private MultiManagerRoleAssignmentDTO daDTO;
    private MultiManagerRoleAssignmentDTO allDTO;
    private List<MultiManagerRoleAssignmentDTO> roles;
    private List<MultiManagerRoleAssignmentDTO> myRoles;
    private Map<String, Object>[] tasks;
    private ObjectIdLong mmrRoleId = new ObjectIdLong(1L);
    private ObjectIdLong daRoleId = new ObjectIdLong(2L);
    private ObjectIdLong allRoleId = new ObjectIdLong(3L);
    private ObjectIdLong personId = new ObjectIdLong(52L);
    private String personNumber = "personNumber";

    private MockedStatic<CurrentUserAccountManager> currentUserAccountManagerMockedStatic;
    private MockedStatic<SwitchRoleFacadeServiceHelper> switchRoleFacadeServiceHelperMockedStatic;
    private MockedStatic<DelegationSummary> mockedDelegationSummary;
    private MockedStatic<TimeZoneAccessorServiceFactory> mockedTimeZoneAccessorServiceFactory;
    private MockedStatic<State> stateMockedStatic;

    @BeforeEach
    public void setUp() {
        myRoles = populateRoleAssignmentsList();
        defaultDTO = myRoles.get(0);
        daDTO = myRoles.get(1);
        allDTO = myRoles.get(2);
        tasks = populateTasks();

        currentUserAccountManagerMockedStatic = mockStatic(CurrentUserAccountManager.class);
        switchRoleFacadeServiceHelperMockedStatic = mockStatic(SwitchRoleFacadeServiceHelper.class);
        mockedDelegationSummary = mockStatic(DelegationSummary.class);
        mockedTimeZoneAccessorServiceFactory = mockStatic(TimeZoneAccessorServiceFactory.class);
        stateMockedStatic = mockStatic(State.class);

        currentUserAccountManagerMockedStatic.when(CurrentUserAccountManager::getPersonality).thenReturn(mockPersonality);
        roleDelegateManager.permissionsService = permissionsService;
        roleDelegateManager.releaseToggleService = releaseToggleService;
    }

    @AfterEach
    public void tearDown() {
        currentUserAccountManagerMockedStatic.close();
        switchRoleFacadeServiceHelperMockedStatic.close();
        mockedDelegationSummary.close();
        mockedTimeZoneAccessorServiceFactory.close();
        stateMockedStatic.close();
    }

    @Test
    public void getSwitchRoleDelegatesTest() {
        mockedDelegationSummary.when(() -> DelegationSummary.doRetrieveByProxyMgrPersonNumber(personNumber)).thenReturn(delegationSummary);
        ITimeZoneAccessorService iTzService = mock(ITimeZoneAccessorService.class);
        mockedTimeZoneAccessorServiceFactory.when(TimeZoneAccessorServiceFactory::getTimeZoneAccessorService).thenReturn(iTzService);
        KTimeZone kTZone = mock(KTimeZone.class);
        when(iTzService.getTimeZoneForEmployee(anyLong(), any(KDate.class))).thenReturn(kTZone);

        // has no delegations
        when(daFacade.hasDelegations()).thenReturn(false);
        List<IDelegationsList> switchRoleDelegates = roleDelegateManager.getSwitchRoleDelegates();
        assertTrue(switchRoleDelegates.isEmpty());

        // has delegations
        when(daFacade.hasDelegations()).thenReturn(true);
        when(daFacade.loadDelegationsList()).thenReturn(tasks);
        switchRoleDelegates = roleDelegateManager.getSwitchRoleDelegates();
        // future delegation is not returned
        assertEquals(2, switchRoleDelegates.size());
        for (int i = 0; i < switchRoleDelegates.size(); i++) {
            verifyIDelegationsList(switchRoleDelegates.get(i), tasks[i]);
        }
    }

    @Test
    public void getDelegationRolesNoDelegationsTest() {
        mockedDelegationSummary.when(() -> DelegationSummary.doRetrieveByProxyMgrPersonNumber(personNumber)).thenReturn(delegationSummary);
        ITimeZoneAccessorService iTzService = mock(ITimeZoneAccessorService.class);
        mockedTimeZoneAccessorServiceFactory.when(TimeZoneAccessorServiceFactory::getTimeZoneAccessorService).thenReturn(iTzService);
        KTimeZone kTZone = mock(KTimeZone.class);
        when(daFacade.hasDelegations()).thenReturn(false);
        List<DelegationRoleAssignmentDTO> delegationRoles = roleDelegateManager.getDelegationRoles();
        assertTrue(delegationRoles.isEmpty());
    }

    @Test
    public void getDelegationRolesWithDelegationsTest() {
        mockedDelegationSummary.when(() -> DelegationSummary.doRetrieveByProxyMgrPersonNumber(personNumber)).thenReturn(delegationSummary);
        ITimeZoneAccessorService iTzService = mock(ITimeZoneAccessorService.class);
        mockedTimeZoneAccessorServiceFactory.when(TimeZoneAccessorServiceFactory::getTimeZoneAccessorService).thenReturn(iTzService);
        KTimeZone kTZone = mock(KTimeZone.class);
        when(iTzService.getTimeZoneForEmployee(anyLong(), any(KDate.class))).thenReturn(kTZone);

        // has delegations
        when(daFacade.hasDelegations()).thenReturn(true);
        when(daFacade.loadDelegationsList()).thenReturn(tasks);
        List<DelegationRoleAssignmentDTO> delegationRoles = roleDelegateManager.getDelegationRoles();
        // future delegation is not returned
        assertEquals(2, delegationRoles.size());
        for (int i = 0; i < delegationRoles.size(); i++) {
            verifyDelegationRolesList(delegationRoles.get(i), tasks[i]);
        }
    }

    private List<MultiManagerRoleAssignmentDTO> populateRoleAssignmentsList() {
        List<MultiManagerRoleAssignmentDTO> roleAssignments = new ArrayList<>();
        roleAssignments.add(new MultiManagerRoleAssignmentDTO(mmrRoleId.toLong(), "MMRName", true, "description"));
        roleAssignments.add(new MultiManagerRoleAssignmentDTO(daRoleId.toLong(), "DAName", false, "description"));
        roleAssignments.add(new MultiManagerRoleAssignmentDTO(allRoleId.toLong(), "ALLName", false, "description"));
        return roleAssignments;
    }

    private Map<String, Object>[] populateTasks() {
        Map<String, Object>[] tasks = new HashMap[3];
        tasks[0] = createTask("10", new ObjectIdLong(53), "12345", getKDate(LocalDate.now().minusDays(10)), getKDate(LocalDate.now().plusDays(5)));
        tasks[1] = createTask("11", new ObjectIdLong(54), "13579", null, null);
        // future tasks
        tasks[2] = createTask("12", new ObjectIdLong(55), "24680", getKDate(LocalDate.now().plusDays(1)), getKDate(LocalDate.now().plusDays(10)));
        return tasks;
    }

    private Map<String, Object> createTask(String taskId, ObjectIdLong delegatorId, String delegatorNum, KDate startDate, KDate endDate) {
        Map<String, Object> taskMap = new HashMap<>(6);
        taskMap.put(DelegateAuthorityFacade.DELEGATOR_ID, taskId);
        taskMap.put(DelegateAuthorityFacade.DELEGATOR_PERSON_ID, delegatorId);
        taskMap.put(DelegateAuthorityFacade.DELEGATOR_PERSON_NUMBER, delegatorNum);
        taskMap.put(DelegateAuthorityFacade.DELEGATOR_NAME, "delegatorName");
        taskMap.put(DelegateAuthorityFacade.DELEGATOR_ROLE, new String());
        taskMap.put(DelegateAuthorityFacade.START_DATE, startDate == null ? "" : startDate);
        taskMap.put(DelegateAuthorityFacade.END_DATE, endDate == null ? "" : endDate);
        return taskMap;
    }

    private void verifyIDelegationsList(IDelegationsList actual, Map<String, Object> expected) {
        assertEquals(expected.get(DelegateAuthorityFacade.DELEGATOR_ID), actual.getId());
        assertEquals(expected.get(DelegateAuthorityFacade.DELEGATOR_PERSON_ID).toString(), actual.getDelegatorId());
        assertEquals(expected.get(DelegateAuthorityFacade.DELEGATOR_NAME), actual.getDelegator());
        assertEquals(expected.get(DelegateAuthorityFacade.DELEGATOR_ROLE), actual.getRole());
        assertEquals(getStringDate(expected.get(DelegateAuthorityFacade.START_DATE)), actual.getStartDate());
        assertEquals(getStringDate(expected.get(DelegateAuthorityFacade.END_DATE)), actual.getEndDate());
    }

    private void verifyDelegationRolesList(DelegationRoleAssignmentDTO actual, Map<String, Object> expected) {
        assertEquals(expected.get(DelegateAuthorityFacade.DELEGATOR_ID), actual.getId());
        assertEquals(expected.get(DelegateAuthorityFacade.DELEGATOR_PERSON_ID).toString(), actual.getDelegator().getId().toString());
        assertEquals(expected.get(DelegateAuthorityFacade.DELEGATOR_PERSON_NUMBER).toString(), actual.getDelegator().getQualifier());
        assertEquals(expected.get(DelegateAuthorityFacade.DELEGATOR_NAME), actual.getDelegatorFullName());
        assertEquals(expected.get(DelegateAuthorityFacade.DELEGATOR_ROLE), actual.getRole());
        assertEquals(getLocalDateFromKDateString(expected.get(DelegateAuthorityFacade.START_DATE).toString()), actual.getStartDate());
        assertEquals(getLocalDateFromKDateString(expected.get(DelegateAuthorityFacade.END_DATE)), actual.getEndDate());
    }

    private LocalDate getLocalDateFromKDateString(Object date) {
        if (date.equals("")) {
            return null;
        } else {
            return LocalDate.parse(date.toString(), DateTimeFormatter.ofPattern("M/dd/yyyy"));
        }
    }

    private String getStringDate(Object date) {
        if (date.equals("")) {
            return null;
        } else {
            return date.toString();
        }
    }

    private KDate getKDate(LocalDate localDate) {
        Date date = localDate == null ? null : Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        return new KDate(date);
    }
}