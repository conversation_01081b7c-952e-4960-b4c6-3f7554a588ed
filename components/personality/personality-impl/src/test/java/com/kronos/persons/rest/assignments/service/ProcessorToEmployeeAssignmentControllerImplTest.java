package com.kronos.persons.rest.assignments.service;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.access.SpringContext;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.ProcessorToEmployeeAssignmentDTO;
import com.kronos.persons.rest.assignments.model.ProcessorToEmployeeCriteriaDTO;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.model.RestErrorBean;
import com.kronos.persons.utils.ExceptionHandler;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import com.kronos.wfc.totalizing.business.extensibility.exception.ExtensibilityProcessorException;
import com.kronos.wfc.totalizing.business.extensibility.validator.ExtensibilityProcessorValidator;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @since 11/2/2021
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class ProcessorToEmployeeAssignmentControllerImplTest {

    private static final String PERSON_NUMBER = "person number";
    private static final String PROCESSOR = "processor";
    private static final String EFFECTIVE_DATE = "2100-01-01";

    @InjectMocks
    private ProcessorToEmployeeAssignmentControllerImpl processorToEmployeeAssignmentController;

    @Mock
    private ProcessorToEmployeeAssignmentServiceImpl processorToEmployeeAssignmentService;

    @Mock
    Properties properties;

    @Mock
    private RestErrorBean restErrorBean;

    @Mock
    private ExtensibilityProcessorValidator processorValidator;

    private MockedStatic<KronosProperties> kronosPropertiesMockedStatic;
    private MockedStatic<SpringContext> springContextMockedStatic;
    private MockedStatic<ExceptionHandler> exceptionHandlerMockedStatic;

    @BeforeEach
    public void setUp() {
        ReflectionTestUtils.setField(processorToEmployeeAssignmentController,"processorValidator",processorValidator);
        kronosPropertiesMockedStatic = Mockito.mockStatic(KronosProperties.class);
        kronosPropertiesMockedStatic.when(KronosProperties::getProperties).thenReturn(properties);
        when(properties.getProperty(anyString(),isNull())).thenReturn("12");
        kronosPropertiesMockedStatic.when(()->KronosProperties.getProperty(anyString(), anyString())).thenReturn("12");
        springContextMockedStatic = Mockito.mockStatic(SpringContext.class);
        exceptionHandlerMockedStatic = Mockito.mockStatic(ExceptionHandler.class);
        exceptionHandlerMockedStatic.when(() -> ExceptionHandler.handleException(any(), anyString(), anyString())).thenReturn(restErrorBean);
        processorValidator = mock(ExtensibilityProcessorValidator.class);
        when(SpringContext.getBean(ExtensibilityProcessorValidator.class)).thenReturn(processorValidator);
    }

    @AfterEach
    public void tearDown() {
        kronosPropertiesMockedStatic.close();
        springContextMockedStatic.close();
        exceptionHandlerMockedStatic.close();
    }


    @Test
    public void createProcessorToEmployeeAssignment() {
        doNothing().when(processorValidator).checkTotalizerExtensionProcessorConfiguration();

        ProcessorToEmployeeAssignmentDTO requestBean = new ProcessorToEmployeeAssignmentDTO();
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(processorToEmployeeAssignmentService.createProcessorToEmployeeAssignment(requestBean)).thenReturn(personality);
        ProcessorToEmployeeAssignmentDTO responseBean = processorToEmployeeAssignmentController.createProcessorToEmployeeAssignment(requestBean);
        assertNotNull(responseBean);
        assertEquals(responseBean, requestBean);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void createProcessorToEmployeeAssignmentException() {
        assertThrows(APIException.class, () -> {
            ProcessorToEmployeeAssignmentDTO requestBean = new ProcessorToEmployeeAssignmentDTO();
            Mockito.when(processorToEmployeeAssignmentService.createProcessorToEmployeeAssignment(any())).thenThrow(RuntimeException.class);
            processorToEmployeeAssignmentController.createProcessorToEmployeeAssignment(requestBean);
        });

    }

    @SuppressWarnings("unchecked")
    @Test
    public void createProcessorToEmployeeAssignmentAPIException() {
        assertThrows(APIException.class, () -> {
            ProcessorToEmployeeAssignmentDTO requestBean = new ProcessorToEmployeeAssignmentDTO();
            Mockito.when(processorToEmployeeAssignmentService.createProcessorToEmployeeAssignment(any())).thenThrow(APIException.class);
            processorToEmployeeAssignmentController.createProcessorToEmployeeAssignment(requestBean);
        });
    }

    @Test
    public void updateProcessorToEmployeeAssignment() {
        ProcessorToEmployeeAssignmentDTO requestBean = mock(ProcessorToEmployeeAssignmentDTO.class);
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(processorToEmployeeAssignmentService.updateProcessorToEmployeeAssignment(requestBean)).thenReturn(personality);
        ProcessorToEmployeeAssignmentDTO responseBean = processorToEmployeeAssignmentController.updateProcessorToEmployeeAssignment(requestBean);
        assertNotNull(responseBean);
        assertEquals(responseBean, requestBean);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void updateProcessorToEmployeeAssignmentException() {
        assertThrows(APIException.class, () -> {
            Mockito.when(processorToEmployeeAssignmentService.updateProcessorToEmployeeAssignment(any())).thenThrow(RuntimeException.class);
            ProcessorToEmployeeAssignmentDTO requestBean = Mockito.mock(ProcessorToEmployeeAssignmentDTO.class);
            processorToEmployeeAssignmentController.updateProcessorToEmployeeAssignment(requestBean);
        });
    }

    @SuppressWarnings("unchecked")
    @Test
    public void updateProcessorToEmployeeAssignmentAPIException() {
        assertThrows(APIException.class, () -> {
            Mockito.when(processorToEmployeeAssignmentService.updateProcessorToEmployeeAssignment(any())).thenThrow(APIException.class);
            ProcessorToEmployeeAssignmentDTO requestBean = Mockito.mock(ProcessorToEmployeeAssignmentDTO.class);
            processorToEmployeeAssignmentController.updateProcessorToEmployeeAssignment(requestBean);
        });
    }

    @Test
    public void multiCreateProcessorToEmployeeAssignments() {
        Mockito.when(KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        ProcessorToEmployeeAssignmentDTO requestBean = Mockito.mock(ProcessorToEmployeeAssignmentDTO.class);
        List<ProcessorToEmployeeAssignmentDTO> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(processorToEmployeeAssignmentService.createProcessorToEmployeeAssignment(any())).thenReturn(personality);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        List<ProcessorToEmployeeAssignmentDTO> responseBean = processorToEmployeeAssignmentController.multiCreateProcessorToEmployeeAssignments(requestDataList);
        assertNotNull(responseBean);
        assertEquals(1, responseBean.size());
    }

    @Test
    public void multiUpdateProcessorToEmployeeAssignments() {
        Mockito.when(KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        ProcessorToEmployeeAssignmentDTO requestBean = Mockito.mock(ProcessorToEmployeeAssignmentDTO.class);
        List<ProcessorToEmployeeAssignmentDTO> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(processorToEmployeeAssignmentService.updateProcessorToEmployeeAssignment(any())).thenReturn(personality);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        List<ProcessorToEmployeeAssignmentDTO> responseBean = processorToEmployeeAssignmentController.multiUpdateProcessorToEmployeeAssignments(requestDataList);
        assertNotNull(responseBean);
        assertEquals(1, responseBean.size());
    }

    @Disabled
    @Test
    public void multiDeleteProcessorToEmployeeAssignments() {
        Mockito.when(KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        ProcessorToEmployeeCriteriaDTO requestBean = new ProcessorToEmployeeCriteriaDTO();
        List<ProcessorToEmployeeCriteriaDTO> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        Personality personality = Mockito.mock(Personality.class);
        Mockito.when(processorToEmployeeAssignmentService.deleteProcessorToEmployeeAssignment(any()))
                .thenReturn(personality);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        processorToEmployeeAssignmentController.multiDeleteProcessorToEmployeeAssignments(requestDataList);
        verify(processorToEmployeeAssignmentService).deleteProcessorToEmployeeAssignment(any());
        verify(processorValidator).checkIfListIsNotEmpty(requestDataList);
        verify(processorValidator).checkTotalizerExtensionProcessorConfiguration();
        verifyNoMoreInteractions(processorToEmployeeAssignmentService, processorValidator);
    }

    @Disabled
    @Test
    public void deleteProcessorToEmployeeAssignment() {
        ProcessorToEmployeeCriteriaDTO requestBean = new ProcessorToEmployeeCriteriaDTO();
        processorToEmployeeAssignmentController.deleteProcessorToEmployeeAssignment(requestBean);
        verify(processorValidator).checkTotalizerExtensionProcessorConfiguration();
        verify(processorToEmployeeAssignmentService).deleteProcessorToEmployeeAssignment(requestBean);
        verifyNoMoreInteractions(processorValidator, processorToEmployeeAssignmentService);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void deleteProcessorToEmployeeAssignmentException() {
        ProcessorToEmployeeCriteriaDTO requestBean = new ProcessorToEmployeeCriteriaDTO();
        Mockito.when(processorToEmployeeAssignmentService.deleteProcessorToEmployeeAssignment(requestBean)).thenThrow(RuntimeException.class);
        assertThrows(APIException.class, () -> {
            processorToEmployeeAssignmentController.deleteProcessorToEmployeeAssignment(requestBean);
        });
    }

    @SuppressWarnings("unchecked")
    @Test
    public void deleteProcessorToEmployeeAssignmentAPIException() {
        assertThrows(APIException.class, () -> {
            ProcessorToEmployeeCriteriaDTO requestBean = new ProcessorToEmployeeCriteriaDTO();
            Mockito.when(processorToEmployeeAssignmentService.deleteProcessorToEmployeeAssignment(requestBean)).thenThrow(APIException.class);
            processorToEmployeeAssignmentController.deleteProcessorToEmployeeAssignment(requestBean);
        });
    }

    @Test
    public void getProcessorToEmployeeAssignmentByPersonId() {
        PersonIdentityBean identityBean = new PersonIdentityBean();
        List arr=getRuleList();
        Mockito.when(processorToEmployeeAssignmentService.getProcessorToEmployeeAssignmentsByParams(isNull(), isNull(), isNull(), isNull())).thenReturn(arr);
        List<ProcessorToEmployeeAssignmentDTO> list = processorToEmployeeAssignmentController
                .getProcessorToEmployeeAssignmentByPersonId(identityBean.getPersonKey());
        assertNotNull(list);
        assertEquals(2, list.size());
    }

    @Disabled
    @Test
    public void getProcessorToEmployeeAssignmentByPersonIdThrowAPIException() {
        PersonIdentityBean identityBean = new PersonIdentityBean();
        doThrow(new APIException()).when(processorToEmployeeAssignmentService).getProcessorToEmployeeAssignmentsByParams(Mockito.anyLong(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
        assertThrows(APIException.class, () -> {
            processorToEmployeeAssignmentController.getProcessorToEmployeeAssignmentByPersonId(identityBean.getPersonKey());
        });
    }

    @Disabled
    @Test
    public void getProcessorToEmployeeAssignmentByPersonIdThrowException() {
        PersonIdentityBean identityBean = new PersonIdentityBean();
        RestErrorBean errorBean = mock(RestErrorBean.class);
        exceptionHandlerMockedStatic.when(() -> ExceptionHandler.handleException(any(), any(), any())).thenReturn(errorBean);
        doThrow(RuntimeException.class).when(processorToEmployeeAssignmentService).getProcessorToEmployeeAssignmentsByParams(Mockito.anyLong(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
        assertThrows(APIException.class, () -> {
            processorToEmployeeAssignmentController.getProcessorToEmployeeAssignmentByPersonId(identityBean.getPersonKey());
        });
    }

    @Test
    public void getProcessorToEmployeeAssignmentByParams() {
        List<ProcessorToEmployeeAssignmentDTO> lst = getRuleList();
        when(processorToEmployeeAssignmentService.getProcessorToEmployeeAssignmentsByParams(Mockito.isNull(), anyString(), anyString(), anyString())).thenReturn(lst);
        List<ProcessorToEmployeeAssignmentDTO> list = processorToEmployeeAssignmentController
                .getProcessorToEmployeeAssignmentsByParams(PERSON_NUMBER, PROCESSOR, EFFECTIVE_DATE);
        assertNotNull(list);
        assertEquals(2, list.size());
    }

    @Test
    public void getProcessorToEmployeeAssignmentByParamsThrowAPIException() {
        assertThrows(APIException.class, () -> {
            doThrow(new APIException()).when(processorToEmployeeAssignmentService).getProcessorToEmployeeAssignmentsByParams(Mockito.isNull(), anyString(), anyString(), anyString());
            processorToEmployeeAssignmentController.getProcessorToEmployeeAssignmentsByParams(PERSON_NUMBER, PROCESSOR, EFFECTIVE_DATE);
        });
    }

    @Test
    public void getProcessorToEmployeeAssignmentByParamsThrowException() {
        assertThrows(APIException.class, () -> {
            RestErrorBean errorBean = mock(RestErrorBean.class);
            exceptionHandlerMockedStatic.when(()->ExceptionHandler.handleException(any(), anyString(), anyString())).thenReturn(errorBean);
            doThrow(RuntimeException.class).when(processorToEmployeeAssignmentService).getProcessorToEmployeeAssignmentsByParams(Mockito.isNull(), anyString(), anyString(), anyString());
            processorToEmployeeAssignmentController.getProcessorToEmployeeAssignmentsByParams(PERSON_NUMBER, PROCESSOR, EFFECTIVE_DATE);
        });
    }
    
    @Test
    public void testMultiCreateWithEmptyList() {
        assertThrows(APIException.class, () -> {
            prepareExceptionMocks();
            processorToEmployeeAssignmentController.multiCreateProcessorToEmployeeAssignments(Collections.emptyList());
        });
    }

    @Test
    public void testMultiUpdateWithEmptyList() {
        assertThrows(APIException.class, () -> {
            prepareExceptionMocks();
            processorToEmployeeAssignmentController.multiUpdateProcessorToEmployeeAssignments(Collections.emptyList());
        });
    }

    @Test
    public void testMultiDeleteWithEmptyList() {
        assertThrows(APIException.class, () -> {
            prepareExceptionMocks();
            processorToEmployeeAssignmentController.multiDeleteProcessorToEmployeeAssignments(Collections.emptyList());
        });
    }

    @Test
    public void testMultiCreateThrowAPIException() {
        assertThrows(APIException.class, () -> {
            ProcessorToEmployeeAssignmentDTO dto = mock(ProcessorToEmployeeAssignmentDTO.class);
            List<ProcessorToEmployeeAssignmentDTO> expectedList = Collections.singletonList(dto);
            doThrow(new APIException()).when(processorToEmployeeAssignmentService).createProcessorToEmployeeAssignment(any());
            processorToEmployeeAssignmentController.multiCreateProcessorToEmployeeAssignments(expectedList);
        });
    }

    @Test
    public void testMultiCreateThrowException() {
        assertThrows(APIException.class, () -> {
            ProcessorToEmployeeAssignmentDTO dto = mock(ProcessorToEmployeeAssignmentDTO.class);
            List<ProcessorToEmployeeAssignmentDTO> expectedList = Collections.singletonList(dto);
            RestErrorBean errorBean = mock(RestErrorBean.class);
            doThrow(RuntimeException.class).when(processorToEmployeeAssignmentService).createProcessorToEmployeeAssignment(any());
            Mockito.when(ExceptionHandler.handleException(any(), any(), any())).thenReturn(errorBean);
            processorToEmployeeAssignmentController.multiCreateProcessorToEmployeeAssignments(expectedList);
        });
    }

    @Test
    public void testMultiUpdateThrowAPIException() {
        assertThrows(APIException.class, () -> {
            ProcessorToEmployeeAssignmentDTO dto = mock(ProcessorToEmployeeAssignmentDTO.class);
            List<ProcessorToEmployeeAssignmentDTO> expectedList = Collections.singletonList(dto);
            doThrow(new APIException()).when(processorToEmployeeAssignmentService).updateProcessorToEmployeeAssignment(any());
            processorToEmployeeAssignmentController.multiUpdateProcessorToEmployeeAssignments(expectedList);
        });
    }

    @Test
    public void testMultiUpdateThrowException() {
        assertThrows(APIException.class, () -> {
            ProcessorToEmployeeAssignmentDTO dto = mock(ProcessorToEmployeeAssignmentDTO.class);
            List<ProcessorToEmployeeAssignmentDTO> expectedList = Collections.singletonList(dto);
            RestErrorBean errorBean = mock(RestErrorBean.class);
            doThrow(RuntimeException.class).when(processorToEmployeeAssignmentService).updateProcessorToEmployeeAssignment(any());
            Mockito.when(ExceptionHandler.handleException(any(), any(), any())).thenReturn(errorBean);
            processorToEmployeeAssignmentController.multiUpdateProcessorToEmployeeAssignments(expectedList);
        });
    }

    @Test
    public void testMultiDeleteThrowAPIException() {
        assertThrows(APIException.class, () -> {
            ProcessorToEmployeeCriteriaDTO dto = mock(ProcessorToEmployeeCriteriaDTO.class);
            List<ProcessorToEmployeeCriteriaDTO> expectedList = Collections.singletonList(dto);
            doThrow(new APIException()).when(processorToEmployeeAssignmentService).deleteProcessorToEmployeeAssignment(any());
            processorToEmployeeAssignmentController.multiDeleteProcessorToEmployeeAssignments(expectedList);
        });
    }

    @Test
    public void testMultiDeleteThrowException() {
        assertThrows(APIException.class, () -> {
            ProcessorToEmployeeCriteriaDTO dto = mock(ProcessorToEmployeeCriteriaDTO.class);
            List<ProcessorToEmployeeCriteriaDTO> expectedList = Collections.singletonList(dto);
            RestErrorBean errorBean = mock(RestErrorBean.class);
            doThrow(RuntimeException.class).when(processorToEmployeeAssignmentService).deleteProcessorToEmployeeAssignment(any());
            Mockito.when(ExceptionHandler.handleException(any(), any(), any())).thenReturn(errorBean);
            processorToEmployeeAssignmentController.multiDeleteProcessorToEmployeeAssignments(expectedList);
        });
    }

    private void prepareExceptionMocks() {
        IKProperties properties = mock(IKProperties.class);
        springContextMockedStatic.when(()->SpringContext.getBean(any())).thenReturn(properties);
        doThrow(new ExtensibilityProcessorException("Error")).when(processorValidator).checkIfListIsNotEmpty(anyList());
    }

    private List<ProcessorToEmployeeAssignmentDTO> getRuleList() {
        List<ProcessorToEmployeeAssignmentDTO> list = new ArrayList<>();
        ProcessorToEmployeeAssignmentDTO bean1 = new ProcessorToEmployeeAssignmentDTO();
        bean1.setEffectiveDate("1753-01-01");
        ProcessorToEmployeeAssignmentDTO bean2 = new ProcessorToEmployeeAssignmentDTO();
        bean2.setEffectiveDate("2100-01-01");
        list.add(bean1);
        list.add(bean2);
        return list;
    }

}