/**
 *
 */
package com.kronos.persons.rest.assignments.service;

import com.kronos.container.api.exception.APIException;
import com.kronos.container.api.util.APIExceptionDetailResult;
import com.kronos.persons.rest.assignments.model.EmployeeJobPreferencesBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.persons.rest.model.ExtensionCriteria;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.rest.model.PersonQualifierBean;
import com.kronos.persons.rest.model.PersonQualifierDetailBean;
import com.kronos.persons.utils.CriteriaHelper;
import com.kronos.persons.utils.NewBatchProcessor;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonAssignmentHelperTest {

	@Mock
	private CriteriaHelper criteriaHelper;

	@Mock
	private ValidatorUtils validatorutils;

	@InjectMocks
	private PersonAssignmentHelper personAssignmentHelper = new PersonAssignmentHelper<>();

	private MockedStatic<RequestContextHolder> requestContextHolderMockedStatic;

	@BeforeEach
	public void setUp() {
		requestContextHolderMockedStatic = Mockito.mockStatic(RequestContextHolder.class);
	}

	@AfterEach
	public void tearDown() {
		requestContextHolderMockedStatic.close();
	}

	@Test
	public void testGetPersonAssignmentList() {
		doNothing()
				.when(criteriaHelper)
				.validateCriteriaForMultiKeyNotAllowed(
						any(ExtensionSearchCriteria.class));
		ExtensionCriteria requestCriteria = getRequestCriteria();
		Mockito.when(
				criteriaHelper.getExtensionCriteria(any(ExtensionSearchCriteria.class))).thenReturn(
				requestCriteria);
		PersonQualifierBean qualifierBean = getPersonQualifierBean();
		Mockito.when(
				criteriaHelper.getPersonQualifiers(any(ExtensionCriteria.class))).thenReturn(
				qualifierBean);
		Consumer<PersonIdentityBean> modifyPersonIdentityBean = mock(Consumer.class);
		Function<PersonIdentityBean, EmployeeJobPreferencesBean> function = bean -> new EmployeeJobPreferencesBean();
		ExtensionSearchCriteria extensionSearchCriteria = new ExtensionSearchCriteria();

		try {
			setMocksForGetPersonAssignmentList();
			personAssignmentHelper.getPersonAssignmentList(
					extensionSearchCriteria, function, modifyPersonIdentityBean, true);
		} catch (APIException ex) {
			List<APIExceptionDetailResult<APIException>> d = (List<APIExceptionDetailResult<APIException>>) ex
					.getResults().get("results");
			Assertions.assertNotNull(d.get(0).getError());
			Assertions.assertNotNull(d.get(2).getError());
			Assertions.assertNotNull(d.get(1).getSuccess());
			verify(modifyPersonIdentityBean).accept(any());
		}
		Assertions.assertTrue(true, "Should have thrown an Exception");
	}

	@Test
	public void getPersonAssignmentList_whenThrowNoError_thenValuesReturned() {

		doNothing()
				.when(criteriaHelper)
				.validateCriteriaForMultiKeyNotAllowed(
						any(ExtensionSearchCriteria.class));
		ExtensionCriteria requestCriteria = getRequestCriteria();
		Mockito.when(
				criteriaHelper.getExtensionCriteria(any(ExtensionSearchCriteria.class))).thenReturn(
				requestCriteria);
		PersonQualifierBean qualifierBean = getPersonQualifierBean();
		Mockito.when(
				criteriaHelper.getPersonQualifiers(any(ExtensionCriteria.class))).thenReturn(
				qualifierBean);
		Consumer<PersonIdentityBean> modifyPersonIdentityBean = mock(Consumer.class);
		Function<PersonIdentityBean, EmployeeJobPreferencesBean> function = bean -> new EmployeeJobPreferencesBean();
		ExtensionSearchCriteria extensionSearchCriteria = new ExtensionSearchCriteria();

		try{
			setMocksForGetPersonAssignmentList();
			List<?> result = personAssignmentHelper.getPersonAssignmentList(
					extensionSearchCriteria, function, modifyPersonIdentityBean, true);
			Assertions.assertEquals(1, result.size(), "One item Expected");
		} catch (APIException ex) {
			Assertions.fail("Should not throw Exception");
		}
	}

	@Test
	public void process_whenContext_thenDoNotSetContext() {
		NewBatchProcessor batchProcessor = setMocksForProcess();

		personAssignmentHelper.process(batchProcessor);

		RequestContextHolder.resetRequestAttributes();
		RequestContextHolder.setRequestAttributes(any());
	}

	@Test
	public void process_whenNoContext_thenSetContext() {
			NewBatchProcessor batchProcessor = mock(NewBatchProcessor.class);
			ServletRequestAttributes attributes = mock(ServletRequestAttributes.class);
		requestContextHolderMockedStatic.when(RequestContextHolder::getRequestAttributes)
					.thenReturn(null, attributes);

			personAssignmentHelper.process(batchProcessor);

		requestContextHolderMockedStatic.verify(() -> RequestContextHolder.resetRequestAttributes());
		requestContextHolderMockedStatic.verify(() -> RequestContextHolder.setRequestAttributes(any()));
	}

	private ExtensionCriteria getRequestCriteria() {
		ExtensionCriteria criteria = new ExtensionCriteria();
		criteria.setExtensionType("employee");
		criteria.setOnlyActivePerson(true);
		criteria.setSearchBy("personnumber");
		List<String> list = new ArrayList<>();
		list.add("10041");
		list.add("10042");
		list.add("10043");
		criteria.setSearchValue(list);
		criteria.setSnapshotDate("2019-08-01");
		return criteria;
	}

	private PersonQualifierBean getPersonQualifierBean() {
		PersonQualifierBean personBean = new PersonQualifierBean();
		PersonQualifierDetailBean detailBean = new PersonQualifierDetailBean();
		detailBean.setPersonId(67L);
		detailBean.setPersonNumber("10042");
		detailBean.setQualifier("10042");
		personBean.addPersonDetails(detailBean);
		personBean.addMissingQualifiers("10041");
		personBean.addMissingQualifiers("10043");
		return personBean;
	}

	private static NewBatchProcessor setMocksForProcess() {
		NewBatchProcessor batchProcessor = mock(NewBatchProcessor.class);
		ServletRequestAttributes attributes = mock(ServletRequestAttributes.class);
		BDDMockito.given(RequestContextHolder.getRequestAttributes())
				.willReturn(attributes);
		return batchProcessor;
	}

	private static void setMocksForGetPersonAssignmentList() {
		ServletRequestAttributes attributes = mock(ServletRequestAttributes.class);
		BDDMockito.given(RequestContextHolder.getRequestAttributes())
				.willReturn(null, attributes);
	}
}
