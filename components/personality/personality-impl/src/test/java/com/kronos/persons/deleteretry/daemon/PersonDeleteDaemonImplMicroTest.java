/*******************************************************************************
 * PersonDeleteDaemonImplMicroTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.deleteretry.daemon;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import com.kronos.eventframework.dto.EventManagerDTO;

/*******************************************************************************
 * Test class for PersonDeleteDaemonImpl
 ******************************************************************************/
@ExtendWith(MockitoExtension.class)
public class PersonDeleteDaemonImplMicroTest {
	@Mock
	EventManagerDTO daemonDTO;

	@Mock
 	DeletePersonAgent deletePersonAgent;

	@InjectMocks
	PersonDeleteDaemonImpl personDeleteDaemonImpl;

	@Test
	public void test_WhenDeletePersonAgent_IsNotNull_ThenExecuteMethod_Invoked(){
		Mockito.doNothing().when(deletePersonAgent).execute();
		personDeleteDaemonImpl.run(daemonDTO);
		 Mockito.verify(deletePersonAgent,Mockito.times(1)).execute();
	}
	@Test
	public  void test_WhenDeletePersonAgent_IsNull_ThenExecuteMethod_NeverInvoked() {
		personDeleteDaemonImpl.deletePersonAgent=null;
		personDeleteDaemonImpl.run(daemonDTO);
		Mockito.verify(deletePersonAgent,Mockito.never()).execute();
	}
}
