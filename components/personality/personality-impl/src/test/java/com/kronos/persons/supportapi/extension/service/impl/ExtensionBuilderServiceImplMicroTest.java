package com.kronos.persons.supportapi.extension.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;

import com.kronos.people.personality.dataaccess.adapter.ExtensionAdapterEnum;
import com.kronos.people.personality.dataaccess.legacy.ExtensionBuilder;
import com.kronos.people.personality.facade.PersonalityCacheFacade;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.AccrualExtension;
import com.kronos.people.personality.model.extension.AllExtension;
import com.kronos.people.personality.model.extension.BaseExtension;
import com.kronos.people.personality.model.extension.DevicesExtension;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.SchedulingExtension;
import com.kronos.people.personality.model.extension.TimekeepingExtension;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.personality.PersonalityCache;
import com.kronos.wfc.commonapp.people.business.personality.PersonalityTriplet;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
public class ExtensionBuilderServiceImplMicroTest {
	
	@InjectMocks
	ExtensionBuilderServiceImpl extensionBuilderServiceImpl;
	
	@Mock
	ExtensionBuilder extensionBuilder;

	private MockedStatic<Personality> mockedPersonality;
	private MockedStatic<PersonalityCache> mockedPersonalityCache;
	
	@Mock
	PersonalityCacheFacade personalityCacheFacade;
	
	@BeforeEach
	public void setUp() {
	    mockedPersonality = Mockito.mockStatic(Personality.class);
		mockedPersonalityCache = Mockito.mockStatic(PersonalityCache.class);
	}

	@AfterEach
	public void tearDown() {
		mockedPersonality.close();
		mockedPersonalityCache.close();
	}

	@Test
	public void testBuildAllExtensionFromDb() {
		ExtensionBuilderServiceImpl impl = extensionBuilderServiceImpl;
		ExtensionBuilderServiceImpl spyImpl = Mockito.spy(impl);

		PersonalityTriplet triplet = Mockito.mock(PersonalityTriplet.class);
		mockedPersonalityCache.when(() -> PersonalityCache.getByPersonIdFromDatabase(new ObjectIdLong(12L))).thenReturn(triplet);

		Personality personality = Mockito.mock(Personality.class);
		Mockito.doReturn(personality).when(spyImpl).createPersonality(Mockito.any());

		Map<String, BaseExtension> allExtensionMap = new HashMap<>();
		allExtensionMap.put(ExtensionAdapterEnum.ACCRUAL.getIdentifier(), new AccrualExtension());
		allExtensionMap.put(ExtensionAdapterEnum.DEVICES.getIdentifier(), new DevicesExtension());
		allExtensionMap.put(ExtensionAdapterEnum.SCHEDULING.getIdentifier(), new SchedulingExtension());
		allExtensionMap.put(ExtensionAdapterEnum.TIMEKEEPING.getIdentifier(), new TimekeepingExtension());
		allExtensionMap.put(ExtensionAdapterEnum.EMPLOYEE.getIdentifier(), new EmployeeExtension());

		Mockito.when(extensionBuilder.buildExtensions(personality)).thenReturn(allExtensionMap);

		AllExtension allExtension = spyImpl.buildAllExtensionFromDb(12L);
		assertEquals("ALL_EXTENSION", allExtension.getIdentifier().getIdentifier());
	}

	@Test
	public void testGetExtensionFromCache() {
		PersonalityResponse<AllExtension> personalityResponse = new PersonalityResponse<>();
		personalityResponse.setextension(new AllExtension());
		Mockito.when(personalityCacheFacade.getAllExtensionForPersonId(Mockito.anyLong(), Mockito.any())).thenReturn(personalityResponse);
		AllExtension allExtensionResp = extensionBuilderServiceImpl.getAllPersonalityExtensionFromCache(12L);
		assertNotNull(allExtensionResp);
	}

	@Test
	public void testGetExtensionFromCacheForNullResponse() {
		Mockito.when(personalityCacheFacade.getAllExtensionForPersonId(Mockito.anyLong(), Mockito.any())).thenReturn(null);
		AllExtension allExtensionResp = extensionBuilderServiceImpl.getAllPersonalityExtensionFromCache(12L);
		assertNotNull(allExtensionResp);
	}

	@Test
	public void testCreatePersonality() {
		ObjectIdLong pid = new ObjectIdLong(1L);
		PersonalityTriplet triplet = new PersonalityTriplet();
		triplet.setPersonId(pid);
		Personality result = extensionBuilderServiceImpl.createPersonality(triplet);
		assertNotNull(result);
		assertEquals(pid,result.getPersonId());
	}
}
