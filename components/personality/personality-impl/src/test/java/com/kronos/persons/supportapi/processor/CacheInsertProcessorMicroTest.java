package com.kronos.persons.supportapi.processor;

import com.kronos.api.commoncomponent.async.APIExecutor;
import com.kronos.api.commoncomponent.util.CommonUtils;
import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.cache.PersonalityDistributedOperationsHandler;
import com.kronos.people.personality.facade.PersonalityCacheFacade;
import com.kronos.persons.rest.supportapi.validation.CacheSupportApiUtil;
import com.kronos.persons.rest.supportapi.validation.SupportApiValidator;
import com.kronos.persons.supportapi.dto.PersonalityCacheInsertRequest;
import com.kronos.persons.supportapi.dto.PersonalityCacheInsertResponse;
import com.kronos.persons.supportapi.dto.PersonalityInsertRequestData;
import com.kronos.persons.supportapi.errorcodes.PersonalitySupportApiConstants;
import com.kronos.persons.supportapi.errorcodes.PersonalitySupportApiErrorCodes;
import com.kronos.concurrent.api.service.KronosThreadPoolService;
import jakarta.ws.rs.core.Response;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CacheInsertProcessorMicroTest {

    @InjectMocks
    private CacheInsertProcessor cacheInsertProcessor;

    @Mock
    private SupportApiValidator supportApiValidator;

    @Mock
    private CacheSupportApiUtil cacheSupportApiUtil;

    @Mock
    private PersonalityDistributedOperationsHandler distributedOperationsHandler;

    @Mock
    private PersonalityCacheFacade cacheFacade;

    @Mock
    private CommonUtils commonUtils;

    @Mock
    private APIExecutor apiExecutor;

    @Mock
    private KronosThreadPoolService kronosThreadPoolService;

    @Mock
    private ExecutorService executor;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(kronosThreadPoolService.newThreadPool(anyString())).thenReturn(executor);
        cacheInsertProcessor.startup();
    }

    @Test
    public void testProcessCacheInsert_ValidRequest() {
        PersonalityCacheInsertRequest request = new PersonalityCacheInsertRequest();
        List<PersonalityInsertRequestData> requestDataList = new ArrayList<>();
        PersonalityInsertRequestData requestData = new PersonalityInsertRequestData();
        requestData.setTenantShortName("tenant1");
        requestData.setPersonIds(Arrays.asList(1L, 2L, 3L));
        requestDataList.add(requestData);
        request.setTenantPersonalityRequestData(requestDataList);

        when(cacheSupportApiUtil.getIntegerValueForProperty(anyString(), anyInt())).thenReturn(5);
        when(apiExecutor.execute(any())).thenReturn(new PersonalityCacheInsertResponse());

        Response response = cacheInsertProcessor.processCacheInsert(request);

        assertNotNull(response);
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
    }

    @Test
    public void testProcessCacheInsert_EmptyRequest() {
        PersonalityCacheInsertRequest request = new PersonalityCacheInsertRequest();
        request.setTenantPersonalityRequestData(new ArrayList<>());

        assertThrows(APIException.class, () -> cacheInsertProcessor.processCacheInsert(request));
    }

    @Test
    public void testProcessCacheInsert_ExceedingMaxTenants() {
        PersonalityCacheInsertRequest request = new PersonalityCacheInsertRequest();
        List<PersonalityInsertRequestData> requestDataList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            PersonalityInsertRequestData requestData = new PersonalityInsertRequestData();
            requestData.setTenantShortName("tenant" + i);
            requestData.setPersonIds(Arrays.asList(1L, 2L, 3L));
            requestDataList.add(requestData);
        }
        request.setTenantPersonalityRequestData(requestDataList);

        when(cacheSupportApiUtil.getIntegerValueForProperty(anyString(), anyInt())).thenReturn(5);
        doThrow(new APIException(PersonalitySupportApiErrorCodes.EXCEPTION_OCCURRED_WHILE_VALIDATING_PERSON_CACHE))
                .when(supportApiValidator).validateTotalTenantsInRequest(anyInt(), anyInt());

        assertThrows(APIException.class, () -> cacheInsertProcessor.processCacheInsert(request));
    }

    @Test
    public void testProcessCacheInsert_ExceedingMaxPersons() {
        PersonalityCacheInsertRequest request = new PersonalityCacheInsertRequest();
        List<PersonalityInsertRequestData> requestDataList = new ArrayList<>();
        PersonalityInsertRequestData requestData = new PersonalityInsertRequestData();
        requestData.setTenantShortName("tenant1");
        List<Long> personIds = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            personIds.add((long) i);
        }
        requestData.setPersonIds(personIds);
        requestDataList.add(requestData);
        request.setTenantPersonalityRequestData(requestDataList);

        when(cacheSupportApiUtil.getIntegerValueForProperty(anyString(), anyInt())).thenReturn(5);
        doThrow(new APIException(PersonalitySupportApiErrorCodes.EXCEPTION_OCCURRED_WHILE_VALIDATING_PERSON_CACHE))
                .when(supportApiValidator).validatePersonIdsList(anyList(), anyInt());

        assertThrows(APIException.class, () -> cacheInsertProcessor.processCacheInsert(request));
    }

    @Test
    public void testProcessCacheInsert_ExceptionHandling() {
        PersonalityCacheInsertRequest request = new PersonalityCacheInsertRequest();
        List<PersonalityInsertRequestData> requestDataList = new ArrayList<>();
        PersonalityInsertRequestData requestData = new PersonalityInsertRequestData();
        requestData.setTenantShortName("tenant1");
        requestData.setPersonIds(Arrays.asList(1L, 2L, 3L));
        requestDataList.add(requestData);
        request.setTenantPersonalityRequestData(requestDataList);

        when(cacheSupportApiUtil.getIntegerValueForProperty(anyString(), anyInt())).thenReturn(5);
        doThrow(new RuntimeException("Test Exception")).when(apiExecutor).execute(any());

        Response response = cacheInsertProcessor.processCacheInsert(request);

        assertNotNull(response);
        assertEquals(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), response.getStatus());
    }
}