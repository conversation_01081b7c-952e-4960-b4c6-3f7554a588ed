package com.kronos.persons.rest.assignments.validation;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.commonbusiness.datatypes.ref.ObjectRefList;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.EmployeeRefs;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignment;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.persons.utils.ResponseHandler;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class BulkPersonAttestationProfileAssignmentValidatorTest {
    private static final Long PERSON_ID = 1L;
    private static final String PERSON_QUALIFIER = "PERSON_QUALIFIER";
    private static final ObjectRef PERSON = new ObjectRef(PERSON_ID, PERSON_QUALIFIER);
    private static final int SEQUENCE_1 = 1;
    private static final int SEQUENCE_2 = 2;
    private static final String ACP_PERSON = "PERSON";
    private static final String DEFAULT_RECORD_BATCH_SIZE = "400";
    private static final String RECORD_BATCH_SIZE_MULTI_READ = "com.kronos.persons.rest.assignments.service.multiread.request.size";
    private static final String RECORD_BATCH_SIZE_MULTI_UPDATE = "com.kronos.persons.rest.assignments.service.multiupdate.request.size";
    private static final String REQUEST_EMPLOYEES = "employees";
    private static final Integer BATCH_SIZE = 2;

    @Mock
    private IKProperties ikProperties;

    @InjectMocks
    private BulkPersonAttestationProfileAssignmentValidator bulkPersonAttestationProfileAssignmentValidator;

    private MockedStatic<ResponseHandler> responseHandlerMockedStatic;
    private MockedStatic<PrsnValidationException> prsnValidationExceptionMockedStatic;

    @BeforeEach
    public void setUp() {
        responseHandlerMockedStatic = Mockito.mockStatic(ResponseHandler.class);
        prsnValidationExceptionMockedStatic = Mockito.mockStatic(PrsnValidationException.class);
    }

    @AfterEach
    public void tearDown() {
        responseHandlerMockedStatic.close();
        prsnValidationExceptionMockedStatic.close();
    }

    @Test
    public void checkDuplicates() throws Exception {
        Map<Integer, PersonAttestationProfileAssignment> personAttestationProfileAssignments = new HashMap<>();
        PersonAttestationProfileAssignment personAttestationProfileAssignment1 = new PersonAttestationProfileAssignment();
        PersonAttestationProfileAssignment personAttestationProfileAssignment2 = new PersonAttestationProfileAssignment();

        personAttestationProfileAssignment1.setEmployee(PERSON);
        personAttestationProfileAssignment2.setEmployee(PERSON);

        personAttestationProfileAssignments.put(SEQUENCE_1, personAttestationProfileAssignment1);
        personAttestationProfileAssignments.put(SEQUENCE_2, personAttestationProfileAssignment2);
        Map<Integer, APIException> exceptionHolder = new HashMap<>();

        bulkPersonAttestationProfileAssignmentValidator.checkDuplicates(personAttestationProfileAssignments, exceptionHolder);

        assertTrue(personAttestationProfileAssignments.size() == 1);
        assertNotNull(exceptionHolder.get(SEQUENCE_2));
    }

    @Test
    public void checkFAPPermissions() throws Exception {
        bulkPersonAttestationProfileAssignmentValidator.checkFAPPermissions();
        ResponseHandler.validateACP(ACP_PERSON);
    }

    @Test
    public void validateRequestBodyNull() throws Exception {
        assertThrows(APIException.class, () -> {
            List<PersonAttestationProfileAssignment> request = null;
            bulkPersonAttestationProfileAssignmentValidator.validateRequestBody(request);
        });
    }

    @Test
    public void validateRequestBodyEmpty() throws Exception {
        assertThrows(APIException.class, () -> {
            List<PersonAttestationProfileAssignment> request = new ArrayList<>();
            bulkPersonAttestationProfileAssignmentValidator.validateRequestBody(request);
        });
    }

    @Test
    public void checkServiceLimitForMultiUpdateOperation() throws Exception {
        when(ikProperties.getProperty(RECORD_BATCH_SIZE_MULTI_UPDATE, DEFAULT_RECORD_BATCH_SIZE)).thenReturn(BATCH_SIZE.toString());

        bulkPersonAttestationProfileAssignmentValidator.checkServiceLimitForMultiUpdateOperation(new ArrayList<>());

        ResponseHandler.validateServiceLimit(anyList(), eq(BATCH_SIZE));
    }

    @Test
    public void checkServiceLimitForMultiReadOperation() throws Exception {
        EmployeeRefs employees = new EmployeeRefs(new ObjectRefList(null, null, Arrays.asList(new ObjectRef())));

        when(ikProperties.getProperty(RECORD_BATCH_SIZE_MULTI_READ, DEFAULT_RECORD_BATCH_SIZE)).thenReturn(BATCH_SIZE.toString());

        bulkPersonAttestationProfileAssignmentValidator.checkServiceLimitForMultiReadOperation(employees);

        ResponseHandler.validateServiceLimit(anyList(), eq(BATCH_SIZE));
    }

    @Test
    public void shouldCreateExceptionIfReadRequestIsNull() throws Exception {
        assertThrows(APIException.class, () -> {
            prsnValidationExceptionMockedStatic.when(() -> PrsnValidationException.invalidPropertyValue(REQUEST_EMPLOYEES, null)).thenThrow(new APIException());
            bulkPersonAttestationProfileAssignmentValidator.validateMultiReadRequest(null);
        });
    }

    @Test
    public void shouldCreateExceptionIfNoEmployees() throws Exception {
        assertThrows(APIException.class, () -> {
            prsnValidationExceptionMockedStatic.when(() -> PrsnValidationException.invalidPropertyValue(REQUEST_EMPLOYEES, null)).thenThrow(new APIException());
            bulkPersonAttestationProfileAssignmentValidator.validateMultiReadRequest(new EmployeeRefs());
        });
    }
}