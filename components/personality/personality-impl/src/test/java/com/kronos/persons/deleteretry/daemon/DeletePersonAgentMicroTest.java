/*******************************************************************************
 * DeletePersonAgentMicroTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.deleteretry.daemon;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;
import java.util.HashSet;
import java.util.Collections;

import com.kronos.tenantprovider.api.TenantProvider;
import com.kronos.tenantprovider.api.exception.TenantProviderException;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonalityToDelete;
import com.kronos.wfc.platform.logging.framework.Log;
import com.kronos.wfc.platform.security.business.state.State;
import com.kronos.wfc.platform.tenant.business.TenantCache;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.persons.deleteretry.model.PersonDeleteRetryEntity;
import com.kronos.persons.deleteretry.service.PersonDeleteRetryDataAccessServiceImpl;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Test class for DeletePersonAgent
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class DeletePersonAgentMicroTest {

    private static final String EXTERNAL_TENANT_NAME = "DEFAULT";
    private static final int DEFAULT_RETRY_COUNT = 1;

    @Mock
    private PersonDeleteRetryDataAccessServiceImpl personDeleteRetryDataAccessService;

    @Mock
    private TenantProvider tenantProvider;

    @Mock
    private IKProperties kronosProperties;

    @InjectMocks
    @Spy
    private DeletePersonAgent deletePersonAgent = new DeletePersonAgent();

    private static MockedStatic<State> stateMockedStatic;
    private static MockedStatic<TenantCache> tenantCacheMockedStatic;
    private static MockedStatic<Log> logMockedStatic;

    @BeforeAll
    public static void setUpClass() {
        stateMockedStatic = Mockito.mockStatic(State.class);
        tenantCacheMockedStatic = Mockito.mockStatic(TenantCache.class);
        logMockedStatic = Mockito.mockStatic(Log.class);
    }

    @AfterAll
    public static void tearDownClass() {
        stateMockedStatic.close();
        tenantCacheMockedStatic.close();
        logMockedStatic.close();
    }

    @Test
    public void givenTenantIDs_whenExecuteIsCalled_thenExpectSuccessfulExecutionOfPerformDelete() {
        HashSet<Long> personIdLong = new HashSet<>();
        personIdLong.add(NumberUtils.LONG_ONE);
        TenantCache tenantCache = mock(TenantCache.class);
        when(TenantCache.getCache()).thenReturn(tenantCache);
        when(tenantCache.getExternalTenantIdfromTenantId(Mockito.anyLong())).thenReturn(EXTERNAL_TENANT_NAME);
        when(tenantCache.getAllTenantIds()).thenReturn(personIdLong);

        deletePersonAgent.execute();
        Mockito.verify(tenantProvider).setTenantId(EXTERNAL_TENANT_NAME);
        Mockito.verify(deletePersonAgent).performDelete();
        Mockito.verify(tenantProvider).removeTenantId();
    }

    @Test
    public void givenCallForTenantIDsFailed_whenExecuteIsCalled_thenExpectAnExceptionGettingLogged() {
        TenantProviderException tenantProviderException = new TenantProviderException("Exception in DeletePersonAgent");
        TenantCache tenantCache = mock(TenantCache.class);
        when(TenantCache.getCache()).thenReturn(tenantCache);
        when(tenantCache.getAllTenantIds()).thenThrow(tenantProviderException);

        deletePersonAgent.execute();
        Mockito.verify(tenantProvider, Mockito.times(1)).removeTenantId();
        logMockedStatic.verify(() -> Log.log(Log.ERROR, tenantProviderException.getMessage(), tenantProviderException));
    }

    @Test
    public void givenPersonDeleteRetryWithPositivePersonIdToDelete_whenPerformDeleteIsCalled_thenPersonIsDeletedAndSaved() throws Exception {
        when(kronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn(DeletePersonAgent.THRESHOLD);
        List<PersonDeleteRetryEntity> inputData = Collections.singletonList(createPersonDeleteRetryEntityTestData(BigInteger.ONE));

        configureDeletePersonAgentJobPreconditions(inputData);

        try (MockedConstruction<PersonalityToDelete> mockedPersonalityToDelete =
            Mockito.mockConstruction(PersonalityToDelete.class, (mock, context) -> {
                when(mock.delete()).thenReturn(Boolean.TRUE);
            })) {
            deletePersonAgent.performDelete();
        }
        PersonDeleteRetryEntity updatedEntity = inputData.get(0);
        assertSame(NumberUtils.INTEGER_ONE, updatedEntity.getIsPersonDeleted());
        assertSame((DEFAULT_RETRY_COUNT + 1), updatedEntity.getRetryCount());
        Mockito.verify(personDeleteRetryDataAccessService, Mockito.times(1)).save(updatedEntity);
    }

    @Test
    public void givenPersonDeleteRetryWithZeroPersonIdToDelete_whenPerformDeleteIsCalled_thenPersonIsNotDeletedAndSaved() throws Exception {
        when(kronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn(DeletePersonAgent.THRESHOLD);
        PersonalityToDelete personalityToDelete = mock(PersonalityToDelete.class);
        List<PersonDeleteRetryEntity> inputData = Collections.singletonList(createPersonDeleteRetryEntityTestData(BigInteger.ZERO));

        configureDeletePersonAgentJobPreconditions(inputData);

        deletePersonAgent.performDelete();
        PersonDeleteRetryEntity updatedEntity = inputData.get(0);
        assertNull(updatedEntity.getIsPersonDeleted());
        assertSame(DEFAULT_RETRY_COUNT, updatedEntity.getRetryCount());
        Mockito.verify(personDeleteRetryDataAccessService, Mockito.times(0)).save(updatedEntity);
    }

    @Test
    public void givenDeleteOperationIsFailedOnPersonIdToDelete_whenPerformDeleteIsCalled_thenPersonIsNotDeletedAndSaved() throws Exception {
        when(kronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn(DeletePersonAgent.THRESHOLD);
        Mockito.mock(PersonalityToDelete.class);
        List<PersonDeleteRetryEntity> inputData = Collections.singletonList(createPersonDeleteRetryEntityTestData(BigInteger.ONE));

        configureDeletePersonAgentJobPreconditions(inputData);

        try (MockedConstruction<PersonalityToDelete> mockedPersonalityToDelete =
            Mockito.mockConstruction(PersonalityToDelete.class, (mock, context) -> {
                when(mock.delete()).thenReturn(Boolean.FALSE);
            })) {
            deletePersonAgent.performDelete();
        }
        PersonDeleteRetryEntity updatedEntity = inputData.get(0);
        assertNull(updatedEntity.getIsPersonDeleted());
        assertSame(DEFAULT_RETRY_COUNT, updatedEntity.getRetryCount());
        Mockito.verify(personDeleteRetryDataAccessService, Mockito.times(0)).save(updatedEntity);
    }

    private void configureDeletePersonAgentJobPreconditions(List<PersonDeleteRetryEntity> inputData) {
        when(personDeleteRetryDataAccessService.getPersonIdsToDelete(Mockito.any(), Mockito.any())).thenReturn(inputData);
        when(personDeleteRetryDataAccessService.updatePersonRetryCount(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(NumberUtils.INTEGER_ONE);
        Mockito.doNothing().when(personDeleteRetryDataAccessService).removeDeletedPerson();
        Mockito.doNothing().when(personDeleteRetryDataAccessService).removeOldRecords(Mockito.any(Timestamp.class));
    }

    private PersonDeleteRetryEntity createPersonDeleteRetryEntityTestData(BigInteger personId) {
        PersonDeleteRetryEntity personDeleteRetry = new PersonDeleteRetryEntity();
        personDeleteRetry.setDeletedBy(EXTERNAL_TENANT_NAME);
        personDeleteRetry.setPersonId(personId);
        personDeleteRetry.setRetryCount(DEFAULT_RETRY_COUNT);
        return personDeleteRetry;
    }
}