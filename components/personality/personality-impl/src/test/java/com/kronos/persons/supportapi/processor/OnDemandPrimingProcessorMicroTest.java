package com.kronos.persons.supportapi.processor;

import com.kronos.api.commoncomponent.async.APIExecutor;
import com.kronos.api.commoncomponent.util.CommonUtils;
import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.util.LogService;
import com.kronos.people.personality.cache.PersonalityCachePrimingController;
import com.kronos.people.personality.properties.KronosPropertiesFacade;
import com.kronos.persons.rest.supportapi.validation.CacheSupportApiUtil;
import com.kronos.persons.rest.supportapi.validation.SupportApiValidator;
import com.kronos.persons.supportapi.dto.OnDemandPrimingRequest;
import com.kronos.persons.supportapi.errorcodes.PersonalitySupportApiErrorCodes;
import com.kronos.tenantprovider.api.TenantProvider;
import jakarta.ws.rs.core.Response;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.concurrent.ExecutorService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class OnDemandPrimingProcessorMicroTest {

    @InjectMocks
    private OnDemandPrimingProcessor onDemandPrimingProcessor;

    @Mock
    private SupportApiValidator supportApiValidator;

    @Mock
    private CacheSupportApiUtil cacheSupportApiUtil;

    @Mock
    private APIExecutor apiExecutor;

    @Mock
    private KronosThreadPoolService kronosThreadPoolService;

    @Mock
    private ExecutorService executor;

    @Mock
    private CommonUtils commonUtils;

    @Mock
    private TenantProvider tenantProvider;

    @Mock
    LogService logService;

    @Mock
    private KronosPropertiesFacade kronosPropertiesFacade;

    @Mock
    private PersonalityCachePrimingController primingController;

    @BeforeEach
    public void setUp() {
        when(kronosThreadPoolService.newThreadPool(anyString())).thenReturn(executor);
        onDemandPrimingProcessor.startup();
    }

    @Test
    public void testValidatePrimingRequest() {
        OnDemandPrimingRequest request = new OnDemandPrimingRequest();
        request.setTenantShortNames(Arrays.asList("tenant1", "tenant2"));

        when(cacheSupportApiUtil.getIntegerValueForProperty(anyString(), anyInt())).thenReturn(10);

        onDemandPrimingProcessor.validatePrimingRequest(request);

        verify(supportApiValidator).validateIfRequestIsBlank(request);
        verify(supportApiValidator).validateTotalTenantsInRequest(2, 10);
        verify(supportApiValidator).validateTenantShortNames(request.getTenantShortNames());
    }

    @Test
    public void testPrimePersonalityCacheProcessor_Success() throws Exception {
        OnDemandPrimingRequest request = new OnDemandPrimingRequest();
        request.setTenantShortNames(Arrays.asList("tenant1"));

        when(apiExecutor.execute(any())).thenReturn(new JSONObject().put("response", "[]"));

        Response response = onDemandPrimingProcessor.primePersonalityCacheProcessor(request);

        assertNotNull(response);
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
    }

    @Test
    public void testPrimePersonalityCacheProcessor_APIException() {
        OnDemandPrimingRequest request = new OnDemandPrimingRequest();
        request.setTenantShortNames(Arrays.asList("tenant1", "tenant2"));

        doThrow(new APIException(PersonalitySupportApiErrorCodes.EMPTY_REQUEST_ERROR_CODE))
                .when(supportApiValidator).validateIfRequestIsBlank(request);

        APIException exception = assertThrows(APIException.class, () -> {
            onDemandPrimingProcessor.primePersonalityCacheProcessor(request);
        });

        assertEquals(PersonalitySupportApiErrorCodes.EMPTY_REQUEST_ERROR_CODE, exception.getErrorCode());
    }

    @Test
    public void testPrimePersonalityCacheProcessor_Exception() {
        OnDemandPrimingRequest request = new OnDemandPrimingRequest();
        request.setTenantShortNames(Arrays.asList("tenant1", "tenant2"));

        doThrow(new RuntimeException("Test Exception"))
                .when(apiExecutor).execute(any());

        APIException exception = assertThrows(APIException.class, () -> {
            onDemandPrimingProcessor.primePersonalityCacheProcessor(request);
        });

        assertEquals(PersonalitySupportApiErrorCodes.EXCEPTION_OCCURRED_WHILE_VALIDATING_PERSON_CACHE, exception.getErrorCode());
    }
}