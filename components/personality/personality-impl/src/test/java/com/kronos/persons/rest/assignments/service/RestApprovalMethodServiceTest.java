package com.kronos.persons.rest.assignments.service;

import com.kronos.commonapp.authz.api.fap.service.IAccessProfileService;
import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.wfc.platform.security.shared.SecurityConstants;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.List;
import jakarta.ws.rs.core.Response;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;

/**
 * Verification for {@link RestApprovalMethodService}.
 * Copyright (C) 2020 Kronos.com
 * Date: Mar 26, 2020
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class RestApprovalMethodServiceTest {
    private static final String SINGLE_APPROVER = "Single Approver";
    private static final String JOB_APPROVAL_BY_LOCATION_MANAGER = "Job Approval by Location Manager";
    private static final long SINGLE_APPROVER_ID = 0L;
    private static final long JOB_APPROVAL_ID = 1L;

    @Mock
    private IKProperties kProperties;
    @Mock
    private IAccessProfileService accessProfileService;
    @InjectMocks
    private RestApprovalMethodService restService;

    @Test
    public void testGetAllApprovalMethodsAccessAllowed() {
        Mockito.when(kProperties.getProperty(Mockito.anyString()))
                .thenReturn(SINGLE_APPROVER)
                .thenReturn(JOB_APPROVAL_BY_LOCATION_MANAGER);
        Mockito.when(accessProfileService.isPermitted(
                eq(SecurityConstants.STANDARD_EMPLOYEE_VIEW),
                eq(SecurityConstants.VIEW))).thenReturn(true);
        Response response = restService.getAllApprovalMethods();
        List<ObjectRef> result = (List<ObjectRef>) response.getEntity();
        assertEquals(2, result.size());
        assertEquals(SINGLE_APPROVER_ID, result.get(0).getId().longValue());
        assertEquals(SINGLE_APPROVER, result.get(0).getQualifier());
        assertEquals(JOB_APPROVAL_ID, result.get(1).getId().longValue());
        assertEquals(JOB_APPROVAL_BY_LOCATION_MANAGER, result.get(1).getQualifier());
    }

    @Test
    public void testGetAllApprovalMethodsAccessDisallowed() {
        Mockito.when(accessProfileService.isPermitted(
                eq(SecurityConstants.STANDARD_EMPLOYEE_VIEW),
                eq(SecurityConstants.VIEW))).thenReturn(false);
        Response response = null;
        try {
            response = restService.getAllApprovalMethods();
        } catch (APIException apiException) {
            assertNull(response);
            assertNotNull(apiException);
            assertEquals(ExceptionConstants.ACCESS_VIOLATED_TO_APPROVAL_METHOD, apiException.getErrorCode());
        }
    }
}
