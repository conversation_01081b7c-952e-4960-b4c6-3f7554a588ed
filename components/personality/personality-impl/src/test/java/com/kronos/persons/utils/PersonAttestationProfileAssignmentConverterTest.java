package com.kronos.persons.utils;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.people.personality.model.AttestationProfileAssignmentDTO;
import com.kronos.people.personality.model.PersonAttestationProfileAssignmentsDTO;
import com.kronos.persons.rest.assignments.model.AttestationProfileAssignment;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignment;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;


@ExtendWith(MockitoExtension.class)
public class PersonAttestationProfileAssignmentConverterTest {
    private static final Long PERSON_ID = 2L;
    private static final String PERSON_QUALIFIER = "PERSON_QUALIFIER";
    private static final Long PROFILE_ID = 3L;
    private static final String PROFILE_QUALIFIER = "PROFILE_QUALIFIER";

    private static final Integer SEQUENCE_1 = 1;
    private static final ObjectRef PERSON = new ObjectRef(PERSON_ID, PERSON_QUALIFIER);
    private static final ObjectRef PROFILE = new ObjectRef(PROFILE_ID, PROFILE_QUALIFIER);
    private static final LocalDate EFFECTIVE_DATE = LocalDate.now();
    private static final LocalDate EXPIRATION_DATE = EFFECTIVE_DATE.plusDays(5);


    private PersonAttestationProfileAssignmentConverter converter = new PersonAttestationProfileAssignmentConverter();

    @Test
    public void convertToDTO() throws Exception {
        Map<Integer, PersonAttestationProfileAssignment> assignments = new HashMap<>();
        PersonAttestationProfileAssignment assignment = new PersonAttestationProfileAssignment();
        assignment.setEmployee(PERSON);
        List<AttestationProfileAssignment> attestationProfileAssignments = new ArrayList<>();
        attestationProfileAssignments.add(buildAttestationProfileAssignment());
        assignment.setAttestationProfileAssignments(attestationProfileAssignments);
        assignments.put(SEQUENCE_1, assignment);

        final Map<Integer, PersonAttestationProfileAssignmentsDTO> result = converter.convertToDTO(assignments);

        final PersonAttestationProfileAssignmentsDTO assignmentDTO = result.get(SEQUENCE_1);
        assertEquals(assignmentDTO.getPerson(), PERSON);
        final AttestationProfileAssignmentDTO attestationProfileAssignmentDTO = assignmentDTO.getAttestationProfileAssignments().get(0);
        assertEquals(attestationProfileAssignmentDTO.getEffectiveDate(), EFFECTIVE_DATE);
        assertEquals(attestationProfileAssignmentDTO.getExpirationDate(), EXPIRATION_DATE);
    }

    @Test
    public void convertToAPI() throws Exception {
        Map<Integer, PersonAttestationProfileAssignmentsDTO> assignments = new HashMap<>();
        PersonAttestationProfileAssignmentsDTO assignmentDTO = new PersonAttestationProfileAssignmentsDTO();
        assignmentDTO.setPerson(PERSON);
        List<AttestationProfileAssignmentDTO> attestationProfileAssignments = new ArrayList<>();
        AttestationProfileAssignmentDTO attestationProfileAssignmentDTO = new AttestationProfileAssignmentDTO();
        attestationProfileAssignmentDTO.setEffectiveDate(EFFECTIVE_DATE);
        attestationProfileAssignmentDTO.setExpirationDate(EXPIRATION_DATE);
        attestationProfileAssignmentDTO.setProfile(PROFILE);
        attestationProfileAssignments.add(attestationProfileAssignmentDTO);
        assignmentDTO.setAttestationProfileAssignments(attestationProfileAssignments);
        assignments.put(SEQUENCE_1, assignmentDTO);

        final Map<Integer, PersonAttestationProfileAssignment> result = converter.convertToAPI(assignments);

        final PersonAttestationProfileAssignment assignment = result.get(SEQUENCE_1);
        assertEquals(assignment.getEmployee(), PERSON);
        final AttestationProfileAssignment attestationProfileAssignment = assignment.getAttestationProfileAssignments().get(0);
        assertEquals(attestationProfileAssignment.getEffectiveDate(), EFFECTIVE_DATE.toString());
        assertEquals(attestationProfileAssignment.getExpirationDate(), EXPIRATION_DATE.toString());
    }

    @Test
    public void convertToDTOWhenManagerRoleAttestationProfileAssignmentsPresent() {
        Map<Integer, PersonAttestationProfileAssignment> assignments = new HashMap<>();
        PersonAttestationProfileAssignment assignment = new PersonAttestationProfileAssignment();
        assignment.setEmployee(PERSON);
        List<AttestationProfileAssignment> attestationProfileAssignments = new ArrayList<>();
        attestationProfileAssignments.add(buildAttestationProfileAssignment());
        assignment.setManagerRoleAttestationProfileAssignments(attestationProfileAssignments);
        assignments.put(SEQUENCE_1, assignment);

        final Map<Integer, PersonAttestationProfileAssignmentsDTO> result = converter.convertToDTO(assignments);

        final PersonAttestationProfileAssignmentsDTO assignmentDTO = result.get(SEQUENCE_1);
        assertEquals(assignmentDTO.getPerson(), PERSON);
        final AttestationProfileAssignmentDTO attestationProfileAssignmentDTO = assignmentDTO.getManagerRoleAttestationProfileAssignments().get(0);
        assertEquals(attestationProfileAssignmentDTO.getEffectiveDate(), EFFECTIVE_DATE);
        assertEquals(attestationProfileAssignmentDTO.getExpirationDate(), EXPIRATION_DATE);
    }

    private static AttestationProfileAssignment buildAttestationProfileAssignment() {
        AttestationProfileAssignment attestationProfileAssignment = new AttestationProfileAssignment();
        attestationProfileAssignment.setEffectiveDate(EFFECTIVE_DATE.toString());
        attestationProfileAssignment.setExpirationDate(EXPIRATION_DATE.toString());
        attestationProfileAssignment.setProfile(PROFILE);
        return attestationProfileAssignment;
    }

}