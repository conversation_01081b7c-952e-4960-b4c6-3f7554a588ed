package com.kronos.persons.rest.assignments.service.adjustmentrule;

import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.persons.rest.assignments.service.adjustmentrule.bean.ProcessorToEmployeeBean;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class AssignmentDateResolverTest {
    private static final String UNEXPECTED_AMOUNT_OF_ASSIGNMENTS = "Unexpected amount of assignments.";
    private static final String PROCESSOR = "Processor Name";
    private static final String SECOND_PROCESSOR = "Second Processor Name";
    private static final String THIRD_PROCESSOR = "Third Processor Name";
    private static final AdapterHelper ADAPTER_HELPER = new AdapterHelper();
    private static final LocalDate NOW = LocalDate.now();

    private static final KDate TODAY = ADAPTER_HELPER.localDateToKdate(NOW);
    private static final KDate TOMORROW = ADAPTER_HELPER.localDateToKdate(NOW.plusDays(1));
    private static final KDate YESTERDAY = ADAPTER_HELPER.localDateToKdate(NOW.minusDays(1));
    private static final KDate TODAY_PLUS_TWO_DAYS = ADAPTER_HELPER.localDateToKdate(NOW.plusDays(2));
    private static final KDate TODAY_PLUS_THREE_DAYS = ADAPTER_HELPER.localDateToKdate(NOW.plusDays(3));
    private static final KDate TODAY_PLUS_FOUR_DAYS = ADAPTER_HELPER.localDateToKdate(NOW.plusDays(4));
    private static final KDate TODAY_PLUS_FIVE_DAYS = ADAPTER_HELPER.localDateToKdate(NOW.plusDays(5));
    private static final KDate ENDLESS = KDate.getEotDate();

    @Test
    public void testAddOneAssignmentToEmptyProfile() {
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, TODAY, TOMORROW), new ArrayList<>());
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, TODAY, TOMORROW);
    }

    @Test
    public void testNewAssignmentBefore() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TODAY, TOMORROW)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, TODAY_PLUS_THREE_DAYS, TODAY_PLUS_FIVE_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(2));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, TODAY, TOMORROW);
        assertAssignmentNameAndDates(result.get(1), PROCESSOR, TODAY_PLUS_THREE_DAYS, TODAY_PLUS_FIVE_DAYS);
    }

    @Test
    public void testCreateNonConflictAfter() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TODAY_PLUS_THREE_DAYS, TODAY_PLUS_FIVE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, TODAY, TOMORROW),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(2));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, TODAY, TOMORROW);
        assertAssignmentNameAndDates(result.get(1), PROCESSOR, TODAY_PLUS_THREE_DAYS, TODAY_PLUS_FIVE_DAYS);
    }

    @Test
    public void testEndInsideSameRule() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TOMORROW, TODAY_PLUS_THREE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, YESTERDAY, TODAY_PLUS_TWO_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, TODAY_PLUS_THREE_DAYS);
    }

    @Test
    public void testCreateDatesOverlapStartInsideDifferentRules() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TOMORROW, TODAY_PLUS_THREE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(SECOND_PROCESSOR, YESTERDAY, TODAY_PLUS_TWO_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(2));
        assertAssignmentNameAndDates(result.get(0), SECOND_PROCESSOR, YESTERDAY, TODAY_PLUS_TWO_DAYS);
        assertAssignmentNameAndDates(result.get(1), PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_THREE_DAYS);
    }

    @Test
    public void testEnclosingDifferentRules() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TODAY, TOMORROW)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(SECOND_PROCESSOR, YESTERDAY, TODAY_PLUS_TWO_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), SECOND_PROCESSOR, YESTERDAY, TODAY_PLUS_TWO_DAYS);
    }

    @Test
    public void testEnclosingDifferentRulesWithRuleAfter() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(Arrays.asList(
                createAssignment(PROCESSOR, TODAY, TOMORROW),
                createAssignment(THIRD_PROCESSOR, TODAY_PLUS_THREE_DAYS, TODAY_PLUS_FIVE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(SECOND_PROCESSOR, YESTERDAY, TODAY_PLUS_TWO_DAYS),
                        existingAssignments);

        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(2));
        assertAssignmentNameAndDates(result.get(0), SECOND_PROCESSOR, YESTERDAY, TODAY_PLUS_TWO_DAYS);
        assertAssignmentNameAndDates(result.get(1), THIRD_PROCESSOR, TODAY_PLUS_THREE_DAYS, TODAY_PLUS_FIVE_DAYS);
    }

    @Test
    public void testDatesOverlapStartTouching() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_FOUR_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(SECOND_PROCESSOR, TODAY, TODAY_PLUS_TWO_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(2));
        assertAssignmentNameAndDates(result.get(0), SECOND_PROCESSOR, TODAY, TODAY_PLUS_TWO_DAYS);
        assertAssignmentNameAndDates(result.get(1), PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_FOUR_DAYS);
    }

    @Test
    public void testDatesOverlapStartTouchingNotSingeExistAssignment() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Arrays.asList(createAssignment(SECOND_PROCESSOR, TOMORROW, TODAY_PLUS_TWO_DAYS),
                        createAssignment(THIRD_PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_THREE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, TODAY, TOMORROW),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(3));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, TODAY, TOMORROW);
        assertAssignmentNameAndDates(result.get(1), SECOND_PROCESSOR, TOMORROW, TODAY_PLUS_TWO_DAYS);
        assertAssignmentNameAndDates(result.get(2), THIRD_PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_THREE_DAYS);
    }

    @Test
    public void testDatesOverlapEndTouching() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TODAY, TODAY_PLUS_TWO_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(SECOND_PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_FOUR_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(2));

        assertAssignmentNameAndDates(result.get(0), PROCESSOR, TODAY, TODAY_PLUS_TWO_DAYS);
        assertAssignmentNameAndDates(result.get(1), SECOND_PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_FOUR_DAYS);
    }

    @Test
    public void testDatesOverlapStartTouchingSameRule() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_FOUR_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, TODAY, TODAY_PLUS_TWO_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, TODAY, TODAY_PLUS_FOUR_DAYS);
    }

    @Test
    public void testStartTouchingSameRule() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TODAY, TODAY_PLUS_TWO_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_FOUR_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));

        assertAssignmentNameAndDates(result.get(0), PROCESSOR, TODAY, TODAY_PLUS_FOUR_DAYS);
    }

    @Test
    public void testNewAssignmentInside() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, YESTERDAY, TODAY_PLUS_FOUR_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(SECOND_PROCESSOR, TOMORROW, TODAY_PLUS_TWO_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(3));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, TOMORROW);
        assertAssignmentNameAndDates(result.get(1), SECOND_PROCESSOR, TOMORROW, TODAY_PLUS_TWO_DAYS);
        assertAssignmentNameAndDates(result.get(2), PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_FOUR_DAYS);
    }

    @Test
    public void testStartAndEndTouching() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(Arrays.asList(
                createAssignment(PROCESSOR, YESTERDAY, TODAY),
                createAssignment(THIRD_PROCESSOR, TOMORROW, TODAY_PLUS_THREE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(SECOND_PROCESSOR, TODAY, TOMORROW),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(3));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, TODAY);
        assertAssignmentNameAndDates(result.get(1), SECOND_PROCESSOR, TODAY, TOMORROW);
        assertAssignmentNameAndDates(result.get(2), THIRD_PROCESSOR, TOMORROW, TODAY_PLUS_THREE_DAYS);
    }

    @Test
    public void testStartAndEndTouchingSameRule() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(Arrays.asList(
                createAssignment(PROCESSOR, YESTERDAY, TODAY),
                createAssignment(PROCESSOR, TOMORROW, TODAY_PLUS_THREE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, TODAY, TOMORROW),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, TODAY_PLUS_THREE_DAYS);

    }

    @Test
    public void testStartAndEndInsideDifferentRules() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(Arrays.asList(
                createAssignment(PROCESSOR, YESTERDAY, TOMORROW),
                createAssignment(THIRD_PROCESSOR, TODAY_PLUS_THREE_DAYS, TODAY_PLUS_FIVE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(SECOND_PROCESSOR, TODAY, TODAY_PLUS_FOUR_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(3));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, TODAY);
        assertAssignmentNameAndDates(result.get(1), SECOND_PROCESSOR, TODAY, TODAY_PLUS_FOUR_DAYS);
        assertAssignmentNameAndDates(result.get(2), THIRD_PROCESSOR, TODAY_PLUS_FOUR_DAYS, TODAY_PLUS_FIVE_DAYS);
    }

    @Test
    public void testStartAndEndInsideSameRules() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(Arrays.asList(
                createAssignment(PROCESSOR, YESTERDAY, TOMORROW),
                createAssignment(PROCESSOR, TODAY_PLUS_THREE_DAYS, TODAY_PLUS_FIVE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, TODAY, TODAY_PLUS_FOUR_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, TODAY_PLUS_FIVE_DAYS);
    }

    @Test
    public void testEndlessEnclosingDifferentRules() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(Arrays.asList(
                createAssignment(PROCESSOR, TODAY, TOMORROW),
                createAssignment(SECOND_PROCESSOR, TODAY_PLUS_THREE_DAYS, TODAY_PLUS_FIVE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(THIRD_PROCESSOR, YESTERDAY, ENDLESS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), THIRD_PROCESSOR, YESTERDAY, ENDLESS);
    }

    @Test
    public void testEndlessEnclosingSameRules() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(Arrays.asList(
                createAssignment(PROCESSOR, TODAY, TOMORROW),
                createAssignment(PROCESSOR, TODAY_PLUS_THREE_DAYS, TODAY_PLUS_FIVE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, YESTERDAY, ENDLESS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, ENDLESS);
    }

    @Test
    public void testEndlessStartTouchingDifferentRules() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(Arrays.asList(
                createAssignment(PROCESSOR, YESTERDAY, TODAY),
                createAssignment(SECOND_PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_THREE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(THIRD_PROCESSOR, TODAY, ENDLESS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(2));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, TODAY);
        assertAssignmentNameAndDates(result.get(1), THIRD_PROCESSOR, TODAY, ENDLESS);
    }

    @Test
    public void testEndlessStartTouchingSameRules() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(Arrays.asList(
                createAssignment(PROCESSOR, YESTERDAY, TODAY),
                createAssignment(PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_THREE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, TODAY, ENDLESS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, ENDLESS);

    }

    @Test
    public void testEndlessStartInsideEndlessDifferentRules() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, YESTERDAY, ENDLESS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(SECOND_PROCESSOR, TODAY, ENDLESS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(2));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, TODAY);
        assertAssignmentNameAndDates(result.get(1), SECOND_PROCESSOR, TODAY, ENDLESS);

    }

    @Test
    public void testEndlessStartInsideEndlessSameRules() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, YESTERDAY, ENDLESS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, TODAY, ENDLESS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, ENDLESS);
    }

    @Test
    public void testEndlessEndInsideEndlessSameRules() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TODAY, ENDLESS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, YESTERDAY, ENDLESS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, ENDLESS);
    }

    @Test
    public void testExactMatchSameRule() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TODAY, TOMORROW)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(PROCESSOR, TODAY, TOMORROW),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, TODAY, TOMORROW);
    }

    @Test
    public void testExactMatchDifferentRule() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TODAY, TOMORROW)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(SECOND_PROCESSOR, TODAY, TOMORROW),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), SECOND_PROCESSOR, TODAY, TOMORROW);

    }

    @Test
    public void testNewEndlessEnclosing() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TODAY, TOMORROW)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(SECOND_PROCESSOR, TODAY, ENDLESS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), SECOND_PROCESSOR, TODAY, ENDLESS);

    }

    @Test
    public void testInsideStartInSameDate() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TODAY, TODAY_PLUS_FIVE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .pastNewAssignmentInToExistingAssignmentCollection(
                        createAssignment(SECOND_PROCESSOR, TODAY, TOMORROW),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(2));
        assertAssignmentNameAndDates(result.get(0), SECOND_PROCESSOR, TODAY, TOMORROW);
        assertAssignmentNameAndDates(result.get(1), PROCESSOR, TOMORROW, TODAY_PLUS_FIVE_DAYS);
    }

    @Test
    public void testRemoveOne() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(
                Collections.singletonList(createAssignment(PROCESSOR, TODAY, TODAY_PLUS_FIVE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .removeAssignmentFromExistingAssignmentCollection(
                        createAssignment(PROCESSOR, TODAY, TODAY_PLUS_FIVE_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(0));
    }

    @Test
    public void testRemoveFirs() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(Arrays.asList(
                createAssignment(PROCESSOR, YESTERDAY, TODAY),
                createAssignment(SECOND_PROCESSOR, TOMORROW, TODAY_PLUS_TWO_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .removeAssignmentFromExistingAssignmentCollection(
                        createAssignment(PROCESSOR, YESTERDAY, TODAY),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(1));
        assertAssignmentNameAndDates(result.get(0), SECOND_PROCESSOR, TOMORROW, TODAY_PLUS_TWO_DAYS);
    }

    @Test
    public void testRemoveMiddle() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(Arrays.asList(
                createAssignment(PROCESSOR, YESTERDAY, TODAY),
                createAssignment(SECOND_PROCESSOR, TODAY, TODAY_PLUS_TWO_DAYS),
                createAssignment(THIRD_PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_THREE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .removeAssignmentFromExistingAssignmentCollection(
                        createAssignment(SECOND_PROCESSOR, TODAY, TODAY_PLUS_TWO_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(2));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, TODAY_PLUS_TWO_DAYS);
        assertAssignmentNameAndDates(result.get(1), THIRD_PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_THREE_DAYS);
    }

    @Test
    public void testRemoveLast() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(Arrays.asList(
                createAssignment(PROCESSOR, YESTERDAY, TODAY),
                createAssignment(SECOND_PROCESSOR, TODAY, TODAY_PLUS_TWO_DAYS),
                createAssignment(THIRD_PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_THREE_DAYS)));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .removeAssignmentFromExistingAssignmentCollection(
                        createAssignment(THIRD_PROCESSOR, TODAY_PLUS_TWO_DAYS, TODAY_PLUS_THREE_DAYS),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(2));
        assertAssignmentNameAndDates(result.get(0), PROCESSOR, YESTERDAY, TODAY);
        assertAssignmentNameAndDates(result.get(1), SECOND_PROCESSOR, TODAY, TODAY_PLUS_THREE_DAYS);
    }

    @Test
    public void testRemoveAndUpdateExpirationDatePreviousAssignment() {
        List<ProcessorToEmployeeBean> existingAssignments = new ArrayList<>(Arrays.asList(
                createAssignment(PROCESSOR, YESTERDAY, TODAY),
                createAssignment(SECOND_PROCESSOR, TODAY, TODAY_PLUS_TWO_DAYS),
                createAssignment(THIRD_PROCESSOR, TODAY_PLUS_TWO_DAYS.plusDays(3), TODAY_PLUS_THREE_DAYS.plusDays(3))));
        List<ProcessorToEmployeeBean> result = AssignmentDateResolver
                .removeAssignmentFromExistingAssignmentCollection(
                        createAssignment(THIRD_PROCESSOR, TODAY_PLUS_TWO_DAYS.plusDays(3), TODAY_PLUS_THREE_DAYS.plusDays(3)),
                        existingAssignments);
        assertThat(UNEXPECTED_AMOUNT_OF_ASSIGNMENTS, result.size(), is(2));
        assertAssignmentNameAndDates(result.get(1), SECOND_PROCESSOR, TODAY, TODAY_PLUS_TWO_DAYS);
    }

    private ProcessorToEmployeeBean createAssignment(String processor, KDate start, KDate end) {
        ProcessorToEmployeeBean assignment = new ProcessorToEmployeeBean();
        assignment.setEffectiveDate(start);
        assignment.setExpirationDate(end);
        assignment.setProcessor(processor);
        return assignment;
    }

    private void assertAssignmentNameAndDates(ProcessorToEmployeeBean resultAssignment, String processor,
                                              KDate expectedStartDate, KDate expectedEndDate) {
        assertThat("Effective date doesn't match.", resultAssignment.getEffectiveDate(), is(expectedStartDate));
        assertThat("Expiration date doesn't match.", resultAssignment.getExpirationDate(), is(expectedEndDate));
        assertThat("Processor name doesn't match.", resultAssignment.getProcessor(), is(processor));
    }
}
