package com.kronos.persons.utils;

import java.util.ArrayList;
import java.util.Collection;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.entry.ContactEntry;
import com.kronos.people.personality.model.extension.entry.PostalAddressEntry;

import com.kronos.persons.rest.model.UserInfoBean;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.types.business.EMailAddressType;
import com.kronos.wfc.commonapp.types.business.PostalAddressType;
import com.kronos.wfc.commonapp.types.business.TelephoneNumberType;
import com.kronos.wfc.platform.persistence.framework.ObjectId;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CurrentUserContactDetailsPopulatorTest {

   @InjectMocks
   private CurrentUserContactDetailsPopulator currentUserContactDetailsPopulator;
   @Mock
   private UserInfoBean userInfoBean;
   @Mock
   private EmployeeExtension extension;

   private MockedStatic<TelephoneNumberType> mockedTelephoneNumberType;

   private Collection<ContactEntry> emailContactEntries;
   @Mock
   private Personality personality;
   @Mock
   private EMailAddressType emailAddressType;
   @Mock
   private TelephoneNumberType telephoneNumberType;
   @Mock
   private PostalAddressType postalAddressType;

   @BeforeEach
   public void setUp() {
      mockedTelephoneNumberType = Mockito.mockStatic(TelephoneNumberType.class);
   }

   @AfterEach
   public void teardown(){
        mockedTelephoneNumberType.close();
   }

   @Test
   public void testPopulateEmailAddress() {
      try (MockedStatic<EMailAddressType> mockedEmailAddressType = Mockito.mockStatic(EMailAddressType.class)) {
         mockedEmailAddressType.when(() -> EMailAddressType.getEMailAddressType(Mockito.any(ObjectId.class))).thenReturn(emailAddressType);

         extension = new EmployeeExtension();
         emailContactEntries = new ArrayList<>();
         ContactEntry contactEntry = new ContactEntry();
         contactEntry.setContactTypeId(10L);
         contactEntry.setContactData("ContactData");
         emailContactEntries.add(contactEntry);
         extension.setEmailContactEntries(emailContactEntries);
         currentUserContactDetailsPopulator.populateContactDetails(userInfoBean, extension, personality);

         mockedEmailAddressType.verify(() -> EMailAddressType.getEMailAddressType(Mockito.any(ObjectId.class)), Mockito.times(1));
         Mockito.verify(personality, Mockito.times(0)).hasEmailNotificationDelivery();
      }
   }

   @Test
   public void testPopulateEmailAddressMailNoitificationTrue() {
      try (MockedStatic<EMailAddressType> mockedEmailAddressType = Mockito.mockStatic(EMailAddressType.class)) {
         mockedEmailAddressType.when(() -> EMailAddressType.getEMailAddressType(Mockito.any(ObjectId.class))).thenReturn(emailAddressType);

         extension = new EmployeeExtension();
         emailContactEntries = new ArrayList<>();
         ContactEntry contactEntry = new ContactEntry();
         contactEntry.setContactTypeId(10L);
         contactEntry.setContactData("ContactData");
         emailContactEntries.add(contactEntry);
         extension.setEmailContactEntries(emailContactEntries);
         ObjectIdLong notificationProfileId = new ObjectIdLong(10L);
         Mockito.when(personality.getNotificationProfileId()).thenReturn(notificationProfileId);
         currentUserContactDetailsPopulator.populateContactDetails(userInfoBean, extension, personality);

         mockedEmailAddressType.verify(() -> EMailAddressType.getEMailAddressType(Mockito.any(ObjectId.class)), Mockito.times(1));
         Mockito.verify(personality, Mockito.times(1)).hasEmailNotificationDelivery();
      }
   }

   @Test
   public void testPopulateTelephoneNumbers() {
        mockedTelephoneNumberType.when(() -> TelephoneNumberType.getTelephoneNumberType(Mockito.any(ObjectId.class))).thenReturn(telephoneNumberType);
      Collection<ContactEntry> telephoneEntries = new ArrayList<>();
      ContactEntry contactEntry = new ContactEntry();
      telephoneEntries.add(contactEntry);
      contactEntry.setContactTypeId(10L);
      contactEntry.setContactData("ContactData");
      extension = new EmployeeExtension();
      extension.setTelContactEntries(telephoneEntries);
      currentUserContactDetailsPopulator.populateContactDetails(userInfoBean, extension, personality);
        mockedTelephoneNumberType.verify(() -> TelephoneNumberType.getTelephoneNumberType(Mockito.any(ObjectId.class)), Mockito.times(1));
   }

   @Test
   public void testPopulatePostalAddress() {
      try (MockedStatic<PostalAddressType> mockedPostalAddressType = Mockito.mockStatic(PostalAddressType.class)) {
         mockedPostalAddressType.when(() -> PostalAddressType.getPostalAddressType(Mockito.any(ObjectId.class))).thenReturn(postalAddressType);

         Collection<PostalAddressEntry> postalAddress = new ArrayList<>();
         PostalAddressEntry postalEntry = new PostalAddressEntry();
         postalAddress.add(postalEntry);
         postalEntry.setContactTypeId(10L);
         postalEntry.setStreet("UP");

         extension = new EmployeeExtension();
         extension.setPostalAddressEntries(postalAddress);

         currentUserContactDetailsPopulator.populateContactDetails(userInfoBean, extension, personality);

         mockedPostalAddressType.verify(() -> PostalAddressType.getPostalAddressType(Mockito.any(ObjectId.class)), Mockito.times(1));
      }
   }

}
