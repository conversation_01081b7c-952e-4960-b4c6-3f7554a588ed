package com.kronos.persons.rest.assignments.service.brazilcompliance;

import com.kronos.commonapp.authz.impl.fap.profiles.AccessProfile;
import com.kronos.container.api.access.SpringContext;
import com.kronos.container.api.exception.APIException;
import com.kronos.container.api.util.APIExceptionDetailResult;
import com.kronos.people.personality.model.brazilcompliance.BrazilAssignmentDTO;
import com.kronos.people.personality.model.brazilcompliance.BrazilEmployeeDTO;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.service.IBrazilAssignmentService;
import com.kronos.people.personality.util.PersonalityHelper;
import com.kronos.persons.rest.assignments.model.brazilcompliance.*;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.model.EmployeeCriteria;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BRCEmployee;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BrazilAttributeEncryptionUtil;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BrazilAttributeMaskingUtil;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonNotification;
import jakarta.ws.rs.core.Response;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigInteger;
import java.time.LocalDate;
import java.util.*;

import static com.kronos.persons.rest.assignments.service.brazilcompliance.BrazilAssignmentRestServiceImpl.PARSING_ERROR_CODE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class BrazilAssignmentRestServiceImplTest {

    private final static String CPF = "cpf";
    private final static String CPF_2 = "cpf2";
    private final static String ENCRYPTED_CPF = "ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZcpf";
    private final static String ENCRYPTED_CPF_2 = "YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYcpf";
    private final static String ESOCIAL = "eSocial";
    private final static String ESOCIAL_2 = "eSocial2";
    private final static String ENCRYPTED_ESOCIAL = "ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZeSocial";
    private final static String ENCRYPTED_ESOCIAL_2 = "YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYeSocial";
    private final static String PIS = "pis";
    private final static String PIS_2 = "pis2";
    private final static String ENCRYPTED_PIS = "ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZpis";
    private final static String ENCRYPTED_PIS_2 = "YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYpis";
    private final static Long PERSON_ID = 243L;
    private final static Long PERSON_ID_2 = 244L;
    private final static Long personIdNotAccessible = 5555L;
    private final static String personNumberNotAccessible = "aaaaaaa";
    private final static Long COMPANY_ID=11L;
    private final static String COMPANY_QUALIFIER="Company A";
    private static final String IDENTIFIRE = "IDENTIFIRE";
    private static final String LOCATION = "LOCATION";
    private static final String CEI = "CEI";
    private static final String CAEPF = "CAEPF";
    private static final String CNO = "CNO";
    private static String DEVICE_GROUP_1 = "DEVICE_GROUP_1";
    private final static String PERSON_NUMBER = "20335";
    private final static String PERSON_NUMBER_2 = "20336";
    private final LocalDate NOW= LocalDate.now();

    @Mock
    PersonIdentityBeanValidator personIdentityBeanValidator;
    @Mock
    IBrazilAssignmentService brazilAssignmentService;
    @Mock
    BrazilAssignmentBOConverter brazilAssignmentBOConverter;
    @Mock
    PersonalityHelper personalityHelper;

    @Mock
    BrazilAssignmentValidator brazilAssignmentValidator;
    @InjectMocks
    private BrazilAssignmentRestServiceImpl brazilAssignmentRestService;
    @Mock
    BrazilAssignmentUpdateHelper brazilAssignmentUpdateHelper;

    @Mock
    private BrazilAttributeEncryptionUtil encryptionUtil;

    private MockedStatic<PersonNotification> personNotificationMockedStatic;
    private MockedStatic<SpringContext> springContextMockedStatic;
    private MockedStatic<AccessProfile> accessProfileMockedStatic;


    @BeforeEach
    public void setUp() {
        Mockito.when(encryptionUtil.decrypt(ENCRYPTED_CPF)).thenReturn(CPF);
        Mockito.when(encryptionUtil.decrypt(ENCRYPTED_ESOCIAL)).thenReturn(ESOCIAL);
        Mockito.when(encryptionUtil.decrypt(ENCRYPTED_PIS)).thenReturn(PIS);

        personNotificationMockedStatic = Mockito.mockStatic(PersonNotification.class);
        springContextMockedStatic = Mockito.mockStatic(SpringContext.class);
        accessProfileMockedStatic = Mockito.mockStatic(AccessProfile.class);
        Mockito.when(encryptionUtil.decrypt(ENCRYPTED_CPF_2)).thenReturn(CPF_2);
        Mockito.when(encryptionUtil.decrypt(ENCRYPTED_ESOCIAL_2)).thenReturn(ESOCIAL_2);
        Mockito.when(encryptionUtil.decrypt(ENCRYPTED_PIS_2)).thenReturn(PIS_2);
        springContextMockedStatic.when(() -> SpringContext.getBean(BrazilAttributeEncryptionUtil.class)).thenReturn(encryptionUtil);
    }

    @AfterEach
    public void tearDown() {
        personNotificationMockedStatic.close();
        springContextMockedStatic.close();
        accessProfileMockedStatic.close();
    }

    @Test
    public void testRetrieveByWhereCriteria() {
        List<BrazilEmployeeDTO> empDtoList = new ArrayList<>();
        empDtoList.add(mockBrazilEmployeeDTO(PERSON_ID));
        Mockito.when(personalityHelper.getEmployeeExtensionByBulk(Mockito.any())).thenReturn(createEmployeeExtensionList(PERSON_ID, PERSON_NUMBER));
        Mockito.when(brazilAssignmentService.findByPersonIds(anyList())).thenReturn(empDtoList);
        Mockito.when(brazilAssignmentBOConverter.convertDTOToBO(Mockito.any(), Mockito.any())).thenReturn(mockBrazilAssignmentCompanyRest(PERSON_ID, PERSON_NUMBER));

        /** Create where */
        BrazilMultiReadWhere where = new BrazilMultiReadWhere();
        BrazilWhereCriteria whereSingleKey = new BrazilWhereCriteria();
        EmployeeCriteria empSingleKey = new EmployeeCriteria();
        whereSingleKey.setEmployees(empSingleKey);
        where.setWhere(whereSingleKey);

        /**  Case 1: key = Person Id, value = "243"  */
        List<String> employeeIds = Arrays.asList(PERSON_ID.toString());
        empSingleKey.setValues(employeeIds);
        empSingleKey.setKey(PersonIdentityBeanValidator.PERSON_ID);
        // Decryption control point is enabled
        accessProfileMockedStatic.when(() -> AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.TRUE);
        assertFullSuccessMultiResponse(brazilAssignmentRestService.multiRead(where), true);
        // Decryption control point is disabled
        accessProfileMockedStatic.when(() -> AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.FALSE);
        assertFullSuccessMultiResponse(brazilAssignmentRestService.multiRead(where), false);

        /**  Case 2: key = Person Number, value = "20335"  */
        employeeIds = Arrays.asList(PERSON_NUMBER);
        empSingleKey.setValues(employeeIds);
        empSingleKey.setKey(PersonIdentityBeanValidator.PERSON_NUMBER);
        // Decryption control point is enabled
        accessProfileMockedStatic.when(() -> AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.TRUE);
        Mockito.when(brazilAssignmentBOConverter.convertDTOToBO(Mockito.any(), Mockito.any())).thenReturn(mockBrazilAssignmentCompanyRest(PERSON_ID, PERSON_NUMBER));
        assertFullSuccessMultiResponse(brazilAssignmentRestService.multiRead(where), true);
        // Decryption control point is disabled
        accessProfileMockedStatic.when(() -> AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.FALSE);
        assertFullSuccessMultiResponse(brazilAssignmentRestService.multiRead(where), false);
    }

    @Test
    public void testRetrieveSingleEmployeeByPersonId() {
        List<BrazilEmployeeDTO> empDtoList = new ArrayList<>();
        empDtoList.add(mockBrazilEmployeeDTO(PERSON_ID));

        Mockito.when(brazilAssignmentService.findByPersonIds(anyList())).thenReturn(empDtoList);
        Mockito.when(brazilAssignmentBOConverter.convertDTOToBO(Mockito.any(),  eq(Boolean.FALSE) )).thenReturn(mockBrazilAssignmentCompanyRest(PERSON_ID, PERSON_NUMBER));
        Mockito.when(personalityHelper.getEmployeeExtensionByBulk(Mockito.any())).thenReturn(createEmployeeExtensionList(PERSON_ID, PERSON_NUMBER));

        // Decryption control point is enabled
        accessProfileMockedStatic.when(() -> AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.TRUE);
        BrazilEmployeeAssignmentsRest response = brazilAssignmentRestService.retrieveByPersonId(PERSON_ID, false);

        assertEquals(PIS, response.getPis());
        assertEquals(ESOCIAL, response.getEsocial());
        assertEquals(CPF, response.getCpf());
        assertEquals(PERSON_ID, response.getPersonIdentity().getPersonKey());
        assertEquals(PERSON_NUMBER, response.getPersonIdentity().getPersonNumber());

        // Decryption control point is disabled
        accessProfileMockedStatic.when(() -> AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.FALSE);
        response = brazilAssignmentRestService.retrieveByPersonId(PERSON_ID, false);

        assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, response.getPis());
        assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, response.getEsocial());
        assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, response.getCpf());
        assertEquals(PERSON_ID, response.getPersonIdentity().getPersonKey());
        assertEquals(PERSON_NUMBER, response.getPersonIdentity().getPersonNumber());
    }


    /**
     * Employee id does not exist
     */
    @Test
    public void testRetrieveSingleEmployeeByPersonIdInvalidPersonId() {
        APIException thrown = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.retrieveByPersonId(5555L, false), "Expected to throw APIException.");
        assertEquals(ExceptionConstants.PERSON_NOT_FOUND_HTTP_404, thrown.getErrorCode());
    }

    /**
     * Employee id is not accessible
     */
    @Test
    public void testRetrieveSingleEmployeeByPersonIdInaccessiblePersonId() {
        List<EmployeeExtension> extList= createEmployeeExtensionList(personIdNotAccessible, personNumberNotAccessible);
        Mockito.when(personalityHelper.getEmployeeExtensionByBulk(anyList())).thenReturn(extList);
        Mockito.when(personIdentityBeanValidator.validatePersonId(personIdNotAccessible, extList.get(0), null)).thenReturn(new APIException());

        assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.retrieveByPersonId(personIdNotAccessible, false), "Expected to throw APIException");
    }

    @Test
    public void testRetrieveSingleEmployeeByPersonIdOrNumber() {
        /**  Test retrieve by Person id **/
        List<BrazilEmployeeDTO> empDtoList = new ArrayList<>();
        empDtoList.add(mockBrazilEmployeeDTO(PERSON_ID));
        Mockito.when(personalityHelper.getEmployeeExtensionByBulk(Mockito.any())).thenReturn(createEmployeeExtensionList(PERSON_ID, PERSON_NUMBER));
        Mockito.when(brazilAssignmentService.findByPersonIds(anyList())).thenReturn(empDtoList);
        Mockito.when(brazilAssignmentBOConverter.convertDTOToBO(Mockito.any(),  eq(Boolean.FALSE) )).thenReturn(mockBrazilAssignmentCompanyRest(PERSON_ID, PERSON_NUMBER));

        // Decryption control point is enabled
        accessProfileMockedStatic.when(() -> AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.TRUE);
        BrazilEmployeeAssignmentsRest response = brazilAssignmentRestService.retrieveByPersonIdOrNumber(PERSON_ID, null, false);

        assertEquals(PIS, response.getPis());
        assertEquals(ESOCIAL, response.getEsocial());
        assertEquals(CPF, response.getCpf());
        assertEquals(PERSON_ID, response.getPersonIdentity().getPersonKey());
        assertEquals(PERSON_NUMBER, response.getPersonIdentity().getPersonNumber());

        // Decryption control point is disabled
        accessProfileMockedStatic.when(() -> AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.FALSE);
        response = brazilAssignmentRestService.retrieveByPersonIdOrNumber(PERSON_ID, null, false);

        assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, response.getPis());
        assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, response.getEsocial());
        assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, response.getCpf());
        assertEquals(PERSON_ID, response.getPersonIdentity().getPersonKey());
        assertEquals(PERSON_NUMBER, response.getPersonIdentity().getPersonNumber());

        /**  Test retrieve by Person Number **/
        // Decryption control point is enabled
        accessProfileMockedStatic.when(() -> AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.TRUE);
        Mockito.when(brazilAssignmentBOConverter.convertDTOToBO(Mockito.any(),  eq(Boolean.FALSE) )).thenReturn(mockBrazilAssignmentCompanyRest(PERSON_ID, PERSON_NUMBER));
        response = brazilAssignmentRestService.retrieveByPersonIdOrNumber(null, PERSON_NUMBER, false);

        assertEquals(PIS, response.getPis());
        assertEquals(ESOCIAL, response.getEsocial());
        assertEquals(CPF, response.getCpf());
        assertEquals(PERSON_ID, response.getPersonIdentity().getPersonKey());
        assertEquals(PERSON_NUMBER, response.getPersonIdentity().getPersonNumber());

        // Decryption control point is disabled
        accessProfileMockedStatic.when(() -> AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.FALSE);
        response = brazilAssignmentRestService.retrieveByPersonIdOrNumber(null, PERSON_NUMBER, false);

        assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, response.getPis());
        assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, response.getEsocial());
        assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, response.getCpf());
        assertEquals(PERSON_ID, response.getPersonIdentity().getPersonKey());
        assertEquals(PERSON_NUMBER, response.getPersonIdentity().getPersonNumber());
    }


    @Test
    public void testRetrieveSingleEmployeeByPersonIdOrNumberNullParams() {

        APIException thrown = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.retrieveByPersonIdOrNumber(null, null, false), "Expected to throw APIException");
        assertEquals(ExceptionConstants.EXCEPTION_101202, thrown.getErrorCode());

        thrown = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.retrieveByPersonIdOrNumber(null, "", false), "Expected to throw APIException");
        assertEquals(ExceptionConstants.EXCEPTION_101202, thrown.getErrorCode());

        Mockito.when(personalityHelper.getPersonIdsByPersonNumbers(Mockito.any())).thenReturn(new HashMap<>());

        thrown = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.retrieveByPersonIdOrNumber(null, "doesnotexist", false), "Expected to throw APIException");
        assertEquals(ExceptionConstants.PERSON_NOT_FOUND_HTTP_404, thrown.getErrorCode());
    }

    @Test
    public void testCreateJsonParsingException() {
        String key = "personId";
        String values = "aaaaa,bbbbb";
        String type = "Long";
        APIException exception = brazilAssignmentRestService.createJsonParsingException(key, values, type);
        assertEquals(PARSING_ERROR_CODE, exception.getErrorCode());
        Map<String, String> uesrParams = exception.getUserParameters();
        assertEquals(3, uesrParams.size());
        assertEquals(values, uesrParams.get("values"));
        assertEquals(key, uesrParams.get("key"));
        assertEquals(type, uesrParams.get("type"));
    }

    @Test
    public void testProcessResponseForBulkRequest() {
        /** Full success */
        List<APIExceptionDetailResult<?>> resultList = createBulkResponseAllSuccess();
        Response response = brazilAssignmentRestService.processResponseForBulkRequest(resultList, 2);
        assertTrue(response.getEntity() instanceof ArrayList);
        assertEquals(resultList.size(), ((ArrayList) response.getEntity()).size());
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());

        List<APIExceptionDetailResult<?>> partialSuccessResponse = createBulkResponsePartialSuccess();
        /** Partial success */
        APIException exception = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.processResponseForBulkRequest(partialSuccessResponse, 2),
                "Expected to throw APIException");

        assertEquals(ExceptionConstants.PARTIAL_SUCCESS, exception.getErrorCode());

        List<APIExceptionDetailResult<?>> allFailureResponse = createBulkResponseAllFailure();
        /** All records failed */
        exception = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.processResponseForBulkRequest(allFailureResponse, 2),
                "Expected to throw APIException");
        assertEquals(ExceptionConstants.ALL_RECORDS_FAILED, exception.getErrorCode());
    }

    @Test
    public void testValidateMultiReadRequest() {
        BrazilMultiReadWhere where = new BrazilMultiReadWhere();
        BrazilWhereCriteria whereSingleKey = new BrazilWhereCriteria();
        EmployeeCriteria empSingleKey = new EmployeeCriteria();
        whereSingleKey.setEmployees(empSingleKey);
        where.setWhere(whereSingleKey);

        /** No key */
        APIException exception = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.validateMultiReadRequest(where),
                "Expected to throw APIException");
        assertEquals(ExceptionConstants.PROPERTY_IS_NULL, exception.getErrorCode());

        /** Key other than personId and PersonNumber */
        empSingleKey.setKey("WrongKey");
        exception = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.validateMultiReadRequest(where),
                "Expected to throw APIException");
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());

        /** Valid Key but no value */
        empSingleKey.setKey(PersonIdentityBeanValidator.PERSON_ID);
        exception = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.validateMultiReadRequest(where),
                "Expected to throw APIException");
        assertEquals(ExceptionConstants.PROPERTY_IS_NULL, exception.getErrorCode());

        /** Valid Key but non-numeric value for person id */
        empSingleKey.setKey(PersonIdentityBeanValidator.PERSON_ID);

        empSingleKey.setValues(Arrays.asList("111111", "222222", "third", "4444"));
        exception = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.validateMultiReadRequest(where),
                "Expected to throw APIException");
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());

        /** Special chars in person id */
        empSingleKey.setValues(Arrays.asList("111@"));
        exception = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.validateMultiReadRequest(where),
                "Expected to throw APIException");
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());

        /** Negative person id */
        empSingleKey.setValues(Arrays.asList("-9999"));
        exception = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.validateMultiReadRequest(where),
                "Expected to throw APIException");
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());

        /** Person id with decimal */
        empSingleKey.setValues(Arrays.asList("99.99"));
        exception = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.validateMultiReadRequest(where),
                "Expected to throw APIException");
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());

        /** Person id bigger than Long.MAX_VALUE */
        BigInteger bigInt = new BigInteger(Long.MAX_VALUE+"").add(BigInteger.ONE);
        empSingleKey.setValues(Arrays.asList(bigInt.toString()));
        exception = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.validateMultiReadRequest(where),
                "Expected to throw APIException");
        assertEquals(ExceptionConstants.INVALID_PROPERTY_VALUE, exception.getErrorCode());

    }

    @Test
    public void testMultiUpdate() throws Exception {
        EmployeeExtension mockEmpExt = Mockito.mock(EmployeeExtension.class);
        when(mockEmpExt.getPersonNumber()).thenReturn(PERSON_NUMBER);
        when(mockEmpExt.getPersonId()).thenReturn(PERSON_ID);
        when(mockEmpExt.getAccessProfileId()).thenReturn(22L);

        Map<Long, EmployeeExtension> idExtMap = new HashMap<>();
        idExtMap.put(PERSON_ID, mockEmpExt);
        Mockito.when(brazilAssignmentUpdateHelper.getPersonIdAndEmployeeExtensionMap(any())).thenReturn(idExtMap);

        Mockito.when(brazilAssignmentBOConverter.convertEmployeePersistenceObjToBO(Mockito.any(), Mockito.any())).thenReturn(mockBrazilAssignmentCompanyRest(PERSON_ID, PERSON_NUMBER));

        BRCEmployee brcEmployee = new BRCEmployee(22L, PERSON_ID, ENCRYPTED_PIS, ENCRYPTED_ESOCIAL, ENCRYPTED_CPF);
        Mockito.doNothing().when(brazilAssignmentValidator).validateRequestIsNotNull(anyList());
        Mockito.when(brazilAssignmentUpdateHelper.updateEmployee(Mockito.any())).thenReturn(brcEmployee);
        Mockito.doNothing().when(brazilAssignmentUpdateHelper).enrichPersonIdentities(anyList(), anyMap(), anyMap());

        //Mockito.doNothing().when(PersonNotification.class, "sendBulkUpdate", new ArrayList<>(), true);        //Todo

        BrazilEmployeeAssignmentsRest updateRequest =  createBrazilEmployeeAssignmentsRest();

        // Decryption control point is enabled
        accessProfileMockedStatic.when(() -> AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.TRUE);
        Response response = brazilAssignmentRestService.multiUpsert(Arrays.asList(updateRequest));
        this.assertFullSuccessMultiResponse(response, true);

        // Decryption control point is disabled
        accessProfileMockedStatic.when(() -> AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.FALSE);
        response = brazilAssignmentRestService.multiUpsert(Arrays.asList(updateRequest));
        this.assertFullSuccessMultiResponse(response, false);
    }

    @Test
    public void testisValidLong() {
        assertTrue(brazilAssignmentRestService.isValidAndPositiveLong("54645645645"));
        assertTrue(brazilAssignmentRestService.isValidAndPositiveLong(new BigInteger(Long.MAX_VALUE+"").toString())); // Long.MAX_VALUE
        assertTrue(brazilAssignmentRestService.isValidAndPositiveLong("0"));
        assertTrue(brazilAssignmentRestService.isValidAndPositiveLong("1"));
        assertFalse(brazilAssignmentRestService.isValidAndPositiveLong("rwrwerwewe"));
        assertFalse(brazilAssignmentRestService.isValidAndPositiveLong(  new BigInteger(Long.MAX_VALUE+"").add(BigInteger.ONE).toString())); // Greater than Long.MAX_VALUE
        assertFalse(brazilAssignmentRestService.isValidAndPositiveLong("-123456789")); // Negative number
        assertFalse(brazilAssignmentRestService.isValidAndPositiveLong("--1")); // Negative number
        assertFalse(brazilAssignmentRestService.isValidAndPositiveLong("")); // Empty string
        assertFalse(brazilAssignmentRestService.isValidAndPositiveLong(null)); // Null string
    }

    @Test
    public void testDecryptionFullSuccess() {
        List<BrazilEmployeeDTO> empDtoList = new ArrayList<>();
        BrazilEmployeeDTO empDTO1 = mockBrazilEmployeeDTO(PERSON_ID);
        BrazilEmployeeAssignmentsRest empRestMock1 = mockBrazilAssignmentCompanyRest(PERSON_ID, PERSON_NUMBER);

        BrazilEmployeeDTO empDTO2 = mockBrazilEmployeeDTO(PERSON_ID_2);
        BrazilEmployeeAssignmentsRest empRestMock2 = mockBrazilAssignmentCompanyRest(PERSON_ID_2, PERSON_NUMBER_2);

        empDtoList.add(empDTO1);
        empDtoList.add(empDTO2);

        List<EmployeeExtension> empExtList = createEmployeeExtensionList();
        Mockito.when(personalityHelper.getEmployeeExtensionByBulk(Mockito.any())).thenReturn(empExtList);
        Mockito.when(brazilAssignmentService.findByPersonIds(anyList())).thenReturn(empDtoList);
        Mockito.when(brazilAssignmentBOConverter.convertDTOToBO(empDTO1, null)).thenReturn(empRestMock1);
        Mockito.when(brazilAssignmentBOConverter.convertDTOToBO(empDTO2, null)).thenReturn(empRestMock2);

        /** Create where */
        BrazilMultiReadWhere where = new BrazilMultiReadWhere();
        BrazilWhereCriteria whereSingleKey = new BrazilWhereCriteria();
        EmployeeCriteria empSingleKey = new EmployeeCriteria();
        whereSingleKey.setEmployees(empSingleKey);
        where.setWhere(whereSingleKey);

        /** Case : Full success key = Person Id, value = "243"  "244" */
        List<String> employeeIds = Arrays.asList(PERSON_ID.toString(), PERSON_ID_2.toString());
        empSingleKey.setValues(employeeIds);
        empSingleKey.setKey(PersonIdentityBeanValidator.PERSON_ID);
        // Decryption control point is enabled
        Mockito.when(AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.TRUE);
        assertFullSuccessMultiResponseTwoEmployees(brazilAssignmentRestService.multiRead(where), true);
        // Decryption control point is disabled
        Mockito.when(AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.FALSE);
        assertFullSuccessMultiResponseTwoEmployees(brazilAssignmentRestService.multiRead(where), false);
    }

    @Test
    public void testDecryptionPartialSuccess() {
        List<BrazilEmployeeDTO> empDtoList = new ArrayList<>();
        BrazilEmployeeDTO empDTO1 = mockBrazilEmployeeDTO(PERSON_ID);
        BrazilEmployeeAssignmentsRest empRestMock1 = mockBrazilAssignmentCompanyRest(PERSON_ID, PERSON_NUMBER);

        BrazilEmployeeDTO empDTO2 = mockBrazilEmployeeDTO(PERSON_ID_2);
        BrazilEmployeeAssignmentsRest empRestMock2 = mockBrazilAssignmentCompanyRest(PERSON_ID_2, PERSON_NUMBER_2);

        empDtoList.add(empDTO1);
        empDtoList.add(empDTO2);

        List<EmployeeExtension> empExtList = createEmployeeExtensionList();
        Mockito.when(personalityHelper.getEmployeeExtensionByBulk(Mockito.any())).thenReturn(empExtList);
        Mockito.when(brazilAssignmentService.findByPersonIds(anyList())).thenReturn(empDtoList);
        Mockito.when(brazilAssignmentBOConverter.convertDTOToBO(empDTO1, null)).thenReturn(empRestMock1);
        Mockito.when(brazilAssignmentBOConverter.convertDTOToBO(empDTO2, null)).thenReturn(empRestMock2);

        /** Create where */
        BrazilMultiReadWhere where = new BrazilMultiReadWhere();
        BrazilWhereCriteria whereSingleKey = new BrazilWhereCriteria();
        EmployeeCriteria empSingleKey = new EmployeeCriteria();
        whereSingleKey.setEmployees(empSingleKey);
        where.setWhere(whereSingleKey);
        List<String> employeeIds = Arrays.asList(PERSON_ID.toString(), PERSON_ID_2.toString());
        empSingleKey.setValues(employeeIds);
        empSingleKey.setKey(PersonIdentityBeanValidator.PERSON_ID);

        /** Case : Partial success key = Person Id, value = "243"  "244" */
        // Decryption control point is enabled
        Mockito.when(AccessProfile.isPermitted(BrazilAssignmentRestServiceImpl.DECRYPT_PII_CONTROL_POINT)).thenReturn(Boolean.TRUE);
        Mockito.when(encryptionUtil.decrypt(ENCRYPTED_CPF)).thenReturn(CPF);
        Mockito.when(encryptionUtil.decrypt(ENCRYPTED_CPF_2)).thenThrow(new APIException());

        APIException thrown = assertThrows(
                APIException.class,
                () -> brazilAssignmentRestService.multiRead(where), "Expected to throw APIException with Partial success.");
        assertEquals(ExceptionConstants.PARTIAL_SUCCESS, thrown.getErrorCode());
        List resultList = (List) thrown.getResults().get("results");
        assertEquals(2, resultList.size());
        APIExceptionDetailResult<?> result1 = (APIExceptionDetailResult<?>) resultList.get(0);
        assertNotNull(result1.getSuccess());
        BrazilEmployeeAssignmentsRest successEmp1 = (BrazilEmployeeAssignmentsRest) result1.getSuccess();
        assertEquals(PERSON_ID, successEmp1.getPersonIdentity().getPersonKey());
        assertEquals(PIS, successEmp1.getPis());
        assertEquals(ESOCIAL, successEmp1.getEsocial());
        assertEquals(CPF, successEmp1.getCpf());

        APIExceptionDetailResult<?> result2 = (APIExceptionDetailResult<?>) resultList.get(1);
        assertNotNull(result2.getError());
    }

    /**
     * Create Data Objects
     * */
    BrazilEmployeeAssignmentsRest createBrazilEmployeeAssignmentsRest(){
        PersonIdentityBean identity = new PersonIdentityBean();
        identity.setPersonKey(PERSON_ID);
        BrazilEmployeeAssignmentsRest bo = new BrazilEmployeeAssignmentsRest();
        bo.setPersonIdentity(identity);
        bo.setCpf(CPF);
        bo.setEsocial(ESOCIAL);
        bo.setPis(PIS);

        /** Populate Company Data */
        List<BrazilAssignmentCompanyDetailRest> companyAssignmentDetails = new ArrayList<>();
        BrazilAssignmentCompanyDetailRest company = new BrazilAssignmentCompanyDetailRest();
        CompanyAttributesRest companyAttributes = new CompanyAttributesRest();
        companyAttributes.setId(COMPANY_ID);
        companyAttributes.setQualifier(COMPANY_QUALIFIER);
        company.setCompany(companyAttributes);
        company.setEffectiveDate(NOW);
        companyAssignmentDetails.add(company);
        BrazilAssignmentCompanyRest companyAssignments= new BrazilAssignmentCompanyRest();
        companyAssignments.setAssignmentDetails(companyAssignmentDetails);
        bo.setCompanyAssignments(companyAssignments);
        return bo;
    }

    private List<APIExceptionDetailResult<?>> createBulkResponseAllSuccess() {
        List<APIExceptionDetailResult<?>> resultList = new ArrayList<>();
        resultList.add(new APIExceptionDetailResult<>(new BrazilEmployeeAssignmentsRest()));
        resultList.add(new APIExceptionDetailResult<>(new BrazilEmployeeAssignmentsRest()));
        return resultList;
    }

    private List<APIExceptionDetailResult<?>> createBulkResponsePartialSuccess() {
        List<APIExceptionDetailResult<?>> resultList = new ArrayList<>();
        resultList.add(new APIExceptionDetailResult<>(new APIException()));
        resultList.add(new APIExceptionDetailResult<>(new BrazilEmployeeAssignmentsRest()));
        return resultList;
    }

    private List<APIExceptionDetailResult<?>> createBulkResponseAllFailure() {
        List<APIExceptionDetailResult<?>> resultList = new ArrayList<>();
        resultList.add(new APIExceptionDetailResult<>(new APIException()));
        resultList.add(new APIExceptionDetailResult<>(new APIException()));
        return resultList;
    }

    private BrazilEmployeeDTO mockBrazilEmployeeDTO(Long personId) {
        BrazilEmployeeDTO dto = new BrazilEmployeeDTO();
        dto.setEmployeeId(personId);
        dto.setCpf(ENCRYPTED_CPF);
        dto.setPis(ENCRYPTED_PIS);
        dto.seteSocial(ENCRYPTED_ESOCIAL);

        List<BrazilAssignmentDTO> companyAssignments = new ArrayList<>();
        BrazilAssignmentDTO companyDTO = new BrazilAssignmentDTO();
        companyDTO.setAttributeId(COMPANY_ID);
        companyDTO.setEffectiveDate(NOW);
        companyAssignments.add(companyDTO);
        dto.setCompanyAssignments(companyAssignments);
        return dto;
    }

    private BrazilEmployeeAssignmentsRest mockBrazilAssignmentCompanyRest(Long personId, String personNumber) {
        BrazilEmployeeAssignmentsRest rest = new BrazilEmployeeAssignmentsRest();
        PersonIdentityBean identity = new PersonIdentityBean();
        identity.setPersonNumber(personNumber);
        identity.setPersonKey(personId);
        rest.setPersonIdentity(identity);
        if(personId == PERSON_ID){
            rest.setPis(ENCRYPTED_PIS);
            rest.setCpf(ENCRYPTED_CPF);
            rest.setEsocial(ENCRYPTED_ESOCIAL);
        } else {
            rest.setPis(ENCRYPTED_PIS_2);
            rest.setCpf(ENCRYPTED_CPF_2);
            rest.setEsocial(ENCRYPTED_ESOCIAL_2);
        }

        // Company data
        BrazilAssignmentCompanyRest companyRest = new BrazilAssignmentCompanyRest();
        BrazilAssignmentCompanyDetailRest company= new BrazilAssignmentCompanyDetailRest();
        CompanyAttributesRest companyAttributes = new CompanyAttributesRest();
        companyAttributes.setId(COMPANY_ID);
        companyAttributes.setQualifier(COMPANY_QUALIFIER);
        company.setCompany(companyAttributes);
        companyRest.setAssignmentDetails(Arrays.asList(company));
        rest.setCompanyAssignments(companyRest);
        return rest;
    }

    private List<EmployeeExtension> createEmployeeExtensionList(Long personId, String personNumber) {
        List<EmployeeExtension> extList = new ArrayList<>();
        EmployeeExtension ext = new EmployeeExtension();
        ext.setPersonId(personId);
        ext.setPersonNumber(personNumber);
        extList.add(ext);
        return  extList;
    }

    private List<EmployeeExtension> createEmployeeExtensionList() {
        List<EmployeeExtension> extList = new ArrayList<>();
        EmployeeExtension ext = new EmployeeExtension();
        ext.setPersonId(PERSON_ID);
        ext.setPersonNumber(PERSON_NUMBER);
        extList.add(ext);

        ext = new EmployeeExtension();
        ext.setPersonId(PERSON_ID_2);
        ext.setPersonNumber(PERSON_NUMBER_2);
        extList.add(ext);
        return  extList;
    }

    private void assertFullSuccessMultiResponse(Response response, boolean shouldDecrypt) {
        assertEquals(200, response.getStatus());
        assertTrue(response.getEntity() instanceof ArrayList);
        ArrayList responseList = (ArrayList) response.getEntity();
        assertEquals(1, responseList.size());
        assertTrue(responseList.get(0) instanceof BrazilEmployeeAssignmentsRest);
        BrazilEmployeeAssignmentsRest detailResult = (BrazilEmployeeAssignmentsRest)responseList.get(0);
        assertEquals(PERSON_ID, detailResult.getPersonIdentity().getPersonKey());
        assertEquals(PERSON_NUMBER, detailResult.getPersonIdentity().getPersonNumber());
        if(shouldDecrypt) {
            assertEquals(CPF, detailResult.getCpf());
            assertEquals(PIS, detailResult.getPis());
            assertEquals(ESOCIAL, detailResult.getEsocial());
        } else {
            assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, detailResult.getCpf());
            assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, detailResult.getPis());
            assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, detailResult.getEsocial());
        }
    }

    private void assertFullSuccessMultiResponseTwoEmployees(Response response, boolean shouldDecrypt) {
        assertEquals(200, response.getStatus());
        assertTrue(response.getEntity() instanceof ArrayList);
        ArrayList responseList = (ArrayList) response.getEntity();
        assertEquals(2, responseList.size());

        // Assert employee one - 243
        assertTrue(responseList.get(0) instanceof BrazilEmployeeAssignmentsRest);
        BrazilEmployeeAssignmentsRest detailResult = (BrazilEmployeeAssignmentsRest) responseList.get(0);
        assertEquals(PERSON_ID, detailResult.getPersonIdentity().getPersonKey());
        assertEquals(PERSON_NUMBER, detailResult.getPersonIdentity().getPersonNumber());
        if (shouldDecrypt) {
            assertEquals(CPF, detailResult.getCpf());
            assertEquals(PIS, detailResult.getPis());
            assertEquals(ESOCIAL, detailResult.getEsocial());
        } else {
            assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, detailResult.getCpf());
            assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, detailResult.getPis());
            assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, detailResult.getEsocial());
        }

        // Assert employee two - 244
        assertTrue(responseList.get(1) instanceof BrazilEmployeeAssignmentsRest);
        detailResult = (BrazilEmployeeAssignmentsRest) responseList.get(1);
        assertEquals(PERSON_ID_2, detailResult.getPersonIdentity().getPersonKey());
        assertEquals(PERSON_NUMBER_2, detailResult.getPersonIdentity().getPersonNumber());
        if (shouldDecrypt) {
            assertEquals(CPF_2, detailResult.getCpf());
            assertEquals(PIS_2, detailResult.getPis());
            assertEquals(ESOCIAL_2, detailResult.getEsocial());
        } else {
            assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, detailResult.getCpf());
            assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, detailResult.getPis());
            assertEquals(BrazilAttributeMaskingUtil.MASKED_STRING, detailResult.getEsocial());
        }
    }
}