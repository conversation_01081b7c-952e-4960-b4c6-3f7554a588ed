/*******************************************************************************
 * EmployeeMatcherMicroTest.java
 * Copyright © 2024 UKG Inc. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.kronos.wfc.platform.businessobject.framework.BusinessProcessingException;
import com.kronos.wfc.platform.licensing.framework.LicenseType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;

import com.kronos.commonbusiness.converter.api.IRepositoryBasedConverter;
import com.kronos.commonbusiness.converter.model.ObjectId;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.model.Employee;
import com.kronos.persons.rest.model.EmployeeRef;
import com.kronos.persons.rest.model.EmployeeSurrogate;
import com.kronos.wfc.commonapp.currency.business.assignment.EmployeeCurrencyAssignment;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignment;
import com.kronos.wfc.commonapp.people.business.jobassignment.JobAssignmentDetails;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.commonapp.people.business.person.TelephoneNumber;
import com.kronos.wfc.commonapp.people.business.person.TelephoneNumberSet;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.types.business.ContactType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.platform.utility.framework.datetime.KDateSpan;
import com.kronos.wfc.scheduling.core.business.EffectiveDatedList;
import com.kronos.wfc.scheduling.core.business.SchedEmployee;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Class with unit tests for {@link EmployeeMatcher}
 */
@ExtendWith(MockitoExtension.class)
public class EmployeeMatcherMicroTest {

	   private static final String LAST_NAME = "Schrute";
	   private static final String FIRST_NAME = "Dwight";
	   private static final Date SNAPSHOT_DATE_UTC = new Date();
	   private static final ObjectId EMPLOYEE_ID = new ObjectId(1729);
	   private static final EffectiveDatedList EFFECTIVE_DATED_LIST = mock(EffectiveDatedList.class);
	   private static final ObjectIdLong LEGACY_ORG_JOB = mock(ObjectIdLong.class);
	   private static final ObjectId CONVERTED_ID = mock(ObjectId.class);
	   private static final boolean HAS_WTK_LICENSE = true;
	   private static final boolean HAS_WFS_LICENSE = false;
	   private static final String EMPLOYEE_NAME = "Dwight Schrute";
	   private static final String SHORT_NAME = "FALSE";
	   private static final String PERSON_NUM = "2";
	   private static final String FIRST_PHONE_NUM = "**************";
	   private static final String PHOTO_ID = "Photo #1729";
	   private static final String CURRENCY_SYMBOL = "$";
	   private static final String CUBA_CURRENCY_CODE = "CUP";
	   private static final String HELP_MESSAGE_ERROR_CODE = "Error code doesn't match";
	   private static final int INVALID_NULL_PARAMETER_ERROR_CODE = 11;

	   private EmployeeMatcher matcherUnderTest;

	   private SchedEmployee legacyEmp;

	   private final KDate snapshot = new KDate(1900, 4, 20);

	   @BeforeEach
	   public void setUp() {
	      matcherUnderTest = spy(new EmployeeMatcher(mock(IRepositoryBasedConverter.class)){
	         @Override
	         protected Person getPersonById(ObjectIdLong id) {
	            Person result = new Person();
	            result.setFirstName(""+id);
	            result.setLastName(""+id);
	            return result;
	         }
	      });
	      matcherUnderTest.setSnapshotDate(snapshot);
	      legacyEmp = mock(SchedEmployee.class);
	   }

	   @Test
	   public void test_convertLegacyBOToDTO_nullBOThrowsException() throws Exception {
		   assertThrows(APIException.class, () -> matcherUnderTest.convertLegacyBOToDTO(null, new Employee()));
	   }

	   @Test
	   public void test_convertLegacyBOToDTO_BOIsTheWrongClassThrowsException() throws Exception {
		   assertThrows(ClassCastException.class, () -> matcherUnderTest.convertLegacyBOToDTO(new Object(), new Employee()));
	   }

	   @Test
	   public void test_convertLegacyBOToDTO_preservesFieldsFromBO() throws Exception {
	      stubForConvertingBOToDTO();
	      stubMatcherToReturnPersonWithFirstnameAndLastname(FIRST_NAME, LAST_NAME);
	      Object dto = matcherUnderTest.convertLegacyBOToDTO(legacyEmp, new Employee());

	      assertEquals(Employee.class, dto.getClass());
	      assertThatDTOHasConvertedFields((Employee) dto);
	   }

	   @Test
	   public void test_convertDTOToDTOSurrogate_nullDTO() throws Exception {
		   assertThrows(NullPointerException.class, () -> matcherUnderTest.convertDTOToDTOSurrogate(null, new EmployeeSurrogate()));
	   }

	   @Test
	   public void test_convertDTOToDTOSurrogate_wrongClass() throws Exception  {
		   assertThrows(ClassCastException.class, () -> matcherUnderTest.convertDTOToDTOSurrogate(new KDate(), new EmployeeSurrogate()));
	   }
	   
	   @Test
	   public void testConvertDTOToDTORef() throws Exception{
		   EmployeeMatcher matcher = new EmployeeMatcher(new EmployeeServiceConverter());
		   Employee src = new Employee();
		   ObjectId id = new ObjectId(12L);
		   src.setId(id);
		   src.setPersonNum("231123");
		   EmployeeRef dest = new EmployeeRef();
		   dest = (EmployeeRef) matcher.convertDTOToDTORef(src, dest);
		   assertEquals((Long)12L, dest.getInternalId());
		   assertEquals("231123", dest.getPersonId());
	   }

	   @Test
	   public void test_convertDTOToDTOSurrogate_copiesCertainFieldsFromDTO() throws Exception  {
	      Employee emp = createSampleEmployee();
	      doReturn(PHOTO_ID).when(matcherUnderTest).getPhotoIdFromEmployee(emp);

	      Object sur = matcherUnderTest.convertDTOToDTOSurrogate(emp, new EmployeeSurrogate());

	      assertEquals(EmployeeSurrogate.class, sur.getClass());
	      assertThatSurrogateHasTheSameFieldsAsDTO((EmployeeSurrogate) sur);
	   }
	   
	@Test
	public void testIdentifyDTORefAsId() throws Exception {
		ObjectIdLong legacyId = new ObjectIdLong(11L);
		EmployeeMatcher matcher = new EmployeeMatcher(new EmployeeServiceConverter()) {
			protected ObjectIdLong findEmployeeIdByPersonNumber(String personNumber) {
				return legacyId;
			}
		};
		EmployeeRef ref = new EmployeeRef();
		ref.setInternalId(1L);
		ref.setPersonId("2341233");
		ObjectId id = new ObjectId(1L);
		assertEquals(1L, matcher.identifyDTORefAsId(ref, id).getId());
		EmployeeRef ref2 = new EmployeeRef();
		ref2.setPersonId("2341234");
		assertEquals(11L, matcher.identifyDTORefAsId(ref2, id).getId());
		EmployeeRef ref3 = new EmployeeRef();
		assertNull(matcher.identifyDTORefAsId(ref3, id));
		EmployeeRef ref4 = new EmployeeRef();
		ref4.setPersonId("");
		assertNull(matcher.identifyDTORefAsId(ref4, id));
				
		EmployeeMatcher matcher2 = new EmployeeMatcher(new EmployeeServiceConverter()) {
			protected ObjectIdLong findEmployeeIdByPersonNumber(String personNumber) {
				return null;
			}
		};
		EmployeeRef ref5 = new EmployeeRef();
		ref5.setPersonId("2341254");
		assertEquals(2341254L, matcher2.identifyDTORefAsId(ref5, id).getId());
		EmployeeRef ref6 = new EmployeeRef();
		ref6.setInternalId(1L);
		ref6.setPersonId("2341254");
		assertEquals(1L, matcher2.identifyDTORefAsId(ref6, id).getId());
		EmployeeRef ref7 = new EmployeeRef();
		ref7.setPersonId("23eewwq");
		assertNull(matcher2.identifyDTORefAsId(ref7, id));
	}
	
	@Test
	public void testFindEmployeeIdByPersonNumber(){
		Personality personality = Mockito.mock(Personality.class); 
		JobAssignment jobAssignmnt = Mockito.mock(JobAssignment.class);
		JobAssignmentDetails jobAssDetails = Mockito.mock(JobAssignmentDetails.class);
		
		Mockito.when(jobAssDetails.getEmployeeId()).thenReturn(new ObjectIdLong(2L));
		Mockito.when(jobAssignmnt.getJobAssignmentDetails()).thenReturn(jobAssDetails);
		Mockito.when(personality.getJobAssignment()).thenReturn(jobAssignmnt);
		
		EmployeeMatcher matcher = new EmployeeMatcher(new EmployeeServiceConverter()) {
			protected Personality getPersonality(String personNumber) {
				return personality;
			}
		};
		assertEquals(2L, matcher.findEmployeeIdByPersonNumber("2341223").longValue());
		
		Mockito.when(personality.getJobAssignment()).thenReturn(null);
		assertNull(matcher.findEmployeeIdByPersonNumber("2341223"));
		
		Mockito.when(jobAssignmnt.getJobAssignmentDetails()).thenReturn(null);
		Mockito.when(personality.getJobAssignment()).thenReturn(jobAssignmnt);
		assertNull(matcher.findEmployeeIdByPersonNumber("2341223"));
		
		EmployeeMatcher matcher2 = new EmployeeMatcher(new EmployeeServiceConverter()) {
			protected Personality getPersonality(String personNumber) {
				return null;
			}
		};
		assertNull(matcher2.findEmployeeIdByPersonNumber("2341223"));
	}
	   
	@Test
	public void testIdentifyLegacyBOAsLegacyId() throws Exception{
		ObjectIdLong id = new ObjectIdLong(12L);
		SchedEmployee src = Mockito.mock(SchedEmployee.class);
		Mockito.when(src.getEmployeeId()).thenReturn(id);
		assertEquals(12L, matcherUnderTest.identifyLegacyBOAsLegacyId(src, id).longValue());
	}
	
	@Test
	public void testResolveLegacyIdToLegacyBO() throws Exception{
		ObjectIdLong id = new ObjectIdLong(12L);
		SchedEmployee sched = Mockito.mock(SchedEmployee.class);
		SchedEmployee sched2 = Mockito.mock(SchedEmployee.class);
		EmployeeMatcher matcher = new EmployeeMatcher(new EmployeeServiceConverter()) {
			protected SchedEmployee getSchedEmployee(ObjectIdLong id) {
				return null;
			}
			
			protected Map getMapFromSchedEmployeeService(ObjectIdLong id, KDateSpan range) {
				Map<ObjectIdLong, Object> map = new HashMap<>();
				map.put(id, sched);
				return map;
			}
		};
		assertEquals(sched, matcher.resolveLegacyIdToLegacyBO(id, new ObjectId()));
		
		EmployeeMatcher matcher2 = new EmployeeMatcher(new EmployeeServiceConverter()) {
			protected SchedEmployee getSchedEmployee(ObjectIdLong id) {
				return sched;
			}
		};
		assertEquals(sched, matcher2.resolveLegacyIdToLegacyBO(id, new ObjectId()));
		
		EmployeeMatcher matcher3 = new EmployeeMatcher(new EmployeeServiceConverter()) {
			protected SchedEmployee getSchedEmployee(ObjectIdLong id) {
				return null;
			}
			
			protected Map getMapFromSchedEmployeeService(ObjectIdLong id, KDateSpan range) {
				Map<ObjectIdLong, Object> map = new HashMap<>();
				return map;
			}
			
			protected SchedEmployee createSchedEmployeeFromId(ObjectIdLong id) {
				return sched2;
			}
		};
		assertEquals(sched2, matcher3.resolveLegacyIdToLegacyBO(id, new ObjectId()));
		
		EmployeeMatcher matcher4 = new EmployeeMatcher(new EmployeeServiceConverter()) {
			protected SchedEmployee getSchedEmployee(ObjectIdLong id) {
				return null;
			}
			
			protected Map getMapFromSchedEmployeeService(ObjectIdLong id, KDateSpan range) {
				Map<ObjectIdLong, Object> map = new HashMap<>();
				map.put(new ObjectIdLong(15L), sched);
				return map;
			}
			
			protected SchedEmployee createSchedEmployeeFromId(ObjectIdLong id) {
				return sched2;
			}
		};
		assertEquals(sched2, matcher4.resolveLegacyIdToLegacyBO(id, new ObjectId()));
		
		EmployeeMatcher matcher5 = new EmployeeMatcher(new EmployeeServiceConverter()) {
			protected SchedEmployee getSchedEmployee(ObjectIdLong id) {
				return null;
			}
			
			protected Map getMapFromSchedEmployeeService(ObjectIdLong id, KDateSpan range) {
				return null;
			}
			
			protected SchedEmployee createSchedEmployeeFromId(ObjectIdLong id) {
				return sched2;
			}
		};
		assertEquals(sched2, matcher5.resolveLegacyIdToLegacyBO(id, new ObjectId()));
	}
	
	@Test
	public void testSettersGetters(){
		KDate snapshotDate2 = new KDate();
		matcherUnderTest.setSnapshotDate(snapshotDate2);
		KDateSpan snapshotSpan2 = new KDateSpan();
		assertEquals(snapshotDate2, matcherUnderTest.getSnapshotDate());
	}

	@Test
	public void testSetSnapshotDateUTCInEmployeeException(){
		Employee employee = Mockito.mock(Employee.class);
		Mockito.doThrow(new RuntimeException()).when(employee).setSnapshotDateUTC(any());
		assertThrows(APIException.class, () -> matcherUnderTest.setSnapshotDateUTCInEmployee(employee));
	}
	
	@Test
	public void testGetPhoneNumber(){
		SchedEmployee sched = Mockito.mock(SchedEmployee.class);
		ObjectIdLong phoneId = new ObjectIdLong(1L);
		Personality person = Mockito.mock(Personality.class);
		TelephoneNumberSet phones = Mockito.mock(TelephoneNumberSet.class);
		TelephoneNumber phone = Mockito.mock(TelephoneNumber.class);
		Mockito.when(phone.getPhoneNumber()).thenReturn("8092345124");
		Mockito.when(phones.getTelephoneNumber(phoneId)).thenReturn(phone);
		Mockito.when(person.getTelephoneNumbers()).thenReturn(phones);
		Mockito.when(sched.getPersonality()).thenReturn(person);
		assertEquals("8092345124", matcherUnderTest.getPhoneNumber(sched, phoneId));
		
		Mockito.when(phones.getTelephoneNumber(phoneId)).thenReturn(null);
		assertEquals("", matcherUnderTest.getPhoneNumber(sched, phoneId));
		
		Mockito.when(person.getTelephoneNumbers()).thenReturn(null);
		assertEquals("", matcherUnderTest.getPhoneNumber(sched, phoneId));
	}
	
	@Test
	public void testValidateSurrogateSolvingConstraints(){
		EmployeeSurrogate sur = Mockito.mock(EmployeeSurrogate.class);
		Mockito.when(sur.getEmployeeId()).thenReturn(new ObjectId());
		try{
			matcherUnderTest.validateSurrogateSolvingConstraints(sur);
		} catch(Exception e){
			fail();
		}
	}
	
	@Test
	public void testValidateSurrogateSolvingConstraintsException(){
		EmployeeSurrogate sur = Mockito.mock(EmployeeSurrogate.class);
		Mockito.when(sur.getEmployeeId()).thenReturn(null);
		Mockito.when(sur.getPersonNumber()).thenReturn(null);
		assertThrows(APIException.class, () -> matcherUnderTest.validateSurrogateSolvingConstraints(sur));
	}
	
	@Test
	public void testValidateSurrogateSolvingConstraintsException2(){
		EmployeeSurrogate sur = Mockito.mock(EmployeeSurrogate.class);
		Mockito.when(sur.getEmployeeId()).thenReturn(null);
		Mockito.when(sur.getPersonNumber()).thenReturn("");
		assertThrows(APIException.class, () -> matcherUnderTest.validateSurrogateSolvingConstraints(sur));
	}
	
	@Test
	public void testValidateDTOSolvingConstraints(){
		Employee emp = Mockito.mock(Employee.class);
		Mockito.when(emp.getId()).thenReturn(new ObjectId());
		try{
			matcherUnderTest.validateDTOSolvingConstraints(emp);
		} catch(Exception e){
			fail();
		}
	}
	
	@Test
	public void testValidateDTOSolvingConstraintsException() {
		Employee emp = Mockito.mock(Employee.class);
		Mockito.when(emp.getId()).thenReturn(null);
		assertThrows(APIException.class, () -> matcherUnderTest.validateDTOSolvingConstraints(emp));
	}
	
	@Test
	public void testIdentifyDTOAsId() throws Exception {
		EmployeeMatcher matcher = new EmployeeMatcher(new EmployeeServiceConverter());
		Employee employee = new Employee();
		ObjectId id = new ObjectId(12L);
		employee.setId(id);
		ObjectId objectIdResponse = matcher.identifyDTOAsId(employee, id);
		assertEquals(12L, objectIdResponse.getId());
	}
	
	@Test
	public void testIdentifyDTOSurrogateAsId() throws Exception{
		ObjectIdLong objId = new ObjectIdLong(14L);
		EmployeeMatcher matcher = new EmployeeMatcher(new EmployeeServiceConverter()){
			protected ObjectIdLong findEmployeeIdByPersonNumber(String personNumber) {
				return objId;
			}
		};
		EmployeeSurrogate sur = new EmployeeSurrogate();
		ObjectId id = new ObjectId(12L);
		sur.setEmployeeId(id);
		sur.setPersonNumber("2341242");
		assertEquals(id, matcher.identifyDTOSurrogateAsId(sur, id));
		
		EmployeeSurrogate sur2 = new EmployeeSurrogate();
		sur2.setPersonNumber("2341242");
		assertEquals(14L, matcher.identifyDTOSurrogateAsId(sur2, id).getId());
		
		EmployeeSurrogate sur3 = new EmployeeSurrogate();
		sur3.setPersonNumber("");
		assertNull(matcher.identifyDTOSurrogateAsId(sur3, id));

		EmployeeSurrogate sur4 = new EmployeeSurrogate();
		assertNull(matcher.identifyDTOSurrogateAsId(sur4, id));
	}
	
	@Test
	public void getOrgNodeSurrogateFromBO() throws Exception{
		EmployeeMatcher matcher = new EmployeeMatcher(new EmployeeServiceConverter());
		SchedEmployee sched = Mockito.mock(SchedEmployee.class);
		ObjectIdLong id = new ObjectIdLong(15L);
		Mockito.when(sched.getEmployeeId()).thenReturn(id);
		assertEquals(15L, matcher.getIdFromBO(sched).getId());
		assertEquals(15L, matcher.convert(sched, ObjectId.class).getId());
	}

	@Test
	public void testGivenNullId_WhenGetPersonById_ThenShouldThrowException(){
		IRepositoryBasedConverter iRepositoryBasedConverterMock = mock(IRepositoryBasedConverter.class);
		EmployeeMatcher employeeMatcher = new EmployeeMatcher(iRepositoryBasedConverterMock);

		BusinessProcessingException resultException = assertThrows(BusinessProcessingException.class,
				() -> employeeMatcher.getPersonById(null));
		assertEquals(INVALID_NULL_PARAMETER_ERROR_CODE, resultException.getErrorCode());
	}

	@Test
	public void testGivenNullEmployeeId_WhenGetCurrencySymbol_ThenShouldThrowException(){
		LicenseType licenseTypeMock = Mockito.mock(LicenseType.class);
		SchedEmployee schedEmployee = new SchedEmployee(licenseTypeMock, null);

		BusinessProcessingException resultException = assertThrows(BusinessProcessingException.class,
				() -> matcherUnderTest.getCurrencySymbol(schedEmployee));
		assertEquals(INVALID_NULL_PARAMETER_ERROR_CODE, resultException.getErrorCode());
	}

	@Test
	public void testGivenSchedEmployeeHasCurrencyAssigment_WhenCurrencyAssingmentHasCurrencyCode_ThenShouldReturnCurrencyCode(){
		Personality personalityMock = Mockito.mock(Personality.class);
		EmployeeCurrencyAssignment employeeCurrencyAssignmentMock = mock(EmployeeCurrencyAssignment.class);
		Mockito.when(employeeCurrencyAssignmentMock.getCurrencyCode()).thenReturn(CUBA_CURRENCY_CODE);
		Mockito.when(personalityMock.getEmpCurrencyAssign()).thenReturn(employeeCurrencyAssignmentMock);
		Mockito.when(legacyEmp.getPersonality()).thenReturn(personalityMock);

		String result = matcherUnderTest.getCurrencyCode(legacyEmp);

		assertEquals("CUP", CUBA_CURRENCY_CODE, result);
	}

	@Test
	public void testGivenSchedEmployeeWithNullPersonId_WhenGetPhoneNumber_ThenShouldThrowException(){
		LicenseType licenseTypeMock = Mockito.mock(LicenseType.class);
		SchedEmployee schedEmployee = new SchedEmployee(licenseTypeMock, null);

		BusinessProcessingException resultException = assertThrows(BusinessProcessingException.class,
				() -> matcherUnderTest.getPhoneNumber(schedEmployee, null));
		assertEquals(INVALID_NULL_PARAMETER_ERROR_CODE, resultException.getErrorCode());
	}
	
	   private void stubMatcherToReturnPersonWithFirstnameAndLastname(String firstname, String lastname) {
	      Person person = mock(Person.class);
	   }

	   private void stubForConvertingBOToDTO() throws Exception {
	      stubBO();
	      stubMatcher();
	   }

	   private void stubBO() {
	      when(legacyEmp.getEmployeeName()).thenReturn(EMPLOYEE_NAME);
	      when(legacyEmp.getEmployeeShortName()).thenReturn(SHORT_NAME);
	      when(legacyEmp.getPersonNumber()).thenReturn(PERSON_NUM);
	      when(legacyEmp.hasWFSLicense()).thenReturn(HAS_WFS_LICENSE);
	      when(legacyEmp.hasWTKLicense()).thenReturn(HAS_WTK_LICENSE);
	   }

	   private void stubMatcher() throws Exception {
	      doReturn(CONVERTED_ID).when(matcherUnderTest).getIdFromBO(legacyEmp);
	      doReturn(FIRST_PHONE_NUM).when(matcherUnderTest).getPhoneNumber(legacyEmp, ContactType.PHONE_1);
	      doReturn(CURRENCY_SYMBOL).when(matcherUnderTest).getCurrencySymbol(legacyEmp);
	      doReturn(PHOTO_ID).when(matcherUnderTest).getPhotoIdFromBO(CONVERTED_ID);
	   }

	   private void assertThatDTOHasConvertedFields(Employee dtoEmp) {
	      assertSame(CONVERTED_ID, dtoEmp.getId());
	      assertSame(EMPLOYEE_NAME, dtoEmp.getFullName());
	      assertSame(SHORT_NAME, dtoEmp.getShortName());
	      assertSame(PERSON_NUM, dtoEmp.getPersonNum());
	      assertSame(FIRST_PHONE_NUM, dtoEmp.getPhoneNumberOne());
	      assertSame(CURRENCY_SYMBOL, dtoEmp.getCurrencySymbol());
	      assertSame(HAS_WFS_LICENSE, dtoEmp.getIsWFSLicensed());
	      assertSame(HAS_WTK_LICENSE, dtoEmp.getIsWTKLicensed());
	      assertSame(PHOTO_ID, dtoEmp.getPhotoId());
	   }

	   private Employee createSampleEmployee() {
	      Employee emp = new Employee();
	      emp.setId(EMPLOYEE_ID);
	      emp.setSnapshotDateUTC(SNAPSHOT_DATE_UTC);
	      emp.setPersonNum(PERSON_NUM);
	      emp.setFullName(EMPLOYEE_NAME);
	      emp.setShortName(SHORT_NAME);
	      emp.setCurrencySymbol(CURRENCY_SYMBOL);
	      emp.setIsWFSLicensed(HAS_WFS_LICENSE);
	      return emp;
	   }

	   private void assertThatSurrogateHasTheSameFieldsAsDTO(EmployeeSurrogate surEmp) {
	      assertSame(EMPLOYEE_ID, surEmp.getEmployeeId());
	      assertSame(SNAPSHOT_DATE_UTC, surEmp.getSnapshotDateUTC());
	      assertSame(PERSON_NUM, surEmp.getPersonNumber());
	      assertSame(EMPLOYEE_NAME, surEmp.getFullName());
	      assertSame(SHORT_NAME, surEmp.getShortName());
	      assertSame(CURRENCY_SYMBOL, surEmp.getCurrencySymbol());
	      assertSame(HAS_WFS_LICENSE, surEmp.getWfsLicensed());
	   }

}
