/*******************************************************************************
 * PersonLeaveAdminAssignmentServiceTest.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.rest.assignments.service;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.AdminAssignmentRequestBean;
import com.kronos.persons.rest.assignments.model.AdminProfileBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.wfc.absencemgmt.leave.business.people.PersonLeaveAdministratorAssignment;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.personality.PersonalityTriplet;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Verifies {@link PersonLeaveAdminAssignmentService}.
 * Copyright (C) 2019 Kronos.com
 * Date: Jun 25, 2019
 *
 * <AUTHOR> Sharma
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonLeaveAdminAssignmentServiceTest {

    @InjectMocks
    PersonLeaveAdminAssignmentService personLeaveAdminAssignmentService;
    @Mock
    private LicenseValidator licenseValidator;

    @Mock
    ValidatorUtils validatorUtils;

    @Mock
    PersonIdentityBeanValidator personIdentityBeanValidator;

    private final String personNumber = "personNumber";

    private final Long personId = 1L;

    private MockedStatic<PersonLeaveAdministratorAssignment> personLeaveAdministratorAssignmentMockedStatic;
    private MockedStatic<Person> personMockedStatic;
    private MockedStatic<Personality> personalityMockedStatic;

    @BeforeEach
    public void setUp() {
        personLeaveAdministratorAssignmentMockedStatic = Mockito.mockStatic(PersonLeaveAdministratorAssignment.class);
        personMockedStatic = Mockito.mockStatic(Person.class);
        personalityMockedStatic = Mockito.mockStatic(Personality.class);
    }

    @AfterEach
    public void tearDown() {
        personLeaveAdministratorAssignmentMockedStatic.close();
        personMockedStatic.close();
        personalityMockedStatic.close();
        Mockito.clearAllCaches();
    }

    @Test
    public void testRetrieveAdminAssignmentRequestByPersonId() {
        PersonIdentityBean personIdentityBean = preparePersonIdentityBean();
        Personality personality = mock(Personality.class);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(personId));
        Mockito.when(personality.getPersonNumber()).thenReturn(personNumber);

        Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(Mockito.any(), Mockito.any(PersonIdentityBean.class), Mockito.any());
        Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any(PersonIdentityBean.class))).thenReturn(personality);
        Mockito.when(personIdentityBeanValidator.createForPersonNumber(Mockito.anyString())).thenReturn(personIdentityBean);
        Mockito.doNothing().when(licenseValidator).validateLeaveLicense(Mockito.any(), Mockito.anyBoolean());
        PersonLeaveAdministratorAssignment personLeaveAdministratorAssignment = Mockito.mock(PersonLeaveAdministratorAssignment.class);
        Mockito.when(personLeaveAdministratorAssignment.getLeaveAdminId()).thenReturn(new ObjectIdLong(personId));
        Mockito.when(PersonLeaveAdministratorAssignment.retrieveLeaveAdminByPersonId(personality.getPersonId())).thenReturn(personLeaveAdministratorAssignment);
        Mockito.when(validatorUtils.notNull(Mockito.any())).thenReturn(true);
        Mockito.when(validatorUtils.isNull(Mockito.any(PersonLeaveAdministratorAssignment.class))).thenReturn(false);
        Mockito.when(validatorUtils.isValidString(Mockito.anyString())).thenReturn(true);
        Person person = Mockito.mock(Person.class);
        Mockito.when(person.getPersonNumber()).thenReturn(personNumber);
        Mockito.when(person.getFullName()).thenReturn("Full Name");

        Mockito.when(Person.getByPersonId(new ObjectIdLong(personId))).thenReturn(person);
        AdminAssignmentRequestBean adminAssignmentRequestBean = personLeaveAdminAssignmentService.retrieve(personIdentityBean);
        assertEquals(adminAssignmentRequestBean.getPersonIdentity().getPersonNumber(), personIdentityBean.getPersonNumber());
    }

    @Test
    public void testRetrieveAdminAssignmentRequestWhenNullPersonLeaveAdminAssignment() {
        assertThrows(APIException.class, () -> {
        PersonIdentityBean personIdentityBean = preparePersonIdentityBean();
        Personality personality = mock(Personality.class);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(personId));
        Mockito.when(personality.getPersonNumber()).thenReturn(personNumber);

        Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(Mockito.any(), Mockito.any(PersonIdentityBean.class), Mockito.any());
        Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any(PersonIdentityBean.class))).thenReturn(personality);
        Mockito.when(personIdentityBeanValidator.createForPersonNumber(Mockito.anyString())).thenReturn(personIdentityBean);
        Mockito.doNothing().when(licenseValidator).validateLeaveLicense(Mockito.any(), Mockito.anyBoolean());
        PersonLeaveAdministratorAssignment personLeaveAdministratorAssignment = Mockito.mock(PersonLeaveAdministratorAssignment.class);
        Mockito.when(personLeaveAdministratorAssignment.getLeaveAdminId()).thenReturn(new ObjectIdLong(personId));

        Mockito.when(PersonLeaveAdministratorAssignment.retrieveLeaveAdminByPersonId(personality.getPersonId())).thenReturn(personLeaveAdministratorAssignment);
        Mockito.when(validatorUtils.notNull(Mockito.any())).thenReturn(true);
        Mockito.when(validatorUtils.isNull(Mockito.any(PersonLeaveAdministratorAssignment.class))).thenReturn(true);
        Mockito.when(validatorUtils.isValidString(Mockito.anyString())).thenReturn(true);
        Person person = Mockito.mock(Person.class);
        Mockito.when(person.getPersonNumber()).thenReturn(personNumber);
        Mockito.when(person.getPersonNumber()).thenReturn(personNumber);
        Mockito.when(person.getFullName()).thenReturn(Mockito.anyString());

        Mockito.when(Person.getByPersonId(new ObjectIdLong(personId))).thenReturn(new Person());
        personLeaveAdminAssignmentService.retrieve(personIdentityBean);
        });
    }

    @Test
    public void testUpdateAdminAssignmentRequestBeanWhenPersonLeaveAdministratorAssignmentRetrieveLeaveAdminByPersonId() {
        AdminAssignmentRequestBean adminAssignmentRequestBean = preparePersonAdminAssignmentRequestBean(personId);
        adminAssignmentRequestBean.setAdministrator(prepareAdminProfileBean());
        Personality personality = mock(Personality.class);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(personId));
        Mockito.when(personality.getPersonNumber()).thenReturn(personNumber);

        Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(Mockito.any(), Mockito.any(PersonIdentityBean.class), Mockito.any());
        Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any(PersonIdentityBean.class))).thenReturn(personality);
        Mockito.doNothing().when(licenseValidator).validateLeaveLicense(Mockito.any(), Mockito.anyBoolean());
        PersonLeaveAdministratorAssignment personLeaveAdministratorAssignment = Mockito.mock(PersonLeaveAdministratorAssignment.class);
        Mockito.when(personLeaveAdministratorAssignment.getLeaveAdminId()).thenReturn(new ObjectIdLong(personId));
        Mockito.when(PersonLeaveAdministratorAssignment.retrieveLeaveAdminByPersonId(personality.getPersonId())).thenReturn(personLeaveAdministratorAssignment);
        Personality personalityResult = personLeaveAdminAssignmentService.updateRequest(adminAssignmentRequestBean);
        assertEquals(personalityResult.getPersonId(), personality.getPersonId());
    }

    @Test
    public void testUpdateAdminAssignmentRequestBeanWhenEmptyAdministrator() {
        assertThrows(APIException.class, () -> {
            AdminAssignmentRequestBean adminAssignmentRequestBean = preparePersonAdminAssignmentRequestBean(personId);
            Personality personality = mock(Personality.class);
            Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(personId));
            Mockito.when(personality.getPersonNumber()).thenReturn(personNumber);

            Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(Mockito.any(), Mockito.any(PersonIdentityBean.class), Mockito.any());
            Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any(PersonIdentityBean.class))).thenReturn(personality);
            Mockito.doNothing().when(licenseValidator).validateLeaveLicense(Mockito.any(), Mockito.anyBoolean());
            PersonLeaveAdministratorAssignment personLeaveAdministratorAssignment = Mockito.mock(PersonLeaveAdministratorAssignment.class);
            Mockito.when(personLeaveAdministratorAssignment.getLeaveAdminId()).thenReturn(new ObjectIdLong(personId));
            Mockito.when(PersonLeaveAdministratorAssignment.retrieveLeaveAdminByPersonId(personality.getPersonId())).thenReturn(personLeaveAdministratorAssignment);
            personLeaveAdminAssignmentService.updateRequest(adminAssignmentRequestBean);
        });
    }

    @Test
    public void testUpdateAdminAssignmentRequestBeanWhenPersonLeaveAdministratorAssignmentRetrieveLeaveAdminByPersonIdReturnNullAndThrowException() {
        assertThrows(APIException.class, () -> {
            AdminAssignmentRequestBean adminAssignmentRequestBean = preparePersonAdminAssignmentRequestBean(personId);
            adminAssignmentRequestBean.setAdministrator(prepareAdminProfileBean());
            Personality personality = mock(Personality.class);
            Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(personId));
            Mockito.when(personality.getPersonNumber()).thenReturn(personNumber);

            Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(Mockito.any(), Mockito.any(PersonIdentityBean.class), Mockito.any());
            Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any(PersonIdentityBean.class))).thenReturn(personality);
            Mockito.doNothing().when(licenseValidator).validateLeaveLicense(Mockito.any(), Mockito.anyBoolean());
            Mockito.when(PersonLeaveAdministratorAssignment.retrieveLeaveAdminByPersonId(personality.getPersonId())).thenReturn(null);
            personLeaveAdminAssignmentService.updateRequest(adminAssignmentRequestBean);
        });
    }

    @Test
    public void testDeleteAdminAssignmentRequestBeanWhenPersonLeaveAdminAssignmentServiceDeleteRequest() {
        AdminAssignmentRequestBean adminAssignmentRequestBean = preparePersonAdminAssignmentRequestBean(personId);
        adminAssignmentRequestBean.setAdministrator(prepareAdminProfileBean());
        Personality personality = mock(Personality.class);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(personId));
        Mockito.when(personality.getPersonNumber()).thenReturn(personNumber);

        Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(Mockito.any(), Mockito.any(PersonIdentityBean.class), Mockito.any());
        Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any(PersonIdentityBean.class))).thenReturn(personality);
        Mockito.doNothing().when(licenseValidator).validateLeaveLicense(Mockito.any(), Mockito.anyBoolean());
        PersonLeaveAdministratorAssignment personLeaveAdministratorAssignment = Mockito.mock(PersonLeaveAdministratorAssignment.class);
        Mockito.when(personLeaveAdministratorAssignment.getLeaveAdminId()).thenReturn(new ObjectIdLong(personId));
        Mockito.when(PersonLeaveAdministratorAssignment.retrieveLeaveAdminByPersonId(personality.getPersonId())).thenReturn(personLeaveAdministratorAssignment);
        Personality personalityResult = personLeaveAdminAssignmentService.deleteRequest(adminAssignmentRequestBean);
        assertEquals(personalityResult.getPersonId(), personality.getPersonId());
    }

    @Test
    public void testDeleteAdminAssignmentRequestBeanWhenPersonLeaveAdministratorAssignmentRetrieveLeaveAdminByPersonIdReturnNullAndThrowException() {
        assertThrows(APIException.class, () -> {
            AdminAssignmentRequestBean adminAssignmentRequestBean = preparePersonAdminAssignmentRequestBean(personId);
            adminAssignmentRequestBean.setAdministrator(prepareAdminProfileBean());
            Personality personality = mock(Personality.class);
            Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(personId));
            Mockito.when(personality.getPersonNumber()).thenReturn(personNumber);

            Mockito.doNothing().when(personIdentityBeanValidator).newvalidate(Mockito.any(), Mockito.any(PersonIdentityBean.class), Mockito.any());
            Mockito.when(personIdentityBeanValidator.getPersonality(Mockito.any(PersonIdentityBean.class))).thenReturn(personality);
            Mockito.doNothing().when(licenseValidator).validateLeaveLicense(Mockito.any(), Mockito.anyBoolean());

            Mockito.when(PersonLeaveAdministratorAssignment.retrieveLeaveAdminByPersonId(personality.getPersonId())).thenReturn(null);
            personLeaveAdminAssignmentService.deleteRequest(adminAssignmentRequestBean);
        });
    }


    @Test
    public void testGetAdministratorPersonLeaveAdminAssignmentServiceGetAdministrator() {
        PersonIdentityBean personIdentityBean = preparePersonIdentityBean();
        Mockito.when(personIdentityBeanValidator.createForPersonNumber(Mockito.anyString())).thenReturn(personIdentityBean);
        Personality personality = mock(Personality.class);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(personId));
        Mockito.when(personality.getPersonNumber()).thenReturn(personNumber);
        Mockito.when(Personality.getByPersonId(Mockito.any(ObjectIdLong.class))).thenReturn(personality);
        PersonLeaveAdministratorAssignment personLeaveAdministratorAssignment = Mockito.mock(PersonLeaveAdministratorAssignment.class);
        Mockito.when(personLeaveAdministratorAssignment.getLeaveAdminId()).thenReturn(new ObjectIdLong(personId));
        PersonIdentityBean result = personLeaveAdminAssignmentService.getAdministrator(personLeaveAdministratorAssignment);
        assertEquals(result.getPersonNumber(), personIdentityBean.getPersonNumber());
    }


    private AdminAssignmentRequestBean preparePersonAdminAssignmentRequestBean(Long personId) {
        AdminAssignmentRequestBean adminAssignmentRequestBean = new AdminAssignmentRequestBean();
        PersonIdentityBean personIdentity = new PersonIdentityBean();
        personIdentity.setPersonKey(personId);
        adminAssignmentRequestBean.setPersonIdentity(personIdentity);
        return adminAssignmentRequestBean;
    }

    private AdminProfileBean prepareAdminProfileBean() {
        AdminProfileBean adminBean = new AdminProfileBean();
        adminBean.setPersonNumber(personNumber);
        return adminBean;
    }

    private PersonIdentityBean preparePersonIdentityBean() {
        PersonIdentityBean personIdentity = new PersonIdentityBean();
        personIdentity.setPersonNumber(personNumber);
        return personIdentity;
    }


}
