package com.kronos.persons.cacheretry.daemon;

import com.kronos.eventframework.dto.EventManagerDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
public class PersonDeleteDaemonImplMicroTest {
	@Mock
	EventManagerDTO daemonDTO;

	@Mock
	PersonCacheUpdateAgent personCacheUpdateAgent;
	
	@InjectMocks
	PersonCacheUpdateDaemonImpl personCacheUpdateDaemonImpl;

	@Test
	public void testRun() throws Exception {
		Mockito.doNothing().when(personCacheUpdateAgent).execute();
		personCacheUpdateDaemonImpl.run(daemonDTO);
		assertTrue(true);
	}

}
