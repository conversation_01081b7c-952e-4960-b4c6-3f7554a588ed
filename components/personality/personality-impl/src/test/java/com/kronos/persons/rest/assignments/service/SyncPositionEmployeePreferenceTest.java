package com.kronos.persons.rest.assignments.service;

import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.persons.positions.dataaccess.api.service.IPositionsEmployeeJobPreferenceService;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.positions.PositionJobContext;
import com.kronos.wfc.commonapp.people.business.positions.PositionJobPreference;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.scheduling.core.business.EmployeeSeniority;
import com.kronos.wfc.scheduling.core.business.preference.JobPreference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.Mockito.never;

@ExtendWith(MockitoExtension.class)
public class SyncPositionEmployeePreferenceTest {

   private static final KDate SENIORITY_DATE = KDate.create(2088, 3, 18);
   private static final Long ORG_JOB_ID = 2L;
   private static final Long ORG_JOB_ID_2 = 22L;
   private static final Long EMPLOYEE_ID = 1L;
   private static final Integer PREFERENCE_RATING = 5;
   private static final Long PURPOSE_ID = 88L;

   @Mock
   private IPositionsEmployeeJobPreferenceService jobPreferenceService;

   @Mock
   private EmployeeExtension employeeExtension;

   @InjectMocks
   private SyncPositionEmployeePreference underTest;

   @Captor
   ArgumentCaptor<Collection<PositionJobPreference>> positionCaptor;

   @BeforeEach
   public void setup() {
      Mockito.when(employeeExtension.isMultiPosition()).thenReturn(false);
   }

   @Test
   public void sync_whenSenoirityAndPreference_thenSaveOneCompleteRecord() {
      List<EmployeeSeniority> seniorities = Collections.singletonList(createEmployeeSeniority(EMPLOYEE_ID, ORG_JOB_ID, SENIORITY_DATE));
      List<JobPreference> jobPrefList = Collections.singletonList(createJobPreference(EMPLOYEE_ID, ORG_JOB_ID, PREFERENCE_RATING));

      underTest.sync(seniorities, jobPrefList, employeeExtension);

      Mockito.verify(jobPreferenceService).savePreferences(positionCaptor.capture());
      List<Collection<PositionJobPreference>> capturer = positionCaptor.getAllValues();
      assertEquals(1, capturer.get(0).size());
      PositionJobPreference actual = capturer.get(0).iterator().next();
      assertEquals(Long.valueOf(Math.negateExact(EMPLOYEE_ID)), actual.getPositionId().toLong());
      assertEquals(ORG_JOB_ID, actual.getOrgJobId().toLong());
      assertEquals(Long.valueOf(PREFERENCE_RATING), actual.getPreferenceRating());
      assertEquals(SENIORITY_DATE, actual.getSeniorityDate());
      assertEquals(1, actual.getPositionJobContexts().size());
      PositionJobContext positionContext = actual.getPositionJobContexts().iterator().next();
      assertEquals(Long.valueOf(Math.negateExact(EMPLOYEE_ID)), positionContext.getPositionId().toLong());
      assertEquals(ORG_JOB_ID, positionContext.getOrgJobId().toLong());
      assertEquals(PURPOSE_ID, positionContext.getContextId().toLong());
   }

   @Test
   public void sync_whenSenoirityAndPreferenceDifferent_thenSaveTwoRecord() {
      List<EmployeeSeniority> seniorities = Collections.singletonList(createEmployeeSeniority(EMPLOYEE_ID, ORG_JOB_ID, SENIORITY_DATE));
      List<JobPreference> jobPrefList = Collections.singletonList(createJobPreference(EMPLOYEE_ID, ORG_JOB_ID_2, PREFERENCE_RATING));

      underTest.sync(seniorities, jobPrefList, employeeExtension);

      Mockito.verify(jobPreferenceService).savePreferences(positionCaptor.capture());
      List<Collection<PositionJobPreference>> ccc = positionCaptor.getAllValues();
      assertEquals(2, ccc.get(0).size());
   }

   @Test
   public void sync_whenSenoirity_thenSaveOneRecord() {
      List<EmployeeSeniority> seniorities = Collections.singletonList(createEmployeeSeniority(EMPLOYEE_ID, ORG_JOB_ID, SENIORITY_DATE));
      underTest.sync(seniorities, Collections.emptyList(), employeeExtension);
      Mockito.verify(jobPreferenceService).savePreferences(positionCaptor.capture());

      List<Collection<PositionJobPreference>> ccc = positionCaptor.getAllValues();

      assertEquals(1, ccc.get(0).size());
   }

   @Test
   public void sync_whenPreference_thenSaveOneRecord() {
      List<JobPreference> jobPrefList = Collections.singletonList(createJobPreference(EMPLOYEE_ID, ORG_JOB_ID_2, PREFERENCE_RATING));
      underTest.sync(Collections.emptyList(), jobPrefList, employeeExtension);
      Mockito.verify(jobPreferenceService).savePreferences(positionCaptor.capture());

      List<Collection<PositionJobPreference>> ccc = positionCaptor.getAllValues();

      assertEquals(1, ccc.get(0).size());
   }

   @Test
   public void sync_whenNotMultiPosition_thenSkip() {
      List<JobPreference> jobPrefList = Collections.singletonList(createJobPreference(EMPLOYEE_ID, ORG_JOB_ID_2, PREFERENCE_RATING));
      Mockito.when(employeeExtension.isMultiPosition()).thenReturn(true);
      underTest.sync(Collections.emptyList(), jobPrefList, employeeExtension);
      Mockito.verify(jobPreferenceService, never()).savePreferences(anyCollection());
   }

   @Test
   public void sync_whenNoRecords_thenSaveNoRecords() {
      underTest.sync(Collections.emptyList(), Collections.emptyList(), employeeExtension);
      Mockito.verify(jobPreferenceService).savePreferences(positionCaptor.capture());

      List<Collection<PositionJobPreference>> ccc = positionCaptor.getAllValues();

      assertEquals(0, ccc.get(0).size());
   }

   private EmployeeSeniority createEmployeeSeniority(Long employeeId, Long orgJobId, KDate seniorityDate) {
      return new EmployeeSeniority(new ObjectIdLong(employeeId), new ObjectIdLong(orgJobId), seniorityDate);
   }

   private JobPreference createJobPreference(Long empId, Long orgJobId, Integer rating) {
      JobPreference jobPreference = new JobPreference(new ObjectIdLong(empId), new ObjectIdLong(orgJobId), rating);
      jobPreference.setPurposeIds(Collections.singletonList(new ObjectIdLong(PURPOSE_ID)));
      return jobPreference;
   }
}
