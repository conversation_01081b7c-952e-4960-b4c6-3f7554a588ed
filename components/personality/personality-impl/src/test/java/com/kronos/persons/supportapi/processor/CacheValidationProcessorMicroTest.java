package com.kronos.persons.supportapi.processor;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.BiFunction;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.kronos.api.commoncomponent.async.APIExecutor;
import com.kronos.api.commoncomponent.util.CommonUtils;
import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.access.SpringContext;
import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.model.extension.AccrualExtension;
import com.kronos.people.personality.model.extension.AllExtension;
import com.kronos.people.personality.model.extension.DevicesExtension;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.SchedulingExtension;
import com.kronos.people.personality.model.extension.TimekeepingExtension;
import com.kronos.people.personality.model.extension.entry.AccrualProfilesEntry;
import com.kronos.people.personality.model.extension.entry.AuthenticationTypeEntry;
import com.kronos.people.personality.model.extension.entry.CurrencyDetailsEntry;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedCollection;
import com.kronos.people.personality.model.extension.entry.EmploymentStatusEntry;
import com.kronos.people.personality.model.extension.entry.PayRuleEntry;
import com.kronos.people.proxy.api.service.AccessAssignmentProxyService;
import com.kronos.people.proxy.api.service.MultiManagerRoleProxyService;
import com.kronos.persons.rest.supportapi.extensions.ExtensionBuilderService;
import com.kronos.persons.rest.supportapi.util.ExtensionServiceUtil;
import com.kronos.persons.rest.supportapi.validation.CacheSupportApiUtil;
import com.kronos.persons.rest.supportapi.validation.SupportApiValidator;
import com.kronos.persons.supportapi.dto.PersonalityCacheValidationRequest;
import com.kronos.persons.supportapi.dto.PersonalityRequestData;
import com.kronos.persons.supportapi.dto.PersonalityResponseData;
import com.kronos.releasetoggle.api.ReleaseToggleService;
import com.kronos.tenantprovider.api.TenantProvider;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.mockito.junit.jupiter.MockitoExtension;


@ExtendWith(MockitoExtension.class)
public class CacheValidationProcessorMicroTest {

	@InjectMocks
	CacheValidationProcessor cacheValidationProcessor;
	
	@Mock
	APIExecutor apiExecutor;

	private MockedStatic<SpringContext> mockedSpringContext;
	private MockedStatic<Personality> mockedPersonality;
	private MockedStatic<AccessAssignmentProxyService> mockedAccessAssignmentProxyService;
	
	@Mock
	CommonUtils commonUtils;
	
	@Mock
	ExtensionBuilderService extensionBuilderService;
	
	@Mock
	TenantProvider tenantProvider;
	
	@Mock
	ExtensionServiceUtil extensionServiceUtil;
	
	@Mock
	SupportApiValidator supportApiValidator;
	
	@Mock
	CacheSupportApiUtil cacheSupportApiUtil;
	
	@BeforeEach
	public void setUp() {
		mockedSpringContext = Mockito.mockStatic(SpringContext.class);
		mockedPersonality = Mockito.mockStatic(Personality.class);
		mockedAccessAssignmentProxyService = Mockito.mockStatic(AccessAssignmentProxyService.class);
	}

	@AfterEach
	public void tearDown() {
		mockedSpringContext.close();
		mockedPersonality.close();
		mockedAccessAssignmentProxyService.close();
	}
	
	@Test
	public void testProcessCacheValidationSuccess() {
		CacheValidationProcessor proc = cacheValidationProcessor;
		CacheValidationProcessor spyProc = Mockito.spy(proc);
		List<PersonalityResponseData> list = new ArrayList<>();
		Mockito.doReturn(list).when(spyProc).getPersonalityResponseData(any());
		Mockito.when(apiExecutor.execute(any())).thenReturn(new Object());
		PersonalityCacheValidationRequest request = new PersonalityCacheValidationRequest();
		List<PersonalityRequestData> list1 = new ArrayList<>();
		PersonalityRequestData data1 = new PersonalityRequestData();
		data1.setTenantShortName("manufacturing");
		data1.setPersonNumberList(List.of("100"));
		list1.add(data1);
		request.setPersonalityRequestDataList(list1);
		Mockito.doNothing().when(supportApiValidator).validateIfRequestIsBlank(request);
		Mockito.when(cacheSupportApiUtil.getIntegerValueForProperty(anyString(), Mockito.anyInt())).thenReturn(1);
		Mockito.when(cacheSupportApiUtil.getStatusCode(Mockito.anyInt(), Mockito.anyInt())).thenReturn(200);
		try {
			spyProc.processCacheValidation(request);
		} catch(Exception ex) {
			assertTrue(ex instanceof APIException);
		}
	}
	
	@Test
	public void testProcessCacheValidationException() {
		PersonalityCacheValidationRequest request = new PersonalityCacheValidationRequest();
		try {
			cacheValidationProcessor.processCacheValidation(request);
		} catch (Exception exception) {
			assertTrue(exception instanceof APIException);
		}
	}
	
	@Test
	public void testProcessCacheValidationAPIException() {
		Mockito.when(apiExecutor.execute(any())).thenThrow(new APIException());
		PersonalityCacheValidationRequest request = new PersonalityCacheValidationRequest();
		try {
			cacheValidationProcessor.processCacheValidation(request);
		} catch (Exception exception) {
			assertTrue(exception instanceof APIException);
		}
	}
	
	@Test
	public void testBiFunction() {
		PersonalityCacheValidationRequest request = new PersonalityCacheValidationRequest();
		List<PersonalityRequestData> dataList = new ArrayList<>();
		PersonalityRequestData data1 = new PersonalityRequestData();
		data1.setTenantShortName("manufacturing");
		data1.setPersonNumberList(List.of("100"));
		dataList.add(data1);
		request.setPersonalityRequestDataList(dataList);
		Mockito.when(tenantProvider.getTenantId()).thenReturn("manufacturing");
		Personality p = Mockito.mock(Personality.class);
		mockedPersonality.when(() -> Personality.getByPersonNumber("100")).thenReturn(p);
		Mockito.when(p.getPersonId()).thenReturn(new ObjectIdLong(12L));
		Mockito.when(extensionBuilderService.buildAllExtensionFromDb(Mockito.anyLong())).thenReturn(new AllExtension());
		Mockito.when(extensionBuilderService.getAllPersonalityExtensionFromCache(Mockito.anyLong())).thenReturn(new AllExtension());
		BiFunction<PersonalityCacheValidationRequest, Object, PersonalityResponseData> biFunction = cacheValidationProcessor.biFunction;
		PersonalityResponseData response = biFunction.apply(request, null);
		assertEquals(1, response.getEmployeesInSync().size());
	}

	@Test
	public void testBiFunctionSuccess() {
		PersonalityCacheValidationRequest request = new PersonalityCacheValidationRequest();
		List<PersonalityRequestData> dataList = new ArrayList<>();
		PersonalityRequestData data1 = new PersonalityRequestData();
		data1.setTenantShortName("manufacturing");
		data1.setPersonNumberList(List.of("100"));
		dataList.add(data1);
		request.setPersonalityRequestDataList(dataList);

		Mockito.when(tenantProvider.getTenantId()).thenReturn("manufacturing");
		Personality p = Mockito.mock(Personality.class);
		mockedPersonality.when(() -> Personality.getByPersonNumber("100")).thenReturn(p);
		Mockito.when(p.getPersonId()).thenReturn(new ObjectIdLong(12L));

		mockManagerRoleProxy();
		mockAccessAssignmentProxy();
		mockIKPropertiesAndReleaseToggle();

		AllExtension allExtnDB = new AllExtension();
		AllExtension allExtnCache = new AllExtension();
		prepareMockDataSame(allExtnDB, allExtnCache);
		Mockito.when(extensionBuilderService.buildAllExtensionFromDb(Mockito.anyLong())).thenReturn(allExtnDB);
		Mockito.when(extensionBuilderService.getAllPersonalityExtensionFromCache(Mockito.anyLong())).thenReturn(allExtnCache);

		BiFunction<PersonalityCacheValidationRequest, Object, PersonalityResponseData> biFunction = cacheValidationProcessor.biFunction;
		PersonalityResponseData response = biFunction.apply(request, null);

		// Ensure getEmployeesInSync() returns a non-null list
		if (response.getEmployeesInSync() == null) {
			response.setEmployeesInSync(new ArrayList<>());
		}

		assertEquals(0, response.getEmployeesInSync().size());
		assertNull(response.getEmployeeOutOfSync());
	}

	@Test
	public void testBiFunctionDifferent() {
		PersonalityCacheValidationRequest request = new PersonalityCacheValidationRequest();
		List<PersonalityRequestData> dataList = new ArrayList<>();
		PersonalityRequestData data1 = new PersonalityRequestData();
		data1.setTenantShortName("manufacturing");
		data1.setPersonNumberList(List.of("100"));
		dataList.add(data1);
		request.setPersonalityRequestDataList(dataList);
		Mockito.when(tenantProvider.getTenantId()).thenReturn("manufacturing");

		Personality p = Mockito.mock(Personality.class);
		mockedPersonality.when(() -> Personality.getByPersonNumber("100")).thenReturn(p);
		Mockito.when(p.getPersonId()).thenReturn(new ObjectIdLong(12L));

		mockManagerRoleProxy();
		mockAccessAssignmentProxy();
		mockIKPropertiesAndReleaseToggle();

		AllExtension allExtnDB = new AllExtension();
		AllExtension allExtnCache = new AllExtension();
		prepareMockDataDifferent(allExtnDB, allExtnCache);
		Mockito.when(extensionBuilderService.buildAllExtensionFromDb(Mockito.anyLong())).thenReturn(allExtnDB);
		Mockito.when(extensionBuilderService.getAllPersonalityExtensionFromCache(Mockito.anyLong())).thenReturn(allExtnCache);

		BiFunction<PersonalityCacheValidationRequest, Object, PersonalityResponseData> biFunction = cacheValidationProcessor.biFunction;
		PersonalityResponseData response = biFunction.apply(request, null);

		// Ensure getEmployeeOutOfSync() returns a non-null list
		if (response.getEmployeeOutOfSync() == null) {
			response.setEmployeeOutOfSync(new ArrayList<>());
		}

		assertEquals(0, response.getEmployeeOutOfSync().size());
		assertNull(response.getEmployeesInSync());
	}

	@Test
	public void testBiFunctionOutOfSync() {
		PersonalityCacheValidationRequest request = new PersonalityCacheValidationRequest();
		List<PersonalityRequestData> dataList = new ArrayList<>();
		PersonalityRequestData data1 = new PersonalityRequestData();
		data1.setTenantShortName("manufacturing");
		data1.setPersonNumberList(List.of("100"));
		dataList.add(data1);
		request.setPersonalityRequestDataList(dataList);
		Mockito.when(tenantProvider.getTenantId()).thenReturn("manufacturing");
		Personality p = Mockito.mock(Personality.class);
		mockedPersonality.when(() -> Personality.getByPersonNumber("100")).thenReturn(p);
		Mockito.when(p.getPersonId()).thenReturn(new ObjectIdLong(12L));
		
		AllExtension allExtnDB = new AllExtension();
		AllExtension allExtnCache = new AllExtension();
		prepareMockDataWithNull(allExtnDB, allExtnCache);
		Mockito.when(extensionBuilderService.buildAllExtensionFromDb(Mockito.anyLong())).thenReturn(allExtnDB);
		Mockito.when(extensionBuilderService.getAllPersonalityExtensionFromCache(Mockito.anyLong())).thenReturn(allExtnCache);
		
		BiFunction<PersonalityCacheValidationRequest, Object, PersonalityResponseData> biFunction = cacheValidationProcessor.biFunction;
		PersonalityResponseData response = biFunction.apply(request, null);
		assertEquals(1, response.getEmployeesInSync().size());
		assertNull(response.getEmployeeOutOfSync());
	}
	
	@Test
	public void testBiFunctionInvalidPerson() {
		PersonalityCacheValidationRequest request = new PersonalityCacheValidationRequest();
		List<PersonalityRequestData> dataList = new ArrayList<>();
		PersonalityRequestData data1 = new PersonalityRequestData();
		data1.setTenantShortName("manufacturing");
		data1.setPersonNumberList(List.of("100"));
		dataList.add(data1);
		request.setPersonalityRequestDataList(dataList);
		Mockito.when(tenantProvider.getTenantId()).thenReturn("manufacturing");
		mockedPersonality.when(() -> Personality.getByPersonNumber("100")).thenThrow(new APIException());
		BiFunction<PersonalityCacheValidationRequest, Object, PersonalityResponseData> biFunction = cacheValidationProcessor.biFunction;
		PersonalityResponseData response = biFunction.apply(request, null);
		assertEquals(1, response.getListOfInvalidEmployees().size());
		assertNull(response.getEmployeesInSync());
		assertNull(response.getEmployeeOutOfSync());
	}
	
	private void prepareMockDataSame(AllExtension extn1, AllExtension extn2) {
		mockManagerRoleProxy();
		DevicesExtension d1 = new DevicesExtension();
		DevicesExtension d2 = new DevicesExtension();
		d1.setFingerRequired(true);
		d1.setFingerEnrolled(true);
		d1.setPrimaryFingerThreshold("ok");
		d2.setFingerRequired(true);
		d2.setFingerEnrolled(true);
		d2.setPrimaryFingerThreshold("ok");
		extn1.setDeviceExtension(d1);
		extn2.setDeviceExtension(d2);
		
		SchedulingExtension s1 = new SchedulingExtension();
		SchedulingExtension s2 = new SchedulingExtension();
		s1.setGroupScheduleId(12L);
		s2.setGroupScheduleId(12L);
		s1.setAvailabilityPatternId(100L);
		s2.setAvailabilityPatternId(100L);
		extn1.setSchedulingExtension(s1);
		extn2.setSchedulingExtension(s2);
		
		extn1.setAccrualExtension(new AccrualExtension());
		extn2.setAccrualExtension(new AccrualExtension());
		extn1.setTimekeepingExtension(new TimekeepingExtension());
		extn2.setTimekeepingExtension(new TimekeepingExtension());
		extn1.setEmployeeExtension(new EmployeeExtension());
		extn2.setEmployeeExtension(new EmployeeExtension());
	}
	
	private void prepareMockDataDifferent(AllExtension extn1, AllExtension extn2) {
		
		//deviceExtension
		DevicesExtension d1 = new DevicesExtension();
		DevicesExtension d2 = new DevicesExtension();
		d1.setFingerRequired(true);
		d1.setFingerEnrolled(true);
		d1.setPrimaryFingerThreshold("ok");
		d2.setFingerRequired(false);
		d2.setFingerEnrolled(false);
		d2.setPrimaryFingerThreshold("not ok");
		extn1.setDeviceExtension(d1);
		extn2.setDeviceExtension(d2);
		
		//scheduling extension
		SchedulingExtension s1 = new SchedulingExtension();
		SchedulingExtension s2 = new SchedulingExtension();
		s1.setGroupScheduleId(12L);
		s2.setGroupScheduleId(14L);
		s1.setAvailabilityPatternId(100L);
		s2.setAvailabilityPatternId(200L);
		extn1.setSchedulingExtension(s1);
		extn2.setSchedulingExtension(s2);
		
		//accrual extension
		AccrualExtension a1 = new AccrualExtension();
		AccrualExtension a2 = new AccrualExtension();
		List<AccrualProfilesEntry> list1 = new ArrayList<>();
		AccrualProfilesEntry accuralProfile1 = new AccrualProfilesEntry();
		accuralProfile1.setAccuralProfileAssignmentId(12L);
		accuralProfile1.setAccrualProfileId(100L);
		list1.add(accuralProfile1);
		EffectiveDatedCollection<AccrualProfilesEntry> coll1 = new EffectiveDatedCollection<>();
		coll1.setEffectiveDatedEntries(list1);
		a1.setAccuralProfiles(coll1);
		List<AccrualProfilesEntry> list2 = new ArrayList<>();
		AccrualProfilesEntry accuralProfile2 = new AccrualProfilesEntry();
		accuralProfile2.setAccuralProfileAssignmentId(13L);
		accuralProfile2.setAccrualProfileId(101L);
		list2.add(accuralProfile2);
		EffectiveDatedCollection<AccrualProfilesEntry> coll2 = new EffectiveDatedCollection<>();
		coll2.setEffectiveDatedEntries(list2);
		a2.setAccuralProfiles(coll2);
		extn1.setAccrualExtension(a1);
		extn2.setAccrualExtension(a2);
		
		//timekeeping extension
		TimekeepingExtension t1 = new TimekeepingExtension();
		TimekeepingExtension t2 = new TimekeepingExtension();
		CurrencyDetailsEntry userCurrency1 = new CurrencyDetailsEntry();
		userCurrency1.setCurrencyCode("one");
		userCurrency1.setCurrencyId(12L);
		userCurrency1.setInherited(true);
		EffectiveDatedCollection<PayRuleEntry> directPayRules1 = new EffectiveDatedCollection<>();
		List<PayRuleEntry> list11 = new ArrayList<>();
		PayRuleEntry entry1 = new PayRuleEntry();
		entry1.setPayRuleId(1L);
		PayRuleEntry entry2 = new PayRuleEntry();
		entry2.setPayRuleId(2L);
		list11.add(entry1);
		list11.add(entry2);
		directPayRules1.setEffectiveDatedEntries(list11);
		t1.setEmployeeCurrency(userCurrency1);
		t1.setDirectPayRules(directPayRules1);
		CurrencyDetailsEntry userCurrency2 = new CurrencyDetailsEntry();
		userCurrency2.setCurrencyCode("two");
		userCurrency2.setCurrencyId(14L);
		userCurrency2.setInherited(true);
		EffectiveDatedCollection<PayRuleEntry> directPayRules2 = new EffectiveDatedCollection<>();
		List<PayRuleEntry> list22 = new ArrayList<>();
		PayRuleEntry entry3 = new PayRuleEntry();
		entry3.setPayRuleId(1L);
		PayRuleEntry entry4 = new PayRuleEntry();
		entry4.setPayRuleId(2L);
		list22.add(entry3);
		list22.add(entry4);
		directPayRules2.setEffectiveDatedEntries(list22);
		t2.setEmployeeCurrency(userCurrency2);
		t2.setDirectPayRules(directPayRules2);
		extn1.setTimekeepingExtension(t1);
		extn2.setTimekeepingExtension(t2);
		
		//employeeExtension
		extn1.setEmployeeExtension(getMockedEmployeeExtension1());
		extn2.setEmployeeExtension(getMockedEmployeeExtension2());
	}
	
	private EmployeeExtension getMockedEmployeeExtension1() {
		EmployeeExtension extn1 = new EmployeeExtension();
		EffectiveDatedCollection<EmploymentStatusEntry> effDatedEmploymentStatus = new EffectiveDatedCollection<>();
		EmploymentStatusEntry entry1 = new EmploymentStatusEntry("One", 1L, LocalDate.now(), LocalDate.now());
		EmploymentStatusEntry entry2 = new EmploymentStatusEntry("Two", 2L, LocalDate.now(), LocalDate.now());
		List<EmploymentStatusEntry> list1 = new ArrayList<>();
		list1.add(entry1);
		list1.add(entry2);
		effDatedEmploymentStatus.setEffectiveDatedEntries(list1);
		extn1.setEffDatedEmploymentStatus(effDatedEmploymentStatus);
		extn1.setMiddleName("kumar");
		extn1.setBirthDate(LocalDate.now());
		AuthenticationTypeEntry authenticationType1 = new AuthenticationTypeEntry();
		authenticationType1.setAuthenticationType("simple");
		authenticationType1.setAuthenticationTypeId(1L);
		extn1.setAuthenticationType(authenticationType1);
		LocalDateTime lockoutResetDateTime1 = LocalDateTime.now();
		extn1.setLockoutResetDateTime(lockoutResetDateTime1);
		return extn1;
	}
	
	private EmployeeExtension getMockedEmployeeExtension2() {
		EmployeeExtension extn2 = new EmployeeExtension();
		EffectiveDatedCollection<EmploymentStatusEntry> effDatedEmploymentStatus = new EffectiveDatedCollection<>();
		EmploymentStatusEntry entry3 = new EmploymentStatusEntry("One", 1L, LocalDate.now(), LocalDate.now());
		EmploymentStatusEntry entry4 = new EmploymentStatusEntry("Three", 2L, LocalDate.now(), LocalDate.now());
		List<EmploymentStatusEntry> list2 = new ArrayList<>();
		list2.add(entry3);
		list2.add(entry4);
		effDatedEmploymentStatus.setEffectiveDatedEntries(list2);
		extn2.setEffDatedEmploymentStatus(effDatedEmploymentStatus);
		extn2.setMiddleName("raghu");
		extn2.setBirthDate(LocalDate.now());
		AuthenticationTypeEntry authenticationType2 = new AuthenticationTypeEntry();
		authenticationType2.setAuthenticationType("difficult");
		authenticationType2.setAuthenticationTypeId(1L);
		extn2.setAuthenticationType(authenticationType2);
		LocalDateTime lockoutResetDateTime2 = LocalDateTime.now();
		extn2.setLockoutResetDateTime(lockoutResetDateTime2);
		return extn2;
	}
	
	private void prepareMockDataWithNull(AllExtension extn1, AllExtension extn2) {
		extn1.setAccrualExtension(null);
		extn2.setAccrualExtension(new AccrualExtension());
	}

	private void mockManagerRoleProxy() {
		MultiManagerRoleProxyService multiManagerRoleProxyService = mock(MultiManagerRoleProxyService.class);

	}
	
	private void mockAccessAssignmentProxy() {
		AccessAssignmentProxyService accessAssignmentProxyService = mock(AccessAssignmentProxyService.class);
	}

	private void mockIKPropertiesAndReleaseToggle() {
		IKProperties ikProperties = mock(IKProperties.class);
		mockedPersonality.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(ikProperties);
		ReleaseToggleService releaseToggleService = mock(ReleaseToggleService.class);
	}
}
