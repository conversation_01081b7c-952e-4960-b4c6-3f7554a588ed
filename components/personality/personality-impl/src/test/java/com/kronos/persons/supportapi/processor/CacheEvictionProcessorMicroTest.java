package com.kronos.persons.supportapi.processor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.ws.rs.core.Response;

import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.kronos.api.commoncomponent.async.APIExecutor;
import com.kronos.api.commoncomponent.util.CommonUtils;
import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.cache.ExtensionCacheUpdater;
import com.kronos.persons.rest.supportapi.validation.CacheSupportApiUtil;
import com.kronos.persons.rest.supportapi.validation.SupportApiValidator;
import com.kronos.persons.supportapi.dto.PersonDetail;
import com.kronos.persons.supportapi.dto.PersonalityCacheEvictRequest;
import com.kronos.persons.supportapi.dto.PersonalityEvictRequestData;
import com.kronos.persons.supportapi.dto.PersonalityEvictResponseData;
import com.kronos.persons.supportapi.errorcodes.PersonalitySupportApiConstants;
import com.kronos.tenantprovider.api.TenantProvider;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;


@ExtendWith(MockitoExtension.class)
public class CacheEvictionProcessorMicroTest {
	
	@InjectMocks
	CacheEvictionProcessor cacheProcessor;
	
	@Mock
	ExtensionCacheUpdater extensionCacheUpdateService;
	
	@Mock
	TenantProvider tenantProvider;

	private MockedStatic<Personality> mockedPersonality;
	
	@Mock
	Personality personality;
	
	@Mock
	CommonUtils commonUtils;
	
	@Mock
	SupportApiValidator supportApiValidator;
	
	@Mock
	APIExecutor executor;
	
	@Mock 
	CacheSupportApiUtil cacheSupportApiUtils;


	@BeforeEach
	public void setUp() {
		mockedPersonality = Mockito.mockStatic(Personality.class);
	}

	@AfterEach
	public void tearDown() {
		mockedPersonality.close();
	}
	
	@Test
	public void testCacheExeFunOnDeleteBothLegacyAndNewCaches() {
		CacheEvictionProcessor processor = cacheProcessor;
		CacheEvictionProcessor spyProcessor = Mockito.spy(processor);
		List<PersonalityEvictResponseData> list1 = new ArrayList<>();
		Mockito.doReturn(list1).when(spyProcessor).getInvalidTenantListFromResponse(Mockito.any());
		PersonalityCacheEvictRequest personalityCacheEvictReq = new PersonalityCacheEvictRequest();
		List<PersonalityEvictRequestData> personalityEvictRequestDataList = new ArrayList<>();
		PersonalityEvictRequestData personalityEvictRequestData = new PersonalityEvictRequestData();
		personalityEvictRequestData.setTenantShortName("manufacturing");
		personalityEvictRequestData.setEvictFrom(PersonalitySupportApiConstants.BOTH_LEGACY_AND_PERSON_CACHE);
		PersonDetail personDetail = new PersonDetail();
		personDetail.setPersonNum("20335");
		List<PersonDetail> personDetailsList = new ArrayList<>();
		personDetailsList.add(personDetail);
		JSONObject joson = getJSONObject();
		mockedPersonality.when(() -> Personality.getByPersonNumber(personDetail.getPersonNum())).thenReturn(personality);
		Mockito.doNothing().when(supportApiValidator).checkForDuplicateParametersInRequest(Mockito.anyList(), Mockito.anyString(), Mockito.anyString());
		Mockito.when(commonUtils.getRequestURI()).thenReturn("");
		Mockito.when(cacheSupportApiUtils.getStatusCode(Mockito.anyInt(), Mockito.anyInt())).thenReturn(200);
		Mockito.when(executor.execute(Mockito.any())).thenReturn(joson);
		personalityEvictRequestData.setPersonNumberList(personDetailsList);
		personalityEvictRequestDataList.add(personalityEvictRequestData);
		personalityCacheEvictReq.setTenantPersonalityRequestData(personalityEvictRequestDataList);
		Response response = spyProcessor.evictPersonCacheProcessor(personalityCacheEvictReq);
		assertNotNull(response);
	}
	

	private JSONObject getJSONObject() {
		PersonalityCacheEvictRequest personalityCacheEvictReq = new PersonalityCacheEvictRequest();
		List<PersonalityEvictRequestData> personalityEvictRequestDataList = new ArrayList<>();
		PersonalityEvictRequestData personalityEvictRequestData = new PersonalityEvictRequestData();
		PersonalityEvictResponseData personalityEvictResponseData = new PersonalityEvictResponseData();
		personalityEvictRequestData.setTenantShortName("manufacturing");
		personalityEvictRequestData.setEvictFrom(PersonalitySupportApiConstants.BOTH_LEGACY_AND_PERSON_CACHE);
		PersonDetail personDetail = new PersonDetail();
		personDetail.setPersonNum("20335");
		List<PersonDetail> personDetailsList = new ArrayList<>();
		personDetailsList.add(personDetail);
		personalityEvictRequestData.setPersonNumberList(personDetailsList);
		personalityEvictRequestDataList.add(personalityEvictRequestData);
		personalityCacheEvictReq.setTenantPersonalityRequestData(personalityEvictRequestDataList);
		 JSONObject joson = new JSONObject(); 
	     joson.put("response", personalityEvictResponseData);
		return joson;
	}

	@Test
	public void testCacheExeFunOnDeleteOnlyOnNewCaches() {
		CacheEvictionProcessor processor = cacheProcessor;
		CacheEvictionProcessor spyProcessor = Mockito.spy(processor);
		List<PersonalityEvictResponseData> list1 = new ArrayList<>();
		Mockito.doReturn(list1).when(spyProcessor).getInvalidTenantListFromResponse(Mockito.any());
		PersonalityCacheEvictRequest personalityCacheEvictReq = new PersonalityCacheEvictRequest();
		List<PersonalityEvictRequestData> personalityEvictRequestDataList = new ArrayList<>();
		PersonalityEvictRequestData personalityEvictRequestData = new PersonalityEvictRequestData();
		personalityEvictRequestData.setTenantShortName("manufacturing");
		personalityEvictRequestData.setEvictFrom(PersonalitySupportApiConstants.NEW_PERSON_CACHE);
		PersonDetail personDetail = new PersonDetail();
		personDetail.setPersonNum("20335");
		List<PersonDetail> personDetailsList = new ArrayList<>();
		personDetailsList.add(personDetail);
		JSONObject joson = getJSONObject();
		mockedPersonality.when(() -> Personality.getByPersonNumber(personDetail.getPersonNum())).thenReturn(personality);
		Mockito.doNothing().when(supportApiValidator).checkForDuplicateParametersInRequest(Mockito.anyList(), Mockito.anyString(), Mockito.anyString());
		Mockito.when(commonUtils.getRequestURI()).thenReturn("");
		Mockito.when(cacheSupportApiUtils.getStatusCode(Mockito.anyInt(), Mockito.anyInt())).thenReturn(200);
		Mockito.when(executor.execute(Mockito.any())).thenReturn(joson);
		personalityEvictRequestData.setPersonNumberList(personDetailsList);
		personalityEvictRequestDataList.add(personalityEvictRequestData);
		personalityCacheEvictReq.setTenantPersonalityRequestData(personalityEvictRequestDataList);
		Response response = spyProcessor.evictPersonCacheProcessor(personalityCacheEvictReq);
		assertNotNull(response);
	}
	
	@Test
	public void testCacheExeFunOnDeleteOnlyOnLegacyCaches() {
		CacheEvictionProcessor processor = cacheProcessor;
		CacheEvictionProcessor spyProcessor = Mockito.spy(processor);
		List<PersonalityEvictResponseData> list1 = new ArrayList<>();
		Mockito.doReturn(list1).when(spyProcessor).getInvalidTenantListFromResponse(Mockito.any());
		PersonalityCacheEvictRequest personalityCacheEvictReq = new PersonalityCacheEvictRequest();
		List<PersonalityEvictRequestData> personalityEvictRequestDataList = new ArrayList<>();
		PersonalityEvictRequestData personalityEvictRequestData = new PersonalityEvictRequestData();
		personalityEvictRequestData.setTenantShortName("manufacturing");
		personalityEvictRequestData.setEvictFrom(PersonalitySupportApiConstants.LEGACY_PERSON_CACHE);
		PersonDetail personDetail = new PersonDetail();
		personDetail.setPersonNum("20335");
		personDetail.setPersonID("283");
		List<PersonDetail> personDetailsList = new ArrayList<>();
		personDetailsList.add(personDetail);
		JSONObject joson = getJSONObject();
		mockedPersonality.when(() -> Personality.getByPersonNumber(personDetail.getPersonNum())).thenReturn(personality);
		Mockito.doNothing().when(supportApiValidator).checkForDuplicateParametersInRequest(Mockito.anyList(), Mockito.anyString(), Mockito.anyString());
		Mockito.when(commonUtils.getRequestURI()).thenReturn("");
		Mockito.when(cacheSupportApiUtils.getStatusCode(Mockito.anyInt(), Mockito.anyInt())).thenReturn(200);
		Mockito.when(executor.execute(Mockito.any())).thenReturn(joson);
		personalityEvictRequestData.setPersonNumberList(personDetailsList);
		personalityEvictRequestDataList.add(personalityEvictRequestData);
		personalityCacheEvictReq.setTenantPersonalityRequestData(personalityEvictRequestDataList);
		Response response = spyProcessor.evictPersonCacheProcessor(personalityCacheEvictReq);
		assertNotNull(response);
	}
	
	
	@Test
	public void testCacheExecutionFunctionWithBothPersonCaches() {
		PersonalityCacheEvictRequest personalityCacheEvictReq = new PersonalityCacheEvictRequest();
		List<PersonalityEvictRequestData> personalityEvictRequestDataList = new ArrayList<>();
		PersonalityEvictRequestData personalityEvictRequestData = new PersonalityEvictRequestData();
		personalityEvictRequestData.setTenantShortName("manufacturing");
		personalityEvictRequestData.setEvictFrom(PersonalitySupportApiConstants.BOTH_LEGACY_AND_PERSON_CACHE);
		Mockito.when(tenantProvider.getTenantId()).thenReturn("manufacturing");
		PersonDetail personDetail = new PersonDetail();
		personDetail.setPersonNum("20335");
		personDetail.setPersonID("283");
		PersonDetail invalidPersonDetail = new PersonDetail();
		invalidPersonDetail.setPersonID("432");
		invalidPersonDetail.setPersonNum("34444444");
		List<PersonDetail> personDetailsList = new ArrayList<>();
		personDetailsList.add(personDetail);
		List<PersonDetail> inValidPersonDetailsList = new ArrayList<>();
		inValidPersonDetailsList.add(invalidPersonDetail);
		JSONObject joson = getJSONObject();
		mockedPersonality.when(() -> Personality.getByPersonNumber(personDetail.getPersonNum())).thenReturn(personality);
		Map<String , List<PersonDetail>> allEmployeeObjects = new HashMap<>();
		allEmployeeObjects.put(PersonalitySupportApiConstants.VALID_EMP_LIST, personDetailsList);
		allEmployeeObjects.put(PersonalitySupportApiConstants.INVALID_EMP_LIST, inValidPersonDetailsList);
		Mockito.when(cacheSupportApiUtils.getValidAndInvalidEmployeeIDs(Mockito.anyList())).thenReturn(allEmployeeObjects);
		personalityEvictRequestData.setPersonNumberList(personDetailsList);
		personalityEvictRequestDataList.add(personalityEvictRequestData);
		personalityCacheEvictReq.setTenantPersonalityRequestData(personalityEvictRequestDataList);
		PersonalityEvictResponseData response = cacheProcessor.cacheEvictExecutionFunction
				.apply(personalityEvictRequestDataList, "");
		assertNotNull(response);
	}
	
	@Test
	public void testCacheExecutionFunctionWithLegacyPersonCaches() {
		PersonalityCacheEvictRequest personalityCacheEvictReq = new PersonalityCacheEvictRequest();
		List<PersonalityEvictRequestData> personalityEvictRequestDataList = new ArrayList<>();
		PersonalityEvictRequestData personalityEvictRequestData = new PersonalityEvictRequestData();
		personalityEvictRequestData.setTenantShortName("manufacturing");
		personalityEvictRequestData.setEvictFrom(PersonalitySupportApiConstants.LEGACY_PERSON_CACHE);
		Mockito.when(tenantProvider.getTenantId()).thenReturn("manufacturing");
		PersonDetail personDetail = new PersonDetail();
		personDetail.setPersonNum("20335");
		personDetail.setPersonID("283");
		PersonDetail invalidPersonDetail = new PersonDetail();
		invalidPersonDetail.setPersonID("432");
		invalidPersonDetail.setPersonNum("34444444");
		List<PersonDetail> personDetailsList = new ArrayList<>();
		personDetailsList.add(personDetail);
		List<PersonDetail> inValidPersonDetailsList = new ArrayList<>();
		inValidPersonDetailsList.add(invalidPersonDetail);
		JSONObject joson = getJSONObject();
		mockedPersonality.when(() -> Personality.getByPersonNumber(personDetail.getPersonNum())).thenReturn(personality);
		Map<String , List<PersonDetail>> allEmployeeObjects = new HashMap<>();
		allEmployeeObjects.put(PersonalitySupportApiConstants.VALID_EMP_LIST, personDetailsList);
		allEmployeeObjects.put(PersonalitySupportApiConstants.INVALID_EMP_LIST, inValidPersonDetailsList);
		Mockito.when(cacheSupportApiUtils.getValidAndInvalidEmployeeIDs(Mockito.anyList())).thenReturn(allEmployeeObjects);
		personalityEvictRequestData.setPersonNumberList(personDetailsList);
		personalityEvictRequestDataList.add(personalityEvictRequestData);
		personalityCacheEvictReq.setTenantPersonalityRequestData(personalityEvictRequestDataList);
		PersonalityEvictResponseData response = cacheProcessor.cacheEvictExecutionFunction
				.apply(personalityEvictRequestDataList, "");
		assertNotNull(response);
	}
	

	@Test
	public void testInvalidateTenant() {
		assertThrows(APIException.class, () -> {
			PersonalityCacheEvictRequest personalityCacheEvictReq = new PersonalityCacheEvictRequest();
			List<PersonalityEvictRequestData> personalityEvictRequestDataList = new ArrayList<>();
			PersonalityEvictRequestData personalityEvictRequestData = new PersonalityEvictRequestData();
			personalityEvictRequestData.setTenantShortName("");
			personalityEvictRequestData.setEvictFrom(PersonalitySupportApiConstants.BOTH_LEGACY_AND_PERSON_CACHE);
			PersonDetail personDetail = new PersonDetail();
			personDetail.setPersonNum("20335");
			List<PersonDetail> personDetailsList = new ArrayList<>();
			personDetailsList.add(personDetail);
			JSONObject joson = getJSONObject();
			mockedPersonality.when(() -> Personality.getByPersonNumber(personDetail.getPersonNum())).thenReturn(personality);
			Mockito.doThrow(APIException.class).when(supportApiValidator).validateTenantShortName(Mockito.anyString());
			personalityEvictRequestData.setPersonNumberList(personDetailsList);
			personalityEvictRequestDataList.add(personalityEvictRequestData);
			personalityCacheEvictReq.setTenantPersonalityRequestData(personalityEvictRequestDataList);
			Response response = cacheProcessor.evictPersonCacheProcessor(personalityCacheEvictReq);
			assertNotNull(response);
		});
		
	}
	
	
	
	@Test
	public void testInvalidatePerson() {
		CacheEvictionProcessor processor = cacheProcessor;
		CacheEvictionProcessor spyProcessor = Mockito.spy(processor);
		List<PersonalityEvictResponseData> list1 = new ArrayList<>();
		Mockito.doReturn(list1).when(spyProcessor).getInvalidTenantListFromResponse(Mockito.any());
		PersonalityCacheEvictRequest personalityCacheEvictReq = new PersonalityCacheEvictRequest();
		List<PersonalityEvictRequestData> personalityEvictRequestDataList = new ArrayList<>();
		PersonalityEvictRequestData personalityEvictRequestData = new PersonalityEvictRequestData();
		personalityEvictRequestData.setTenantShortName("");
		personalityEvictRequestData.setEvictFrom(PersonalitySupportApiConstants.BOTH_LEGACY_AND_PERSON_CACHE);
		PersonDetail personDetail = new PersonDetail();
		personDetail.setPersonNum("20335");
		List<PersonDetail> personDetailsList = new ArrayList<>();
		personDetailsList.add(personDetail);
		JSONObject joson = getJSONObject();
		mockedPersonality.when(() -> Personality.getByPersonNumber(personDetail.getPersonNum())).thenReturn(personality);
		Mockito.doNothing().when(supportApiValidator).checkForDuplicateParametersInRequest(Mockito.anyList(), Mockito.anyString(), Mockito.anyString());
		Mockito.when(commonUtils.getRequestURI()).thenReturn("");
		Mockito.when(cacheSupportApiUtils.getStatusCode(Mockito.anyInt(), Mockito.anyInt())).thenReturn(200);
		Mockito.when(executor.execute(Mockito.any())).thenReturn(joson);
		personalityEvictRequestData.setPersonNumberList(personDetailsList);
		personalityEvictRequestDataList.add(personalityEvictRequestData);
		personalityCacheEvictReq.setTenantPersonalityRequestData(personalityEvictRequestDataList);
		Response response = spyProcessor.evictPersonCacheProcessor(personalityCacheEvictReq);
		assertEquals(200, response.getStatus());
	}
	
	
}
