package com.kronos.persons.rest.assignments.service;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.ProcessProfileAssignmentRequestBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.wfc.commonapp.people.business.person.AccessAssignment;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.commonapp.people.business.person.PersonLicenseType;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonNotification;
import com.kronos.wfc.commonapp.processmanager.business.profiles.workflow.WorkflowProfile;
import com.kronos.wfc.commonapp.processmanager.business.workflow.WorkflowAccessAssignment;
import com.kronos.wfc.platform.licensing.framework.LicenseType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * 
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonProcessProfileAssignmentServiceTest {

	@InjectMocks
	PersonProcessProfileAssignmentService service;

	@Mock
	PersonIdentityBeanValidator personIdentityBeanValidator;

	@Mock
	LicenseValidator licenseValidator;

	@Spy
	ValidatorUtils validatorUtils = new ValidatorUtils();

	@Mock
	LicenseType licenseType;

	@Mock
	PersonLicenseType plt;

	@Mock
	AccessAssignment accessAssignment;

	@Mock
	PersonProcessProfileAssignmentServiceHelper helper;

	@Mock
	WorkflowAccessAssignment workflowAccessAssignment;

	@Mock
	WorkflowProfile workflowProfile;

	@Mock
	private Person person;

	private MockedStatic<LicenseType> licenseTypeMockedStatic;
	private MockedStatic<Personality> personalityMockedStatic;
	private MockedStatic<Person> personMockedStatic;
	private MockedStatic<WorkflowProfile> workflowProfileMockedStatic;
	private MockedStatic<PersonNotification> personNotificationMockedStatic;

	@BeforeEach
	public void setUp() {
		licenseTypeMockedStatic = Mockito.mockStatic(LicenseType.class);
		personalityMockedStatic = Mockito.mockStatic(Personality.class);
		personMockedStatic = Mockito.mockStatic(Person.class);
		workflowProfileMockedStatic = Mockito.mockStatic(WorkflowProfile.class);
		personNotificationMockedStatic = Mockito.mockStatic(PersonNotification.class);
	}

	@AfterEach
	public void tearDown() {
		licenseTypeMockedStatic.close();
		personalityMockedStatic.close();
		personMockedStatic.close();
		workflowProfileMockedStatic.close();
		personNotificationMockedStatic.close();
		Mockito.clearAllCaches();
		Mockito.framework().clearInlineMocks();
	}

	@Test
	public void loadTest() {
		ObjectIdLong id = new ObjectIdLong(1L);
		Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
		PersonIdentityBean employee = new PersonIdentityBean();
		ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
		requestBean.setPersonIdentity(employee);
		requestBean.setEmployeeProcessProfileName("testProfile");
		requestBean.setManagerProcessProfileName("testProfile");

		Personality personality = Mockito.mock(Personality.class);
		Personality adminPersonality = Mockito.mock(Personality.class);
		Mockito.when(Personality.getByPersonId(id)).thenReturn(adminPersonality);
		Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");
		Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
		Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
		Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
		Mockito.when(personality.getNameData()).thenReturn(person);
		Mockito.when(personality.getAccessAssignment()).thenReturn(accessAssignment);
		Mockito.when(plt.getActive()).thenReturn(true);
		Mockito.when(helper.getWorkflowAccessAssignment(Mockito.any(Personality.class))).thenReturn(workflowAccessAssignment);
		Mockito.when(workflowAccessAssignment.getManagerWorkflowProfile()).thenReturn(workflowProfile);
		Mockito.when(workflowAccessAssignment.getEmployeeWorkflowProfile()).thenReturn(workflowProfile);
		ProcessProfileAssignmentRequestBean responseBean = service.load(employee);
		assertNotNull(responseBean);
	}

	@Test
	public void loadTestForAPIException1() {
		assertThrows(APIException.class, () -> {
			ProcessProfileAssignmentRequestBean responseBean = service.load(null);
			assertNotNull(responseBean);
		});
	}

	@Test
	public void loadTestForAPIException2() {
		assertThrows(APIException.class, () -> {
			ObjectIdLong id = new ObjectIdLong(1L);
			Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
			PersonIdentityBean employee = new PersonIdentityBean();
			ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
			requestBean.setPersonIdentity(employee);
			requestBean.setEmployeeProcessProfileName("testProfile");
			requestBean.setManagerProcessProfileName("testProfile");

			Personality personality = Mockito.mock(Personality.class);
			Personality adminPersonality = Mockito.mock(Personality.class);
			Mockito.when(Personality.getByPersonId(id)).thenReturn(adminPersonality);
			Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");
			Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
			Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
			Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
			Mockito.when(personality.getNameData()).thenReturn(person);
			Mockito.when(personality.getAccessAssignment()).thenReturn(accessAssignment);
			Mockito.when(plt.getActive()).thenReturn(true);
			Mockito.when(helper.getWorkflowAccessAssignment(Mockito.any(Personality.class))).thenReturn(null);
			ProcessProfileAssignmentRequestBean responseBean = service.load(employee);
			assertNotNull(responseBean);
		});
	}

	@Test
	public void loadTestForAPIException3() {
		assertThrows(APIException.class, () -> {
			ObjectIdLong id = new ObjectIdLong(1L);
			Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
			PersonIdentityBean employee = new PersonIdentityBean();
			ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
			requestBean.setPersonIdentity(employee);
			requestBean.setEmployeeProcessProfileName("testProfile");
			requestBean.setManagerProcessProfileName("testProfile");

			Personality personality = Mockito.mock(Personality.class);
			Personality adminPersonality = Mockito.mock(Personality.class);
			Mockito.when(Personality.getByPersonId(id)).thenReturn(adminPersonality);
			Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");
			Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
			Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
			Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
			Mockito.when(personality.getNameData()).thenReturn(person);
			Mockito.when(personality.getAccessAssignment()).thenReturn(accessAssignment);
			Mockito.when(plt.getActive()).thenReturn(true);
			Mockito.when(helper.getWorkflowAccessAssignment(Mockito.any(Personality.class))).thenReturn(workflowAccessAssignment);
			Mockito.when(workflowAccessAssignment.getManagerWorkflowProfile()).thenReturn(null);
			ProcessProfileAssignmentRequestBean responseBean = service.load(employee);
			assertNotNull(responseBean);
		});
	}

	@Test
	public void loadTestForAPIException4() {
		assertThrows(APIException.class, () -> {
			ObjectIdLong id = new ObjectIdLong(1L);
			Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
			PersonIdentityBean employee = new PersonIdentityBean();
			ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
			requestBean.setPersonIdentity(employee);
			requestBean.setEmployeeProcessProfileName("testProfile");
			requestBean.setManagerProcessProfileName("testProfile");

			Personality personality = Mockito.mock(Personality.class);
			Personality adminPersonality = Mockito.mock(Personality.class);
			Mockito.when(Personality.getByPersonId(id)).thenReturn(adminPersonality);
			Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");
			Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
			Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
			Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
			Mockito.when(personality.getNameData()).thenReturn(person);
			Mockito.when(personality.getAccessAssignment()).thenReturn(accessAssignment);
			Mockito.when(plt.getActive()).thenReturn(true);
			Mockito.when(helper.getWorkflowAccessAssignment(Mockito.any(Personality.class))).thenReturn(workflowAccessAssignment);
			Mockito.when(workflowAccessAssignment.getEmployeeWorkflowProfile()).thenReturn(null);
			Mockito.when(workflowAccessAssignment.getManagerWorkflowProfile()).thenReturn(workflowProfile);
			ProcessProfileAssignmentRequestBean responseBean = service.load(employee);
			assertNotNull(responseBean);
		});
	}

	@Test
	public void updateTest() throws Exception {
		ObjectIdLong id = new ObjectIdLong(1L);
		Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
		PersonIdentityBean employee = new PersonIdentityBean();
		ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
		requestBean.setPersonIdentity(employee);
		requestBean.setEmployeeProcessProfileName("testProfile");
		requestBean.setManagerProcessProfileName("testProfile");

		Personality personality = Mockito.mock(Personality.class);
		Personality adminPersonality = Mockito.mock(Personality.class);
		Mockito.when(Personality.getByPersonId(id)).thenReturn(adminPersonality);
		Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");
		Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
		Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
		Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
		Mockito.when(personality.getNameData()).thenReturn(person);
		Mockito.when(personality.getAccessAssignment()).thenReturn(accessAssignment);
		Mockito.when(plt.getActive()).thenReturn(true);
		Mockito.when(helper.getWorkflowAccessAssignment(Mockito.any(Personality.class))).thenReturn(workflowAccessAssignment);
		Mockito.when(workflowAccessAssignment.getPersonId()).thenReturn(new ObjectIdLong(1L));
		Mockito.when(workflowAccessAssignment.getManagerWorkflowProfile()).thenReturn(workflowProfile);
		Mockito.when(workflowAccessAssignment.getEmployeeWorkflowProfile()).thenReturn(workflowProfile);
		Mockito.when(WorkflowProfile.getWorkflowProfile(Mockito.anyString())).thenReturn(workflowProfile);

		Personality response = service.update(requestBean);
		assertNotNull(response);
	}

	@Test
	public void updateTestForException1() throws Exception {
		assertThrows(APIException.class, () -> {
			ObjectIdLong id = new ObjectIdLong(1L);
			Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
			PersonIdentityBean employee = new PersonIdentityBean();
			ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
			requestBean.setPersonIdentity(employee);
			requestBean.setEmployeeProcessProfileName("testProfile");
			requestBean.setManagerProcessProfileName("testProfile");

			Personality personality = Mockito.mock(Personality.class);
			Personality adminPersonality = Mockito.mock(Personality.class);
			Mockito.when(Personality.getByPersonId(id)).thenReturn(adminPersonality);
			Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");
			Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
			Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
			Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
			Mockito.when(personality.getNameData()).thenReturn(person);
			Mockito.when(personality.getAccessAssignment()).thenReturn(accessAssignment);
			Mockito.when(plt.getActive()).thenReturn(true);
			Mockito.when(helper.getWorkflowAccessAssignment(Mockito.any(Personality.class))).thenReturn(null);
			Personality response = service.update(requestBean);
			assertNotNull(response);
		});
	}

	@Test
	public void updateTestForException2() throws Exception {
		assertThrows(APIException.class, () -> {
			ObjectIdLong id = new ObjectIdLong(1L);
			Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
			PersonIdentityBean employee = new PersonIdentityBean();
			ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
			requestBean.setPersonIdentity(employee);
			requestBean.setEmployeeProcessProfileName(null);
			requestBean.setManagerProcessProfileName(null);

			Personality personality = Mockito.mock(Personality.class);
			Personality adminPersonality = Mockito.mock(Personality.class);
			Mockito.when(Personality.getByPersonId(id)).thenReturn(adminPersonality);
			Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");
			Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
			Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
			Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
			Mockito.when(personality.getNameData()).thenReturn(person);
			Mockito.when(personality.getAccessAssignment()).thenReturn(accessAssignment);
			Mockito.when(plt.getActive()).thenReturn(true);
			Mockito.when(helper.getWorkflowAccessAssignment(Mockito.any(Personality.class))).thenReturn(workflowAccessAssignment);
			Mockito.when(workflowAccessAssignment.getManagerWorkflowProfile()).thenReturn(workflowProfile);
			Mockito.when(workflowAccessAssignment.getEmployeeWorkflowProfile()).thenReturn(workflowProfile);
			Personality response = service.update(requestBean);
			assertNotNull(response);
		});
	}

	@Test
	public void updateTestForExistingEmployee() throws Exception {
		ObjectIdLong id = new ObjectIdLong(1L);
		Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);

		PersonIdentityBean employee = new PersonIdentityBean();
		ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
		requestBean.setPersonIdentity(employee);
		requestBean.setEmployeeProcessProfileName(null); // Employee profile is null
		requestBean.setManagerProcessProfileName("testProfile"); // Valid manager profile name

		Personality personality = Mockito.mock(Personality.class);
		Personality adminPersonality = Mockito.mock(Personality.class);
		Mockito.when(Personality.getByPersonId(id)).thenReturn(adminPersonality);
		Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");

		Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity()))
				.thenReturn(personality);

		Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
		Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
		Mockito.when(personality.getNameData()).thenReturn(person);
		Mockito.when(personality.getAccessAssignment()).thenReturn(accessAssignment);
		Mockito.when(plt.getActive()).thenReturn(true);

		Mockito.when(helper.getWorkflowAccessAssignment(Mockito.any(Personality.class)))
				.thenReturn(workflowAccessAssignment);

		Mockito.when(workflowAccessAssignment.getPersonId()).thenReturn(new ObjectIdLong(1L));
		Mockito.when(workflowAccessAssignment.getManagerWorkflowProfile()).thenReturn(workflowProfile);
		Mockito.when(workflowAccessAssignment.getEmployeeWorkflowProfile()).thenReturn(workflowProfile);

		workflowProfileMockedStatic.when(() -> WorkflowProfile.getWorkflowProfile((String) null))
					.thenReturn(workflowProfile);
		workflowProfileMockedStatic.when(() -> WorkflowProfile.getWorkflowProfile("testProfile"))
					.thenReturn(workflowProfile);
			Mockito.when(person.getPersonId()).thenReturn(id);
			Mockito.when(person.getPersonNumber()).thenReturn("12345");

			Personality response = service.update(requestBean);
			assertNotNull(response);
	}


	@Test
	public void updateTestForExistingManager() throws Exception {
		ObjectIdLong id = new ObjectIdLong(1L);
		Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);

		PersonIdentityBean employee = new PersonIdentityBean();
		ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
		requestBean.setPersonIdentity(employee);
		requestBean.setEmployeeProcessProfileName("testProfile");
		requestBean.setManagerProcessProfileName("managerProfile"); // Added valid manager profile name

		Personality personality = Mockito.mock(Personality.class);
		Personality adminPersonality = Mockito.mock(Personality.class);
		Mockito.when(Personality.getByPersonId(id)).thenReturn(adminPersonality);
		Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");

		Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity()))
				.thenReturn(personality);

		Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
		Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
		Mockito.when(personality.getNameData()).thenReturn(person);
		Mockito.when(personality.getAccessAssignment()).thenReturn(accessAssignment);
		Mockito.when(plt.getActive()).thenReturn(true);
		Mockito.when(helper.getWorkflowAccessAssignment(Mockito.any(Personality.class)))
				.thenReturn(workflowAccessAssignment);

		Mockito.when(workflowAccessAssignment.getPersonId()).thenReturn(new ObjectIdLong(1L));
		Mockito.when(workflowAccessAssignment.getManagerWorkflowProfile()).thenReturn(workflowProfile);
		Mockito.when(workflowAccessAssignment.getEmployeeWorkflowProfile()).thenReturn(workflowProfile);

		// Mocking both profiles to prevent APIException
		Mockito.when(WorkflowProfile.getWorkflowProfile("testProfile")).thenReturn(workflowProfile);
		Mockito.when(WorkflowProfile.getWorkflowProfile("managerProfile")).thenReturn(workflowProfile);

		Mockito.when(person.getPersonId()).thenReturn(id);
		Mockito.when(person.getPersonNumber()).thenReturn("12345");

		Personality response = service.update(requestBean);
		assertNotNull(response);
	}

	@Test
	public void updateTestForException4() throws Exception {
		assertThrows(APIException.class, () -> {
			ObjectIdLong id = new ObjectIdLong(1L);
			Mockito.when(Person.getByPersonId(Mockito.any())).thenReturn(person);
			PersonIdentityBean employee = new PersonIdentityBean();
			ProcessProfileAssignmentRequestBean requestBean = new ProcessProfileAssignmentRequestBean();
			requestBean.setPersonIdentity(employee);
			requestBean.setEmployeeProcessProfileName("testProfile");
			requestBean.setManagerProcessProfileName(null);

			Personality personality = Mockito.mock(Personality.class);
			Personality adminPersonality = Mockito.mock(Personality.class);
			Mockito.when(Personality.getByPersonId(id)).thenReturn(adminPersonality);
			Mockito.when(adminPersonality.getPersonNumber()).thenReturn("2");
			Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
			Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
			Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(plt);
			Mockito.when(personality.getNameData()).thenReturn(person);
			Mockito.when(personality.getAccessAssignment()).thenReturn(accessAssignment);
			Mockito.when(plt.getActive()).thenReturn(true);
			Mockito.when(helper.getWorkflowAccessAssignment(Mockito.any(Personality.class))).thenReturn(workflowAccessAssignment);
			Mockito.when(workflowAccessAssignment.getManagerWorkflowProfile()).thenReturn(null);
			Mockito.when(workflowAccessAssignment.getEmployeeWorkflowProfile()).thenReturn(workflowProfile);

			Personality response = service.update(requestBean);
			assertNotNull(response);
		});
	}
}
