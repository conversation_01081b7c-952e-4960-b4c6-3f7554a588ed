/*******************************************************************************
 * NewBulkBatchProcessorTest.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.utils;

import com.kronos.container.api.exception.APIException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

/**
 * The class to unit test methods of NewBulkBatchProcessor.
 *
 * <AUTHOR>
 */

@ExtendWith(MockitoExtension.class)
public class NewBulkBatchProcessorTest {

    @Mock
    private Function<Integer, List<String>> bulkRequestProcessor;

    private NewBulkBatchProcessor<Integer, String> newBulkBatchProcessor;

    @BeforeEach
    public void setUp() {
        Collection<Integer> requestDataCollection = Arrays.asList(1, 2);
        newBulkBatchProcessor = new NewBulkBatchProcessor<>(requestDataCollection, bulkRequestProcessor);
    }

    @Test
    public void testBulkProcessSuccess() {
        List<String> successData = new ArrayList<>();
        successData.add("success1");
        successData.add("success2");
        when(bulkRequestProcessor.apply(anyInt())).thenReturn(successData);
        List<String> result = newBulkBatchProcessor.bulkProcess();
        assertNotNull(result);
        assertEquals(4, result.size());
        assertTrue(result.contains("success1"));
        assertTrue(result.contains("success2"));
    }

    @Test
    public void testBulkProcessAllFail() {
        when(bulkRequestProcessor.apply(anyInt())).thenThrow(new APIException());
        assertThrows(APIException.class , newBulkBatchProcessor::bulkProcess);
    }

    @Test
    public void testBulkProcessWhenFailOnNoSuccessTrueThenReturnException() {
        newBulkBatchProcessor.setFailOnNoSucesses(true);
        List<String> successData = new ArrayList<>();
        successData.add("success 1");
        when(bulkRequestProcessor.apply(anyInt())).thenReturn(successData).thenThrow(new APIException());
        assertThrows(APIException.class , newBulkBatchProcessor::bulkProcess);
    }

    @Test
    public void testBulkProcessWhenFailOnNoSuccessFalseThenReturnException() {
        newBulkBatchProcessor.setFailOnNoSucesses(false);
        List<String> successData = new ArrayList<>();
        successData.add("success 1");
        when(bulkRequestProcessor.apply(anyInt())).thenReturn(successData).thenThrow(new APIException());
        assertThrows(APIException.class , newBulkBatchProcessor::bulkProcess);
    }

}
