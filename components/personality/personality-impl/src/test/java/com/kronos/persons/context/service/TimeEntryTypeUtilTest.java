/**
 * package to include test classes to unit test person service functionality.
 */
package com.kronos.persons.context.service;

import com.kronos.wfc.commonapp.people.business.person.AccessAssignment;
import com.kronos.wfc.commonapp.people.business.person.AssignTimeEntrySet;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.user.CurrentUserAccountManager;
import com.kronos.wfc.platform.businessobject.framework.BusinessProcessingException;
import com.kronos.wfc.platform.logging.framework.Log;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * The class to unit test methods of TimeEntryTypeUtil.
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class TimeEntryTypeUtilTest {

    private static final long DEFAULT_TIME_ENTRY_TYPE = AssignTimeEntrySet.TIME_ENTRY_PROJECT;

    private final TimeEntryTypeUtil targetClass = new TimeEntryTypeUtil();

    @Mock
    AccessAssignment accessAssignment;

    @Mock
    Personality personality;

    @Captor
    private ArgumentCaptor<String> argumentCaptor;

    private MockedStatic<CurrentUserAccountManager> mockedCurrentUserAccountManager;
    private MockedStatic<Log> mockedLog;

    @BeforeEach
    public void setUp() {
        Mockito.when(personality.getAccessAssignment()).thenReturn(accessAssignment);
        mockedCurrentUserAccountManager = Mockito.mockStatic(CurrentUserAccountManager.class);
        mockedCurrentUserAccountManager.when(CurrentUserAccountManager::getPersonality).thenReturn(personality);
        mockedLog = Mockito.mockStatic(Log.class);
    }

    @AfterEach
    public void tearDown() {
        if (mockedCurrentUserAccountManager != null) {
            mockedCurrentUserAccountManager.close();
        }
        if (mockedLog != null) {
            mockedLog.close();
        }
    }

    @Test
    public void givenNewDate_whenTimeEntryTypeIsNotNull_thenExpectTimeEntryTypeIsSetToDefault() {
        Date newDate = new Date();
        Mockito.when(accessAssignment.getTimeEntryTypeId(Mockito.any(KDate.class))).thenReturn(new ObjectIdLong(DEFAULT_TIME_ENTRY_TYPE));
        long timeEntryValue = targetClass.getTimeEntryType(newDate);
        assertEquals(DEFAULT_TIME_ENTRY_TYPE, timeEntryValue);
    }

    @Test
    public void givenNewDate_whenTimeEntryTypeIsNull_thenExpectTimeEntryTypeIsUnassigned() {
        Date newDate = new Date();
        Mockito.when(accessAssignment.getTimeEntryTypeId(Mockito.any(KDate.class))).thenReturn(null);
        long timeEntryValue = targetClass.getTimeEntryType(newDate);
        assertEquals(TimeEntryTypeUtil.TIME_ENTRY_UNASSIGNED, timeEntryValue);
    }

    @Test
    public void givenNewDate_whenGetTimeEntryMethodsThrowsException_thenExpectExceptionMessageInLogs() {
        Date newDate = new Date();
        String exceptionMessage = "unexpected exception";
        BusinessProcessingException businessProcessingException = new BusinessProcessingException(exceptionMessage);
        Mockito.when(accessAssignment.getEffectiveDatedTimeEntryMethods()).thenThrow(businessProcessingException);
        Mockito.when(accessAssignment.getTimeEntryTypeId(Mockito.any(KDate.class))).thenCallRealMethod();
        targetClass.getTimeEntryType(newDate);
        mockedLog.verify(() -> Log.log(Mockito.eq(Log.ERROR), argumentCaptor.capture()), Mockito.times(1));
        assertEquals(exceptionMessage, argumentCaptor.getValue());
    }
}
