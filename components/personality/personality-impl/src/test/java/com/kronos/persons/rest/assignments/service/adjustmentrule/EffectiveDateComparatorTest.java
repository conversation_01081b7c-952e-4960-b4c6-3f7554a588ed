/**
 * 
 */
package com.kronos.persons.rest.assignments.service.adjustmentrule;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import com.kronos.persons.rest.assignments.service.adjustmentrule.bean.ProcessorToEmployeeBean;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
public class EffectiveDateComparatorTest {
    
    @InjectMocks
    EffectiveDateComparator cmp;
    
    
    @Test
    public void compareFirtAgrNull(){
        int value = cmp.compare(null, new Object());
        assertEquals(1, value);
    }
    
    @Test
    public void compareSecondAgrNull(){
        int value = cmp.compare(new Object(), null);
        assertEquals(-1, value);
    }
    
    @Test
    public void compare(){
        ProcessorToEmployeeBean bean1 = new ProcessorToEmployeeBean();
        bean1.setEffectiveDate(KDate.createDate());
        ProcessorToEmployeeBean bean2= new ProcessorToEmployeeBean();
        bean2.setEffectiveDate(KDate.createDate());
        int value = cmp.compare(bean1, bean2);
        assertEquals(0, value);
    }

}
