/*******************************************************************************
 * RestPersonLeaveProfileAssignmentTest.java
 * Copyright © 2024 UKG Inc. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.rest.assignments.service;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.access.SpringContext;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.AssignmentProfileRequestBean;
import com.kronos.persons.rest.assignments.model.AssignmentProfileRequestBeanWrapper;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.PersistenceException;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;

/**
 * Verifies {@link RestPersonLeaveProfileAssignment}.
 * Copyright (C) 2019 Kronos.com
 * Date: Jun 25, 2019
 *
 * <AUTHOR> Kuchynski
 */
@ExtendWith(MockitoExtension.class)
public class RestPersonLeaveProfileAssignmentTest {

    private static final String ALL_RECORDS_FAILED_ERROR_CODE = "WCO-101271";
    private static final String EXCEPTION_MESSAGE_ERROR_CODE = "WCO-101299";
    private static final String INVALID_PROPERTY_VALUE_ERROR_CODE = "WCO-101232";
    private static final String PROPERTY_NAME_KEY = "propertyName";
    private static final String PROPERTY_VALUE_KEY = "propertyValue";
    private static final String REQUEST_DATA_PROPERTY_NAME = "RequestData";
    private static final String WCO_PREFIX = "WCO-";
    private static final int WCO_ERROR_CODE = 101500;

    @InjectMocks
    private RestPersonLeaveProfileAssignment restPersonLeaveAssignment;
    @Mock
    private PersonLeaveProfileAssignmentService leaveProfileAssignService;
    @Mock
    private PersonAssignmentHelper<AssignmentProfileRequestBean> personAssignmentHelper;

    private MockedStatic<KronosProperties> mockedKronosProperties;
    private MockedStatic<SpringContext> mockedSpringContext;

    @BeforeEach
    public void setUp() {
        mockedKronosProperties = Mockito.mockStatic(KronosProperties.class);
        mockedSpringContext = Mockito.mockStatic(SpringContext.class);
        mockedKronosProperties.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("4");
        mockedKronosProperties.when(() -> KronosProperties.getPropertyRaw(anyString(), anyString())).thenReturn("4");
    }

    @AfterEach
    public void tearDown() {
        mockedKronosProperties.close();
        mockedSpringContext.close();
    }


    @Test
    public void testMultiUpdate() {
        AssignmentProfileRequestBean requestBean = mock(AssignmentProfileRequestBean.class);
        List<AssignmentProfileRequestBean> requestDataList = Lists.newArrayList(requestBean);
        doNothing().when(leaveProfileAssignService).validate(any(AssignmentProfileRequestBeanWrapper.class));
        doNothing().when(leaveProfileAssignService).multiUpdate(anyList());
        List<AssignmentProfileRequestBean> result = restPersonLeaveAssignment.multiUpdate(requestDataList);
        assertEquals(requestDataList, result);
    }

    @Test
    public void testMultiUpdateEmptyRequest() {
        assertThrows(APIException.class, () -> {
        List<AssignmentProfileRequestBean> requestDataList = Lists.newArrayList();
        restPersonLeaveAssignment.multiUpdate(requestDataList);
        });
    }

    @Test
    public void testMultiUpdateRequestLengthExceedsAllowed() {
        assertThrows(APIException.class, () -> {
        mockedKronosProperties.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("2");
        List<AssignmentProfileRequestBean> requestDataList = ImmutableList.<AssignmentProfileRequestBean>builder()
                .add(mock(AssignmentProfileRequestBean.class))
                .add(mock(AssignmentProfileRequestBean.class))
                .add(mock(AssignmentProfileRequestBean.class))
                .add(mock(AssignmentProfileRequestBean.class)).build();
        restPersonLeaveAssignment.multiUpdate(requestDataList);
        });
    }
    
	@Test
	public void testUpdate() {
		Personality personality = mock(Personality.class);
		AssignmentProfileRequestBean requestBean = mock(AssignmentProfileRequestBean.class);

		Mockito.when(leaveProfileAssignService.updateRequest(requestBean)).thenReturn(personality);

		AssignmentProfileRequestBean result = restPersonLeaveAssignment.update(requestBean);
		assertEquals(requestBean, result);
	}
	
	@Test
	public void testUpdateException() {
        assertThrows(APIException.class, () -> {
		AssignmentProfileRequestBean requestBean = mock(AssignmentProfileRequestBean.class);
		Mockito.when(leaveProfileAssignService.updateRequest(requestBean)).thenThrow(new APIException("Error Occured"));
	
		restPersonLeaveAssignment.update(requestBean);
        });
	}

    @Test
    public void testGivenNullPersonNumber_WhenRetrieve_ThenThrowsAPIException(){
        APIException result = assertThrows(APIException.class, () -> restPersonLeaveAssignment.retrieve(null));
        assertEquals(INVALID_PROPERTY_VALUE_ERROR_CODE, result.getErrorCode());
        assertEquals(REQUEST_DATA_PROPERTY_NAME, result.getUserParameters().get(PROPERTY_NAME_KEY));
        assertNull(result.getUserParameters().get(PROPERTY_VALUE_KEY));
    }

    @Test
    public void testGivenPersonLeaveProfileAssignmentServiceThrowsPersistenceException_WhenRetrieve_ThenCatchAndThrowAPIException(){
        IKProperties ikPropertiesMock = mock(IKProperties.class);
        int persistenceExceptionSubType = 3;
        String expectedErrorCode = generateWCOErrorCode(persistenceExceptionSubType);
        mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(ikPropertiesMock);
        Mockito.when(leaveProfileAssignService.retrieve(any())).thenThrow(new PersistenceException(persistenceExceptionSubType));

        APIException result = assertThrows(APIException.class, () -> restPersonLeaveAssignment.retrieve("100"));
        assertEquals(expectedErrorCode, result.getErrorCode());
    }

    @Test
    public void testGivenNullPersonId_WhenRetrieveByPersonId_ThenThrowsAPIException(){
        APIException result = assertThrows(APIException.class, () -> restPersonLeaveAssignment.retrievebyPersonId(null));
        assertEquals(INVALID_PROPERTY_VALUE_ERROR_CODE, result.getErrorCode());
        assertEquals(REQUEST_DATA_PROPERTY_NAME, result.getUserParameters().get(PROPERTY_NAME_KEY));
        assertNull(result.getUserParameters().get(PROPERTY_VALUE_KEY));

    }

    @Test
    public void testGivenPersonLeaveProfileAssignmentServiceThrowsPersistenceException_WhenRetrieveByPersonId_ThenCatchAndThrowAPIException(){
        IKProperties ikPropertiesMock = mock(IKProperties.class);
        int persistenceExceptionSubType = 3;
        String expectedErrorCode = generateWCOErrorCode(persistenceExceptionSubType);
        mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(ikPropertiesMock);
        Mockito.when(leaveProfileAssignService.retrieve(any())).thenThrow(new PersistenceException(persistenceExceptionSubType));

        APIException result = assertThrows(APIException.class, () -> restPersonLeaveAssignment.retrievebyPersonId(1L));
        assertEquals(expectedErrorCode, result.getErrorCode());
    }

    @Test
    public void testWhenRetrieveListIsExecuted_ThenCallsItsDependencyToCreateListOfAssignmentProfile(){
        ExtensionSearchCriteria input = new ExtensionSearchCriteria();

        restPersonLeaveAssignment.retrieveList(input);

        Mockito.verify(personAssignmentHelper).getPersonAssignmentList(Mockito.eq(input), any());
    }

    @Test
    public void testGivenNullPointerExceptionIsThrownBecauseRequestBeanHasNullPersonIdentity_WhenUpdate_ThenCatchAndThrowAPIException(){
        AssignmentProfileRequestBean input = new AssignmentProfileRequestBean();
        Mockito.when(leaveProfileAssignService.updateRequest(input)).thenThrow(new NullPointerException());

        APIException result = assertThrows(APIException.class, () -> restPersonLeaveAssignment.update(input));
        assertEquals(EXCEPTION_MESSAGE_ERROR_CODE, result.getErrorCode());
    }

    @Test
    public void testGivenPersonLeaveProfileAssignmentServiceThrowsPersistenceException_WhenUpdate_ThenCatchAndThrowAPIException(){
        IKProperties ikPropertiesMock = mock(IKProperties.class);
        mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(ikPropertiesMock);
        int persistenceExceptionSubType = 6;
        String expectedErrorCode = generateWCOErrorCode(persistenceExceptionSubType);
        AssignmentProfileRequestBean input = new AssignmentProfileRequestBean();
        Mockito.when(leaveProfileAssignService.updateRequest(input)).thenThrow(new PersistenceException(persistenceExceptionSubType));

        APIException result = assertThrows(APIException.class, () -> restPersonLeaveAssignment.update(input));
        assertEquals(expectedErrorCode, result.getErrorCode());
    }

    @Test
    public void testGivenNullAssignmentProfileRequestBean_WhenDelete_ThenThrowAPIException(){
        APIException result = assertThrows(APIException.class, () -> restPersonLeaveAssignment.delete(null));
        assertEquals(INVALID_PROPERTY_VALUE_ERROR_CODE, result.getErrorCode());
        assertEquals(REQUEST_DATA_PROPERTY_NAME, result.getUserParameters().get(PROPERTY_NAME_KEY));
        assertNull(result.getUserParameters().get(PROPERTY_VALUE_KEY));
    }

    @Test
    public void testGivenPersonLeaveProfileAssignmentServiceThrowsPersistenceException_WhenDelete_ThenCatchAndThrowAPIException(){
        IKProperties ikPropertiesMock = mock(IKProperties.class);
        mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(ikPropertiesMock);
        int persistenceExceptionSubType = 7;
        String expectedErrorCode = generateWCOErrorCode(persistenceExceptionSubType);
        AssignmentProfileRequestBean input = new AssignmentProfileRequestBean();
        Mockito.when(leaveProfileAssignService.deleteRequest(input)).thenThrow(new PersistenceException(persistenceExceptionSubType));

        APIException result = assertThrows(APIException.class, () -> restPersonLeaveAssignment.delete(input));
        assertEquals(expectedErrorCode, result.getErrorCode());
    }

    @Test
    public void testGivenPersonLeaveProfileAssignmentServiceThrowsNullPointerException_WhenDelete_ThenCatchAndThrowAPIException(){
        AssignmentProfileRequestBean input = new AssignmentProfileRequestBean();
        Mockito.when(leaveProfileAssignService.deleteRequest(input)).thenThrow(new NullPointerException());

        APIException result = assertThrows(APIException.class, () -> restPersonLeaveAssignment.delete(input));
        assertEquals(EXCEPTION_MESSAGE_ERROR_CODE, result.getErrorCode());
    }

    @Test
    public void testGivenListWith2RequestBeansAsInput_WhenMultiDelete_ThenShouldCallDependency2Times(){
        AssignmentProfileRequestBean input = new AssignmentProfileRequestBean();
        List<AssignmentProfileRequestBean> inputList = Arrays.asList(input, input);

        restPersonLeaveAssignment.multiDelete(inputList);

        Mockito.verify(leaveProfileAssignService, Mockito.times(2)).deleteRequest(input);
    }

    @Test
    public void testGivenPersonLeaveProfileAssignmentServiceThrowsAPIException_WhenMultiDeleteExecutesBatchProcessor_ThenCatchAndThrowAPIException(){
        AssignmentProfileRequestBean input = new AssignmentProfileRequestBean();
        List<AssignmentProfileRequestBean> inputList = Collections.singletonList(input);
        Mockito.when(leaveProfileAssignService.deleteRequest(input)).thenThrow(new APIException());

        APIException result = assertThrows(APIException.class, () -> restPersonLeaveAssignment.multiDelete(inputList));
        assertEquals(ALL_RECORDS_FAILED_ERROR_CODE, result.getErrorCode());
    }

    @Test
    public void testGivenPersonLeaveProfileAssignmentServiceThrowsNullPointerException_WhenMultiDeleteExecutesBatchProcessor_ThenCatchAndThrowAPIException(){
        AssignmentProfileRequestBean input = new AssignmentProfileRequestBean();
        List<AssignmentProfileRequestBean> inputList = Collections.singletonList(input);
        Mockito.when(leaveProfileAssignService.deleteRequest(input)).thenThrow(new NullPointerException());

        APIException result = assertThrows(APIException.class, () -> restPersonLeaveAssignment.multiDelete(inputList));
        assertEquals(ALL_RECORDS_FAILED_ERROR_CODE, result.getErrorCode());
    }

    @Test
    public void testGivenPersonLeaveProfileAssignmentServiceThrowsPersistenceException_WhenMultiDeleteExecutesBatchProcessor_ThenCatchAndThrowAPIException(){
        IKProperties ikPropertiesMock = mock(IKProperties.class);
        mockedSpringContext.when(() -> SpringContext.getBean(IKProperties.class)).thenReturn(ikPropertiesMock);
        AssignmentProfileRequestBean input = new AssignmentProfileRequestBean();
        List<AssignmentProfileRequestBean> inputList = Collections.singletonList(input);
        Mockito.when(leaveProfileAssignService.deleteRequest(input)).thenThrow(new PersistenceException(7));

        APIException result = assertThrows(APIException.class, () -> restPersonLeaveAssignment.multiDelete(inputList));
        assertEquals(ALL_RECORDS_FAILED_ERROR_CODE, result.getErrorCode());
    }

    private String generateWCOErrorCode(int subType){
        return WCO_PREFIX + (WCO_ERROR_CODE + subType);
    }
}
