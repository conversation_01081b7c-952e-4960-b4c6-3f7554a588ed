package com.kronos.persons.rest.assignments.service.brazilcompliance;


import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.people.personality.model.brazilcompliance.BrazilAssignmentDTO;
import com.kronos.people.personality.model.brazilcompliance.BrazilAssignmentRepTypeDTO;
import com.kronos.people.personality.model.brazilcompliance.BrazilEmployeeDTO;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentCompanyDetailRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentPcaDetailRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilAssignmentRepTypeDetailRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.BrazilEmployeeAssignmentsRest;
import com.kronos.persons.rest.assignments.model.brazilcompliance.CompanyAttributesRest;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.timekeeping.brazil.compliance.setup.api.model.CompanyDTO;
import com.kronos.timekeeping.brazil.compliance.setup.api.model.PaycodeAttributeDefinitionDTO;
import com.kronos.timekeeping.brazil.compliance.setup.api.service.ICompanyService;
import com.kronos.timekeeping.brazil.compliance.setup.api.service.IPaycodeAttributeDefService;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BRCAssignment;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BRCAssignmentSet;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BRCEmployee;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BrazilAttributeNameUtil;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.pca.BRCAssignmentPca;
import com.kronos.wfc.commonapp.people.business.person.brazilcompliance.reptype.BRCAssignmentRepType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.platform.utility.framework.datetime.KDateTime;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BRCConfigType.BRC_PAYCODE_ATTRIBUTE;
import static com.kronos.wfc.commonapp.people.business.person.brazilcompliance.BRCConfigType.BRC_REPTYPE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class BrazilAssignmentBOConverterTest {
    private static final Long PERSONID = 243L;
    private static final String PERSON_NUMBER = "20335";
    private static final Long COMPANYID = 33333L;
    private static final String COMPANYNAME = "COMPANY-33333";
    private static final Long PCAID = 44444L;
    private static final String PCANAME = "PCA-44444";
    private static final Long REPTYPEID = 1L;
    private static final String REPTYPENAME = "REP-A";

    private static final String UNION_AGREEMENT_NUMBER = "2222222";
    private static final ObjectRef REP_TYPE_REF = new ObjectRef(REPTYPEID, REPTYPENAME);
    private static final ObjectRef COMPANY_REF = new ObjectRef(COMPANYID, COMPANYNAME);
    private static final ObjectRef PCA_REF = new ObjectRef(PCAID, PCANAME);
    private static final String PIS = "11111111";
    private static final String ESOCIAL = "eSocial";
    private static final String CPF = "111111";
    private static final String IDENTIFIRE = "IDENTIFIRE";
    private static final String LOCATION = "LOCATION";
    private static final String CEI = "CEI";
    private static final String CAEPF = "CAEPF";
    private static final String CNO = "CNO";
    private static String DEVICE_GROUP_1 = "DEVICE_GROUP_1";

    private final LocalDateTime EFFECTIVE_DATE = LocalDateTime.now().minusYears(10);

    @Mock
    BrazilAttributeNameUtil brazilAttributeNameUtil;

    @Mock
    ICompanyService companyService;
    @Mock
    private IPaycodeAttributeDefService paycodeAttributeDefService;
    @Mock
    private AdapterHelper adapterHelper;
    @Mock
    KDateTime kDateTime;
    @InjectMocks
    BrazilAssignmentBOConverter brazilAssignmentBOConverter;


    @Test
    public void testConvertDTOToBO() {
        when(companyService.findCompanyById(COMPANYID.longValue())).thenReturn(getCompanyDTO(COMPANY_REF));
        when(brazilAttributeNameUtil.getRepTypeObjectRefById(REP_TYPE_REF.getId())).thenReturn(REP_TYPE_REF);
        when(brazilAttributeNameUtil.getPaycodeAttributeObjectRefById(PCA_REF.getId())).thenReturn(PCA_REF);

        BrazilEmployeeAssignmentsRest employeeBO = brazilAssignmentBOConverter.convertDTOToBO(createBrazilEmployeeDTO(), false);
        assertEquals(PERSONID, employeeBO.getPersonIdentity().getPersonKey());
        assertEquals(CPF, employeeBO.getCpf());
        assertEquals(PIS, employeeBO.getPis());
        assertEquals(ESOCIAL, employeeBO.getEsocial());

        List<BrazilAssignmentCompanyDetailRest> companyAssignments =employeeBO.getCompanyAssignments().getAssignmentDetails();
        assertEquals(1, companyAssignments.size());
        CompanyAttributesRest companyAttributes =companyAssignments.get(0).getCompany();
        assertEquals(COMPANY_REF.getId(), companyAttributes.getId());
        assertEquals(COMPANY_REF.getQualifier(), companyAttributes.getQualifier());
        assertEquals(EFFECTIVE_DATE.toLocalDate(), employeeBO.getCompanyAssignments().getAssignmentDetails().get(0).getEffectiveDate());
        assertNull(companyAttributes.getCaepf());
        assertNull(companyAttributes.getCei());
        assertNull(companyAttributes.getCno());
        assertNull(companyAttributes.getIdentifier());
        assertNull(companyAttributes.getLocation());
        assertNull(companyAttributes.getDeviceGroup());

        assertEquals(1, employeeBO.getPcaAssignments().getAssignmentDetails().size());
        assertEquals(PCA_REF.getId(), employeeBO.getPcaAssignments().getAssignmentDetails().get(0).getPayCodeAttribute().getId());
        assertEquals(PCA_REF.getQualifier(), employeeBO.getPcaAssignments().getAssignmentDetails().get(0).getPayCodeAttribute().getQualifier());
        assertEquals(EFFECTIVE_DATE.toLocalDate(), employeeBO.getPcaAssignments().getAssignmentDetails().get(0).getEffectiveDate());

        assertEquals(1, employeeBO.getRepTypeAssignments().getAssignmentDetails().size());
        assertEquals(REP_TYPE_REF.getId(), employeeBO.getRepTypeAssignments().getAssignmentDetails().get(0).getRepType().getId());
        assertEquals(REP_TYPE_REF.getQualifier(), employeeBO.getRepTypeAssignments().getAssignmentDetails().get(0).getRepType().getQualifier());
        assertEquals(EFFECTIVE_DATE.toLocalDate(), employeeBO.getRepTypeAssignments().getAssignmentDetails().get(0).getEffectiveDate());

        // Test the additional company attributes
        employeeBO = brazilAssignmentBOConverter.convertDTOToBO(createBrazilEmployeeDTO(), true);
        companyAssignments = employeeBO.getCompanyAssignments().getAssignmentDetails();
        assertEquals(1, companyAssignments.size());
        companyAttributes = companyAssignments.get(0).getCompany();
        assertEquals(COMPANY_REF.getId(), companyAttributes.getId());
        assertEquals(COMPANY_REF.getQualifier(), companyAttributes.getQualifier());
        assertEquals(EFFECTIVE_DATE.toLocalDate(), employeeBO.getCompanyAssignments().getAssignmentDetails().get(0).getEffectiveDate());
        assertEquals("CAEPF", companyAttributes.getCaepf());
        assertEquals("CEI", companyAttributes.getCei());
        assertEquals("CNO", companyAttributes.getCno());
        assertEquals("IDENTIFIRE", companyAttributes.getIdentifier());
        assertEquals("LOCATION", companyAttributes.getLocation());
        assertNotNull(companyAttributes.getDeviceGroup());
    }

    private CompanyDTO getCompanyDTO(ObjectRef companyRef) {
        CompanyDTO company = new CompanyDTO();
        company.setCompanyId(companyRef.getId());
        company.setCompanyName(companyRef.getQualifier());
        company.setCaepf(CAEPF);
        company.setCno(CNO);
        company.setCei(CEI);
        company.setDeviceGroup(new ObjectRef(1L, DEVICE_GROUP_1));
        company.setLocation(LOCATION);
        company.setEmployerIdentifier(IDENTIFIRE);
        return company;
    }

    @Test
    public void testConvertDTOToBOWhenNoData() {
        BrazilEmployeeDTO employeeDto = new BrazilEmployeeDTO();
        employeeDto.setEmployeeId(PERSONID);

        BrazilEmployeeAssignmentsRest employeeBO = brazilAssignmentBOConverter.convertDTOToBO(employeeDto,false);
        assertEquals(PERSONID, employeeBO.getPersonIdentity().getPersonKey());
        assertNull(employeeBO.getCpf());
        assertNull(employeeBO.getPis());
        assertNull(employeeBO.getEsocial());

        assertEquals(0, employeeBO.getCompanyAssignments().getAssignmentDetails().size());
        assertEquals(0, employeeBO.getPcaAssignments().getAssignmentDetails().size());
        assertEquals(0, employeeBO.getRepTypeAssignments().getAssignmentDetails().size());
    }

    @Test
    public void testConvertPcaBOToPersistenceObj() {
        PaycodeAttributeDefinitionDTO pcaDto = new PaycodeAttributeDefinitionDTO();
        pcaDto.setPaycodeAttributeDefId(PCAID);
        when(paycodeAttributeDefService.findPaycodeAttributeDefinitionByName(PCANAME)).thenReturn(pcaDto);
        BrazilAssignmentPcaDetailRest pca = new BrazilAssignmentPcaDetailRest();
        pca.setPayCodeAttribute(new ObjectRef(PCANAME));
        pca.setEffectiveDate(EFFECTIVE_DATE.toLocalDate());
        BRCAssignment assignmentResult = brazilAssignmentBOConverter.convertPcaBOToPersistenceObj(new ObjectIdLong(PERSONID), pca);
        assertEquals(PCAID.longValue(), ((BRCAssignmentPca)assignmentResult).getPcaId().get().longValue());
        assertEquals(PERSONID.longValue(), assignmentResult.getPersonId().longValue());
        verify(adapterHelper).localDateTimeToKdateTime(any());
    }

    @Test
    public void testConvertEmployeePersistenceObjToBOPca() {
        PaycodeAttributeDefinitionDTO pcaDto = new PaycodeAttributeDefinitionDTO();
        pcaDto.setPaycodeAttributeDefId(PCAID);
        pcaDto.setPaycodeAttributeDefName(PCANAME);
        when(adapterHelper.kDateToLocalDate(any())).thenReturn(EFFECTIVE_DATE.toLocalDate());
        when(paycodeAttributeDefService.findPaycodeAttributeDefinitionById(PCAID)).thenReturn(pcaDto);
        BRCEmployee brcEmployee = new BRCEmployee(PERSONID);
        brcEmployee.setPcaSet(getPCASet());
        BrazilEmployeeAssignmentsRest brazilEmployeeAssignmentsRest = brazilAssignmentBOConverter.convertEmployeePersistenceObjToBO(brcEmployee, new PersonIdentityBean());
        assertEquals(3L, brazilEmployeeAssignmentsRest.getPcaAssignments().getAssignmentDetails().stream().filter(assignments -> assignments.getPayCodeAttribute().getId().equals(PCAID)).count());
        assertEquals(3L, brazilEmployeeAssignmentsRest.getPcaAssignments().getAssignmentDetails().stream().filter(assignments -> assignments.getPayCodeAttribute().getQualifier().equals(PCANAME)).count());
        assertEquals(3L, brazilEmployeeAssignmentsRest.getPcaAssignments().getAssignmentDetails().stream().filter(assignments -> assignments.getEffectiveDate().isEqual(EFFECTIVE_DATE.toLocalDate())).count());
    }

    @Test
    public void testConvertRepTypeBOToPersistenceObj() {
        BrazilAssignmentRepTypeDetailRest repType = new BrazilAssignmentRepTypeDetailRest();
        repType.setRepType(new ObjectRef(REPTYPENAME));
        repType.setEffectiveDate(EFFECTIVE_DATE.toLocalDate());
        repType.setUnionAgreementNumber(UNION_AGREEMENT_NUMBER);
        BRCAssignment assignmentResult = brazilAssignmentBOConverter.convertRepTypeBOToPersistenceObj(new ObjectIdLong(PERSONID), repType);
        assertEquals(REPTYPEID.longValue(), ((BRCAssignmentRepType)assignmentResult).getRepTypeId().get().longValue());
        assertEquals(PERSONID.longValue(), assignmentResult.getPersonId().longValue());
        assertEquals(UNION_AGREEMENT_NUMBER, ((BRCAssignmentRepType)assignmentResult).getUnionAgreementNumber());
        verify(adapterHelper).localDateTimeToKdateTime(any());
    }

    @Test
    public void testConvertEmployeePersistenceObjToBORepType() {
        when(adapterHelper.kDateToLocalDate(any())).thenReturn(EFFECTIVE_DATE.toLocalDate());
        BRCEmployee brcEmployee = new BRCEmployee(PERSONID);
        brcEmployee.setRepTypeSet(getRepTypeSet());
        BrazilEmployeeAssignmentsRest brazilEmployeeAssignmentsRest = brazilAssignmentBOConverter.convertEmployeePersistenceObjToBO(brcEmployee, new PersonIdentityBean());
        assertEquals(3L, brazilEmployeeAssignmentsRest.getRepTypeAssignments().getAssignmentDetails().stream().filter(assignments -> assignments.getRepType().getId().equals(REPTYPEID)).count());
        assertEquals(3L, brazilEmployeeAssignmentsRest.getRepTypeAssignments().getAssignmentDetails().stream().filter(assignments -> assignments.getRepType().getQualifier().equals(REPTYPENAME)).count());
        assertEquals(3L, brazilEmployeeAssignmentsRest.getRepTypeAssignments().getAssignmentDetails().stream().filter(assignments -> assignments.getEffectiveDate().isEqual(EFFECTIVE_DATE.toLocalDate())).count());
        assertEquals(3L, brazilEmployeeAssignmentsRest.getRepTypeAssignments().getAssignmentDetails().stream().filter(assignments -> assignments.getUnionAgreementNumber().equals(UNION_AGREEMENT_NUMBER)).count());
    }

    /**
     * Create Mock DTO
     **/

    private BrazilEmployeeDTO createBrazilEmployeeDTO() {
        BrazilEmployeeDTO brazilEmployeeDTO = new BrazilEmployeeDTO();
        brazilEmployeeDTO.setEmployeeId(PERSONID);
        brazilEmployeeDTO.setPis(PIS);
        brazilEmployeeDTO.seteSocial(ESOCIAL);
        brazilEmployeeDTO.setCpf(CPF);
        // Company
        brazilEmployeeDTO.setCompanyAssignments(createBrazilAssignmentDetailDTOForCompany());

        // PCA
        brazilEmployeeDTO.setPcaAssignments(createBrazilAssignmentDetailDTOForPca());

        // Rep Type
        brazilEmployeeDTO.setRepTypeAssignments(createBrazilAssignmentDetailDTOForRepType());

        return brazilEmployeeDTO;
    }

    private List<BrazilAssignmentDTO> createBrazilAssignmentDetailDTOForCompany() {
        List<BrazilAssignmentDTO> list = new ArrayList<>();
        BrazilAssignmentDTO dto = new BrazilAssignmentDTO();
        dto.setAttributeId(COMPANY_REF.getId());
        dto.setPersonId(PERSONID);
        dto.setEffectiveDate(EFFECTIVE_DATE.toLocalDate());
        list.add(dto);
        return list;
    }

    private List<BrazilAssignmentDTO> createBrazilAssignmentDetailDTOForPca() {
        List<BrazilAssignmentDTO> list = new ArrayList<>();
        BrazilAssignmentDTO dto = new BrazilAssignmentDTO();
        dto.setAttributeId(PCA_REF.getId());
        dto.setPersonId(PERSONID);
        dto.setEffectiveDate(EFFECTIVE_DATE.toLocalDate());
        list.add(dto);
        return list;
    }

    private List<BrazilAssignmentDTO> createBrazilAssignmentDetailDTOForRepType() {
        List<BrazilAssignmentDTO> list = new ArrayList<>();
        BrazilAssignmentRepTypeDTO dto = new BrazilAssignmentRepTypeDTO();
        dto.setAttributeId(REP_TYPE_REF.getId());
        dto.setPersonId(PERSONID);
        dto.setUnionAgreementNumber(UNION_AGREEMENT_NUMBER);
        dto.setEffectiveDate(EFFECTIVE_DATE.toLocalDate());
        list.add(dto);
        return list;
    }

    private BRCAssignmentSet getPCASet() {
        List<BRCAssignment> pcaList = new ArrayList<>();
        for (int i = 0; i < 3; ++i) {
            BRCAssignmentPca pca = new BRCAssignmentPca(new ObjectIdLong(PERSONID));
            pca.setPcaId(PCAID);
            pca.setEffectiveDate(KDate.getNewSotDate());
            pca.setObjectId(new ObjectIdLong(10 + i));
            pcaList.add(pca);
        }
        return new BRCAssignmentSet(BRC_PAYCODE_ATTRIBUTE, pcaList, new ObjectIdLong(PERSONID.toString()));
    }

    private BRCAssignmentSet getRepTypeSet() {
        List<BRCAssignment> repTypeList = new ArrayList<>();
        for (int i = 0; i < 3; ++i) {
            BRCAssignmentRepType repType = new BRCAssignmentRepType(new ObjectIdLong(PERSONID));
            repType.setRepTypeId(REPTYPEID);
            repType.setEffectiveDate(KDate.getNewSotDate());
            repType.setUnionAgreementNumber(UNION_AGREEMENT_NUMBER);
            repType.setObjectId(new ObjectIdLong(10 + i));
            repTypeList.add(repType);
        }
        return new BRCAssignmentSet(BRC_REPTYPE, repTypeList, new ObjectIdLong(PERSONID.toString()));
    }
}