/*******************************************************************************
 * PersonProcessProfileAssignmentServiceHelperTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.rest.assignments.service;

import com.kronos.wfc.commonapp.people.business.person.AccessAssignment;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.processmanager.business.workflow.WorkflowAccessAssignment;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedConstruction;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonProcessProfileAssignmentServiceHelperTest {

    private final PersonProcessProfileAssignmentServiceHelper personProcessProfileAssignmentServiceHelper = new PersonProcessProfileAssignmentServiceHelper();

    @Test
    public void testGetWorkflowAccessAssignment() throws Exception {
        Personality personality = new Personality();
        ObjectIdLong id = new ObjectIdLong(1L);
        AccessAssignment assignment = new AccessAssignment();
        assignment.setAccessProfileId(id);
        assignment.setPreferenceProfileId(id);
        personality.setAccessAssignment(assignment);

        try (MockedConstruction<WorkflowAccessAssignment> mocked = Mockito.mockConstruction(WorkflowAccessAssignment.class,
                (mock, context) -> {
                    Mockito.doNothing().when(mock).refresh();
                })) {
            WorkflowAccessAssignment result = personProcessProfileAssignmentServiceHelper.getWorkflowAccessAssignment(personality);
            WorkflowAccessAssignment workflowAccessAssignment = mocked.constructed().get(0);
            assertEquals(workflowAccessAssignment, result);
            Mockito.verify(workflowAccessAssignment).refresh();
        }
    }
}