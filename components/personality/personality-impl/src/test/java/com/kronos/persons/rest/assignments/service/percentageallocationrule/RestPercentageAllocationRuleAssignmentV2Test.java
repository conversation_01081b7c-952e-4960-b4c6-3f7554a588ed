package com.kronos.persons.rest.assignments.service.percentageallocationrule;

import com.google.common.collect.Lists;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.percentageallocationrule.PercentageAllocationRuleAssignmentBean;
import com.kronos.persons.rest.assignments.model.percentageallocationrule.PercentageAllocationRuleCriteriaBean;
import com.kronos.persons.rest.assignments.service.PersonAssignmentHelper;
import com.kronos.persons.rest.assignments.service.adjustmentrule.bean.AssignmentBeanHolder;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.model.EmployeeCriteria;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.rest.model.ExtensionWhereCriteria;
import com.kronos.wfc.commonapp.people.business.person.PersonLicenseTypeSet;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.user.CurrentUserAccountManager;
import com.kronos.wfc.platform.datetime.framework.factory.TimeFactory;
import com.kronos.wfc.platform.datetime.framework.factory.TimeFactoryIfc;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import com.kronos.wfc.totalizing.business.extensibility.ProcessorToEmployee;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.mock.web.MockHttpServletResponse;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RestPercentageAllocationRuleAssignmentV2Test {

    @InjectMocks
    RestPercentageAllocationRuleAssignmentV2 restPercentageAllocationRuleAssignment;

    @Mock
    PercentageAllocationRuleAssignmentService assignmentService;

    @Mock
    Personality personality;

    @Mock
    PersonLicenseTypeSet personLicenseTypeSet;

    @Mock
    HttpServletResponse httpResponse;

    @Mock
    PersonAssignmentHelper<PercentageAllocationRuleAssignmentBean> personAssignmentHelper;

    @Mock
    ProcessorToEmployee processorToEmployee;

    private MockedStatic<KronosProperties> kronosPropertiesMockedStatic;
    private MockedStatic<CurrentUserAccountManager> currentUserAccountManagerMockedStatic;
    private MockedStatic<TimeFactory> timeFactoryMockedStatic;

    @BeforeEach
    public void setupManagerDefaultManager() {
        kronosPropertiesMockedStatic = mockStatic(KronosProperties.class);
        kronosPropertiesMockedStatic.when(() -> KronosProperties.getPropertyRaw(anyString(), anyString())).thenReturn("4");
        currentUserAccountManagerMockedStatic = mockStatic(CurrentUserAccountManager.class);
        currentUserAccountManagerMockedStatic.when(CurrentUserAccountManager::getPersonality).thenReturn(personality);
        timeFactoryMockedStatic = mockStatic(TimeFactory.class);
        TimeFactoryIfc timeFactoryIfc = mock(TimeFactoryIfc.class);
        timeFactoryMockedStatic.when(TimeFactory::getFactory).thenReturn(timeFactoryIfc);
        when(timeFactoryIfc.getProperty(eq("util.datetime.durationConvertUsesTable"),eq("true"))).thenReturn("false");

        Mockito.when(personality.getLicenseTypes()).thenReturn(personLicenseTypeSet);
        Mockito.when(personLicenseTypeSet.hasManagerLicense()).thenReturn(true);
    }

    @AfterEach
    public void closeMocks() {
        kronosPropertiesMockedStatic.close();
        currentUserAccountManagerMockedStatic.close();
        timeFactoryMockedStatic.close();
    }

    @Test
    public void assignPercentageAllocationRule() {
        PercentageAllocationRuleAssignmentBean requestBean = new PercentageAllocationRuleAssignmentBean();
        Personality personality = mock(Personality.class);
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(assignmentService.assignPercentageAllocationRule(requestBean)).thenReturn(personality);
        PercentageAllocationRuleAssignmentBean responseBean = restPercentageAllocationRuleAssignment.assignPercentageAllocationRule(requestBean);
        assertNotNull(responseBean);
        assertEquals(responseBean,requestBean);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void assignPercentageAllocationRuleException() {

        PercentageAllocationRuleAssignmentBean requestBean = new PercentageAllocationRuleAssignmentBean();
        Mockito.when(assignmentService.assignPercentageAllocationRule(Mockito.any())).thenThrow(RuntimeException.class);

        assertThrows(APIException.class, () -> {
            restPercentageAllocationRuleAssignment.assignPercentageAllocationRule(requestBean);
        });
    }

    @Test
    public void updatePercentageAllocationRuleAssignment() {
        PercentageAllocationRuleAssignmentBean requestBean = new PercentageAllocationRuleAssignmentBean();
        Personality personality = mock(Personality.class);
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(assignmentService.updatePercentageAllocationRuleAssignment(requestBean)).thenReturn(personality);
        PercentageAllocationRuleAssignmentBean responseBean = restPercentageAllocationRuleAssignment.updatePercentageAllocationRuleAssignment(requestBean);
        assertNotNull(responseBean);
        assertEquals(responseBean,requestBean);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void updatePercentageAllocationRuleAssignmentException() {
        Mockito.when(assignmentService.updatePercentageAllocationRuleAssignment(Mockito.any())).thenThrow(RuntimeException.class);
        PercentageAllocationRuleAssignmentBean requestBean = mock(PercentageAllocationRuleAssignmentBean.class);

        assertThrows(APIException.class, () -> {
            restPercentageAllocationRuleAssignment.updatePercentageAllocationRuleAssignment(requestBean);
        });
    }

    @Test
    public void multiCreate() {
        kronosPropertiesMockedStatic.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        PercentageAllocationRuleAssignmentBean requestBean = mock(PercentageAllocationRuleAssignmentBean.class);
        List<PercentageAllocationRuleAssignmentBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        Personality personality = mock(Personality.class);
        Mockito.when(assignmentService.assignPercentageAllocationRule(Mockito.any())).thenReturn(personality);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        List<PercentageAllocationRuleAssignmentBean> responseBean = restPercentageAllocationRuleAssignment.multiCreate(requestDataList);
        assertNotNull(responseBean);
        assertEquals(1, responseBean.size());
    }

    @Test
    public void multiUpdate() {
        kronosPropertiesMockedStatic.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        PercentageAllocationRuleAssignmentBean requestBean = mock(PercentageAllocationRuleAssignmentBean.class);
        List<PercentageAllocationRuleAssignmentBean> requestDataList = new ArrayList<>();
        requestDataList.add(requestBean);
        Personality personality = mock(Personality.class);
        Mockito.when(assignmentService.updatePercentageAllocationRuleAssignment(Mockito.any())).thenReturn(personality);
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        Mockito.when(personality.getPersonNumber()).thenReturn("1111");
        List<PercentageAllocationRuleAssignmentBean> responseBean = restPercentageAllocationRuleAssignment.multiUpdate(requestDataList);
        assertNotNull(responseBean);
        assertEquals(1, responseBean.size());
    }

    @Test
    public void multiDelete() {
        kronosPropertiesMockedStatic.when(() -> KronosProperties.getProperty(anyString(), anyString())).thenReturn("5");
        PercentageAllocationRuleCriteriaBean requestBean = new PercentageAllocationRuleCriteriaBean();
        List<PercentageAllocationRuleCriteriaBean> requestDataList = new ArrayList<>();
        PercentageAllocationRuleAssignmentBean employeeAdjustmentRuleAssignmentBean
                = mock(PercentageAllocationRuleAssignmentBean.class);
        requestDataList.add(requestBean);
        AssignmentBeanHolder<PercentageAllocationRuleAssignmentBean> beanHolder =
                new AssignmentBeanHolder<>(employeeAdjustmentRuleAssignmentBean);
        Mockito.when(assignmentService.validateAndReturnHolderForDelete(Mockito.any()))
                .thenReturn(beanHolder);
        doNothing().when(assignmentService).deletePercentageAllocationRuleAssignments(Lists.newArrayList(beanHolder));
        restPercentageAllocationRuleAssignment.multiDelete(requestDataList);
    }

    @Test
    public void deletePercentageAllocationRuleAssignmentException() {
        PercentageAllocationRuleCriteriaBean requestBean = new PercentageAllocationRuleCriteriaBean();
        restPercentageAllocationRuleAssignment.deletePercentageAllocationRuleAssignment(requestBean);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void deletePercentageAllocationRuleAssignment() {
        assertThrows(APIException.class, () -> {
            HttpServletResponse httpResponse = new MockHttpServletResponse();
            PercentageAllocationRuleCriteriaBean requestBean = new PercentageAllocationRuleCriteriaBean();
            Mockito.when(assignmentService.deletePercentageAllocationRuleAssignment(requestBean)).thenThrow(RuntimeException.class);
            restPercentageAllocationRuleAssignment.deletePercentageAllocationRuleAssignment(requestBean);
        });
    }

    @Test
    public void getAllPercentageAllocationRuleAssignment() throws Exception {
        Mockito.when(assignmentService.getAllPercentageAllocationRules()).thenReturn(getRuleList());
        List<PercentageAllocationRuleAssignmentBean> list = restPercentageAllocationRuleAssignment.getAllPercentageAllocationRuleAssignments();
        assertNotNull(list);
        assertEquals(2, list.size());
    }

    @Disabled
    @Test
    public void getPercentageAllocationRuleAssignment() throws Exception {
        PercentageAllocationRuleCriteriaBean requestBean = new PercentageAllocationRuleCriteriaBean();
        PersonIdentityBean identityBean = new PersonIdentityBean();

        Mockito.when(assignmentService.getPercentageAllocationRules(Mockito.anyLong(), anyString(), anyString(), anyString())).thenReturn(getRuleList());
        List<PercentageAllocationRuleAssignmentBean> list = restPercentageAllocationRuleAssignment
                .getPercentageAllocationRuleAssignment(identityBean.getPersonKey(),requestBean.getProcessor(),requestBean.getEffectiveDate());
       assertNotNull(list);
        assertEquals(2, list.size());
    }

    private List<PercentageAllocationRuleAssignmentBean> getRuleList() {
        List<PercentageAllocationRuleAssignmentBean> list = new ArrayList<>();
        PercentageAllocationRuleAssignmentBean bean1 = new PercentageAllocationRuleAssignmentBean();
        bean1.setEffectiveDate("1111");
        PercentageAllocationRuleAssignmentBean bean2 = new PercentageAllocationRuleAssignmentBean();
        bean2.setEffectiveDate("2222");
        list.add(bean1);
        list.add(bean2);
        return list;
    }

    @Test
    public void getPercentageAllocationRulesByPersonNumbers() throws Exception {
        ExtensionSearchCriteria searchCriteria = new ExtensionSearchCriteria();
        ExtensionWhereCriteria where = new ExtensionWhereCriteria();
        EmployeeCriteria employees = new EmployeeCriteria();
        List<String> values = new ArrayList<>();
        searchCriteria.setReturnUnassignedEmployees(false);
        employees.setKey("personNumber");
        values.add("20338");
        employees.setValues(values);
        where.setEmployees(employees);
        searchCriteria.setWhere(where);

        Mockito.when(personAssignmentHelper.getBulkPersonAssignmentList(Mockito.any(), Mockito.any(), Mockito.anyBoolean()))
                .thenReturn(getRuleList());

        List<PercentageAllocationRuleAssignmentBean> list = restPercentageAllocationRuleAssignment.getPercentageAllocationRulesByPersonNumbers(searchCriteria);
        assertEquals(2, list.size());
    }

    @Test
    public void getPARulesByPersonNumWithFailOnNoAssignmentFalse() throws Exception{
        ExtensionSearchCriteria searchCriteria = new ExtensionSearchCriteria();
        ExtensionWhereCriteria where = new ExtensionWhereCriteria();
        EmployeeCriteria employees = new EmployeeCriteria();
        List<String> values = new ArrayList<>();
        searchCriteria.setFailOnNoAssignment(false);
        employees.setKey("personNumber");
        values.add("20338");
        employees.setValues(values);
        where.setEmployees(employees);
        searchCriteria.setWhere(where);
        Mockito.when(personAssignmentHelper.getBulkPersonAssignmentList(Mockito.any(),Mockito.any(),Mockito.anyBoolean())).thenReturn(new ArrayList<>());
        List<PercentageAllocationRuleAssignmentBean> list = restPercentageAllocationRuleAssignment.getPercentageAllocationRulesByPersonNumbers(searchCriteria);
        assertEquals(0, list.size());
    }

    @Test
    public void getPARulesByPersonNumWithFailOnNoAssignmentFalseAndReturnUnassignFlagTrue() throws Exception{
        ExtensionSearchCriteria searchCriteria = new ExtensionSearchCriteria();
        ExtensionWhereCriteria where = new ExtensionWhereCriteria();
        EmployeeCriteria employees = new EmployeeCriteria();
        List<String> values = new ArrayList<>();
        searchCriteria.setFailOnNoAssignment(false);
        searchCriteria.setReturnUnassignedEmployees(true);
        employees.setKey("personNumber");
        values.add("20338");
        employees.setValues(values);
        where.setEmployees(employees);
        searchCriteria.setWhere(where);
        Mockito.when(personAssignmentHelper.getBulkPersonAssignmentList(Mockito.any(),Mockito.any(),Mockito.anyBoolean())).thenReturn(new ArrayList<>());
        List<PercentageAllocationRuleAssignmentBean> list = restPercentageAllocationRuleAssignment.getPercentageAllocationRulesByPersonNumbers(searchCriteria);
        assertEquals(0, list.size());
    }

    @Test
    public void getPARulesByPersonNumWithReturnUnassignFlagTrue() throws Exception {
        ExtensionSearchCriteria searchCriteria = new ExtensionSearchCriteria();
        ExtensionWhereCriteria where = new ExtensionWhereCriteria();
        EmployeeCriteria employees = new EmployeeCriteria();
        List<String> values = new ArrayList<>();
        searchCriteria.setReturnUnassignedEmployees(true);
        employees.setKey("personNumber");
        values.add("20338");
        employees.setValues(values);
        where.setEmployees(employees);
        searchCriteria.setWhere(where);
        Mockito.when(personAssignmentHelper.getBulkPersonAssignmentList(Mockito.any(),Mockito.any(),Mockito.anyBoolean())).thenReturn(getRuleList());
        List<PercentageAllocationRuleAssignmentBean> list = restPercentageAllocationRuleAssignment.getPercentageAllocationRulesByPersonNumbers(searchCriteria);
        assertEquals(2, list.size());
    }

    @Test
    public void assignPercentageAllocationRuleWithoutPermissions() {
        currentUserAccountManagerMockedStatic.when(CurrentUserAccountManager::getPersonality).thenReturn(personality);
        Mockito.when(personality.getLicenseTypes()).thenReturn(personLicenseTypeSet);
        Mockito.when(personLicenseTypeSet.hasManagerLicense()).thenReturn(false);

        PercentageAllocationRuleAssignmentBean requestBean = new PercentageAllocationRuleAssignmentBean();

        assertThrows(APIException.class, () -> {
            restPercentageAllocationRuleAssignment.assignPercentageAllocationRule(requestBean);
        });
    }


    @Test
    public void updatePercentageAllocationRuleAssignmentWithoutPermissions() {
        currentUserAccountManagerMockedStatic.when(CurrentUserAccountManager::getPersonality).thenReturn(personality);
        Mockito.when(personality.getLicenseTypes()).thenReturn(personLicenseTypeSet);
        Mockito.when(personLicenseTypeSet.hasManagerLicense()).thenReturn(false);

        PercentageAllocationRuleAssignmentBean requestBean = new PercentageAllocationRuleAssignmentBean();

        assertThrows(APIException.class, () -> {
            restPercentageAllocationRuleAssignment.updatePercentageAllocationRuleAssignment(requestBean);
        });
    }

    @Test
    public void multiUpdatePercentageAllocationRuleAssignmentWithoutPermissions() {
        currentUserAccountManagerMockedStatic.when(CurrentUserAccountManager::getPersonality).thenReturn(personality);
        Mockito.when(personality.getLicenseTypes()).thenReturn(personLicenseTypeSet);
        Mockito.when(personLicenseTypeSet.hasManagerLicense()).thenReturn(false);

        List<PercentageAllocationRuleAssignmentBean> requestDataList = Collections.singletonList(new PercentageAllocationRuleAssignmentBean());

        assertThrows(APIException.class, () -> {
            restPercentageAllocationRuleAssignment.multiUpdate(requestDataList);
        });
    }

    @Test
    public void multiCreatePercentageAllocationRuleAssignmentWithoutPermissions() {
        currentUserAccountManagerMockedStatic.when(CurrentUserAccountManager::getPersonality).thenReturn(personality);
        Mockito.when(personality.getLicenseTypes()).thenReturn(personLicenseTypeSet);
        Mockito.when(personLicenseTypeSet.hasManagerLicense()).thenReturn(false);

        List<PercentageAllocationRuleAssignmentBean> requestDataList = Collections.singletonList(new PercentageAllocationRuleAssignmentBean());

        assertThrows(APIException.class, () -> {
            restPercentageAllocationRuleAssignment.multiCreate(requestDataList);
        });
    }

    @Test
    public void multiDeletePercentageAllocationRuleAssignmentWithoutPermissions() {
        currentUserAccountManagerMockedStatic.when(CurrentUserAccountManager::getPersonality).thenReturn(personality);
        Mockito.when(personality.getLicenseTypes()).thenReturn(personLicenseTypeSet);
        Mockito.when(personLicenseTypeSet.hasManagerLicense()).thenReturn(false);

        List<PercentageAllocationRuleCriteriaBean> requestDataList = Collections.singletonList(new PercentageAllocationRuleCriteriaBean());
        assertThrows(APIException.class, () -> {
            restPercentageAllocationRuleAssignment.multiDelete(requestDataList);
        });
    }

    @Test
    public void getPercentageAllocationRuleAssignmentWithoutPermissions() {
        currentUserAccountManagerMockedStatic.when(CurrentUserAccountManager::getPersonality).thenReturn(personality);
        Mockito.when(personality.getLicenseTypes()).thenReturn(personLicenseTypeSet);
        Mockito.when(personLicenseTypeSet.hasManagerLicense()).thenReturn(false);

        assertThrows(APIException.class, () -> {
            restPercentageAllocationRuleAssignment.getPercentageAllocationRuleAssignment(1L, "", "");
        });
    }


    @Test
    public void getPercentageAllocationRuleAssignmentByPersonNumberWithoutPermissions() {
        currentUserAccountManagerMockedStatic.when(CurrentUserAccountManager::getPersonality).thenReturn(personality);
        Mockito.when(personality.getLicenseTypes()).thenReturn(personLicenseTypeSet);
        Mockito.when(personLicenseTypeSet.hasManagerLicense()).thenReturn(false);

        assertThrows(APIException.class, () -> {
            restPercentageAllocationRuleAssignment.getPercentageAllocationRuleAssignmentByPersonNumber("123", "", "");
        });
    }


    @Test
    public void deletePercentageAllocationRuleAssignmentWithoutPermissions() {
        currentUserAccountManagerMockedStatic.when(CurrentUserAccountManager::getPersonality).thenReturn(personality);
        Mockito.when(personality.getLicenseTypes()).thenReturn(personLicenseTypeSet);
        Mockito.when(personLicenseTypeSet.hasManagerLicense()).thenReturn(false);

        PercentageAllocationRuleCriteriaBean requestBean = new PercentageAllocationRuleCriteriaBean();
        assertThrows(APIException.class, () -> {
        restPercentageAllocationRuleAssignment.deletePercentageAllocationRuleAssignment(requestBean);
        });
    }

    @Test
    public void getAllPercentageAllocationRuleAssignmentsWithoutPermissions() {
        currentUserAccountManagerMockedStatic.when(CurrentUserAccountManager::getPersonality).thenReturn(personality);

        Mockito.when(personality.getLicenseTypes()).thenReturn(personLicenseTypeSet);
        Mockito.when(personLicenseTypeSet.hasManagerLicense()).thenReturn(false);

        assertThrows(APIException.class, () ->
                restPercentageAllocationRuleAssignment.getAllPercentageAllocationRuleAssignments()
        );
    }

    @Test
    public void getPercentageAllocationRulesAssignmentByPersonNumbersWithoutPermissions() {
        currentUserAccountManagerMockedStatic.when(CurrentUserAccountManager::getPersonality).thenReturn(personality);

        Mockito.when(personality.getLicenseTypes()).thenReturn(personLicenseTypeSet);
        Mockito.when(personLicenseTypeSet.hasManagerLicense()).thenReturn(false);

        ExtensionSearchCriteria requestBean = new ExtensionSearchCriteria();

        assertThrows(APIException.class, () ->
                restPercentageAllocationRuleAssignment.getPercentageAllocationRulesByPersonNumbers(requestBean)
        );
    }
}