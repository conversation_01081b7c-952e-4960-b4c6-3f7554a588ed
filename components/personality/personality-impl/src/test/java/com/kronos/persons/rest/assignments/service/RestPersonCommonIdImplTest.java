package com.kronos.persons.rest.assignments.service;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.beans.validator.PersonCommonIdBeanValidator;
import com.kronos.persons.rest.assignments.model.PersonCommonIdRequest;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.extensions.service.CriteriaValidator;
import com.kronos.persons.rest.model.ExtensionCriteria;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.rest.model.PersonQualifierBean;
import com.kronos.persons.rest.model.SearchValues;
import com.kronos.persons.utils.CriteriaHelper;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.commonapp.people.business.commonobjectid.CommonObjectId;
import com.kronos.wfc.commonapp.people.business.commonobjectid.CommonObjectIdService;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.datetime.framework.KServer;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.PersistenceException;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RestPersonCommonIdImplTest {
	
	@Spy 
	private ValidatorUtils validatorUtils = new ValidatorUtils();
	@Mock
	private PersonIdentityBeanValidator personidentityvalidatorl;
	
	private PersonCommonIdRequest requestData;
	private PersonIdentityBean personIdentity;
	@Mock
	private Personality personality;
	@Mock
	private CommonObjectIdService commonObjectIdService;
	@Mock
	private PersonCommonIdBeanValidator personcommonidvalidator;

	@InjectMocks
	@Spy
	private RestPersonCommonIdImpl restPersonCommonIdImplSpy = new RestPersonCommonIdImplForTest();
	@Mock
	private CommonObjectId commonObjectId;
	private ObjectIdLong personId;
	@Mock
	KServer kserver;
	@Mock
	CriteriaHelper criteriaHelper;
	@Mock
    CriteriaValidator criteriaValidator;
	@Mock
	private PersonQualifierBean personQualifierBean;
	@Mock
	ResponseHandler responseHandler;
	ExtensionSearchCriteria searchCriteria;
	ExtensionCriteria extensionCriteria;
	List<String> searchValueIdentifier;
	List<SearchValues> multiKeyValues;
	SearchValues searchValue ;
	List<CommonObjectId> commonObjectIdList;

	private MockedStatic<KronosProperties> mockedKronosProperties;
	private MockedStatic<ResponseHandler> mockedResponseHandler;
	private MockedStatic<PrsnException> mockedPrsnException;
	

	@BeforeEach
	public void setUp() throws Exception {
		requestData = new PersonCommonIdRequest();
		personIdentity = new PersonIdentityBean();
		personId = new ObjectIdLong(10L);
		searchCriteria = new ExtensionSearchCriteria();
		extensionCriteria = new ExtensionCriteria();
		searchValueIdentifier = new ArrayList<>();
		multiKeyValues = new ArrayList<>();
		searchValue = new SearchValues();
		commonObjectIdList = new ArrayList<>();
		mockedKronosProperties = Mockito.mockStatic(KronosProperties.class);
		mockedResponseHandler = Mockito.mockStatic(ResponseHandler.class);
		mockedPrsnException = Mockito.mockStatic(PrsnException.class);
	}

	@AfterEach
	public void tearDown() throws Exception {
		requestData = null;
		personIdentity = null;
		searchCriteria =null;
		extensionCriteria=null;
		searchValueIdentifier=null;
		multiKeyValues=null;
		searchValue=null;
		commonObjectIdList=null;
		mockedKronosProperties.close();
		mockedResponseHandler.close();
		mockedPrsnException.close();
	}
	
	class RestPersonCommonIdImplForTest extends RestPersonCommonIdImpl
	{
		@Override
		protected CommonObjectIdService getCommonObjectIdService() {
			return commonObjectIdService;
		}
	    @Override
	    protected CommonObjectId getCommonObjectId(ObjectIdLong personId, PersonCommonIdRequest requestData) {
	    	return commonObjectId;
	    }
	}

	@Test
	public void testDeleteShouldThrowExceptionForNullPersonIdentity() {
		try {
			restPersonCommonIdImplSpy.delete(requestData);	
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
				fail();
		}
	}
	
	@Test
	public void testDeleteeShouldThrowGenericException() {
		Mockito.doThrow(new PersistenceException()).when(ResponseHandler.class);
		ResponseHandler.validateForNullRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		try {
			restPersonCommonIdImplSpy.delete(requestData);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testDeleteeShouldThrowxception() {
		Mockito.doThrow(new RuntimeException()).when(ResponseHandler.class);
		ResponseHandler.validateForNullRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(Exception.class))).thenReturn(new APIException());
		try {
			restPersonCommonIdImplSpy.delete(requestData);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	
	@Test
	public void testDeleteShouldSuccessfullyDelete() throws Exception {
		personIdentity.setPersonNumber("1235");
		requestData.setPersonIdentity(personIdentity);
		Mockito.mockStatic(CommonObjectIdService.class);
		Mockito.when(commonObjectIdService.retrieveCommonObjectIdByPersonId(Mockito.any())).thenReturn(commonObjectId);
		Mockito.when(personidentityvalidatorl.getPersonality(personIdentity)).thenReturn(personality);
		try {
			restPersonCommonIdImplSpy.delete(requestData);	
		}catch (Exception e){
			fail();
		}

	}


	@Test
	public void testUpdateShouldThrowExceptionForNullPersonIdentity() {
		Mockito.when(personidentityvalidatorl.getPersonality(Mockito.any())).thenReturn(null);
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(Exception.class))).thenReturn(new APIException());

		APIException exception = assertThrows(APIException.class, () -> {
			restPersonCommonIdImplSpy.update(requestData);
		});

		assertNotNull(exception);
	}
	
	
	@Test
	public void testUpdateShouldThrowAPIException() {
		Mockito.doThrow(new APIException()).when(personcommonidvalidator).validate(Mockito.any(), Mockito.any(), Mockito.any());
		try {
			restPersonCommonIdImplSpy.update(requestData);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	
	@Test
	public void testUpdateShouldGenericException() {
		Mockito.doThrow(new PersistenceException()).when(personcommonidvalidator).validate(Mockito.any(), Mockito.any(), Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		try {
			restPersonCommonIdImplSpy.update(requestData);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}

	@Test
	public void testUpdateShouldSuccessfullyInsert() {
		Mockito.when(personality.getPersonId()).thenReturn(personId);
		Mockito.when(personidentityvalidatorl.getPersonality(Mockito.any())).thenReturn(personality);
		Mockito.when(commonObjectIdService.retrieveCommonObjectIdByPersonId(Mockito.any())).thenReturn(commonObjectId);
		Mockito.when(commonObjectId.getPersonId()).thenReturn(personId);
		Mockito.when(commonObjectId.getCommonIdText()).thenReturn("coid");
		Mockito.when(commonObjectId.getAssociateIdText()).thenReturn("aoid");

		// Ensure the validator does not throw an exception
		Mockito.doNothing().when(personcommonidvalidator).validate(Mockito.any(), Mockito.any(), Mockito.any());

		PersonCommonIdRequest responseBean = restPersonCommonIdImplSpy.update(requestData);
		assertNull(responseBean);
	}
	
	@Test
	public void testMultiUpdateShouldPassSuccessfully() {
		mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("10");
		List<PersonCommonIdRequest> requestDataList = new ArrayList<>();
		requestDataList.add(requestData);
		Personality personality = Mockito.mock(Personality.class);
		Mockito.when(personidentityvalidatorl.getPersonality(Mockito.any())).thenReturn(personality);
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(10L));
		Mockito.when(personality.getPersonNumber()).thenReturn("1024");
		List<PersonCommonIdRequest> responseBean = restPersonCommonIdImplSpy.multiUpdate(requestDataList);
		assertNotNull(responseBean);
		assertEquals(1, responseBean.size());
	}
	
	
	@Test
	public void testMultiUpdateShouldThrowException() {
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(Exception.class))).thenReturn(new APIException());
		List<PersonCommonIdRequest> requestDataList = new ArrayList<>();
		requestDataList.add(requestData);
		try {
			restPersonCommonIdImplSpy.multiUpdate(requestDataList);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testMultiUpdateShouldThrowGenericException() {
		Mockito.doThrow(new PersistenceException()).when(personcommonidvalidator).validate(Mockito.any(), Mockito.any(), Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		List<PersonCommonIdRequest> requestDataList = new ArrayList<>();
		requestDataList.add(requestData);
		try {
			restPersonCommonIdImplSpy.multiUpdate(requestDataList);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testMultiUpdateShouldThrowAPIException() {
		Mockito.doThrow(new APIException()).when(personcommonidvalidator).validate(Mockito.any(), Mockito.any(), Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(APIException.class))).thenReturn(new APIException());
		List<PersonCommonIdRequest> requestDataList = new ArrayList<>();
		requestDataList.add(requestData);
		try {
			restPersonCommonIdImplSpy.multiUpdate(requestDataList);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testMultiDeleteShouldPassSuccessfully() {
		mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("10");
		List<PersonCommonIdRequest> requestDataList = new ArrayList<>();
		requestData.setPersonIdentity(personIdentity);
		requestDataList.add(requestData);
		Personality personality = Mockito.mock(Personality.class);
		//Mockito.mockStatic(CommonObjectIdService.class);
		Mockito.when(commonObjectIdService.retrieveCommonObjectIdByPersonId(Mockito.any())).thenReturn(null);
		Mockito.when(personidentityvalidatorl.getPersonality(personIdentity)).thenReturn(personality);
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(10L));
		Mockito.when(personality.getPersonNumber()).thenReturn("1024");
		
		try{
			restPersonCommonIdImplSpy.multiDelete(requestDataList);	
		}catch (Exception e){
			fail();
		}
	}
	
	@Test
	public void testMultiDeleteShouldThrowAPIExceptionForMissingPersonIdentity() {
		try {
			 List<PersonCommonIdRequest> personCommonIdList = new ArrayList<>();
			 personCommonIdList.add(requestData);
			restPersonCommonIdImplSpy.multiDelete(personCommonIdList);
		} catch (APIException e) {
			assertEquals(ExceptionConstants.ALL_RECORDS_FAILED, e.getErrorCode());
		}
	}
	
	@Test
	public void testMultiDeleteShouldThrowGenericException() {
		try {
			List<PersonCommonIdRequest> personCommonIdList = new ArrayList<>();
			Mockito.doThrow(new PersistenceException()).when(personidentityvalidatorl).validate(Mockito.any(),
					Mockito.any(), Mockito.any());
			mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
			requestData.setPersonIdentity(personIdentity);
			personCommonIdList.add(requestData);
			restPersonCommonIdImplSpy.multiDelete(personCommonIdList);
			fail();
		} catch (Exception e) {
			if (!(e instanceof APIException))
				fail();
		}
	}
	
	@Test
	public void testMultiDeleteShouldThrowException() {
		try {
			List<PersonCommonIdRequest> personCommonIdList = new ArrayList<>();
			Mockito.doThrow(new RuntimeException()).when(personidentityvalidatorl).validate(Mockito.any(),
					Mockito.any(), Mockito.any());
			mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(Exception.class))).thenReturn(new APIException());
			requestData.setPersonIdentity(personIdentity);
			personCommonIdList.add(requestData);
			restPersonCommonIdImplSpy.multiDelete(personCommonIdList);
			fail();
		} catch (Exception e) {
			if (!(e instanceof APIException))
				fail();
		}
	}

	@Test
	public void testRetrieveShouldThrowExceptionForNullPersonNumber() {
		try {
			 restPersonCommonIdImplSpy.retrieve(null);
		} catch (APIException e) {
			assertEquals(ExceptionConstants.EXCEPTION_101202, e.getErrorCode());
		}
	}
	
	@Test
	public void testRetrieveShouldThrowExceptionForInvalidPersonNumber() {
		try {
			 String personNumber = "123";
			 List<String> personNumbers = new ArrayList<>();
			 personNumbers.add(personNumber);
			restPersonCommonIdImplSpy.retrieve(personNumber);
			 Mockito.when(commonObjectIdService.retrieveByPersonNumber(personNumbers)).thenReturn(null);
		} catch (APIException e) {
			assertEquals(ExceptionConstants.INVALID_AOID_COID_PERSONID_OR_PERSONNUMBER, e.getErrorCode());
		}
	}
	
	@Test
	public void testRetrieveShouldPassSuccessfully() {
		String personNumber = "123";
		List<String> personNumbers = new ArrayList<>();
		personNumbers.add(personNumber);
		List<CommonObjectId> coidBeanList = new ArrayList<>();
		CommonObjectId commpnObject = new CommonObjectId();
		commpnObject.setAssociateIdText("aoid");
		commpnObject.setCommonIdText("coid");
		commpnObject.setPersonNumber(personNumber);
		coidBeanList.add(commpnObject);
		Mockito.when(commonObjectIdService.retrieveByPersonNumber(personNumbers)).thenReturn(coidBeanList);
		PersonCommonIdRequest response = restPersonCommonIdImplSpy.retrieve(personNumber);
		setAssertions(commpnObject, response);
	}
	
	@Test
	public void testRetrieveShouldThrowExceptionForNullPersonId() {
		try {
			 restPersonCommonIdImplSpy.retrieveByPersonId(null);
		} catch (APIException e) {
			assertEquals(ExceptionConstants.INVALID_AOID_COID_PERSONID_OR_PERSONNUMBER, e.getErrorCode());
		}
	}
	
	@Test
	public void testRetrieveByPersonIdShouldPassSuccessfully() {
		Long personId = 123L;
		CommonObjectId commonObject = getPersonIdList(personId);
		PersonCommonIdRequest response = restPersonCommonIdImplSpy.retrieveByPersonId(personId);
		setAssertions(commonObject, response);
	}
	private void setAssertions(CommonObjectId commonObject, PersonCommonIdRequest response) {
		assertEquals(commonObject.getAssociateIdText(), response.getAoid());
		assertEquals(commonObject.getCommonIdText(), response.getCoid());
		assertEquals(commonObject.getPersonNumber(), response.getPersonIdentity().getPersonNumber());
	}
	private CommonObjectId getPersonIdList(Long personId) {
		List<Long> personIds = new ArrayList<>();
		personIds.add(personId);
		List<CommonObjectId> coidBeanList = new ArrayList<>();
		CommonObjectId commonObject = new CommonObjectId();
		commonObject.setAssociateIdText("aoid");
		commonObject.setCommonIdText("coid");
		commonObject.setPersonId(new ObjectIdLong(personId));
		coidBeanList.add(commonObject);
		Mockito.when(commonObjectIdService.retrieveByPersonId(personIds)).thenReturn(coidBeanList);
		return commonObject;
	}
	
	@Test
	public void testMultiReadAllRecordFailure() {
		try {
			 prepareCriteria();
			 Mockito.when(criteriaHelper.getExtensionCriteria(searchCriteria)).thenReturn(extensionCriteria );
			 restPersonCommonIdImplSpy.multiRead(searchCriteria );
		} catch (APIException e) {
			assertEquals(ExceptionConstants.ALL_RECORDS_FAILED, e.getErrorCode());
		}
	}
	
	@Test
	public void testMultiReadPartialSuccess() {
		try {
			prepareCriteria();
			CommonObjectId commonObjectId = new CommonObjectId();
			commonObjectId.setAssociateIdText("1234");
			commonObjectId.setCommonIdText("6578");
			commonObjectIdList.add(commonObjectId);
			Mockito.when(commonObjectIdService.retrieveByAssociateAndClientIds(Mockito.any()))
					.thenReturn(commonObjectIdList);
			Mockito.when(criteriaHelper.getExtensionCriteria(searchCriteria)).thenReturn(extensionCriteria);
			restPersonCommonIdImplSpy.multiRead(searchCriteria);
		} catch (APIException e) {
			assertEquals(ExceptionConstants.PARTIAL_SUCCESS, e.getErrorCode());
		}
	}
	
	@Test
	public void testMultiReadPartialSuccessWithValidPersonId() {
		try {
			extensionCriteria.setSearchBy("personId");
			prepareCriteria();
			Mockito.when(criteriaHelper.getPersonQualifiers(extensionCriteria)).thenReturn(personQualifierBean);
			List<Long> personIds= new ArrayList<>();
			long personId = 10L;
			personIds.add(personId);
			Mockito.when(personQualifierBean.getPersonIdList()).thenReturn(personIds);
			getPersonIdList(personId);
			Mockito.when(criteriaHelper.getExtensionCriteria(searchCriteria)).thenReturn(extensionCriteria);
			restPersonCommonIdImplSpy.multiRead(searchCriteria);
		} catch (APIException e) {
			assertEquals(ExceptionConstants.PARTIAL_SUCCESS, e.getErrorCode());
		}
	}

	
	@Test
	public void testMultiReadByAoidAllRecordFail() {
		try {
			extensionCriteria.setSearchBy("aoid");
			prepareCriteria();
			Mockito.when(criteriaHelper.getPersonQualifiers(extensionCriteria)).thenReturn(personQualifierBean);
			List<Long> personIds= new ArrayList<>();
			long personId = 10L;
			personIds.add(personId);
			Mockito.when(personQualifierBean.getPersonIdList()).thenReturn(personIds);
			getPersonIdList(personId);
			Mockito.when(criteriaHelper.getExtensionCriteria(searchCriteria)).thenReturn(extensionCriteria);
			restPersonCommonIdImplSpy.multiRead(searchCriteria);
		} catch (APIException e) {
			assertEquals(ExceptionConstants.ALL_RECORDS_FAILED, e.getErrorCode());
		}
	}
	private void prepareCriteria() {
		searchValueIdentifier.add("123");
		searchValueIdentifier.add("423");
		extensionCriteria.setSearchValue(searchValueIdentifier);
		searchValue.setAoid("123");
		searchValue.setCoid("456");
		multiKeyValues.add(searchValue);
		extensionCriteria.setMultiKeyValues(multiKeyValues);
	}
	
	@Test
	public void testMultiReadAllRecordFailWithInvalidPersonIds() {
		try {
			extensionCriteria.setSearchBy("personId");
			prepareCriteria();
			Mockito.when(criteriaHelper.getPersonQualifiers(extensionCriteria)).thenReturn(personQualifierBean);
			long personId = 10L;
			Mockito.when(personQualifierBean.getPersonIdList()).thenReturn(null);
			getPersonIdList(personId);
			Mockito.when(criteriaHelper.getExtensionCriteria(searchCriteria)).thenReturn(extensionCriteria);
			restPersonCommonIdImplSpy.multiRead(searchCriteria);
		} catch (APIException e) {
			assertEquals(ExceptionConstants.ALL_RECORDS_FAILED, e.getErrorCode());
		}
	}
	
	@Test
	public void testMultiReadByAoidCoidAllFailureForCoidRequired() {
		try {
			prepareCriteria();
			Mockito.when(criteriaHelper.getPersonQualifiers(extensionCriteria)).thenReturn(personQualifierBean);
			mockedKronosProperties.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("10");
			mockedKronosProperties.when(() -> KronosProperties.getPropertyAsBoolean(CommonObjectId.COID_REQUIRED_KEY, false)).thenReturn(true);
			long personId = 10L;
			Mockito.when(personQualifierBean.getPersonIdList()).thenReturn(null);
			getPersonIdList(personId);
			Mockito.when(criteriaHelper.getExtensionCriteria(searchCriteria)).thenReturn(extensionCriteria);
			restPersonCommonIdImplSpy.multiRead(searchCriteria);
		} catch (APIException e) {
			assertEquals(ExceptionConstants.ALL_RECORDS_FAILED, e.getErrorCode());
		}
	}
	
}
