/*******************************************************************************
 * NewBatchProcessorTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.utils;

import com.kronos.container.api.exception.APIException;
import com.kronos.container.api.util.APIExceptionDetailResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

/**
 * Test class for {@link NewBatchProcessor}
 */
@ExtendWith(MockitoExtension.class)
public class NewBatchProcessorTest {
    @Mock
    private Function<Integer, String> requestProcessor;

    private NewBatchProcessor<Integer, String> newBatchProcessor;
    private final Collection<Integer> requestDataCollection = Arrays.asList(1, 2);

    @BeforeEach
    public void setUp() {
        newBatchProcessor = new NewBatchProcessor<>(requestDataCollection, requestProcessor);
    }

    @Test
    public void testProcessSuccess() {
        String successData = "success1";
        when(requestProcessor.apply(anyInt())).thenReturn(successData);
        List<String> result = newBatchProcessor.process();
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("success1"));
    }

    @Test
    public void testProcessFail() {
        when(requestProcessor.apply(anyInt())).thenThrow(new APIException());
        assertThrows(APIException.class, newBatchProcessor::process);
    }

    @Test
    public void testBulkProcessWhenFailOnNoSuccessTrueThenReturnException() {
        String successData = "success1";
        when(requestProcessor.apply(anyInt())).thenReturn(successData).thenThrow(new APIException());
        assertThrows(APIException.class, newBatchProcessor::process);
    }

    @Test
    public void testProcessWithoutThrowForSucess() {
        String successData = "success1";
        when(requestProcessor.apply(anyInt())).thenReturn(successData);
        Map<Integer, APIExceptionDetailResult<?>> result = newBatchProcessor.processWithoutThrow();
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(successData, result.get(1).getSuccess());
        assertEquals(successData, result.get(2).getSuccess());
    }

    @Test
    public void testProcessWithoutThrowForException() {
        APIException apiException = new APIException();
        when(requestProcessor.apply(anyInt())).thenThrow(apiException);
        Map<Integer, APIExceptionDetailResult<?>> result = newBatchProcessor.processWithoutThrow();
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(apiException, result.get(1).getError());
        assertEquals(apiException, result.get(2).getError());
    }

    @Test
    public void testProcessWithoutThrowFoePartialSuccess() {
        String successData = "success1";
        APIException apiException = new APIException();
        when(requestProcessor.apply(anyInt())).thenReturn(successData).thenThrow(apiException);
        Map<Integer, APIExceptionDetailResult<?>> result = newBatchProcessor.processWithoutThrow();
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(successData, result.get(1).getSuccess());
        assertEquals(apiException, result.get(2).getError());
    }

}