package com.kronos.persons.rest.assignments.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.AdminProfileBean;
import com.kronos.persons.rest.assignments.model.AttendanceAdminAssignmentBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.PersistenceException;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RestPersonAttendanceAdminAssignmentTest {

	@InjectMocks
	RestPersonAttendanceAdminAssignment restService;

	@Mock
	PersonAttendanceAdminAssignmentService personAttendanceAdminAssignment;

	@Mock
	PersonAssignmentHelper<AttendanceAdminAssignmentBean> assignmentHeplper;

	@Mock
	Personality persoanlity;

	private MockedStatic<KronosProperties> kronosPropertiesMockedStatic;
	private MockedStatic<PrsnException> prsnExceptionMockedStatic;

	@BeforeEach
	public void setUp() {
		kronosPropertiesMockedStatic = Mockito.mockStatic(KronosProperties.class);
		prsnExceptionMockedStatic = Mockito.mockStatic(PrsnException.class);
	}

	@AfterEach
	public void tearDown() {
		kronosPropertiesMockedStatic.close();
		prsnExceptionMockedStatic.close();
	}

	@Test
	public void update() {
		AdminProfileBean employee = new AdminProfileBean();
		AdminProfileBean admin = new AdminProfileBean();
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		Mockito.when(personAttendanceAdminAssignment.updateRequest(requestBean)).thenReturn(persoanlity);
		Mockito.when(persoanlity.getPersonNumber()).thenReturn("111");
		Mockito.when(persoanlity.getPersonId()).thenReturn(new ObjectIdLong(1L));
		requestBean.setPersonIdentity(employee);
		requestBean.setAdministrator(admin);
		AttendanceAdminAssignmentBean responseBean = restService.update(requestBean);
		assertNotNull(responseBean);
	}

	@SuppressWarnings("unchecked")
	@Test
	public void updateException() {
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		Mockito.when(personAttendanceAdminAssignment.updateRequest(requestBean)).thenThrow(APIException.class);
		try {
			restService.update(requestBean);
		} catch (APIException e) {
			assertTrue(true);
		}
	}

	@Test
	public void testUpdateShouldGenericException() {
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		Mockito.doThrow(new PersistenceException()).when(personAttendanceAdminAssignment).updateRequest(Mockito.any());
		prsnExceptionMockedStatic.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		try {
			restService.update(requestBean);
			fail();
		} catch (Exception e) {
			if (!(e instanceof APIException))
				fail();
		}
	}

	@Test
	public void testUpdateShouldException() {
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		Mockito.doThrow(new RuntimeException()).when(personAttendanceAdminAssignment).updateRequest(Mockito.any());
		prsnExceptionMockedStatic.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restService.update(requestBean);
			fail();
		} catch (Exception e) {
			if (!(e instanceof APIException))
				fail();
		}
	}

	@Test
	public void delete() {
		AdminProfileBean employee = new AdminProfileBean();
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		requestBean.setPersonIdentity(employee);
		try {
			restService.delete(requestBean);
		} catch (Exception e) {
			fail();
		}
	}

	@SuppressWarnings("unchecked")
	@Test
	public void deleteException() {
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		Mockito.when(personAttendanceAdminAssignment.deleteRequest(requestBean)).thenThrow(APIException.class);
		try {
			restService.delete(requestBean);
		} catch (APIException e) {
			assertTrue(true);
		}
	}

	@Test
	public void testDeleteShouldThrowGenericException() {
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		Mockito.doThrow(new PersistenceException()).when(personAttendanceAdminAssignment).deleteRequest(Mockito.any());
		prsnExceptionMockedStatic.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		try {
			restService.delete(requestBean);
			fail();
		} catch (Exception e) {
			if (!(e instanceof APIException))
				fail();
		}
	}

	@Test
	public void testDeleteShouldThrowException() {
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		Mockito.doThrow(new RuntimeException()).when(personAttendanceAdminAssignment).deleteRequest(Mockito.any());
		prsnExceptionMockedStatic.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restService.delete(requestBean);
			fail();
		} catch (Exception e) {
			if (!(e instanceof APIException))
				fail();
		}
	}

	@Test
	public void multiUpdate() {
		kronosPropertiesMockedStatic.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("5");
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		List<AttendanceAdminAssignmentBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);
		Personality personality = Mockito.mock(Personality.class);
		Mockito.doNothing().when(personAttendanceAdminAssignment).bulkUpdateRequest(Mockito.anyList());
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
		Mockito.when(personality.getPersonNumber()).thenReturn("1111");
		List<AttendanceAdminAssignmentBean> responseList = restService.multiUpdate(requestDataList);
		assertEquals(1, responseList.size());
	}

	@Test
	public void testMultiUpdateEmptyRequest() {
		kronosPropertiesMockedStatic.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("5");

		assertThrows(APIException.class, () -> {
			restService.multiUpdate(Collections.emptyList());
		});
	}

	@Test
	public void testMultiUpdateShouldThrowException() {
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		List<AttendanceAdminAssignmentBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);

		// Mock KronosProperties to return a valid integer string
		kronosPropertiesMockedStatic.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("5");

		Mockito.doThrow(new RuntimeException()).when(personAttendanceAdminAssignment).bulkUpdateRequest(Mockito.anyList());
		prsnExceptionMockedStatic.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());

		APIException exception = assertThrows(APIException.class, () -> {
			restService.multiUpdate(requestDataList);
		});

		assertNotNull(exception);
	}

	@Test
	public void testMultiUpdateShouldThrowAPIException() {
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		List<AttendanceAdminAssignmentBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);

		// Mock KronosProperties to return a valid integer string
		kronosPropertiesMockedStatic.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("5");

		Mockito.doThrow(new APIException()).when(personAttendanceAdminAssignment).bulkUpdateRequest(Mockito.anyList());
		prsnExceptionMockedStatic.when(() -> PrsnException.getAPIException(Mockito.any(APIException.class))).thenReturn(new APIException());

		APIException exception = assertThrows(APIException.class, () -> {
			restService.multiUpdate(requestDataList);
		});

		assertNotNull(exception);
	}

	@Test
	public void multiDelete() {
		kronosPropertiesMockedStatic.when(() -> KronosProperties.getProperty(Mockito.anyString(), Mockito.anyString())).thenReturn("5");
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		List<AttendanceAdminAssignmentBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);
		Personality personality = Mockito.mock(Personality.class);
		Mockito.when(personAttendanceAdminAssignment.deleteRequest(Mockito.any())).thenReturn(personality);
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
		Mockito.when(personality.getPersonNumber()).thenReturn("1111");
		try {
			restService.multiDelete(requestDataList);
		} catch (Exception e) {
			fail();
		}
	}

	@Test
	public void testMultiDeleteShouldThrowGenericException() {
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		List<AttendanceAdminAssignmentBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);

		Mockito.doThrow(new PersistenceException()).when(personAttendanceAdminAssignment).deleteRequest(Mockito.any(AttendanceAdminAssignmentBean.class));
		prsnExceptionMockedStatic.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());

		APIException exception = assertThrows(APIException.class, () -> {
			for (AttendanceAdminAssignmentBean bean : requestDataList) {
				restService.delete(bean);
			}
		});

		assertNotNull(exception);
	}

	@Test
	public void testMultiDeleteShouldThrowException() {
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		List<AttendanceAdminAssignmentBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);

		Mockito.doThrow(new RuntimeException()).when(personAttendanceAdminAssignment).deleteRequest(Mockito.any(AttendanceAdminAssignmentBean.class));
		prsnExceptionMockedStatic.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());

		APIException exception = assertThrows(APIException.class, () -> {
			for (AttendanceAdminAssignmentBean bean : requestDataList) {
				restService.delete(bean);
			}
		});

		assertNotNull(exception);
	}

	@Test
	public void testMultiDeleteShouldThrowAPIException() {
		AttendanceAdminAssignmentBean requestBean = new AttendanceAdminAssignmentBean();
		List<AttendanceAdminAssignmentBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean);

		Mockito.doThrow(new APIException()).when(personAttendanceAdminAssignment).deleteRequest(Mockito.any(AttendanceAdminAssignmentBean.class));
		prsnExceptionMockedStatic.when(() -> PrsnException.getAPIException(Mockito.any(APIException.class))).thenReturn(new APIException());

		APIException exception = assertThrows(APIException.class, () -> {
			for (AttendanceAdminAssignmentBean bean : requestDataList) {
				restService.delete(bean);
			}
		});

		assertNotNull(exception);
	}

	@Test
	public void retrieve() {
		AttendanceAdminAssignmentBean bean = new AttendanceAdminAssignmentBean();
		Mockito.when(assignmentHeplper.getPersonAssignmentByPersonId(Mockito.anyLong(), Mockito.any())).thenReturn(bean);
		assertEquals(bean, restService.retrieveByPersonId(10L));
	}

	@Test
	public void retrieveByPersonNumber() {
		AttendanceAdminAssignmentBean bean = new AttendanceAdminAssignmentBean();
		Mockito.when(assignmentHeplper.getPersonAssignmentByPersonNumber(Mockito.any(), Mockito.any())).thenReturn(bean);
		String personNumber = "123";
		assertEquals(bean, restService.retrieveByPersonNumber(personNumber));
	}

	@SuppressWarnings("unchecked")
	@Test
	public void retrieveException() {
		Mockito.when(assignmentHeplper.getPersonAssignmentByPersonId(Mockito.anyLong(), Mockito.any())).thenThrow(APIException.class);
		try {
			restService.retrieveByPersonId(10L);
		} catch (APIException e) {
			assertTrue(true);
		}
	}

	@Test
	public void retrieveList() {
		Function<PersonIdentityBean, AttendanceAdminAssignmentBean> getDataListFunction = personAttendanceAdminAssignment::retrieve;
		ExtensionSearchCriteria criteria = new ExtensionSearchCriteria();
		AttendanceAdminAssignmentBean requestBean1 = new AttendanceAdminAssignmentBean();
		List<AttendanceAdminAssignmentBean> requestDataList = new ArrayList<>();
		requestDataList.add(requestBean1);
		Mockito.when(assignmentHeplper.getPersonAssignmentList(criteria, getDataListFunction)).thenReturn(requestDataList);
		List<AttendanceAdminAssignmentBean> responseList = restService.retrieveList(criteria);
		assertEquals(0, responseList.size());
	}
}
