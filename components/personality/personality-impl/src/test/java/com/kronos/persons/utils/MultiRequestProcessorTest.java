package com.kronos.persons.utils;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.RuleAssignmentBean;
import com.kronos.persons.rest.model.BeanWrapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MultiRequestProcessorTest {

    private static final String ENTITY_NAME = "Test Entity";
    private static final String ACTION_NAME = "Unit test";

    private final Consumer<BeanWrapper<RuleAssignmentBean>> validator = mock(Consumer.class);
    private final Consumer<List<BeanWrapper<RuleAssignmentBean>>> validEntityHandler = mock(Consumer.class);

    private final MultiRequestProcessor<RuleAssignmentBean> processorToTest = new MultiRequestProcessor<>(
            ENTITY_NAME,
            ACTION_NAME,
            validator,
            validEntityHandler
    );

    @Test
    public void testApply_testHappyPath() {
        List<BeanWrapper<RuleAssignmentBean>> inputEntities = generateInputDataMocks();

        List<RuleAssignmentBean> successfulHandledEntities = processorToTest.apply(inputEntities);

        assertEquals(inputEntities.size(), successfulHandledEntities.size());
    }

    @Test
    public void testApply_ShouldNotHandleInvalidEntities() {
        Mockito.doAnswer(invocation -> {
            BeanWrapper<RuleAssignmentBean> wrapper = (BeanWrapper<RuleAssignmentBean>) invocation.getArguments()[0];
            when(wrapper.getApiException()).thenReturn(mock(APIException.class));
            return null;
        }).when(validator).accept(any());
        List<BeanWrapper<RuleAssignmentBean>> inputEntities = generateInputDataMocks();

        List<RuleAssignmentBean> successfulHandledEntities = processorToTest.apply(inputEntities);

        assertEquals(0, successfulHandledEntities.size());
//        verifyZeroInteractions(validEntityHandler);
        Mockito.verifyNoInteractions(validEntityHandler);
    }

    @Test
    public void testApply_ShouldFilterOutEntitiesWithExceptionsAfterHandling() {
        Mockito.doAnswer(invocation -> {
            List<BeanWrapper<RuleAssignmentBean>> wrappers = (List<BeanWrapper<RuleAssignmentBean>>) invocation.getArguments()[0];
            when(wrappers.get(0).getApiException()).thenReturn(mock(APIException.class));
            return null;
        }).when(validEntityHandler).accept(any());
        List<BeanWrapper<RuleAssignmentBean>> inputEntities = generateInputDataMocks();

        List<RuleAssignmentBean> successfulHandledEntities = processorToTest.apply(inputEntities);

        assertEquals(2, successfulHandledEntities.size());
    }

    private List<BeanWrapper<RuleAssignmentBean>> generateInputDataMocks() {
        return Arrays.asList(generateBeanWrapper(), generateBeanWrapper(), generateBeanWrapper());
    }

    private BeanWrapper<RuleAssignmentBean> generateBeanWrapper() {
        RuleAssignmentBean bean = mock(RuleAssignmentBean.class);
        BeanWrapper<RuleAssignmentBean> wrapper = mock(BeanWrapper.class);
        return wrapper;
    }

}