package com.kronos.persons.utils;

import com.google.common.collect.Lists;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.model.EmployeeWageWorkRulesDTO;
import com.kronos.persons.rest.model.wageoverride.EmployeeWageWorkRulesWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.function.Function;

import static com.kronos.persons.rest.exception.ExceptionConstants.ALL_RECORDS_FAILED;
import static com.kronos.persons.rest.exception.ExceptionConstants.PARTIAL_SUCCESS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for {@link MultiUpsertWageWorkRuleOverrideBulkProcessor}.
 * <p>
 * Copyright (C) 2020 Kronos.com
 * <p>
 * Date: June 15, 2020
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class MultiUpsertWageWorkRuleOverrideBulkProcessorTest {

    private static final Long PERSON_ID_1 = 1L;
    private static final Long PERSON_ID_2 = 2L;
    private static final String PERSON_NUMBER_1 = "PERSON_NUMBER_1";
    private static final String PERSON_NUMBER_2 = "PERSON_NUMBER_2";
    private static final String ERROR_CODE = "WCO-XXXXXX";
    private static final APIException API_EXCEPTION = new APIException(ERROR_CODE);

    private EmployeeWageWorkRulesDTO dto1;
    private EmployeeWageWorkRulesDTO dto2;
    private EmployeeWageWorkRulesWrapper wrapper1;
    private EmployeeWageWorkRulesWrapper wrapper2;
    private List<EmployeeWageWorkRulesDTO> employeeWageWorkRules;

    @BeforeEach
    public void setUp() {
        initializeEmployeeWageWorkRulesDTOs();
        initializeEmployeeWageWorkRulesWrappers();
        employeeWageWorkRules = Lists.newArrayList(dto1, dto2);
    }

    @Test
    public void testProcessSuccess() {
        Function<List<EmployeeWageWorkRulesDTO>, List<EmployeeWageWorkRulesWrapper>> processor
                = (employeeWageWorkRulesDTO -> Lists.newArrayList(wrapper1, wrapper2));
        MultiUpsertWageWorkRuleOverrideBulkProcessor bulkProcessor =
                new MultiUpsertWageWorkRuleOverrideBulkProcessor(employeeWageWorkRules, processor);
        List<EmployeeWageWorkRulesDTO> result = bulkProcessor.process();
        assertEquals(2, result.size());
        assertEquals(new ObjectRef(PERSON_ID_1, PERSON_NUMBER_1), result.get(0).getEmployee());
        assertEquals(new ObjectRef(PERSON_ID_2, PERSON_NUMBER_2), result.get(1).getEmployee());
    }

    @Test
    public void testProcessPartialSuccess() {
        wrapper2.setApiException(API_EXCEPTION);
        Function<List<EmployeeWageWorkRulesDTO>, List<EmployeeWageWorkRulesWrapper>> processor
                = (employeeWageWorkRulesDTO -> Lists.newArrayList(wrapper1, wrapper2));
        MultiUpsertWageWorkRuleOverrideBulkProcessor bulkProcessor =
                new MultiUpsertWageWorkRuleOverrideBulkProcessor(employeeWageWorkRules, processor);
        List<EmployeeWageWorkRulesDTO> result = null;
        try {
            result = bulkProcessor.process();
        } catch (APIException apiException) {
            assertEquals(PARTIAL_SUCCESS, apiException.getErrorCode());
            assertFalse(apiException.getResults().isEmpty());
        }
        assertNull(result);
    }

    @Test
    public void testProcessAllError() {
        wrapper1.setApiException(API_EXCEPTION);
        wrapper2.setApiException(API_EXCEPTION);
        Function<List<EmployeeWageWorkRulesDTO>, List<EmployeeWageWorkRulesWrapper>> processor
                = (employeeWageWorkRulesDTO -> Lists.newArrayList(wrapper1, wrapper2));
        MultiUpsertWageWorkRuleOverrideBulkProcessor bulkProcessor =
                new MultiUpsertWageWorkRuleOverrideBulkProcessor(employeeWageWorkRules, processor);
        List<EmployeeWageWorkRulesDTO> result = null;
        try {
            result = bulkProcessor.process();
        } catch (APIException apiException) {
            assertEquals(ALL_RECORDS_FAILED, apiException.getErrorCode());
            assertFalse(apiException.getResults().isEmpty());
        }
        assertNull(result);
    }

    private void initializeEmployeeWageWorkRulesDTOs() {
        dto1 = new EmployeeWageWorkRulesDTO();
        dto1.setEmployee(new ObjectRef(PERSON_ID_1, PERSON_NUMBER_1));
        dto2 = new EmployeeWageWorkRulesDTO();
        dto2.setEmployee(new ObjectRef(PERSON_ID_2, PERSON_NUMBER_2));
    }

    private void initializeEmployeeWageWorkRulesWrappers() {
        wrapper1 = new EmployeeWageWorkRulesWrapper();
        wrapper1.setDTO(dto1);
        wrapper2 = new EmployeeWageWorkRulesWrapper();
        wrapper2.setDTO(dto2);
    }
}
