/*******************************************************************************
 * PersonDeleteRetryDataAccessServiceImplTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.deleteretry.service;

import com.kronos.persons.deleteretry.model.PersonDeleteRetryEntity;
import com.kronos.persons.deleteretry.repository.PersonDeleteRetryRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

/**
 * Test class for PersonDeleteRetryDataAccessServiceImpl
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class PersonDeleteRetryDataAccessServiceImplTest {

    private static final int DEFAULT_RETRY_COUNT = 1;

    private static final BigInteger PERSON_ID = BigInteger.ONE;

    private static final Integer THRESHOLD = 3;

    private static final String EXTERNAL_TENANT_NAME = "DEFAULT";

    @InjectMocks
    private PersonDeleteRetryDataAccessServiceImpl personDeleteRetryDataAccessService;

    @Mock
    private PersonDeleteRetryRepository personDeleteRetryRepository;

    @Test
    public void testUpdatePersonRetryCount() {
        int expectedPersonDeleteRetryCount = 2;
        Mockito.when(personDeleteRetryRepository.updatePersonRetryCount(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(expectedPersonDeleteRetryCount);
        int updatedPersonDeleteRetryCount = personDeleteRetryDataAccessService.updatePersonRetryCount(PERSON_ID, DEFAULT_RETRY_COUNT, Timestamp.valueOf(LocalDateTime.now(ZoneOffset.UTC)));
        assertEquals(expectedPersonDeleteRetryCount, updatedPersonDeleteRetryCount);
    }

    @Test
    public void testGetPersonIdsToDelete() {
        List<PersonDeleteRetryEntity> inputData = new ArrayList<>();
        PersonDeleteRetryEntity personDeleteRetry = getPersonDeleteRetryEntity();
        inputData.add(personDeleteRetry);
        Mockito.when(personDeleteRetryRepository.getPersonIdToDelete(Mockito.anyInt(), Mockito.any())).thenReturn(inputData);
        List<PersonDeleteRetryEntity> outputData = personDeleteRetryDataAccessService.getPersonIdsToDelete(THRESHOLD, Timestamp.valueOf(LocalDateTime.now(ZoneOffset.UTC)));
        assertEquals(inputData.size(), outputData.size());
        assertEquals(inputData.get(0).getPersonId(), outputData.get(0).getPersonId());
    }

    @Test
    public void testSave() {
        PersonDeleteRetryEntity entityToSave = getPersonDeleteRetryEntity();
        Mockito.when(personDeleteRetryRepository.save(Mockito.any())).thenReturn(entityToSave);
        personDeleteRetryDataAccessService.save(entityToSave);
        Mockito.verify(personDeleteRetryRepository).save(entityToSave);
    }

    @Test
    public void testDelete() {
        PersonDeleteRetryEntity entityToDelete = getPersonDeleteRetryEntity();
        Mockito.doNothing().when(personDeleteRetryRepository).delete(Mockito.any());
        personDeleteRetryDataAccessService.delete(entityToDelete);
        Mockito.verify(personDeleteRetryRepository, Mockito.times(1)).delete(entityToDelete);
    }

    @Test
    public void testRemoveDeletedPerson() {
        Mockito.doNothing().when(personDeleteRetryRepository).removeDeletedPerson();
        personDeleteRetryDataAccessService.removeDeletedPerson();
        Mockito.verify(personDeleteRetryRepository, Mockito.times(1)).removeDeletedPerson();
    }

    @Test
    public void testRemoveOldRecords() {
        Timestamp inputTimestamp = Timestamp.valueOf(LocalDateTime.now(ZoneOffset.UTC));
        Mockito.doNothing().when(personDeleteRetryRepository).removeOldRecords(Mockito.any());
        personDeleteRetryDataAccessService.removeOldRecords(inputTimestamp);
        Mockito.verify(personDeleteRetryRepository, Mockito.times(1)).removeOldRecords(inputTimestamp);
    }

    private PersonDeleteRetryEntity getPersonDeleteRetryEntity() {
        PersonDeleteRetryEntity personDeleteRetry = new PersonDeleteRetryEntity();
        personDeleteRetry.setDeletedBy(EXTERNAL_TENANT_NAME);
        personDeleteRetry.setPersonId(PERSON_ID);
        personDeleteRetry.setRetryCount(DEFAULT_RETRY_COUNT);
        return personDeleteRetry;
    }
}
