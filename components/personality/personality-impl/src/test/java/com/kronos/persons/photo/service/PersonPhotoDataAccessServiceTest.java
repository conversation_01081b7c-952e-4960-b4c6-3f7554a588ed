package com.kronos.persons.photo.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.kronos.persons.photo.entity.EmpPhoto;
import com.kronos.persons.photo.repository.PersonPhotoImageRepository;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class PersonPhotoDataAccessServiceTest {
	
	@InjectMocks
	PersonPhotoDataAccessService personPhotoDataAccessService;
	
	@Mock
	PersonPhotoImageRepository empPhotoRepository;
	
	private final Long person = 123L;
	
	@Test
	public void testFindByPersonIdList() {
		List<Long> personIds = getPersonIds();
//		PowerMockito.when(empPhotoRepository.findAllById(Mockito.anyList())).thenReturn(response());
		Mockito.when(empPhotoRepository.findAllById(Mockito.anyList())).thenReturn(response());
		List<EmpPhoto> responseList = personPhotoDataAccessService.findByPersonIdList(personIds);
		assertEquals(person,responseList.get(0).getPersonid());
	}
	
	@Test
	public void testGetEmpPhotoListByPersonIdsAndUpdateDtm() {
		List<Long> personIds = getPersonIds();
		LocalDateTime localDateTime = LocalDateTime.now();
//		PowerMockito.when(empPhotoRepository.getEmpPhotoListByPersonIdsAndUpdateDtm(personIds,localDateTime)).thenReturn(response());
		Mockito.when(empPhotoRepository.getEmpPhotoListByPersonIdsAndUpdateDtm(personIds,localDateTime)).thenReturn(response());
		List<EmpPhoto> responseList = personPhotoDataAccessService.getEmpPhotoListByPersonIdsAndUpdateDtm(personIds,localDateTime);
		assertEquals(person,responseList.get(0).getPersonid());

	}
	
	List<Long> getPersonIds() {
		List<Long> personIds = new ArrayList<>(); 
		personIds.add(person);
		return personIds;
	}
	
	List<EmpPhoto> response() {
		List<EmpPhoto> empPhotoList = new ArrayList<>();
		EmpPhoto empPhoto = new EmpPhoto();
		empPhoto.setPersonid(person);
		empPhotoList.add(empPhoto);
		return empPhotoList;
		
	}

}
