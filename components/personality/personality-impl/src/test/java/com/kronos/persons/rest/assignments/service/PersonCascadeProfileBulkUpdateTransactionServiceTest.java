package com.kronos.persons.rest.assignments.service;

import com.google.common.collect.Lists;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonNotification;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.SQLStatement;
import com.kronos.wfc.timekeeping.cascade.business.config.CascadeProfile;
import com.kronos.wfc.timekeeping.cascade.business.people.PersonCascadeProfileAssignment;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonCascadeProfileBulkUpdateTransactionServiceTest {

    private static final ObjectIdLong CASCADE_PROFILE_ID = new ObjectIdLong(1L);
    private static final ObjectIdLong PERSON_CASCADE_PROFILE_ASSIGNMENT_ID = new ObjectIdLong(2L);

    private PersonCascadeProfileBulkUpdateTransactionService transactionService;

    @Mock
    private SQLStatement sqlStatement;

    @Mock
    private PersonCascadeProfileAssignment personCascadeProfileAssignment;

    @Mock
    private CascadeProfile cascadeProfile;


    private void initTransactionService() {
        transactionService = PersonCascadeProfileBulkUpdateTransactionService
                .getTransactionService(cascadeProfile, Lists.newArrayList(personCascadeProfileAssignment), false);
    }

    private void initTransactionServiceSetNotifyPersonCacheTrue() {
        transactionService = PersonCascadeProfileBulkUpdateTransactionService
                .getTransactionService(cascadeProfile, Lists.newArrayList(personCascadeProfileAssignment), true);
    }

    @Test
    public void testTransaction() throws Exception {
        initTransactionService();
        when(personCascadeProfileAssignment.getId()).thenReturn(PERSON_CASCADE_PROFILE_ASSIGNMENT_ID);
        when(cascadeProfile.getId()).thenReturn(CASCADE_PROFILE_ID);

        try (var mockedPersonNotification = mockStatic(PersonNotification.class)) {
            mockedPersonNotification.when(() -> PersonNotification.sendUpdate(any(ObjectIdLong.class))).thenAnswer(invocation -> null);

            transactionService.transaction();

            verify(cascadeProfile, times(1)).getId();
            verify(personCascadeProfileAssignment, times(1)).getId();
            verify(sqlStatement, times(0)).execute();
        }
    }

    @Test
    public void testTransactionWhenNotifyPersonCacheIsTrue() {
        mockConstruction(SQLStatement.class, (mock, context) -> {
            doNothing().when(mock).execute();
        });
        initTransactionServiceSetNotifyPersonCacheTrue();
        when(personCascadeProfileAssignment.getId()).thenReturn(PERSON_CASCADE_PROFILE_ASSIGNMENT_ID);
        when(cascadeProfile.getId()).thenReturn(CASCADE_PROFILE_ID);

        try (var mockedPersonNotification = mockStatic(PersonNotification.class)) {
            mockedPersonNotification.when(() -> PersonNotification.sendUpdate(any(ObjectIdLong.class))).thenAnswer(invocation -> null);

            transactionService.transaction();

            verify(cascadeProfile, times(1)).getId();
            verify(personCascadeProfileAssignment, times(1)).getId();
            verify(sqlStatement, times(0)).execute();
        }
    }
}