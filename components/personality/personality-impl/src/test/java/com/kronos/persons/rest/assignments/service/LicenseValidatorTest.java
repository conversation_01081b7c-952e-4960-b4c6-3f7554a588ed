/*******************************************************************************
 * LicenseValidatorTest.java
 * Copyright 2024, UKG.com. All rights reserved.
 ******************************************************************************/
package com.kronos.persons.rest.assignments.service;

import com.kronos.container.api.exception.APIException;
import com.kronos.licensing.api.services.LicenseManager;
import com.kronos.people.personality.service.PersonalityService;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.wfc.commonapp.people.business.person.PersonLicenseType;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.licensing.framework.LicenseType;
import com.kronos.wfc.platform.licensing.framework.LicenseTypeCache;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.shared.SecurityConstants;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> castiblanco
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class LicenseValidatorTest {

    @InjectMocks
    private LicenseValidator licenseValidator;

    @Mock
    private LicenseManager licenseManager;

    @Mock
    private PersonalityService personalityService;

    @Mock
    private Personality personality;

    @Mock
    private LicenseType licenseType;

    @Mock
    private PersonLicenseType personLicenseType;

    private MockedStatic<LicenseType> mockedLicenseType;
    private MockedStatic<Personality> mockedPersonality;

    @BeforeEach
    public void setUp() {
        Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
        mockedLicenseType = Mockito.mockStatic(LicenseType.class);
        mockedPersonality = Mockito.mockStatic(Personality.class);
    }

    @AfterEach
    public void tearDown() {
        mockedLicenseType.close();
        mockedPersonality.close();
        Mockito.clearAllCaches();
        Mockito.framework().clearInlineMocks();
    }

    @Test
    public void validateLicense() {
        Mockito.when(licenseManager.hasLBBAccessForPerson(Mockito.anyString(), Mockito.anyLong())).thenReturn(true);
        licenseValidator.validateLicense(personality, SecurityConstants.WORKFORCE_ATTENDANCE);
        Mockito.verify(licenseManager).hasLBBAccessForPerson(Mockito.anyString(), Mockito.anyLong());
    }

    @Test
    public void validateLicenseWhenLBBAccessForPersonIsTrue() {
        Mockito.when(licenseManager.hasLBBAccessForPerson(Mockito.anyString(), Mockito.anyLong())).thenReturn(true);
        licenseValidator.validateLeaveLicense(personality.getPersonId(), true);
        Mockito.verify(licenseManager).hasLBBAccessForPerson(Mockito.anyString(), Mockito.anyLong());
    }

    @Test
    public void givenLicenseIsFalse_whenValidateLeaveLicenseIsWorkforceManager_thenReturnException() {
        Mockito.when(Personality.getByPersonId(Mockito.any())).thenReturn(personality);
        Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
        Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(personLicenseType);
        Mockito.when(personLicenseType.getActive()).thenReturn(false);
        APIException thrown = Assertions.assertThrows(APIException.class, () -> {
            licenseValidator.validateLeaveLicense(personality.getPersonId(), false);
        });
        assertEquals(ExceptionConstants.USER_DOESNT_HAVE_PERMISSION, thrown.getErrorCode());
    }

    @Test
    public void givenLicenseIsTrue_whenValidateLeaveLicenseIsWorkforceManager_thenNoReturnException() {
        Mockito.when(Personality.getByPersonId(Mockito.any())).thenReturn(personality);
        Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenseType);
        Mockito.when(personality.getLicenseType(Mockito.any())).thenReturn(personLicenseType);
        Mockito.when(personLicenseType.getActive()).thenReturn(true);
        licenseValidator.validateLeaveLicense(personality.getPersonId(), false);
        Mockito.verify(personLicenseType).getActive();
    }

    @Test
    public void validateLicenseForLBBAccessForPersonIsFalse() {
        try {
            Mockito.when(licenseManager.hasLBBAccessForPerson(Mockito.anyString(), Mockito.anyLong())).thenReturn(false);
            licenseValidator.validateLeaveLicense(personality.getPersonId(), true);
        } catch (APIException e) {
            Mockito.verify(licenseManager).hasLBBAccessForPerson(Mockito.anyString(), Mockito.anyLong());
            assertEquals(ExceptionConstants.EMPLOYEE_DOES_NOT_HAVE_WFL_LICENSE, e.getErrorCode());
        }
    }

    @Test
    public void validateLicenseNullPersonality() {
        licenseValidator.validateLicense(null, SecurityConstants.WORKFORCE_ATTENDANCE);
    }

    @Test
    public void validateLicenseExceptoin() {
        Mockito.when(licenseManager.hasLBBAccessForPerson(Mockito.anyString(), Mockito.anyLong())).thenReturn(false);
        try {
            licenseValidator.validateLicense(personality, SecurityConstants.WORKFORCE_ATTENDANCE);
        } catch (APIException e) {
            Mockito.verify(licenseManager).hasLBBAccessForPerson(Mockito.anyString(), Mockito.anyLong());
            assertTrue(true);
        }
    }

    @Test
    public void validateManagerRole() {
        Mockito.when(personalityService.isManager(Mockito.anyLong())).thenReturn(true);
        licenseValidator.validateManagerRole(personality);
        Mockito.verify(personalityService).isManager(Mockito.anyLong());
    }

    @Test
    public void validateManagerRoleException() {
        assertThrows(APIException.class, () -> {
            Mockito.when(personalityService.isManager(Mockito.anyLong())).thenReturn(false);
            licenseValidator.validateManagerRole(personality);
            Mockito.verify(personalityService).isManager(Mockito.anyLong());
        });
    }

    @Test
    public void validateManagerRoleNullPersonality() {
        licenseValidator.validateManagerRole(null);
    }
}
