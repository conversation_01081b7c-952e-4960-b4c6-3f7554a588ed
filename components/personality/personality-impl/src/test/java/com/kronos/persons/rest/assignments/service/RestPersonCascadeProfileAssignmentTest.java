package com.kronos.persons.rest.assignments.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.commons.lang3.math.NumberUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import com.google.common.collect.Lists;
import com.kronos.container.api.exception.APIException;
import com.kronos.people.assignment.cache.api.IAssignmentCacheNotifier;
import com.kronos.persons.rest.assignments.model.AssignmentProfileRequestBean;
import com.kronos.persons.rest.assignments.model.AssignmentProfileRequestBeanWrapper;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.PersistenceException;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class RestPersonCascadeProfileAssignmentTest {
	
	@InjectMocks
	RestPersonCascadeProfileAssignment restPersonCascadeProfileAssignment;
	@Mock
	PersonCascadeProfileAssignmentService personCascadeServiceImpl;
	@Mock
	PersonAssignmentHelper<AssignmentProfileRequestBean> personAssignmentHelper;
	String personNumber="123";
	private Long personId =10L;
	AssignmentProfileRequestBean requestBean = new AssignmentProfileRequestBean();
	AssignmentProfileRequestBean requestBean2 = new AssignmentProfileRequestBean();
	@Mock
	private Personality personality;
	@Mock
	private ExtensionSearchCriteria extensionCriteria;

	@Mock
	IAssignmentCacheNotifier assignmentCacheNotifier;

	private MockedStatic<PrsnException> mockedPrsnException;
	
	@BeforeEach
	public void setup() {
		mockedPrsnException = Mockito.mockStatic(PrsnException.class);
		Mockito.when(personality.getPersonId()).thenReturn(new ObjectIdLong(1L));
	}

	@AfterEach
	public void tearDown() {
		mockedPrsnException.close();
	}
	
	@Test
	public void testRetrieveByPersonNumber() {
		PersonIdentityBean personIdentity= new PersonIdentityBean();
		personIdentity.setPersonNumber(personNumber);
		requestBean.setPersonIdentity(personIdentity);
		Mockito.when(personCascadeServiceImpl.retrieveByPersonNumber(personNumber)).thenReturn(requestBean );
		AssignmentProfileRequestBean response = restPersonCascadeProfileAssignment.retrieveByPersonNumber(personNumber);
		assertNotNull(response);
		assertEquals(personNumber, response.getPersonIdentity().getPersonNumber());
	}
	
	@Test
	public void testRetrieveByPersonNumberShouldThrowGenericException() {
		Mockito.doThrow(new PersistenceException()).when(personCascadeServiceImpl).retrieveByPersonNumber(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.retrieveByPersonNumber(personNumber);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testRetrieveByPersonNumberThrowException() {
		Mockito.doThrow(new RuntimeException()).when(personCascadeServiceImpl).retrieveByPersonNumber(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.retrieveByPersonNumber(personNumber);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testRetrieveByPersonNumberThrowAPIException() {
		Mockito.doThrow(new APIException()).when(personCascadeServiceImpl).retrieveByPersonNumber(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.retrieveByPersonNumber(personNumber);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testRetrieveByPersonId() {
		PersonIdentityBean personIdentity= new PersonIdentityBean();
		personIdentity.setPersonNumber(personNumber);
		requestBean.setPersonIdentity(personIdentity);
		Mockito.when(personCascadeServiceImpl.retrieveByPersonId(personId)).thenReturn(requestBean );
		AssignmentProfileRequestBean response = restPersonCascadeProfileAssignment.retrieveByPersonId(personId );
		assertNotNull(response);
		assertEquals(personNumber, response.getPersonIdentity().getPersonNumber());
	}
	
	@Test
	public void testRetrieveByPersonIdShouldThrowGenericException() {
		Mockito.doThrow(new PersistenceException()).when(personCascadeServiceImpl).retrieveByPersonId(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.retrieveByPersonId(personId);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testRetrieveByPersonIdThrowException() {
		Mockito.doThrow(new RuntimeException()).when(personCascadeServiceImpl).retrieveByPersonId(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.retrieveByPersonId(personId);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testRetrieveByPersonIdThrowAPIException() {
		Mockito.doThrow(new APIException()).when(personCascadeServiceImpl).retrieveByPersonId(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.retrieveByPersonId(personId);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
	}
	
	@Test
	public void testUpdate() {
		PersonIdentityBean personIdentity= new PersonIdentityBean();
		personIdentity.setPersonNumber(personNumber);
		requestBean.setPersonIdentity(personIdentity);
		Mockito.when(personCascadeServiceImpl.updateRequest(Mockito.any())).thenReturn(personality);
		AssignmentProfileRequestBean response = restPersonCascadeProfileAssignment.update(requestBean );
		assertNotNull(response);
		assertEquals(personNumber, response.getPersonIdentity().getPersonNumber());
		Mockito.verify(assignmentCacheNotifier, Mockito.times(1)).sendMessageByPersonID(Mockito.anyString(),
				Mockito.anyList());
	}
	
	@Test
	public void testUpdateShouldThrowGenericException() {
		Mockito.doThrow(new PersistenceException()).when(personCascadeServiceImpl).updateRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.update(requestBean);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
		Mockito.verify(assignmentCacheNotifier, Mockito.times(0)).sendMessageByPersonID(Mockito.anyString(),
				Mockito.anyList());
	}
	
	@Test
	public void testUpdateThrowException() {
		Mockito.doThrow(new RuntimeException()).when(personCascadeServiceImpl).updateRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.update(requestBean);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
		Mockito.verify(assignmentCacheNotifier, Mockito.times(0)).sendMessageByPersonID(Mockito.anyString(),
				Mockito.anyList());
	}
	
	@Test
	public void testUpdateThrowAPIException() {
		Mockito.doThrow(new APIException()).when(personCascadeServiceImpl).updateRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.update(requestBean);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
		Mockito.verify(assignmentCacheNotifier, Mockito.times(0)).sendMessageByPersonID(Mockito.anyString(),
				Mockito.anyList());
	}
	
	
	@Test
	public void testDelete() {
		PersonIdentityBean personIdentity= new PersonIdentityBean();
		personIdentity.setPersonNumber(personNumber);
		requestBean.setPersonIdentity(personIdentity);
		Mockito.when(personCascadeServiceImpl.deleteRequest(Mockito.any())).thenReturn(personality);
		restPersonCascadeProfileAssignment.delete(requestBean );
		Mockito.verify(personCascadeServiceImpl,Mockito.times(1)).deleteRequest(Mockito.any());
		Mockito.verify(assignmentCacheNotifier, Mockito.times(1)).sendMessageByPersonID(Mockito.anyString(),
				Mockito.anyList());
	}
	
	@Test
	public void testDeleteShouldThrowGenericException() {
		Mockito.doThrow(new PersistenceException()).when(personCascadeServiceImpl).deleteRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.delete(requestBean);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
		Mockito.verify(assignmentCacheNotifier, Mockito.times(0)).sendMessageByPersonID(Mockito.anyString(),
				Mockito.anyList());
	}
	
	@Test
	public void testDeleteThrowException() {
		Mockito.doThrow(new RuntimeException()).when(personCascadeServiceImpl).deleteRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.delete(requestBean);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
		Mockito.verify(assignmentCacheNotifier, Mockito.times(0)).sendMessageByPersonID(Mockito.anyString(),
				Mockito.anyList());
	}
	
	@Test
	public void testDeleteThrowAPIException() {
		Mockito.doThrow(new APIException()).when(personCascadeServiceImpl).deleteRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.delete(requestBean);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
		Mockito.verify(assignmentCacheNotifier, Mockito.times(0)).sendMessageByPersonID(Mockito.anyString(),
				Mockito.anyList());
	}
	
	@Test
	public void testMultiUpdateValidBean() {
		PersonIdentityBean personIdentity = new PersonIdentityBean();
		personIdentity.setPersonNumber(personNumber);
		requestBean.setPersonIdentity(personIdentity);
		AssignmentProfileRequestBeanWrapper beanWrapper = new AssignmentProfileRequestBeanWrapper(requestBean);
		List<AssignmentProfileRequestBeanWrapper> listForUpdate = Lists.newArrayList(beanWrapper);
		doNothing().when(personCascadeServiceImpl).validate(eq(beanWrapper));
		doNothing().when(personCascadeServiceImpl).multiUpdate(eq(listForUpdate));
		List<AssignmentProfileRequestBean> requestList = Lists.newArrayList(requestBean);
		List<AssignmentProfileRequestBean> responseList = restPersonCascadeProfileAssignment
				.multiUpdate(requestList);
		assertNotNull(responseList);
		assertNull(beanWrapper.getApiException());
		assertEquals(personNumber, responseList.get(0).getPersonIdentity().getPersonNumber());
		verify(personCascadeServiceImpl, times(NumberUtils.INTEGER_ONE)).validate(eq(beanWrapper));
		verify(personCascadeServiceImpl, times(NumberUtils.INTEGER_ONE)).multiUpdate(listForUpdate);
	}

	@Test
	public void testMultiUpdateInvalidBean() {
		PersonIdentityBean personIdentity = new PersonIdentityBean();
		personIdentity.setPersonNumber(personNumber);
		requestBean.setPersonIdentity(personIdentity);
		AssignmentProfileRequestBeanWrapper beanWrapper = new AssignmentProfileRequestBeanWrapper(requestBean);
		/*doAnswer(invocation -> {
			invocation.getArgumentAt(0, AssignmentProfileRequestBeanWrapper.class)
					.setApiException(new APIException());
			return invocation;
		}).when(personCascadeServiceImpl).validate(eq(beanWrapper));*/		//Todo

		List<AssignmentProfileRequestBean> requestList = Lists.newArrayList(requestBean);
		try {
			restPersonCascadeProfileAssignment.multiUpdate(requestList);
		} catch (APIException apiException) {
			assertEquals(ExceptionConstants.ALL_RECORDS_FAILED, apiException.getErrorCode());
			verify(personCascadeServiceImpl, times(NumberUtils.INTEGER_ONE))
					.multiUpdate(eq(Collections.emptyList()));
		}
	}

	@Test
	public void testMultiDelete() {
		PersonIdentityBean personIdentity= new PersonIdentityBean();
		personIdentity.setPersonNumber(personNumber);
		personIdentity.setPersonKey(1L);
		requestBean.setPersonIdentity(personIdentity);
		PersonIdentityBean personIdentity2 = new PersonIdentityBean();
		personIdentity2.setPersonNumber("1234");
		requestBean2.setPersonIdentity(personIdentity2);
		Mockito.when(personCascadeServiceImpl.deleteRequest(Mockito.any())).thenReturn(personality);
		List<AssignmentProfileRequestBean> requestList = new ArrayList<>();
		requestList.add(requestBean);
		requestList.add(requestBean2);
		restPersonCascadeProfileAssignment.multiDelete(requestList);
		Mockito.verify(personCascadeServiceImpl,Mockito.times(2)).deleteRequest(Mockito.any());
		Mockito.verify(assignmentCacheNotifier, Mockito.times(1)).sendMessageByPersonID(Mockito.anyString(),
				Mockito.anyList());
	}
	
	
	@Test
	public void testMultiDeleteShouldThrowGenericException() {
		Mockito.doThrow(new PersistenceException()).when(personCascadeServiceImpl).deleteRequest(Mockito.any());
		List<AssignmentProfileRequestBean> requestList = new ArrayList<>();
		requestList.add(requestBean);
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(GenericException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.multiDelete(requestList);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
		Mockito.verify(assignmentCacheNotifier, Mockito.times(1)).sendMessageByPersonID(Mockito.anyString(),
				Mockito.anyList());
	}
	
	@Test
	public void testMultiDeleteThrowException() {
		List<AssignmentProfileRequestBean> requestList = new ArrayList<>();
		requestList.add(requestBean);
		Mockito.doThrow(new RuntimeException()).when(personCascadeServiceImpl).deleteRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.multiDelete(requestList);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
		Mockito.verify(assignmentCacheNotifier, Mockito.times(1)).sendMessageByPersonID(Mockito.anyString(),
				Mockito.anyList());
	}
	
	@Test
	public void testMultiDeleteThrowAPIException() {
		List<AssignmentProfileRequestBean> requestList = new ArrayList<>();
		requestList.add(requestBean);
		Mockito.doThrow(new APIException()).when(personCascadeServiceImpl).deleteRequest(Mockito.any());
		mockedPrsnException.when(() -> PrsnException.getAPIException(Mockito.any(RuntimeException.class))).thenReturn(new APIException());
		try {
			restPersonCascadeProfileAssignment.multiDelete(requestList);
			fail();
		}catch (Exception e){
			if (! (e instanceof APIException))
					fail();
		}
		Mockito.verify(assignmentCacheNotifier, Mockito.times(1)).sendMessageByPersonID(Mockito.anyString(),
				Mockito.anyList());
	}
	
	@Test
	public void testRetrieveList() {
		List<AssignmentProfileRequestBean> requestList = new ArrayList<>();
		requestList.add(requestBean);
		Mockito.when(personAssignmentHelper.getPersonAssignmentList(Mockito.any(), Mockito.any())).thenReturn(requestList);
		List<AssignmentProfileRequestBean> responseList = restPersonCascadeProfileAssignment.retrieveList(extensionCriteria);
		assertEquals(1, responseList.size());
	}

}
