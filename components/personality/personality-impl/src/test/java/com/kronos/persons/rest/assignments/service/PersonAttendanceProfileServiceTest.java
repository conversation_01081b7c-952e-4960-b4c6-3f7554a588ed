package com.kronos.persons.rest.assignments.service;

import com.kronos.container.api.access.SpringContext;
import com.kronos.container.api.exception.APIException;
import com.kronos.licensing.api.services.LicenseManager;
import com.kronos.people.personality.dataaccess.adapter.AdapterHelper;
import com.kronos.persons.rest.assignments.model.PersonAttendanceProfileAssignmentBean;
import com.kronos.persons.rest.assignments.model.PersonAttendanceProfileBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.wfc.absencemgmt.attendance.business.config.Profile;
import com.kronos.wfc.absencemgmt.service.business.people.AttendanceProfileAssignmentList;
import com.kronos.wfc.absencemgmt.service.business.people.PersonAttendanceProfileAssignment;
import com.kronos.wfc.absencemgmt.service.business.people.PersonAttendanceProfileAssignmentListGuest;
import com.kronos.wfc.commonapp.people.business.person.PersonLicenseType;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.datetime.framework.KServer;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.licensing.framework.LicenseType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.platform.xml.api.bean.APIPersistenceException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class PersonAttendanceProfileServiceTest {

	@InjectMocks
	private PersonAttendanceProfileService employeeAssignmentService;
	@Spy
	private ValidatorUtils validatorUtils = new ValidatorUtils();
	@Mock
	private PersonIdentityBeanValidator personIdentityBeanValidator;
	private PersonAttendanceProfileBean requestBean;
	private PersonIdentityBean employee;
	@Mock
	private Personality personality;
	@Mock
	private PersonAttendanceProfileAssignmentListGuest guest;
	@Mock
	private AttendanceProfileAssignmentList currentAssignments;
	@Mock
	private LicenseType licenceType;
	@Mock
	private PersonLicenseType personLicenseType;
	@Mock
	private Profile profile;
	@Mock
	KServer kserver;
	public static final String PROFILE_NAME = "ProfileName";
	@Mock
	private AdapterHelper adapterHelper;
	@Mock
	private LicenseManager licenseManager;
	
    @Mock
	LicenseValidator licenseValidator;

	private MockedStatic<SpringContext> springContextMockedStatic;
	private MockedStatic<Profile> profileMockedStatic;
	private MockedStatic<AttendanceProfileAssignmentList> attendanceProfileAssignmentListMockedStatic;
	private MockedStatic<PersonAttendanceProfileAssignmentListGuest> personAttendanceProfileAssignmentListGuestMockedStatic;
	private MockedStatic<APIPersistenceException> apiPersistenceExceptionMockedStatic;
	private MockedStatic<LicenseType> licenseTypeMockedStatic;

	@BeforeEach
	public void setUp() throws Exception {
		requestBean = new PersonAttendanceProfileBean();
		employee = new PersonIdentityBean();
		springContextMockedStatic = Mockito.mockStatic(SpringContext.class);
		profileMockedStatic = Mockito.mockStatic(Profile.class);
		attendanceProfileAssignmentListMockedStatic = Mockito.mockStatic(AttendanceProfileAssignmentList.class);
		personAttendanceProfileAssignmentListGuestMockedStatic = Mockito.mockStatic(PersonAttendanceProfileAssignmentListGuest.class);
		apiPersistenceExceptionMockedStatic = Mockito.mockStatic(APIPersistenceException.class);
		licenseTypeMockedStatic = Mockito.mockStatic(LicenseType.class);
	}

	@AfterEach
	public void tearDown() throws Exception {
		requestBean = null;
		employee = null;
		springContextMockedStatic.close();
		profileMockedStatic.close();
		attendanceProfileAssignmentListMockedStatic.close();
		personAttendanceProfileAssignmentListGuestMockedStatic.close();
		apiPersistenceExceptionMockedStatic.close();
		licenseTypeMockedStatic.close();
	}

	@Test
	public void testUpdateRequestshouldThrowExceptionForNullEmployee() {
		try {
			mockCurrentAssignmentList();
			employeeAssignmentService.updateRequest(requestBean);
		} catch (APIException e) {
			assertEquals(ExceptionConstants.MISSING_PROPERTY,e.getErrorCode());
		}
	}

	@Test
	public void testUpdateRequestshouldThrowExceptionForInvalidBadgeNumberForEmployee() {
		try {
			mockCurrentAssignmentList();
			requestBean.setPersonIdentity(employee);
			employee.setBadgeNumber("InvalidbadgeNumber");
			apiPersistenceExceptionMockedStatic.when(() -> APIPersistenceException.personNotFound(employee.getBadgeNumber()))
					.thenCallRealMethod();
			APIPersistenceException exception = APIPersistenceException.personNotFound(employee.getBadgeNumber());
			when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity()))
					.thenThrow(exception);
			mockKeyValue(PersonIdentityBeanValidator.BADGE_NUMBER, "InvalidbadgeNumber");
			employeeAssignmentService.updateRequest(requestBean);
		} catch (GenericException e) {
			assertTrue(isValidException(e, APIPersistenceException.PERSON_NOT_FOUND, PersonIdentityBeanValidator.BADGE_NUMBER, "InvalidbadgeNumber"));
		}
	}
	
	@Test
	public void testUpdateRequestshouldThrowExceptionForInvalidPersonNumberNumberForEmployee() {
		try {
			mockCurrentAssignmentList();
			requestBean.setPersonIdentity(employee);
			employee.setPersonNumber("InvalidPersonNumber");
			apiPersistenceExceptionMockedStatic.when(()->APIPersistenceException.personNotFound(employee.getPersonNumber()))
					.thenCallRealMethod();
			APIPersistenceException exception = APIPersistenceException.personNotFound(employee.getPersonNumber());
			when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity()))
					.thenThrow(exception);
			mockKeyValue(PersonIdentityBeanValidator.PERSON_NUMBER, "InvalidbadgeNumber");
			employeeAssignmentService.updateRequest(requestBean);
		} catch (GenericException e) {
			assertTrue(isValidException(e, APIPersistenceException.PERSON_NOT_FOUND, PersonIdentityBeanValidator.PERSON_NUMBER,"InvalidPersonNumber"));
		}
	}
	
	@Test
	public void testUpdateRequestshouldThrowExceptionForInvalidAoidForEmployee() {
		try {
			mockCurrentAssignmentList();
			requestBean.setPersonIdentity(employee);
			employee.setAoid("InvalidAoid");
			apiPersistenceExceptionMockedStatic.when(()->APIPersistenceException.personNotFound(employee.getAoid()))
					.thenCallRealMethod();
			APIPersistenceException exception = APIPersistenceException.personNotFound(employee.getAoid());
			when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity()))
					.thenThrow(exception);
			mockKeyValue(PersonIdentityBeanValidator.ASSOCIATE_ID, "InvalidAoid");
			employeeAssignmentService.updateRequest(requestBean);
		} catch (GenericException e) {
			assertTrue(isValidException(e, APIPersistenceException.PERSON_NOT_FOUND, PersonIdentityBeanValidator.ASSOCIATE_ID, "InvalidAoid" ));
		}
	}

	private void mockKeyValue(String key, String value) {
		when(personIdentityBeanValidator.getIdentityKey(requestBean.getPersonIdentity())).thenReturn(key);
		when(personIdentityBeanValidator.getIdentityValue(requestBean.getPersonIdentity())).thenReturn(value);
	}
	@Test
	public void testUpdateRequestshouldThrowExceptionForInvalidEmployeeKeyForEmployee() {
		try {
			mockCurrentAssignmentList();
			requestBean.setPersonIdentity(employee);
			employee.setEmployeeKey(10L);
			apiPersistenceExceptionMockedStatic.when(()->APIPersistenceException.personNotFound(employee.getEmployeeKey().toString()))
					.thenCallRealMethod();
			APIPersistenceException exception = APIPersistenceException.personNotFound(employee.getEmployeeKey().toString());
			when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity()))
					.thenThrow(exception);
			mockKeyValue(PersonIdentityBeanValidator.EMPLOYEE_KEY, 10+"");
			employeeAssignmentService.updateRequest(requestBean);
		} catch (GenericException e) {
			assertTrue(isValidException(e, APIPersistenceException.PERSON_NOT_FOUND, PersonIdentityBeanValidator.EMPLOYEE_KEY, 10+""));
		}
	}
	
	@Test
	public void testUpdateRequestshouldThrowExceptionForInvalidUserKeyForEmployee() {
		try {
			mockCurrentAssignmentList();
			requestBean.setPersonIdentity(employee);
			employee.setUserKey(20L);
			apiPersistenceExceptionMockedStatic.when(()->APIPersistenceException.personNotFound(employee.getUserKey().toString()))
					.thenCallRealMethod();
			APIPersistenceException exception = APIPersistenceException.personNotFound(employee.getUserKey().toString());
			when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity()))
					.thenThrow(exception);
			mockKeyValue(PersonIdentityBeanValidator.USER_KEY, 20+"");
			employeeAssignmentService.updateRequest(requestBean);
		} catch (GenericException e) {
			assertTrue(isValidException(e, APIPersistenceException.PERSON_NOT_FOUND, PersonIdentityBeanValidator.USER_KEY, 20+""));
		}
	}

	@Test
	public void testUpdateRequestshouldThrowExceptionForInvalidPersonKeyForEmployee() {
		mockCurrentAssignmentList();
		requestBean.setPersonIdentity(employee);
		employee.setPersonKey(30L);
		String personkey = employee.getPersonKey().toString();
		apiPersistenceExceptionMockedStatic.when(()->APIPersistenceException.personNotFound(personkey))
				.thenCallRealMethod();
		APIPersistenceException ex = APIPersistenceException.personNotFound(personkey);
		Mockito.when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity()))
				.thenThrow(ex);
		mockKeyValue(PersonIdentityBeanValidator.PERSON_KEY, 30 + "");
		GenericException exception = assertThrows(GenericException.class, () -> {
			employeeAssignmentService.updateRequest(requestBean);
		});

		assertTrue(isValidException(exception, APIPersistenceException.PERSON_NOT_FOUND, PersonIdentityBeanValidator.PERSON_KEY, 30 + ""));
	}

	@Test
	
	public void testUpdateRequestshouldPassSuccessfully() {
		requestBean.setPersonIdentity(employee);
		mockCurrentAssignmentList();
		mockActiveLicence();
		when(SpringContext.getBean(LicenseManager.class)).thenReturn(licenseManager);
		try {
			Personality personalityFromUpdate = employeeAssignmentService.updateRequest(requestBean);
		}
		catch(Exception e) {
			assertTrue(!(e instanceof APIException));
		}
	}

	@Test
	public void testUpdateRequestshouldThrowExceptionForNullProfileName() {
		requestBean.setPersonIdentity(employee);
		setEmpProfileAssignment();
		Mockito.when(Profile.getByName(Mockito.anyString())).thenReturn(null);
		mockCurrentAssignmentList();
		mockActiveLicence();
		Mockito.when(AttendanceProfileAssignmentList.isEmptyProfileAssignmentMissing(currentAssignments))
				.thenReturn(true);
		when(personality.getPersonId()).thenReturn(new ObjectIdLong(10L));
		KDate kdate = new KDate();
		when(adapterHelper.localDateToKdate(Mockito.any())).thenReturn(kdate );
		try {
			employeeAssignmentService.updateRequest(requestBean);
		} catch (APIException e) {
			assertEquals(e.getErrorCode(), ExceptionConstants.INVALID_BEAN);
		}
	}
	

	@Test
	public void testUpdateRequestshouldPassSuccessfullyForValidProfile() {
		requestBean.setPersonIdentity(employee);
		setEmpProfileAssignment();
		Mockito.when(Profile.getByName(Mockito.anyString())).thenReturn(profile);
		when(profile.getId()).thenReturn(new ObjectIdLong(10L));
		mockCurrentAssignmentList();
		Mockito.when(SpringContext.getBean(LicenseManager.class)).thenReturn(licenseManager);
		mockActiveLicence();
		Mockito.when(AttendanceProfileAssignmentList.isEmptyProfileAssignmentMissing(currentAssignments))
				.thenReturn(true);
		when(personality.getPersonId()).thenReturn(new ObjectIdLong(10L));
		KDate kdate = new KDate();
		when(adapterHelper.localDateToKdate(Mockito.any())).thenReturn(kdate );
		try {
			Personality currentPersonality = employeeAssignmentService.updateRequest(requestBean);
		}
		catch(Exception e) {
			assertTrue(!(e instanceof APIException));
		}
	}

	@Test
	public void testRetrieveShouldTrhowExceptionForNullAttendanceProfileId() {

		requestBean.setPersonIdentity(employee);
		Mockito
				.when(PersonAttendanceProfileAssignmentListGuest.getSetForPersonality(Mockito.any(Personality.class)))
				.thenReturn(guest);
		List currentEmpAssignment = createPersonAttendanceProfileAssignmentList();
		when(personIdentityBeanValidator.getPersonality(employee)).thenReturn(personality);
		String personNumber = "12345";
		when(personality.getPersonNumber()).thenReturn(personNumber);
		when(guest.getAttendanceProfileAssignmentSet()).thenReturn(currentAssignments);
        when(currentAssignments.getAllMembers()).thenReturn(currentEmpAssignment);
		try {
			employeeAssignmentService.retrieve(employee);
		} catch (APIException e) {
			assertEquals(e.getErrorCode(), ExceptionConstants.PROFILE_DOES_NOT_EXIST);
		}
	}

	@Test
	public void testRetrieveShouldPassSuccessfully() {

		Mockito
				.when(PersonAttendanceProfileAssignmentListGuest.getSetForPersonality(Mockito.any(Personality.class)))
				.thenReturn(guest);
		List currentEmpAssignment = createPersonAttendanceProfileAssignmentList();
		((PersonAttendanceProfileAssignment) currentEmpAssignment.get(0)).setAttendanceProfileId(new ObjectIdLong(10L));
		when(personIdentityBeanValidator.getPersonality(employee)).thenReturn(personality);
		String personNumber = "12345";
		when(personality.getPersonNumber()).thenReturn(personNumber);
		when(guest.getAttendanceProfileAssignmentSet()).thenReturn(currentAssignments);
		when(currentAssignments.getAllMembers()).thenReturn(currentEmpAssignment);
		PersonAttendanceProfileBean watProfile = employeeAssignmentService.retrieve(employee);
		assertNotNull(watProfile);
		assertEquals(personNumber,watProfile.getPersonIdentity().getPersonNumber());
	}

	@Test
	public void testRetrieveShouldPassSuccessfullyAndSetProfileName() {

		Mockito
				.when(PersonAttendanceProfileAssignmentListGuest.getSetForPersonality(Mockito.any(Personality.class)))
				.thenReturn(guest);
		List currentEmpAssignment = createPersonAttendanceProfileAssignmentList();
		((PersonAttendanceProfileAssignment) currentEmpAssignment.get(0)).setAttendanceProfileId(new ObjectIdLong(10L));
		when(personIdentityBeanValidator.getPersonality(employee)).thenReturn(personality);
		Mockito.when(Profile.getById(Mockito.any(ObjectIdLong.class))).thenReturn(profile);
		when(profile.getName()).thenReturn("ProfileName");
		String personNumber = "12345";
		when(personality.getPersonNumber()).thenReturn(personNumber);
		when(guest.getAttendanceProfileAssignmentSet()).thenReturn(currentAssignments);
        when(currentAssignments.getAllMembers()).thenReturn(currentEmpAssignment);
		PersonAttendanceProfileBean watProfile = employeeAssignmentService.retrieve(employee);
		assertNotNull(watProfile);
		assertEquals(personNumber,watProfile.getPersonIdentity().getPersonNumber());
		
	}

	@Test
	public void testUpdateRequest_insertEmptyProfileAssignmentSuccess() throws Exception {
		requestBean.setPersonIdentity(employee);
		setEmpProfileAssignment();
		PersonAttendanceProfileAssignment empProfileAssignment = new PersonAttendanceProfileAssignment(new ObjectIdLong(10L));
		empProfileAssignment.setAttendanceProfileId(new ObjectIdLong(-1L));
		Mockito.when(AttendanceProfileAssignmentList.makeAssignmentToEmptyProfileOnly(new ObjectIdLong(10L)))
		.thenReturn(empProfileAssignment);
		Mockito.when(Profile.getByName(Mockito.anyString())).thenReturn(profile);
		when(profile.getId()).thenReturn(new ObjectIdLong(10L));
		mockCurrentAssignmentList();
		Mockito.when(SpringContext.getBean(LicenseManager.class)).thenReturn(licenseManager);
		mockActiveLicence();
		Mockito.when(AttendanceProfileAssignmentList.isEmptyProfileAssignmentMissing(currentAssignments))
				.thenReturn(true);
		when(personality.getPersonId()).thenReturn(new ObjectIdLong(10L));
		KDate kdate = new KDate();
		when(adapterHelper.localDateToKdate(Mockito.any())).thenReturn(kdate );
		Personality response = employeeAssignmentService.updateRequest(requestBean);
		assertNotNull(response);
	}
	
	@Test
	public void testUpdateRequest_whenJobAssignmentIdIsNull() throws Exception {
		requestBean.setPersonIdentity(employee);
		setEmpProfileAssignment();
		Mockito.when(Profile.getByName(Mockito.anyString())).thenReturn(profile);
		when(profile.getId()).thenReturn(new ObjectIdLong(10L));
		mockCurrentAssignmentList();
		Mockito.when(SpringContext.getBean(LicenseManager.class)).thenReturn(licenseManager);
		mockActiveLicence();
		Mockito.when(AttendanceProfileAssignmentList.isEmptyProfileAssignmentMissing(currentAssignments))
				.thenReturn(false);
		when(personality.getPersonId()).thenReturn(new ObjectIdLong(10L));
		when(personality.getJobAssignmentId()).thenReturn(null);
		KDate kdate = new KDate();
		when(adapterHelper.localDateToKdate(Mockito.any())).thenReturn(kdate );
		Personality response = employeeAssignmentService.updateRequest(requestBean);
		assertNotNull(response);
	}
	
	@Test
	public void testUpdateRequest_whenJobAssignmentIsEqualsEmployeeId() throws Exception {
		requestBean.setPersonIdentity(employee);
		setEmpProfileAssignment();
		Mockito.when(Profile.getByName(Mockito.anyString())).thenReturn(profile);
		when(profile.getId()).thenReturn(new ObjectIdLong(10L));
		mockCurrentAssignmentList();
		Mockito.when(SpringContext.getBean(LicenseManager.class)).thenReturn(licenseManager);
		mockActiveLicence();
		Mockito.when(AttendanceProfileAssignmentList.isEmptyProfileAssignmentMissing(currentAssignments))
				.thenReturn(false);
		when(personality.getPersonId()).thenReturn(new ObjectIdLong(10L));
		when(personality.getJobAssignmentId()).thenReturn(new ObjectIdLong(10L));
		KDate kdate = new KDate();
		when(adapterHelper.localDateToKdate(Mockito.any())).thenReturn(kdate );
		Personality response = employeeAssignmentService.updateRequest(requestBean);
		assertNotNull(response);
	}
	
	@Test
	public void testUpdateRequest_whenJobAssignmentNotEqualsEmployeeId() throws Exception {
		requestBean.setPersonIdentity(employee);
		setEmpProfileAssignment();
		Mockito.when(Profile.getByName(Mockito.anyString())).thenReturn(profile);
		when(profile.getId()).thenReturn(new ObjectIdLong(10L));
		mockCurrentAssignmentList();
		Mockito.when(SpringContext.getBean(LicenseManager.class)).thenReturn(licenseManager);
		mockActiveLicence();
		Mockito.when(AttendanceProfileAssignmentList.isEmptyProfileAssignmentMissing(currentAssignments))
				.thenReturn(false);
		when(personality.getPersonId()).thenReturn(new ObjectIdLong(10L));
		when(personality.getJobAssignmentId()).thenReturn(new ObjectIdLong(15L));
		KDate kdate = new KDate();
		when(adapterHelper.localDateToKdate(Mockito.any())).thenReturn(kdate );
		try {
			Personality currentPersonality = employeeAssignmentService.updateRequest(requestBean);
		}
		catch(Exception e) {
			assertTrue(!(e instanceof APIException));
		}
	}
	
	private List createPersonAttendanceProfileAssignmentList() {
		List<PersonAttendanceProfileAssignment> personAttendanceProfileAssignment = new ArrayList<>();
		PersonAttendanceProfileAssignment empProfileAssignment = new PersonAttendanceProfileAssignment(
				new ObjectIdLong(10L));
//		empProfileAssignment.setEffectiveDate(null);
		personAttendanceProfileAssignment.add(empProfileAssignment);
		return personAttendanceProfileAssignment;
	}

	private void setEmpProfileAssignment() {
		List<PersonAttendanceProfileAssignmentBean> empProfileAssignmentList = createEmployeeAssignment();
		requestBean.setAttendanceProfileAssignments(empProfileAssignmentList);
	}

	private List<PersonAttendanceProfileAssignmentBean> createEmployeeAssignment() {
		List<PersonAttendanceProfileAssignmentBean> empProfileAssignmentList = new ArrayList<>();
		PersonAttendanceProfileAssignmentBean empProfileAssignment = new PersonAttendanceProfileAssignmentBean();
		empProfileAssignment.setEffectiveDate(LocalDate.of(2010, 2, 13));
		empProfileAssignment.setProfileName("ProfileName");
		empProfileAssignmentList.add(empProfileAssignment);
		return empProfileAssignmentList;
	}

	private void mockActiveLicence() {
		Mockito.when(LicenseType.getLicenseType(Mockito.anyString())).thenReturn(licenceType);
		when(personality.getLicenseType(Mockito.any(LicenseType.class))).thenReturn(personLicenseType);
		when(personLicenseType.getActive()).thenReturn(true);
	}

	private void mockCurrentAssignmentList() {
		when(personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity())).thenReturn(personality);
		personAttendanceProfileAssignmentListGuestMockedStatic.when(()->PersonAttendanceProfileAssignmentListGuest.getSetForPersonalityFromDB(Mockito.any()))
				.thenReturn(guest);
		when(guest.getAttendanceProfileAssignmentSet()).thenReturn(currentAssignments);
	}

	private boolean isValidException(Exception exception, int errorCode,
			String propertyName, String propertyValue) {
		int actualErrorCode = ((GenericException) exception).getErrorCode();
		return actualErrorCode == errorCode;
	}

}
