package com.kronos.persons.rest.assignments.service;

import com.kronos.commonbusiness.datatypes.ia.IARequest;
import com.kronos.commonbusiness.datatypes.ia.IAResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PositionDetailEntityRestImplTest {
    @Mock
    private PositionDetailEntityService positionDetailEntityServiceApi;
    @InjectMocks
    private PositionDetailEntityRestImpl positionDetailEntityRest;

    @Test
    public void testGetData() {
        IAResponse newResponse = new IAResponse();
        IARequest newRequest = new IARequest();
        when(positionDetailEntityServiceApi.getData(newRequest)).thenReturn(newResponse);
        IAResponse response = positionDetailEntityRest.getData(newRequest);
        assertNotNull(response);
        assertEquals(newResponse, response);
        verify(positionDetailEntityServiceApi).getData(newRequest);
    }
}
