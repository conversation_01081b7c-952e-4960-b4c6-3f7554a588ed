package com.kronos.persons.utils;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.container.api.exception.APIException;
import com.kronos.container.api.util.APIExceptionDetailResult;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignment;
import com.kronos.persons.rest.assignments.validation.PersonAttestationProfileAssignmentValidator;
import com.kronos.persons.rest.beans.HyperFindFilterBean;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kronos.persons.rest.exception.ExceptionConstants.ALL_RECORDS_FAILED;
import static com.kronos.persons.rest.exception.ExceptionConstants.PARTIAL_SUCCESS;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.doThrow;


@ExtendWith(MockitoExtension.class)
public class BulkProcessingHelperTest {
    private static final Long PERSON_ID = 2L;
    private static final Long PERSON_ID_2 = 4L;
    private static final String PERSON_QUALIFIER = "PERSON_QUALIFIER";
    private static final String PERSON_QUALIFIER_2 = "PERSON_QUALIFIER_2";
    private static final Integer SEQUENCE_1 = 1;
    private static final Integer SEQUENCE_2 = 2;
    private static final String PROFILE_QUALIFIER_2 = "PROFILE_QUALIFIER2";
    private static final ObjectRef PERSON = new ObjectRef(PERSON_ID, PERSON_QUALIFIER);
    private static final ObjectRef PERSON_2 = new ObjectRef(PERSON_ID_2, PERSON_QUALIFIER_2);
    private static final HyperFindFilterBean HYPER_FIND_FILTER_EMPTY_OBJECT = new HyperFindFilterBean();
    @Mock
    private PersonAttestationProfileAssignmentValidator personAttestationProfileAssignmentValidator;
    @Mock
    private APIException exception;
    @InjectMocks
    private BulkProcessingHelper bulkProcessingHelper;

    @Test
    public void createMultiUpsertResponse(){
        Map<Integer, PersonAttestationProfileAssignment> assignments = new HashMap<>();
        PersonAttestationProfileAssignment assignment1 = new PersonAttestationProfileAssignment();
        assignment1.setEmployee(PERSON);
        assignments.put(SEQUENCE_1, assignment1);

        Map<Integer, PersonAttestationProfileAssignment> requestSnapshot = new HashMap<>(assignments);
        Map<Integer, APIException> exceptionHolder = new HashMap<>();

        final List<PersonAttestationProfileAssignment> response = bulkProcessingHelper
                .createMultiUpsertResponse(requestSnapshot, assignments, exceptionHolder);

        assertEquals(assignment1,response.get(0));
    }

    @Test
    public void shouldCreatePartialResponse(){
        Map<Integer, PersonAttestationProfileAssignment> assignments = new HashMap<>();
        PersonAttestationProfileAssignment assignment1 = new PersonAttestationProfileAssignment();
        assignment1.setEmployee(PERSON);
        assignments.put(SEQUENCE_1, assignment1);

        PersonAttestationProfileAssignment failedAssignment = new PersonAttestationProfileAssignment();
        failedAssignment.setEmployee(PERSON_2);
        Map<Integer, PersonAttestationProfileAssignment> requestSnapshot = new HashMap<>(assignments);
        requestSnapshot.put(SEQUENCE_1, assignment1);
        requestSnapshot.put(SEQUENCE_2, failedAssignment);
        Map<Integer, APIException> exceptionHolder = new HashMap<>();
        exceptionHolder.put(SEQUENCE_2, exception);
        APIException resultException = null;
        try {

            bulkProcessingHelper
                    .createMultiUpsertResponse(requestSnapshot, assignments, exceptionHolder);
        } catch (APIException e) {
            resultException = e;
        }
        assertEquals(PARTIAL_SUCCESS,resultException.getErrorCode());
        final List<APIExceptionDetailResult> results = (List<APIExceptionDetailResult>) resultException.getResults().get("results");
        assertEquals(exception,results.get(1).getError());
        assertEquals(assignment1,results.get(0).getSuccess());
    }

    @Test
    public void shouldCreateFullFailureResponse(){
        Map<Integer, PersonAttestationProfileAssignment> assignments = new HashMap<>();
        PersonAttestationProfileAssignment assignment1 = new PersonAttestationProfileAssignment();
        assignment1.setEmployee(PERSON);
        PersonAttestationProfileAssignment failedAssignment = new PersonAttestationProfileAssignment();
        failedAssignment.setEmployee(PERSON_2);
        Map<Integer, PersonAttestationProfileAssignment> requestSnapshot = new HashMap<>(assignments);
        requestSnapshot.put(SEQUENCE_1, assignment1);
        Map<Integer, APIException> exceptionHolder = new HashMap<>();
        exceptionHolder.put(SEQUENCE_1, exception);
        APIException resultException = null;
        try {

            bulkProcessingHelper
                    .createMultiUpsertResponse(requestSnapshot, assignments, exceptionHolder);
        } catch (APIException e) {
            resultException = e;
        }
        assertEquals(ALL_RECORDS_FAILED,resultException.getErrorCode());
        final List<APIExceptionDetailResult> results = (List<APIExceptionDetailResult>) resultException.getResults().get("results");
        assertEquals(exception,results.get(0).getError());
    }

    @Test
    public void resolveEmployees(){
        Map<Integer, PersonAttestationProfileAssignment> assignments = new HashMap<>();
        PersonAttestationProfileAssignment assignment1 = new PersonAttestationProfileAssignment();
        PersonAttestationProfileAssignment assignment2 = new PersonAttestationProfileAssignment();
        ObjectRef personWithId = new ObjectRef(PERSON_ID);
        ObjectRef personWithQualifier = new ObjectRef(PROFILE_QUALIFIER_2);
        assignment1.setEmployee(personWithId);
        assignment1.setHyperFindFilter(HYPER_FIND_FILTER_EMPTY_OBJECT);
        assignment2.setEmployee(personWithQualifier);
        assignment2.setHyperFindFilter(HYPER_FIND_FILTER_EMPTY_OBJECT);
        assignments.put(SEQUENCE_1, assignment1);
        assignments.put(SEQUENCE_2, assignment2);
        Map<Integer, APIException> exceptionHolder = null;
        Map<Integer, PersonAttestationProfileAssignment> requestSnapshot = new HashMap<>();
        Map<Long, Object> resolvedById = new HashMap<>();
        Map<String, Object> resolvedByQualifier = new HashMap<>();
        resolvedById.put(PERSON_ID, new ObjectRef(PERSON_ID, PERSON_QUALIFIER));
        resolvedByQualifier.put(PROFILE_QUALIFIER_2, new ObjectRef(PERSON_ID_2, PROFILE_QUALIFIER_2));
        when(personAttestationProfileAssignmentValidator.validateReadAccessAndGetObjectRefsByPersonIds(anyMap()))
                .thenReturn(resolvedById);
        when(personAttestationProfileAssignmentValidator.validateReadAccessAndGetObjectRefsByPersonNums(anyMap()))
                .thenReturn(resolvedByQualifier);

        bulkProcessingHelper.resolveAndValidateEmployees(assignments, exceptionHolder, requestSnapshot);

        verify(personAttestationProfileAssignmentValidator).validatePersonNotNullReference(personWithId);
        verify(personAttestationProfileAssignmentValidator).validatePersonNotNullReference(personWithQualifier);
        assertEquals(PERSON_QUALIFIER,assignment1.getEmployee().getQualifier());
        assertEquals(PERSON_ID_2,assignment2.getEmployee().getId());
    }

    @Test
    public void shouldAddErrorAndCleanFailedElementsIfEmployeeNotFound(){
        Map<Integer, PersonAttestationProfileAssignment> assignments = new HashMap<>();
        PersonAttestationProfileAssignment assignment1 = new PersonAttestationProfileAssignment();
        ObjectRef personWithId = new ObjectRef(PERSON_ID);
        assignment1.setEmployee(personWithId);
        assignment1.setHyperFindFilter(HYPER_FIND_FILTER_EMPTY_OBJECT);
        assignments.put(SEQUENCE_1, assignment1);
        Map<Integer, APIException> exceptionHolder = new HashMap<>();
        Map<Integer, PersonAttestationProfileAssignment> requestSnapshot = new HashMap<>();
        Map<Long, Object> resolvedById = new HashMap<>();
        Map<String, Object> resolvedByQualifier = new HashMap<>();
        resolvedById.put(PERSON_ID, exception);
        resolvedByQualifier.put(PROFILE_QUALIFIER_2, new ObjectRef(PERSON_ID_2, PROFILE_QUALIFIER_2));
        when(personAttestationProfileAssignmentValidator.validateReadAccessAndGetObjectRefsByPersonIds(anyMap()))
                .thenReturn(resolvedById);


        bulkProcessingHelper.resolveAndValidateEmployees(assignments, exceptionHolder, requestSnapshot);

        assertTrue(assignments.isEmpty());
        assertEquals(exception,exceptionHolder.get(SEQUENCE_1));
    }

    @Test
    public void numberInput(){
        List<PersonAttestationProfileAssignment> input = new ArrayList<>();
        PersonAttestationProfileAssignment assignment1 = new PersonAttestationProfileAssignment();
        PersonAttestationProfileAssignment assignment2 = new PersonAttestationProfileAssignment();
        input.addAll(Arrays.asList(assignment1, assignment2));
        final Map<Integer, PersonAttestationProfileAssignment> numberedInput =
                bulkProcessingHelper.numberInput(input);

        assertTrue(numberedInput.keySet().containsAll(Arrays.asList(SEQUENCE_1, SEQUENCE_2)));
        assertEquals(assignment1,numberedInput.get(SEQUENCE_1));
        assertEquals(assignment2,numberedInput.get(SEQUENCE_2));
    }

    @Test
    public void testResolveAndValidateEmployeesThrowAPIException(){
        Map<Integer, PersonAttestationProfileAssignment> assignments = new HashMap<>();
        PersonAttestationProfileAssignment assignment1 = new PersonAttestationProfileAssignment();
        ObjectRef personWithId = new ObjectRef();
        assignment1.setEmployee(personWithId);
        assignments.put(SEQUENCE_1, assignment1);
        Map<Integer, APIException> exceptionHolder = new HashMap<>();
        Map<Integer, PersonAttestationProfileAssignment> requestSnapshot = new HashMap<>();
        Map<Long, Object> resolvedById = new HashMap<>();
        Map<String, Object> resolvedByQualifier = new HashMap<>();
        exceptionHolder.put(SEQUENCE_1, exception);
        requestSnapshot.put(SEQUENCE_1,assignment1);
        when(personAttestationProfileAssignmentValidator.validateReadAccessAndGetObjectRefsByPersonIds(anyMap()))
                .thenReturn(resolvedById);
        when(personAttestationProfileAssignmentValidator.validateReadAccessAndGetObjectRefsByPersonNums(anyMap()))
                .thenReturn(resolvedByQualifier);
        doThrow(exception).when(personAttestationProfileAssignmentValidator).validatePersonNotNullReference(any());
        assertEquals(1,assignments.size());
        bulkProcessingHelper.resolveAndValidateEmployees(assignments, exceptionHolder, requestSnapshot);
        assertEquals(exception,exceptionHolder.get(SEQUENCE_1));
        assertNull(requestSnapshot.get(SEQUENCE_1).getEmployee());
        assertTrue(assignments.isEmpty());
    }
}