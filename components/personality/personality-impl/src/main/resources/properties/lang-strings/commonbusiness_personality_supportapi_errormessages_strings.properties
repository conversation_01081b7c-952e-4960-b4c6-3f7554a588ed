WCO-129500.MESSAGE=Request body cannot be left blank. Please provide request body.
WCO-129500.HTTPSTATUSCODE=400
WCO-129501.MESSAGE=Duplicate tenant(s) found in request : {invalidTenants}
WCO-129501.HTTPSTATUSCODE=400
WCO-129502.MESSAGE=Invalid cache names are provided in request. Valid caches are : {validCaches}
WCO-129502.HTTPSTATUSCODE=400
WCO-129503.MESSAGE=tenantShortName is missing in the request. Please provide tenantShortName.
WCO-129503.HTTPSTATUSCODE=400
WCO-129504.MESSAGE=Tenants : {invalidTenants} provided in request is not valid.
WCO-129504.HTTPSTATUSCODE=400
WCO-129505.MESSAGE=PersonNumbers are not provided in the request. Please provided person numbers.
WCO-129505.HTTPSTATUSCODE=400
WCO-129506.MESSAGE=Maximum 5 persons are allowed in the request.
WCO-129506.HTTPSTATUSCODE=400
WCO-129507.MESSAGE=PersonsDetails provided in request are not valid.
WCO-129507.HTTPSTATUSCODE=400
WCO-129508.MESSAGE=Invalid evictFrom field provided in request is not valid.
WCO-129508.HTTPSTATUSCODE=400
WCO-129509.MESSAGE=Exception occurred while processing caching request.
WCO-129509.HTTPSTATUSCODE=500
WCO-129510.MESSAGE=Maximum 1 tenant is allowed in the request.
WCO-129510.HTTPSTATUSCODE=400
WCO-129511.MESSAGE=Exception occurred while validating person cache.
WCO-129511.HTTPSTATUSCODE=500
WCO-129512.MESSAGE=Duplicate person(s) found in request : {invalidEmployees}
WCO-129512.HTTPSTATUSCODE=400