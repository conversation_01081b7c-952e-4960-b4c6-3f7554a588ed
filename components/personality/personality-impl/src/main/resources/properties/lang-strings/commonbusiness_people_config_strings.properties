All_People=All People
All_People_Description=It includes all employees past, present and future and not just employees it also includes any terminations.
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPName.MISSING_EXTERNAL_IDENTIFIER_READ=Missing External Identifier Read
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPDescription.MISSING_EXTERNAL_IDENTIFIER_READ=Missing External Identifier Read ACP.
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPDescription.EXTERNAL_IDENTIFIER=Controls the user's ability to retrieve, assign and unassign external identifiers.
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPName.EXTERNAL_IDENTIFIER=External Identifier
com.kronos.persons.rest.beans.validator.NONE=< None >
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPName.LIGHT_WEIGHT_EMPLOYEE_RECORDS_READ=Lightweight Employee Records API
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPDescription.LIGHT_WEIGHT_EMPLOYEE_RECORDS_READ=Controls the user's ability to retrieve Lightweight Employee Records using the API.
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPName.CURRENT_USER_INFORMATION_READ=Current User Information API
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPDescription.CURRENT_USER_INFORMATION_READ=Controls the user's ability to retrieve their User Information using the API.
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPName.EMPLOYEE_PHOTO=Employee Photo
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPDescription.EMPLOYEE_PHOTO=Controls the user's abilities to retrieve Employee Photo using the API.
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPName.JOB_PREFERENCES_AND_SCHEDULING_CONTEXT=Job Preference and Scheduling Context
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPDescription.JOB_PREFERENCES_AND_SCHEDULING_CONTEXT=Controls Access to the Employee Role Job Preferences and Scheduling Context View in the People Editor. The Employee Job Transfer Set FACP must be allowed to use this FACP.
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPName.EMPLOYEE_PUNCH_INTERPRETATION_RULE_ASSIGNMENT=Employee Punch Interpretation Rule Assignment API
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPDescription.EMPLOYEE_PUNCH_INTERPRETATION_RULE_ASSIGNMENT=Controls the user's ability to retrieve punch interpretation rule
com.kronos.punchinterpretation.punchrestrictions.simple=Simple
com.kronos.punchinterpretation.punchrestrictions.full=Full
com.kronos.persons.rest.service.EMPLOYEE_JOB_PREFERENCE_IS_EMPLOYEE_REQUEST_YES=Yes
com.kronos.persons.rest.service.EMPLOYEE_JOB_PREFERENCE_IS_EMPLOYEE_REQUEST_NO=No
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPName.CURRENT_USER_DELEGATIONS_READ=Current User Delegations API
com.kronos.wfc.wtk.accessprofiles.business.functionaccessprofiles.ACPDescription.CURRENT_USER_DELEGATIONS_READ=Controls the ability to access current user delegation roles via API
