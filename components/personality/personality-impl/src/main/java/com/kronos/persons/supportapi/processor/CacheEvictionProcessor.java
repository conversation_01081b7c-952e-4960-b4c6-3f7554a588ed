/*
 * *****************************************************************************
 * Copyright (c) 2020 Kronos, Inc. All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * Kronos, Inc. ("Confidential Information").  You shall not
 * Copyright (c) 2020 Kronos Inc. All Rights Reserved.
 *
 * KRONOS MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE
 * SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
 * PURPOSE, OR NON-INFRINGEMENT. KRONOS SHALL NOT BE LIABLE FOR ANY DAMAGES
 * SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING
 * THIS SOFTWARE OR ITS DERIVATIVES.
 ****************************************************************************
 *
 **/
package com.kronos.persons.supportapi.processor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.ws.rs.core.Response;

import org.apache.commons.collections.CollectionUtils;
import org.json.JSONObject;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kronos.api.commoncomponent.async.APIExecutor;
import com.kronos.api.commoncomponent.async.ExecutionContext;
import com.kronos.api.commoncomponent.util.CommonUtils;
import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.people.personality.cache.ExtensionCacheUpdater;
import com.kronos.people.personality.notification.entry.EventType;
import com.kronos.persons.rest.supportapi.enums.CacheEvictionTypeEnum;
import com.kronos.persons.rest.supportapi.validation.CacheSupportApiUtil;
import com.kronos.persons.rest.supportapi.validation.SupportApiValidator;
import com.kronos.persons.supportapi.dto.PersonDetail;
import com.kronos.persons.supportapi.dto.PersonalityCacheEvictRequest;
import com.kronos.persons.supportapi.dto.PersonalityEvictRequestData;
import com.kronos.persons.supportapi.dto.PersonalityEvictResponseData;
import com.kronos.persons.supportapi.errorcodes.PersonalitySupportApiConstants;
import com.kronos.persons.supportapi.errorcodes.PersonalitySupportApiErrorCodes;
import com.kronos.tenantprovider.api.TenantProvider;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonNotification;


/**
 * This class is responsible for cache eviction  on the list of personIDs that are passed.
 * The  processor identifies the valid personIDs, evicts their personality caches using ExtensionCacheUpdater service.
 *
 */
@Named
public class CacheEvictionProcessor {

	private static final KLogger LOGGER = KLoggerFactory.getKLogger(CacheEvictionProcessor.class);
	
	@Inject
	TenantProvider tenantProvider;
	
	@Inject
	SupportApiValidator supportApiValidator;
	
	@Inject 
	CacheSupportApiUtil cacheSupportApiUtil;
	
	@Inject
	APIExecutor apiExecutor;
	
	@Inject
	ExtensionCacheUpdater cacheEvictService;
	
	@Inject
	CommonUtils commonUtils;
		
	
	
	/**
	 * This function provides functionality to evict/delete personality caches of Persons that are provided in the request.
	 * @param personalityCacheEvictRequest
	 * 
	 * @return Response
	 */
	public Response evictPersonCacheProcessor(PersonalityCacheEvictRequest personalityCacheEvictRequest) {
		try {
			validateCacheEvictRequest(personalityCacheEvictRequest);
			List<String> tenantList = getTenantList(personalityCacheEvictRequest.getTenantPersonalityRequestData());
			List<PersonalityEvictRequestData> tenantPersonalityRequestList = personalityCacheEvictRequest.getTenantPersonalityRequestData();
			ExecutionContext<List<PersonalityEvictRequestData> , String , PersonalityEvictResponseData > executionContext = new ExecutionContext<>();
			setExecutionContextParams(tenantList, tenantPersonalityRequestList, executionContext);
			Object object = apiExecutor.execute(executionContext);
			List<PersonalityEvictResponseData> personalityResponseData = getInvalidTenantListFromResponse(object);
			return Response.status(getStatusCodeForResponse(personalityResponseData)).entity(object.toString()).build();
		} catch (APIException apiException) {
			LOGGER.error("Support API : APIException occurred in evictPersonCacheProcessor while performing evict operation: ",apiException);
			throw apiException;
		} catch (Exception exception) {
			LOGGER.error("Support API : Exception occurred in evictPersonCacheProcessor while performing evict operation: ",exception);
			cacheSupportApiUtil.exceptionResolverAPI(PersonalitySupportApiErrorCodes.EXCEPTION_OCCURRED_IN_PROCESSING_CACHING_REQUEST, exception);
			throw new APIException(PersonalitySupportApiErrorCodes.EXCEPTION_OCCURRED_IN_PROCESSING_CACHING_REQUEST);
		}
	}
	
	/**
	 * @param personalityResponseData
	 * @return
	 */
	private int getStatusCodeForResponse(List<PersonalityEvictResponseData> personalityResponseData) {
		int invalidPersonCount = 0;
		int totalPersonCount = 0;
		for(PersonalityEvictResponseData personalityData : personalityResponseData) {
			for(PersonDetail personDetails :personalityData.getPersonNumberList()) {
				if(PersonalitySupportApiConstants.INVALID_EMP_STATUS.equalsIgnoreCase(personDetails.getStatus())) {
					invalidPersonCount = invalidPersonCount + 1;
				}
				totalPersonCount = totalPersonCount + 1;
			}
		}
		return cacheSupportApiUtil.getStatusCode(invalidPersonCount, totalPersonCount);
	}
	
	/**
	 * This method is used to get list of invalid tenants from response object
	 * @param object
	 * @return
	 */
	public List<PersonalityEvictResponseData> getInvalidTenantListFromResponse(Object object) {
		try {
			return Arrays.asList(new ObjectMapper().readValue(((JSONObject) object).get("response").toString(), PersonalityEvictResponseData[].class));
		} catch (Exception exception) {
			LOGGER.error("Support API : Exception occurred in getInvalidTenantListFromResponse while getting list of invalid tenants: ",exception);
		}
		return null;
	}

	
	/**
	 * this method sets the ExecutionContextParams 
	 * @param tenantList
	 * @param tenantPersonalityRequestList
	 * @param executionContext
	 */
	private void setExecutionContextParams(List<String> tenantList,
			List<PersonalityEvictRequestData> tenantPersonalityRequestList,
			ExecutionContext<List<PersonalityEvictRequestData>, String, PersonalityEvictResponseData> executionContext) {
		executionContext.setRequestUri(commonUtils.getRequestURI());
		executionContext.setExecutionFunction(cacheEvictExecutionFunction);
		executionContext.setT(tenantPersonalityRequestList);
		executionContext.setTenantList(tenantList);
		executionContext.setTenantAware(true);
		executionContext.setAsync(false);
	}

	
	private List<String> getTenantList(List<PersonalityEvictRequestData> evictionRequestList) {
		List<String> tenantList = new ArrayList<>();
		evictionRequestList.forEach(personalityrequest-> {
			tenantList.add(personalityrequest.getTenantShortName());
		});
		return tenantList;
		
	}

	/**
	 * This is BiFunction definition for  personality cache evict operation , It calls cache ExtensionCacheUpdater.evict() service methods to clear 
	 * legacy , new or both legacy and new caches depending on "evictFrom" parameter value.
	 * perform evict operation on the valid employees.
	 */
	BiFunction<List<PersonalityEvictRequestData>, String, PersonalityEvictResponseData> cacheEvictExecutionFunction = (tenantPersonalityList , object ) -> {
		PersonalityEvictResponseData tenantPersonalityResponseData = new PersonalityEvictResponseData();
		List<PersonDetail> invalidPersonObjList = new ArrayList<>();
		for( PersonalityEvictRequestData personalityRequestData : tenantPersonalityList) {
			if(tenantProvider.getTenantId().equalsIgnoreCase(personalityRequestData.getTenantShortName())){
				Map<String,List<PersonDetail>> validAndInvalidPersonObjects = cacheSupportApiUtil.getValidAndInvalidEmployeeIDs(personalityRequestData.getPersonNumberList());
				List<PersonDetail> validPersonObjList = validAndInvalidPersonObjects.get(PersonalitySupportApiConstants.VALID_EMP_LIST);
				invalidPersonObjList.addAll(validAndInvalidPersonObjects.get(PersonalitySupportApiConstants.INVALID_EMP_LIST));
				if(!CollectionUtils.isEmpty(validPersonObjList)) {
					try {
						processCacheEviction(personalityRequestData, validPersonObjList);
					}
					catch(Exception e) {
						LOGGER.error("Support API : Exception occurred while processing cache eviction request for the tenant : {}", tenantProvider.getTenantId());
						validPersonObjList.forEach(validPersonObj-> {
							validPersonObj.setStatus(PersonalitySupportApiConstants.EVICTION_FAILED);
						});
					}
					validPersonObjList.forEach(validPersonObj-> {
						validPersonObj.setStatus(PersonalitySupportApiConstants.SUCCESSFULLY_EVICTED);
					});
				}
				setResponseParameters(tenantPersonalityResponseData, personalityRequestData, validPersonObjList,
						invalidPersonObjList);
				break;
			}	
		}
		return tenantPersonalityResponseData;
	};


	private void setResponseParameters(PersonalityEvictResponseData tenantPersonalityResponseData,
			PersonalityEvictRequestData personalityRequestData, List<PersonDetail> validPersonObjList,
			List<PersonDetail> invalidPersonObjList) {
		if(!CollectionUtils.isEmpty(invalidPersonObjList)) {
			invalidPersonObjList = cacheSupportApiUtil.setStatusIfPersonNotFound(invalidPersonObjList);
		}
		tenantPersonalityResponseData.setPersonNumberList(validPersonObjList);
		tenantPersonalityResponseData.getPersonNumberList().addAll(invalidPersonObjList);
		tenantPersonalityResponseData.setTenantShortName(tenantProvider.getTenantId());
		tenantPersonalityResponseData.setEvictFrom(personalityRequestData.getEvictFrom());
	}

	/**
	 * this method processes eviction of person caches on the basis of evictFrom parameter.
	 * cases : deletes personality data from legacy if evictFrom =legacy, delete personality data from new if evictFrom = new.
	 * and deletes personality data from both legacy and new if evictFrom = both or evictFrom = empty.
	 * @param tenantPersonalityReqData
	 * @param validPersonObjList
	 */
	private void processCacheEviction(PersonalityEvictRequestData tenantPersonalityReqData,
			List<PersonDetail> validPersonObjList) {
		String evictFrom = "";
		List<Long> validPersonIDs = validPersonObjList.stream().map(personObject->Long.valueOf(Long.parseLong(personObject.getPersonID()))).collect(Collectors.toList());
		try {
			evictFrom = tenantPersonalityReqData.getEvictFrom();
			if(CacheEvictionTypeEnum.BOTH_LEGACY_AND_PERSON_CACHE.getValue().equalsIgnoreCase(evictFrom)) {
				cacheEvictService.deleteFromLegacyAndNewPersonCache(validPersonIDs);
				LOGGER.info("Support API : Eviction done successfully from both new and legacy person cache for persons : {} ", validPersonIDs);
			}
			else if(CacheEvictionTypeEnum.LEGACY_PERSON_CACHE.getValue().equalsIgnoreCase(evictFrom)) {
				cacheEvictService.deleteFromLegacyPersonCache(validPersonIDs);
				LOGGER.info("Support API : Eviction done successfully from legacyPersonCache for persons : {} ", validPersonIDs);
			}
			else if (CacheEvictionTypeEnum.NEW_PERSON_CACHE.getValue().equalsIgnoreCase(evictFrom)){
				validPersonIDs.forEach(personId-> {
					cacheEvictService.refreshNewPersonalityCache(personId, PersonNotification.DELETE_EVENT);
		    });
				LOGGER.info("Support API : Eviction done successfully from newPersonCache for persons : {} ", validPersonIDs);
			}
			else {
				cacheEvictService.deleteFromLegacyAndNewPersonCache(validPersonIDs);
				LOGGER.info("Support API : Eviction done successfully from bothPersonCache for persons : {} ", validPersonIDs);
			}
		}
		catch(Exception e) {
			LOGGER.error("Error occurred in Eviction process while evicting personIDs : {}  for cache type : {} ", validPersonIDs, evictFrom);
		}
	}
	
	/**
	 * this method validates the person cache evict json request.
	 * @param personalityCacheEvictRequest
	 */
	private void validateCacheEvictRequest(PersonalityCacheEvictRequest personalityCacheEvictRequest) {
		List<PersonalityEvictRequestData> evictionRequestList = personalityCacheEvictRequest.getTenantPersonalityRequestData();
		ArrayList<String> cacheEvictionValues = new ArrayList<>();
		if(evictionRequestList == null || evictionRequestList.isEmpty()) {
			LOGGER.error("Support API : Validation Failed : empty data is provided in request.");
			throw new APIException(PersonalitySupportApiErrorCodes.EMPTY_REQUEST_ERROR_CODE);
		}
		Integer maxTenantsAllowedInRequest = cacheSupportApiUtil.getIntegerValueForProperty(PersonalitySupportApiConstants.MAX_TENANT_ALLOWED_EVICT_REQUEST
				, PersonalitySupportApiConstants.MAX_TENANT_DEFAULT_VALUE_EVICT_REQUEST);
		supportApiValidator.validateTotalTenantsInRequest(evictionRequestList.size(), maxTenantsAllowedInRequest);
		
		evictionRequestList.forEach(tenantRequestDetail-> {	
			supportApiValidator.validateTenantShortName(tenantRequestDetail.getTenantShortName());
		});
		for (CacheEvictionTypeEnum cacheValue : CacheEvictionTypeEnum.values()) {
			cacheEvictionValues.add(cacheValue.getValue());
		}
		evictionRequestList.forEach(tenantRequestDetail-> {	
			if (tenantRequestDetail.getEvictFrom() != null) {
				if (tenantRequestDetail.getEvictFrom().equals("") || !cacheEvictionValues.contains(tenantRequestDetail.getEvictFrom())) {
					LOGGER.error("Support API : Validation Failed : Invalid data is provided in attribute evictFrom");
					throw new APIException(PersonalitySupportApiErrorCodes.INVALID_EVICT_FROM_IDENTIFIER);
				}
			}
		});
		Integer maxPersonsAllowed = cacheSupportApiUtil.getIntegerValueForProperty(PersonalitySupportApiConstants.MAX_PERSON_ALLOWED_EVICT_REQUEST
				, PersonalitySupportApiConstants.MAX_PERSON_DEFAULT_VALUE_EVICT_REQUEST);
		evictionRequestList.forEach(personalityEvictRequestDataObject -> {
			supportApiValidator.validatePersonDetails(personalityEvictRequestDataObject.getPersonNumberList(),maxPersonsAllowed);
		});
		checkForDuplicateValuesInRequest(personalityCacheEvictRequest, evictionRequestList);
	}

	/**
	 * This method is used to check whether there are duplicate tenants or persons in the request provided
	 * 
	 * @param personalityCacheEvictRequest
	 * @param evictionRequestList
	 */
	private void checkForDuplicateValuesInRequest(PersonalityCacheEvictRequest personalityCacheEvictRequest,
			List<PersonalityEvictRequestData> evictionRequestList) {
		supportApiValidator.checkForDuplicateParametersInRequest(getTenantList(evictionRequestList)
				, PersonalitySupportApiErrorCodes.DUPLICATE_TENANT_FOUND_IN_REQUEST_ERROR_CODE, PersonalitySupportApiConstants.TENANTS_KEY);
		List<String> personList;
		for(PersonalityEvictRequestData data : personalityCacheEvictRequest.getTenantPersonalityRequestData()) {
			personList = new ArrayList<>();
			for(PersonDetail personDetail :	data.getPersonNumberList()) {
				personList.add(personDetail.getPersonNum());
			}
			supportApiValidator.checkForDuplicateParametersInRequest(personList, PersonalitySupportApiErrorCodes.DUPLICATE_PERSON_FOUND_IN_REQUEST_ERROR_CODE
					, PersonalitySupportApiConstants.EMPLOYEE_KEY);
		}
	}
}