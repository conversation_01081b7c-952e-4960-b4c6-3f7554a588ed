/***********************************************************************
 * LightPersonServiceImpl.java
 *
 * Copyright 2022, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.persons.context.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.people.personality.dataaccess.entity.EmployeeDTO;
import com.kronos.people.personality.dataaccess.service.IPersonReadService;
import com.kronos.persons.context.utils.PeopleConverter;
import com.kronos.persons.rest.beans.LightPersonInfoBean;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.model.LightPersonInformationSearchCriteria;
import com.kronos.persons.utils.DateUtils;

/**
 * <AUTHOR>
 *
 */
@Named
public class LightPersonServiceImpl implements ILightPersonService {

	private KLogger LOGGER = KLoggerFactory.getKLogger(LightPersonServiceImpl.class);

	private static final int DEFAULT_INDEX_COUNT = 0;

	private final String DEFAULT_MAX_COUNT_SIZE = "global.person.lightweight.records.page.size.max";
	
	private final String DEFAULT_MAXCOUNTSIZE = "10000";

	@Inject
	private IPersonReadService personReadService;

	@Inject
	private PeopleConverter peopleConverter;

	@Inject
	private IKProperties kronosProperties;

	@Override
	public LightPersonInfoBean findLightPersonRecords(LightPersonInformationSearchCriteria searchCriteria) {
		Integer maxCountSize = Integer
				.parseInt(kronosProperties.getProperty(DEFAULT_MAX_COUNT_SIZE, DEFAULT_MAXCOUNTSIZE));
		Long pageSize = Objects.isNull(searchCriteria.getCount()) ? maxCountSize : searchCriteria.getCount();
		Long index = Objects.isNull(searchCriteria.getIndex()) ? 0L : searchCriteria.getIndex();
		if (pageSize <= DEFAULT_INDEX_COUNT) {
			throwInvalidCountException(pageSize);
		}
		if (pageSize > maxCountSize) {
			throwPageSizeException(maxCountSize);
		}
		Integer pageIndex = 0;
		if (index != null && index >= DEFAULT_INDEX_COUNT) {
			pageIndex = index.intValue();
		} else {
			throwInvalidIndexException(index);
		}
		LocalDate localDate = searchCriteria.getWhere().getSnapshotDate();
		LocalDateTime effectiveDateTime = null;
		LocalDateTime expirationDateTime = null;
		if (Objects.isNull(localDate)) {
			effectiveDateTime = DateUtils.getStartingOfTheDay(LocalDate.now());
			expirationDateTime = DateUtils.getMidNight(LocalDate.now());
		} else {
			effectiveDateTime = DateUtils.getStartingOfTheDay(localDate);
			expirationDateTime = DateUtils.getMidNight(localDate);
		}
		return findLightPersonRecords(searchCriteria, pageSize, pageIndex, effectiveDateTime, expirationDateTime);
	}

	public void throwInvalidIndexException(Long index) {
		LOGGER.error("The index {}", index);
		APIException exception = new APIException(ExceptionConstants.INVALID_INDEX);
		exception.addUserParameter("indexValue", String.valueOf(index));
		throw exception;
	}

	public void throwInvalidCountException(Long count) {
		LOGGER.error("The count {}", count);
		APIException exception = new APIException(ExceptionConstants.INVALID_COUNT);
		exception.addUserParameter("countValue", String.valueOf(count));
		throw exception;
	}
	
	private void throwPageSizeException(Integer maxCountSize) {
		LOGGER.error("Supplied Count's value exceeds maximum allowed limit");
		APIException exception = new APIException(ExceptionConstants.ERROR_ZERO_PAGE_SIZE);
		exception.addUserParameter(ExceptionConstants.COUNTLIMIT, String.valueOf(maxCountSize));
		throw exception;
	}
	
	private LightPersonInfoBean findLightPersonRecords(LightPersonInformationSearchCriteria searchCriteria,
			Long pageSize, Integer pageIndex, LocalDateTime effectiveDateTime, LocalDateTime expirationDateTime) {
		List<EmployeeDTO> employeeDTOs = null;
		LightPersonInfoBean lightPersonInformation = null;
		try {
			employeeDTOs = personReadService.findLightPersonRecords(pageIndex.longValue(), pageSize, effectiveDateTime, expirationDateTime, searchCriteria);
			lightPersonInformation = peopleConverter.convertRestToEntityBeansForfind(employeeDTOs);
		} catch (APIException apiExp) {
			LOGGER.error("APIException while geting data:-", apiExp);
			throw apiExp;
		} catch (Exception exp) {
			LOGGER.error("Exception while geting data:-", exp);
			throw new APIException(ExceptionConstants.GENERIC_EXCEPTION);
		}
		return lightPersonInformation;
	}
}
