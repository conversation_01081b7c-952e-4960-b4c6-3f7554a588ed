package com.kronos.persons.supportapi.processor;

import com.kronos.api.commoncomponent.async.APIExecutor;
import com.kronos.api.commoncomponent.async.ExecutionContext;
import com.kronos.api.commoncomponent.util.CommonUtils;
import com.kronos.concurrent.api.service.KronosThreadPoolService;
import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.people.personality.cache.PersonalityDistributedOperationsHandler;
import com.kronos.people.personality.facade.PersonalityCacheFacade;
import com.kronos.persons.rest.supportapi.validation.CacheSupportApiUtil;
import com.kronos.persons.rest.supportapi.validation.SupportApiValidator;
import com.kronos.persons.supportapi.dto.PersonalityCacheInsertRequest;
import com.kronos.persons.supportapi.dto.PersonalityCacheInsertResponse;
import com.kronos.persons.supportapi.dto.PersonalityInsertRequestData;
import com.kronos.persons.supportapi.errorcodes.PersonalitySupportApiConstants;
import com.kronos.persons.supportapi.errorcodes.PersonalitySupportApiErrorCodes;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.function.BiFunction;

@Named
public class CacheInsertProcessor {

    private static final KLogger LOGGER = KLoggerFactory.getKLogger(CacheInsertProcessor.class);
    private static final String PERSONALITY_CACHE_INSERT = "Personality_Cache_Insert";

    @Inject
    SupportApiValidator supportApiValidator;

    @Inject
    CacheSupportApiUtil cacheSupportApiUtil;

    @Inject
    PersonalityDistributedOperationsHandler distributedOperationsHandler;

    @Inject
    PersonalityCacheFacade cacheFacade;

    @Inject
    CommonUtils commonUtils;

    @Inject
    APIExecutor apiExecutor;

    @Inject
    KronosThreadPoolService kronosThreadPoolService;

    ExecutorService executor;

    @PostConstruct
    public void startup(){
        executor = kronosThreadPoolService.newThreadPool(PERSONALITY_CACHE_INSERT);
    }

    public void validatePersonalityCacheInsertRequest(PersonalityCacheInsertRequest personalityCacheInsertRequest) {
        List<PersonalityInsertRequestData> insertRequestList = personalityCacheInsertRequest.getTenantPersonalityRequestData();
        if(insertRequestList == null || insertRequestList.isEmpty()) {
            LOGGER.error("Support API : Validation Failed : empty data is provided in request.");
            throw new APIException(PersonalitySupportApiErrorCodes.EMPTY_REQUEST_ERROR_CODE);
        }

        Integer maxTenantsAllowedInRequest = cacheSupportApiUtil.getIntegerValueForProperty(PersonalitySupportApiConstants.MAX_Tenant_ALLOWED_IN_INSERT_CACHE_REQUEST
                , PersonalitySupportApiConstants.MAX_Tenant_DEFAULT_ALLOWED_IN_INSERT_CACHE_REQUEST);
        supportApiValidator.validateTotalTenantsInRequest(insertRequestList.size(), maxTenantsAllowedInRequest);

        Integer maxPersonsAllowed = cacheSupportApiUtil.getIntegerValueForProperty(PersonalitySupportApiConstants.MAX_PERSON_ALLOWED_IN_INSERT_CACHE_REQUEST
                , PersonalitySupportApiConstants.MAX_PERSON_DEFAULT_VALUE_FOR_INSERT_CACHE_REQUEST);

        insertRequestList.forEach(personalityInsertRequestDataObject -> {
            supportApiValidator.validatePersonIdsList(personalityInsertRequestDataObject.getPersonIds(),maxPersonsAllowed);
        });


    }

    private void setExecutionContextParams(List<String> tenantList,
                                           List<PersonalityInsertRequestData> tenantPersonalityRequestList,
                                           ExecutionContext<List<PersonalityInsertRequestData>, String, PersonalityCacheInsertResponse> executionContext) {
        executionContext.setRequestUri(commonUtils.getRequestURI());
        executionContext.setExecutionFunction(cacheInsertBiFunction);
        executionContext.setT(tenantPersonalityRequestList);
        executionContext.setTenantList(tenantList);
        executionContext.setTenantAware(true);
        executionContext.setAsync(false);
    }

    BiFunction<List<PersonalityInsertRequestData>, String, PersonalityCacheInsertResponse> cacheInsertBiFunction = (tenantPersonalityList, requestURI) -> {
        PersonalityCacheInsertResponse personalityCacheInsertResponse = new PersonalityCacheInsertResponse();
        for(PersonalityInsertRequestData personalityInsertRequestData : tenantPersonalityList) {
            try {
                ConcurrentLinkedQueue<Long> personIdsQueue = new ConcurrentLinkedQueue<>(personalityInsertRequestData.getPersonIds());
                personalityCacheInsertResponse.setTenantShortName(personalityInsertRequestData.getTenantShortName());
                personalityCacheInsertResponse.setPersonIds(new ArrayList<>(personIdsQueue));
                int primeCount = distributedOperationsHandler.pushInRedisViaCacheOperation(
                        personIdsQueue,
                        cacheSupportApiUtil.getIntegerValueForProperty(PersonalitySupportApiConstants.MAX_PRIME_SIZE_ALLOWED_IN_INSERT_CACHE_REQUEST,
                                PersonalitySupportApiConstants.MAX_PRIME_SIZE_DEFAULT_IN_INSERT_CACHE_REQUEST),
                        personalityInsertRequestData.getTenantShortName(),
                        executor,
                        cacheFacade::multiPutWhilePriming);
                personalityCacheInsertResponse.setPrimeCount(primeCount);
                personalityCacheInsertResponse.setMessage(PersonalitySupportApiConstants.SUBMIT_REQUEST_SUCCESS);
            }catch(Exception ex) {
                LOGGER.error("SupportAPI : Exception occurred while inserting personality cache", ex);
                personalityCacheInsertResponse.setMessage(PersonalitySupportApiConstants.SUBMIT_REQUEST_FAILED);
            }
        }
        return personalityCacheInsertResponse;
    };

    public Response processCacheInsert(PersonalityCacheInsertRequest personalityCacheInsertRequest) {
        try {
            // Validation checks using existing validators
            validatePersonalityCacheInsertRequest(personalityCacheInsertRequest);
            List<String> tenantList = getTenantList(personalityCacheInsertRequest.getTenantPersonalityRequestData());
            List<PersonalityInsertRequestData> tenantPersonalityRequestList = personalityCacheInsertRequest.getTenantPersonalityRequestData();
            ExecutionContext<List<PersonalityInsertRequestData> , String , PersonalityCacheInsertResponse> executionContext = new ExecutionContext<>();
            setExecutionContextParams(tenantList, tenantPersonalityRequestList, executionContext);
            Object executorResponse = apiExecutor.execute(executionContext);
            int httpCode = Response.Status.OK.getStatusCode();
            return Response.status(httpCode).entity(executorResponse.toString()).build();
        } catch (APIException apiException) {
            LOGGER.error("SupportAPI : APIException occurred in processCacheInsert() method", apiException);
            throw apiException;
        } catch (Exception e) {
            LOGGER.error("Exception occurred while inserting personality cache", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity("Error inserting personality cache").build();
        }
    }

    private List<String> getTenantList(List<PersonalityInsertRequestData> insertRequestList) {
        List<String> tenantList = new ArrayList<>();
        insertRequestList.forEach(personalityrequest-> {
            tenantList.add(personalityrequest.getTenantShortName());
        });
        return tenantList;
    }
}