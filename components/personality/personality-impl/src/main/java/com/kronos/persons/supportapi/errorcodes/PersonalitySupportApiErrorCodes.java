/*
 * *****************************************************************************
 * Copyright (c) 2020 Kronos, Inc. All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * Kronos, Inc. ("Confidential Information").  You shall not
 * Copyright (c) 2020 Kronos Inc. All Rights Reserved.
 *
 * KRONOS MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE
 * SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
 * PURPOSE, OR NON-INFRINGEMENT. KRONOS SHALL NOT BE LIABLE FOR ANY DAMAGES
 * SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR DIS<PERSON>IBUTING
 * THIS SOFTWARE OR ITS DERIVATIVES.
 ****************************************************************************
 *
 **/
package com.kronos.persons.supportapi.errorcodes;

/**
 * This class defines the error-codes which will be used in BGP  support API's
 * 
 * <AUTHOR>
 *
 */
public class PersonalitySupportApiErrorCodes {
	
	private PersonalitySupportApiErrorCodes() {
		
	}

	public static final String EMPTY_REQUEST_ERROR_CODE = "WCO-129500";
	
	public static final String DUPLICATE_TENANT_FOUND_IN_REQUEST_ERROR_CODE = "WCO-129501";
	
	public static final String INVALID_CACHE_NAME_PROVIDED_IN_REQUEST = "WCO-129502";
	
	public static final String TENANT_SHORT_NAME_MISSING_IN_REQUEST = "WCO-129503";
	
	public static final String INVALID_TENANT_ERROR = "WCO-129504";
	
	public static final String PERSON_NUMBER_MISSING_IN_REQUEST_ERROR_CODE = "WCO-129505";
	
	public static final String MAXIMUM_PERSONS_ALLOWED_IN_REQUEST = "WCO-129506";
	
	public static final String INVALID_PERSON_ID_PROVIDED_IN_REQUEST = "WCO-129507";
	
	public static final String INVALID_EVICT_FROM_IDENTIFIER = "WCO-129508";
	
	public static final String EXCEPTION_OCCURRED_IN_PROCESSING_CACHING_REQUEST = "WCO-129509";
	
	public static final String MAXIMUM_TENANTS_ALLOWED_IN_REQUEST = "WCO-129510";
	
	public static final String EXCEPTION_OCCURRED_WHILE_VALIDATING_PERSON_CACHE = "WCO-129511";
	
	public static final String DUPLICATE_PERSON_FOUND_IN_REQUEST_ERROR_CODE = "WCO-129512";

	public static final String TENANT_NAME_MISSING_IN_REQUEST_ERROR_CODE = "WCO-129503";

	public static final String DUPLICATE_PERSON_FOUND_IN_REQUEST = "WCO-129513";

	public static final String MULTI_NODE_PRIMING_NOT_ENABLED = "WCO-129514";
}
