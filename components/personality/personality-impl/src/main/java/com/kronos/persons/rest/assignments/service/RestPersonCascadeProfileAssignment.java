package com.kronos.persons.rest.assignments.service;

import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.people.assignment.cache.api.IAssignmentCacheNotifier;
import com.kronos.persons.rest.assignments.model.AssignmentProfileRequestBean;
import com.kronos.persons.rest.assignments.model.AssignmentProfileRequestBeanWrapper;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.utils.BulkProcessor;
import com.kronos.persons.utils.NewBatchProcessor;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("RestPersonCascadeProfileAssignment")
public class RestPersonCascadeProfileAssignment implements
		IRestPersonCascadeProfileAssignment {

	private static KLogger logger = KLoggerFactory.getKLogger(RestPersonCascadeProfileAssignment.class);

	private static final String CASCADE_PROFILE = "CASCADE_PROFILE";

	@Inject
	PersonCascadeProfileAssignmentService personCascadeServiceImpl;
	
	@Inject
	ValidatorUtils validatorutils;
	
	@Inject
	PersonAssignmentHelper<AssignmentProfileRequestBean> personAssignmentHelper;

	@Inject
	IAssignmentCacheNotifier assignmentCacheNotifier;
	
	public static final String PERSON_CASCADE_PROFILE_ASSIGN = "PersonCascadeProfileAssignment";
	
	@Override
	public AssignmentProfileRequestBean retrieveByPersonNumber(String personNumber) {
		try {
			return personCascadeServiceImpl.retrieveByPersonNumber(personNumber);
		} catch (APIException apiEx) {
			throw apiEx;
		} catch (GenericException e) {
			throw PrsnException.getAPIException(e);
		} catch (Exception e) {
			throw PrsnException.getAPIException(e);
		}
	}

	@Override
	public AssignmentProfileRequestBean retrieveByPersonId(Long personId) {
		try {
			return personCascadeServiceImpl.retrieveByPersonId(personId);
		} catch (APIException apiEx) {
			throw apiEx;
		} catch (GenericException e) {
			throw PrsnException.getAPIException(e);
		} catch (Exception e) {
			throw PrsnException.getAPIException(e);
		}
	}
	
	@Override
	public List<AssignmentProfileRequestBean> retrieveList(ExtensionSearchCriteria searchCriteria) {
		Function<PersonIdentityBean, AssignmentProfileRequestBean> retrieveFunction = (PersonIdentityBean p) -> {
			return personCascadeServiceImpl.retrieve(p);
		};
		return personAssignmentHelper.getPersonAssignmentList(searchCriteria, retrieveFunction);	
	}

	@Override
	public AssignmentProfileRequestBean update(AssignmentProfileRequestBean requestBean) {
		Personality personality = null;
		try {
			ResponseHandler.validateForNullRequest(requestBean);
			personality = personCascadeServiceImpl.updateRequest(requestBean);
			AssignmentProfileRequestBean aprb = ResponseHandler.apiSuccessResponse(requestBean, personality);
			sendNotificationToCascadeAssignmentCache(Arrays.asList(String.valueOf(personality.getPersonId().longValue())));
			return aprb;
		} catch (APIException apiEx) {
			throw apiEx;
		} catch (GenericException e) {
			throw PrsnException.getAPIException(e);
		} catch (Exception e) {
			throw PrsnException.getAPIException(e);
		}
	}

	@Override
	public void delete(AssignmentProfileRequestBean requestBean) {
		try {
			ResponseHandler.validateForNullRequest(requestBean);
			Personality personality = personCascadeServiceImpl.deleteRequest(requestBean);
			sendNotificationToCascadeAssignmentCache(
					Arrays.asList(String.valueOf(personality.getPersonId().longValue())));
		} catch (APIException apiEx) {
			throw apiEx;
		} catch (GenericException e) {
			throw PrsnException.getAPIException(e);
		} catch (Exception e) {
			throw PrsnException.getAPIException(e);
		}
	}

	@Override
	public List<AssignmentProfileRequestBean> multiUpdate(List<AssignmentProfileRequestBean> requestDataList) {
		ResponseHandler.validateForNullRequest(requestDataList);
		ResponseHandler.validServiceLimit(requestDataList);
		List<AssignmentProfileRequestBeanWrapper> wrapperRequestDataList = requestDataList
				.stream()
				.map(AssignmentProfileRequestBeanWrapper::new)
				.collect(Collectors.toList());
		BulkProcessor<AssignmentProfileRequestBeanWrapper, AssignmentProfileRequestBean> bulkProcessor =
				new BulkProcessor<>(wrapperRequestDataList, requestMultiUpdateProcessor);
		return bulkProcessor.process();
	}

	private Function<List<AssignmentProfileRequestBeanWrapper>,
			List<AssignmentProfileRequestBean>> requestMultiUpdateProcessor = (
			List<AssignmentProfileRequestBeanWrapper> requestDataWrapperList) -> {
			List<AssignmentProfileRequestBeanWrapper> listForUpdate = requestDataWrapperList.stream()
					.peek(beanWrapper -> personCascadeServiceImpl.validate(beanWrapper))
					.filter(beanWrapper -> Objects.isNull(beanWrapper.getApiException()))
					.collect(Collectors.toList());
			personCascadeServiceImpl.multiUpdate(listForUpdate);
			List<AssignmentProfileRequestBean> list =  listForUpdate.stream()
					.filter(beanWrapper -> Objects.isNull(beanWrapper.getApiException()))
					.map(AssignmentProfileRequestBeanWrapper::getBean)
					.collect(Collectors.toList());
			sendNotificationToCascadeAssignmentCache(
					list.stream().map(bean -> String.valueOf(bean.getPersonIdentity().getPersonKey()))
							.collect(Collectors.toList()));
			return list;
	};

	@Override
	public void multiDelete(
			List<AssignmentProfileRequestBean> requestDataList) {
		ResponseHandler.validateForNullRequest(requestDataList);
		ResponseHandler.validServiceLimit(requestDataList);
		List<String> listOfIds = new LinkedList<>();
		Function<AssignmentProfileRequestBean, AssignmentProfileRequestBean> requestProcessor = (
				AssignmentProfileRequestBean reqData) -> {
			try {
				Personality personality = personCascadeServiceImpl.deleteRequest(reqData);
				AssignmentProfileRequestBean retval = ResponseHandler.apiSuccessResponse(reqData, personality);
				listOfIds.add(String.valueOf(personality.getPersonId().longValue()));
				return retval;
			} catch (APIException apiEx) {
				throw apiEx;
			} catch (GenericException e) {
				throw PrsnException.getAPIException(e);
			} catch (Exception e) {
				throw PrsnException.getAPIException(e);
			}
		};
		NewBatchProcessor<AssignmentProfileRequestBean, AssignmentProfileRequestBean> batchprocessor = new NewBatchProcessor<>(requestDataList, requestProcessor);
        try {
        	batchprocessor.process();	
        } finally {
        	sendNotificationToCascadeAssignmentCache(listOfIds);
        }
		
	}

	public void sendNotificationToCascadeAssignmentCache(List<String> listOfIds) {
		logger.info("*** wfd-170543 *** RestPersonCascadeProfileAssignment:sendNotificationToCascadeAssignmentCache: Start of method. ListOfIdsSize: {}",Optional.ofNullable(listOfIds).map(List::size).orElse(null));
		assignmentCacheNotifier.sendMessageByPersonID(CASCADE_PROFILE, Optional.ofNullable(listOfIds)
				.orElse(Collections.emptyList()).stream().filter(Objects::nonNull).collect(Collectors.toList()));
	}
}
