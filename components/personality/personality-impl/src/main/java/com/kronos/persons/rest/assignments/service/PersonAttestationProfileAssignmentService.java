package com.kronos.persons.rest.assignments.service;

import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.dataaccess.entity.AttestationProfileAssignment;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;

import com.kronos.people.personality.dataaccess.repository.AttestationProfileAssignmentRepository;
import com.kronos.persons.rest.assignments.model.EmployeeRefs;
import com.kronos.persons.rest.assignments.model.PersonAttestationAssignmentCriteria;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignment;
import com.kronos.persons.rest.beans.HyperFindFilterBean;
import com.kronos.persons.utils.NewBatchProcessor;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileDTO;
import com.kronos.timekeeping.service.attestation.api.service.AttestationProfileAssignmentService;
import com.kronos.timekeeping.service.attestation.api.service.AttestationProfileSetupService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kronos.people.personality.service.PersonAssignmentService;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignmentDTO;
import com.kronos.persons.rest.assignments.service.converter.AttestationProfileAssignmentConverter;
import com.kronos.persons.rest.assignments.validation.PersonAttestationProfileAssignmentValidator;
import com.kronos.persons.utils.PersonAttestationProfileAssignmentConverter;
import com.kronos.timekeeping.service.attestation.api.dto.AttestationProfileAssignmentDTO;


@Service
@Transactional
public class PersonAttestationProfileAssignmentService {

    private final PersonAssignmentService personAssignmentService;

    private final AttestationProfileAssignmentService attestationProfileAssignmentService;

    private final PersonAttestationProfileAssignmentValidator validator;

    private final AttestationProfileAssignmentConverter converter;

    private AttestationProfileAssignmentRepository attestationProfileAssignmentRepository;

    private AttestationProfileSetupService attestationProfileSetupService;

    private final PersonAttestationProfileAssignmentConverter personAttestationProfileAssignmentConverter;

    @Inject
    public PersonAttestationProfileAssignmentService(
            PersonAssignmentService personAssignmentService,
            PersonAttestationProfileAssignmentValidator validator,
            AttestationProfileAssignmentService attestationProfileAssignmentService,
            AttestationProfileAssignmentConverter converter,
            AttestationProfileAssignmentRepository attestationProfileAssignmentRepository,
            AttestationProfileSetupService attestationProfileSetupService,
            PersonAttestationProfileAssignmentConverter personAttestationProfileAssignmentConverter) {
        this.personAssignmentService = personAssignmentService;
        this.validator = validator;
        this.attestationProfileAssignmentService = attestationProfileAssignmentService;
        this.converter = converter;
        this.attestationProfileAssignmentRepository = attestationProfileAssignmentRepository;
        this.attestationProfileSetupService = attestationProfileSetupService;
        this.personAttestationProfileAssignmentConverter = personAttestationProfileAssignmentConverter;
    }

    public List<PersonAttestationProfileAssignmentDTO> getAttestationProfileAssignments(Long personId) {
        return getAttestationProfileAssignments(personId, false);
    }


    public List<PersonAttestationProfileAssignmentDTO> getAttestationProfileAssignments(Long personId, boolean isAssignToManagerRole) {
        validator.validateReadAccess(personId);
        validator.validateManagerRoleReadFAP(isAssignToManagerRole);
        return findByPersonId(personId, isAssignToManagerRole);
    }

    public List<PersonAttestationProfileAssignmentDTO> getAttestationProfileAssignments(String personNumber) {
        return getAttestationProfileAssignments(personNumber, false);
    }

    public List<PersonAttestationProfileAssignmentDTO> getAttestationProfileAssignments(String personNumber, boolean isAssignToManagerRole) {
        Long personId = validator.validateReadAccessAndGetPersonId(personNumber);
        validator.validateManagerRoleReadFAP(isAssignToManagerRole);
        return findByPersonId(personId, isAssignToManagerRole);
    }

    private List<PersonAttestationProfileAssignmentDTO> findByPersonId(Long personId, boolean isAssignToManagerRole) {
        if (isAssignToManagerRole && !validator.hasManagerLicense(personId)){
            return Collections.emptyList();
        }

        return attestationProfileAssignmentService.getAttestationProfileAssignments(personId, isAssignToManagerRole).stream()
            .map(n -> converter.toPersonAttestationProfileAssignmentDTO(n, personId))
            .sorted(Comparator.comparing(PersonAttestationProfileAssignmentDTO::getEffectiveDate))
            .collect(Collectors.toList());
    }

    public PersonAttestationProfileAssignmentDTO create(Long personId, PersonAttestationProfileAssignmentDTO dto) {
        validator.validateCreateAccess(personId, dto);
        return createAttestationProfileAssignment(personId, dto);
    }

    public PersonAttestationProfileAssignmentDTO create(PersonAttestationProfileAssignmentDTO dto) {
        Long personId = validator.validateCreateAccessAndGetPersonId(dto);
        return createAttestationProfileAssignment(personId, dto);
    }

    public Map<Integer, PersonAttestationProfileAssignment> multiUpsert(Map<Integer, APIException> excptionHolder, Map<Integer, PersonAttestationProfileAssignment> assignments,Boolean mergeEffectiveDating) {
        validator.validateCreateAccess(excptionHolder, assignments);
        validator.validatePersonAttestationProfileAssignments(excptionHolder, assignments);
        return continueWithValidPersonAttestationProfileAssignments(excptionHolder, assignments,mergeEffectiveDating);
    }

    public List<PersonAttestationProfileAssignment> getPersonAttestationProfileAssignment(EmployeeRefs employees, PersonAttestationAssignmentCriteria criteria) {
        validator.validateManagerRoleReadFAP(criteria.isIncludeManagerRole());

        List<ObjectRef> employeeRefs = new ArrayList<>(employees.getEmployees().getExclusiveObjectRefList());

        Map<ObjectRef, Object> validatedEmployeeRefs = validateAndDistinctPersonList(employeeRefs, employees.getHyperFindFilter());
        processPersonList(validatedEmployeeRefs, criteria);

        Function<Object, PersonAttestationProfileAssignment> requestProcessor = (Object entry) -> {
            if( entry instanceof APIException) {
                throw (APIException)entry;
            }
            else {
                return (PersonAttestationProfileAssignment)entry;
            }
        };

        NewBatchProcessor<Object, PersonAttestationProfileAssignment> batchprocessor =
                new NewBatchProcessor<>(validatedEmployeeRefs.values(), requestProcessor);
        return batchprocessor.process();
    }

    /**
     * Return Map with ObjectRef as key and null as value when employee is resolved or APIException when not
     *
     */
    private Map<ObjectRef, Object> validateAndDistinctPersonList(List<ObjectRef> employeeRefs, HyperFindFilterBean hyperFindFilter) {
        Map<ObjectRef, Object> validatedEmployeeRefs = new LinkedHashMap<>();

        List<ObjectRef> distinctList = employeeRefs.stream().distinct().collect(Collectors.toList());

        List<Long> employeeIds = distinctList.stream().filter(employee -> employee.getId() != null)
                .map(ObjectRef::getId).collect(Collectors.toList());
        List<String> personNumbers = distinctList.stream().filter(employee -> employee.getQualifier() != null && employee.getId() == null)
                .map(ObjectRef::getQualifier).collect(Collectors.toList());

        Map<Long, Object> employeeIdsMap = validator.validateReadAccessAndGetObjectRefsByPersonIds(employeeIds, hyperFindFilter);
        Map<String, Object> personNumsMap = validator.validateReadAccessAndGetObjectRefsByPersonNums(personNumbers, hyperFindFilter);

        for(ObjectRef ref : distinctList) {
            Object o = ref.getId() != null ? employeeIdsMap.get(ref.getId()) : personNumsMap.get(ref.getQualifier());
            if(o instanceof ObjectRef) {
                ObjectRef resolvedEmployeeRef = (ObjectRef)o;
                if(!validatedEmployeeRefs.containsKey(resolvedEmployeeRef)) {
                    validatedEmployeeRefs.put(resolvedEmployeeRef, null);
                }
            }
            else {
                validatedEmployeeRefs.put(ref, o);
            }
        }
        return validatedEmployeeRefs;
    }

    /**
     *   Re-populate map with retrieved list of assignments for resolved employees
     */
    private void processPersonList(Map<ObjectRef, Object> validatedEmployeeRefs, PersonAttestationAssignmentCriteria criteria) {
        List<ObjectRef> employeeRefsToProcess = validatedEmployeeRefs.entrySet().stream()
                .filter(entry -> Objects.isNull(entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if(employeeRefsToProcess.isEmpty()) {
            //there are no valid employees in request  - so nothing to process
            return;
        }
        List<Long> personIds = employeeRefsToProcess.stream()
                .map(ObjectRef::getId)
                .collect(Collectors.toList());

        Map<Long, PersonAttestationProfileAssignment> result = this.getAttestationProfileAssignments(personIds, criteria);

        for(ObjectRef employeeRef : employeeRefsToProcess) {
            PersonAttestationProfileAssignment value = result.get(employeeRef.getId());
            value.setEmployee(employeeRef);
            validatedEmployeeRefs.put(employeeRef, value);
        }
    }

    /**
     *   Retrieve all AttestationProfileAssignment in one single DB call
     *   and collect them in map where key is personId and value is PersonAttestationProfileAssignment
     */
    private Map<Long, PersonAttestationProfileAssignment> getAttestationProfileAssignments(List<Long> personIds,
                                                                                            PersonAttestationAssignmentCriteria criteria) {
        List<AttestationProfileAssignment> profileAssignments = attestationProfileAssignmentRepository
                .findByPersonIdsAndManagerRoles(personIds, getIsManagerRoles(criteria));

        Map<Long, List<AttestationProfileAssignment>> assignmentsByPersonId = new HashMap<>();
        Map<Long, AttestationProfileDTO> profilesMap = new HashMap<>();
        profileAssignments.stream().forEach(assignment -> {
            assignmentsByPersonId.computeIfAbsent(assignment.getPersonAssignmentId(), k -> new ArrayList<>()).add(assignment);
            profilesMap.computeIfAbsent(assignment.getProfileId(),
                    profileId -> attestationProfileSetupService.getAttestationProfile(profileId));
        });

        Map<Long, PersonAttestationProfileAssignment> result = new HashMap<>(personIds.size());
        PersonAttestationProfileAssignment personAttestationProfileAssignment = null;
        for(Long personId : personIds) {
            personAttestationProfileAssignment = converter.toPersonAttestationProfileAssignment(
                    assignmentsByPersonId.getOrDefault(personId, Collections.emptyList()), profilesMap, criteria);
            clearManagerRoleAssignmentsIfNoManagerLicense(personId, personAttestationProfileAssignment);
            result.put(personId, personAttestationProfileAssignment);
        }
        return result;
    }

    private List<Boolean> getIsManagerRoles(PersonAttestationAssignmentCriteria criteria) {
        List<Boolean> isManagerRoles = new ArrayList<>();
        if (criteria.isIncludeEmployeeRole()) {
            isManagerRoles.add(Boolean.FALSE);
        }
        if (criteria.isIncludeManagerRole()) {
            isManagerRoles.add(Boolean.TRUE);
        }
        return isManagerRoles;
    }

    private void clearManagerRoleAssignmentsIfNoManagerLicense(Long personId, PersonAttestationProfileAssignment value) {
        if (CollectionUtils.isNotEmpty(value.getManagerRoleAttestationProfileAssignments())
                && !validator.hasManagerLicense(personId)) {
            value.getManagerRoleAttestationProfileAssignments().clear();
        }
    }

    private Map<Integer, PersonAttestationProfileAssignment> continueWithValidPersonAttestationProfileAssignments(Map<Integer, APIException> excptionHolder, Map<Integer, PersonAttestationProfileAssignment> assignments,
    		Boolean mergeEffectiveDating) {
        return assignments.isEmpty()? assignments: personAttestationProfileAssignmentConverter.convertToAPI(personAssignmentService
                .multiUpsert(excptionHolder, personAttestationProfileAssignmentConverter.convertToDTO(assignments),mergeEffectiveDating));
    }

    private PersonAttestationProfileAssignmentDTO createAttestationProfileAssignment(Long personId,
                                                                                     PersonAttestationProfileAssignmentDTO dto) {
        AttestationProfileAssignmentDTO profileAssignmentDTO = converter.toAttestationProfileAssignmentDTO(dto);
        AttestationProfileAssignmentDTO updatedPersonAssignmentDTO = personAssignmentService.addProfileAssignment(
                personId, profileAssignmentDTO);

        return converter.toPersonAttestationProfileAssignmentDTO(updatedPersonAssignmentDTO, personId);
    }
}
