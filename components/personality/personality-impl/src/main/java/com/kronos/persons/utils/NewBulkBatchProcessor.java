package com.kronos.persons.utils;

import com.kronos.container.api.exception.APIException;
import com.kronos.container.api.util.APIExceptionDetailResult;
import com.kronos.persons.rest.exception.ExceptionConstants;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;

public class NewBulkBatchProcessor<T, U>  {

    private Collection<T> requestDataCollection;
    private Function<T, List<U>> bulkRequestProcessor;
    
    // We need to preserve the existing behavior of the PercentAllocationRuleAssignment API
    // and also fix the bug where we incorrectly return an error if we have an empty success list.
    // If we ever are able to rev the API this should be removed.
    private boolean failOnNoSuccesses = false;

    public NewBulkBatchProcessor(Collection<T> requestDataCollection, Function<T, List<U>> bulkRequestProcessor) {
        this.requestDataCollection = requestDataCollection;
        this.bulkRequestProcessor = bulkRequestProcessor;
    }

    public List<U> bulkProcess() {

        APIException partialSuccess = new APIException(ExceptionConstants.PARTIAL_SUCCESS);
        APIException allError = new APIException(ExceptionConstants.ALL_RECORDS_FAILED);

        List<APIExceptionDetailResult<?>> resultList = new ArrayList<>();
        List<U> successList = new ArrayList<>();
        List<APIExceptionDetailResult<?>> errorList = new ArrayList<>(); //error tracker for partial success

        //stream process request data
        this.requestDataCollection.stream().forEach(requestData -> {
            try{
                List<U> responseObjList  = this.bulkRequestProcessor.apply(requestData);
                responseObjList.forEach(success -> {
                    successList.add(success);
                    resultList.add(new APIExceptionDetailResult<>(success));
                });
            } catch (APIException apiExp) {
                APIExceptionDetailResult<APIException> error = new APIExceptionDetailResult<>(apiExp);
                resultList.add(error);
                errorList.add(error);
            }
        });

        if (failOnNoSuccesses && successList.isEmpty()) {
            allError.setResults(resultList);
            throw allError;
        }
        if (errorList.size() == this.requestDataCollection.size()) {
            allError.setResults(resultList);
            throw allError;
        }
        if (errorList.size() > 0) {
            partialSuccess.setResults(resultList);
            throw partialSuccess;
        }
        return successList;
    }
    
    public void setFailOnNoSucesses(boolean failOnNoSuccesses) {
    	this.failOnNoSuccesses = failOnNoSuccesses;
    }
}
