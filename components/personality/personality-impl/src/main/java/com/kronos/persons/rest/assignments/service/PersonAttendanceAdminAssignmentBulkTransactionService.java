package com.kronos.persons.rest.assignments.service;

import com.google.common.collect.Lists;
import com.kronos.wfc.absencemgmt.service.business.people.PersonAttendanceAdministratorAssignment;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.entity.business.AbstractEntity;
import com.kronos.wfc.platform.persistence.framework.SQLStatement;
import com.kronos.wfc.platform.persistence.framework.Transaction;
import com.kronos.wfc.platform.persistence.framework.statement.SqlString;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Transaction service for bulk update all PersonAttendanceAdministratorAssignments with one administratorPersonality.
 * Copyright (C) 2019 Kronos.com
 * Date: Jul 05, 2019
 *
 * <AUTHOR>
 */
public final class PersonAttendanceAdminAssignmentBulkTransactionService extends Transaction {

    private static final String PERSON_ATTENDANCE_ADMINISTRATOR_ASSIGNMENT_BULK_UPDATE =
            "com.kronos.wfc.absencemgmt.service.business.people.PersonAttendanceAdministratorAssignment.bulkUpdate";

    private Personality administratorPersonality;
    private List<PersonAttendanceAdministratorAssignment> personAttendanceAdministratorAssignments;

    private PersonAttendanceAdminAssignmentBulkTransactionService(
            Personality administratorPersonality,
            List<PersonAttendanceAdministratorAssignment> personAttendanceAdministratorAssignments) {
        this.personAttendanceAdministratorAssignments = personAttendanceAdministratorAssignments;
        this.administratorPersonality = administratorPersonality;
    }

    /**
     * Get new {@link PersonAttendanceAdminAssignmentBulkTransactionService}.
     *
     * @param administratorPersonality {@link Personality}
     * @param personAttendanceAdministratorAssignments the list of {@link PersonAttendanceAdministratorAssignment}
     * @return Transaction service for update
     */
    public static PersonAttendanceAdminAssignmentBulkTransactionService getTransactionService(
            Personality administratorPersonality,
            List<PersonAttendanceAdministratorAssignment> personAttendanceAdministratorAssignments) {
        return new PersonAttendanceAdminAssignmentBulkTransactionService(administratorPersonality,
                personAttendanceAdministratorAssignments);
    }

    /**
     * Update administrator for all personAttendanceAdministratorAssignments.
     */
    @Override
    protected void transaction() {
        List<String> personIds = personAttendanceAdministratorAssignments.stream()
                .peek(AbstractEntity::deleteFromCache)
                .map(personality -> personality.getObjectId().toString())
                .collect(Collectors.toList());
        SqlString adminId = new SqlString(administratorPersonality.getPersonId().toString());
        SQLStatement sqlStatement = new SQLStatement(SQLStatement.UPDATE,
                PERSON_ATTENDANCE_ADMINISTRATOR_ASSIGNMENT_BULK_UPDATE,
                Lists.newArrayList(adminId, new SqlString(StringUtils.join(personIds, ","))));
        sqlStatement.execute();
    }
}
