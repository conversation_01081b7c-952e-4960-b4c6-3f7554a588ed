package com.kronos.persons.context.service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.function.Predicate;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.logging.slf4jadapter.util.Loggable;
import com.kronos.people.personality.dataaccess.service.IPersonReadService;
import com.kronos.persons.context.utils.PeopleConverter;
import com.kronos.persons.rest.beans.LightPersonInformation;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.model.LightPersonInformationSearchCriteria;
/**
 * 
 * <AUTHOR>
 *
 */

@Named
public class PersonAoidServiceImpl implements IPersonAoidService {
	
	private KLogger LOGGER = KLoggerFactory.getKLogger(PersonAoidServiceImpl.class);

	private  final String MAX_TIME_SPAN = "com.kronos.person.rest.service.without.aoid.max.time.span";
	
	private  final String DEFAULT_MAX_COUNT_SIZE = "com.kronos.person.rest.service.default.max.count.size";
	private  final String DEFAULT_MAXTIMESPAN = "14";
	
	private  final String DEFAULT_MAXCOUNTSIZE = "20";

	@Inject
	private IPersonReadService personReadService;

	@Inject
	private PeopleConverter peopleConverter;

	@Inject
	private IKProperties kronosProperties;

	private Predicate<Object> nullCheck = x -> Objects.nonNull(x);
	
	

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.kronos.persons.context.service#getPersonWithoutAoid(LightPersonInformationSearchCriteria searchCriteria)
	 */
	
	@Override
	@Loggable
	public LightPersonInformation getPersonWithoutAoid(LightPersonInformationSearchCriteria searchCriteria) {
		LocalDateTime startDate = null;
		LocalDateTime endDate = null;
		if (nullCheck.test(searchCriteria.getWhere().getDateRange())) {
			 startDate = searchCriteria.getWhere().getDateRange().getStartDateTime();
			 endDate = searchCriteria.getWhere().getDateRange().getEndDateTime();	
		}
		Long pageSize = searchCriteria.getCount();
		Integer maxCountSize = Integer.parseInt(kronosProperties.getProperty(DEFAULT_MAX_COUNT_SIZE, DEFAULT_MAXCOUNTSIZE));
		Integer maxTimeSpan = Integer.parseInt(kronosProperties.getProperty(MAX_TIME_SPAN, DEFAULT_MAXTIMESPAN));
		Long pageNum = searchCriteria.getIndex();
		Integer pageIndex = 0;
		if (pageSize == null || pageSize < 1) {
			pageSize = maxCountSize.longValue();
		}
		if (pageSize > maxCountSize) {
			throwPageSizeException(maxCountSize);
		}

		if (pageNum != null && !(pageNum < 1)) {
			pageIndex = pageNum.intValue() - 1;
		}
		validateStartandEndDate(startDate, endDate, maxTimeSpan);
		LocalDateTime todaySystemDate = LocalDateTime.now();
		if (Objects.isNull(startDate) && Objects.isNull(endDate)) {
			startDate = todaySystemDate.minusDays(maxTimeSpan);
			endDate = todaySystemDate;
		}
		LightPersonInformation lightPersonInformation = null;
		try {
			lightPersonInformation = peopleConverter.convertRestToEntityBeans(personReadService.findWithoutaoid(pageIndex.longValue(), pageSize, startDate, endDate));
		} catch (APIException apiExp) {
			throwException(apiExp);
		} catch (Exception exp) {
			LOGGER.error("Exception while geting data:-", exp);
			throw new APIException(ExceptionConstants.GENERIC_EXCEPTION);
		}

		return lightPersonInformation;
	}

	private void throwException(APIException apiExp) {
		if (apiExp.getErrorCode().equals(ExceptionConstants.DATAERROR)
				||apiExp.getErrorCode().equals(ExceptionConstants.NO_DATA_NOT_FOUND_FOR_PAYLOAD)) {
			throw apiExp;
		}
		LOGGER.error("APIException while geting data:-", apiExp);
		throw new APIException(ExceptionConstants.GENERIC_EXCEPTION);
	}

	private void throwPageSizeException(Integer maxCountSize) {
		LOGGER.error("Supplied Count's value exceeds maximum allowed limit");
		APIException exception = new APIException(ExceptionConstants.ERROR_ZERO_PAGE_SIZE);
		exception.addUserParameter(ExceptionConstants.COUNTLIMIT, String.valueOf(maxCountSize));
		throw exception;
	}
	
	
     
	 private void validateStartandEndDate(LocalDateTime startDate, LocalDateTime endDate,Integer maxTimeSpan){
			LocalDateTime todaySystemDate = LocalDateTime.now();
			
			if (nullCheck.test(startDate) && nullCheck.test(endDate)) {
				if (startDate.isBefore(todaySystemDate) || endDate.isBefore(todaySystemDate)) {
					dateRangeValidation(startDate, endDate, maxTimeSpan);
				}
			} else if (Objects.isNull(startDate) && Objects.isNull(endDate)) {
				startDate = todaySystemDate.minusDays(maxTimeSpan);
				endDate = todaySystemDate;
			} else {
				if (Objects.isNull(startDate)) {
					LOGGER.error("Error: startDate is null");
					throw new APIException(ExceptionConstants.NULL_START_DATE_EXCEPTION);
				} else {
					LOGGER.error("Error: endDate is null");
					throw new APIException(ExceptionConstants.NULL_END_DATE_EXCEPTION);
				}
			}
			}
			private void dateRangeValidation(LocalDateTime startDate, LocalDateTime endDate, Integer maxTimeSpan) {
				if (startDate.isAfter(endDate)) {
					LOGGER.error("Error: time spain is greater than given limit");
					throw new APIException(ExceptionConstants.END_DATE_BEFORE_START_DATE_EXCEPTION);
				}
				long noOfDaysBetween = ChronoUnit.DAYS.between(startDate, endDate);
				LOGGER.error("Info: noOfDaysBetween:-"+noOfDaysBetween);
				if ((noOfDaysBetween) > maxTimeSpan) {
					LOGGER.error("Error: Supplied date range exceeds maximum allowed date range");
					APIException exception = new APIException(ExceptionConstants.SERVICE_LIMIT_EXCEDDED);
					exception.addUserParameter(ExceptionConstants.DAYRANGE, String.valueOf(maxTimeSpan));
					throw exception;
				}
			}

}
