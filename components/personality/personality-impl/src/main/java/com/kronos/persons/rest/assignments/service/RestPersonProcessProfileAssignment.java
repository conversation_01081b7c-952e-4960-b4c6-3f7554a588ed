package com.kronos.persons.rest.assignments.service;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import jakarta.inject.Inject;

import com.kronos.wfc.commonapp.people.business.personality.delete.PersonNotification;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kronos.authz.api.annotations.IsPermitted;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.ProcessProfileAssignmentRequestBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.utils.NewBatchProcessor;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
/**
 * 
 * <AUTHOR>
 *
 */
@Service("RestPersonProcessProfileAssignment")
public class RestPersonProcessProfileAssignment implements
		IRestPersonProcessProfileAssignment {

	private static final String WBAP_PROFILE_ASSIGNMENT = "WBAP_PROFILE_ASSIGNMENT";
	private static final String ADD = "ADD";
	
	@Inject
	private PersonProcessProfileAssignmentService personProcessProfileAssignmentService;
	
	@Inject @Lazy
    PersonAssignmentHelper<ProcessProfileAssignmentRequestBean> assignmentHeplper;

	
	@IsPermitted(accessControlPoint =WBAP_PROFILE_ASSIGNMENT)
	@Override
	public ProcessProfileAssignmentRequestBean retrieveByPersonNumber(String personNumber) {
		PersonIdentityBean identityBean = new PersonIdentityBean();
		identityBean.setPersonNumber(personNumber);
		return retrieveProcessProfileForIdentity(identityBean);
	}

	@IsPermitted(accessControlPoint =WBAP_PROFILE_ASSIGNMENT)
	@Override
	public ProcessProfileAssignmentRequestBean retrieveByPersonId(Long personId) {
		PersonIdentityBean identityBean = new PersonIdentityBean();
		identityBean.setPersonKey(personId);
		return retrieveProcessProfileForIdentity(identityBean);
	}
	
	private ProcessProfileAssignmentRequestBean retrieveProcessProfileForIdentity (PersonIdentityBean identityBean){
		try {
			return personProcessProfileAssignmentService.load(identityBean);
		} catch (APIException apiEx) {
			throw apiEx;
		} catch (GenericException e) {
			throw PrsnException.getAPIException(e);
		} catch (Exception e) {
			throw PrsnException.getAPIException(e);
		}
	}
	
	@IsPermitted(accessControlPoint =WBAP_PROFILE_ASSIGNMENT)
	@Override
    public List<ProcessProfileAssignmentRequestBean> retrieveList(ExtensionSearchCriteria searchCriteria) {
        return assignmentHeplper.getPersonAssignmentList(searchCriteria, personProcessProfileAssignmentService::load);
    }
	
    @IsPermitted(accessControlPoint =WBAP_PROFILE_ASSIGNMENT, action =ADD)
	@Override
	public ProcessProfileAssignmentRequestBean update(ProcessProfileAssignmentRequestBean requestBean) {
		Personality personality = personProcessProfileAssignmentService.update(requestBean);
		return ResponseHandler.apiSuccessResponse(requestBean, personality);
	}

    @IsPermitted(accessControlPoint =WBAP_PROFILE_ASSIGNMENT, action =ADD)
    @Override
    public List<ProcessProfileAssignmentRequestBean> multiUpdate(List<ProcessProfileAssignmentRequestBean> requestDataList) {
    	ResponseHandler.validateForNullRequest(requestDataList);
        ResponseHandler.validServiceLimit(requestDataList);
		List<String> personIds = new ArrayList<>();
        Function<ProcessProfileAssignmentRequestBean, ProcessProfileAssignmentRequestBean> requestProcessor = (
        		ProcessProfileAssignmentRequestBean reqData) -> {
                	try {
						Personality personality = personProcessProfileAssignmentService.update(reqData,false);
						if(personality != null) {
							personIds.add(personality.getPersonId().toString());
						}
						return ResponseHandler.apiSuccessResponse(reqData, personality);
                	}catch (APIException apiEx) {
        				throw apiEx;
        			} catch (GenericException e) {
        				throw PrsnException.getAPIException(e);
        			} catch (Exception e) {
        				throw PrsnException.getAPIException(e);
        			}
                };
		NewBatchProcessor<ProcessProfileAssignmentRequestBean, ProcessProfileAssignmentRequestBean> batchProcessor = new NewBatchProcessor<>(requestDataList, requestProcessor);
		List<ProcessProfileAssignmentRequestBean> responseBeanList = batchProcessor.process();
		PersonNotification.sendBulkUpdate(personIds,true);
		return responseBeanList;
	}

}
