/*
 * *****************************************************************************
 * Copyright (c) 2020 Kronos, Inc. All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * Kronos, Inc. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with Kronos.
 *
 * KRONOS MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE
 * SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
 * PURPOSE, OR NON-INFRINGEMENT. KRONOS SHALL NOT BE LIABLE FOR ANY DAMAGES
 * SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING
 * THIS SOFTWARE OR ITS DERIVATIVES.
 ****************************************************************************
 *
 **/
package com.kronos.persons.supportapi.processor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.BiFunction;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.ws.rs.core.Response;

import org.apache.commons.collections.CollectionUtils;
import org.json.JSONObject;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Objects;
import com.kronos.api.commoncomponent.async.APIExecutor;
import com.kronos.api.commoncomponent.async.ExecutionContext;
import com.kronos.api.commoncomponent.util.CommonUtils;
import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.people.personality.model.extension.AccrualExtension;
import com.kronos.people.personality.model.extension.AllExtension;
import com.kronos.people.personality.model.extension.DevicesExtension;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.SchedulingExtension;
import com.kronos.people.personality.model.extension.TimekeepingExtension;
import com.kronos.persons.rest.supportapi.extensions.ExtensionBuilderService;
import com.kronos.persons.rest.supportapi.util.ExtensionServiceUtil;
import com.kronos.persons.rest.supportapi.validation.CacheSupportApiUtil;
import com.kronos.persons.rest.supportapi.validation.SupportApiValidator;
import com.kronos.persons.supportapi.dto.EmployeeOutOfSync;
import com.kronos.persons.supportapi.dto.PersonalityCacheValidationRequest;
import com.kronos.persons.supportapi.dto.PersonalityRequestData;
import com.kronos.persons.supportapi.dto.PersonalityResponseData;
import com.kronos.persons.supportapi.errorcodes.PersonalitySupportApiConstants;
import com.kronos.persons.supportapi.errorcodes.PersonalitySupportApiErrorCodes;
import com.kronos.tenantprovider.api.TenantProvider;
import com.kronos.wfc.commonapp.people.business.personality.Personality;

/**
 * This class is used for validating person data present in cache by comparing it with data present in database
 * 
 * <AUTHOR>
 */
@Named
public class CacheValidationProcessor {

	@Inject
	APIExecutor apiExecutor;
	
	@Inject
	CommonUtils commonUtils;
	
	@Inject
	ExtensionBuilderService extensionBuilderService;
	
	@Inject 
	CacheSupportApiUtil cacheSupportApiUtil;
	
	@Inject
	TenantProvider tenantProvider;
	
	@Inject
	ExtensionServiceUtil extensionServiceUtil;
	
	@Inject
	SupportApiValidator supportApiValidator;
	
	private static final KLogger LOGGER = KLoggerFactory.getKLogger(CacheValidationProcessor.class);
	
	/**
	 * This method is used to initiate cache-validation process
	 * 
	 * @return
	 */
	public Response processCacheValidation(PersonalityCacheValidationRequest cacheValidationRequest) {
		try {
			validateRequestParameters(cacheValidationRequest);
			ExecutionContext<PersonalityCacheValidationRequest, Object, PersonalityResponseData> executionContext = new ExecutionContext<>();
			setParametersInExecutionContext(executionContext, getTenantList(cacheValidationRequest), cacheValidationRequest);
			Object executorResponse = apiExecutor.execute(executionContext);
			List<PersonalityResponseData> personalityResponseData = getPersonalityResponseData(executorResponse);
			return Response.status(getStatusCodeForResponse(personalityResponseData)).entity(executorResponse.toString()).build();
		} catch (APIException apiException) {
			LOGGER.error("SupportAPI : APIException occurred in processCacheValidation() method", apiException);
			throw apiException;
		} catch (Exception exception) {
			LOGGER.error("SupportAPI : Exception occurred in processCacheValidation() method", exception);
			cacheSupportApiUtil.exceptionResolverAPI(PersonalitySupportApiErrorCodes.EXCEPTION_OCCURRED_WHILE_VALIDATING_PERSON_CACHE, exception);
			throw new APIException(PersonalitySupportApiErrorCodes.EXCEPTION_OCCURRED_WHILE_VALIDATING_PERSON_CACHE);
		}
	}
	
	/**
	 * This method is used to find statusCode by checking if there are any invalid persons 
	 * @param personalityResponseData
	 * @return
	 */
	private int getStatusCodeForResponse(List<PersonalityResponseData> personalityResponseData) {
		int countOfInvalidPersons = 0;
		int totalPersons = 0;
		for(PersonalityResponseData data : personalityResponseData) {
			if(!CollectionUtils.isEmpty(data.getListOfInvalidEmployees())) {
				countOfInvalidPersons  = countOfInvalidPersons + data.getListOfInvalidEmployees().size();
				totalPersons = totalPersons + data.getListOfInvalidEmployees().size();
			}
			if(!CollectionUtils.isEmpty(data.getEmployeesInSync())) {
				totalPersons = totalPersons + data.getEmployeesInSync().size();
			}
			if(!CollectionUtils.isEmpty(data.getEmployeeOutOfSync())) {
				totalPersons = totalPersons + data.getEmployeeOutOfSync().size();
			}
		}
		return cacheSupportApiUtil.getStatusCode(countOfInvalidPersons, totalPersons);
	}
	

	/**
	 * This method is used to get tenants from personalityCacheValidationRequest
	 * @param cacheValidationRequest
	 * @return
	 */
	private List<String> getTenantList(PersonalityCacheValidationRequest cacheValidationRequest) {
		List<String> tenantList = new ArrayList<>();
		cacheValidationRequest.getPersonalityRequestDataList().stream().forEach(personalityRequestData -> {
			tenantList.add(personalityRequestData.getTenantShortName());
		});
		return tenantList;
	}

	/**
	 * This method is used set parameters in execution context
	 * @param executionContext
	 */
	private void setParametersInExecutionContext(ExecutionContext<PersonalityCacheValidationRequest, Object, PersonalityResponseData> executionContext,
			List<String> tenantList, PersonalityCacheValidationRequest personalityCacheValidationRequest) {
		executionContext.setAsync(false);
		executionContext.setRequestUri(commonUtils.getRequestURI());
		executionContext.setTenantAware(true);
		executionContext.setTenantList(tenantList);
		executionContext.setT(personalityCacheValidationRequest);
		executionContext.setExecutionFunction(biFunction);
	}
	
	/**
	 * This method is used to get list of tenants and their corresponding employees from response object
	 * @param object
	 * @return
	 */
	public List<PersonalityResponseData> getPersonalityResponseData(Object object) {
		try {
			return Arrays.asList(new ObjectMapper().readValue(((JSONObject) object).get("response").toString(), PersonalityResponseData[].class));
		} catch (Exception exception) {
			LOGGER.error("Support API : Exception occurred in getPersonalityResponseData while getting list of tenants: ",exception);
		}
		return new ArrayList<>();
	}
	
	/**
	 * This method is used to check request parameters in request DTO
	 * @param personalityCacheValidationRequest
	 */
	public void validateRequestParameters(PersonalityCacheValidationRequest personalityCacheValidationRequest) {
		supportApiValidator.validateIfRequestIsBlank(personalityCacheValidationRequest);
		Integer maxTenantsAllowed = cacheSupportApiUtil.getIntegerValueForProperty(PersonalitySupportApiConstants
				.MAX_TENANT_ALLOWED_IN_VALIDATE_CACHE_REQUEST, PersonalitySupportApiConstants.MAX_TENANT_DEFAULT_VALUE_FOR_VALIDATE_CACHE_REQUEST);
		supportApiValidator.validateTotalTenantsInRequest(personalityCacheValidationRequest.getPersonalityRequestDataList().size(), maxTenantsAllowed);
		personalityCacheValidationRequest.getPersonalityRequestDataList().stream().forEach(personalityData-> {
			supportApiValidator.validateTenantShortName(personalityData.getTenantShortName());
		});
		Integer maxPersonsAllowed = cacheSupportApiUtil.getIntegerValueForProperty(PersonalitySupportApiConstants
				.MAX_PERSON_ALLOWED_IN_VALIDATE_CACHE_REQUEST, PersonalitySupportApiConstants.MAX_PERSON_DEFAULT_VALUE_FOR_VALIDATE_CACHE_REQUEST);
		personalityCacheValidationRequest.getPersonalityRequestDataList().stream().forEach(personalityData -> {
			supportApiValidator.validatePersons(personalityData.getPersonNumberList(), maxPersonsAllowed);
		});
		personalityCacheValidationRequest.getPersonalityRequestDataList().stream().forEach(tenantData-> {
			supportApiValidator.checkForDuplicateParametersInRequest(tenantData.getPersonNumberList(), 
				PersonalitySupportApiErrorCodes.DUPLICATE_PERSON_FOUND_IN_REQUEST_ERROR_CODE, PersonalitySupportApiConstants.EMPLOYEE_KEY);
		});
	}
	
	BiFunction<PersonalityCacheValidationRequest, Object, PersonalityResponseData> biFunction = (requestData, object) -> {
		PersonalityResponseData tenantPersonalityResponseData = new PersonalityResponseData();
		tenantPersonalityResponseData.setTenantShortName(tenantProvider.getTenantId());
		List<String> invalidPersonNumbers = new ArrayList<>();
		for(PersonalityRequestData personalityRequestData : requestData.getPersonalityRequestDataList()) {
			if(personalityRequestData.getTenantShortName().equalsIgnoreCase(tenantProvider.getTenantId())) {
				for(String personNumber : personalityRequestData.getPersonNumberList()) {
					Long personId = null;
					try {
						Personality personality = Personality.getByPersonNumber(personNumber);
						personId = personality.getPersonId().toLong();
					} catch (Exception exception) {
						invalidPersonNumbers.add(personNumber);
					}
					retrieveAndCompareExtensions(tenantPersonalityResponseData, personNumber, personId);
				}
			}
		}
		setListOfInvalidEmployeeInResponse(tenantPersonalityResponseData, invalidPersonNumbers);
		return tenantPersonalityResponseData;
	};

	/**
	 * This method is used to initiate getting extension objects for person from database and cache and compare them
	 * 
	 * @param tenantPersonalityResponseData
	 * @param personNumber
	 * @param personId
	 */
	private void retrieveAndCompareExtensions(PersonalityResponseData tenantPersonalityResponseData, String personNumber, Long personId) {
		try {
			if(personId!=null) {
				AllExtension allExtensionFromDb = extensionBuilderService.buildAllExtensionFromDb(personId);
				AllExtension allExtensionFromCache = extensionBuilderService.getAllPersonalityExtensionFromCache(personId);
				compareDataInExtensions(personNumber, allExtensionFromDb, allExtensionFromCache, tenantPersonalityResponseData);
			}
		} catch (Exception exception) {
			LOGGER.error("SupportAPI : Exception occurred in retrieveAndCompareExtensions() method", exception);
			throw exception;
		}
	}

	/**
	 * This method is used to set list of invalid employees in response
	 * 
	 * @param tenantPersonalityResponseData
	 * @param invalidPersonNumbers
	 */
	private void setListOfInvalidEmployeeInResponse(PersonalityResponseData tenantPersonalityResponseData,
			List<String> invalidPersonNumbers) {
		if(!invalidPersonNumbers.isEmpty()) {
			if(tenantPersonalityResponseData.getListOfInvalidEmployees()==null) {
				tenantPersonalityResponseData.setListOfInvalidEmployees(new ArrayList<>());
			}
			LOGGER.info("SupportAPI : List of invalid persons found in request are: {}", invalidPersonNumbers);
			tenantPersonalityResponseData.getListOfInvalidEmployees().addAll(invalidPersonNumbers);
		}
	}
	
	/**
	 * This method is used to compare extensions in AllExtension object for to find out-of-sync attributes
	 * 
	 * @param allExtensionFromDb
	 * @param allExtensionFromCache
	 * @param tenantPersonalityResponseData
	 */
	private void compareDataInExtensions(String personNum, AllExtension allExtensionFromDb, AllExtension allExtensionFromCache
			, PersonalityResponseData tenantPersonalityResponseData) {
		
		List<String> employeesInSyncList = new ArrayList<>();
		List<EmployeeOutOfSync> employeeOutOfSyncList = new ArrayList<>();
		try {
			if(allExtensionFromDb == null || allExtensionFromCache == null || allExtensionFromDb.equals(allExtensionFromCache)
					|| checkIfChildExtensionsAreNull(allExtensionFromDb) || checkIfChildExtensionsAreNull(allExtensionFromCache)) {
				employeesInSyncList.add(personNum);
			} else {
				EmployeeOutOfSync employeeOutOfSync = new EmployeeOutOfSync();
				employeeOutOfSync.setPersonNumber(personNum);
				getOutOfSyncAttributesFromExtensions(allExtensionFromDb, allExtensionFromCache, employeeOutOfSync);
				if(employeeOutOfSync.getOutOfSyncAttributes().isEmpty()) {
					employeeOutOfSync.getOutOfSyncAttributes().add(extensionServiceUtil.getOutOfSyncAttribute(
						PersonalitySupportApiConstants.ALL_EXTENSION, allExtensionFromDb.toString(), allExtensionFromCache.toString()));
				}
				employeeOutOfSyncList.add(employeeOutOfSync);
			}
			setListOfEmployeesInResponse(tenantPersonalityResponseData, employeesInSyncList, employeeOutOfSyncList);
		} catch (Exception exception) {
			LOGGER.error("SupportAPI : Exception occurred in compareDataInExtensions() method for personNum : "+personNum, exception);
		}
	}
	
	/**
	 * This method will check if instances of accrualExtension, deviceExtension, employeeExtension, schedulingExtension and timekeepingExtension
	 * in AllExtension object are null
	 * @param allExtension
	 * @return
	 */
	private boolean checkIfChildExtensionsAreNull(AllExtension allExtension) {
		return (allExtension.getAccrualExtension() == null && allExtension.getDeviceExtension() == null && allExtension.getEmployeeExtension() == null
				&& allExtension.getSchedulingExtension() == null && allExtension.getTimekeepingExtension() == null);
	}

	/**
	 * This method is used to find out of sync attributes in each type of extension objects
	 * 
	 * @param allExtensionFromDb
	 * @param allExtensionFromCache
	 * @param employeeOutOfSync
	 * @throws IllegalAccessException
	 */
	private void getOutOfSyncAttributesFromExtensions(AllExtension allExtensionFromDb,
			AllExtension allExtensionFromCache, EmployeeOutOfSync employeeOutOfSync) throws IllegalAccessException {
		List<String> propertiesToSkip = cacheSupportApiUtil.getPropertiesToSkip(PersonalitySupportApiConstants.SKIP_CACHE_VALIDATION_PROPERTIES);
		if(!Objects.equal(allExtensionFromDb.getDeviceExtension(), allExtensionFromCache.getDeviceExtension())) {
			extensionServiceUtil.findOutOfSyncAttributes(getDevicesExtnObj(allExtensionFromDb), getDevicesExtnObj(allExtensionFromCache)
					, null, employeeOutOfSync.getOutOfSyncAttributes(), propertiesToSkip);
		}
		if(!Objects.equal(allExtensionFromDb.getSchedulingExtension(), allExtensionFromCache.getSchedulingExtension())) {
			extensionServiceUtil.findOutOfSyncAttributes(getSchedulingExtnObj(allExtensionFromDb), getSchedulingExtnObj(allExtensionFromCache)
					, null, employeeOutOfSync.getOutOfSyncAttributes(), propertiesToSkip);
		}
		if(!Objects.equal(allExtensionFromDb.getAccrualExtension(), allExtensionFromCache.getAccrualExtension())) {
			extensionServiceUtil.findOutOfSyncAttributes(getAccrualExtnObj(allExtensionFromDb), getAccrualExtnObj(allExtensionFromCache)
					, null, employeeOutOfSync.getOutOfSyncAttributes(), propertiesToSkip);
		}
		if(!Objects.equal(allExtensionFromDb.getEmployeeExtension(), allExtensionFromCache.getEmployeeExtension())) {
			extensionServiceUtil.findOutOfSyncAttributes(getEmployeeExtnObj(allExtensionFromDb), getEmployeeExtnObj(allExtensionFromCache)
					, null, employeeOutOfSync.getOutOfSyncAttributes(), propertiesToSkip);
		}
		if(!Objects.equal(allExtensionFromDb.getTimekeepingExtension(), allExtensionFromCache.getTimekeepingExtension())) {
			extensionServiceUtil.findOutOfSyncAttributes(getTimekeepingExtnObj(allExtensionFromDb), getTimekeepingExtnObj(allExtensionFromCache)
					, null, employeeOutOfSync.getOutOfSyncAttributes(), propertiesToSkip);
		}
	}
	
	/**
	 * This method is used to set employeesInSync and employeeOutOfSync persons in response object
	 * 
	 * @param tenantPersonalityResponseData
	 * @param employeesInSyncList
	 * @param employeeOutOfSyncList
	 */
	private void setListOfEmployeesInResponse(PersonalityResponseData tenantPersonalityResponseData, List<String> employeesInSyncList
			, List<EmployeeOutOfSync> employeeOutOfSyncList) {
		
		if(!employeesInSyncList.isEmpty()) {
			if(tenantPersonalityResponseData.getEmployeesInSync()==null) {
				tenantPersonalityResponseData.setEmployeesInSync(new ArrayList<>());
			}
			tenantPersonalityResponseData.getEmployeesInSync().addAll(employeesInSyncList);
		}
		if(!employeeOutOfSyncList.isEmpty()) {
			if(tenantPersonalityResponseData.getEmployeeOutOfSync()==null) {
				tenantPersonalityResponseData.setEmployeeOutOfSync(new ArrayList<>());
			}
			tenantPersonalityResponseData.getEmployeeOutOfSync().addAll(employeeOutOfSyncList);
		}
	}
	
	/**
	 * This method is used to return DevicesExtension from AllExtension object
	 * @param allExtension
	 * @return
	 */
	private  DevicesExtension getDevicesExtnObj(AllExtension allExtension) {
		return allExtension.getDeviceExtension() != null ? allExtension.getDeviceExtension() : new DevicesExtension();
	}
	
	/**
	 * This method is used to return SchedulingExtension from AllExtension object
	 * @param allExtension
	 * @return
	 */
	private  SchedulingExtension getSchedulingExtnObj(AllExtension allExtension) {
		return allExtension.getSchedulingExtension() != null ? allExtension.getSchedulingExtension() : new SchedulingExtension();
	}
	
	/**
	 * This method is used to return AccrualExtension from AllExtension object
	 * @param allExtension
	 * @return
	 */
	private  AccrualExtension getAccrualExtnObj(AllExtension allExtension) {
		return allExtension.getAccrualExtension() != null ? allExtension.getAccrualExtension() : new AccrualExtension();
	}
	
	/**
	 * This method is used to return EmployeeExtension from AllExtension object
	 * @param allExtension
	 * @return
	 */
	private  EmployeeExtension getEmployeeExtnObj(AllExtension allExtension) {
		return allExtension.getEmployeeExtension() != null ? allExtension.getEmployeeExtension() : new EmployeeExtension();
	}
	
	/**
	 * This method is used to return TimekeepingExtension from AllExtension object
	 * @param allExtension
	 * @return
	 */
	private  TimekeepingExtension getTimekeepingExtnObj(AllExtension allExtension) {
		return allExtension.getTimekeepingExtension() != null ? allExtension.getTimekeepingExtension() : new TimekeepingExtension();
	}
	
	
}
