package com.kronos.persons.utils;

import com.kronos.people.personality.model.AttestationProfileAssignmentDTO;
import com.kronos.people.personality.model.PersonAttestationProfileAssignmentsDTO;
import com.kronos.persons.rest.assignments.model.AttestationProfileAssignment;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignment;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class PersonAttestationProfileAssignmentConverter {

    public Map<Integer, PersonAttestationProfileAssignmentsDTO> convertToDTO(Map<Integer, PersonAttestationProfileAssignment> assignments) {
        return assignments.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> convertToDTO(entry.getValue())));
    }

    private PersonAttestationProfileAssignmentsDTO convertToDTO(PersonAttestationProfileAssignment personAttestationProfileAssignment) {
        PersonAttestationProfileAssignmentsDTO assignmentDTO = new PersonAttestationProfileAssignmentsDTO();
        assignmentDTO.setPerson(personAttestationProfileAssignment.getEmployee());
        assignmentDTO.setAttestationProfileAssignments(Optional.ofNullable(personAttestationProfileAssignment.getAttestationProfileAssignments())
                .map(lst -> lst.stream().map(this::convertToDTO).collect(Collectors.toList()))
                .orElse(null));
        assignmentDTO.setManagerRoleAttestationProfileAssignments(Optional
                .ofNullable(personAttestationProfileAssignment.getManagerRoleAttestationProfileAssignments())
                .map(lst -> lst.stream().map(this::convertToDTO).collect(Collectors.toList()))
                .orElse(null));
        return assignmentDTO;
    }

    private AttestationProfileAssignmentDTO convertToDTO(AttestationProfileAssignment attestationProfileAssignment) {
        AttestationProfileAssignmentDTO attestationProfileAssignmentDTO = new AttestationProfileAssignmentDTO();
        attestationProfileAssignmentDTO.setProfile(attestationProfileAssignment.getProfile());
        final LocalDate effectiveDate = Optional.ofNullable(attestationProfileAssignment.getEffectiveDate())
                .map(LocalDate::parse)
                .orElse(null);
        final LocalDate expirationDate = Optional.ofNullable(attestationProfileAssignment.getExpirationDate())
                .map(LocalDate::parse)
                .orElse(null);
        attestationProfileAssignmentDTO.setEffectiveDate(effectiveDate);
        attestationProfileAssignmentDTO.setExpirationDate(expirationDate);
        return attestationProfileAssignmentDTO;
    }

    public Map<Integer, PersonAttestationProfileAssignment> convertToAPI(Map<Integer, PersonAttestationProfileAssignmentsDTO> assignments) {
        return assignments.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> convertToAPI(entry.getValue())));
    }

    private PersonAttestationProfileAssignment convertToAPI(PersonAttestationProfileAssignmentsDTO personAttestationProfileAssignmentsDTO) {
        PersonAttestationProfileAssignment assignment = new PersonAttestationProfileAssignment();
        assignment.setEmployee(personAttestationProfileAssignmentsDTO.getPerson());
        assignment.setAttestationProfileAssignments(Optional.ofNullable(personAttestationProfileAssignmentsDTO.getAttestationProfileAssignments())
                .map(lst -> lst.stream().map(this::convertToAPI).collect(Collectors.toList()))
                .orElse(null));
        assignment.setManagerRoleAttestationProfileAssignments(Optional
                .ofNullable(personAttestationProfileAssignmentsDTO.getManagerRoleAttestationProfileAssignments())
                .map(lst -> lst.stream().map(this::convertToAPI).collect(Collectors.toList()))
                .orElse(null));
        return assignment;
    }

    private AttestationProfileAssignment convertToAPI(AttestationProfileAssignmentDTO attestationProfileAssignmentDTO) {
        AttestationProfileAssignment attestationProfileAssignment = new AttestationProfileAssignment();
        attestationProfileAssignment.setProfile(attestationProfileAssignmentDTO.getProfile());
        final String effectiveDate = Optional.ofNullable(attestationProfileAssignmentDTO.getEffectiveDate())
                .map(LocalDate::toString)
                .orElse(null);
        attestationProfileAssignment.setEffectiveDate(effectiveDate);
        final String expirationDate = Optional.ofNullable(attestationProfileAssignmentDTO.getExpirationDate())
                .map(LocalDate::toString)
                .orElse(null);
        attestationProfileAssignment.setExpirationDate(expirationDate);
        return attestationProfileAssignment;
    }
}
