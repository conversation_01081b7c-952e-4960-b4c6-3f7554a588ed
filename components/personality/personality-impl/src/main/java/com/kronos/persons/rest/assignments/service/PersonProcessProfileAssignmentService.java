package com.kronos.persons.rest.assignments.service;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.persons.rest.assignments.model.ProcessProfileAssignmentRequestBean;
import com.kronos.persons.rest.assignments.model.RestEntity;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.persons.utils.ExtensionConstant;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonNotification;
import com.kronos.wfc.commonapp.processmanager.business.profiles.workflow.WorkflowProfile;
import com.kronos.wfc.commonapp.processmanager.business.workflow.WorkflowAccessAssignment;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
@Named
public class PersonProcessProfileAssignmentService extends RestEntity {

	private static final String WORKFLOW_ACCESS_ASSIGNMENT = "workflowAccessAssignment";
	private static final String MANAGER_PROCESS_PROFILE = "managerProcessProfile";
	private static final String EMPLOYEE_PROCESS_PROFILE = "employeeProcessProfile";
	private static final String MANAGER_PROCESS_PROFILE_NAME = "managerProcessProfileName";
	private static final String EMPLOYEE_PROCESS_PROFILE_NAME = "employeeProcessProfileName";
	
	@Inject
	ValidatorUtils validatorUtils;

	@Inject
	PersonIdentityBeanValidator personIdentityBeanValidator;
	
	@Inject
	PersonProcessProfileAssignmentServiceHelper personProcessProfileAssignmentServiceHelper;
	
	public ProcessProfileAssignmentRequestBean load(PersonIdentityBean identityBean) {
		Personality personality = validateAndGetPerson(identityBean);
		return getAssignmentForPerson(personality);
	}

	private ProcessProfileAssignmentRequestBean getAssignmentForPerson(Personality personality) {
		ProcessProfileAssignmentRequestBean responseBean = new ProcessProfileAssignmentRequestBean();
		responseBean.setPersonIdentity(personIdentityBeanValidator.createForPersonNumber(personality.getPersonNumber()));
		
		WorkflowAccessAssignment workflowAccessAssignment = personProcessProfileAssignmentServiceHelper.getWorkflowAccessAssignment(personality);   
		if(null == workflowAccessAssignment) {
			throw PrsnValidationException.noBeanFound(WORKFLOW_ACCESS_ASSIGNMENT);
		}
		
		getManagerProcessProfile(responseBean, workflowAccessAssignment);
		getEmployeeProcessProfile(responseBean, workflowAccessAssignment);
		
		return responseBean;
	}

	private void getEmployeeProcessProfile(
			ProcessProfileAssignmentRequestBean responseBean,
			WorkflowAccessAssignment workflowAccessAssignment) {
		WorkflowProfile employeeWorkflowProfile = (WorkflowProfile) workflowAccessAssignment.getEmployeeWorkflowProfile();
		if(null == employeeWorkflowProfile) {
			throw PrsnValidationException.inconsistentData(EMPLOYEE_PROCESS_PROFILE);
		}
		responseBean.setEmployeeProcessProfileName(employeeWorkflowProfile.getName());
	}

	private void getManagerProcessProfile(
			ProcessProfileAssignmentRequestBean responseBean,
			WorkflowAccessAssignment workflowAccessAssignment) {
		WorkflowProfile managerWorkflowProfile = (WorkflowProfile) workflowAccessAssignment.getManagerWorkflowProfile();
		if(null == managerWorkflowProfile) {
			throw PrsnValidationException.inconsistentData(MANAGER_PROCESS_PROFILE);
		}
		responseBean.setManagerProcessProfileName(managerWorkflowProfile.getName());
	}

	private Personality validateAndGetPerson(PersonIdentityBean identityBean) {
		if (validatorUtils.isNull(identityBean)) {
		    throw PrsnValidationException.missingProperty(ExtensionConstant.PERSON_IDENITY);
		}
		personIdentityBeanValidator.newvalidate(null, identityBean, null);
		Personality personality = personIdentityBeanValidator.getPersonality(identityBean);
		return personality;
	}

	public Personality update(ProcessProfileAssignmentRequestBean requestBean ) {
		return update(requestBean,true);
	}

	public Personality update(ProcessProfileAssignmentRequestBean requestBean, boolean notifyPersonCache ) {
		// validation - Both Employee and Manager profile name cannot be null.
		if (null == requestBean.getEmployeeProcessProfileName() && null == requestBean.getManagerProcessProfileName()){
			throw PrsnValidationException.missingProperty(EMPLOYEE_PROCESS_PROFILE_NAME+", "+MANAGER_PROCESS_PROFILE_NAME);
		}
		Personality personality = validateAndGetPerson(requestBean.getPersonIdentity());
		updateAssignmentForPerson(requestBean, personality, notifyPersonCache);
		return personality;
	}

	private ProcessProfileAssignmentRequestBean updateAssignmentForPerson(ProcessProfileAssignmentRequestBean requestBean, Personality personality, boolean notifyPersonUpdate) {
		ProcessProfileAssignmentRequestBean responseBean = new ProcessProfileAssignmentRequestBean();
		responseBean.setPersonIdentity(personIdentityBeanValidator.createForPersonNumber(personality.getPersonNumber()));
		
		WorkflowAccessAssignment workflowAccessAssignment = personProcessProfileAssignmentServiceHelper.getWorkflowAccessAssignment(personality);
		if(null == workflowAccessAssignment) {
			throw PrsnValidationException.noBeanFound(WORKFLOW_ACCESS_ASSIGNMENT);
		}
		String employeeWorkflowProfileName = validateEmployeeWorkflowProfileName(requestBean, workflowAccessAssignment);
		String managerWorkflowProfileName = validateManagerWorkflowProfileName(requestBean, workflowAccessAssignment);
		WorkflowProfile employeeWorkflowProfile = WorkflowProfile.getWorkflowProfile(employeeWorkflowProfileName);
		if(null == employeeWorkflowProfile) {
			throw PrsnValidationException.invalidPropertyValue(EMPLOYEE_PROCESS_PROFILE_NAME, employeeWorkflowProfileName);
		}
		ObjectIdLong employeeWorkflowProfileId = (ObjectIdLong) employeeWorkflowProfile.getObjectId();
		responseBean.setEmployeeProcessProfileName(employeeWorkflowProfileName);
		
		WorkflowProfile managerWorkflowProfile = WorkflowProfile.getWorkflowProfile(managerWorkflowProfileName);
		if(null == managerWorkflowProfile) {
			throw PrsnValidationException.invalidPropertyValue(MANAGER_PROCESS_PROFILE_NAME, managerWorkflowProfileName);
		}
		ObjectIdLong managerWorkflowProfileId = (ObjectIdLong) managerWorkflowProfile.getObjectId();
		responseBean.setManagerProcessProfileName(managerWorkflowProfileName);
		
		workflowAccessAssignment.setManagerWorkflowProfileId(managerWorkflowProfileId);
		workflowAccessAssignment.setEmployeeWorkflowProfileId(employeeWorkflowProfileId);
		workflowAccessAssignment.update();
		if(notifyPersonUpdate) {
			PersonNotification.sendUpdate(workflowAccessAssignment.getPersonId());
		}
		return responseBean;
	}

	private String validateEmployeeWorkflowProfileName(
			ProcessProfileAssignmentRequestBean requestBean, WorkflowAccessAssignment workflowAccessAssignment) {
		String employeeWorkflowProfileName = requestBean.getEmployeeProcessProfileName();
		if (null == employeeWorkflowProfileName){
			// get existing name and set it to employeeWorkflowProfileName
			employeeWorkflowProfileName = getExistingEmployeeWorkflowProfileName(workflowAccessAssignment);
		}
		return employeeWorkflowProfileName;
	}

	private String getExistingEmployeeWorkflowProfileName(WorkflowAccessAssignment workflowAccessAssignment) {
		if (workflowAccessAssignment.getEmployeeWorkflowProfile() != null){
			return workflowAccessAssignment.getEmployeeWorkflowProfile().getName();
		} else {
			throw PrsnValidationException.inconsistentData(EMPLOYEE_PROCESS_PROFILE_NAME);
		}
	}
	
	private String validateManagerWorkflowProfileName(
			ProcessProfileAssignmentRequestBean requestBean,
			WorkflowAccessAssignment workflowAccessAssignment) {
		String managerWorkflowProfileName = requestBean.getManagerProcessProfileName();
		if (null == managerWorkflowProfileName){
			// get existing name and set it to managerWorkflowProfileName
			managerWorkflowProfileName = getExistingManagerWorkflowProfileName(workflowAccessAssignment);
		}
		return managerWorkflowProfileName;
	}

	private String getExistingManagerWorkflowProfileName(WorkflowAccessAssignment workflowAccessAssignment) {
		if (workflowAccessAssignment.getManagerWorkflowProfile() != null){
			return workflowAccessAssignment.getManagerWorkflowProfile().getName();
		} else {
			throw PrsnValidationException.inconsistentData(MANAGER_PROCESS_PROFILE_NAME);
		}
	}
	
}
