package com.kronos.persons.accountmanagement.service;

import com.kronos.accountmanagement.domain.UserDTO;
import com.kronos.container.api.access.SpringContext;
import com.kronos.releasetoggle.api.model.ReleaseToggleType;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.internal.KronosPassword;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import com.kronos.wfc.platform.security.business.authentication.ssoplugin.SSOOidSubject;
import java.security.SecureRandom;
import com.kronos.wfc.commonapp.types.business.LogonProfile;
import com.kronos.releasetoggle.api.ReleaseToggleService;

public class PasswordServiceUtility {
  private static String LOWERCASE = "abcdefghijklmnopqrstuvwxyz";
  private static String UPPERCASE = LOWERCASE.toUpperCase();
  private static String NUMBERS = "**********";
  private static String SPECIAL = "!@#$%^&*_=+-/";

  public static final String PASSWORD_SUFFIX_ALLOWED_KEY = "site.security.passwrd.suffix.allowed";

  private static ReleaseToggleService releaseToggleService;

  private PasswordServiceUtility() {
    throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
  }

  public static String generateRandomPassword() {
    String[] characterSets = new String[4];
    characterSets[0] = LOWERCASE;
    characterSets[1] = UPPERCASE;
    characterSets[2] = NUMBERS;
    characterSets[3] = SPECIAL;
    String allChars = String.join("", characterSets);

    SecureRandom randomIndex = new SecureRandom();

    char[] pwd = new char[20];

    for (int i = 0; i<pwd.length; i++) {
      pwd[i] = allChars.charAt(randomIndex.nextInt(allChars.length()));
    }

    for (String characterSet : characterSets) {
      pwd[randomIndex.nextInt(pwd.length)] = characterSet.charAt(randomIndex.nextInt(characterSet.length()));
    }

    return new String(pwd);
  }

  public static boolean isDefaultPasswordCriteriaMet(Personality personality) {
    if (getReleaseToggleService().getValue(ReleaseToggleType.ALLOW_PASSWORD_SUFFIX.getValue()) && KronosProperties.getPropertyAsBoolean(PASSWORD_SUFFIX_ALLOWED_KEY, false) &&
        !AMSUtils.isAuthTypeFederated(personality) && LogonProfile.getDefaultProfile()!=null && LogonProfile.getDefaultProfile().getPasswordSuffix()!=null) {
      // If release toggle password suffix is allowed and the default profile is not null and the feature flag is on
      return true;
    }
    return false;
  }

  public static ReleaseToggleService getReleaseToggleService() {
    if(releaseToggleService == null){
      releaseToggleService = SpringContext.getBean(ReleaseToggleService.class);
    }
    return releaseToggleService;
  }


  }


