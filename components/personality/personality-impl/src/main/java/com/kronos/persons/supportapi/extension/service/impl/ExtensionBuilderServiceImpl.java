/*
 * *****************************************************************************
 * Copyright (c) 2020 Kronos, Inc. All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * Kronos, Inc. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with Kronos.
 *
 * KRONOS MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE
 * SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
 * PURPOSE, OR NON-INFRINGEMENT. KRONOS SHALL NOT BE LIABLE FOR ANY DAMAGES
 * SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING
 * THIS SOFTWARE OR ITS DERIVATIVES.
 ****************************************************************************
 *
 **/
package com.kronos.persons.supportapi.extension.service.impl;

import java.time.LocalDate;
import java.util.Map;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.people.personality.dataaccess.adapter.ExtensionAdapterEnum;
import com.kronos.people.personality.dataaccess.legacy.ExtensionBuilder;
import com.kronos.people.personality.facade.PersonalityCacheFacade;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.AccrualExtension;
import com.kronos.people.personality.model.extension.AllExtension;
import com.kronos.people.personality.model.extension.BaseExtension;
import com.kronos.people.personality.model.extension.DevicesExtension;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.SchedulingExtension;
import com.kronos.people.personality.model.extension.TimekeepingExtension;
import com.kronos.persons.rest.supportapi.extensions.ExtensionBuilderService;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.personality.PersonalityCache;
import com.kronos.wfc.commonapp.people.business.personality.PersonalityTriplet;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;

/**
 * This is the implementation class for getting Extension objects for person from database or cache
 * 
 * <AUTHOR>
 */
@Named
public class ExtensionBuilderServiceImpl implements ExtensionBuilderService {

	@Inject
	ExtensionBuilder extensionBuilder;
	
	@Inject
	PersonalityCacheFacade personalityCacheFacade;
	
	
	/**
	 * This method is used to get AllExtension data of a person from database
	 */
	@Override
	public AllExtension buildAllExtensionFromDb(Long personId) {
		AllExtension allExtension = new AllExtension();
		PersonalityTriplet triplet = PersonalityCache.getByPersonIdFromDatabase(new ObjectIdLong(personId));
		Personality personality = createPersonality(triplet);
		Map<String, BaseExtension> map = extensionBuilder.buildExtensions(personality);
		allExtension.setAccrualExtension((AccrualExtension) map.get(ExtensionAdapterEnum.ACCRUAL.getIdentifier()));
		allExtension.setEmployeeExtension((EmployeeExtension) map.get(ExtensionAdapterEnum.EMPLOYEE.getIdentifier()));
		allExtension.setDeviceExtension((DevicesExtension) map.get(ExtensionAdapterEnum.DEVICES.getIdentifier()));
		allExtension.setSchedulingExtension((SchedulingExtension) map.get(ExtensionAdapterEnum.SCHEDULING.getIdentifier()));
		allExtension.setTimekeepingExtension((TimekeepingExtension) map.get(ExtensionAdapterEnum.TIMEKEEPING.getIdentifier()));
		return allExtension;
	}
	
	/**
	 * This method is used to get AllExtension data of a person from personality cache
	 */
	@Override
	public AllExtension getAllPersonalityExtensionFromCache(Long personId) {
		PersonalityResponse<AllExtension> personalityResponse = personalityCacheFacade.getAllExtensionForPersonId(personId, LocalDate.now());
		return personalityResponse != null ? personalityResponse.getExtension() : new AllExtension();
	}
	
	/**
	 * This method is used to create personality object using personalityTriplet
	 * @param triplet
	 * @return
	 */
	public Personality createPersonality(PersonalityTriplet triplet) {
		return new Personality(triplet);
	}

}
