package com.kronos.persons.rest.assignments.service;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.persons.rest.assignments.model.ProcessorToEmployeeAssignmentDTO;
import com.kronos.persons.rest.assignments.model.ProcessorToEmployeeCriteriaDTO;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.utils.ExtensionConstant;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.timekeeping.fixedpercentallocation.business.configuration.FPARuleService;
import com.kronos.wfc.totalizing.business.extensibility.Processor;
import com.kronos.wfc.totalizing.business.extensibility.ProcessorToEmployee;
import com.kronos.wfc.totalizing.business.extensibility.entity.ExtensibilityCacheManager;
import com.kronos.wfc.totalizing.business.extensibility.validator.ExtensibilityProcessorValidator;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.util.List;
import java.util.Set;

@Named
public class ProcessorToEmployeeAssignmentServiceImpl extends RuleAssignmentService<ProcessorToEmployeeAssignmentDTO> implements ProcessorToEmployeeAssignmentService {

    @Inject
    private ExtensibilityProcessorValidator processorValidator;

    @Override
    public Personality createProcessorToEmployeeAssignment(ProcessorToEmployeeAssignmentDTO assignmentBean) {
        validateProcessorRef(assignmentBean.getProcessor());
        Personality record = super.assignRecord(assignmentBean);
        updateCache(assignmentBean.getProcessor().getId());
        return record;
    }

    @Override
    public Personality updateProcessorToEmployeeAssignment(ProcessorToEmployeeAssignmentDTO assignmentBean) {
        validateProcessorRef(assignmentBean.getProcessor());
        Personality record = super.updateRecord(assignmentBean);
        updateCache(assignmentBean.getProcessor().getId());
        return record;
    }

    @Override
    public List<ProcessorToEmployeeAssignmentDTO> getProcessorToEmployeeAssignmentsByParams
            (Long personId, String personNumber, String processor, String effectiveDate) {
        if (validatorUtils.notNull(effectiveDate)) {
            validatorUtils.stringtokdate(ExtensionConstant.EFFECTIVE_DATE, effectiveDate);
        }
        List<ProcessorToEmployeeAssignmentDTO> processorToEmployeeAssignmentDTOs = super.getRuleAssignment(personId,
                personNumber, processor, effectiveDate);
        if (((validatorUtils.notNull(personId)) || (validatorUtils.notNull(personNumber)))
                && validatorUtils.notNull(processor) && validatorUtils.isNull(effectiveDate)) {
            processorToEmployeeAssignmentDTOs.removeIf(p -> !p.getProcessor().getQualifier().equals(processor));
        } else if (validatorUtils.isNull(personId) && validatorUtils.isNull(personNumber)
                && validatorUtils.notNull(processor) && validatorUtils.notNull(effectiveDate)) {
            processorToEmployeeAssignmentDTOs.removeIf(p -> !p.getEffectiveDate().equals(effectiveDate));
        }
        return processorToEmployeeAssignmentDTOs;
    }

    @Override
    public Personality deleteProcessorToEmployeeAssignment(ProcessorToEmployeeCriteriaDTO criteriaBean) {
        Personality deleteRuleAssignment = super.deleteRuleAssignment(criteriaBean);
        ExtensibilityCacheManager.flushAllCaches();
        FPARuleService.getInstance().updateSingleEmployeeTotalizationStatusTimeStamp(deleteRuleAssignment.getId());
        return deleteRuleAssignment;
    }

    @Override
    protected ProcessorToEmployeeAssignmentDTO newRuleAssignment(ProcessorToEmployee processorToEmployee) {
        return new ProcessorToEmployeeAssignmentDTO(processorToEmployee);
    }

    @Override
    public void setProcessor(ProcessorToEmployeeAssignmentDTO bean, String processorName) {
        if (validatorUtils.isNull(processorName)) {
            return;
        }
        Processor processor = getProcessorByName(processorName);
        bean.getWrappedProcessorToEmployee().setProcessor(processor);
    }

    @Override
    protected boolean isProcessorMyType(Processor processor) {
        processorValidator.validateSupportedRules(processor::getClassName);
        return true;
    }

    @Override
    protected void sendBulkNotification(Set<Long> personIdsLong) {
        //This will not apply to the custom extensions, no operation needed
    }

    @Override
    protected void sendNotification(PersonIdentityBean personIdentity) {
        //This will not apply to the custom extensions, no operation needed
    }

    @Override
    protected String getProcessorClassName() {
        return null;
    }

    @Override
    protected String getDagenityName() {
        return null;
    }

    @Override
    protected String getRuleType() {
        return null;
    }

    private void validateProcessorRef(ObjectRef processorRef) {
        if (validatorUtils.notNull(processorRef)) {
            Processor processor = processorValidator.checkIdAndQualifierMismatch(processorRef.getId(), processorRef.getQualifier());
            processorValidator.checkIfProcessorHasEmployeeType(processor);
        }
    }

    private void updateCache(Long processorId) {
        ExtensibilityCacheManager.flushAllCaches();
        FPARuleService.getInstance().updateAffectedEmployeesTotalizationStatusTimeStamp(
            new ObjectIdLong(processorId));
    }
}
