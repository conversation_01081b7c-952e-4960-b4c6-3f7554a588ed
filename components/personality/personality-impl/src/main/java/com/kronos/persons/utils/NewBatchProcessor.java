package com.kronos.persons.utils;

import com.kronos.container.api.exception.APIException;
import com.kronos.container.api.util.APIExceptionDetailResult;
import com.kronos.persons.rest.exception.ExceptionConstants;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 
 * <AUTHOR>
 *
 * @param <T> Request bean type
 * @param <U> Response bean type
 */
public class NewBatchProcessor<T, U> {

	private Collection<T> requestDataCollection;
	private Function<T, U> requestProcessor;

	public NewBatchProcessor(Collection<T> requestDataCollection, Function<T, U> requestProcessor) {
		this.requestDataCollection = requestDataCollection;
		this.requestProcessor = requestProcessor;
	}
	
	public List<U> process(){
		
		APIException partialSuccess = getAPIException(ExceptionConstants.PARTIAL_SUCCESS);
		APIException allError = getAPIException(ExceptionConstants.ALL_RECORDS_FAILED);
		
		List<APIExceptionDetailResult<?>> resultList = new ArrayList<>();
		List<U> successList = new ArrayList<>();

		//stream process request data
		this.requestDataCollection.forEach( requestData -> {
						try{
							U responseObj  = this.requestProcessor.apply(requestData);
							successList.add(responseObj);
							APIExceptionDetailResult<U> success = new APIExceptionDetailResult<>(responseObj);
							resultList.add(success);
						} catch (APIException apiExp) {
							APIExceptionDetailResult<APIException> error = new APIExceptionDetailResult<>(apiExp);
							resultList.add(error);
						}
						});

		if (successList.isEmpty()){
			allError.setResults(resultList);
			throw allError;
		}
		if (successList.size() != requestDataCollection.size()){
			partialSuccess.setResults(resultList);
			throw partialSuccess;
		}
		return successList;
	}

	/**
	 * Process request and put in Map without success and partial error throw
	 * @return Map<U, APIExceptionDetailResult<?>>
	 */
	public Map<T, APIExceptionDetailResult<?>> processWithoutThrow(){

		Map<T, APIExceptionDetailResult<?>> resultMap = new HashMap<>();

		//stream process request data
		this.requestDataCollection.forEach(requestData -> {
			try{
				U responseObj  = this.requestProcessor.apply(requestData);
				APIExceptionDetailResult<U> success = new APIExceptionDetailResult<>(responseObj);
				resultMap.put(requestData, success);
			} catch (APIException apiExp) {
				APIExceptionDetailResult<APIException> error = new APIExceptionDetailResult<>(apiExp);
				resultMap.put(requestData, error);
			}
		});

		return resultMap;
	}

	private APIException getAPIException(String exceptionConstant) {
		return new APIException(exceptionConstant);
	}

}
