package com.kronos.persons.utils;

import com.kronos.container.api.access.SpringContext;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.service.PersonalityService;
import com.kronos.persons.context.service.CurrentUser;
import com.kronos.persons.rest.model.UserInfoBean;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.user.UserAccount;

public class UserInfoBeanConverter {
	
   private UserInfoBeanConverter() {
   }

   private static PersonalityService personalityService;

   private static CurrentUserContactDetailsPopulator currentUserContactDetailsPopulator;

   public static UserInfoBean createCurrentUserDetailsBean(Personality personality, CurrentUser currentUser, boolean includeContactInformation) {
     
      UserInfoBean bean = new UserInfoBean();
      loadCurrentUserFields(bean, currentUser);
      loadPersonalityFields(bean, personality);

      if (includeContactInformation) {
         EmployeeExtension employeeExtension = getPersonalityService().findEmployeeExtension(bean.getEmployeeId()).getExtension();
         getCurrentUserContactDetailsPopulator().populateContactDetails(bean, employeeExtension, personality);
      }
      return bean;

   }
          
    private static void loadPersonalityFields(UserInfoBean bean, Personality personality) {
        loadUserAccountFields(bean, personality);
    }

    private static void loadUserAccountFields(UserInfoBean bean, Personality personality) {
        UserAccount userAccount = personality.getUserAccount();
        if(userAccount != null) {
            bean.setUserName(userAccount.getUserName());
        }

        Person nameData = personality.getNameData();
        if(nameData != null) {
            bean.setFirstName(nameData.getFirstName());
            bean.setMiddleInitial(nameData.getMiddleInitial());
            bean.setLastName(nameData.getLastName());
            bean.setPersonNumber(nameData.getPersonNumber());
          }
    }

    private static void loadCurrentUserFields(UserInfoBean bean, CurrentUser currentUser) {
        bean.setUserLocale(currentUser.getUserLocale());
        bean.setTimeZone(currentUser.getTimeZone());
        bean.setEmployeeId(currentUser.getEmployeeId());
    }

   private static PersonalityService getPersonalityService() {
      if (personalityService == null) {
         personalityService = SpringContext.getBean(PersonalityService.class);
      }
      return personalityService;
   }

   private static CurrentUserContactDetailsPopulator getCurrentUserContactDetailsPopulator() {
      if (currentUserContactDetailsPopulator == null) {
         currentUserContactDetailsPopulator = SpringContext.getBean(CurrentUserContactDetailsPopulator.class);
      }
      return currentUserContactDetailsPopulator;
   }
 
}
