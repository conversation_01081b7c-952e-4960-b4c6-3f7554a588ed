package com.kronos.persons.rest.assignments.service;

import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response;

import com.kronos.authz.api.annotations.IsPermitted;
import com.kronos.persons.EmployeeAssignmentsConstants;
import org.springframework.stereotype.Service;

import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignmentDTO;

@Service("PersonAttestationProfileAssignmentRestServiceImpl")
public class PersonAttestationProfileAssignmentRestServiceImpl
    implements PersonAttestationProfileAssignmentRestService {

    @Inject
    private PersonAttestationProfileAssignmentService personAttestationProfileAssignmentService;

    @Override
    @IsPermitted(accessControlPoint = EmployeeAssignmentsConstants.EMPLOYEE_ASSIGNMENTS_READ, action = EmployeeAssignmentsConstants.ALLOWED)
    public Response retrieveByPersonId(Long personId, boolean isAssignToManagerRole) {
        return Response.ok(personAttestationProfileAssignmentService.getAttestationProfileAssignments(personId, isAssignToManagerRole))
            .build();
    }

    @Override
    @IsPermitted(accessControlPoint = EmployeeAssignmentsConstants.EMPLOYEE_ASSIGNMENTS_EDIT, action = EmployeeAssignmentsConstants.ALLOWED)
    public Response create(Long personId, PersonAttestationProfileAssignmentDTO dto) {
        PersonAttestationProfileAssignmentDTO result = personAttestationProfileAssignmentService.create(personId, dto);
        return Response.status(Response.Status.CREATED)
            .entity(result)
            .build();
    }
}
