/***********************************************************************
 * TimeEntryTypeUtil.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/

package com.kronos.persons.context.service;

import java.util.Date;

import com.kronos.wfc.commonapp.people.business.user.CurrentUserAccountManager;
import com.kronos.wfc.platform.logging.framework.Log;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;

/**
 * This class is the utility class to get the time entry details.
 * 
 * <AUTHOR>
 *
 */
public class TimeEntryTypeUtil {
	
	 public static final long TIME_ENTRY_UNASSIGNED = 0;

	 public TimeEntryTypeUtil(){
		 
	 }
	 
	/**
	    * Gets the time entry type ID.
	    *
	    * @param asOfDate the time entry method 'as of' date
	    * @return Returns the type ID of the time entry that covers the specified asOfDate
	    */
	 public long getTimeEntryType(Date asOfDate) {
	      // default type
	      long ttType = TIME_ENTRY_UNASSIGNED;
	      try {
	         KDate kdate = KDate.createDate(asOfDate.getTime());
	         ObjectIdLong timeEntryTypeId = CurrentUserAccountManager.getPersonality().getAccessAssignment().getTimeEntryTypeId(kdate);
	         if (timeEntryTypeId != null) {
	            ttType = timeEntryTypeId.longValue();
	         }
	      }
	      catch (Exception ex) {
	         Log.log(Log.ERROR, ex.getMessage());
	      }
	      return ttType;
	   }
	
	
}