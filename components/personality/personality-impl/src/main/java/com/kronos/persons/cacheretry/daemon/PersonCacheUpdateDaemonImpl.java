package com.kronos.persons.cacheretry.daemon;

import java.util.Objects;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.springframework.beans.factory.annotation.Qualifier;

import com.kronos.eventframework.api.EventManagerExecutor;
import com.kronos.eventframework.dto.EventManagerDTO;
import com.kronos.people.personality.Operation;

@Named("PersonCacheUpdateDaemon")
public class PersonCacheUpdateDaemonImpl implements EventManagerExecutor {

	@Inject
	@Qualifier("PersonCacheUpdateAgent")
	Operation personCacheUpdateAgent;

	/**
	 * This method will execute the Logic for Person Cache Update daemon
	 */
	@Override
	public void run(EventManagerDTO eventManagerDTO) {
		if (Objects.nonNull(personCacheUpdateAgent))
			personCacheUpdateAgent.execute();
	}
}
