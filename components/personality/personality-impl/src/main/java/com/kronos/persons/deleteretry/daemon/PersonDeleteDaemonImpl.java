package com.kronos.persons.deleteretry.daemon;

import java.util.Objects;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.springframework.beans.factory.annotation.Qualifier;

import com.kronos.eventframework.api.EventManagerExecutor;
import com.kronos.eventframework.dto.EventManagerDTO;
import com.kronos.people.personality.Operation;

@Named("PersonDeleteDaemon")
public class PersonDeleteDaemonImpl implements EventManagerExecutor {

	@Inject
	@Qualifier("DeletePersonAgent")
	Operation deletePersonAgent;

	/**
	 * This method will execute the Logic for Person Delete daemon
	 */
	@Override
	public void run(EventManagerDTO eventManagerDTO) {
		if (Objects.nonNull(deletePersonAgent))
			deletePersonAgent.execute();
	}
}
