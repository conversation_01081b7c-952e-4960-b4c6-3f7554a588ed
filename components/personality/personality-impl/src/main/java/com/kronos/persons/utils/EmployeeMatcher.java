/***********************************************************************
 * EmployeeMatcher.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/

package com.kronos.persons.utils;

import java.util.Collections;
import java.util.Date;
import java.util.Map;

import com.kronos.commonbusiness.converter.api.IMatcher;
import com.kronos.commonbusiness.converter.api.IRepositoryBasedConverter;
import com.kronos.commonbusiness.converter.injector.AbstractMatcher;
import com.kronos.commonbusiness.converter.model.ObjectId;
import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.persons.rest.model.Employee;
import com.kronos.persons.rest.model.EmployeeRef;
import com.kronos.persons.rest.model.EmployeeSurrogate;
import com.kronos.persons.rest.service.PersonRestServiceConstants;
import com.kronos.wfc.commonapp.currency.business.assignment.EmployeeCurrencyAssignment;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.commonapp.people.business.person.TelephoneNumber;
import com.kronos.wfc.commonapp.people.business.person.TelephoneNumberSet;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.types.business.ContactType;
import com.kronos.wfc.datacollection.empphoto.business.EmpPhotoIdCache;
import com.kronos.wfc.platform.datetime.framework.KServer;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.platform.utility.framework.datetime.KDateSpan;
import com.kronos.wfc.platform.utility.framework.datetime.KPreference;
import com.kronos.wfc.scheduling.core.business.SchedEmpCache;
import com.kronos.wfc.scheduling.core.business.SchedEmployee;
import com.kronos.wfc.scheduling.core.business.SchedEmployeeService;

/**
 * The Matcher class for employee.
 * <AUTHOR>
 *
 */
public class EmployeeMatcher extends AbstractMatcher {

	private KDate snapshotDate = new KDate();

	/**
	 * Logger.
	 */
	private static final KLogger LOGGER = KLoggerFactory.getKLogger(EmployeeMatcher.class);

	/**
	 * Constructor which takes in an implementation of IRepositoryBasedConverter.
	 * @param converter implementation of IRepositoryBasedConverter
	 */
	public EmployeeMatcher(IRepositoryBasedConverter converter) {
		super(converter, Employee.class, EmployeeSurrogate.class, SchedEmployee.class, EmployeeRef.class);
	}

	/**
	 * @param converter implementation of IRepositoryBasedConverter
	 */
	public static void register(IRepositoryBasedConverter converter) {
		LOGGER.debug("Registering EmployeeMatcher with the converter framework.");
		IMatcher matcher = new EmployeeMatcher(converter);
		matcher.register();
	}

	/* (non-Javadoc)
	 * @see com.kronos.wfc.commons.commons.converter.AbstractMatcher#convertDTOToDTORef(java.lang.Object, java.lang.Object)
	 */
	@Override
	public Object convertDTOToDTORef(Object src, Object dest) throws APIException {
		Employee employee = (Employee) src;
		EmployeeRef empref = new EmployeeRef();
		empref.setInternalId(employee.getId().getId());
		empref.setPersonId(employee.getPersonNum());
		return empref;
	}

	/* (non-Javadoc)
	 * @see com.kronos.wfc.commons.commons.converter.AbstractMatcher#convertDTOToDTOSurrogate(java.lang.Object, java.lang.Object)
	 */
	@Override
	public Object convertDTOToDTOSurrogate(Object src, Object dest) throws APIException {
		Employee emp = (Employee) src;
		EmployeeSurrogate sur = (EmployeeSurrogate) dest;
		sur.setEmployeeId(emp.getId());
		sur.setSnapshotDateUTC(emp.getSnapshotDateUTC());
		sur.setPersonNumber(emp.getPersonNum());
		sur.setFullName(emp.getFullName());
		sur.setShortName(emp.getShortName());
		sur.setCurrencySymbol(emp.getCurrencySymbol());
		sur.setWfsLicensed(emp.getIsWFSLicensed());
		sur.setPhotoId(getPhotoIdFromEmployee(emp));
		return sur;
	}

	/**
	 * This method returns PhotoId from EmpPhotoIdCache based on employee intance's Id.
	 * @param emp Employee instance
	 * @return String containing the photoId
	 */
	String getPhotoIdFromEmployee(Employee emp) {
		try {
			return EmpPhotoIdCache.getInstance().getEmployeePhotoId(new ObjectIdLong(emp.getId().getId()));
		}catch(Exception e) {
			LOGGER.error("Exception while getting photoId from Employee",e.getMessage());
			throw e;
		}
	}

	/* (non-Javadoc)
	 * @see com.kronos.wfc.commons.commons.converter.AbstractMatcher#identifyDTOAsId(java.lang.Object, com.kronos.wfc.commons.commons.model.ObjectId)
	 */
	@Override
	public ObjectId identifyDTOAsId(Object src, ObjectId id) throws APIException {
		return ((Employee) src).getId();
	}

	/* (non-Javadoc)
	 * @see com.kronos.wfc.commons.commons.converter.AbstractMatcher#identifyDTOSurrogateAsId(java.lang.Object, com.kronos.wfc.commons.commons.model.ObjectId)
	 */
	@Override
	public ObjectId identifyDTOSurrogateAsId(Object src, ObjectId id) throws APIException {

		EmployeeSurrogate sur = (EmployeeSurrogate) src;

		ObjectId destination = sur.getEmployeeId();

		if (destination == null && (sur.getPersonNumber() != null && sur.getPersonNumber().trim().length() > 0)) {
			// find the employee id based on person number
			ObjectIdLong legacyId = findEmployeeIdByPersonNumber(sur.getPersonNumber());
			LOGGER.debug("Employee id extracted from person number : "+legacyId);
			destination = convert(legacyId, ObjectId.class);
		}
		return destination;
	}

	/* (non-Javadoc)
	 * @see com.kronos.wfc.commons.commons.converter.AbstractMatcher#identifyDTORefAsId(java.lang.Object, com.kronos.wfc.commons.commons.model.ObjectId)
	 */
	@Override
	public ObjectId identifyDTORefAsId(Object src, ObjectId id) throws APIException {

		EmployeeRef sur = (EmployeeRef) src;

		Long internalId = sur.getInternalId();

		if (internalId == null && (sur.getPersonId() != null && sur.getPersonId().trim().length() > 0)) {
			// find the employee id based on person number
			ObjectIdLong legacyId = findEmployeeIdByPersonNumber(sur.getPersonId());

			if (legacyId == null) {
				// maybe the ID we got was an internal ID
				try {
					legacyId = new ObjectIdLong(Long.parseLong(sur.getPersonId()));
				} catch (NumberFormatException ex) {
					LOGGER.error(PersonRestServiceConstants.NUMBER_FORMAT_EXCEPTION_PARSING_SURROGATE_PERSONID, ex.getMessage()); 
					legacyId = null;
				}
			}

			return convert(legacyId, ObjectId.class);
		}
		if (internalId == null) {
			return null;
		}
		return new ObjectId(internalId);
	}

	/**
	 * This method finds employeeId from the passed personNumber.
	 * @param personNumber String containing the personNumber
	 * @return ObjectIdLong instance
	 */
	protected ObjectIdLong findEmployeeIdByPersonNumber(String personNumber) {
		Personality person = getPersonality(personNumber);
		if (person != null && person.getJobAssignment() != null
				&& person.getJobAssignment().getJobAssignmentDetails() != null) {
			return person.getJobAssignment().getJobAssignmentDetails().getEmployeeId();
		}
		return null;
	}

	/**
	 * @param personNumber String containing the personNumber
	 * @return Personality instance
	 */
	protected Personality getPersonality(String personNumber) {
		return Personality.getByPersonNumber(personNumber);
	}

	/* (non-Javadoc)
	 * @see com.kronos.wfc.commons.commons.converter.AbstractMatcher#identifyLegacyBOAsLegacyId(java.lang.Object, com.kronos.wfc.platform.persistence.framework.ObjectIdLong)
	 */
	@Override
	public ObjectIdLong identifyLegacyBOAsLegacyId(Object src, ObjectIdLong id) throws APIException {
		return ((SchedEmployee) src).getEmployeeId();
	}

	/* (non-Javadoc)
	 * @see com.kronos.wfc.commons.commons.converter.AbstractMatcher#resolveLegacyIdToLegacyBO(com.kronos.wfc.platform.persistence.framework.ObjectIdLong, java.lang.Object)
	 */
	@Override
	public Object resolveLegacyIdToLegacyBO(ObjectIdLong id, Object dest) throws APIException {

		SchedEmployee employee = getSchedEmployee(id);
		if (employee == null) {// usually for the employee API it is null
			KDate now = KDate.createDate();
			KDateSpan range = new KDateSpan(now, now);
			Map<?, ?> map = getMapFromSchedEmployeeService(id, range);
			if (map != null && map.size() > 0) {
				employee = (SchedEmployee) map.get(id);
			}

			if (employee == null) {
				employee = createSchedEmployeeFromId(id);
			}
		}

		return employee;
	}

	/**
	 * @param id ObjectIdLong instance
	 * @return SchedEmployee instance
	 */
	protected SchedEmployee createSchedEmployeeFromId(ObjectIdLong id) {
		return new SchedEmployee(id);
	}

	/**
	 * @param id ObjectIdLong instance
	 * @param range KDateSpan
	 * @return Map
	 */
	protected Map getMapFromSchedEmployeeService(ObjectIdLong id, KDateSpan range) {
		return SchedEmployeeService.getInstance().getSchedEmployeesById(Collections.singletonList(id), range);
	}

	/**
	 * @param id ObjectIdLong instance
	 * @return SchedEmployee instance
	 */
	protected SchedEmployee getSchedEmployee(ObjectIdLong id) {
		return SchedEmpCache.getSchedEmpCache().getSchedEmployeeById(id);
	}

	/* (non-Javadoc)
	 * @see com.kronos.wfc.commons.commons.converter.IMatcher#convertLegacyBOToDTO(java.lang.Object, java.lang.Object)
	 */
	@Override
	public Object convertLegacyBOToDTO(Object src, Object dest) throws APIException {
		LOGGER.debug("Converting legacy BO to DTOs");
		convertSchedEmployeeToEmployee((SchedEmployee) src, (Employee) dest);
		return dest;
	}

	/**
	 * @return snapshotDate
	 */
	public KDate getSnapshotDate() {
		return snapshotDate;
	}

	/**
	 * @param snapshotDate sets the snapShotDate
	 */
	public void setSnapshotDate(KDate snapshotDate) {
		this.snapshotDate = snapshotDate;
	}

	/**
	 * Populates the parameters of legacy instance passed to the employee instance.
	 * @param legacy SchedEmployee instance
	 * @param emp Employee instance
	 */
	protected void convertSchedEmployeeToEmployee(SchedEmployee legacy, Employee emp) {
		ObjectId id = setIdInEmployee(legacy);
		setSnapshotDateUTCInEmployee(emp);
		emp.setId(id);
		emp.setFullName(legacy.getEmployeeName());
		emp.setShortName(legacy.getEmployeeShortName());
		emp.setPersonNum(legacy.getPersonNumber());
		emp.setPhoneNumberOne(getPhoneOne(legacy));
		emp.setCurrencySymbol(getCurrencySymbol(legacy));
		emp.setIsWFSLicensed(legacy.hasWFSLicense());
		emp.setIsWTKLicensed(legacy.hasWTKLicense());
		emp.setPhotoId(getPhotoIdFromBO(id));
	}

	/**
	 * Sets snapShotDayteUTC in employee instance
	 * @param emp Employee instance
	 */
	protected void setSnapshotDateUTCInEmployee(Employee emp) {
		try {
			emp.setSnapshotDateUTC(convert(snapshotDate, Date.class));
		} catch (Exception exception) {
			LOGGER.error(PersonRestServiceConstants.ERROR_SETTING_SNAPSHOT_IN_EMPLOYEE_MESSAGE);
			throw new APIException(PersonRestServiceConstants.ERROR_SETTING_PARAMETERS_FOR_EMPLOYEE, exception);
		}
	}

	/**
	 * Returns the id from legacy object.
	 * @param legacy SchedEmployee instance
	 * @return ObjectId instance
	 */
	protected ObjectId setIdInEmployee(SchedEmployee legacy) {
		ObjectId id = null;
		try {
			id = getIdFromBO(legacy);
		} catch (Exception exception) {
			LOGGER.error(PersonRestServiceConstants.ERROR_FETCHING_ID_FROM_LEGACY_FOR_SETTING_IN_EMPLOYEE_MESSAGE);
			throw new APIException(PersonRestServiceConstants.ERROR_SETTING_PARAMETERS_FOR_EMPLOYEE, exception);
		}
		return id;
	}

	/**
	 * Returns person from id.
	 * @param id ObjectIdLong instance
	 * @return Person instance
	 */
	Person getPersonById(ObjectIdLong id) {
		try {
			return Person.getByPersonId(id);
		}catch(Exception e) {
			LOGGER.error("Exception while getting person id",e.getMessage());
			throw e;
		}
	}

	/**
	 * Converts ObjectId instance to legacy instance and returns.
	 * @param legacy SchedEmployee instance
	 * @return ObjectId instance
	 * @throws Exception incase the conversion fails
	 */
	ObjectId getIdFromBO(SchedEmployee legacy) throws Exception {
		return convert(legacy, ObjectId.class);
	}

	/**
	 * Converts the legacy instance to ObjectId type and returns
	 * @param legacy SchedEmployee instance
	 * @param type ObjectId class type
	 * @return ObjectId
	 * @throws Exception incase the conversion fails
	 */
	ObjectId convert(SchedEmployee legacy, Class<ObjectId> type) throws Exception {
		return super.convert(legacy.getEmployeeId(), type);
	}

	/**
	 * @param id ObjectId instance
	 * @return String instance
	 */
	String getPhotoIdFromBO(ObjectId id) {
		ObjectIdLong idLong = new ObjectIdLong(id.getId());
		LOGGER.debug("Getting photo id from BO..for id "+idLong);
		return EmpPhotoIdCache.getInstance().getEmployeePhotoId(idLong);
	}

	/**
	 * Return the phone number one String for the specified employee, or an
	 * empty string if not found.
	 *
	 * @param schedEmp
	 *            schedEmployee
	 * @return String - formatter as user-entered
	 */
	private String getPhoneOne(SchedEmployee schedEmp) {
		return getPhoneNumber(schedEmp, ContactType.PHONE_1);
	}

	/**
	 * Return the phone number String for the specified employee, or an empty
	 * string if not found.
	 *
	 * @param schedEmp
	 *            schedEmployee
	 * @param phoneId
	 *            id of phone field - see class constants
	 * @return String - formatter as user-entered
	 */
	String getPhoneNumber(SchedEmployee schedEmp, ObjectIdLong phoneId) {
		LOGGER.debug("Getting phone numbers..");
		try {
			TelephoneNumberSet phones = schedEmp.getPersonality().getTelephoneNumbers();
			if (phones != null) {
				TelephoneNumber phone = phones.getTelephoneNumber(phoneId);
				if (phone != null)
					return phone.getPhoneNumber();
			}
			return "";
		}catch(Exception e) {
			LOGGER.error("Exception while getting phone numbers",e.getMessage());
			throw e;
		}
	}

	/**
	 * @deprecated
	 * Returns Currency symbol.
	 * @param schedEmp SchedEmployee instance
	 * @return String instance
	 */
	String getCurrencySymbol(SchedEmployee schedEmp) {
		LOGGER.debug("Getting currency symbol..");
		return getCurrencyCode(schedEmp);
	}

	/**
	 * Returns Currency code.
	 * @param schedEmp SchedEmployee instance
	 * @return String instance
	 */
	String getCurrencyCode(SchedEmployee schedEmp) {
		LOGGER.debug("Getting currency code..");
		try {
			EmployeeCurrencyAssignment currencyAssignment = schedEmp.getPersonality().getEmpCurrencyAssign();
			//as per the requirement of multi-currency returning currency code
			return currencyAssignment.getCurrencyCode();
		}catch(Exception e) {
			LOGGER.error("Exception while getting currency code",e.getMessage());
			throw e;
		}
	}
	
	
	
	/* (non-Javadoc)
	 * @see com.kronos.wfc.commons.commons.converter.AbstractMatcher#validateSurrogateSolvingConstraints(java.lang.Object)
	 */
	@Override
	protected void validateSurrogateSolvingConstraints(Object o) {
		EmployeeSurrogate sur = (EmployeeSurrogate) o;
		if ((sur.getEmployeeId() == null)
				&& (sur.getPersonNumber() == null || (sur.getPersonNumber().trim().length() == 0))) {
			LOGGER.error(PersonRestServiceConstants.ERROR_VALIDATING_EMPLOYEESURROGATE_MESSAGE);
			throw new APIException(PersonRestServiceConstants.ERROR_VALIDATING_EMPLOYEESURROGATE);
		}
	}

	/* (non-Javadoc)
	 * @see com.kronos.wfc.commons.commons.converter.AbstractMatcher#validateDTOSolvingConstraints(java.lang.Object)
	 */
	@Override
	protected void validateDTOSolvingConstraints(Object o) {
		Employee emp = (Employee) o;
		if (emp.getId() == null) {
			LOGGER.error(PersonRestServiceConstants.ERROR_VALIDATING_EMPLOYEEDTO_MESSAGE);
			throw new APIException(PersonRestServiceConstants.ERROR_VALIDATING_EMPLOYEEDTO);
		}
	}

}
