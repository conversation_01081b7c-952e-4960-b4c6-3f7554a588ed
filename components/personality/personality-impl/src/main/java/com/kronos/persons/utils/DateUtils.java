package com.kronos.persons.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;

import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.persons.rest.beans.LocalDateSpan;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.platform.utility.framework.datetime.KDateSpan;
import com.kronos.wfc.platform.utility.framework.datetime.KDateTime;
import com.kronos.wfc.platform.utility.framework.datetime.KTime;
import com.kronos.wfc.platform.xml.api.bean.APIValidationException;

/**
 * <AUTHOR>
 */
public class DateUtils {

    public static final String DATESPAN_SEPARATOR = " ";

    public static final String DATERANGE_ERROR = "Invalid date range specified. Use space separated dates in YYYY-MM-DD format";

    private static final String DATE_TIME_SEPARATOR = "T";

    private static DateTimeFormatter dateformat = DateTimeFormatter.ISO_LOCAL_DATE;
    private static DateTimeFormatter datetimeformat = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

    private static final KLogger LOGGER = KLoggerFactory.getKLogger(DateUtils.class);

    /**
     * Convert the LocalDate to the KDate
     * 
     * @param date
     *            LocalDate
     * @return KDate the kronos date
     */
    public static KDate localDateToKdate(LocalDate date) {
        return (Optional.ofNullable(date).isPresent())
                ? KDate.createDate(date.getYear(), date.getMonthValue(), date.getDayOfMonth())
                : null;
    }

    /**
     * Convert the date string to local date with default date format
     * 
     * @param The
     *            {@link String} object
     * @return The {@link LocalDate} object.
     */
    public static LocalDate convertStringToLocalDate(String dateString) {
        LocalDate date = null;
        if (StringUtils.isNotBlank(dateString)) {
            try {
                date = LocalDate.parse(dateString, dateformat);
            } catch (Exception ex) {
                LOGGER.error("Error occured while parsing " + dateString, ex);
            }
        }
        return date;
    }

    public static LocalDateTime convertStringToLocalDateTime(String dateTimeString) {
        LocalDateTime date = null;
        if (StringUtils.isNotBlank(dateTimeString)) {
            try {
                date = LocalDateTime.parse(dateTimeString, datetimeformat);
            } catch (Exception ex) {
                LOGGER.error("Error occured while parsing " + dateTimeString, ex);
            }
        }
        return date;
    }

    /**
     * Convert the date string to local date with default date format
     * 
     * @param The
     *            {@link String} object
     * @param {@link
     *            String} datePattern
     * @return The {@link LocalDate} object.
     */
    public static LocalDate convertStringToLocalDate(String dateString, String datePattern) {
        LocalDate date = null;
        if (StringUtils.isNotBlank(dateString)) {
            try {
                DateTimeFormatter formatter = new DateTimeFormatterBuilder().parseLenient().appendPattern(datePattern).toFormatter();
                date = LocalDate.parse(dateString, formatter);
            } catch (Exception ex) {
                LOGGER.error("Error occured while parsing " + dateString, ex);
            }
        }
        return date;
    }

    public static KDateSpan kDateSpanFromLocalDateSpan(LocalDateSpan dateSpan) {
        if (dateSpan == null)
            return null;

        return new KDateSpan(localDateToKdate(LocalDate.parse(dateSpan.getStartDate(), dateformat)),
                localDateToKdate(LocalDate.parse(dateSpan.getEndDate(), dateformat)));
    }

    public static KDate stringtokdate(String value) {
        LocalDate lDate = LocalDate.parse(value, dateformat);
        return KDate.createDate(lDate.getYear(),
                lDate.getMonthValue(), lDate.getDayOfMonth());
    }

    public static KDateTime stringtokdatetime(String value) {
        if (value.contains(DATE_TIME_SEPARATOR)) {
            LocalDateTime lDateTime = LocalDateTime
                    .parse(value, datetimeformat);
            return localDateTimeToKdateTime(lDateTime);
        } else {
            LocalDate lDate = LocalDate.parse(value, dateformat);
            KDate date = KDate.createDate(lDate.getYear(),
                    lDate.getMonthValue(), lDate.getDayOfMonth());
            return localDateTimeToKdateTime(date, new KTime(0));
        }
    }

    /**
     * Convert the LocalDateTime BO to the KDateTime.
     * 
     * @param localDateTime
     *            LocalDateTime
     * @return KDateTime the Kronos date time
     */
    public static KDateTime localDateTimeToKdateTime(LocalDateTime localDateTime) {
        return (Optional.ofNullable(localDateTime).isPresent()) ? KDateTime.createDateTime(
                localDateToKdate(localDateTime.toLocalDate()), localTimeToKtime(localDateTime.toLocalTime())) : null;
    }

    /**
     * Convert the LocalTime BO to the KTime.
     * 
     * @param localTime
     *            the local time
     * @return KTime the Kronos time
     */
    public static KTime localTimeToKtime(LocalTime localTime) {
        return (Optional.ofNullable(localTime).isPresent()) ? KTime.create(localTime.getHour(), localTime.getMinute(),
                localTime.getSecond(), (int) TimeUnit.NANOSECONDS.toMillis(localTime.getNano())) : null;
    }

    /**
     * Convert the LocalDateTime BO to the KDateTime.
     * 
     * @param date
     * @param time
     * @return KDateTime the Kronos date time
     */
    public static KDateTime localDateTimeToKdateTime(KDate date, KTime time) {
        return (Optional.ofNullable(date).isPresent() && Optional.ofNullable(time).isPresent()) ? new KDateTime(date, time) : null;
    }
    
    /**
     * Convert the KDateTime BO to the LocalDateTime.
     * 
     * @param kDateTime
     *            The kronos date and time
     * @return LocalDateTime the local date time
     */
    public static LocalDateTime kDateTimeToLocalDateTime(KDateTime kDateTime) {
        return LocalDateTime.of(kDateToLocalDate(kDateTime.getDate()), kTimeToLocalTime(kDateTime.getTime()));
    }
    
    /**
     * Convert the KDate BO to the LocalDate.
     * 
     * @param date
     *            KDate
     * @return LocalDate the local date
     */
    public static LocalDate kDateToLocalDate(KDate date) {
        return LocalDate.of(date.getYear(), date.getMonth(), date.getDay());
    }
    
    /**
     * Convert the KTime BO to the LocalTime.
     * 
     * @param kTime
     *            KTime
     * @return LocalTime the local time
     */
    @SuppressWarnings("deprecation")
    public static LocalTime kTimeToLocalTime(KTime kTime) {
        return LocalTime.of(kTime.getHour(), kTime.getMinute(), kTime.getSecond(),
                (int) TimeUnit.MILLISECONDS.toNanos(kTime.getMillis()));
    }
    
    /**
     * Parse the property as a {@link LocalTime} property.
     * <p/>
     * {@link APIValidationException} is thrown if value has non-convertible
     * type or value.
     *
     * @param name
     *            The property name.
     * @param value
     *            The property value.
     */
    public static KTime stringtoktime(String value){
        LocalTime localTime = LocalTime.parse(value);
        return KTime.create(localTime.getHour(), localTime.getMinute(), localTime.getSecond(),
                (int) TimeUnit.NANOSECONDS.toMillis(localTime.getNano()));
    }

	public static LocalDateTime getStartingOfTheDay(LocalDate dateTime) {
		return LocalDateTime.of(dateTime, LocalTime.MIN);
	}

	public static LocalDateTime getMidNight(LocalDate dateTime) {
		return LocalDateTime.of(dateTime, LocalTime.MAX);
	}
}