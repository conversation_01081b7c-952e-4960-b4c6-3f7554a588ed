/***********************************************************************
 * EmployeeServiceConverter.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/

package com.kronos.persons.utils;

import com.kronos.commonbusiness.converter.injector.Converter;


/**
 * The converter class for Employee. 
 * <AUTHOR>
 */
public class EmployeeServiceConverter extends Converter {

	/* (non-Javadoc)
	 * @see com.kronos.commonbusiness.converter.injector.Converter#loadRepository()
	 */
	@Override
	protected void loadRepository() {
		super.loadRepository();
		EmployeeInjectorsRegistrar.INSTANCE.register(this);
	}
}
