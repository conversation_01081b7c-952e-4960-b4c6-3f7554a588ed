package com.kronos.persons.deleteretry.repository;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.kronos.persons.deleteretry.model.PersonDeleteRetryEntity;

@Repository
@Transactional
public interface PersonDeleteRetryRepository extends JpaRepository<PersonDeleteRetryEntity, Long> {
	
	@Modifying
	@Query(nativeQuery = true , value = "UPDATE person_delete_retry pdre SET pdre.retryCount = pdre.retryCount + 1, pdre.lastUpdatedtm = :currentUTCTtime "
			+ " where pdre.personid = :personId and pdre.retryCount = :retryCount")
	int updatePersonRetryCount(@Param("personId") BigInteger personId,
			@Param("retryCount") Integer retryCount, @Param("currentUTCTtime") Timestamp currentUTCTtime);

	@Query(nativeQuery = true , value ="SELECT * FROM person_delete_retry pdre where pdre.retrycount < :threshold and pdre.lastUpdatedtm < :timeDif "
			+ " and pdre.isPersonDeleted = 0 and EXISTS (SELECT p.personid from person p where pdre.personid = p.personid and p.deletedsw = 1) ")
	List<PersonDeleteRetryEntity> getPersonIdToDelete(@Param("threshold") int threshold,
			@Param("timeDif") Timestamp timeDif);
	
	@Modifying
	@Query(nativeQuery = true , value ="DELETE FROM person_delete_retry pdre where pdre.isPersonDeleted = 0 "
			+ " and NOT EXISTS (SELECT p.personid from person p where pdre.personid = p.personid and p.deletedsw = 1) ")
	void removeDeletedPerson();
	
	@Modifying
	@Query(nativeQuery = true, value = "DELETE FROM person_delete_retry pdre where pdre.lastUpdatedtm < :time")
	void removeOldRecords(@Param("time") Timestamp time);
}	
