package com.kronos.persons.context.service;

import java.util.List;

import com.kronos.commonapp.authz.api.fap.permissions.IPermission;
import com.kronos.commonapp.userpreferences.api.IKPreferences;

/**
 * Application Context Class to hold data for currentUser details,
 * User Prefernces and list of permissions
 * <AUTHOR>
 *
 */
public class ApplicationContextImpl implements IApplicationContext{
	
	
	private CurrentUser currentUser;
	private IKPreferences preference;
	private List<IPermission> permission;
	private List<IPermission> managerPermission;
	
	
	/**
	 * getUser Details
	 */
	@Override
	public CurrentUser getCurrentUser() {
		return currentUser;
	}
	public void setCurrentUser(CurrentUser currentUser) {
		this.currentUser = currentUser;
	}
	
	/**
	 * get User preferences
	 */
	@Override
	public IKPreferences getPreference() {
		return preference;
	}
	public void setPreference(IKPreferences preference) {
		this.preference = preference;
	}
	
	/**
	 * 
	 * @param permission the permission to set
	 */
	public void setPermission(List<IPermission> permission) {
		this.permission = permission;
	}
	
	public void setManagerPermission(List<IPermission> managerPermission) {
		this.managerPermission = managerPermission;
	}
	@Override
	public List<IPermission> getPermissions() {
		return permission;
	}
	@Override
	public List<IPermission> getManagerPermissions() {
		return managerPermission;
	}
	

}
