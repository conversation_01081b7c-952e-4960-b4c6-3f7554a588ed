package com.kronos.persons.rest.assignments.validation;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import jakarta.inject.Inject;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.container.api.exception.APIException;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.util.PersonalityHelper;
import com.kronos.persons.rest.assignments.model.AttestationProfileAssignment;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignment;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignmentDTO;
import com.kronos.persons.rest.beans.HyperFindFilterBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.exception.PrsnInvalidBeanException;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.timekeeping.service.attestation.api.service.AttestationProfileSetupService;
import com.kronos.wfc.commonapp.people.business.person.PersonLicenseTypeSet;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.user.CurrentUserAccountManager;
import com.kronos.wfc.platform.security.business.authorization.profiles.AccessProfile;
import com.kronos.wfc.platform.security.shared.SecurityConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import static com.kronos.persons.rest.exception.ExceptionConstants.EMPTY_MULTI_UPDATE_REQUEST;
import static com.kronos.persons.rest.exception.ExceptionConstants.MANAGER_SELF_EDIT_NOT_ALLOWED;

@Component
public class PersonAttestationProfileAssignmentValidator {
    private static final String EFFECTIVE_DATE = "effectiveDate";
    private static final String PROFILE_NAME = "profile.qualifier";
    private static final String PROFILE = "profile";
    private static final String PERSON_NUMBER = "person.qualifier";
    private static final String PERSON = "person";
    private static final String FOREVER_DATE = "3000-01-01";

    private static final LocalDate SOT_DATE = LocalDate.parse("1900-01-01", DateTimeFormatter.ISO_LOCAL_DATE);

    @Inject PersonIdentityBeanValidator personIdentityBeanValidator;
    private AttestationProfileSetupService attestationProfileSetupService;

    @Inject PersonalityHelper personalityHelper;

    private enum ObjectRefType {
        PERSON,
        PROFILE
    }
    public PersonAttestationProfileAssignmentValidator(
        PersonIdentityBeanValidator personIdentityBeanValidator, PersonalityHelper personalityHelper) {
        this.personIdentityBeanValidator = personIdentityBeanValidator;
        this.personalityHelper = personalityHelper;
    }


    public void validateReadAccess(Long personId) {
        PersonIdentityBean personIdentity = personIdentityBeanValidator.buildPersonIdentity(personId);
        personIdentityBeanValidator.getPersonality(personIdentity);
    }

    public Long validateReadAccessAndGetPersonId(String personNumber) {
        PersonIdentityBean personIdentity = personIdentityBeanValidator.createForPersonNumber(personNumber);
        Personality personality = personIdentityBeanValidator.getPersonality(personIdentity);
        return personality.getPersonId().toLong();
    }

    public Map<Long, Object> validateReadAccessAndGetObjectRefsByPersonIds(List<Long> personIds, HyperFindFilterBean hyperFindFilter) {
        Map<Long, Object> employees = new HashMap<>();
        if (personIds != null && !personIds.isEmpty()) {
            Map<Long, EmployeeExtension> existingPersons = personalityHelper.getPersonNumbersByEmployeeIds(personIds);

            personIds.forEach(entry -> employees.put(entry,
                    personIdentityBeanValidator.validatePersonId(entry, existingPersons.get(entry), hyperFindFilter)));
        }
        return employees;
    }

    public Map<Long, Object> validateReadAccessAndGetObjectRefsByPersonIds(Map<Long, HyperFindFilterBean> personsWithFilters) {
        Map<Long, Object> employees = new HashMap<>();
        if (personsWithFilters != null && !personsWithFilters.isEmpty()) {
            Map<Long, EmployeeExtension> existingPersons = personalityHelper.getPersonNumbersByEmployeeIds(new ArrayList<>(personsWithFilters.keySet()));

            final boolean hasAccessToEveryone = personIdentityBeanValidator.hasAccessToEveryone();
            personsWithFilters.forEach((id, filter) -> {
                Object answer = personIdentityBeanValidator.validatePerson(existingPersons.get(id), filter,
                        () -> buildPersonIdentity(id), hasAccessToEveryone);
                employees.put(id, answer);
            });
        }
        return employees;
    }

    public void validatePersonNotNullReference(ObjectRef employee) {
        validateObjectRefIsNotNullReference(employee, ObjectRefType.PERSON);
    }

    public Map<String, Object> validateReadAccessAndGetObjectRefsByPersonNums(List<String> personNumbers, HyperFindFilterBean hyperFindFilter) {
        Map<String, Object> employees = new HashMap<>();
        if (personNumbers != null && !personNumbers.isEmpty()) {
            Map<String, EmployeeExtension> existingPersons = personalityHelper.getPersonIdsByPersonNumbers(personNumbers);

            personNumbers.forEach(entry -> employees.put(entry,
                    personIdentityBeanValidator.validatePersonNumber(entry, existingPersons.get(entry), hyperFindFilter)));
        }
        return employees;
    }

    public Map<String, Object> validateReadAccessAndGetObjectRefsByPersonNums(Map<String, HyperFindFilterBean> personNumbers) {
        Map<String, Object> employees = new HashMap<>();
        if (personNumbers != null && !personNumbers.isEmpty()) {
            Map<String, EmployeeExtension> existingPersons = personalityHelper.getPersonIdsByPersonNumbers(new ArrayList<>(personNumbers.keySet()));

            final boolean hasAccessToEveryone = personIdentityBeanValidator.hasAccessToEveryone();
            personNumbers.forEach((personNumber, filter) -> {
                Object answer = personIdentityBeanValidator.validatePerson(existingPersons.get(personNumber), filter,
                        () -> personIdentityBeanValidator.createForPersonNumber(personNumber), hasAccessToEveryone);
                employees.put(personNumber, answer);
            });
        }
        return employees;
    }

    public void validateCreateAccess(Long personId, PersonAttestationProfileAssignmentDTO dto) {
        PersonIdentityBean personIdentity = buildPersonIdentity(personId);
        PrsnInvalidBeanException multiException = new PrsnInvalidBeanException();
        Personality personality = validatePersonAccessForCreate(personIdentity, dto, multiException);
        validateCreateAccessForManagerRole(dto, personality);
    }


    public Long validateCreateAccessAndGetPersonId(PersonAttestationProfileAssignmentDTO dto) {
        PrsnInvalidBeanException multiException = new PrsnInvalidBeanException();

        ObjectRef person = dto.getPerson();
        validatePersonRef(person, multiException);
        PersonIdentityBean personIdentity = buildPersonIdentity(person, multiException);

        Personality personality = validatePersonAccessForCreate(personIdentity, dto, multiException);
        validateCreateAccessForManagerRole(dto, personality);
        return personality.getPersonId().toLong();
    }


    private void validateCreateAccessForManagerRole(PersonAttestationProfileAssignmentDTO dto, Personality personality) {
        if (Boolean.TRUE.equals(dto.getAssignToManagerRole())) {
            validateManagerRoleWriteFAP();
            if (!hasManagerLicense(personality)) {
                throw PrsnValidationException.noManagerLicenseOfPerson();
            }
        }
    }

    public boolean hasManagerLicense(Long personId) {
        PersonIdentityBean personIdentity = personIdentityBeanValidator.buildPersonIdentity(personId);
        Personality personality = personIdentityBeanValidator.getPersonality(personIdentity);
        return hasManagerLicense(personality);
    }

    private boolean hasManagerLicense(Personality personality){
        return Optional.ofNullable(personality)
                .map(Personality::getLicenseTypes)
                .map(PersonLicenseTypeSet::hasManagerLicense)
                .orElse(false);
    }


    public void validateManagerRoleReadFAP(Boolean isAssignToManagerRole) {
        if (Boolean.TRUE.equals(isAssignToManagerRole) && !AccessProfile.isPermitted(SecurityConstants.TIMEKEEPER_MANAGER_ROLE_VIEW, SecurityConstants.VIEW)) {
            throw PrsnValidationException.noPermissionToAccessManagerRoleForUser();
        }
    }

    public void validateManagerRoleWriteFAP() {
        if (!AccessProfile.isPermitted(SecurityConstants.TIMEKEEPER_MANAGER_ROLE_VIEW, SecurityConstants.ADD)) {
            throw PrsnValidationException.noPermissionToAccessManagerRoleForUser();
        }
    }

    public void validateCreateAccess(Map<Integer, APIException> excptionHolder, Map<Integer, PersonAttestationProfileAssignment> assignments) {
        if(!personIdentityBeanValidator.managerAllowedEditHimself()){
            restrictModificationOfOwnProfile(excptionHolder,assignments);
        }
    }

    public void validatePersonAttestationProfileAssignments(Map<Integer, APIException> exceptionHolder, Map<Integer, PersonAttestationProfileAssignment> assignments) {

        List<Integer> failedElements = new ArrayList<>();
        assignments.forEach((key, value) -> {
            validateAttestationProfileAssignments(value.getAttestationProfileAssignments(), exceptionHolder, key);
            validateManagerRoleAttestationProfileAssignments(value.getEmployee().getId(),
                    value.getManagerRoleAttestationProfileAssignments(), exceptionHolder, key);
            if (exceptionHolder.containsKey(key)) {
                failedElements.add(key);
            }
        });
        assignments.keySet().removeAll(failedElements);
    }

    private void validateManagerRoleAttestationProfileAssignments(Long personId, List<AttestationProfileAssignment> managerRoleAttestationProfileAssignments,
                                                                  Map<Integer, APIException> exceptionHolder, Integer key) {
        if (!CollectionUtils.isEmpty(managerRoleAttestationProfileAssignments)) {
            validateManagerRoleWriteFAP();
            if (!hasManagerLicense(personId)) {
                exceptionHolder.put(key, PrsnValidationException.noManagerLicenseOfPerson());
            } else {
                validateAttestationProfileAssignments(managerRoleAttestationProfileAssignments, exceptionHolder, key);
            }
        }
    }

    private void validateAttestationProfileAssignments(List<AttestationProfileAssignment> attestationProfileAssignments,
                                                       Map<Integer, APIException> exceptionHolder, Integer key) {

        if (CollectionUtils.isEmpty(attestationProfileAssignments)) {
            return;
        }
        PrsnInvalidBeanException multiException = new PrsnInvalidBeanException();
        for (int i = 0; i < attestationProfileAssignments.size(); i++) {
            if (attestationProfileAssignments.get(i) == null){
                throw new APIException(EMPTY_MULTI_UPDATE_REQUEST);
            }
            AttestationProfileAssignment assignment = attestationProfileAssignments.get(i);
            validateEffectiveDate(assignment.getEffectiveDate(), multiException);
            validateObjectRefIsNotNullReference(assignment.getProfile(), ObjectRefType.PROFILE, multiException);
            if (multiExceptionHasErrors(multiException)) {
                exceptionHolder.put(key, multiException.getApiException());
                break;
            }
        }
    }

    private boolean multiExceptionHasErrors(PrsnInvalidBeanException multiException) {
        return multiException.numberOfWrappedExceptions()>0;
    }

    private void validateObjectRefIsNotNullReference(ObjectRef profile, ObjectRefType profile1, PrsnInvalidBeanException multiException) {
        try {
            validateObjectRefIsNotNullReference(profile, profile1);
        }catch (APIException e){
            multiException.addApiExceptionToList(e);
        }
    }

    private void restrictModificationOfOwnProfile(Map<Integer, APIException> excptionHolder, Map<Integer, PersonAttestationProfileAssignment> assignments) {
        final Personality personality = CurrentUserAccountManager.getPersonality();
        List<Integer> failedEntries = new ArrayList<>();
        assignments.forEach((key, value) -> {
            try {
                throwErrorIfSameEmployee(value.getEmployee(), personality);
            } catch (APIException e) {
                excptionHolder.put(key, e);
                failedEntries.add(key);
            }
        });
        excptionHolder.keySet().removeAll(failedEntries);
    }

    private void throwErrorIfSameEmployee(ObjectRef profile, Personality personality) {
        if (profile.getQualifier().equals(personality.getPersonNumber())){
            throw new APIException(MANAGER_SELF_EDIT_NOT_ALLOWED);
        }
    }

    private void validatePersonRef(ObjectRef person, PrsnInvalidBeanException multiException) {
        try {
            validateObjectRef(person, ObjectRefType.PERSON);
        } catch (APIException e) {
            multiException.addApiExceptionToList(e);
        }
    }

    private PersonIdentityBean buildPersonIdentity(Long personId) {
        PersonIdentityBean personIdentity = new PersonIdentityBean();
        personIdentity.setPersonKey(personId);
        return personIdentity;
    }

    private PersonIdentityBean buildPersonIdentity(ObjectRef person,
                                                   PrsnInvalidBeanException multiException) {
        if (!hasExceptions(multiException)) {
            Long id = person.getId();
            if (id == null) { // id should take precedence over qualifier
                return personIdentityBeanValidator.createForPersonNumber(person.getQualifier());
            }
            return buildPersonIdentity(id);
        }
        return new PersonIdentityBean();
    }

    private Personality validatePersonAccessForCreate(PersonIdentityBean personIdentity,
                                                      PersonAttestationProfileAssignmentDTO dto,
                                                      PrsnInvalidBeanException multiException) {
        Personality personality = personIdentityBeanValidator.getPersonality(personIdentity);

        validateDTO(dto, multiException);

        if (hasExceptions(multiException)) {
            throw multiException.getApiException();
        }

        return personality;
    }

    private void validateDTO(PersonAttestationProfileAssignmentDTO dto,
                             PrsnInvalidBeanException multiException) {

        validateEffectiveDate(dto.getEffectiveDate(), multiException);
        validateProfile(dto.getProfile(), multiException);
    }

    private void validateEffectiveDate(LocalDate effectiveDate,
                                       PrsnInvalidBeanException multiException) {
        if (effectiveDate == null) {
            multiException.addApiExceptionToList(PrsnValidationException.missingProperty(EFFECTIVE_DATE));
        } else {
            validateEffectiveDateInValidRange(effectiveDate.toString(), multiException, effectiveDate);
        }
    }
    private void validateEffectiveDate(String effectiveDate,
                                       PrsnInvalidBeanException multiException) {
        if (StringUtils.isBlank(effectiveDate)) {
            multiException.addApiExceptionToList(PrsnValidationException.missingProperty(EFFECTIVE_DATE));
        }else {
            try {
                LocalDate effectiveLocalDate = LocalDate.parse(effectiveDate);
                validateEffectiveDateInValidRange(effectiveDate, multiException, effectiveLocalDate);
            } catch (Exception e) {
                multiException.addApiExceptionToList(PrsnValidationException
                        .invalidParameter(EFFECTIVE_DATE, effectiveDate));
            }
        }
    }

    private void validateEffectiveDateInValidRange(String effectiveDate, PrsnInvalidBeanException multiException, LocalDate effectiveLocalDate) {
        LocalDate foreverLocalDate = LocalDate.parse(FOREVER_DATE, DateTimeFormatter.ISO_LOCAL_DATE);
        if (effectiveLocalDate.isBefore(SOT_DATE) || effectiveLocalDate.isAfter(foreverLocalDate)){
            multiException.addApiExceptionToList(PrsnValidationException.invalidParameter(EFFECTIVE_DATE, effectiveDate));
        }
    }

    private void validateProfile(ObjectRef profile,
                                 PrsnInvalidBeanException multiException) {
        try {
            validateObjectRef(profile, ObjectRefType.PROFILE);
        } catch (APIException e) {
            multiException.addApiExceptionToList(e);
        }
    }

    private void validateObjectRef(ObjectRef objectRef, ObjectRefType refType) {
        validateObjectRefIsNotNullReference(objectRef, refType);

        Long id = objectRef.getId();
        String qualifier = objectRef.getQualifier();
        if (id == null) { // id should take precedence over qualifier
            validateObjectRefQualifier(qualifier, refType);
        } else {
            validateObjectRefId(id, refType);
        }
    }
    private void validateObjectRefIsNotNullReference(ObjectRef objectRef, ObjectRefType refType) {
        if (objectRef == null) {
            throwMissingRefException(refType);
        }

        if (objectRef.isNullReference()) {
            throwEmptyRefException(refType);
        }
        if (objectRef.getId() == null && StringUtils.isBlank(objectRef.getQualifier())) {
            throwEmptyRefException(refType);
        }
    }

    private void throwMissingRefException(ObjectRefType refType) {
        if (refType.equals(ObjectRefType.PERSON)) {
            throw PrsnValidationException.missingProperty(PERSON);
        } else if (refType.equals(ObjectRefType.PROFILE)) {
            throw PrsnValidationException.missingProperty(PROFILE);
        }
    }

    private void throwEmptyRefException(ObjectRefType refType) {
        if (refType.equals(ObjectRefType.PERSON)) {
            throw PrsnValidationException.nullProperty(PERSON);
        } else if (refType.equals(ObjectRefType.PROFILE)) {
            throw PrsnValidationException.nullProperty(PROFILE);
        }
    }

    private void throwEmptyQualifierException(ObjectRefType refType) {
        if (refType.equals(ObjectRefType.PERSON)) {
            throw PrsnValidationException.invalidPropertyValue(PERSON_NUMBER, StringUtils.EMPTY);
        } else if (refType.equals(ObjectRefType.PROFILE)) {
            throw PrsnValidationException.invalidPropertyValue(PROFILE_NAME, StringUtils.EMPTY);
        }
    }

    private void validateObjectRefId(Long id, ObjectRefType refType) {
        if (refType.equals(ObjectRefType.PROFILE)) {
            attestationProfileSetupService.getAttestationProfile(
                id); // profile service should throw exception otherwise
        }
    }

    private void validateObjectRefQualifier(String qualifier, ObjectRefType refType) {
        if (StringUtils.isEmpty(qualifier)) {
            throwEmptyQualifierException(refType);
        }

        if (refType.equals(ObjectRefType.PROFILE)) {
            attestationProfileSetupService.checkAttestationProfileExists(
                qualifier); // profile service should throw exception otherwise
        }
    }

    private boolean hasExceptions(PrsnInvalidBeanException multiException) {
        return multiException.numberOfWrappedExceptions() > 0;
    }

    /**
     * Sets attestationProfileSetupService. Initializes through setter in order to avoid possible circular references
     *
     * @param attestationProfileSetupService attestation profile setup service
     */
    @Inject
    public void setAttestationProfileSetupService(AttestationProfileSetupService attestationProfileSetupService) {
        this.attestationProfileSetupService = attestationProfileSetupService;
    }
}
