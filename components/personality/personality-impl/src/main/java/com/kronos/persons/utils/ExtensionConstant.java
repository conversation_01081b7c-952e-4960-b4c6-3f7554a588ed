/**
 *
 */
package com.kronos.persons.utils;

/**
 * <AUTHOR>
 *
 */
public interface ExtensionConstant {

   String EMPLOYEE_BASE_EXTENSION_URI_TOKEN = "baseemployee";
   String EMPLOYEE_EXTENSION_URI_TOKEN = "employee";
   String EMPLOYEE_REF_EXTENSION_URI_TOKEN = "employeeref";
   String DEVICE_EXTENSION_URI_TOKEN = "device";
   String ACCRUAL_EXTENSION_URI_TOKEN = "accrual";
   String TIMEKEEPING_EXTENSION_URI_TOKEN = "timekeeping";
   String SCHEDULING_EXTENSION_URI_TOKEN = "scheduling";

   String LOAD_ACTION = "Load";

   String QUERY_PARAM_SEARCH_BY = "searchBy";

   String QUERY_PARAM_SEARCH_VALUE = "searchValue";

   String QUERY_PARAM_SNAPSHOT_DATE = "snapShotDate";

   String DATE_FORMAT_DDMMYYYY = "MM/dd/yyyy";

   String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

   String ERROR = "Error:";

   String EFFECTIVE_DATE = "effectiveDate";
   String EXPIRATION_DATE = "expirationDate";

   String AOID_SEARCH_BY_VALUE = "externalidentifier";
   String MULTI_DELETE = "multi_delete";
   String SEARCH_BY_VALUE_PERSON_ID = "personid";

   String AOID_SEARCH_KEY = "aoid";

   String COID_SEARCH_KEY = "coid";

   //Allowed supported keys for multi read api
   String PERSON_NUMBER = "personnumber";
   String PERSON_ID = "personid";
   String JOBASSIGNMENT_ID = "jobassignmentid";
   String USER_NAME = "username";
   String USER_ACCOUNT_ID = "useraccountid";
   String BADGE_NUMBER = "badgenumber";
   String USER_EMAIL_ADDRESS = "useremailaddress";

   String PERSON_IDENITY = "personIdentity";
   String HYPER_FIND_QUERY_NAME = "hyperFindQueryName";
   String HYPER_FIND = "hyperfind";
   String VISIBILITY_CODE = "visibilityCode";
   String START_DATE = "startDate";
   String END_DATE = "endDate";
   String DATE_RAGE = "dateRange";
   String QUALIFIER = "qualifier";
   String ID = "id";

   String MULTIPLE_POSITIONS_FEATURE = "pe.multiplepositions";
   String DEFAULT_NONE = "< None >";
   String NONE = "com.kronos.persons.rest.beans.validator.NONE";
   long NONE_ID = -1L;

   String POSITION_ID = "positionId";
   String POSITION_EXTERNAL_ID = "positionExternalId";
   String POSITION_NAME = "positionName";
   String ACCRUAL_BY_POSITIONS_FEATURE = "Accrual By Positions";
}
