package com.kronos.persons.rest.assignments.service;

import com.google.common.collect.Lists;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonNotification;
import com.kronos.wfc.platform.entity.business.AbstractEntity;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.persistence.framework.SQLStatement;
import com.kronos.wfc.platform.persistence.framework.Transaction;
import com.kronos.wfc.platform.persistence.framework.statement.SqlString;
import com.kronos.wfc.timekeeping.cascade.business.config.CascadeProfile;
import com.kronos.wfc.timekeeping.cascade.business.people.PersonCascadeProfileAssignment;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Transaction service for bulk update all personCascadeProfileAssignments with one {@link CascadeProfile}.
 * Copyright (C) 2019 Kronos.com
 * Date: Jul 02, 2019
 *
 * <AUTHOR>
 */
public final class PersonCascadeProfileBulkUpdateTransactionService extends Transaction {
    private static final String PERSON_CASCADE_PROFILE_ASSIGNMENT_BULK_UPDATE =
            "com.kronos.wfc.timekeeping.cascade.business.people.PersonCascadeProfileAssignment.bulkUpdate";
    private CascadeProfile cascadeProfile;
    private List<PersonCascadeProfileAssignment> personCascadeProfileAssignments;
    private boolean notifyPersoncache = true;

    private PersonCascadeProfileBulkUpdateTransactionService(CascadeProfile cascadeProfile,
                                                 List<PersonCascadeProfileAssignment> personCascadeProfileAssignments, boolean notifyPersoncache) {
        this.cascadeProfile = cascadeProfile;
        this.personCascadeProfileAssignments = personCascadeProfileAssignments;
        this.notifyPersoncache = notifyPersoncache;
    }

    /**
     * Gets new PersonCascadeProfileBulkUpdateTransactionService.
     *
     * @param cascadeProfile                  the cascade profile wat need to save for list
     *                                        of {@link PersonCascadeProfileAssignment}
     * @param personCascadeProfileAssignments list of {@link PersonCascadeProfileAssignment}
     * @return transaction service for update
     */
    public static PersonCascadeProfileBulkUpdateTransactionService getTransactionService(CascadeProfile cascadeProfile,
                                                 List<PersonCascadeProfileAssignment> personCascadeProfileAssignments) {
        return new PersonCascadeProfileBulkUpdateTransactionService(cascadeProfile, personCascadeProfileAssignments,true);
    }

    public static PersonCascadeProfileBulkUpdateTransactionService getTransactionService(CascadeProfile cascadeProfile,
                                                                                         List<PersonCascadeProfileAssignment> personCascadeProfileAssignments, boolean notifyPersoncache) {
        return new PersonCascadeProfileBulkUpdateTransactionService(cascadeProfile, personCascadeProfileAssignments, notifyPersoncache);
    }

    /**
     * Update cascade profile for all personCascadeProfileAssignments by bulk update.
     */
    @Override
    protected void transaction() {
        List<String> personIds = personCascadeProfileAssignments.stream()
                .map(personCascadeProfileAssignment -> personCascadeProfileAssignment.getId().toString())
                .collect(Collectors.toList());

        if(notifyPersoncache) {
               personIds.stream().forEach(personId -> PersonNotification.sendUpdate(new ObjectIdLong(Long.parseLong(personId))));
        }
        SqlString commaSeparatedPersonIds = new SqlString(StringUtils.join(personIds, ","));
        SqlString profileId = new SqlString(cascadeProfile.getId().toString());
        SQLStatement sqlStatement = new SQLStatement(SQLStatement.UPDATE, PERSON_CASCADE_PROFILE_ASSIGNMENT_BULK_UPDATE,
                Lists.newArrayList(profileId, commaSeparatedPersonIds));
        sqlStatement.execute();
    }
}
