package com.kronos.persons.deleteretry.daemon;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.apache.commons.lang3.math.NumberUtils;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.people.personality.Operation;
import com.kronos.persons.deleteretry.model.PersonDeleteRetryEntity;
import com.kronos.persons.deleteretry.service.PersonDeleteRetryDataAccessService;
import com.kronos.tenantprovider.api.TenantProvider;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonalityToDelete;
import com.kronos.wfc.platform.logging.framework.Log;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.business.state.State;
import com.kronos.wfc.platform.tenant.business.TenantCache;

@Named("DeletePersonAgent")
public class DeletePersonAgent implements Operation {

	@Inject
	IKProperties kronosProperties;

	@Inject
	TenantProvider tenantProvider;

	@Inject
	PersonDeleteRetryDataAccessService personDeleteRetryDataAccessService;

	protected static final String THRESHOLD = "3";
	protected static final String TIME_DIFF_MIN = "20";
	protected static final String TIME_TO_DELETE_MONTHS = "2";

	protected void performDelete() {
		Integer threshold = Integer
				.parseInt(getKronosProperties().getProperty("site.daemon.persondelete.threshold", THRESHOLD));
		Long timeDifferenceMin = Long.parseLong(
				getKronosProperties().getProperty("site.daemon.persondelete.timeDifferenceMin", TIME_DIFF_MIN));
		Long timeToDeleteInMonths = Long.parseLong(getKronosProperties()
				.getProperty("site.daemon.persondelete.timeToDeleteInMonths", TIME_TO_DELETE_MONTHS));

		/**
		 * get records from personDeeleteRetry which were added 20 minutes
		 * before and still exits in person table.
		 */
		Timestamp timediff = Timestamp.valueOf(LocalDateTime.now(ZoneOffset.UTC).minusMinutes(timeDifferenceMin));
		List<PersonDeleteRetryEntity> personDeleteRetryList = personDeleteRetryDataAccessService
				.getPersonIdsToDelete(threshold, timediff);
		/**
		 * Now update the records in personDeleteRetry table with
		 * retrycount=retryCount + 1 and Lastupdatedtm= currentUTCTime
		 */
		if (Objects.nonNull(personDeleteRetryList)) {
			for (PersonDeleteRetryEntity personDeleteRetry : personDeleteRetryList) {
				int isPersonUpdated = personDeleteRetryDataAccessService.updatePersonRetryCount(
						personDeleteRetry.getPersonId(), personDeleteRetry.getRetryCount(),
						Timestamp.valueOf(LocalDateTime.now(ZoneOffset.UTC)));
				Boolean success = deletePersonStep(isPersonUpdated, personDeleteRetry);
				if (success) {
					personDeleteRetry.setIsPersonDeleted(NumberUtils.INTEGER_ONE);
					personDeleteRetry.setRetryCount(personDeleteRetry.getRetryCount() + NumberUtils.INTEGER_ONE);
					personDeleteRetryDataAccessService.save(personDeleteRetry);
				}
			}
		}

		/**
		 * Delete from retry table the records which which were picked from
		 * Person because deletion started on them and they were marked as
		 * isDeleted and later on they got deleted.
		 */
		personDeleteRetryDataAccessService.removeDeletedPerson();
		// remove records which are older than the supplied value
		personDeleteRetryDataAccessService.removeOldRecords(
				Timestamp.valueOf(LocalDateTime.now(ZoneOffset.UTC).minusMonths(timeToDeleteInMonths)));
	}

	/**
	 * This method provides a main loop to Iterate through each tenant.
	 */
	@Override
	public void execute() {
		try {
			try {
				for (Long tenant : getAllTenantIds()) {
					tenantOperation(tenant, Boolean.TRUE);
					performDelete();
				}
			} finally {
				State.destroy();
				tenantOperation(NumberUtils.LONG_ZERO, Boolean.FALSE);
			}
		} catch (Exception e) {
			Log.log(Log.ERROR, "Exception in DeletePersonAgent", e);
		}

	}

	protected Boolean deletePersonStep(int isPersonUpdated, PersonDeleteRetryEntity personDeleteRetry) {
		BigInteger personidToDelete = personDeleteRetry.getPersonId();
		if (isPersonUpdated == 1 && personidToDelete.signum() == 1) {
			String personDeleteInitiator = personDeleteRetry.getDeletedBy();
			State.initializeState("daemon.personDelete", "daemon.personDelete", personDeleteInitiator);
			PersonalityToDelete personalityToDelete = new PersonalityToDelete(
					new ObjectIdLong(personidToDelete.longValue()));
			return personalityToDelete.delete();
		}
		return Boolean.FALSE;
	}

	protected Set<Long> getAllTenantIds() {
		return TenantCache.getCache().getAllTenantIds();
	}

	protected void tenantOperation(Long tenantId, Boolean flagToSetOrRemove) {
		if (flagToSetOrRemove) {
			tenantProvider.setTenantId(TenantCache.getCache().getExternalTenantIdfromTenantId(tenantId));
		}
		else {
			tenantProvider.removeTenantId();
		}

	}

	protected IKProperties getKronosProperties() {
		return kronosProperties;
	}

	protected void setKronosProperties(IKProperties kronosProperties) {
		this.kronosProperties = kronosProperties;
	}
}