package com.kronos.persons.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.people.personality.model.Criteria;
import com.kronos.people.personality.model.IdentifierType;
import com.kronos.people.personality.util.OptionalPersonId;
import com.kronos.persons.photo.entity.EmpPhoto;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.PhotoBean;
import com.kronos.persons.rest.beans.PhotoInfoBean;
import com.kronos.persons.rest.exception.PrsnPersistenceException;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.persons.rest.model.PhotoCriteria;
import com.kronos.persons.rest.service.EmployeePhotoResultResolver;
import com.kronos.persons.rest.service.PersonPhotoServiceConstants;
import com.kronos.wfc.commonapp.people.business.commonobjectid.CommonObjectId;
import com.kronos.wfc.commonapp.people.business.commonobjectid.CommonObjectIdService;
import com.kronos.wfc.datacollection.empphoto.business.EmpPhotoBusinessValidationException;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.properties.framework.KronosProperties;


/**
 * Employee Photo Utility Class based on search criteria provided 
 * (single key or multi-key flow).
 * Single-Key represents search type are of either personId or personNumber
 * Multi-Key represents search type are based on aoid/coid pair.
 *
 */
public class PersonPhotoUtils {

	private PersonPhotoUtils() {

	}

	private static final KLogger LOGGER = KLoggerFactory.getKLogger(PersonPhotoUtils.class);

	/**
	 * Retrieves the List object by {@code idList}
	 * 
	 * @param idList
	 *
	 * @return an object of List containing person number. Null will be returned
	 *         if not found.
	 */
	public static List<CommonObjectId> getPersonCommonIdMapByAoidCoid(List<List<String>> idList) {
		CommonObjectIdService commonObjectIdService = new CommonObjectIdService();
		return commonObjectIdService.retrieveByAssociateAndClientIds(idList);
	}

	public static void populateErrorMap(List<String> listAoidReq, Map<String, String> aoidCoidMap,
			Map<Object, PhotoBean> aoidErrorMap, PhotoCriteria photoCriteria) {
		LOGGER.debug("PersonPhotoUtils::populateErrorMap() Populate Error Map based on AOID/COID identifiers for multi-key: Start");
		boolean coidRequired = KronosProperties.getPropertyAsBoolean(CommonObjectId.COID_REQUIRED_KEY, false);
		LOGGER.info("PersonPhotoServiceImpl::multiKeyFlow() : COID is required :: {}", coidRequired);
		for (String aoid : listAoidReq) {
			APIException ex = null;
			if (!coidRequired) {
				LOGGER.error("PersonPhotoUtils::populateErrorMap() : Invalid property value found for AOID identifeer");
				ex = PrsnValidationException.invalidPropertyValue(ExtensionConstant.AOID_SEARCH_KEY, aoid);
			} else {
				LOGGER.error("PersonPhotoUtils::populateErrorMap() : Invalid property value found for AOID/COID pair identifeer");
				ex = PrsnValidationException.invalidPropertyValueCombination(ExtensionConstant.AOID_SEARCH_KEY, aoid,
						ExtensionConstant.COID_SEARCH_KEY,
						aoidCoidMap.get(aoid));
			}
			PhotoBean bean = new PhotoBean();
			EmployeePhotoResultResolver photoResolver = new EmployeePhotoResultResolver();
            List<String> aoidCoidValue = new ArrayList<>();
            List<List<String>> aoidCoidList = new ArrayList<>();
            List<String> multiKeyList = photoCriteria.getMultiKey();
            if(!multiKeyList.isEmpty() && multiKeyList.get(0).equalsIgnoreCase(ExtensionConstant.AOID_SEARCH_KEY)) {
            	aoidCoidValue.add(aoid);
            	aoidCoidValue.add(aoidCoidMap.get(aoid));
            } else {
            	aoidCoidValue.add(aoidCoidMap.get(aoid));
            	aoidCoidValue.add(aoid);
            }
            aoidCoidList.add(aoidCoidValue);
            ex.setInputDetail(photoResolver.getSearchCriteriaForMultiKey(multiKeyList,aoidCoidList));
            bean.setError(ex);
			aoidErrorMap.put(aoid, bean);
		}
		LOGGER.debug("PersonPhotoUtils::populateErrorMap() : End");
	}

	public static List<PhotoBean> getResponseList(Map<Object, PhotoBean> responseMap) {
		List<PhotoBean> repsonseList = new ArrayList<>();
		responseMap.forEach((key, value) -> repsonseList.add(value));
		return repsonseList;
	}

	/**
	 * Populates the AOID/COID list to get the persons associated for multi-key criteria.
	 * 
	 * @param criteriaBean
	 * @param listAoidReq
	 * @param aoidCoidMap
	 * @return
	 */
	public static List<List<String>> createAoidCoidList(PhotoCriteria criteriaBean, List<String> listAoidReq,
			Map<String, String> aoidCoidMap) {
		return criteriaBean.getMultiKeyValues().stream().map(searchValues -> {
			List<String> aoidCoidList = new ArrayList<>();
			aoidCoidList.add(searchValues.getAoid());
			aoidCoidList.add(searchValues.getCoid());
			listAoidReq.add(searchValues.getAoid());
			aoidCoidMap.put(searchValues.getAoid(), searchValues.getCoid());
			return aoidCoidList;
		}).collect(Collectors.toList());
	}

	public static Criteria createCriteria(Collection<Long> personIds) {
		Criteria criteria = new Criteria();
		criteria.setIdsType(IdentifierType.PERSONID);
		criteria.setIds(personIds.toArray(new Long[0]));
		return criteria;
	}


	/**
	 * Encode images to base64 string from byte array.
	 *
	 * @param byteArray - byte array of image to encode
	 * @return String - base64 encoding of byteArray
	 */
	public static String getEncodedImage(byte[] byteArray){
		LOGGER.debug("PersonPhotoUtils::getEncodedImage():: Trying to parse the image from  byteArray to base64 encoded string : Start");
		if (byteArray == null) {
			return null;
		}
		String strBase64Image;
		try {
			// base64 encode the image content
			strBase64Image =  Base64.getEncoder().encodeToString(byteArray);
		}
		catch (Exception e) {
			LOGGER.error("getEncodedImage:: INVALID_IMAGE :{} ", e.getLocalizedMessage());
			throw new EmpPhotoBusinessValidationException(EmpPhotoBusinessValidationException.INVALID_IMAGE,e);
		}
		if (strBase64Image == null || strBase64Image.isEmpty()) {
			LOGGER.error("getEncodedImage() :: INVALID_IMAGE ");
			throw new EmpPhotoBusinessValidationException(EmpPhotoBusinessValidationException.INVALID_IMAGE);
		}
		LOGGER.debug("PersonPhotoUtils::getEncodedImage():: Image encoded successfully : End");
		return strBase64Image;
	}

	/**
	 * Populates the employee bean object into response bean for single key criteria
	 * @param personIds
	 * @param photoBeanObj
	 * @return
	 */
	public static Map<Object, PhotoBean> populateSingleKeyListResponse(List<Long> personIds, List<EmpPhoto> photoBeanObj) {
		LOGGER.debug("PersonPhotoUtils::populateSingleKeyListResponse():: Start");
		Map<Object, PhotoBean> photoBeanMap = new LinkedHashMap<>();
		Map<Long, EmpPhoto> photoBeanhelper = constructPhotoBeanMap(photoBeanObj);
		personIds.stream().forEach(photoObj->{
			PhotoBean pb=new PhotoBean();
			PersonIdentityBean idBean = new PersonIdentityBean();
			EmpPhoto empPhoto = photoBeanhelper.get(photoObj);
			idBean.setPersonKey(photoObj);
			pb.setPhoto(populateEmpPhotoInfoBean(empPhoto));
			pb.setPersonIdentity(idBean);
			photoBeanMap.put(photoObj,pb);
		});
		LOGGER.debug("PersonPhotoUtils::populateSingleKeyListResponse():: End");
		return photoBeanMap;
	}

	/**
	 * Populates the employee bean object into response bean for multi key criteria
	 * @param commonIdReqList
	 * @param photoBeanObj
	 * @return
	 */
	public static Map<Object, PhotoBean> populateMultiKeyListResponse(List<CommonObjectId> commonIdReqList, List<EmpPhoto> photoBeanObj ) {
		LOGGER.debug("PersonPhotoUtils::populateMultiKeyListResponse():: Start");
		Map<Object,PhotoBean> photoBeanMap = new LinkedHashMap<>();
		Map<ObjectIdLong,CommonObjectId> commonIdMap =
				commonIdReqList.stream()
				.collect(Collectors.toMap(CommonObjectId::getPersonId,
						Function.identity()));
		Map<Long, EmpPhoto> photoBeanhelper = constructPhotoBeanMap(photoBeanObj);
		commonIdMap.entrySet().forEach(entry -> {
			Long personId =  entry.getKey().longValue();
			CommonObjectId commonIdReq= entry.getValue();
			EmpPhoto photoObj = photoBeanhelper.get(personId);
			PhotoBean photoBean = new PhotoBean();
			PersonIdentityBean personIdentity = new PersonIdentityBean();
			personIdentity.setAoid(commonIdReq.getAssociateIdText());
			personIdentity.setCoid(commonIdReq.getCommonIdText());
			personIdentity.setPersonNumber(commonIdReq.getPersonNumber());
			personIdentity.setPersonKey(commonIdReq.getPersonId().toLong());
			photoBean.setPersonIdentity(personIdentity);
			photoBean.setPhoto(populateEmpPhotoInfoBean(photoObj));
			photoBeanMap.put(commonIdReq.getPersonId().toLong(),photoBean);
		});
		LOGGER.debug("PersonPhotoUtils::populateMultiKeyListResponse():: End");
		return photoBeanMap;
	}

	private static Map<Long, EmpPhoto> constructPhotoBeanMap(List<EmpPhoto> photoBeanObj) {
		return photoBeanObj.stream()
				.collect(Collectors.toMap(EmpPhoto::getPersonid,
						Function.identity()));
	}

	/**
	 * Construct response photo object from employee photo bean object.
	 * @param photoObj
	 * @return
	 */
	private static PhotoInfoBean populateEmpPhotoInfoBean(EmpPhoto photoObj) {
		PhotoInfoBean infobean = null;
		if(Objects.nonNull(photoObj) && Objects.nonNull(photoObj.getPersonid())) {
			infobean = new PhotoInfoBean();					
			if(Objects.nonNull(photoObj.getImageContent()) && photoObj.getImageContent().length>0) {
				String encodePhotoData = PersonPhotoUtils.getEncodedImage(photoObj.getImageContent());
				infobean.setImage(encodePhotoData);
				infobean.setImageType(PersonPhotoServiceConstants.PHOTO_IMAGE_FORMAT_TYPE);	
			}
			infobean.setLastUpdateDateTime(photoObj.getUpdatedtm());
		}
		return infobean;
	}
	
	/**
	 * Parsing the response object for partial success, full success or full failure scenarios for single-key criteria
	 * @param photoBeanResponse
	 * @param searchBy
	 * @param personIdCollection
	 * @return
	 */
	public static List<PhotoBean> getSingleKeyResponseData(Map<Object, PhotoBean> photoBeanResponse,String searchBy, Map<Object, OptionalPersonId> personIdCollection) {
		LOGGER.debug("PersonPhotoUtils::getSingleKeyResponseData():: Parsing the response for partial success, full success or full failure scenarios --  Start"); 
		List<PhotoBean> photoBeanResponseList = new ArrayList<>();	
		 personIdCollection.forEach((k,v) -> 
		 {
			 PhotoBean reponseBean = null;
			 if(!photoBeanResponse.isEmpty() && photoBeanResponse.get(v.getPersonId()) != null) {
				 reponseBean= photoBeanResponse.get(v.getPersonId());
				 if(PersonPhotoServiceConstants.PERSON_NUM_IDENTIFIER.equalsIgnoreCase(searchBy)) {
					 PersonIdentityBean identity = reponseBean.getPersonIdentity();
					 identity.setPersonNumber(k.toString().toUpperCase());
					 reponseBean.setPersonIdentity(identity);
				 }
			 } else {
				 LOGGER.error("PersonPhotoUtils::getSingleKeyResponseData():: Person Not Found Error for Single-Key Employee Photo Criteria");
				 EmployeePhotoResultResolver photoResolver = new EmployeePhotoResultResolver();
				 reponseBean = new PhotoBean();
				 reponseBean.setError(PrsnPersistenceException.personNotFoundForEmpPhoto(searchBy, k.toString()));
				 APIException apiEx = reponseBean.getError();
				 apiEx.setInputDetail(photoResolver.getSearchCriteriaForSingleKey(searchBy,Arrays.asList(k.toString())));
				 reponseBean.setError(apiEx);
			 }
			 photoBeanResponseList.add(reponseBean);
		 });
		return photoBeanResponseList;
		 
	 }
	
	/**
	 * Parsing the response object for partial success, full success or full failure scenarios for multi-key criteria
	 * @param photoCriteria
	 * @param aoidCoidMap 
	 * @param aoidPersonNumMap
	 * @param photoBeanResponse
	 * @param aoidErrorMap
	 * @return
	 */
	public static List<PhotoBean> getMultiKeyResponseData(PhotoCriteria photoCriteria, Map<String, Long> aoidPersonNumMap, Map<Object, PhotoBean> photoBeanResponse,Map<Object, PhotoBean> aoidErrorMap) {
		LOGGER.debug("PersonPhotoUtils::getMultiKeyResponseData():: Parsing the response for partial success, full success or full failure scenarios --  Start"); 
		return photoCriteria.getMultiKeyValues().stream().map(searchValue -> {
			String aoid = searchValue.getAoid();
            Optional<Long> personNumOpt = Optional.ofNullable(aoidPersonNumMap.get(aoid));
            PhotoBean bean = null;
            if (personNumOpt.isPresent()) {
            	bean = photoBeanResponse.get(personNumOpt.get());
                if (bean.getError() != null) {
                	LOGGER.error("PersonPhotoUtils::getMultiKeyResponseData():: Person Not Found Error for Multi-Key Employee Photo Criteria"); 
                	EmployeePhotoResultResolver photoResolver = new EmployeePhotoResultResolver();
                	bean.setError(PrsnPersistenceException.personNotFoundForEmpPhoto(ExtensionConstant.AOID_SEARCH_KEY, aoid));
                    APIException apiEx = bean.getError();
                    List<String> aoidCoidValue = new ArrayList<>();
                    List<List<String>> aoidCoidList = new ArrayList<>();
                    List<String> multiKeyList = photoCriteria.getMultiKey();
                    if(multiKeyList.get(0).equalsIgnoreCase(ExtensionConstant.AOID_SEARCH_KEY)) {
                    	aoidCoidValue.add(aoid);
                    	aoidCoidValue.add(searchValue.getCoid());
                    } else {
                    	aoidCoidValue.add(searchValue.getCoid());
                    	aoidCoidValue.add(aoid);
                    }
                    aoidCoidList.add(aoidCoidValue);
                    apiEx.setInputDetail(photoResolver.getSearchCriteriaForMultiKey(multiKeyList,aoidCoidList));
                    bean.setError(apiEx);
                }
            } else {
                bean = aoidErrorMap.get(aoid);
            }
            return bean;
		}).collect(Collectors.toList());
	}
	
	
}
