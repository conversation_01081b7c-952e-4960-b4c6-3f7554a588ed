package com.kronos.persons.cacheretry.repository;

import com.kronos.persons.cacheretry.model.FailedPersonCacheUpdateEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface FailedPersonCacheUpdateRepository extends JpaRepository<FailedPersonCacheUpdateEntity, Long> {

    FailedPersonCacheUpdateEntity findByPersonIdAndEventType(Long personId, String eventType);

    void deleteByIdIn(List<Long> ids);

    boolean existsByPersonIdAndEventType(Long personId, String eventType);
}
