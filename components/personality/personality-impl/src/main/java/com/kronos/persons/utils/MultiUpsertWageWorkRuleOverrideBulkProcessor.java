package com.kronos.persons.utils;

import com.kronos.persons.rest.model.EmployeeWageWorkRulesDTO;
import com.kronos.persons.rest.model.wageoverride.EmployeeWageWorkRulesWrapper;
import java.util.List;
import java.util.function.Function;

/**
 * The processor for generation response for the "v1/commons/persons/wage_work_rules/multi_upsert" API.
 * Copyright (C) 2020 Kronos.com
 * Date: June 4, 2020
 *
 * <AUTHOR>
 */
public class MultiUpsertWageWorkRuleOverrideBulkProcessor extends WageWorkRuleOverrideBulkProcessor {

    private final List<EmployeeWageWorkRulesDTO> employeeWageWorkRules;
    private final Function<List<EmployeeWageWorkRulesDTO>, List<EmployeeWageWorkRulesWrapper>> processor;

    /**
     * Constructor.
     *
     * @param employeeWageWorkRules list of {@link EmployeeWageWorkRulesDTO}
     * @param processor             processor to process employee wage work rules
     */
    public MultiUpsertWageWorkRuleOverrideBulkProcessor(List<EmployeeWageWorkRulesDTO> employeeWageWorkRules,
                                                        Function<List<EmployeeWageWorkRulesDTO>, List<EmployeeWageWorkRulesWrapper>> processor) {
        this.employeeWageWorkRules = employeeWageWorkRules;
        this.processor = processor;
    }

    @Override
    protected int getResponseSize(List<EmployeeWageWorkRulesWrapper> resultList) {
        return employeeWageWorkRules.size();
    }

    @Override
    protected List<EmployeeWageWorkRulesWrapper> applyProcessor() {
        return processor.apply(employeeWageWorkRules);
    }

    @Override
    protected void setInputDetail(EmployeeWageWorkRulesWrapper wrapper) {
        wrapper.getApiException().setInputDetail(wrapper.getDTO());
    }

    @Override
    protected EmployeeWageWorkRulesDTO getDtoForResponse(EmployeeWageWorkRulesWrapper wrapper) {
        EmployeeWageWorkRulesDTO dtoForResponse = wrapper.getDTO();
        dtoForResponse.setEffectiveDatedWageWorkRules(null);
        return dtoForResponse;
    }
}
