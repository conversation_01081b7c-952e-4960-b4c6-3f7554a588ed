package com.kronos.persons.rest.assignments.service;

import jakarta.inject.Named;

import com.kronos.wfc.commonapp.people.business.person.AccessAssignment;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.processmanager.business.workflow.WorkflowAccessAssignment;
/**
 * 
 * <AUTHOR>
 *
 */
@Named
public class PersonProcessProfileAssignmentServiceHelper  {

	
	/**
	 * Gets the workflow access assigment freshly loaded from the database for the identity in this bean.  
	 */
	public WorkflowAccessAssignment getWorkflowAccessAssignment(Personality personality) {
		AccessAssignment accessAssignment = personality.getAccessAssignment();
		
		WorkflowAccessAssignment returnValue = new WorkflowAccessAssignment(accessAssignment);
		returnValue.refresh();

		return returnValue;
	}

}
