package com.kronos.persons.photo.service;

import java.time.LocalDateTime;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.springframework.stereotype.Service;

import com.kronos.persons.photo.entity.EmpPhoto;
import com.kronos.persons.photo.repository.PersonPhotoImageRepository;

/**
 * Data Access Layer for Employee Photo Objects. 
 */
@Service
public class PersonPhotoDataAccessService implements IPersonPhotoDataAccessService{

	@Inject
	PersonPhotoImageRepository empPhotoRepository;

	public List<EmpPhoto> findByPersonIdList(List<Long> personIds) {
		return  empPhotoRepository.findAllById(personIds);
	}

	@Override
	@Transactional
	public List<EmpPhoto> getEmpPhotoListByPersonIdsAndUpdateDtm(List<Long> personIds,
			LocalDateTime modifiedSinceDateTime) {
		return empPhotoRepository.getEmpPhotoListByPersonIdsAndUpdateDtm(personIds, modifiedSinceDateTime);
	}
}