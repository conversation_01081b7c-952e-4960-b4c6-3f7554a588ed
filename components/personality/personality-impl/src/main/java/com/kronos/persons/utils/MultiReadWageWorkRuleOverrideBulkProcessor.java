package com.kronos.persons.utils;

import com.kronos.persons.rest.model.EmployeeCriteria;
import com.kronos.persons.rest.model.EmployeeWageWorkRulesDTO;
import com.kronos.persons.rest.model.WageWorkRuleMultiReadWhere;
import com.kronos.persons.rest.model.wageoverride.EmployeeWageWorkRulesWrapper;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * The processor for generation response for the "v1/commons/persons/wage_work_rules/multi_read" API.
 * Copyright (C) 2020 Kronos.com
 * Date: May 25, 2020
 *
 * <AUTHOR>
 */
public class MultiReadWageWorkRuleOverrideBulkProcessor extends WageWorkRuleOverrideBulkProcessor {

    private final WageWorkRuleMultiReadWhere where;
    private final Function<WageWorkRuleMultiReadWhere, List<EmployeeWageWorkRulesWrapper>> processor;

    /**
     * Constructor.
     *
     * @param where     hold request information for multi read of wage and work rules
     * @param processor processor to process employee wage work rules
     */
    public MultiReadWageWorkRuleOverrideBulkProcessor(WageWorkRuleMultiReadWhere where,
                                                      Function<WageWorkRuleMultiReadWhere, List<EmployeeWageWorkRulesWrapper>> processor) {
        this.where = where;
        this.processor = processor;
    }

    @Override
    protected int getResponseSize(List<EmployeeWageWorkRulesWrapper> resultList) {
        return resultList.size();
    }

    @Override
    protected List<EmployeeWageWorkRulesWrapper> applyProcessor() {
        return processor.apply(where);
    }

    @Override
    protected void setInputDetail(EmployeeWageWorkRulesWrapper wrapper) {
        if (wrapper.getErrorInput() != null) {
            WageWorkRuleMultiReadWhere temp = copyWhere();
            if (wrapper.getMultiKey()) {
                List<List<String>> multiKeyValues = new ArrayList<>();
                List<String> personPositionInfo = (List<String>) wrapper.getErrorInput();
                multiKeyValues.add(personPositionInfo);
                temp.getEmployees().setMultiKeyValues(multiKeyValues);
            } else {
                List<String> values = new ArrayList<>();
                String personInfo = (String) wrapper.getErrorInput();
                values.add(personInfo);
                temp.getEmployees().setValues(values);
            }
            wrapper.getApiException().setInputDetail(temp);
        } else {
            wrapper.getApiException().setInputDetail(where);
        }
    }

    @Override
    protected EmployeeWageWorkRulesDTO getDtoForResponse(EmployeeWageWorkRulesWrapper wrapper) {
        return wrapper.getDTO();
    }


    private WageWorkRuleMultiReadWhere copyWhere() {
        WageWorkRuleMultiReadWhere newWhere = new WageWorkRuleMultiReadWhere();
        EmployeeCriteria employees = new EmployeeCriteria();
        if (where.getEmployees().getKey()!=null) {
            employees.setKey(where.getEmployees().getKey());
            employees.setValues(new ArrayList<>());
        } else {
            employees.setMultiKey(where.getEmployees().getMultiKey());
            employees.setMultiKeyValues(new ArrayList<>());
        }
        newWhere.setEmployees(employees);
        newWhere.setStartDate(where.getStartDate());
        newWhere.setEndDate(where.getEndDate());
        newWhere.setOverridesOnly(where.isOverridesOnly());
        newWhere.setRawWage(where.isRawWage());
        return newWhere;
    }

    public List<EmployeeWageWorkRulesWrapper> processWithErrors() {
        List<EmployeeWageWorkRulesWrapper> responseList = applyProcessor();
        return responseList;
    }
}
