package com.kronos.persons.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.apache.commons.lang3.StringUtils;

import com.kronos.container.api.access.SpringContext;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.people.personality.model.Criteria;
import com.kronos.people.personality.model.PersonalityResponse;
import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.service.PersonalityService;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.persons.rest.extensions.service.CriteriaValidator;
import com.kronos.persons.rest.model.EmployeeCriteria;
import com.kronos.persons.rest.model.ExtensionCriteria;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.rest.model.ExtensionWhereCriteria;
import com.kronos.persons.rest.model.PersonQualifierBean;
import com.kronos.persons.rest.model.PersonQualifierDetailBean;
import com.kronos.persons.rest.model.SearchValues;
import static com.kronos.persons.utils.ExtensionUtils.getIdentifierType;
import com.kronos.people.personality.model.IdentifierType;
import java.lang.reflect.Constructor;

/**
 * <AUTHOR>
 */
@Named
public class CriteriaHelper {

    @Inject
    CriteriaValidator criteriaValidator;

    @Inject
    PersonIdentityBeanValidator personIdentityBeanValidator;

    private static final KLogger LOGGER = KLoggerFactory.getKLogger(CriteriaHelper.class);

    public ExtensionCriteria getExtensionCriteria(ExtensionSearchCriteria searchCriteria) {
        ExtensionCriteria extensionCriteria = new ExtensionCriteria();
        boolean isMultiKey = false;
        boolean isSingleKey = false;
        if (searchCriteria != null && searchCriteria.getWhere() != null) {
            ExtensionWhereCriteria whereCriteria = searchCriteria.getWhere();
            extensionCriteria.setExtensionType(whereCriteria.getExtensionType());
            extensionCriteria.setOnlyActivePerson(whereCriteria.getOnlyActivePerson());
            extensionCriteria.setIncludeBaseWages(whereCriteria.getIncludeBaseWages());
            extensionCriteria.setHyperFindFilter(whereCriteria.getHyperFindFilter());
            if (whereCriteria.getEmployees() != null) {
                isSingleKey = populateEmployeeCriteriaForSingleKey(extensionCriteria, whereCriteria);
                List<String> multiKey = whereCriteria.getEmployees().getMultiKey();
                isMultiKey = populateEmployeeCriteriaForMultiKey(extensionCriteria, whereCriteria, multiKey);
            } else {
                LOGGER.error(ExtensionConstant.ERROR + " employees details are missing in request criteria.");
                throw PrsnValidationException.missingProperty("employees");
            }
            extensionCriteria.setSnapshotDate(whereCriteria.getSnapshotDate());
            extensionCriteria.setSnapshotDateTime(whereCriteria.getSnapshotDateTime());
            extensionCriteria.setIsEmployeeAllowed(whereCriteria.getIsEmployeeAllowed());
            extensionCriteria.setIncludeAccrualPolicyDetails(whereCriteria.getIncludeAccrualPolicyDetails());
        }
        validateSingleAndMultiKey(isMultiKey, isSingleKey);

        return extensionCriteria;
    }

	private void validateSingleAndMultiKey(boolean isMultiKey, boolean isSingleKey) {
		if (!isMultiKey && !isSingleKey) {
            throw ExceptionHandler.getAPIException(ExceptionConstants.EXCEPTION_101203);
        }
        if (isMultiKey && isSingleKey) {
            throw ExceptionHandler.getAPIException(ExceptionConstants.MULTIPLE_KEY_PROPERTIES);
        }
	}

    public PersonQualifierBean getPersonQualifiers(ExtensionCriteria extensionCriteria) {
        PersonQualifierBean qualifierBean = new PersonQualifierBean();
        Map<Object, PersonalityResponse<EmployeeExtension>> extensionMap = getExtensionMap(extensionCriteria);
        extensionMap.forEach((k, v) -> {
            String searchValue = k.toString();
            EmployeeExtension empExt = v.getExtension();
            if (empExt != null) {
                PersonQualifierDetailBean bean = new PersonQualifierDetailBean();
                bean.setPersonId(empExt.getPersonId());
                bean.setPersonNumber(empExt.getPersonNumber());
                bean.setQualifier(searchValue);
                qualifierBean.addPersonDetails(bean);
            } else {
                qualifierBean.addMissingQualifiers(searchValue);
            }
        });
        return qualifierBean;
    }

    private boolean populateEmployeeCriteriaForMultiKey(ExtensionCriteria extensionCriteria,
            ExtensionWhereCriteria whereCriteria, List<String> multiKey) {
        if (multiKey != null) {
            if (multiKey.size() == 2) {
                List<List<String>> multiValueList = whereCriteria.getEmployees().getMultiKeyValues();
                List<SearchValues> searchValuesList = getSearchValueFromMultiValueList(multiValueList, multiKey);
                extensionCriteria.setMultiKeyValues(searchValuesList);
            }
            extensionCriteria.setMultiKey(multiKey);
            return true;
        }
        return false;
    }

    private boolean populateEmployeeCriteriaForSingleKey(
            ExtensionCriteria extensionCriteria, ExtensionWhereCriteria whereCriteria) {

        EmployeeCriteria employeeCriteria = whereCriteria.getEmployees();
        if (employeeCriteria == null || StringUtils.isBlank(employeeCriteria.getKey())) {
            return false;
        }

        List<String> nonNullPersonValues = new ArrayList<>();
        String key = employeeCriteria.getKey().toLowerCase();
        IdentifierType identifierType = getIdentifierType(key);
        if (identifierType == null) {
            LOGGER.error("Identifier type is null");
            throw ExceptionHandler.getAPIException(ExceptionConstants.EXCEPTION_101203);
        }
        Constructor<?> constructor = criteriaValidator.getSearchValueConstructor(identifierType.getDataType());

        processEmployeeCriteriaValues(
                employeeCriteria.getValues(),
                constructor,
                nonNullPersonValues::add,
                e -> {
                    LOGGER.error("Invalid value {} for the given key type", e.getMessage());
                    throw ExceptionHandler.getAPIException(ExceptionConstants.EXCEPTION_101202);
                }
        );

        extensionCriteria.setSearchValue(nonNullPersonValues);
        extensionCriteria.setSearchBy(key);
        return true;
    }

    private List<SearchValues> getSearchValueFromMultiValueList(List<List<String>> multiValueList, List<String> multiKey) {
        List<SearchValues> searchValuesList = null;
        if (multiValueList == null || multiValueList.isEmpty()) {
            return searchValuesList;
        }
        int keySize = multiKey.size();
        for (int i = 0; i < keySize; i++) {
            String key = multiKey.get(i);
            if (ExtensionConstant.AOID_SEARCH_KEY.equalsIgnoreCase(key)) {
                searchValuesList = updateAoidValues(multiValueList, searchValuesList, i, keySize);
            } else if (ExtensionConstant.COID_SEARCH_KEY.equalsIgnoreCase(key)) {
                searchValuesList = updateCoidValues(multiValueList, searchValuesList, i, keySize);
            }
        }
        return searchValuesList;
    }

    private List<SearchValues> updateCoidValues(List<List<String>> multiValueList, List<SearchValues> searchValuesList, int i,
            int keySize) {
        if (searchValuesList == null) {
            searchValuesList = multiValueList.stream().map(multiValue -> {
                if (keySize != multiValue.size()) {
                    throw ExceptionHandler.getAPIException(ExceptionConstants.EXCEPTION_101202);
                }
                SearchValues searchValues = new SearchValues();
                searchValues.setCoid(multiValue.get(i));
                return searchValues;
            }).collect(Collectors.toList());
        } else {
            updateCoidValues(multiValueList, i, searchValuesList);
        }
        return searchValuesList;
    }

    private List<SearchValues> updateAoidValues(List<List<String>> multiValueList, List<SearchValues> searchValuesList, int i,
            int keySize) {
        if (searchValuesList == null) {
            searchValuesList = multiValueList.stream().map(multiValue -> {
                if (keySize != multiValue.size()) {
                    throw ExceptionHandler.getAPIException(ExceptionConstants.EXCEPTION_101202);
                }
                SearchValues searchValues = new SearchValues();
                searchValues.setAoid(multiValue.get(i));
                return searchValues;
            }).collect(Collectors.toList());
        } else {
            updateAoidValues(multiValueList, i, searchValuesList);
        }
        return searchValuesList;
    }

    private void updateCoidValues(List<List<String>> multiValueList, int counter, List<SearchValues> searchValuesList) {
        for (int i = 0; i < multiValueList.size(); i++) {
            List<String> multiValue = multiValueList.get(i);
            SearchValues searchValues = searchValuesList.get(i);
            searchValues.setCoid(multiValue.get(counter));
        }
    }

    private void updateAoidValues(List<List<String>> multiValueList, int counter, List<SearchValues> searchValuesList) {
        for (int i = 0; i < multiValueList.size(); i++) {
            List<String> multiValue = multiValueList.get(i);
            SearchValues searchValues = searchValuesList.get(i);
            searchValues.setAoid(multiValue.get(counter));
        }
    }

    private Map<Object, PersonalityResponse<EmployeeExtension>> getExtensionMap(ExtensionCriteria extensionCriteria) {
        CriteriaValidator criteriaValidator = SpringContext.getBean(CriteriaValidator.class);
        PersonalityService personalityService = SpringContext.getBean(PersonalityService.class);
        Criteria criteria = criteriaValidator.validateAndGetCriteria(extensionCriteria);
        return personalityService
                .findEmployeeExtensionsByCriteria(criteria);
    }
    
    /**
	 * FLC-61767: validation to disallow multi_key added
	 * 
	 * @param searchCriteria
	 */
	public void validateCriteriaForMultiKeyNotAllowed(
			ExtensionSearchCriteria searchCriteria) {
		if (searchCriteria != null && searchCriteria.getWhere() != null) {
			ExtensionWhereCriteria whereCriteria = searchCriteria.getWhere();
			if (whereCriteria.getEmployees() != null) {
				EmployeeCriteria employeeCriteria = whereCriteria
						.getEmployees();
				if (employeeCriteria.getMultiKey() != null
						|| employeeCriteria.getMultiKeyValues() != null) {
					LOGGER.error("multi_key not allowed for lightweight extensions and employee refs");
					throw ExceptionHandler
							.getAPIException(ExceptionConstants.EXCEPTION_101221);
				}
			}
		}
	}

    public void validateCriteriaKeyValue(ExtensionSearchCriteria searchCriteria) {
        if (searchCriteria != null && searchCriteria.getWhere() != null) {
            ExtensionWhereCriteria whereCriteria = searchCriteria.getWhere();
            EmployeeCriteria employeeCriteria = whereCriteria.getEmployees();

            if (employeeCriteria != null && StringUtils.isNotBlank(employeeCriteria.getKey())) {
                String key = employeeCriteria.getKey().toLowerCase();
                IdentifierType identifierType = getIdentifierType(key);
                Constructor<?> constructor = criteriaValidator.getSearchValueConstructor(identifierType.getDataType());

                processEmployeeCriteriaValues(
                        employeeCriteria.getValues(),
                        constructor,
                        value -> {},
                        e -> {
                            LOGGER.error("Invalid value {} for the given key type", e.getMessage());
                            throw ExceptionHandler.getAPIException(ExceptionConstants.EXCEPTION_101202);
                        }
                );
            }
        }
    }

    private <T> void processEmployeeCriteriaValues(
            List<T> values,
            Constructor<?> constructor,
            Consumer<T> onSuccess,
            Consumer<Exception> onError) {

        if (values != null) {
            for (T value : values) {
                if (value != null) {
                    try {
                        constructor.newInstance(value);
                        onSuccess.accept(value);
                    } catch (Exception e) {
                        onError.accept(e);
                    }
                }
            }
        }
    }
}
