package com.kronos.persons.utils;

import java.util.Map;

import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.model.RestErrorBean;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.logging.framework.Log;
import com.kronos.wfc.platform.xml.api.bean.APIProcessingException;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

public class ExceptionHandler {

    private static final KLogger LOGGER = KLoggerFactory.getKLogger(ExceptionHandler.class);

    private ExceptionHandler() {

    }

    public static APIException getAPIException(String errorCode) {
        return new APIException(errorCode);
    }

    public static APIException getAPIExceptionWithUserParameters(String errorCode, Map<String, String> userParam) {
        APIException apie = getAPIException(errorCode);
        if (userParam != null && !userParam.isEmpty()) {
            for (Map.Entry<String, String> entry : userParam.entrySet()) {
                apie.addUserParameter(entry.getKey(), entry.getValue());
            }
        }
        return apie;
    }

    public static APIException getException(Exception ex) {
        LOGGER.error(ExtensionConstant.ERROR, ex);
        if (ex instanceof GenericException) {
            return ExceptionHandler.getAPIException(ExceptionConstants.EXCEPTION_101205);
        } else if (ex instanceof APIException) {
            String errorCode = ((APIException) ex).getErrorCode();
            if(StringUtils.isNoneBlank(errorCode)) {
                throw ExceptionHandler.getAPIException(errorCode);
            } else {
                return ExceptionHandler.getAPIException(ExceptionConstants.EXCEPTION_101205);
            }
        } else {
            return ExceptionHandler.getAPIException(ExceptionConstants.EXCEPTION_101206);
        }
    }

    public static RestErrorBean handleException(Throwable e, String beanName,
            String actionName) {
        Throwable exception = null;
        if (!(e instanceof GenericException) && !(e instanceof Error)) {
            exception = APIProcessingException.actionFailure(actionName, beanName, (Exception) e);
        } else {
            exception = e;
        }
        Log.log(Log.API, Log.ERROR, "Exception occurred in", beanName, "when performing the", actionName, "action.");
        return new RestErrorBean(exception);
    }

}
