package com.kronos.persons.photo.repository;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.kronos.persons.photo.entity.EmpPhoto;

@Repository
@Transactional
public interface PersonPhotoImageRepository extends JpaRepository<EmpPhoto, Long> {


	@Query(value="SELECT personid, photocontentobjtxt, updatedtm, capturedtm, datasourceid, deletedsw  FROM empphoto"
			+ " WHERE personId IN :personIds AND updatedtm > :modifiedSinceDateTime"
			+ " UNION ALL"
			+ " SELECT personid, '' as photocontentobjtxt, updatedtm, capturedtm, datasourceid, deletedsw FROM empphoto e1 "
			+ " WHERE personid IN :personIds AND NOT EXISTS"
			+ " (SELECT personid FROM empphoto e2 "
			+ " WHERE updatedtm > :modifiedSinceDateTime AND e1.personid =e2.personid)", nativeQuery=true)
	 List<EmpPhoto> getEmpPhotoListByPersonIdsAndUpdateDtm(@Param("personIds") List<Long> personIds,
			 @Param("modifiedSinceDateTime") LocalDateTime modifiedSinceDateTime);
	
}
 