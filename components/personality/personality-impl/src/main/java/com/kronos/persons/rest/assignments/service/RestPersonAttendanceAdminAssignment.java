package com.kronos.persons.rest.assignments.service;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.kronos.persons.rest.assignments.model.AttendanceAdminAssignmentWrapper;
import com.kronos.persons.utils.BulkProcessor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.AttendanceAdminAssignmentBean;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.extensions.service.CriteriaValidator;
import com.kronos.persons.rest.model.ExtensionSearchCriteria;
import com.kronos.persons.utils.CriteriaHelper;
import com.kronos.persons.utils.NewBatchProcessor;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.exceptions.framework.GenericException;

@Service("RestPersonAttendanceAdminAssignment")
public class RestPersonAttendanceAdminAssignment implements IRestPersonAttendanceAdminAssignment {

    @Inject @Lazy
    private PersonAttendanceAdminAssignmentService personAttendanceAdminAssignment;
    
    @Inject @Lazy
    CriteriaHelper criteriaHelper;
    
    @Inject @Lazy
    CriteriaValidator criteriaValidator;
    
    @Inject @Lazy
    PersonAssignmentHelper<AttendanceAdminAssignmentBean> assignmentHeplper;
    
    public static final String PERSON_ATTENDANCE_ADMIN_ASSIGN = "PersonAttendanceAdminAssignment";

    @Override
    public AttendanceAdminAssignmentBean update(AttendanceAdminAssignmentBean requestBean) {
        try {
        	ResponseHandler.validateForNullRequest(requestBean);
            Personality personality = personAttendanceAdminAssignment.updateRequest(requestBean);
            return ResponseHandler.apiSuccessResponse(requestBean, personality);

        } catch (APIException apiEx) {
			throw apiEx;
		} catch (GenericException e) {
			throw PrsnException.getAPIException(e);
		} catch (Exception e) {
			throw PrsnException.getAPIException(e);
		}
    }

    @Override
    public void delete(AttendanceAdminAssignmentBean requestBean) {
        try {
        	ResponseHandler.validateForNullRequest(requestBean);
        	personAttendanceAdminAssignment.deleteRequest(requestBean);    
        } catch (APIException apiEx) {
			throw apiEx;
		} catch (GenericException e) {
			throw PrsnException.getAPIException(e);
		} catch (Exception e) {
			throw PrsnException.getAPIException(e);
		}
    }

    @Override
    public List<AttendanceAdminAssignmentBean> multiUpdate(List<AttendanceAdminAssignmentBean> requestDataList) {
    	ResponseHandler.validateForNullRequest(requestDataList);
        ResponseHandler.validServiceLimit(requestDataList);
        List<AttendanceAdminAssignmentWrapper> wrapperRequestDataList = requestDataList
                .stream()
                .map(AttendanceAdminAssignmentWrapper::new)
                .collect(Collectors.toList());
        BulkProcessor<AttendanceAdminAssignmentWrapper, AttendanceAdminAssignmentBean> bulkProcessor
                = new BulkProcessor<>(wrapperRequestDataList, multiUpdateRequestProcessor);
        return bulkProcessor.process();
    }

    private Function<List<AttendanceAdminAssignmentWrapper>,
            List<AttendanceAdminAssignmentBean>> multiUpdateRequestProcessor = (
                    List<AttendanceAdminAssignmentWrapper> requestDataWrapperList) -> {
        try {
            List<AttendanceAdminAssignmentWrapper> listForUpdate = requestDataWrapperList.stream()
                    .peek(beanWrapper -> personAttendanceAdminAssignment.validateAdministratorWrapper(beanWrapper))
                    .filter(beanWrapper -> Objects.isNull(beanWrapper.getApiException()))
                    .collect(Collectors.toList());
            personAttendanceAdminAssignment.bulkUpdateRequest(listForUpdate);
            return listForUpdate.stream()
                    .filter(beanWrapper -> Objects.isNull(beanWrapper.getApiException()))
                    .map(AttendanceAdminAssignmentWrapper::getBean)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw PrsnException.getAPIException(e);
        }
    };

    @Override
    public void multiDelete(List<AttendanceAdminAssignmentBean> requestDataList) {
    	ResponseHandler.validateForNullRequest(requestDataList);
        ResponseHandler.validServiceLimit(requestDataList);
        Function<AttendanceAdminAssignmentBean, AttendanceAdminAssignmentBean> requestProcessor = (
                AttendanceAdminAssignmentBean reqData) -> {
                	try {
                		Personality personality = personAttendanceAdminAssignment.deleteRequest(reqData);
                		return ResponseHandler.apiSuccessResponse(reqData, personality);
                	}catch (APIException apiEx) {
        				throw apiEx;
        			} catch (GenericException e) {
        				throw PrsnException.getAPIException(e);
        			} catch (Exception e) {
        				throw PrsnException.getAPIException(e);
        			}
                };                
        NewBatchProcessor<AttendanceAdminAssignmentBean, AttendanceAdminAssignmentBean> batchprocessor = new NewBatchProcessor<>(requestDataList, requestProcessor);
        batchprocessor.process();
    }

    @Override
    public AttendanceAdminAssignmentBean retrieveByPersonId(Long personId) {
        return assignmentHeplper.getPersonAssignmentByPersonId(personId, personAttendanceAdminAssignment::retrieve);
    }
    
    @Override
    public AttendanceAdminAssignmentBean retrieveByPersonNumber(String personNumber) {
        return assignmentHeplper.getPersonAssignmentByPersonNumber(personNumber, personAttendanceAdminAssignment::retrieve);
    }

    @Override
    public List<AttendanceAdminAssignmentBean> retrieveList(ExtensionSearchCriteria searchCriteria) {
        return assignmentHeplper.getPersonAssignmentList(searchCriteria, personAttendanceAdminAssignment::retrieve);
    }
    
}
