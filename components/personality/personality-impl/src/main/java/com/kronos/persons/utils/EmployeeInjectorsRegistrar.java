/***********************************************************************
 * EmployeeInjectorsRegistrar.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.persons.utils;

import com.kronos.commonbusiness.converter.api.IInjectorsRegistrar;
import com.kronos.commonbusiness.converter.api.IRepositoryBasedConverter;


/**
 * Registers the converter with a matcher.
 * <AUTHOR>
 *
 */
public class EmployeeInjectorsRegistrar implements IInjectorsRegistrar {

	public static final IInjectorsRegistrar INSTANCE = new EmployeeInjectorsRegistrar();

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.kronos.wfc.commons.commons.converter.IInjectorsRegistrar#register(com
	 * .kronos.wfc.commons.commons.converter.IRepositoryBasedConverter)
	 */
	@Override
	public void register(IRepositoryBasedConverter c) {
		EmployeeMatcher.register(c);
	}
}
