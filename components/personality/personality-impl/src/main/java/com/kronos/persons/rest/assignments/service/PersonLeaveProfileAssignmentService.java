package com.kronos.persons.rest.assignments.service;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.AssignmentProfileRequestBean;
import com.kronos.persons.rest.assignments.model.AssignmentProfileRequestBeanWrapper;
import com.kronos.persons.rest.assignments.model.RestEntity;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.persons.rest.model.BeanWrapper;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.absencemgmt.leave.business.config.LeaveProfile;
import com.kronos.wfc.absencemgmt.leave.business.people.PersonLeaveProfileAssignment;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.businessobject.framework.BusinessValidationException;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import org.apache.commons.lang3.StringUtils;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Named
public class PersonLeaveProfileAssignmentService extends RestEntity {

	@Inject
	private LicenseValidator licenseValidator;

	@Inject
	ValidatorUtils validatorUtils;

	@Inject
	PersonIdentityBeanValidator personIdentityBeanValidator;

	private static final String LEAVE_PROFILE = "LeaveProfile";

	public AssignmentProfileRequestBean retrieve(PersonIdentityBean personIdentity) {
		Personality personality = validateAndGetPerson(personIdentity);
		return getAssignmentForPerson(personality);
	}

	private AssignmentProfileRequestBean getAssignmentForPerson(Personality personality) {
		AssignmentProfileRequestBean requestBean = new AssignmentProfileRequestBean();
		PersonLeaveProfileAssignment personLeaveProfileAssignment = PersonLeaveProfileAssignment
				.retrieveLeaveProfileByPersonId(personality.getPersonId());
		if(validatorUtils.isNull(personLeaveProfileAssignment)) {
			throw PrsnValidationException.nonExistentLeaveProfile();
		}
		requestBean.setPersonIdentity(personIdentityBeanValidator.createForPersonNumber(personality.getPersonNumber()));
		requestBean.setAssignmentProfile(getLeaveProfile(personLeaveProfileAssignment));
		return requestBean;
	}

	private Personality validateAndGetPerson(PersonIdentityBean personIdentity) {
		return personIdentityBeanValidator.getPersonality(personIdentity);
	}
	
	public Personality updateRequest(AssignmentProfileRequestBean requestBean) {
		PersonLeaveProfileAssignment personLeaveProfileAssignment;
		personIdentityBeanValidator.newvalidate(null, requestBean.getPersonIdentity(), null);
		Personality personality = personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity());
		licenseValidator.validateLeaveLicense(personality.getPersonId(), true);
		personLeaveProfileAssignment = PersonLeaveProfileAssignment
					.retrieveLeaveProfileByPersonId(personality.getPersonId());
		this.setWrappedObject(personLeaveProfileAssignment);
		setLeaveProfile(requestBean.getAssignmentProfile());
		doUpdateOnly();
		return personality;
	}

    /**
     * Validates bean from the wrapper.
     * In case of exception on the validation stage it will be stored into the wrapper.
     *
     * @param beanWrapper instance of {@link AssignmentProfileRequestBean}
     */
    public void validate(BeanWrapper<AssignmentProfileRequestBean> beanWrapper) {
        try {
            AssignmentProfileRequestBean bean = beanWrapper.getBean();
            validateLeaveProfileName(bean.getAssignmentProfile());
            personIdentityBeanValidator.newvalidate(null, bean.getPersonIdentity(), null);
            Personality personality = personIdentityBeanValidator.getPersonality(bean.getPersonIdentity());
			licenseValidator.validateLeaveLicense(personality.getPersonId(), true);
            beanWrapper.setBean(ResponseHandler.apiSuccessResponse(bean, personality));
        } catch (APIException apiException) {
            beanWrapper.setApiException(apiException);
        }
    }

    private void validateLeaveProfileName(String leaveProfileName) {
        if (StringUtils.isBlank(leaveProfileName)) {
            throw PrsnException.getAPIException(BusinessValidationException.nullProperty(LEAVE_PROFILE));
        }
    }

    /**
     * Save beans what contains in beanWrappers.
     *
     * @param beanWrappers list of bean wrappers with valid beans
     */
    public void multiUpdate(List<AssignmentProfileRequestBeanWrapper> beanWrappers) {
        Map<String, List<AssignmentProfileRequestBeanWrapper>> wrappers = beanWrappers.stream()
                .collect(Collectors.groupingBy(beanWrapper -> beanWrapper.getBean().getAssignmentProfile()));
        wrappers.forEach((profileName, wrapperList) -> {
            try {
                LeaveProfile leaveProfile = getLeaveProfile(profileName);
                List<Personality> personalities = getPersonalities(wrapperList);
                PersonLeaveProfileBulkUpdateTransactionService transactionService =
                        PersonLeaveProfileBulkUpdateTransactionService
                                .getTransactionService(leaveProfile, personalities);
                transactionService.run();
            } catch (APIException apiException) {
				saveExceptionInWrappers(apiException, wrapperList);
			} catch (GenericException genericException) {
                APIException apiException = PrsnException.getAPIException(genericException);
                saveExceptionInWrappers(apiException, wrapperList);
            } catch (Exception exception) {
                APIException apiException = PrsnException.getAPIException(exception);
                saveExceptionInWrappers(apiException, wrapperList);
            }
        });
    }

    private List<Personality> getPersonalities(List<AssignmentProfileRequestBeanWrapper> wrapperList) {
        return wrapperList.stream()
                .map(AssignmentProfileRequestBeanWrapper::getBean)
                .map(bean -> personIdentityBeanValidator.getPersonality(bean.getPersonIdentity()))
                .collect(Collectors.toList());
    }

    /**
     * Sets exception in every beanWrapper from List
     *
     * @param exception    the {@link APIException}
     * @param beanWrappers the List of {@link BeanWrapper}
     */
    private void saveExceptionInWrappers(APIException exception,
                                         List<AssignmentProfileRequestBeanWrapper> beanWrappers) {
        beanWrappers.forEach(beanWrapper -> beanWrapper.setApiException(exception));
    }

	public Personality deleteRequest(AssignmentProfileRequestBean requestBean) {
		PersonLeaveProfileAssignment personLeaveProfileAssignment;
		personIdentityBeanValidator.newvalidate(null, requestBean.getPersonIdentity(), null);
		Personality personality = personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity());
		licenseValidator.validateLeaveLicense(personality.getPersonId(), true);
		personLeaveProfileAssignment = PersonLeaveProfileAssignment
				.retrieveLeaveProfileByPersonId(personality.getPersonId());
		this.setWrappedObject(personLeaveProfileAssignment);
		doDelete();
		return personality;
	}

    /**
     * Set Leave profile.
     *
     * @param leaveProfile The leave profile name to set.
     */
    public void setLeaveProfile(String leaveProfile) {
        ((PersonLeaveProfileAssignment) getWrappedObject()).setProfileId(getLeaveProfile(leaveProfile).getId());
    }

    /**
     * Gets {@link LeaveProfile} by leave profile name.
     *
     * @param leaveProfileName the name of leave profile
     * @return LeaveProfile
     * @throws BusinessValidationException if LeaveProfile not found
     */
    private LeaveProfile getLeaveProfile(String leaveProfileName) {
        validateLeaveProfileName(leaveProfileName);
        LeaveProfile profile = LeaveProfile.retrieveByName(leaveProfileName);
        if (Objects.isNull(profile)) {
        	throw BusinessValidationException.invalidPropertyValue(LEAVE_PROFILE, leaveProfileName);
        }
        return profile;
    }

	/**
	 * Get The Leave Profile.
	 * @param personLeaveProfileAssignment 
	 * 
	 * @return Returns the Leave profile name.
	 */
	public String getLeaveProfile(PersonLeaveProfileAssignment personLeaveProfileAssignment) {
		ObjectIdLong profileId = personLeaveProfileAssignment.getProfileId();
		if (profileId == null || profileId.isNull())
			return null;

		LeaveProfile profile = LeaveProfile.retrieveById(profileId);

		if (profile == null)
			return null;

		return profile.getName();
	}
}