package com.kronos.persons.utils;

import com.kronos.container.api.exception.APIException;
import com.kronos.container.api.util.APIExceptionDetailResult;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.model.BeanWrapper;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * BulkProcessor.
 * Copyright (C) 2019 Kronos.com
 * Date: Jun 17, 2019
 *
 * @param <T> Request bean type
 * @param <U> Response bean type
 * <AUTHOR>
 */
public class BulkProcessor<T extends BeanWrapper, U> {

    private List<T> requestDataList;
    private Function<List<T>, List<U>> requestProcessor;

    /**
     * Constructor.
     *
     * @param requestDataList  the list from request.
     * @param requestProcessor the function for prepare response for request list
     */
    public BulkProcessor(List<T> requestDataList, Function<List<T>, List<U>> requestProcessor) {
        this.requestDataList = requestDataList;
        this.requestProcessor = requestProcessor;
    }

    /**
     * Prepares response list.
     *
     * @return response list
     */
    public List<U> process() {
        APIException partialSuccess = new APIException(ExceptionConstants.PARTIAL_SUCCESS);
        APIException allError = new APIException(ExceptionConstants.ALL_RECORDS_FAILED);
        List<U> responseList = this.requestProcessor.apply(requestDataList);
        if (responseList.isEmpty()) {
            allError.setResults(getResultList());
            throw allError;
        }
        if (responseList.size() < requestDataList.size()) {
            partialSuccess.setResults(getResultList());
            throw partialSuccess;
        }
        return responseList;
    }

    private List<APIExceptionDetailResult<?>> getResultList() {
        return requestDataList.stream().map(requestData -> {
            if (Objects.nonNull(requestData.getApiException())) {
                return new APIExceptionDetailResult<>(requestData.getApiException());
            } else {
                return new APIExceptionDetailResult<>(requestData.getBean());
            }
        }).collect(Collectors.toList());
    }
}
