/***********************************************************************
 * Copyright 2018, Kronos Incorporated. All rights
 * reserved.
 **********************************************************************/

package com.kronos.persons.context.switchrole;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.commonapp.authz.api.services.permission.PermissionsService;
import com.kronos.commonapp.authz.services.model.Permissions;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.releasetoggle.api.ReleaseToggleService;
import com.kronos.people.proxy.api.dto.GenericObjectRef;
import com.kronos.persons.context.service.ApplicationContextManagerImpl;
import com.kronos.persons.rest.model.DelegationRoleAssignmentDTO;
import com.kronos.persons.rest.model.EmployeeRefBean;
import com.kronos.persons.utils.DateUtils;
import com.kronos.wfc.commonapp.people.business.person.delegateauthority.MultiManagerRoleAssignmentDTO;
import com.kronos.wfc.commonapp.people.business.person.delegation.SwitchRoleFacadeServiceHelper;
import com.kronos.wfc.commonapp.people.business.user.CurrentUserAccountManager;
import com.kronos.wfc.commonapp.people.facade.person.delegateauthority.DelegateAuthorityFacade;
import com.kronos.wfc.commonapp.people.facade.person.delegateauthority.DelegateAuthorityFacadeFactory;
import com.kronos.wfc.platform.datetime.bridge.ITimeZoneAccessorService;
import com.kronos.wfc.platform.datetime.bridge.TimeZoneAccessorServiceFactory;
import com.kronos.wfc.platform.datetime.framework.KTimeZone;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.business.state.State;
import com.kronos.wfc.platform.security.framework.SecurityConstsIfc;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;
import com.kronos.wfc.platform.utility.framework.datetime.KTimeStamp;
import org.apache.commons.lang3.StringUtils;

import static com.kronos.wfc.commonapp.people.facade.person.delegateauthority.DelegateAuthorityFacadeFactory.createFacade;

@Named
public class RoleDelegateManager implements IRoleDelegateManager {

	static final String ALLOW_MMR_FACP = "ALLOW_MULTIPLE_MANAGER_ROLES";
	private static final String ENABLE_MMR_FACP = "EnableMultipleManagerRolesFACP";

	@Inject
	PermissionsService permissionsService;

	@Inject
	ReleaseToggleService releaseToggleService;

	public static final String LOCK_ROLECTX = "LOCK_ROLECTX";
	public static final String ROLEDELEGATE_CONTEXT = "ROLEDELEGATE_CONTEXT";
	public static final long WAITTIME_MILLS = 300000;
	public static final String DELEGATOR_ID = "delegatorId";

	protected DelegateAuthorityFacade daFacade;
	protected SwitchRoleFacadeServiceHelper helper;

	private static KLogger LOGGER = KLoggerFactory.getKLogger(RoleDelegateManager.class);
	
	@PostConstruct
	public void init() {
		daFacade = createFacade();
		helper = new SwitchRoleFacadeServiceHelper();
	}

	@Override
	public IRoleDelegateContext getRoleDelegateContext() {
		return getRoleDelegateContextImpl();
	}

	/**
	 * get the role Delegate Context Impl 
	 * {
    "switchRoleDelegates": [
        {
            "id": "-1337",
            "delegatorId": "-1227",
            "delegator": "Myself",
            "role": ""
        },
        {
            "id": "4",
            "delegatorId": "101",
            "delegator": "Martin, Michael",
            "role": "For Manufacturing Managers",
            "startDate": "2018-01-18 00:00:00.000",
            "endDate": "2018-01-25 00:00:00.000"
        }
    ],
    "currentDelegationName": "Myself",
    "currentDelegationRole": "Mathur, Manish",
    "isMySelf": true
}
	 * @return
	 */
	public IRoleDelegateContext getRoleDelegateContextImpl() {
		RoleDelegateContext rdContext = new RoleDelegateContext();
		List<IDelegationsList> switchRoleDelegates = getSwitchRoleDelegates();

		rdContext.setSwitchRoleDelegates(switchRoleDelegates);
		rdContext.setCurrentDelegationName(getCurrentDelegationName());
		rdContext.setCurrentDelegationRole(getCurrentDelegationRole());

		String currentTaskId = getCurrentTaskId();
		rdContext.setCurrentTaskId(currentTaskId);

		boolean isMyself = getIsMySelfBoolean();
		rdContext.setIsMySelf(isMyself);

		if (!releaseToggleService.getValue(ENABLE_MMR_FACP) ||
				!permissionsService.getPermission(ALLOW_MMR_FACP,
						CurrentUserAccountManager.getPersonality().getPersonId().toLong(), false).getPermissions().isEmpty()
		) {

			ObjectIdLong personId = CurrentUserAccountManager.getPersonality().getPersonId();
			List<MultiManagerRoleAssignmentDTO> myRoles = getMyRoles(personId);
			rdContext.setMyRoles(myRoles);

			Object activeRoleId = State.getFromSessionState(SecurityConstsIfc.ACTIVE_ROLE_ID);
			if (activeRoleId != null) {
				rdContext.setActiveRole(getActiveRole(myRoles, (ObjectIdLong) activeRoleId));
			} else if (isMyself) {
				rdContext.setActiveRole(myRoles.stream().filter(MultiManagerRoleAssignmentDTO::getDefault).findAny().orElse(null));
			} else if (currentTaskId != null) {
				rdContext.setActiveDelegateTask(getActiveDelegateTask(switchRoleDelegates, currentTaskId));
			}
		} else {
			rdContext.setMyRoles(new ArrayList<>());
		}

		return rdContext;
	}

	/**
	 * Get active role. If activeRole is not present in myRoles,
	 * we think that user in DA and get activeRole from delegateeTasks
	 * @param myRoles  RoleAssignments for Person without DA roles
	 * @return MultiManagerRoleAssignmentDTO
	 */
	private MultiManagerRoleAssignmentDTO getActiveRole(List<MultiManagerRoleAssignmentDTO> myRoles, ObjectIdLong activeRoleId) {
		return myRoles.stream().filter(v -> activeRoleId.toLong().equals(v.getAssignmentId())).findAny().orElse(null);
	}

	/**
	 * Get RoleAssignments for Person without DA roles
	 * @param personId logonUser personId
	 * @return List<MultiManagerRoleAssignmentDTO>
	 */
	private List<MultiManagerRoleAssignmentDTO> getMyRoles(ObjectIdLong personId) {
		return helper.findAllRoleAssignmentByPersonId(personId, false);
	}

	/**
	 * This method get data for the Switch role delegates as
	 * "switchRoleDelegates": [
        {
            "id": "-1337",
            "delegatorId": "-1227",
            "delegator": "Myself",
            "role": ""
        },
	 * @return {@link}
	 */
	@SuppressWarnings("unchecked")
	public List<IDelegationsList> getSwitchRoleDelegates() {
		List<IDelegationsList> delegationsList = new ArrayList<>();
		if (daFacade.hasDelegations()) {
			Map[] dList = daFacade.loadDelegationsList();
			for (Map element : dList) {
				IDelegationsList delegation = convertToDelegation(element);
				if (delegation != null) {
					delegationsList.add(delegation);
				}
			}
		}
		return delegationsList;
	}

	public List<DelegationRoleAssignmentDTO> getDelegationRoles() {
		List<DelegationRoleAssignmentDTO> delegationsList = new ArrayList<>();
		if (daFacade.hasDelegations()) {
			Map[] dList = daFacade.loadDelegationsList();
			for (Map element : dList) {
				DelegationRoleAssignmentDTO delegation = convertToDto(element);
				if (delegation != null) {
					delegationsList.add(delegation);
				}
			}
		}
		return delegationsList;
	}

	/**
	 * Get the name of Current Delegation assigned
	 * @return {@String}
	 */
	protected String getCurrentDelegationName() {
		return daFacade.getCurrentDelegationName();
	}

	/**
	 * Gets the role of Current Delegation Assigned
	 * @return {@String}
	 */
	protected String getCurrentDelegationRole() {
		return daFacade.getCurrentDelegationRole();
	}

	/**
	 * Get the id of current Task
	 * @return {@String}
	 */
	protected String getCurrentTaskId() {
		return daFacade.getCurrentTaskId();
	}

	/**
	 * Get a boolean value if logged in one is himself or not
	 * @return {@Boolean}
	 */
	protected boolean getIsMySelfBoolean() {
		   return daFacade.isMyself();
	   }

	/**
	 * Returns a DelegateAuthorityfacade from DelegateFacade Factory
	 * @return {@link}
	 */
	protected DelegateAuthorityFacade createFacade() {
		return DelegateAuthorityFacadeFactory.createFacade();
	}

	/**
	 * If the active role is a MMR and it's default role then the answer is true; otherwise it will be false
	 * @param activeRole MultiManagerRoleAssignmentDTO
	 * @param myRoles List<MultiManagerRoleAssignmentDTO>
	 * @return The boolean for if the current delegation is myself to set.
	 */
	protected boolean isMyself(MultiManagerRoleAssignmentDTO activeRole, List<MultiManagerRoleAssignmentDTO> myRoles) {
		if(!myRoles.contains(activeRole)) {
			return false;
		}
		return activeRole.getDefault();
	}

	private IDelegationsList getActiveDelegateTask(List<IDelegationsList> switchRoleDelegates, String currentTaskId) {
		return switchRoleDelegates.stream().filter(v -> currentTaskId.equals(v.getId())).findAny().orElse(null);
	}

	// Convert to delegation, and filter out future delegation from displaying
	private IDelegationsList convertToDelegation(Map<String, Object> element) {
		IDelegationsList delegation = null;
		KDate sKDate = element.get(DelegateAuthorityFacade.START_DATE) == "" ? null
				: (KDate) element.get(DelegateAuthorityFacade.START_DATE);
		if (sKDate == null || !isDelegatedStartAfterDelegatorDate(sKDate,element)) {
			delegation = new DelegationsList();
			delegation.setId((String) element.get(DelegateAuthorityFacade.DELEGATOR_ID));
			delegation.setDelegatorId(element.get(DelegateAuthorityFacade.DELEGATOR_PERSON_ID).toString());
			delegation.setDelegator((String) element.get(DelegateAuthorityFacade.DELEGATOR_NAME));
			delegation.setRole((String) element.get(DelegateAuthorityFacade.DELEGATOR_ROLE));
			delegation.setStartDate(sKDate == null ? null : sKDate.toString());
			String eDate = element.get(DelegateAuthorityFacade.END_DATE) == "" ? null
					: ((KDate) element.get(DelegateAuthorityFacade.END_DATE)).toString();
			delegation.setEndDate(eDate);
		}
		return delegation;
	}

	private DelegationRoleAssignmentDTO convertToDto(Map<String, Object> element) {
		DelegationRoleAssignmentDTO dto = null;
		KDate sKDate = StringUtils.isEmpty(element.get(DelegateAuthorityFacade.START_DATE).toString()) ? null
				: (KDate) element.get(DelegateAuthorityFacade.START_DATE);
		ObjectIdLong delegatorPersonId = (ObjectIdLong) element.get(DelegateAuthorityFacade.DELEGATOR_PERSON_ID);
		if ((sKDate == null || !isDelegatedStartAfterDelegatorDate(sKDate,element)) && !DelegateAuthorityFacade.MYSELFAOID.equals(delegatorPersonId)) {
			dto = new DelegationRoleAssignmentDTO();
			dto.setId((String) element.get(DelegateAuthorityFacade.DELEGATOR_ID));
			dto.setDelegatorFullName(element.get(DelegateAuthorityFacade.DELEGATOR_NAME).toString());

			EmployeeRefBean delegator = new EmployeeRefBean();
			delegator.setId(((ObjectIdLong) element.get(DelegateAuthorityFacade.DELEGATOR_PERSON_ID)).toLong());
			delegator.setQualifier((String) element.get(DelegateAuthorityFacade.DELEGATOR_PERSON_NUMBER));
			dto.setDelegator(delegator);

			dto.setRole((String) element.get(DelegateAuthorityFacade.DELEGATOR_ROLE));
			dto.setStartDate(sKDate == null ? null : DateUtils.kDateToLocalDate(sKDate));
			LocalDate eDate = StringUtils.isEmpty(element.get(DelegateAuthorityFacade.END_DATE).toString()) ? null
					: DateUtils.kDateToLocalDate((KDate) element.get(DelegateAuthorityFacade.END_DATE));
			dto.setEndDate(eDate);
		}
		return dto;
	}

	private boolean isDelegatedStartAfterDelegatorDate(KDate sKDate, Map<String, Object> element){
		ITimeZoneAccessorService tzAccessor = TimeZoneAccessorServiceFactory.getTimeZoneAccessorService();
		ObjectIdLong delegatorIdObj= (ObjectIdLong) element.get(DELEGATOR_ID);
		Long delegatorId = delegatorIdObj.toLong();
		KTimeZone ktz = tzAccessor.getTimeZoneForEmployee(delegatorId, KDate.today());
		TimeZone tz = ktz.getJavaTimeZone();
		KTimeStamp nowTS = KTimeStamp.createTimeStamp(tz);
		KDate today = nowTS.getDate();
		return sKDate.isAfterDate(today);
	}
}

