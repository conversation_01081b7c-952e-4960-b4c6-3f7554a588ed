/***********************************************************************
 * Copyright 2018, Kronos Incorporated. All rights
 * reserved.
 **********************************************************************/
package com.kronos.persons.context.utils;

public enum ContextErrorCode {
	
	ERROR_FAILED_TO_ACQUIRE_LOCK("WFP-113520"),
	ERROR_FAILED_TO_ACQUIRE_LOCK_MESSAGE_LOG("Error Failed To Acquire Lock :: getPreparedData "),
	
	ERROR_THREAD_INTERRUPTED("WFP-113521"),
	ERROR_THREAD_INTERRUPTED_MESSAGE_LOG("Thread Interrupted");
	
	private String value;
	
	private ContextErrorCode(String value) {
		this.value=value;
	}
	
	public String getValue() {
		return value;
	}
	

}