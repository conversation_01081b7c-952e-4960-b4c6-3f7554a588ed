package com.kronos.persons.utils;

public class IntegrationUserProviderThreadLocal {

    private static final IntegrationUserProviderThreadLocal integrationUserProviderThreadZLocal = new IntegrationUserProviderThreadLocal();

    private static ThreadLocal<Boolean> integrationUserThreadLocal = ThreadLocal
            .withInitial(() -> false);


   private void IntegrationUserProviderThreadLocal() {
    }

    public static IntegrationUserProviderThreadLocal getInstance() {

        return integrationUserProviderThreadZLocal;
    }

    public static Boolean getIntegrationUserThreadLocal() {
        return integrationUserThreadLocal.get();
    }

    public void setIntegrationUserThreadLocal(Boolean isIntegrationUserThreadLocal) {
        this.integrationUserThreadLocal.set(isIntegrationUserThreadLocal);


    }

    public static void removeIntegrationUserThreadLocal() {
        integrationUserThreadLocal.remove();
    }


}

