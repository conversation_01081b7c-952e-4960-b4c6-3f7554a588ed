
package com.kronos.persons.supportapi.errorcodes;

/*
 * *****************************************************************************
 * Copyright (c) 2020 Kronos, Inc. All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * Kronos, Inc. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with Kronos.
 *
 * <PERSON><PERSON><PERSON><PERSON> MAKES NO REPRESENTATIONS OR WARRANTIES ABOUT THE SUITABILITY OF THE
 * SOFTWARE, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
 * PURPOSE, OR NON-INFRINGEMENT. KRONOS SHALL NOT BE LIABLE FOR ANY DAMAGES
 * SUFFERED BY LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING
 * THIS SOFTWARE OR ITS DERIVATIVES.
 ****************************************************************************
 *
 **/

/**
 * declares the constants used for caching support API.
 * <AUTHOR>
 *
 */

public class PersonalitySupportApiConstants {
	
	private PersonalitySupportApiConstants() {
		
	}
	
	public static final String TENANTS_KEY = "invalidTenants";
	
	public static final String EMPTY_TENANTS = "";
	
	public static final String EMPLOYEE_KEY = "invalidEmployees";
	
	public static final String INVALID_REQUEST = "invalidRequest";
	
	public static final String TENANT_NAME = "tenant";
	
	public static final String VALID_EMP_LIST = "VALID_EMPLOYEE_LIST";
	
	public static final String INVALID_EMP_LIST = "INVALID_EMPLOYEE_LIST";
	
	public static final String INVALID_EMP_STATUS = "NOT FOUND";
	
	public static final String SUCCESSFULLY_EVICTED = "Evicted";
	
	public static final String EVICTION_FAILED = "Failed";
	
	public static final String MAX_PERSON_ALLOWED_EVICT_REQUEST = "max.person.allowed.personality.evict.request";
	
	public static final String MAX_TENANT_ALLOWED_EVICT_REQUEST = "max.tenant.allowed.personality.evict.request";
	
	public static final String MAX_PERSON_ALLOWED_IN_VALIDATE_CACHE_REQUEST = "max.person.allowed.personality.validate.cache.request";
	
	public static final String MAX_TENANT_ALLOWED_IN_VALIDATE_CACHE_REQUEST = "max.tenant.allowed.personality.validate.cache.request";
	
	public static final Integer MAX_TENANT_DEFAULT_VALUE_EVICT_REQUEST = 1;

	public static final Integer MAX_PERSON_DEFAULT_VALUE_EVICT_REQUEST = 5;
	
	public static final Integer MAX_TENANT_DEFAULT_VALUE_FOR_VALIDATE_CACHE_REQUEST = 1;

	public static final Integer MAX_PERSON_DEFAULT_VALUE_FOR_VALIDATE_CACHE_REQUEST = 5;
	
	public static final int HTTP_PARTIAL_CONTENT_STATUS_CODE = 207;
	
	public static final String NEW_PERSON_CACHE = "newPersonCache";
	
	public static final String LEGACY_PERSON_CACHE = "legacyPersonCache";	
	
	public static final String BOTH_LEGACY_AND_PERSON_CACHE = "bothPersonCache";
	
	public static final String DOT_SEPARATOR = ".";
	
	public static final String COMMA = ",";
	
	public static final String ALL_EXTENSION = "AllExtension";
	
	public static final String SKIP_CACHE_VALIDATION_PROPERTIES = "skip.cache.validation.properties";

	public static final int HTTP_PARTIAL_SUCCESS_STATUS = 207;

	public static final String SUBMIT_REQUEST_SUCCESS = "Request submitted successfully";

	public static final String SUBMIT_REQUEST_FAILED = "Request submission failed";

	public static final Integer MAX_PERSON_DEFAULT_VALUE_FOR_INSERT_CACHE_REQUEST = 5;

	public static final Integer MAX_PRIME_SIZE_DEFAULT_IN_INSERT_CACHE_REQUEST = 5;

	public static final String MAX_PRIME_SIZE_ALLOWED_IN_INSERT_CACHE_REQUEST = "max.prime.size.allowed.personality.insert.cache.request";

	public static final String MAX_PERSON_ALLOWED_IN_INSERT_CACHE_REQUEST = "max.person.allowed.personality.insert.cache.request";

	public static final String MAX_Tenant_ALLOWED_IN_INSERT_CACHE_REQUEST = "max.tenant.allowed.personality.insert.cache.request";

	public static final Integer MAX_Tenant_DEFAULT_ALLOWED_IN_INSERT_CACHE_REQUEST = 1;
	
	
}
