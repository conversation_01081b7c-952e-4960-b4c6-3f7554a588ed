package com.kronos.persons.utils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.inject.Named;

import org.apache.commons.lang3.StringUtils;

import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.persons.rest.model.PhotoCriteria;
import com.kronos.persons.rest.model.PhotoSearchCriteria;
import com.kronos.persons.rest.model.PhotoSearchWhereCriteria;
import com.kronos.persons.rest.model.SearchValues;
import com.kronos.persons.rest.service.PersonPhotoServiceConstants;
/**
 * Helper class to populate and validate the criteria provided to retrieve employees photos
 * based on either single key or multi key identifiers. 
 *
 */
@Named
public class PersonPhotoCriteriaHelper {

	private static final KLogger LOGGER = KLoggerFactory.getKLogger(PersonPhotoCriteriaHelper.class);
	
	public PhotoCriteria getPersonPhotoCriteria(PhotoSearchCriteria searchCriteria) {
		LOGGER.debug("PersonPhotoCriteriaHelper::getPersonPhotoCriteria() - Validating the search criteria -Start");
		PhotoCriteria photoCriteria = new PhotoCriteria();
        boolean isMultiKey = false;
        boolean isSingleKey = false;
        if (searchCriteria != null && searchCriteria.getWhere() != null) {
            PhotoSearchWhereCriteria whereCriteria = searchCriteria.getWhere();
            if (whereCriteria.getEmployees() != null) {
                isSingleKey = populateEmployeeCriteriaForSingleKey(photoCriteria, whereCriteria);
                List<String> multiKey = whereCriteria.getEmployees().getMultiKey();
                isMultiKey = populateEmployeeCriteriaForMultiKey(photoCriteria, whereCriteria, multiKey);
            } else {
                LOGGER.error(ExtensionConstant.ERROR + PersonPhotoServiceConstants.EMPLOYEE_MISSING_PROP_DETAILS);
                throw PrsnValidationException.missingIdentier(PersonPhotoServiceConstants.EMPLOYEE_MISSING_IDENTIFIER_NAME);
            }
        }
        validateSingleAndMultiKey(isMultiKey, isSingleKey);
        LocalDateTime modifiedSince = searchCriteria.getMultiReadOptions() == null ? null : searchCriteria.getMultiReadOptions().getModifiedSinceDateTime();
        if(Objects.nonNull(modifiedSince)) {
        	photoCriteria.setModifiedSinceDateTime(modifiedSince);
        }
		LOGGER.debug("PersonPhotoCriteriaHelper::getPersonPhotoCriteria() - Validating the search criteria -End");
        return photoCriteria;
    }


	private boolean populateEmployeeCriteriaForMultiKey(PhotoCriteria photoCriteria,
			PhotoSearchWhereCriteria whereCriteria, List<String> multiKey) {
		if (multiKey != null) {
			if (multiKey.size() == 2) {
				List<List<String>> multiValueList = whereCriteria.getEmployees().getMultiKeyValues();
				List<SearchValues> searchValuesList = getSearchValueFromMultiValueList(multiValueList, multiKey);
				photoCriteria.setMultiKeyValues(searchValuesList);
			}
			photoCriteria.setMultiKey(multiKey);
			return true;
		}
		LOGGER.info("PersonPhotoCriteriaHelper::populateEmployeeCriteriaForMultiKey() - No Multi Key identifier found to retrieve employee photos");
		return false;
	}

	/**
	 * Populate search values in criteria for Single Key identifiers
	 * @param photoCriteria
	 * @param whereCriteria
	 * @return
	 */
	private boolean populateEmployeeCriteriaForSingleKey(PhotoCriteria photoCriteria, PhotoSearchWhereCriteria whereCriteria) {
		LOGGER.debug("PersonPhotoCriteriaHelper::populateEmployeeCriteriaForSingleKey() - Start");
		if (StringUtils.isNotBlank(whereCriteria.getEmployees().getKey())) {
			photoCriteria.setSearchValue(whereCriteria.getEmployees().getValues());
			photoCriteria.setSearchBy(whereCriteria.getEmployees().getKey().toLowerCase());
			return true;
		}
		LOGGER.info("PersonPhotoCriteriaHelper::populateEmployeeCriteriaForSingleKey() - No Single Key identifier found to retrieve employee photos");
		return false;
	}

	/**
	 * Populate search values in criteria for Multi-Key(AOID & COID pair) identifiers
	 * @param multiValueList
	 * @param multiKey
	 * @return
	 */
	private List<SearchValues> getSearchValueFromMultiValueList(List<List<String>> multiValueList, List<String> multiKey) {
		List<SearchValues> searchValuesList = null;
		if (multiValueList == null || multiValueList.isEmpty()) {
			return searchValuesList;
		}
		int keySize = multiKey.size();
		for (int i = 0; i < keySize; i++) {
			String key = multiKey.get(i);
			if (ExtensionConstant.AOID_SEARCH_KEY.equalsIgnoreCase(key)) {
				searchValuesList = updateAoidValues(multiValueList, searchValuesList, i, keySize);
			} else if (ExtensionConstant.COID_SEARCH_KEY.equalsIgnoreCase(key)) {
				searchValuesList = updateCoidValues(multiValueList, searchValuesList, i, keySize);
			}
		}
		return searchValuesList;
	}

	private List<SearchValues> updateCoidValues(List<List<String>> multiValueList, List<SearchValues> searchValuesList, int i,
			int keySize) {
		if (searchValuesList == null) {
			searchValuesList = multiValueList.stream().map(multiValue -> {
				if (keySize != multiValue.size()) {
					throw ExceptionHandler.getAPIException(ExceptionConstants.EXCEPTION_101202);
				}
				SearchValues searchValues = new SearchValues();
				searchValues.setCoid(multiValue.get(i));
				return searchValues;
			}).collect(Collectors.toList());
		} else {
			updateCoidValues(multiValueList, i, searchValuesList);
		}
		return searchValuesList;
	}

	private List<SearchValues> updateAoidValues(List<List<String>> multiValueList, List<SearchValues> searchValuesList, int i,
			int keySize) {
		if (searchValuesList == null) {
			searchValuesList = multiValueList.stream().map(multiValue -> {
				if (keySize != multiValue.size()) {
					throw ExceptionHandler.getAPIException(ExceptionConstants.EXCEPTION_101202);
				}
				SearchValues searchValues = new SearchValues();
				searchValues.setAoid(multiValue.get(i));
				return searchValues;
			}).collect(Collectors.toList());
		} else {
			updateAoidValues(multiValueList, i, searchValuesList);
		}
		return searchValuesList;
	}

	private void updateCoidValues(List<List<String>> multiValueList, int counter, List<SearchValues> searchValuesList) {
		for (int i = 0; i < multiValueList.size(); i++) {
			List<String> multiValue = multiValueList.get(i);
			SearchValues searchValues = searchValuesList.get(i);
			searchValues.setCoid(multiValue.get(counter));
		}
	}

	private void updateAoidValues(List<List<String>> multiValueList, int counter, List<SearchValues> searchValuesList) {
		for (int i = 0; i < multiValueList.size(); i++) {
			List<String> multiValue = multiValueList.get(i);
			SearchValues searchValues = searchValuesList.get(i);
			searchValues.setAoid(multiValue.get(counter));
		}
	}
	
	/**
	 * Validate either single-key or multi-key provided in request
	 * 
	 * @param isMultiKey
	 * @param isSingleKey
	 */
	private void validateSingleAndMultiKey(boolean isMultiKey, boolean isSingleKey) {
		if (!isMultiKey && !isSingleKey) {
			LOGGER.error("PersonPhotoCriteriaHelper::validateSingleAndMultiKey() - neither single-key or multi-key identifiers not provided");
            throw ExceptionHandler.getAPIException(ExceptionConstants.EXCEPTION_101203);
        }
        if (isMultiKey && isSingleKey) {
			LOGGER.error("PersonPhotoCriteriaHelper::validateSingleAndMultiKey() - Both single-key and multi-key identifiers provided");
            throw ExceptionHandler.getAPIException(ExceptionConstants.MULTIPLE_KEY_PROPERTIES);
        }
	}
}
