package com.kronos.persons.utils;

import com.kronos.container.api.exception.APIException;
import com.kronos.container.api.util.APIExceptionDetailResult;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.model.EmployeeWageWorkRulesDTO;
import com.kronos.persons.rest.model.wageoverride.EmployeeWageWorkRulesWrapper;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * The abstract processor contains common methods
 * for generation response for the "v1/commons/persons/wage_work_rules" APIs.
 * Copyright (C) 2020 Kronos.com
 * Date: Jun 30, 2020
 *
 * <AUTHOR>
 */
public abstract class WageWorkRuleOverrideBulkProcessor {

    /**
     * Return list of {@link EmployeeWageWorkRulesDTO} or {@link APIException} if
     * processor return list with {@link APIException}.
     *
     * @return list of {@link EmployeeWageWorkRulesDTO}
     * @throws APIException if searching wage work rule overrides contains exceptions
     */
    public List<EmployeeWageWorkRulesDTO> process() {
        List<EmployeeWageWorkRulesWrapper> responseList = applyProcessor();
        long successCount = responseList.stream().filter(wrapper -> Objects.isNull(wrapper.getApiException()))
                .count();
        if (successCount == 0) {
            throw allError(responseList);
        } else if (successCount < getResponseSize(responseList)) {
            throw partialSuccess(responseList);
        } else {
            return responseList.stream().map(EmployeeWageWorkRulesWrapper::getDTO).collect(Collectors.toList());
        }
    }

    private APIException allError(List<EmployeeWageWorkRulesWrapper> responseList) {
        APIException allError = new APIException(ExceptionConstants.ALL_RECORDS_FAILED);
        allError.setResults(getResultList(responseList));
        throw allError;
    }

    private APIException partialSuccess(List<EmployeeWageWorkRulesWrapper> responseList) {
        APIException partialSuccess = new APIException(ExceptionConstants.PARTIAL_SUCCESS);
        partialSuccess.setResults(getResultList(responseList));
        throw partialSuccess;
    }

    private List<APIExceptionDetailResult<?>> getResultList(List<EmployeeWageWorkRulesWrapper> responseList) {
        return responseList.stream().map(wrapper -> {
            if (Objects.nonNull(wrapper.getApiException())) {
                setInputDetail(wrapper);
                return new APIExceptionDetailResult<>(wrapper.getApiException());
            } else {
                return new APIExceptionDetailResult<>(getDtoForResponse(wrapper));
            }
        }).collect(Collectors.toList());
    }

    /**
     * Sets detail about exception what contains in the wrapper.
     *
     * @param wrapper wrapper that hold dto with corresponding exception
     */
    protected abstract void setInputDetail(EmployeeWageWorkRulesWrapper wrapper);

    /**
     * Returns dto for part with error for the partial success response.
     *
     * @param wrapper wrapper that hold dto with corresponding exception
     * @return dto from wrapper
     */
    protected abstract EmployeeWageWorkRulesDTO getDtoForResponse(EmployeeWageWorkRulesWrapper wrapper);

    /**
     * @return List of {@link EmployeeWageWorkRulesWrapper}
     */
    protected abstract List<EmployeeWageWorkRulesWrapper> applyProcessor();

    /**
     * @return expected size of response.
     * To support multi-read with single key input while employee could have multiposition situation, response size is
     * the size of return list from applyProcessor
     * @param resultList result from applyProcessor
     */
    protected abstract int getResponseSize(List<EmployeeWageWorkRulesWrapper> resultList);
}
