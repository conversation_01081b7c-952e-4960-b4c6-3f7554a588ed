package com.kronos.persons.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import org.apache.commons.lang3.StringUtils;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.model.BatchResponseBean;
import com.kronos.persons.rest.model.BatchResponseDetailsBean;
import com.kronos.persons.rest.model.BatchResponseResultsBean;
import com.kronos.persons.rest.model.PersonalityBean;
import com.kronos.persons.rest.model.ResponseBean;
import com.kronos.persons.rest.model.RestErrorBean;
import com.kronos.wfc.commonapp.people.business.personality.Personality;


/**
 * 
 * <AUTHOR>
 *
 * @param <T> Collection of objects to process using requestProcessor function.
 */
public class BatchProcessor<T> {
	
	private List<T> requestDataList;
	private Function<T, Personality> requestProcessor;
	private String action;
	
	public BatchProcessor(List<T> requestDataList, Function<T, Personality> requestProcessor, String action) {
		this.requestDataList = requestDataList;
		this.requestProcessor = requestProcessor;
		this.action = action;
	}
	
	public BatchResponseBean process(){
		
		//capture the details including results & error offsets
		BatchResponseDetailsBean detailresponse = new BatchResponseDetailsBean();
		List<BatchResponseResultsBean> results = new ArrayList<>();
		List<Integer> errorOffsets = new ArrayList<>();
		
		final Integer[] index = new Integer[1];
		index[0] = 0;

		//stream process request data
		this.requestDataList.stream().forEach(requestData -> {
			index[0] = index[0] + 1;
			BatchResponseResultsBean resultresponse = new BatchResponseResultsBean();
			try {
				Personality personality = this.requestProcessor.apply(requestData);
				ResponseBean successResponse = ResponseHandler.batchServiceSuccessResponse(personality);
					Object obj = requestData;
					if (requestData instanceof PersonalityBean && !StringUtils.equals(this.action, ExtensionConstant.MULTI_DELETE)) {
						obj = getPersonalityBean(requestData, successResponse.getPersonId());
					}
					successResponse.setPersonId(null);
					successResponse.setPersonNumber(null);
					successResponse.setInput(obj);
				resultresponse.setSuccess(successResponse);
			} catch (Exception e) {
				errorOffsets.add(index[0]);
				// return error response
				RestErrorBean errorResponse = ResponseHandler.batchServiceErrorResponse(e, this.action);
				resultresponse.setError(errorResponse);
			}
			results.add(resultresponse);
		});
		return ResponseHandler.generateBatchResponse(detailresponse, results, errorOffsets);
	
	}

	private PersonalityBean getPersonalityBean(T requestData, Long personId ) {
		PersonalityBean personalityBean = (PersonalityBean) requestData;
		PersonIdentityBean identityBean = personalityBean.getPersonIdentity();
		if(null== identityBean) {
			identityBean = new PersonIdentityBean();
			identityBean.setPersonKey(personId);
			personalityBean.setPersonIdentity(identityBean);
		}
		return personalityBean;
	}

}
