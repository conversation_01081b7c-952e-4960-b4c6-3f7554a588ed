package com.kronos.persons.context.switchrole;

import java.util.List;

import jakarta.inject.Named;

import com.kronos.wfc.commonapp.people.business.person.delegateauthority.MultiManagerRoleAssignmentDTO;

@Named
public class RoleDelegateContext implements IRoleDelegateContext {

	private List<IDelegationsList> switchRoleDelegates;

	private String currentDelegationName;

	private String currentDelegationRole;

	private String currentTaskId;

	private boolean isMySelf;

	private IDelegationsList activeDelegateTask;

	private MultiManagerRoleAssignmentDTO activeRole;

	private List<MultiManagerRoleAssignmentDTO> myRoles;

	public RoleDelegateContext() {
	}

	/**
	 * get the list of switch role Delegates
	 */
	@Override
	public List<IDelegationsList> getSwitchRoleDelegates() {
		return switchRoleDelegates;
	}

	/**
	 * get the name of CurrentDelegation Name
	 * 
	 */
	@Override
	public String getCurrentDelegationName() {
		return currentDelegationName;
	}

	/**
	 * get the current delegation role
	 */
	@Override
	public String getCurrentDelegationRole() {
		return currentDelegationRole;
	}

	/**
	 * get the current task id
	 */
	@Override
	public String getCurrentTaskId() {
		return currentTaskId;
	}

	/**
	 * get the boolean value of myselfboolean
	 */
	@Override
	public boolean getIsMySelf() {
		return isMySelf;
	}

	/**
	 * @param switchRoleDelegates
	 *            The switchRoleDelegates to set.
	 */
	public void setSwitchRoleDelegates(List<IDelegationsList> switchRoleDelegates) {
		this.switchRoleDelegates = switchRoleDelegates;
	}

	/**
	 * @param currentDelegationName
	 *            The currentDelegationName to set.
	 */
	public void setCurrentDelegationName(String currentDelegationName) {
		this.currentDelegationName = currentDelegationName;
	}

	/**
	 * @param currentDelegationRole
	 *            The currentDelegationRole to set.
	 */
	public void setCurrentDelegationRole(String currentDelegationRole) {
		this.currentDelegationRole = currentDelegationRole;
	}

	/**
	 * @param currentTaskId
	 *            The currentTaskId to set.
	 */
	public void setCurrentTaskId(String currentTaskId) {
		this.currentTaskId = currentTaskId;
	}

	public void setIsMySelf(boolean isMySelf) {
		this.isMySelf = isMySelf;
	}


	@Override
	public IDelegationsList getActiveDelegateTask() {
		return activeDelegateTask;
	}

	public void setActiveDelegateTask(IDelegationsList activeDelegateTask) {
		this.activeDelegateTask = activeDelegateTask;
	}

	@Override
	public MultiManagerRoleAssignmentDTO getActiveRole() {
		return activeRole;
	}

	public void setActiveRole(MultiManagerRoleAssignmentDTO activeRole) {
		this.activeRole = activeRole;
	}

	@Override
	public List<MultiManagerRoleAssignmentDTO> getMyRoles() {
		return myRoles;
	}

	public void setMyRoles(List<MultiManagerRoleAssignmentDTO> myRoles) {
		this.myRoles = myRoles;
	}
}
