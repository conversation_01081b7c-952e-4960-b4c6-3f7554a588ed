package com.kronos.persons.utils;

import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.model.BeanWrapper;
import com.kronos.persons.rest.model.RestErrorBean;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

public class MultiRequestProcessor <E> implements Function<List<BeanWrapper<E>>, List<E>> {

    private final String entityName;
    private final String actionName;
    private final Consumer<BeanWrapper<E>> validator;
    private final Consumer<List<BeanWrapper<E>>> validEntityHandler;

    public MultiRequestProcessor(String entityName, String actionName, Consumer<BeanWrapper<E>> validator, Consumer<List<BeanWrapper<E>>> validEntityHandler) {
        this.entityName = entityName;
        this.actionName = actionName;
        this.validator = validator;
        this.validEntityHandler = validEntityHandler;
    }

    @Override
    public List<E> apply(List<BeanWrapper<E>> wrappers) {
        try {
            List<BeanWrapper<E>> validEntities = new ArrayList<>();
            for (BeanWrapper<E> wrapper : wrappers) {
                validator.accept(wrapper);
                if (Objects.isNull(wrapper.getApiException())) {
                    validEntities.add(wrapper);
                }
            }
            if (!validEntities.isEmpty()) {
                validEntityHandler.accept(validEntities);
            }
            return validEntities.stream()
                    .filter(beanWrapper -> Objects.isNull(beanWrapper.getApiException()))
                    .map(BeanWrapper::getBean)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            RestErrorBean restError = ExceptionHandler.handleException(e, entityName, actionName);
            throw PrsnException.getAPIException(restError);
        }
    }

}
