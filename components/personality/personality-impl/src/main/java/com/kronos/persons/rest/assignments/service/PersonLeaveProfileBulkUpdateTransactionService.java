package com.kronos.persons.rest.assignments.service;

import com.google.common.collect.Lists;
import com.kronos.wfc.absencemgmt.leave.business.config.LeaveProfile;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.SQLStatement;
import com.kronos.wfc.platform.persistence.framework.Transaction;
import com.kronos.wfc.platform.persistence.framework.statement.SqlString;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Transaction service for bulk update all persons with one {@link LeaveProfile}.
 * Copyright (C) 2019 Kronos.com
 * Date: Jun 25, 2019
 *
 * <AUTHOR>
 */
public final class PersonLeaveProfileBulkUpdateTransactionService extends Transaction {
    private static final String PERSON_LEAVE_PROFILE_ASSIGNMENT_BULK_UPDATE =
            "com.kronos.wfc.absencemgmt.leave.business.people.PersonLeaveProfileAssignment.bulkUpdate";
    private List<Personality> personalities;
    private LeaveProfile leaveProfile;

    private PersonLeaveProfileBulkUpdateTransactionService(LeaveProfile leaveProfile,
                                                           List<Personality> personalities) {
        this.personalities = personalities;
        this.leaveProfile = leaveProfile;
    }

    /**
     * Get new {@link PersonLeaveProfileBulkUpdateTransactionService}.
     *
     * @param leaveProfile  the leave profile
     * @param personalities the list of {@link Personality} what need to be update
     * @return Transaction service for update
     */
    public static PersonLeaveProfileBulkUpdateTransactionService
    getTransactionService(LeaveProfile leaveProfile, List<Personality> personalities) {
        return new PersonLeaveProfileBulkUpdateTransactionService(leaveProfile, personalities);
    }

    @Override
    protected void transaction() {
        update();
    }

    /**
     * Update leave profile for all employee by bulk update.
     */
    private void update() {
        List<String> personIds = personalities.stream()
                .map(personality -> personality.getPersonId().toString())
                .collect(Collectors.toList());

        SqlString commaSeparatedPersonIds = new SqlString(StringUtils.join(personIds, ","));
        SqlString profileId = new SqlString(leaveProfile.getId().toString());

        SQLStatement sqlStatement = new SQLStatement(SQLStatement.UPDATE, PERSON_LEAVE_PROFILE_ASSIGNMENT_BULK_UPDATE,
                Lists.newArrayList(profileId, commaSeparatedPersonIds));
        sqlStatement.execute();
    }
}
