package com.kronos.persons.rest.assignments.service;

import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.persons.rest.assignments.model.ProcessorToEmployeeAssignmentDTO;
import com.kronos.persons.rest.assignments.model.ProcessorToEmployeeCriteriaDTO;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.model.RestErrorBean;
import com.kronos.persons.utils.ExceptionHandler;
import com.kronos.persons.utils.NewBatchProcessor;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.totalizing.business.extensibility.validator.ExtensibilityProcessorValidator;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.util.List;
import java.util.function.UnaryOperator;

@Named
public class ProcessorToEmployeeAssignmentControllerImpl implements IRestProcessorToEmployeeAssignment {

    private static final String PROCESSOR_TO_EMPLOYEE_ASSIGNMENT = "ProcessorToEmployeeAssignment";

    private static final String CREATE_ACTION = "create";

    private static final String UPDATE_ACTION = "update";

    private static final String DELETE_ACTION = "delete";

    private static final KLogger LOGGER = KLoggerFactory.getKLogger(ProcessorToEmployeeAssignmentControllerImpl.class);

    @Inject
    private PersonAssignmentHelper<ProcessorToEmployeeAssignmentDTO> personAssignmentHelper;

    @Inject
    private ProcessorToEmployeeAssignmentService service;

    @Inject
    private ExtensibilityProcessorValidator processorValidator;

    public ProcessorToEmployeeAssignmentDTO createProcessorToEmployeeAssignment(
            ProcessorToEmployeeAssignmentDTO requestBean) {
        try {
            checkCommonValidation();
            ResponseHandler.validateForNullRequest(requestBean);
            Personality personality = service.createProcessorToEmployeeAssignment(requestBean);
            return ResponseHandler.apiSuccessResponse(requestBean, personality);
        } catch (APIException apiEx) {
            LOGGER.error("Error occured while processing the assign request", apiEx);
            throw apiEx;
        } catch (Exception e) {
            LOGGER.error("Error occured while processing the assign request", e);
            RestErrorBean restError =
                    ExceptionHandler.handleException(e, PROCESSOR_TO_EMPLOYEE_ASSIGNMENT, CREATE_ACTION);
            throw PrsnException.getAPIException(restError);
        }
    }

    public ProcessorToEmployeeAssignmentDTO updateProcessorToEmployeeAssignment(
            ProcessorToEmployeeAssignmentDTO requestBean) {
        try {
            checkCommonValidation();
            ResponseHandler.validateForNullRequest(requestBean);
            Personality personality = service.updateProcessorToEmployeeAssignment(requestBean);
            return ResponseHandler.apiSuccessResponse(requestBean, personality);
        } catch (APIException apiEx) {
            LOGGER.error("Error occured while processing the update request", apiEx);
            throw apiEx;
        } catch (Exception e) {
            LOGGER.error("Error occured while processing the update request", e);
            RestErrorBean restError =
                    ExceptionHandler.handleException(e, PROCESSOR_TO_EMPLOYEE_ASSIGNMENT, UPDATE_ACTION);
            throw PrsnException.getAPIException(restError);
        }
    }

    public List<ProcessorToEmployeeAssignmentDTO> multiUpdateProcessorToEmployeeAssignments(
            List<ProcessorToEmployeeAssignmentDTO> requestDataList) {
        processorValidator.checkIfListIsNotEmpty(requestDataList);
        checkCommonValidation();
        ResponseHandler.validateForNullRequest(requestDataList);
        ResponseHandler.validServiceLimit(requestDataList);
        UnaryOperator<ProcessorToEmployeeAssignmentDTO> requestProcessor = (
                ProcessorToEmployeeAssignmentDTO reqData) -> {
            try {
                Personality personality = service.updateProcessorToEmployeeAssignment(reqData);
                return ResponseHandler.apiSuccessResponse(reqData, personality);
            } catch (APIException apiEx) {
                throw apiEx;
            } catch (Exception e) {
                RestErrorBean restError =
                        ExceptionHandler.handleException(e, PROCESSOR_TO_EMPLOYEE_ASSIGNMENT, UPDATE_ACTION);
                throw PrsnException.getAPIException(restError);
            }

        };
        NewBatchProcessor<ProcessorToEmployeeAssignmentDTO, ProcessorToEmployeeAssignmentDTO> batchprocessor =
                new NewBatchProcessor<>(requestDataList, requestProcessor);
        return batchprocessor.process();
    }

    public List<ProcessorToEmployeeAssignmentDTO> multiCreateProcessorToEmployeeAssignments(
            List<ProcessorToEmployeeAssignmentDTO> requestDataList) {
        processorValidator.checkIfListIsNotEmpty(requestDataList);
        checkCommonValidation();
        ResponseHandler.validateForNullRequest(requestDataList);
        ResponseHandler.validServiceLimit(requestDataList);
        UnaryOperator<ProcessorToEmployeeAssignmentDTO> requestProcessor = (
                ProcessorToEmployeeAssignmentDTO reqData) -> {
            try {
                Personality personality = service.createProcessorToEmployeeAssignment(reqData);
                return ResponseHandler.apiSuccessResponse(reqData, personality);
            } catch (APIException apiEx) {
                throw apiEx;
            } catch (Exception e) {
                RestErrorBean restError = ExceptionHandler.handleException(e, PROCESSOR_TO_EMPLOYEE_ASSIGNMENT,
                        CREATE_ACTION);
                throw PrsnException.getAPIException(restError);
            }
        };
        NewBatchProcessor<ProcessorToEmployeeAssignmentDTO, ProcessorToEmployeeAssignmentDTO> batchprocessor =
                new NewBatchProcessor<>(requestDataList, requestProcessor);
        return batchprocessor.process();
    }

    public void multiDeleteProcessorToEmployeeAssignments(List<ProcessorToEmployeeCriteriaDTO> criteriaBeanList) {
        processorValidator.checkIfListIsNotEmpty(criteriaBeanList);
        checkCommonValidation();
        ResponseHandler.validateForNullRequest(criteriaBeanList);
        ResponseHandler.validServiceLimit(criteriaBeanList);
        UnaryOperator<ProcessorToEmployeeCriteriaDTO> requestProcessor = (
                ProcessorToEmployeeCriteriaDTO criteriaBean) -> {
            try {
                Personality personality = service.deleteProcessorToEmployeeAssignment(criteriaBean);
                return ResponseHandler.apiSuccessResponse(criteriaBean, personality);
            } catch (APIException apiEx) {
                throw apiEx;
            } catch (Exception e) {
                RestErrorBean restError =
                        ExceptionHandler.handleException(e, PROCESSOR_TO_EMPLOYEE_ASSIGNMENT, DELETE_ACTION);
                throw PrsnException.getAPIException(restError);
            }
        };
        NewBatchProcessor<ProcessorToEmployeeCriteriaDTO, ProcessorToEmployeeCriteriaDTO> batchprocessor =
                new NewBatchProcessor<>(criteriaBeanList, requestProcessor);
        batchprocessor.process();
    }

    public void deleteProcessorToEmployeeAssignment(ProcessorToEmployeeCriteriaDTO criteriaBean) {
        try {
            checkCommonValidation();
            ResponseHandler.validateForNullRequest(criteriaBean);
            service.deleteProcessorToEmployeeAssignment(criteriaBean);
        } catch (APIException apiEx) {
            throw apiEx;
        } catch (Exception e) {
            LOGGER.error("Error occured while processing the delete request", e);
            RestErrorBean restError =
                    ExceptionHandler.handleException(e, PROCESSOR_TO_EMPLOYEE_ASSIGNMENT, DELETE_ACTION);
            throw PrsnException.getAPIException(restError);
        }
    }

    public List<ProcessorToEmployeeAssignmentDTO> getProcessorToEmployeeAssignmentByPersonId(Long personId) {
        try {
            checkCommonValidation();
            return service.getProcessorToEmployeeAssignmentsByParams(personId, null, null, null);
        } catch (APIException apiEx) {
            throw apiEx;
        } catch (Exception e) {
            RestErrorBean restError =
                    ExceptionHandler.handleException(e, PROCESSOR_TO_EMPLOYEE_ASSIGNMENT, DELETE_ACTION);
            throw PrsnException.getAPIException(restError);
        }

    }


    public List<ProcessorToEmployeeAssignmentDTO> getProcessorToEmployeeAssignmentsByParams(
            String personNumber, String processor, String effectivedate) {
        try {
            checkCommonValidation();
            return service.getProcessorToEmployeeAssignmentsByParams(null, personNumber, processor, effectivedate);
        } catch (APIException apiEx) {
            throw apiEx;
        } catch (Exception e) {
            RestErrorBean restError =
                    ExceptionHandler.handleException(e, PROCESSOR_TO_EMPLOYEE_ASSIGNMENT, DELETE_ACTION);
            throw PrsnException.getAPIException(restError);
        }
    }

    private void checkCommonValidation() {
        processorValidator.checkTotalizerExtensionProcessorConfiguration();
    }
}
