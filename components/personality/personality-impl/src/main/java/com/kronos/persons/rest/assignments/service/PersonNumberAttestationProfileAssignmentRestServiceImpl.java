package com.kronos.persons.rest.assignments.service;

import com.kronos.authz.api.annotations.IsPermitted;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.EmployeeAssignmentsConstants;
import com.kronos.persons.rest.assignments.model.AttestationProfileAssignRoleSelect;
import com.kronos.persons.rest.assignments.model.EmployeeRefs;
import com.kronos.persons.rest.assignments.model.PersonAttestationAssignmentCriteria;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignment;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignmentDTO;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignmentRequest;
import com.kronos.persons.rest.assignments.validation.BulkPersonAttestationProfileAssignmentValidator;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.utils.BulkProcessingHelper;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.commonapp.people.business.personality.delete.PersonNotification;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class PersonNumberAttestationProfileAssignmentRestServiceImpl
    implements PersonNumberAttestationProfileAssignmentRestService {

    @Inject
    private PersonAttestationProfileAssignmentService personAttestationProfileAssignmentService;
    @Inject
    private BulkProcessingHelper bulkProcessingHelper;
    @Inject
    private BulkPersonAttestationProfileAssignmentValidator bulkPersonAttestationProfileAssignmentValidator;

    @Override
    @IsPermitted(accessControlPoint = EmployeeAssignmentsConstants.EMPLOYEE_ASSIGNMENTS_READ, action = EmployeeAssignmentsConstants.ALLOWED)
    public Response retrieveByPersonNumber(String personNumber, boolean isAssignToManagerRole) {
        return Response.ok(personAttestationProfileAssignmentService.getAttestationProfileAssignments(personNumber, isAssignToManagerRole))
            .build();
    }

    @Override
    @IsPermitted(accessControlPoint = EmployeeAssignmentsConstants.EMPLOYEE_ASSIGNMENTS_EDIT, action = EmployeeAssignmentsConstants.ALLOWED)
    public Response create(PersonAttestationProfileAssignmentDTO dto) {
        PersonAttestationProfileAssignmentDTO result = personAttestationProfileAssignmentService.create(dto);
        return Response.status(Response.Status.CREATED)
            .entity(result)
            .build();
    }

    @Override
    public Response multiUpdate(List<PersonAttestationProfileAssignment> request, Boolean mergeEffectiveDating) {
        validateUpdateRequest(request);
        Map<Integer, PersonAttestationProfileAssignment> personAttestationProfileAssignments = bulkProcessingHelper.numberInput(request);
        Map<Integer, PersonAttestationProfileAssignment> requestSnapshot = new HashMap<>(personAttestationProfileAssignments);
        Map<Integer, APIException> exceptionHolder = new HashMap<>();
        bulkProcessingHelper.resolveAndValidateEmployees(personAttestationProfileAssignments, exceptionHolder, requestSnapshot);
        bulkPersonAttestationProfileAssignmentValidator.checkDuplicates(personAttestationProfileAssignments, exceptionHolder);
        Map<Integer, PersonAttestationProfileAssignment> result = continueWithValidPersonAttestationProfileAssignments(personAttestationProfileAssignments, exceptionHolder, mergeEffectiveDating);
        sendUpdateNotificationsToEvictCache(getEmployeeIds(result), true);

        return Response.status(Response.Status.CREATED)
                .entity(bulkProcessingHelper.createMultiUpsertResponse(requestSnapshot, result, exceptionHolder))
                .build();
    }

    @Override
    public List<PersonAttestationProfileAssignment> multiRead(PersonAttestationProfileAssignmentRequest request) {
        validateReadRequest(request);
        bulkPersonAttestationProfileAssignmentValidator.checkFAPPermissions();
        PersonAttestationAssignmentCriteria criteria = buildCriteria(request.getSelect());
        EmployeeRefs requestRef = request.getWhere();
        bulkPersonAttestationProfileAssignmentValidator.validateMultiReadRequest(requestRef);
        bulkPersonAttestationProfileAssignmentValidator.checkServiceLimitForMultiReadOperation(requestRef);
        return personAttestationProfileAssignmentService.getPersonAttestationProfileAssignment(requestRef, criteria);
    }

    private PersonAttestationAssignmentCriteria buildCriteria(AttestationProfileAssignRoleSelect select) {
        PersonAttestationAssignmentCriteria criteria = new PersonAttestationAssignmentCriteria();
        if (select == null) {
            criteria.setIncludeEmployeeRole(true);
        } else if (AttestationProfileAssignRoleSelect.ALL.equals(select)) {
            criteria.setIncludeEmployeeRole(true);
            criteria.setIncludeManagerRole(true);
        } else {
            criteria.setIncludeManagerRole(AttestationProfileAssignRoleSelect.MANAGER_ROLE.equals(select));
            criteria.setIncludeEmployeeRole(AttestationProfileAssignRoleSelect.EMPLOYEE_ROLE.equals(select));
        }
        return criteria;
    }

    private void validateUpdateRequest(List<PersonAttestationProfileAssignment> request) {
        bulkPersonAttestationProfileAssignmentValidator.validateRequestBody(request);
        bulkPersonAttestationProfileAssignmentValidator.checkFAPPermissions();
        bulkPersonAttestationProfileAssignmentValidator.checkServiceLimitForMultiUpdateOperation(request);
    }

    private void validateReadRequest(PersonAttestationProfileAssignmentRequest request) {
        if (Objects.isNull(request)) {
            throw new APIException(ExceptionConstants.EMPTY_MULTI_READ_REQUEST);
        }
    }

    private Map<Integer, PersonAttestationProfileAssignment> continueWithValidPersonAttestationProfileAssignments(Map<Integer, PersonAttestationProfileAssignment> personAttestationProfileAssignments,
                                                                                                                  Map<Integer, APIException> exceptionHolder,Boolean mergeEffectiveDating) {
        return personAttestationProfileAssignments.isEmpty() ? personAttestationProfileAssignments :
                personAttestationProfileAssignmentService
                        .multiUpsert(exceptionHolder, personAttestationProfileAssignments,mergeEffectiveDating);
    }

    private void sendUpdateNotificationsToEvictCache(List<String> employeeIds, Boolean evictFromNewPersonCache) {
        if (!CollectionUtils.isEmpty(employeeIds)) {
            PersonNotification.sendBulkUpdate(employeeIds, evictFromNewPersonCache);
        }
    }

    private List<String> getEmployeeIds(Map<Integer, PersonAttestationProfileAssignment> attestationProfileAssignments) {
        return attestationProfileAssignments.values().stream()
            .map(pap -> pap.getEmployee().getId().toString())
            .collect(Collectors.toList());
    }
}
