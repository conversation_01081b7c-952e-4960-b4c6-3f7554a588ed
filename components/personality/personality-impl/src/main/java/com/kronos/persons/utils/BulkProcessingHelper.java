package com.kronos.persons.utils;

import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.container.api.exception.APIException;
import com.kronos.container.api.util.APIExceptionDetailResult;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignment;
import com.kronos.persons.rest.assignments.validation.PersonAttestationProfileAssignmentValidator;
import com.kronos.persons.rest.beans.HyperFindFilterBean;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.kronos.persons.rest.exception.ExceptionConstants.ALL_RECORDS_FAILED;
import static com.kronos.persons.rest.exception.ExceptionConstants.PARTIAL_SUCCESS;

@Component
public class BulkProcessingHelper {

    @Inject
    @Lazy
    private PersonAttestationProfileAssignmentValidator personAttestationProfileAssignmentValidator;


    public List<PersonAttestationProfileAssignment> createMultiUpsertResponse(Map<Integer, PersonAttestationProfileAssignment> requestSnapshot, Map<Integer, PersonAttestationProfileAssignment> result, Map<Integer, APIException> exceptionHolder) {
        if (noFailures(exceptionHolder)) {
            return new ArrayList<>(result.values());
        } else {
            prepareAndThrowAPIException(requestSnapshot, result, exceptionHolder);
        }
        return new ArrayList<>();
    }

    public void resolveAndValidateEmployees(Map<Integer, PersonAttestationProfileAssignment> assignments,
                                            Map<Integer, APIException> exceptionHolder,
                                            Map<Integer, PersonAttestationProfileAssignment> requestSnapshot) {


        Map<Long, HyperFindFilterBean> unresolvedEmployeeIds = new HashMap<>();
        Map<String, HyperFindFilterBean> unresolvedEmployeeQualifiers = new HashMap<>();

        extractEmployeeObjectRefs(assignments, exceptionHolder, requestSnapshot, unresolvedEmployeeIds, unresolvedEmployeeQualifiers);

        final Map<Long, Object> employeesRetrievedByIds = personAttestationProfileAssignmentValidator
                .validateReadAccessAndGetObjectRefsByPersonIds(unresolvedEmployeeIds);
        final Map<String, Object> employeesRetrievedByQualifiers = personAttestationProfileAssignmentValidator
                .validateReadAccessAndGetObjectRefsByPersonNums(unresolvedEmployeeQualifiers);

        mapAssignmentsWithResolvedEmployees(assignments, employeesRetrievedByIds, employeesRetrievedByQualifiers, exceptionHolder);
    }

    private void extractEmployeeObjectRefs(Map<Integer, PersonAttestationProfileAssignment> assignments, Map<Integer, APIException> exceptionHolder,
                                           Map<Integer, PersonAttestationProfileAssignment> requestSnapshot, Map<Long, HyperFindFilterBean> unresolvedEmployeeIds,
                                           Map<String, HyperFindFilterBean> unresolvedEmployeeQualifiers) {
        List<Integer> failedElementIds = new ArrayList<>();
        assignments.forEach((key, assignment) -> {
            ObjectRef employee = assignment.getEmployee();
            if (!validateEmployeeNotNull(exceptionHolder, requestSnapshot, key, employee)) {
                failedElementIds.add(key);
                return;
            }
            if (employee.getId() != null) {
                unresolvedEmployeeIds.put(employee.getId(), assignment.getHyperFindFilter());
            } else {
                unresolvedEmployeeQualifiers.put(employee.getQualifier(), assignment.getHyperFindFilter());
            }
        });
        assignments.keySet().removeAll(failedElementIds);
    }

    private boolean validateEmployeeNotNull(Map<Integer, APIException> exceptionHolder, Map<Integer, PersonAttestationProfileAssignment> requestSnapshot,
                                            Integer key, ObjectRef employee) {
        boolean answer = true;
        try {
            personAttestationProfileAssignmentValidator.validatePersonNotNullReference(employee);
        } catch (APIException e) {
            exceptionHolder.put(key, e);
            requestSnapshot.get(key).setEmployee(null);
            answer = false;
        }
        return answer;
    }


    public Map<Integer, PersonAttestationProfileAssignment> numberInput(List<PersonAttestationProfileAssignment> input) {
        int start = 1;
        Map<Integer, PersonAttestationProfileAssignment> numberedValues = new HashMap<>();

        for (PersonAttestationProfileAssignment personAttestationProfileAssignment : input) {
            numberedValues.put(start, personAttestationProfileAssignment);
            start++;
        }
        return numberedValues;
    }


    private void mapAssignmentsWithResolvedEmployees(Map<Integer, PersonAttestationProfileAssignment> assignments,
                                                     Map<Long, Object> employeesRetrievedByIds,
                                                     Map<String, Object> employeesRetrievedByQualifiers,
                                                     Map<Integer, APIException> exceptionHolder) {
        List<Integer> failedElementIds = new ArrayList<>();
        assignments.forEach((key, assignment) -> {
            final ObjectRef employee = assignment.getEmployee();
            final Long employeeId = employee.getId();
            final String employeeQualifier = employee.getQualifier();
            Object employeeSearchResult = null;
            if (employeeId != null) {
                employeeSearchResult = employeesRetrievedByIds.get(employeeId);
            } else {
                employeeSearchResult = employeesRetrievedByQualifiers.get(employeeQualifier);
            }
            if (employeeSearchResult instanceof APIException) {
                exceptionHolder.put(key, (APIException) employeeSearchResult);
                failedElementIds.add(key);
            } else {
                assignment.setEmployee((ObjectRef) employeeSearchResult);
            }
        });
        assignments.keySet().removeAll(failedElementIds);
    }


    private boolean noFailures(Map<Integer, APIException> exceptionHolder) {
        return exceptionHolder.isEmpty();
    }

    private void prepareAndThrowAPIException(Map<Integer, PersonAttestationProfileAssignment> requestSnapshot, Map<Integer, PersonAttestationProfileAssignment> result, Map<Integer, APIException> exceptionHolder) {
        final String ERROR_CODE = requestSnapshot.size() == exceptionHolder.size() ? ALL_RECORDS_FAILED : PARTIAL_SUCCESS;
        APIException errorResponse = new APIException(ERROR_CODE);
        errorResponse.setResults(requestSnapshot.entrySet().stream().map(a -> createErrorSegment(a.getKey(), a.getValue(), result, exceptionHolder)).collect(Collectors.toList()));
        throw errorResponse;
    }

    private APIExceptionDetailResult<PersonAttestationProfileAssignment> createErrorSegment(Integer key, PersonAttestationProfileAssignment value, Map<Integer, PersonAttestationProfileAssignment> result, Map<Integer, APIException> exceptionHolder) {
        final APIException exception = exceptionHolder.get(key);
        if (exception != null) {
            exception.setInputDetail(value);
            return new APIExceptionDetailResult<>(exception);
        } else {
            return new APIExceptionDetailResult<>(result.get(key));
        }
    }
}
