/***********************************************************************
 * ApplicationContextServiceImpl.java
 *
 * Copyright 2017, Kronos Incorporated. All rights reserved.
 **********************************************************************/
package com.kronos.persons.context.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.commonapp.authz.api.fap.permissions.IPermission;
import com.kronos.commonapp.authz.impl.fap.permissions.Permission;
import com.kronos.commonapp.userpreferences.api.IKPreferences;
import com.kronos.persons.context.utils.CurrentUserUtil;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.user.CurrentUserAccountManager;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.business.authorization.profiles.AccessProfile;
import com.kronos.wfc.platform.security.business.state.State;

/**
 * This {@code ApplicationContextServiceImpl} is the implementation of
 * Application Context service.
 * 
 * <AUTHOR>
 *
 */
@Named
public class ApplicationContextManagerImpl implements IApplicationContextManager {

	public static final String PERMISSION_CONTEXT = "PERMISSION_CONTEXT";

	@Inject
	IKPreferences pref;

	@Inject
	CurrentUserUtil currentUserUtil;

	/**
	 * Returns the current logged-in user
	 * 
	 * @return CurrentUser
	 */
	protected CurrentUser getCurrentLoggedInUser() {
		return currentUserUtil.getCurrentLoggedInUser();
	}

	/**
	 * Returns an instance of Personality using current useraccount manager
	 * 
	 * @return personality the personality
	 * 
	 */
	protected Personality getPersonality() {
		return CurrentUserAccountManager.getPersonality();
	}

	/**
	 * Returns the list of permissions
	 * 
	 * @param managerContext
	 *            the manager context
	 * @return permissions the permissions
	 * 
	 */
	@Override
	public List<IPermission> getPermissions(boolean managerContext) {
		List<IPermission> permissions = new ArrayList<>();
		Personality personality = getPersonality();
		Map hashPerm = getHashDataForPermission(personality);
		Collection wfcPermissions = hashPerm.values();
		Long contextId = personality.getPersonId().toLong();
		if (managerContext) {
			// if manager context, the context id must be different from the
			// manager person id
			contextId = contextId + 1;
		}
		ObjectIdLong contextObjectId = new ObjectIdLong(contextId);
		for (Object p : wfcPermissions) {
			com.kronos.wfc.platform.security.business.authorization.permissions.Permission wfcPerm = (com.kronos.wfc.platform.security.business.authorization.permissions.Permission) p;
			getPermissionsList(permissions, personality, contextObjectId, wfcPerm);
		}
		return permissions;
	}

	/**
	 * @param personality
	 *            the personality
	 * @return Hashtable the hashtable
	 */
	protected Hashtable getHashDataForPermission(Personality personality) {
		return personality.getAccessAssignment().getAccessProfile().getProfilePermissions();
	}

	/**
	 * @param permissions
	 *            the permissions
	 * @param personality
	 *            the personality
	 * @param contextObjectId
	 *            the contextObjectId
	 * @param wfcPerm
	 *            the wfcPerm
	 */
	protected void getPermissionsList(List<IPermission> permissions, Personality personality,
			ObjectIdLong contextObjectId,
			com.kronos.wfc.platform.security.business.authorization.permissions.Permission wfcPerm) {
		if (AccessProfile.isPermitted(personality, wfcPerm.getAccessControlPoint().getName(),
				wfcPerm.getAction().getName(), contextObjectId)) {
			permissions.add(new Permission(wfcPerm.getAccessControlPoint().getName(), wfcPerm.getAction().getName(),
					wfcPerm.getScope().getName()));
		}
	}

	protected ApplicationContextImpl setApplicationData() {
		ApplicationContextImpl applicationContextDto = new ApplicationContextImpl();
		applicationContextDto.setPreference(pref);
		applicationContextDto.setCurrentUser(getCurrentLoggedInUser());
		applicationContextDto.setPermission(getPermissions(false));
		applicationContextDto.setManagerPermission(getPermissions(true));
		return applicationContextDto;
	}


	/**
	 * get data from session cache
	 * @param applicationContext
	 * @return {@link}
	 */
	public Object getFromSessionCache(String applicationContext) {
		return State.getFromSessionState(applicationContext);
	}

	/**
	 * Method to put data in session cache
	 * @param applicationContext
	 *            application context
	 * @param context
	 *            the context passed
	 */
	protected void putInSessionCache(String applicationContext, ApplicationContextImpl context) {
		State.putInSessionState(applicationContext, context);
	}

	@Override
	public IApplicationContext getApplicationContext() {
		ApplicationContextImpl contextFromSessionCache = (ApplicationContextImpl) getFromSessionCache(
				PERMISSION_CONTEXT);
		if (contextFromSessionCache != null) {
			return contextFromSessionCache;
		}
		contextFromSessionCache = setApplicationData();
		putInSessionCache(PERMISSION_CONTEXT, contextFromSessionCache);
		return contextFromSessionCache;
	}


	/**
	 * method to remove the key from sessionCache
	 * @param key
	 */
	public void removeFromSessionCache(String key) {
		State.removeFromSessionState(key);

	}
}
