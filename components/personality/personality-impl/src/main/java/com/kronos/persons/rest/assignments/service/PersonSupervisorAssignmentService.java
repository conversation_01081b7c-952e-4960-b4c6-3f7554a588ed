package com.kronos.persons.rest.assignments.service;

import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.people.personality.dataaccess.entity.WtkEmployee;
import com.kronos.people.personality.dataaccess.repository.WTKPeopleRepository;
import com.kronos.people.personality.service.PersonalityService;
import com.kronos.persons.rest.assignments.model.RestEntity;
import com.kronos.persons.rest.assignments.model.SupervisorAssignmentRequestBean;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.exception.PrsnPersistenceException;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.business.authorization.profiles.AccessProfile;
import com.kronos.wfc.platform.security.shared.SecurityConstants;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Named
public class PersonSupervisorAssignmentService extends RestEntity {

	private static final KLogger LOGGER = KLoggerFactory.getKLogger(PersonSupervisorAssignmentService.class);

	private static final String SUPERVISOR = "supervisor";

	private static final String ACP_PE_REPORTS_TO_FIELD = "PE_REPORTS_TO_FIELD";

	@Inject
	ValidatorUtils validatorUtils;

	@Inject
	PersonIdentityBeanValidator personIdentityBeanValidator;

	@Inject
	WTKPeopleRepository wtkPeopleRepository;

	@Inject
	PersonalityService personalityService;

	public SupervisorAssignmentRequestBean retrieve(PersonIdentityBean identityBean) {
		Personality personality = validateAndGetPerson(identityBean);
		validatePermission(ACP_PE_REPORTS_TO_FIELD, SecurityConstants.VIEW);
		return getAssignmentForPerson(personality);
	}

	private SupervisorAssignmentRequestBean getAssignmentForPerson(Personality personality) {
		SupervisorAssignmentRequestBean responseBean = new SupervisorAssignmentRequestBean();
		Optional<List<WtkEmployee>> optWtkEmployeeList = wtkPeopleRepository.findByPersonId(personality.getPersonId().toLong());

		if (!optWtkEmployeeList.isPresent() || optWtkEmployeeList.get().isEmpty()){
			throw PrsnPersistenceException.personNotFound(PersonIdentityBeanValidator.PERSON_ID,personality.getPersonId().toString());
		}

		responseBean.setPersonIdentity(personIdentityBeanValidator.createForPersonNumber(personality.getPersonNumber()));

		ObjectIdLong supervisorId = new ObjectIdLong(optWtkEmployeeList.get().get(0).getSupervisorId());

		if (validatorUtils.notNull(supervisorId) && !supervisorId.isNull()) {
			Person pObj = Person.getByPersonId(supervisorId);
            responseBean.setSupervisorPersonNumber(pObj.getPersonNumber());
			responseBean.setSupervisorName(pObj.getFullName());
		}
		return responseBean;
	}

	private Personality validateAndGetPerson(PersonIdentityBean identityBean) {
		personIdentityBeanValidator.newvalidate(null, identityBean, null);
		return personIdentityBeanValidator.getPersonality(identityBean);
	}

	public Personality updateRequest(SupervisorAssignmentRequestBean requestBean) {

		personIdentityBeanValidator.newvalidate(null, requestBean.getPersonIdentity(), null);
		validatePermission(ACP_PE_REPORTS_TO_FIELD, SecurityConstants.ADD);
		Personality personality = personIdentityBeanValidator.getPersonality(requestBean.getPersonIdentity());

		if (validateUnAssignExisting(requestBean)){
			Optional<List<WtkEmployee>> optWtkEmployeeList = wtkPeopleRepository.findByPersonId(ObjectIdLong.longValue(personality.getPersonId()));
			if (optWtkEmployeeList.isPresent() && !optWtkEmployeeList.get().isEmpty()){
				WtkEmployee wtkEmployee = optWtkEmployeeList.get().get(0);
				if (Objects.isNull(requestBean.getPersonIdentity()) ) requestBean.setPersonIdentity(new PersonIdentityBean());
				setSupervisor(wtkEmployee, requestBean);
				wtkPeopleRepository.save(wtkEmployee);
				return personality;
			}else{
				throw PrsnPersistenceException.personNotFound(PersonIdentityBeanValidator.PERSON_NUMBER,requestBean.getSupervisorPersonNumber());
			}
		}else{
			return null;
		}
	}


	private void setSupervisor(WtkEmployee wtkEmployee, SupervisorAssignmentRequestBean supervisor) {
		validatePermission(ACP_PE_REPORTS_TO_FIELD, SecurityConstants.ADD);
		if (Objects.isNull(supervisor)
				|| Objects.isNull(supervisor.getSupervisorPersonNumber())
				|| supervisor.getSupervisorPersonNumber().equalsIgnoreCase("null")
				|| supervisor.getSupervisorPersonNumber().equals("")) {
				wtkEmployee.setSupervisorId(null);
				return;
		}
		Personality admin;
		try{
			PersonIdentityBean personIdentityBean = new PersonIdentityBean();
			personIdentityBean.setPersonNumber(supervisor.getSupervisorPersonNumber());
			admin = personIdentityBeanValidator.getPersonality(personIdentityBean);
		}catch (Exception io){
			throw PrsnPersistenceException.personNotFound(PersonIdentityBeanValidator.PERSON_NUMBER,supervisor.getSupervisorPersonNumber());
		}
		if(!personalityService.isManager(admin.getPersonId().toLong())){
			throw PrsnPersistenceException.personNotManager(admin.getPersonId().toString());
		}
		ObjectIdLong adminId = admin.getPersonId();
		wtkEmployee.setSupervisorId(ObjectIdLong.longValue(adminId));
	}

	private void validatePermission(String accessControlPointName,String action) {
		if (!AccessProfile.isPermitted(accessControlPointName, action)) {
			LOGGER.error("The current User is not authorized to access this operation {0} ACP: {1} Action: {2}", SUPERVISOR, accessControlPointName, action);
			throw new APIException(ExceptionConstants.UNAUTHORIZED_ERROR);
		}
	}

	private boolean validateUnAssignExisting(SupervisorAssignmentRequestBean requestBean) {
		if(!Objects.isNull(requestBean) && (Objects.isNull(requestBean.getUnAssignExisting()) || !requestBean.getUnAssignExisting())){
			return false;
		}
		return true;
	}

}
