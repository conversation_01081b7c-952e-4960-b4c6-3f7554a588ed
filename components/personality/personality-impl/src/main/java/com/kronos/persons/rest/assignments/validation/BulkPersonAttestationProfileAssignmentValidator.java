package com.kronos.persons.rest.assignments.validation;

import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.commonbusiness.datatypes.ref.ObjectRef;
import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.EmployeeRefs;
import com.kronos.persons.rest.assignments.model.PersonAttestationProfileAssignment;
import com.kronos.persons.rest.beans.validator.HyperFindFilterBeanValidator;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.persons.utils.ResponseHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.kronos.persons.rest.exception.ExceptionConstants.DUPLICATE_EMPLOYEE;
import static com.kronos.persons.rest.exception.ExceptionConstants.EMPTY_MULTI_UPDATE_REQUEST;

@Component
public class BulkPersonAttestationProfileAssignmentValidator {

    private static final String ACP_PERSON = "PERSON";
    private static final String DEFAULT_RECORD_BATCH_SIZE = "400";
    private static final String RECORD_BATCH_SIZE_MULTI_READ = "com.kronos.persons.rest.assignments.service.multiread.request.size";
    private static final String RECORD_BATCH_SIZE_MULTI_UPDATE = "com.kronos.persons.rest.assignments.service.multiupdate.request.size";
    private static final String REQUEST_EMPLOYEES = "employees";
    @Inject
    @Lazy
    private IKProperties ikProperties;

    @Inject
    @Lazy
    private HyperFindFilterBeanValidator hyperFindFilterBeanValidator;


    public void checkDuplicates(Map<Integer, PersonAttestationProfileAssignment> personAttestationProfileAssignments,
                                Map<Integer, APIException> exceptionHolder) {
        Set<ObjectRef> uniqueEmployees = new HashSet<>();
        final Set<Map.Entry<Integer, PersonAttestationProfileAssignment>> entries = personAttestationProfileAssignments.entrySet();
        entries.stream().filter(e -> !uniqueEmployees.add(e.getValue().getEmployee())).collect(Collectors.toList())
                .forEach(e -> {
                    exceptionHolder.put(e.getKey(), new APIException(DUPLICATE_EMPLOYEE));
                    personAttestationProfileAssignments.remove(e.getKey());
                });

    }

    public void validateRequestBody(List<PersonAttestationProfileAssignment> request) {
        if (CollectionUtils.isEmpty(request)) {
            throw new APIException(EMPTY_MULTI_UPDATE_REQUEST);
        }
    }

    public void checkFAPPermissions() {
        ResponseHandler.validateACP(ACP_PERSON);
    }

    public void checkServiceLimitForMultiUpdateOperation(List<PersonAttestationProfileAssignment> request) {
        validateServiceLimit(RECORD_BATCH_SIZE_MULTI_UPDATE,request);
    }

    public void validateMultiReadRequest(EmployeeRefs employees) {
        if (employees == null || employees.getEmployees() == null) {
            throw PrsnValidationException.invalidPropertyValue(REQUEST_EMPLOYEES, null);
        }
        hyperFindFilterBeanValidator.validateHyperFindFilter(employees.getHyperFindFilter());
    }

    public void checkServiceLimitForMultiReadOperation(EmployeeRefs employees) {
        validateServiceLimit(RECORD_BATCH_SIZE_MULTI_READ, new ArrayList(employees.getEmployees().getExclusiveObjectRefList()));
    }

    private void validateServiceLimit(String operationBatchSize, List requestElements){
        int batchSize = Integer.parseInt(ikProperties.getProperty(operationBatchSize, DEFAULT_RECORD_BATCH_SIZE));
        ResponseHandler.validateServiceLimit(new ArrayList<>(requestElements), batchSize);
    };
}
