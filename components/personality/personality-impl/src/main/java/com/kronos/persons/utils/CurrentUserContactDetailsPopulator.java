package com.kronos.persons.utils;

import static com.kronos.people.personality.dataaccess.adapter.AdapterHelper.runConsumerIfPredicatePass;
import static com.kronos.persons.utils.ExtensionUtils.getNullorEmptyCheckPredicate;
import static com.kronos.persons.utils.ExtensionUtils.isNull;
import static com.kronos.persons.utils.ExtensionUtils.longToObjectIdLong;
import java.util.Collection;
import java.util.stream.Collectors;
import jakarta.inject.Named;

import com.kronos.people.personality.model.extension.EmployeeExtension;
import com.kronos.people.personality.model.extension.entry.ContactEntry;
import com.kronos.people.personality.model.extension.entry.PostalAddressEntry;
import com.kronos.persons.rest.beans.extensions.ContactDataExtension;
import com.kronos.persons.rest.beans.extensions.PostalAddressDataExtension;
import com.kronos.persons.rest.model.UserInfoBean;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.types.business.EMailAddressType;
import com.kronos.wfc.commonapp.types.business.PostalAddressType;
import com.kronos.wfc.commonapp.types.business.TelephoneNumberType;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;

@Named
public class CurrentUserContactDetailsPopulator {

   public void populateContactDetails(UserInfoBean userInfoBean, EmployeeExtension extension,
         Personality personality) {

      Collection<ContactEntry> emailContactEntries = extension.getEmailContactEntries();
      if (emailContactEntries != null && !emailContactEntries.isEmpty()) {
         populateEmailAddress(userInfoBean, emailContactEntries, personality);

      }
      runConsumerIfPredicatePass(extension.getTelContactEntries(), getNullorEmptyCheckPredicate(), userInfoBean,
            this::populateTelephoneNumber);
      runConsumerIfPredicatePass(extension.getPostalAddressEntries(), getNullorEmptyCheckPredicate(),
            userInfoBean, this::populatePostalAddress);

   }

   private void populateTelephoneNumber(UserInfoBean userInfoBean, Collection<ContactEntry> telephoneEntries) {
      Collection<ContactDataExtension> telephoneContactList = telephoneEntries.stream().map(telephone -> {

         TelephoneNumberType type = TelephoneNumberType.getTelephoneNumberType(longToObjectIdLong(telephone.getContactTypeId()));
         ContactDataExtension contactDataExtension = new ContactDataExtension();
         contactDataExtension.setContactType(type.getShortName());
         contactDataExtension.setContactTypeId(telephone.getContactTypeId());
         contactDataExtension.setContactData(telephone.getContactData());
         contactDataExtension.setSMSSwitch(telephone.isSMSSwitch());

         return contactDataExtension;
      }).collect(Collectors.toList());
      userInfoBean.setTelephoneContacts(telephoneContactList);

   }

   private void populatePostalAddress(UserInfoBean userInfoBean, Collection<PostalAddressEntry> postalAddressEntries) {
      Collection<PostalAddressDataExtension> postalAddressList = postalAddressEntries.stream().map(address -> {
         PostalAddressType type = PostalAddressType.getPostalAddressType(longToObjectIdLong(address.getContactTypeId()));
         PostalAddressDataExtension postalAddressDataExtension = new PostalAddressDataExtension();
         postalAddressDataExtension.setCity(address.getCity());
         postalAddressDataExtension.setCountry(address.getCountry());
         postalAddressDataExtension.setState(address.getState());
         postalAddressDataExtension.setStreet(address.getStreet());
         postalAddressDataExtension.setZipCode(address.getZipCode());
         postalAddressDataExtension.setContactType(type.getShortName());
         postalAddressDataExtension.setContactTypeId(address.getContactTypeId());
         return postalAddressDataExtension;
      }).collect(Collectors.toList());
      userInfoBean.setPostalAddresses(postalAddressList);
   }

   private void populateEmailAddress(UserInfoBean userInfoBean, Collection<ContactEntry> emailEntries, Personality personality) {
      Boolean hasEmailNotificationDelivery = loadHasEmailNotificationDeliveryProperty(personality);
      Collection<ContactDataExtension> emailContactEntries = emailEntries.stream().map(email -> {
         EMailAddressType type = EMailAddressType.getEMailAddressType(longToObjectIdLong(email.getContactTypeId()));
         ContactDataExtension contactDataExtension = new ContactDataExtension();
         contactDataExtension.setContactType(type.getShortName());
         contactDataExtension.setContactTypeId(email.getContactTypeId());
         contactDataExtension.setContactData(email.getContactData());
         contactDataExtension.setSMSSwitch(email.isSMSSwitch());
         contactDataExtension.setHasEmailNotificationDelivery(hasEmailNotificationDelivery);

         return contactDataExtension;
      }).collect(Collectors.toList());
      userInfoBean.setEmailContacts(emailContactEntries);
   }

   private static Boolean loadHasEmailNotificationDeliveryProperty(Personality personality) {
      ObjectIdLong oIdL = personality.getNotificationProfileId();
      if (isNull(oIdL)) {
         return false;
      } else {
         return personality.hasEmailNotificationDelivery();
      }
   }

}
