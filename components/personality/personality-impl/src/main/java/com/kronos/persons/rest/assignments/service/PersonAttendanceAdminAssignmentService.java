package com.kronos.persons.rest.assignments.service;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.AdminProfileBean;
import com.kronos.persons.rest.assignments.model.AttendanceAdminAssignmentBean;
import com.kronos.persons.rest.assignments.model.AttendanceAdminAssignmentWrapper;
import com.kronos.persons.rest.assignments.model.RestEntity;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.persons.utils.ExtensionConstant;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.absencemgmt.service.business.people.PersonAttendanceAdministratorAssignment;
import com.kronos.wfc.commonapp.people.business.person.Person;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.security.shared.SecurityConstants;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Named
public class PersonAttendanceAdminAssignmentService extends RestEntity {

	private static final String ADMINISTRATOR = "Administrator";

	@Inject
	private LicenseValidator licenseValidator;
	 
	@Inject
	ValidatorUtils validatorUtils;

	@Inject
	PersonIdentityBeanValidator personIdentityBeanValidator;

	public AttendanceAdminAssignmentBean retrieve(PersonIdentityBean identityBean) {
		Personality personality = validateAndGetEmployee(identityBean);
		return getAttendanceAssignment(personality);
	}

	public Personality updateRequest(AttendanceAdminAssignmentBean requestBean) {
		Personality employeePersonality = validateAndGetEmployee(requestBean.getPersonIdentity());
		Personality adminPersonality = validateAndGetAdministrator(requestBean);
		updateForEmployee(employeePersonality, adminPersonality);
		return employeePersonality;
	}

	/**
	 * Bulk update AttendanceAdminAssignments.
	 *
	 * @param requestBean list of bean wrappers
	 */
	public void bulkUpdateRequest(List<AttendanceAdminAssignmentWrapper> requestBean) {
		Map<AdminProfileBean, List<AttendanceAdminAssignmentWrapper>> wrappers = requestBean.stream()
				.collect(Collectors.groupingBy(beanWrapper -> beanWrapper.getBean().getAdministrator()));
		wrappers.forEach(this::updateAllAssignmentsWithOneAdministrator);
	}

	/**
	 * Bulk update PersonAttendanceAdministratorAssignments with one AdminProfileBean.
	 *
	 * @param administrator AdminProfileBean
	 * @param wrapperList list of assigments
	 *
	 */
	private void updateAllAssignmentsWithOneAdministrator(AdminProfileBean administrator,
														  List<AttendanceAdminAssignmentWrapper> wrapperList) {
		try {
			PersonAttendanceAdminAssignmentBulkTransactionService.getTransactionService(
					personIdentityBeanValidator.getPersonality(administrator),
					getAttendanceAdminAssignmentsByPerson(wrapperList)).run();
		} catch (APIException apiException) {
			saveExceptionInWrappers(apiException, wrapperList);
		} catch (GenericException genericException) {
			APIException apiException = PrsnException.getAPIException(genericException);
			saveExceptionInWrappers(apiException, wrapperList);
		} catch (Exception exception) {
			APIException apiException = PrsnException.getAPIException(exception);
			saveExceptionInWrappers(apiException, wrapperList);
		}
	}

	/**
	 * Gets PersonAttendanceAdministratorAssignments for beans by PersonId.
	 *
	 * @param wrapperList the list of beanWrapper.
	 * @return {@link List<PersonAttendanceAdministratorAssignment>}
	 */
	private List<PersonAttendanceAdministratorAssignment> getAttendanceAdminAssignmentsByPerson(
			List<AttendanceAdminAssignmentWrapper> wrapperList) {
		List<PersonAttendanceAdministratorAssignment> assignmentList = new ArrayList<>();
		wrapperList.forEach(beanWrapper -> {
			try {
				Personality employeePersonality =  personIdentityBeanValidator
						.getPersonality(beanWrapper.getBean().getPersonIdentity());
				beanWrapper.setBean(ResponseHandler.apiSuccessResponse(beanWrapper.getBean(), employeePersonality));
				PersonAttendanceAdministratorAssignment assignment = PersonAttendanceAdministratorAssignment
						.retrieveAttendanceAdminByPersonId(employeePersonality.getPersonId());
				if (validatorUtils.isNull(assignment)) {
					throw PrsnValidationException.nonExistentAttendanceAdminAssignment();
				}
				assignmentList.add(assignment);
			} catch (APIException apiException) {
				beanWrapper.setApiException(apiException);
			}
		});
		return assignmentList;
	}

	/**
	 * Sets exception in each beanWrapper from list
	 *
	 * @param exception    the {@link APIException}
	 * @param beanWrappers the List of {@link com.kronos.persons.rest.model.BeanWrapper}
	 */
	private void saveExceptionInWrappers(APIException exception, List<AttendanceAdminAssignmentWrapper> beanWrappers) {
		beanWrappers.forEach(beanWrapper -> beanWrapper.setApiException(exception));
	}

	public Personality deleteRequest(AttendanceAdminAssignmentBean requestBean) {
		Personality employeePersonality = validateAndGetEmployee(requestBean.getPersonIdentity());
		PersonAttendanceAdministratorAssignment assignment = PersonAttendanceAdministratorAssignment
				.retrieveAttendanceAdminByPersonId(employeePersonality.getPersonId());

		if (validatorUtils.isNull(assignment)) {
			throw PrsnValidationException.nonExistentAttendanceAdminAssignment();
		}
		assignment.delete();
		return employeePersonality;
	}

	private void updateForEmployee(Personality personality, Personality admin) {
		PersonAttendanceAdministratorAssignment assignment = PersonAttendanceAdministratorAssignment
				.retrieveAttendanceAdminByPersonId(personality.getPersonId());

		if (validatorUtils.isNull(assignment)) {
			throw PrsnValidationException.nonExistentAttendanceAdminAssignment();
		}
		assignment.setAttendanceAdminId(admin.getPersonId());
		assignment.update();
	}

	private Personality validateAndGetEmployee(PersonIdentityBean identityBean) {
		if (validatorUtils.isNull(identityBean)) {
		    throw PrsnValidationException.missingProperty(ExtensionConstant.PERSON_IDENITY);
		}
		personIdentityBeanValidator.newvalidate(null, identityBean, null);
		Personality employeePersonality = personIdentityBeanValidator.getPersonality(identityBean);
		licenseValidator.validateLicense(employeePersonality, SecurityConstants.WORKFORCE_ATTENDANCE);
		return employeePersonality;
	}

	private Personality validateAndGetAdministrator(AttendanceAdminAssignmentBean requestBean) {
		Personality adminPersonality = null;
		if (validatorUtils.isNull(requestBean.getAdministrator())) {
			throw PrsnValidationException.missingProperty(ADMINISTRATOR);
		}
		personIdentityBeanValidator.validate(null, requestBean.getAdministrator(), null);
		adminPersonality = personIdentityBeanValidator.getPersonality(requestBean.getAdministrator());
		licenseValidator.validateManagerRole(adminPersonality);
		return adminPersonality;
	}

	/**
	 * Validates AttendanceAdminAssignment bean from the wrapper.
	 * In case of exception on the validation stage it will be stored into the wrapper.
	 *
	 * @param beanWrapper instance of {@link AttendanceAdminAssignmentWrapper}
	 */
	public void validateAdministratorWrapper(AttendanceAdminAssignmentWrapper beanWrapper) {
		try {
			AttendanceAdminAssignmentBean bean = beanWrapper.getBean();
			if (validatorUtils.isNull(bean.getAdministrator())) {
				beanWrapper.setApiException(PrsnValidationException.missingProperty(ADMINISTRATOR));
				return;
			}
			personIdentityBeanValidator.validate(null, bean.getAdministrator(), null);
			validateAndGetEmployee(bean.getPersonIdentity());
		} catch (APIException apiException) {
			beanWrapper.setApiException(apiException);
		}
	}

    private AttendanceAdminAssignmentBean getAttendanceAssignment(Personality personality) {
		AttendanceAdminAssignmentBean bean = new AttendanceAdminAssignmentBean();
		PersonAttendanceAdministratorAssignment assignment = PersonAttendanceAdministratorAssignment
				.retrieveAttendanceAdminByPersonId(personality.getPersonId());
		if (validatorUtils.isNull(assignment)) {
			throw PrsnValidationException.nonExistentAttendanceAdminAssignment();
		}
		bean.setPersonIdentity(personIdentityBeanValidator.createForPersonNumber(personality.getPersonNumber()));

		ObjectIdLong adminId = assignment.getAttendanceAdminId();
		if (validatorUtils.notNull(adminId) && !adminId.isNull()) {
			Person pObj = Person.getByPersonId(adminId);
			AdminProfileBean adminBean = new AdminProfileBean();
			adminBean.setPersonNumber(pObj.getPersonNumber());
			adminBean.setFullName(pObj.getFullName());
			adminBean.setDisplayName(adminBean.getDisplayName());
			bean.setAdministrator(adminBean);
		}
		return bean;
	}
}
