package com.kronos.persons.context.switchrole;

public class DelegationsList implements IDelegationsList {

	private String id;

	private String delegatorId;

	private String delegator;

	private String role;

	private String startDate;

	private String endDate;

	public DelegationsList() {
	}

	/**
	 * {@inheritDoc}
	 * @return Returns the id.
	 */
	public String getId() {
		return id;
	}

	/**
	 * {@inheritDoc}
	 * @param id
	 *            The id to set.
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * {@inheritDoc}
	 * @param delegatorId
	 *            The delegatorId to set.
	 */
	public void setDelegatorId(String delegatorId) {
		this.delegatorId = delegatorId;
	}

	/**
	 * {@inheritDoc}
	 * @return Returns the delegator id.
	 */
	public String getDelegatorId() {
		return delegatorId;
	}

	/**
	 * {@inheritDoc}
	 * @return Returns the delegator.
	 */
	public String getDelegator() {
		return delegator;
	}

	/**
	 * {@inheritDoc}
	 * @param delegator
	 *            The delegator to set.
	 */
	public void setDelegator(String delegator) {
		this.delegator = delegator;
	}

	/**
	 * {@inheritDoc}
	 * @return Returns the role.
	 */
	public String getRole() {
		return role;
	}

	/**{@inheritDoc}
	 * @param role
	 *            The role to set.
	 */
	public void setRole(String role) {
		this.role = role;
	}

	/**{@inheritDoc}
	 * @return Returns the startDate.
	 */
	public String getStartDate() {
		return startDate;
	}

	/**{@inheritDoc}
	 * @param startDate
	 *            The startDate to set.
	 */
	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	/**{@inheritDoc}
	 * @return Returns the endDate.
	 */
	public String getEndDate() {
		return endDate;
	}

	/**
	 * {@inheritDoc}
	 * @param endDate
	 *            The endDate to set.
	 */
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

}
