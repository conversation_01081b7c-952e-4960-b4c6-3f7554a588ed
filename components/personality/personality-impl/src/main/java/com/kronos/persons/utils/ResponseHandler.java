package com.kronos.persons.utils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

import com.kronos.commonapp.authz.impl.fap.profiles.AccessProfile;
import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.extensions.ExceptionBean;
import com.kronos.persons.rest.exception.ExceptionConstants;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.persons.rest.model.BatchResponseBean;
import com.kronos.persons.rest.model.BatchResponseDetailsBean;
import com.kronos.persons.rest.model.BatchResponseResultsBean;
import com.kronos.persons.rest.model.PersonalityBean;
import com.kronos.persons.rest.model.ResponseBean;
import com.kronos.persons.rest.model.ResponseEntity;
import com.kronos.persons.rest.model.RestErrorBean;
import com.kronos.wfc.commonapp.people.business.person.PersonLicenseTypeSet;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.commonapp.people.business.user.CurrentUserAccountManager;
import com.kronos.wfc.commonapp.people.business.user.UserAccount;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import org.springframework.util.StringUtils;

/**
 * 
 * <AUTHOR>
 *
 */
public class ResponseHandler {
    
    private static final KLogger LOGGER = KLoggerFactory.getKLogger(ResponseHandler.class);

    private static final String RECORD_BATCH_SIZE = "com.kronos.persons.rest.service.recordbatch.size";
    private static final String DEFAULT_RECORD_BATCH_SIZE = "100";
    private static final String REQUEST_DATA = "RequestData";
    
    private ResponseHandler() {

    }
    public static<T> T apiSuccessResponse(T requestBean,Personality personality) {
    	
    	if(requestBean instanceof PersonalityBean) {
    		addPersonIdentityInfo((PersonalityBean)requestBean, personality);
    	}
    	ResponseEntity<T> response = new ResponseEntity<T>(requestBean);
    	return response.getInput();
    }
    
    private static void addPersonIdentityInfo(PersonalityBean requestBean, Personality personality){
    	PersonIdentityBean personIdentity =  requestBean.getPersonIdentity();
		if ( personIdentity == null){
			personIdentity = new PersonIdentityBean();
			decorateResponseBean(requestBean, personality);
			requestBean.setPersonIdentity(personIdentity);
			personIdentity.setPersonKey(personality.getPersonId().toLong());
		}
    }

  private static void decorateResponseBean(PersonalityBean requestBean, Personality personality) {
    UserAccount userAccount = null;
    if(Objects.nonNull(personality)) {
      userAccount = personality.getUserAccount();
    }
    if (Objects.nonNull(userAccount)) {
      Long mfaRequired = userAccount.getMfaRequired();
      String username = userAccount.getUserName();
      String ssoUsername = userAccount.getSsoUsername();

      if (Objects.nonNull(requestBean.getUser()) && Objects.nonNull(requestBean.getUser().getUserAccount())) {
        requestBean.getUser().getUserAccount().setMfaRequired(mfaRequired == 1L);

        if(StringUtils.hasLength(username)) {
          requestBean.getUser().getUserAccount().setUserName(username);
        }
        if(StringUtils.hasLength(ssoUsername)) {
          requestBean.getUser().getUserAccount().setSsoUsername(ssoUsername);
        }
      }
    }
  }

	public static ResponseBean batchServiceSuccessResponse(
            Personality personality) {
        ResponseBean response = new ResponseBean();
        response.setPersonId(personality.getPersonId().toLong());
        response.setPersonNumber(personality.getPersonNumber());
        return response;

    }

    public static RestErrorBean batchServiceErrorResponse(Throwable e,
            String actionName) {
        return ExceptionHandler.handleException(e, "Personality", actionName);
    }

    /**
     * Check allowed service limit
     * 
     * @param requestList
     */
    public static void validServiceLimit(List<?> requestList) {
        validateServiceLimit(requestList, RECORD_BATCH_SIZE, DEFAULT_RECORD_BATCH_SIZE);
    }

    public static void validateServiceLimit(List<?> requestList, String propertyKey, String defaultSize) {
        String batchSizeStr = KronosProperties.getProperty(propertyKey, defaultSize);
        int batchSize = Integer.parseInt(batchSizeStr);

        validateServiceLimit(requestList, batchSize);
    }

    public static void validateServiceLimit(List<?> requestList, int batchSize) {
        if (requestList != null && requestList.size() > batchSize) {
            LOGGER.error("Persons REST API : Service limit error, number of records exceed the allowed limit :" + batchSize);
            Map<String, String> userparams = new HashMap<String, String>();
            userparams.put("batchSize", Integer.toString(batchSize));
            APIException limitExp = ExceptionHandler.getAPIExceptionWithUserParameters(ExceptionConstants.EXCEPTION_101207, userparams);
            
            List<HashMap<String, Integer>> limitList = new LinkedList<>();
            HashMap<String, Integer> limitMap1 = new LinkedHashMap<>();
            limitMap1.put("employees",batchSize);
            
            limitExp.setLimitsDetail(limitList);
            throw limitExp;
        }
    }

    public static BatchResponseBean generateBatchResponse(
            BatchResponseDetailsBean detailresponse,
            List<BatchResponseResultsBean> results, List<Integer> errorOffsets) {

        BatchResponseBean batchresponse = new BatchResponseBean();

        if (!errorOffsets.isEmpty()) {
            // for error
            batchresponse.setErrorCode(ExceptionConstants.EXCEPTION_101205);
            batchresponse.setMessage("completed with error(s).");
            detailresponse.setErrorOffSets(errorOffsets);
        } else {
            // for success
            batchresponse.setMessage("success");
        }

        detailresponse.setResults(results);
        batchresponse.setDetails(detailresponse);
        return batchresponse;

    }
    
	public static <T extends ExceptionBean> List<T> setPersonResponseStatus(List<T> responseList) {
		Function<T, T> responseProcessor = (T resData) -> {
			if (resData.getError() == null)
				return resData;
			else
				throw resData.getError();
		};
		NewBatchProcessor<T, T> processor = new NewBatchProcessor<>(responseList, responseProcessor);
		return processor.process();
	}
	
	public static void validateForNullRequest(Object requestData) {
		Optional<Object> requestOptional = Optional.ofNullable(requestData);
		requestOptional.orElseThrow(() -> PrsnValidationException.invalidPropertyValue(REQUEST_DATA, null));
	}
    
    public static void validateACP(String accessControlPoint) {
        if (!AccessProfile.isPermitted(accessControlPoint)) {
            throw PrsnValidationException.accessViolation("Attestation profile assignment API", accessControlPoint);
        }

    }

    public static void validateManager() {
        if (isNotManager()) {
            throw PrsnValidationException.accessViolation("PercentageAllocationRuleAssignment", null);
        }
    }

    private static boolean isNotManager() {
        return Optional.ofNullable(CurrentUserAccountManager.getPersonality())
                .map(Personality::getLicenseTypes)
                .map(PersonLicenseTypeSet::hasManagerLicense)
                .map(isManager -> !isManager)
                .orElse(true);
    }
}
