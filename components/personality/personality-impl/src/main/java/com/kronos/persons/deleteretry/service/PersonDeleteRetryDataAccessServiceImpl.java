package com.kronos.persons.deleteretry.service;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;
import jakarta.inject.Inject;

import org.springframework.stereotype.Repository;

import com.kronos.persons.deleteretry.repository.PersonDeleteRetryRepository;
import com.kronos.persons.deleteretry.model.PersonDeleteRetryEntity;

@Repository
public class PersonDeleteRetryDataAccessServiceImpl implements PersonDeleteRetryDataAccessService {

	@Inject
	private PersonDeleteRetryRepository personDeleteRetryRepository;

	@Override
	public Integer updatePersonRetryCount(BigInteger personId, Integer retryCount,
			Timestamp currentUTCTtime) {
		return personDeleteRetryRepository.updatePersonRetryCount(personId, retryCount, currentUTCTtime);
	}

	@Override
	public List<PersonDeleteRetryEntity> getPersonIdsToDelete(Integer threshold, Timestamp timeDiff) {
		return personDeleteRetryRepository.getPersonIdToDelete(threshold, timeDiff);
	}

	@Override
	public void save(PersonDeleteRetryEntity pdre) {
		personDeleteRetryRepository.save(pdre);
	}

	@Override
	public void delete(PersonDeleteRetryEntity pdre) {
		personDeleteRetryRepository.delete(pdre);
	}
	
	@Override
	public void removeDeletedPerson(){
		personDeleteRetryRepository.removeDeletedPerson();
	}
	
	@Override
	public void removeOldRecords(Timestamp time){
		personDeleteRetryRepository.removeOldRecords(time);
	}
	
}
