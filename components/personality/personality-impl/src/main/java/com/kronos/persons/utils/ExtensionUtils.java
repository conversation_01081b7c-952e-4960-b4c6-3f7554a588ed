/**
 * 
 */
package com.kronos.persons.utils;

import static com.kronos.wfc.platform.xml.api.restwrapper.converter.RestConstants.JSON_ERROR_MESSAGE_TEMPLATE;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.people.personality.model.IdentifierType;
import com.kronos.people.personality.model.extension.BaseExtension;
import com.kronos.people.personality.model.extension.entry.EffectiveDatedEntry;
import com.kronos.persons.rest.beans.extensions.AbstractDataExtension;
import com.kronos.persons.rest.model.ExtensionCriteria;
import com.kronos.persons.rest.model.PersonalityBean;
import com.kronos.wfc.commonapp.dataaccess.framework.Profile;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.platform.properties.framework.KronosProperties;
import com.kronos.wfc.platform.utility.framework.datetime.KConstants;
import com.kronos.wfc.platform.utility.framework.datetime.KDate;

/**
 * <AUTHOR>
 */
@SuppressWarnings("deprecation")
public class ExtensionUtils {
    private static final KLogger LOGGER = KLoggerFactory.getKLogger(ExtensionUtils.class);

    private static Map<String, IdentifierType> identifierTypeMap = new HashMap<>();
    
    private ExtensionUtils() {

    }

    static {
        identifierTypeMap.put(ExtensionConstant.PERSON_ID, IdentifierType.PERSONID);
        identifierTypeMap.put(ExtensionConstant.PERSON_NUMBER, IdentifierType.PERSONNUMBER);
        identifierTypeMap.put(ExtensionConstant.JOBASSIGNMENT_ID, IdentifierType.JOBASSIGNMENTID);
        identifierTypeMap.put(ExtensionConstant.USER_NAME, IdentifierType.USERNAME);
        identifierTypeMap.put(ExtensionConstant.USER_ACCOUNT_ID, IdentifierType.USERACCOUNTID);
        identifierTypeMap.put(ExtensionConstant.BADGE_NUMBER, IdentifierType.BADGENUMBER);
        identifierTypeMap.put(ExtensionConstant.USER_EMAIL_ADDRESS, IdentifierType.USEREMAILADDRESS);
    }

    public static IdentifierType getIdentifierType(String type) {
        return identifierTypeMap.get(type.toLowerCase());
    }

    public static Predicate<Long> getLongNullCheckPredicate() {
        return v -> notNull(v) && v.longValue() != 0;
    }

    @SuppressWarnings("rawtypes")
    public static <T> Predicate<Collection<T>> getNullorEmptyCheckPredicate() {
        return v -> notNull(v) && !v.isEmpty();
    }

    public static ObjectIdLong longToObjectIdLong(Long value) {
        return new ObjectIdLong(value);
    }

    public static void setDates(EffectiveDatedEntry source, EffectiveDatedEntry target) {
        target.setEffectiveDate(source.getEffectiveDate());
        target.setExpirationDate(source.getExpirationDate());
    }

    public static Object getPrivateFieldValue(Object obj, String fieldName) {
        try {
            Field f = obj.getClass().getDeclaredField(fieldName);
            f.setAccessible(true);
            return f.get(obj);
        } catch (Exception e) {
            LOGGER.error("Error occured while retrieving the field: " + fieldName, e);
        }
        return null;
    }

    public static String getErrorResponseMessage(String action, String message) {
        return JSON_ERROR_MESSAGE_TEMPLATE.replaceAll("actionNamePlaceHolder", action).replaceAll("errorMessagePlaceHolder", message);
    }

    public static String getLocalizedValue(String key, String defaultValue) {
        return KronosProperties.get(key, defaultValue);
    }

    public static String getLocalizedValue(Profile profile) {
        return getLocalizedValue(profile.getStringPropertyKey(), profile.getName());
    }

    public static <U> void runConsumerIfPredicatePass(U typeToTest, Predicate<U> predicate, Consumer<U> consumer) {
        if (predicate.test(typeToTest)) {
            consumer.accept(typeToTest);
        }
    }

    public static boolean isNull(Object value) {
        return !notNull(value);
    }

    public static boolean notNull(Object value) {
        return Optional.ofNullable(value).isPresent();
    }

    public static String formatDateString(String dateString) {
        LocalDate date = DateUtils.convertStringToLocalDate(dateString);
        if (notNull(date)) {
            return date.toString();
        }
        return null;
    }
    
    public static String formatDateStringWithPattern(String dateString, String datePattern) {
        LocalDate date = DateUtils.convertStringToLocalDate(dateString, datePattern);
        if (notNull(date)) {
            return date.toString();
        }
        return null;
    }
    
    public static void populateCommonExtensionAttribute(BaseExtension extension, AbstractDataExtension dataExtension) {
        dataExtension.setActive(extension.isActive());
        dataExtension.setPersonId(extension.getPersonId());
        dataExtension.setPersonNumber(extension.getPersonNumber());
    }

    public static LocalDate getEndDate() {
        KDate date = KConstants.endOfTime_Date;
        return LocalDate.of(date.getYear(), date.getMonth(), date.getDay());
    }
    
    public static boolean withinDateRange(LocalDate startDate, LocalDate endDate, LocalDate date) {
        int result = startDate.compareTo(date);
        if (result <= 0) {
            return endDate.compareTo(date) > 0;
        } else {
            return false;
        }
    }
    
    public static List<List<String>> createAoidCoidList(ExtensionCriteria criteriaBean, List<String> listAoidReq,
            Map<String, String> aoidCoidMap) {
        return criteriaBean.getMultiKeyValues().stream().map(searchValues -> {
            List<String> aoidCoidList = new ArrayList<String>();
            aoidCoidList.add(searchValues.getAoid());
            aoidCoidList.add(searchValues.getCoid());
            listAoidReq.add(searchValues.getAoid());
            aoidCoidMap.put(searchValues.getAoid(), searchValues.getCoid());
            return aoidCoidList;
        }).collect(Collectors.toList());
    }

    /**
     * Method to used to remove the personality cache data information on cache when exist an error in the process
     *
     * @param reqData Attribute that contains the request with the person information to update
     */
    public static void removePersonalityCacheOnError(PersonalityBean reqData) {
        if (reqData.getPersonality() != null) {
            reqData.getPersonality().removeFromCaches();
        }
    }
}
