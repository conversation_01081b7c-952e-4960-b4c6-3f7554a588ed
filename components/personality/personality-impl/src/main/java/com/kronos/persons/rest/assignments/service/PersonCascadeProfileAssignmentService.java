package com.kronos.persons.rest.assignments.service;

import com.kronos.container.api.exception.APIException;
import com.kronos.persons.rest.assignments.model.AssignmentProfileRequestBean;
import com.kronos.persons.rest.assignments.model.AssignmentProfileRequestBeanWrapper;
import com.kronos.persons.rest.assignments.model.RestEntity;
import com.kronos.persons.rest.beans.PersonIdentityBean;
import com.kronos.persons.rest.beans.validator.PersonIdentityBeanValidator;
import com.kronos.persons.rest.beans.validator.ValidatorUtils;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.persons.rest.exception.PrsnValidationException;
import com.kronos.persons.rest.model.BeanWrapper;
import com.kronos.persons.utils.ResponseHandler;
import com.kronos.wfc.commonapp.people.business.personality.Personality;
import com.kronos.wfc.platform.exceptions.framework.GenericException;
import com.kronos.wfc.platform.persistence.framework.ObjectIdLong;
import com.kronos.wfc.timekeeping.cascade.business.config.CascadeProfile;
import com.kronos.wfc.timekeeping.cascade.business.people.PersonCascadeProfileAssignment;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Named
public class PersonCascadeProfileAssignmentService extends RestEntity {

	@Inject
	ValidatorUtils validatorUtils;

	@Inject
	PersonIdentityBeanValidator personIdentityBeanValidator;

	private static final String PERSON_IDENTITY = "personIdentity";
	
	private static final String CASCADE_PROFILE = "cascadeProfile";
	
	private static final String PROFILE = "profile";
	
	

	public AssignmentProfileRequestBean retrieve(PersonIdentityBean requestBean) {
		Personality personality = validateAndGetPersonality(requestBean);
		return getCascadeAssignment(personality);
	}
	
	public AssignmentProfileRequestBean retrieveByPersonNumber(String personNumber) {
		PersonIdentityBean identityBean = new PersonIdentityBean();
		identityBean.setPersonNumber(personNumber);
		Personality personality = personIdentityBeanValidator.getPersonByPersonNumber(identityBean);
		if (personality != null) {
			return getCascadeAssignment(personality);
		} else {
			throw PrsnValidationException.invalidPropertyValue("personNumber", personNumber);
		}
	}

	public AssignmentProfileRequestBean retrieveByPersonId(Long personId) {
		Personality personality = personIdentityBeanValidator.getPersonByPersonId(personId);
		return getAssiAssignmentProfileByPersonality(personId,personality);
	}

	public AssignmentProfileRequestBean retrieveByPersonIdFromPersonality(Long personId) {
		Personality personality = Personality.getByPersonId(new ObjectIdLong(personId));
		return getAssiAssignmentProfileByPersonality(personId,personality);
	}

	private AssignmentProfileRequestBean getAssiAssignmentProfileByPersonality(Long personId, Personality personality) {
		if (personality != null) {
			return getCascadeAssignment(personality);
		} else {
			throw PrsnValidationException.invalidPropertyValue("personId", personId);
		}
	}

	private AssignmentProfileRequestBean getCascadeAssignment(Personality personality) {
		AssignmentProfileRequestBean requestBean = new AssignmentProfileRequestBean();
		PersonCascadeProfileAssignment personCascadeProfileAssignment = PersonCascadeProfileAssignment
				.retrieveCascadeProfileByPersonId(personality.getPersonId());
		requestBean.setPersonIdentity(personIdentityBeanValidator.createForPersonNumber(personality.getPersonNumber()));
		requestBean.setAssignmentProfile(getCascadeProfile(personCascadeProfileAssignment));
		//Change to add the ID in the cache (Enable it)
		requestBean.setProfileId(personCascadeProfileAssignment.getProfileId().longValue());
		return requestBean;
	}

	private Personality validateAndGetPersonality(PersonIdentityBean identityBean) {
		if(identityBean == null) {
			throw PrsnValidationException.missingProperty(PERSON_IDENTITY);
		}
		personIdentityBeanValidator.newvalidate(null,
				identityBean, null);
		return personIdentityBeanValidator
				.getPersonality(identityBean);
	}

	public Personality updateRequest(AssignmentProfileRequestBean requestBean) {

		PersonCascadeProfileAssignment personCascadeProfileAssignment;
		if(requestBean.getPersonIdentity() == null) {
			throw PrsnValidationException.missingProperty(PERSON_IDENTITY);
		}
		personIdentityBeanValidator.newvalidate(null,
				requestBean.getPersonIdentity(), null);
		Personality personality = personIdentityBeanValidator
				.getPersonality(requestBean.getPersonIdentity());
		personCascadeProfileAssignment = PersonCascadeProfileAssignment
				.retrieveCascadeProfileByPersonId(personality.getPersonId());
		this.setWrappedObject(personCascadeProfileAssignment);
		setCascadeProfile(requestBean.getAssignmentProfile());
		doUpdateOnly();
		return personality;
	}

	/**
	 * Save beans what contains in beanWrappers.
	 *
	 * @param beanWrappers list of bean wrappers with valid beans
	 */
	public void multiUpdate(List<AssignmentProfileRequestBeanWrapper> beanWrappers) {
		Map<String, List<AssignmentProfileRequestBeanWrapper>> wrappers =
				groupWrappersByAssignmentProfile(beanWrappers);
		wrappers.forEach((profileName, wrapperList) -> {
			try {
				multiUpdate(profileName, wrapperList);
			} catch (APIException apiException) {
				saveExceptionInWrappers(apiException, wrapperList);
			} catch (GenericException genericException) {
				APIException apiException = PrsnException.getAPIException(genericException);
				saveExceptionInWrappers(apiException, wrapperList);
			} catch (Exception exception) {
				APIException apiException = PrsnException.getAPIException(exception);
				saveExceptionInWrappers(apiException, wrapperList);
			}
		});
	}

	private void multiUpdate(String profileName, List<AssignmentProfileRequestBeanWrapper> wrapperList) {
		CascadeProfile cascadeProfile = getCascadeProfile(profileName);
		List<PersonCascadeProfileAssignment> personCascadeProfileAssignments =
				convertToPersonCascadeProfile(wrapperList);
		PersonCascadeProfileBulkUpdateTransactionService transactionService =
				PersonCascadeProfileBulkUpdateTransactionService
						.getTransactionService(cascadeProfile, personCascadeProfileAssignments,false);
		transactionService.run();
	}

	private Map<String, List<AssignmentProfileRequestBeanWrapper>> groupWrappersByAssignmentProfile(
			List<AssignmentProfileRequestBeanWrapper> beanWrappers) {
		return beanWrappers
				.stream()
                .collect(Collectors.groupingBy(beanWrapper -> beanWrapper.getBean().getAssignmentProfile()));
	}

	private List<PersonCascadeProfileAssignment> convertToPersonCascadeProfile(
			List<AssignmentProfileRequestBeanWrapper> wrapperList) {
		return wrapperList.stream()
				.map(this::getPersonCascadeProfileAssignment)
				.collect(Collectors.toList());
	}

	/**
	 * Gets PersonCascadeProfileAssignment for bean what contains in bean wrapper.
	 *
	 * @param beanWrapper the beanWrapper.
	 * @return {@link PersonCascadeProfileAssignment}
	 */
	private PersonCascadeProfileAssignment getPersonCascadeProfileAssignment(
			AssignmentProfileRequestBeanWrapper beanWrapper) {
		Personality personality = personIdentityBeanValidator
				.getPersonality(beanWrapper.getBean().getPersonIdentity());
		beanWrapper.getBean().getPersonIdentity().setPersonKey(personality.getPersonId().longValue());
		return PersonCascadeProfileAssignment.retrieveCascadeProfileByPersonId(personality.getPersonId());
	}

	/**
	 * Sets exception in every beanWrapper from list.
	 *
	 * @param exception    the {@link APIException}
	 * @param beanWrappers the List of {@link BeanWrapper}
	 */
	private void saveExceptionInWrappers(APIException exception,
                                          List<AssignmentProfileRequestBeanWrapper> beanWrappers) {
		beanWrappers.forEach(beanWrapper -> beanWrapper.setApiException(exception));
	}

	/**
	 * Validates bean from the wrapper.
	 * In case of exception on the validation stage it will be stored into the wrapper.
	 *
	 * @param beanWrapper instance of {@link AssignmentProfileRequestBean}
	 */
	public void validate(BeanWrapper<AssignmentProfileRequestBean> beanWrapper) {
		try {
			AssignmentProfileRequestBean bean = beanWrapper.getBean();
			validateCascadeProfileName(bean.getAssignmentProfile());
			Personality personality = validateAndGetPersonality(bean.getPersonIdentity());
			beanWrapper.setBean(ResponseHandler.apiSuccessResponse(bean, personality));
		} catch (APIException apiException) {
			beanWrapper.setApiException(apiException);
		}
	}

	/**
	 * Validate name for cascade profile.
	 *
	 * @param cascadeProfileName name of cascade profile
	 * @throws APIException if name of cascade profile not valid
	 */
	private String validateCascadeProfileName(String cascadeProfileName) {
		if (Objects.isNull(cascadeProfileName)) {
			throw PrsnValidationException.missingProperty(CASCADE_PROFILE);
		} else {
			cascadeProfileName = cascadeProfileName.trim();
		}
		return cascadeProfileName;
	}

	public Personality deleteRequest(AssignmentProfileRequestBean requestBean) {
		PersonCascadeProfileAssignment personCascadeProfileAssignment;
		if(requestBean.getPersonIdentity() == null) {
			throw PrsnValidationException.missingProperty(PERSON_IDENTITY);
		}
		personIdentityBeanValidator.newvalidate(null,
				requestBean.getPersonIdentity(), null);
		Personality personality = personIdentityBeanValidator
				.getPersonality(requestBean.getPersonIdentity());
		personCascadeProfileAssignment = PersonCascadeProfileAssignment
				.retrieveCascadeProfileByPersonId(personality.getPersonId());
		this.setWrappedObject(personCascadeProfileAssignment);
		doDelete();
		return personality;
	}

	/**
	 * Set Cascade profile.
	 * 
	 * @param cascadeProfile
	 *            The cascade profile name to set.
	 */
	public void setCascadeProfile(String cascadeProfile) {
		((PersonCascadeProfileAssignment) getWrappedObject())
				.setProfileId(getCascadeProfile(cascadeProfile).getId());
	}

	/**
	 * Gets cascade profile by name.
	 *
	 * @param cascadeProfileName the name of cascade profile
	 * @return CascadeProfile by name
	 * @throws APIException if cascadeProfile not found.
	 */
	private CascadeProfile getCascadeProfile(String cascadeProfileName) {
		cascadeProfileName = validateCascadeProfileName(cascadeProfileName);
		CascadeProfile cascadeProfile = CascadeProfile.retrieveByName(cascadeProfileName);
		if (Objects.isNull(cascadeProfile)) {
			throw PrsnValidationException.invalidPropertyValue(CASCADE_PROFILE, cascadeProfileName);
		}
		return cascadeProfile;
	}

	/**
	 * Get The Cascade Profile.
	 * @param personCascadeProfileAssignment 
	 * 
	 * @return Returns the Cascade profile name.
	 */
	public String getCascadeProfile(PersonCascadeProfileAssignment personCascadeProfileAssignment) {
		ObjectIdLong profileId = personCascadeProfileAssignment.getProfileId();
		if (profileId == null || profileId.isNull())
			return null;

		CascadeProfile profile = CascadeProfile.retrieveById(profileId);
		if (profile == null)
			return null;

		return profile.getName();
	}
}
