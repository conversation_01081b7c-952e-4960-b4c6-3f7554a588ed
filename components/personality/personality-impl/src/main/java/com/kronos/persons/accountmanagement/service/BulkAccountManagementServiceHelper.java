package com.kronos.persons.accountmanagement.service;

import com.kronos.accountmanagement.api.AccountManagement;
import com.kronos.accountmanagement.domain.AccountManagementResponse;
import com.kronos.accountmanagement.domain.TransactionDTO;
import com.kronos.accountmanagement.domain.UserDTO;
import com.kronos.accountmanagement.domain.UserFailureListDTO;
import com.kronos.commonapp.kronosproperties.api.IKProperties;
import com.kronos.container.api.exception.APIException;
import com.kronos.logging.slf4jadapter.KLogger;
import com.kronos.logging.slf4jadapter.KLoggerFactory;
import com.kronos.persons.rest.exception.PrsnException;
import com.kronos.wfc.commonapp.people.business.user.UserException;
import java.text.MessageFormat;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class BulkAccountManagementServiceHelper {
  private static final KLogger LOGGER = KLoggerFactory.getKLogger(BulkAccountManagementServiceHelper.class);

  protected static final String AMS_BULKAPI_POLLING_INTERVAL = "com.kronos.people.auth.amsbulkapi.pollinginterval";
  protected static final String AMS_BULKAPI_TIMEOUT = "com.kronos.people.auth.amsbulkapi.timeout";
  protected static final String JOB_FAILURE = "JOB_FAILURE";
  protected static final String JOB_SUCCESS = "JOB_SUCCESS";
  protected static final String JOB_COMPLETED_WITH_ERRORS = "JOB_COMPLETED_WITH_ERRORS";
  protected static final String CREATE =  "create";
  protected static final String UPDATE =  "update";
  protected static final String DELETE =  "delete";

  private final AccountManagement accountManagement;
  private final String tenantId;
  private final IKProperties properties;

  public BulkAccountManagementServiceHelper(AccountManagement accountManagement, String tenantId, IKProperties properties) {
    this.accountManagement = accountManagement;
    this.tenantId = tenantId;
    this.properties = properties;
  }

  public AccountManagementResponse<UserFailureListDTO> bulkOperation(List<UserDTO> users, String action) {
    StopWatch stopWatch = new StopWatch();
    stopWatch.start("ams-bulk-" + action);

    try {
      AccountManagementResponse<TransactionDTO> response = amsBulkCall(users, action);

      if (responseHasErrors(response) || Objects.isNull(response.getResponse())) {
        LOGGER.error("AMS Bulk {} action call failed {}", action, response);
        String amsError = response.getError();
        if (StringUtils.isNotEmpty(amsError)) {
          throw amsErrorResponseException(amsError);
        } else {
          throw PrsnException.getAmsUserDefinedError("Unknown error from AMS.");
        }
      } else {
        try {
          String transactionId = response.getResponse().getTransactionId();
          LOGGER.debug("AMS call executed with response = {}, transaction={}", response,
              transactionId);
          return amsTransactionStatusPolling(transactionId);
        } catch (InterruptedException interruptedException) {
          throw PrsnException.getAmsUserDefinedError("Unknown Interruption error from AMS while polling status.");
        }
      }
    } finally {
      stopWatch.stop();
      LOGGER.debug("Bulk import call to AMS service completed in {} ms", stopWatch.getLastTaskTimeMillis());
    }
  }

  private AccountManagementResponse<TransactionDTO> amsBulkCall(List<UserDTO> users, String action) {

    try {
      switch (action) {
        case CREATE:
          return accountManagement.createUsers(tenantId, users);
        case UPDATE:
          return accountManagement.updateUsers(tenantId, users);
        case DELETE:
          List<String> userIds = users.stream().map(UserDTO::getUserId)
              .collect(Collectors.toList());
          return accountManagement.deleteUsers(tenantId, userIds);
        default:
          LOGGER.warn("Bulk operation {} not implemented", action);
          throw PrsnException.getAmsUserDefinedError(MessageFormat.format("Bulk AMS Operation not supported for {0} action", action));
      }
    } catch(APIException apiException) { throw  apiException;}
    catch (Exception exception) {
      throw PrsnException.getAmsUserDefinedError(MessageFormat.format("Unknown error from AMS during {0} action", action));
    }
  }

  protected AccountManagementResponse<UserFailureListDTO> amsTransactionStatusPolling(String transactionId) throws InterruptedException {
    AccountManagementResponse<UserFailureListDTO> userError = null;
    long period = Long.parseLong(properties.getProperty(AMS_BULKAPI_POLLING_INTERVAL, "500"));
    long timeout = Long.parseLong(properties.getProperty(AMS_BULKAPI_TIMEOUT, "60000"));

    long startPollingTime = System.currentTimeMillis();

    String transactionStatus;
    while(true) {
      transactionStatus  = pollTransactionStatus(transactionId);

      if(shouldStopPolling(transactionStatus) || ((System.currentTimeMillis()-startPollingTime) + period > timeout)) {
        break;
      }
      Thread.sleep(period);
    }

    if(isJobFailed(transactionStatus)) {
      try {
        userError = accountManagement.getTransactionErrors(tenantId, transactionId);
        if (responseHasErrors(userError) || Objects.isNull(userError.getResponse())) {
          LOGGER.debug("An error occurred during AMS transaction error call. TransactionId: {}, error: {}", transactionId, userError);
          String amsError = userError.getError();
          if (StringUtils.isNotEmpty(amsError)) {
            throw amsErrorResponseException(amsError);
          } else {
            throw PrsnException.getAmsUserDefinedError("Unknown error from AMS during transaction error call.");
          }
        }
        LOGGER.info("AMS transaction error completed with response: {}", userError);
      } catch (APIException apiException) {
        throw apiException;
      } catch (Exception e) {
        LOGGER.error("An error occurred during AMS transaction error call. TransactionId: {}, message: {}",
                transactionId, e.getMessage());
        throw PrsnException.getAmsUserDefinedError(
                "Unknown exception from AMS during transaction error call.");
      }
    }
     return userError;
  }

  private String pollTransactionStatus(String transactionId) {
    try {
      AccountManagementResponse<TransactionDTO> response = accountManagement.getTransaction(tenantId, transactionId);
      if (responseHasErrors(response) || Objects.isNull(response.getResponse())) {
        LOGGER.error("An error occurred during AMS transaction status call: {}", response);

        String amsError = response.getError();
        if (StringUtils.isNotEmpty(amsError)) {
         throw amsErrorResponseException(amsError);
        } else {
          throw PrsnException.getAmsUserDefinedError(
                  "Unknown error from AMS during transaction status call.");
        }
      }
      String transactionStatus = response.getResponse().getStatus();
      LOGGER.debug("AMS Transaction Polling status, transactionId {}, transactionStatus={}",
              transactionId, transactionStatus);
      return transactionStatus;
    } catch (APIException apiException) {
      throw apiException;
    } catch (Exception e) {
      LOGGER.error("AMS Transaction Polling status failed, transactionId {}, message={}",
              transactionId, e.getMessage());
      throw PrsnException.getAmsUserDefinedError(
              "Unknown exception from AMS during transaction status call.");
    }
  }

  private boolean shouldStopPolling(String transactionStatus) {
    return JOB_FAILURE.equalsIgnoreCase(transactionStatus)
            || JOB_SUCCESS.equalsIgnoreCase(transactionStatus)
            || JOB_COMPLETED_WITH_ERRORS.equalsIgnoreCase(transactionStatus);
  }

  private boolean isJobFailed(String transactionStatus) {
    return JOB_FAILURE.equalsIgnoreCase(transactionStatus)
            || JOB_COMPLETED_WITH_ERRORS.equalsIgnoreCase(transactionStatus);
  }

  private boolean responseHasErrors(AccountManagementResponse<?> response) {
    return response.isError();
  }

  private RuntimeException amsErrorResponseException(String amsError) {
    if(amsError.contains("Provided UPN") && amsError.contains("is not unique in this tenant")) {
      return UserException.userAccountSsoUsernameNotUnique();
    }
    return PrsnException.getAmsRestSideError(amsError);
  }

}