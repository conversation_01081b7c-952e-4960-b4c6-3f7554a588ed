package com.kronos.persons.utils;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.kronos.commonapp.kronosproperties.api.IKProperties;

@Named
public class PredictiveSchedulingHelper {
	private static final String GLOBAL_PREDICTIVE_SCHEDULING_ENABLED = "global.PredictiveScheduling.enabled";

	@Inject
	private IKProperties properties;

	public boolean isPredictiveSchedulingEnabled() {
		return properties.getPropertyAsBoolean(GLOBAL_PREDICTIVE_SCHEDULING_ENABLED, false);
	}
}
