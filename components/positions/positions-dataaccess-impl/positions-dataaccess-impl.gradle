//If deployToBackend is set to true, then this will be added into swimlane-runtime automatically if it isn't there already.
ext.deployToBackend=true

dependencies {

    api project(':positions-dataaccess-api')
    api project(':personality-impl')

    //Add dependencies here
    api (depManifest.setVersion(group: 'com.kronos.container', name: 'master-container-api')) {transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.dataaccess', name: 'dataaccess-framework-api')) {transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.container', name: 'cache-framework-api')) {transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'release-toggle-api')) {transitive=false}
    api (depManifest.setVersion(group: 'com.kronos.legacy', name: 'wfp')) {transitive=false}
	api (depManifest.setVersion(group: 'org.hibernate.orm', name: 'hibernate-core'))
	api (depManifest.setVersion(group:'jakarta.persistence',name:'jakarta.persistence-api'))
	api (depManifest.setVersion(group: 'antlr', name: 'antlr'))
//    api (depManifest.setVersion(group: 'org.jboss', name: 'jandex'))
    implementation 'io.smallrye:jandex:3.0.0'

<<<<<<< HEAD
    testImplementation (depManifest.setVersion(group: 'com.kronos.healthcheck-framework', name: 'healthcheck-wfmDB-api')) {transitive=false}
    testImplementation (depManifest.setVersion(group: 'org.junit.jupiter', name: 'junit-jupiter-api'))
    testImplementation (depManifest.setVersion(group: 'org.mockito', name: 'mockito-core'))
    testImplementation (depManifest.setVersion(group: 'org.mockito', name: 'mockito-junit-jupiter'))
//    testImplementation (depManifest.setVersion(group: 'org.powermock', name: 'powermock-module-junit4'))
//    testImplementation (depManifest.setVersion(group: 'org.powermock', name: 'powermock-api-mockito'))

    testImplementation (depManifest.setVersion(group: 'org.springframework', name: 'spring-test'))
    testImplementation (depManifest.setVersion(group: 'org.springframework', name: 'spring-jdbc'))
    testImplementation (depManifest.setVersion(group: 'hsqldb', name: 'hsqldb'))
    testImplementation (depManifest.setVersion(group: 'jakarta.el', name: 'jakarta.el-api'))
	testImplementation (depManifest.setVersion(group: 'net.bytebuddy', name: 'byte-buddy'))
    testImplementation 'io.smallrye:jandex:3.0.0'
    testRuntimeOnly(depManifest.setVersion(group: 'com.google.guava', name: 'guava'))
    //    testImplementation (depManifest.setVersion(group: 'org.jboss', name: 'jandex'))
    testImplementation (depManifest.setVersion(group: 'org.apache.poi', name: 'poi'))
    testImplementation (depManifest.setVersion(group: 'com.kronos.api', name: 'api-gateway-impl')) {transitive=false}



    testRuntimeOnly (depManifest.setVersion(group: 'org.springframework.amqp', name: 'spring-amqp'))
    testRuntimeOnly (depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'textlabel-framework-api')){transitive=false}
=======
    testCompile (depManifest.setVersion(group: 'org.springframework', name: 'spring-test'))
    testCompile (depManifest.setVersion(group: 'org.springframework', name: 'spring-jdbc'))
    testCompile (depManifest.setVersion(group: 'hsqldb', name: 'hsqldb'))
    testCompile (depManifest.setVersion(group: 'javax.el', name: 'javax.el-api'))
	testCompile (depManifest.setVersion(group: 'net.bytebuddy', name: 'byte-buddy'))
    testCompile (depManifest.setVersion(group: 'org.jboss', name: 'jandex'))
	
    testRuntime (depManifest.setVersion(group: 'org.jadira.usertype', name: 'usertype.core')) {transitive=false}
    testRuntime (depManifest.setVersion(group: 'org.jadira.usertype', name: 'usertype.extended')) {transitive=false}
    testRuntime (depManifest.setVersion(group: 'org.jadira.usertype', name: 'usertype.spi')) {transitive=false}
    testRuntime (depManifest.setVersion(group: 'org.springframework.amqp', name: 'spring-amqp'))
    testRuntime(depManifest.setVersion(group: 'com.kronos.commonbusiness', name: 'textlabel-framework-api')){transitive=false}
>>>>>>> r9int-build-1974
}
